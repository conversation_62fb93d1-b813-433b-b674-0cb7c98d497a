package com.ctrip.tour.business.dashboard.tktBusiness.strategy.impl;

import com.ctrip.soa._24922.*;
import com.ctrip.soa._27181.BIBaseReportQueryServiceClient;
import com.ctrip.soa._27181.GetRawDataRequestType;
import com.ctrip.soa._27181.GetRawDataResponseType;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.*;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.SinglePeriodTrendLineBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.UserPermissionBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.bo.ExamineConfigBo;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardExamineeConfigV2Dao;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.*;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.MetricCalStrategy;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.ctrip.tour.rights.client.QconfigRequestType;
import com.ctrip.tour.rights.client.QconfigResponseType;
import com.ctrip.tour.rights.client.TourRightsServiceClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

@Component
public class Bus11MetricStrategy implements MetricCalStrategy {

    @Autowired
    private BIBaseReportQueryServiceClient baseReportQueryServiceClient;

    @Autowired
    private BusinessDashboardExamineeConfigV2Dao examineeConfigV2Dao;

    @Autowired
    private SinglePeriodTrendLineBiz singlePeriodTrendLineBiz;

    @Autowired
    private RemoteConfig remoteConfig;

    @Autowired
    private UserPermissionBiz userPermissionBiz;

    @Autowired
    private TourRightsServiceClient tourRightsServiceClient;

    @Autowired
    private SwitchNewTableHelper switchNewTableHelper;

    @Override
    public Future<MetricDetailInfo> getSingleMetricCardData(String domainName,
                                                            TimeFilter timeFilter,
                                                            MetricInfoBean metricInfoBean,
                                                            String d,
                                                            Boolean needRank) throws Exception {

        MetricDetailInfo metricDetailInfo = new MetricDetailInfo();
        List<MetricDetailInfo> subMetricDetailInfoList = new ArrayList<>();
        metricDetailInfo.setSubMetricDetailInfoList(subMetricDetailInfoList);
        metricDetailInfo.setMetric(getMetricName());

        CheckUserPermissionResponseType checkUserPermissionReq = userPermissionBiz.checkUserPermission(new CheckUserPermissionRequestType(timeFilter, domainName, null, null));
        List<String> subMetricList = checkUserPermissionReq.getDomesticBasicConfig()
                .getMetricCardConfigMap()
                .get(getMetricName())
                .getSubMetricList();
        for(String subMetric : subMetricList) {
            MetricDetailInfo subMetricDetailInfo = getRivalMetricCardData(domainName, timeFilter, metricInfoBean, d, needRank, subMetric);
            subMetricDetailInfoList.add(subMetricDetailInfo);
        }
        return new AsyncResult<>(metricDetailInfo);
    }


    public MetricDetailInfo getRivalMetricCardData(String domainName,
                                                   TimeFilter timeFilter,
                                                   MetricInfoBean metricInfoBean,
                                                   String d,
                                                   Boolean needRank,
                                                   String subMetric) throws Exception {
        MetricDetailInfo metricDetailInfo = new MetricDetailInfo();


        Map<String, Double> dimMap = new HashMap<>();
        metricDetailInfo.setDimData(dimMap);
        metricDetailInfo.setSubMetric(subMetric);

        if ("domesticDayTour".equals(subMetric)) {
            MetricHelper.setOdtMetricCardDrillDownInfo(metricInfoBean.getOdtLevel(), metricInfoBean.getOdtRegionList(), metricDetailInfo, remoteConfig);
        } else {
            MetricHelper.setOdtMetricCardDrillDownInfo(metricInfoBean.getOverseaOdtLevel(), metricInfoBean.getOverseaOdtRegionList(), metricDetailInfo, remoteConfig);
        }

        SqlParamterBean bean = Bus11Helper.getMetricCardSqlBean(timeFilter, null, metricInfoBean, domainName, d, subMetric);
        Integer addtionalGapDay = Bus11Helper.getAddtionalGapDay(timeFilter, null, d, bean.getAndMap());
        GetRawDataRequestType metricCardReq = bean.convertBeanToRequest(true);
        GetRawDataResponseType metricCardRes = switchNewTableHelper.switchRemoteDatabase(metricCardReq);
        Bus11Helper.processMetricCardData(metricCardRes, dimMap);

//        Integer gapDays = Bus11Helper.getGapDays(timeFilter, null, d);
        Integer gapDays = getGapDays(timeFilter, d);

        Bus11Helper.calMetricCardAverageData(dimMap, gapDays, addtionalGapDay);
        Bus11Helper.makeUpMetricData(dimMap, getTarget(subMetric, timeFilter, null));

        if (needRank) {
            SqlParamterBean rankingBean = Bus11Helper.getRankingSqlBean(domainName, timeFilter, d,subMetric);
            GetRawDataRequestType rankingReq = rankingBean.convertBeanToRequest(true);
            GetRawDataResponseType rankingRes = switchNewTableHelper.switchRemoteDatabase(rankingReq);
            Bus11Helper.processRankData(rankingRes, metricDetailInfo);
        }

        return metricDetailInfo;
    }



    /**
     * 获取线路覆盖目标
     *
     * @param subMetric
     * @param timeFilter
     * @param examineConfigBean
     * @return
     */
    private Double getTarget(String subMetric,
                             TimeFilter timeFilter,
                             ExamineConfigBean examineConfigBean) throws Exception {
        //国内日游线路覆盖
        if("domesticDayTour".equals(subMetric)){
            return 0.03d;
        }
        //出境日游线路覆盖
        QconfigRequestType request = new QconfigRequestType();
        request.setKey("line.commodity.power.target");
        QconfigResponseType response = tourRightsServiceClient.getQconfig(request);
//        String value = "[{\"2024\":{\"Q1\":0.25,\"Q2\":0.25,\"Q3\":0.25,\"Q4\":0.25}},{\"2025\":{\"Q1\":0.25,\"Q2\":0.25,\"Q3\":0.25,\"Q4\":0.25}}]";
        return Bus11Helper.getOverseaRivalTarget(response.getValue(), timeFilter, examineConfigBean);
    }


    public Integer getGapDays(TimeFilter timeFilter,
                               String d) throws Exception {
        SqlParamterBean gapDaysSqlBean = Bus11Helper.getGapDaysSqlBean(timeFilter.getYear(), timeFilter.getDateType(),
                timeFilter.getMonth(), timeFilter.getQuarter(), "11", d);
        GetRawDataRequestType gapDaysReq = gapDaysSqlBean.convertBeanToRequest(true);
        GetRawDataResponseType gapDaysRes = switchNewTableHelper.switchRemoteDatabase(gapDaysReq);
        return MetricHelper.getGapDays(gapDaysRes);
    }

    @Override
    public GetTrendLineDataResponseType getSingleTrendlineData(GetTrendLineDataRequestType request,
                                                               MetricInfoBean metricInfoBean,
                                                               String d) throws Exception {
        String queryType = request.getQueryType();
        if ("trendline".equals(queryType)) {
            return getSingleTrendlineDataWithoutDrillDown(request, metricInfoBean, d);
        } else {
            return getSingleTrendlineDataWithDrillDown(request, metricInfoBean, d);
        }
    }


    private GetTrendLineDataResponseType getSingleTrendlineDataWithoutDrillDown(GetTrendLineDataRequestType request,
                                                                                MetricInfoBean metricInfoBean,
                                                                                String d) throws Exception {
        GetTrendLineDataResponseType response = new GetTrendLineDataResponseType();
        List<TrendLineDetailInfo> trendLineDetailInfoList = new ArrayList<>();
        response.setTrendLineDetailInfoList(trendLineDetailInfoList);


        TimeFilter timeFilter = request.getTimeFilter();
        String domainName = request.getDomainName();
        String subMetric = request.getSubMetric();

        List<ExamineConfigBean> examineConfigBeanList = MultiPeriodMappingHelper.getMultiPeriodConfigList(timeFilter, d, examineeConfigV2Dao.queryMetricAllConfig(domainName, d, getMetricName()), null);
        List<Future<SinglePeriodDataBean>> futureList = new ArrayList<>();

        Map<String,Double> targetMap = new HashMap<>();
        for (ExamineConfigBean bean : examineConfigBeanList) {
            ExamineConfigBo bo = new ExamineConfigBo();
            MetricInfoBean innerMetricInfoBean = bo.getMetricInfoBean(bean.getBusinessDashboardExamineeConfigV2(), remoteConfig);
            SqlParamterBean sqlParamterBean = Bus11Helper.getMetricCardSqlBean(null, bean, innerMetricInfoBean, domainName, d, subMetric);
            GetRawDataRequestType singlePeriodReq = sqlParamterBean.convertBeanToRequest(true);
            futureList.add(singlePeriodTrendLineBiz.getBus11SinglePeriodTrendLineData(request, innerMetricInfoBean, d, singlePeriodReq, bean));


            targetMap.put(bean.getTimeMap().get("currentTime"), getTarget(subMetric, null, bean));
        }

        //转化汇总返回数据
        PeriodDataBean periodDataBean = new PeriodDataBean();
        periodDataBean.setBeanData(futureList);

        //国内日游覆盖从23年开始看  出境日游覆盖从24年开始看
        String type = "domesticDayTour".equals(subMetric) ? "2023" : "2024";
        List<String> timeList = DateUtil.getAllSelectedTime(request.getTimeFilter(), type);


        Bus11Helper.processOverallTrendLineData(trendLineDetailInfoList, periodDataBean, timeList, targetMap);


        return response;
    }


    private GetTrendLineDataResponseType getSingleTrendlineDataWithDrillDown(GetTrendLineDataRequestType request,
                                                                             MetricInfoBean metricInfoBean,
                                                                             String d) throws Exception {
        GetTrendLineDataResponseType response = new GetTrendLineDataResponseType();
        List<TrendLineDetailInfo> trendLineDetailInfoList = new ArrayList<>();
        response.setTrendLineDetailInfoList(trendLineDetailInfoList);

        TimeFilter timeFilter = request.getTimeFilter();
        String domainName = request.getDomainName();
        String subMetric = request.getSubMetric();
        String field = MetricHelper.getDrillDownColumnName(request.getDrillDownFilter().getField());

        List<ExamineConfigBean> examineConfigBeanList = MultiPeriodMappingHelper.getMultiPeriodConfigList(timeFilter, d, examineeConfigV2Dao.queryMetricAllConfig(domainName, d, getMetricName()), null);
        List<Future<SinglePeriodDataBean>> futureList = new ArrayList<>();


        for (ExamineConfigBean bean : examineConfigBeanList) {
            ExamineConfigBo bo = new ExamineConfigBo();
            DrillDownFieldBean drillDownFieldBean = remoteConfig.getDrillDownFieldBean("11", subMetric, field);
            MetricInfoBean innerMetricInfoBean = bo.getMetricInfoBean(bean.getBusinessDashboardExamineeConfigV2(), remoteConfig, drillDownFieldBean.getExamineTypeList());
            if(GeneralUtil.isEmpty(innerMetricInfoBean)){
                continue;
            }
            SqlParamterBean sqlParamterBean = Bus11Helper.getTrendLineWithDrillDownSqlBean(innerMetricInfoBean, d, request.getDrillDownFilter(), bean, remoteConfig, subMetric);
            GetRawDataRequestType singlePeriodReq = sqlParamterBean.convertBeanToRequest(true);
            futureList.add(singlePeriodTrendLineBiz.getBus11SinglePeriodTrendLineData(request, innerMetricInfoBean, d, singlePeriodReq, bean));

        }

        //转化汇总返回数据
        PeriodDataBean periodDataBean = new PeriodDataBean();
        periodDataBean.setBeanData(futureList);

        //国内日游覆盖从23年开始看  出境日游覆盖从24年开始看
        String type = "domesticDayTour".equals(subMetric) ? "2023" : "2024";
        List<String> timeList = DateUtil.getAllSelectedTime(request.getTimeFilter(), type);

        Bus11Helper.processDrilldownTrendLineData(trendLineDetailInfoList, periodDataBean, timeList);


        return response;
    }


    @Override
    public GetTableDataResponseType getSingleTableData(GetTableDataRequestType request,
                                                       MetricInfoBean metricInfoBean,
                                                       String d) throws Exception {
        GetTableDataResponseType response = new GetTableDataResponseType();
        List<TableDataItem> tableDataItemList = new ArrayList<>();
        response.setTableDataItemList(tableDataItemList);
        String subMetric = request.getSubMetric();

        SqlParamterBean bean = Bus11Helper.getTableSqlBean(request, metricInfoBean, d, remoteConfig, subMetric);
        Integer addtionalGapDay = Bus11Helper.getAddtionalGapDay(request.getTimeFilter(), null, d, bean.getAndMap());
        GetRawDataRequestType tableReq = bean.convertBeanToRequest(true);
        GetRawDataResponseType tableRes = switchNewTableHelper.switchRemoteDatabase(tableReq);

        Bus10Helper.processTableData(tableRes, tableDataItemList);

//        Integer gapDays = Bus11Helper.getGapDays(request.getTimeFilter(), null, d);
        Integer gapDays = getGapDays(request.getTimeFilter(), d);
        Bus11Helper.calTableAverageData(tableDataItemList, gapDays, addtionalGapDay);
        Bus11Helper.makeUpTableData(tableDataItemList, getTarget(subMetric, request.getTimeFilter(), null));

        response.setTotalNum(tableRes.getTotalNum());
        return response;
    }

    @Override
    public GetDrillDownBaseInfoResponseType getSingleDrillDownBaseInfo(GetDrillDownBaseInfoRequestType request,
                                                                       MetricInfoBean metricInfoBean,
                                                                       String d) throws Exception {
        GetDrillDownBaseInfoResponseType response = new GetDrillDownBaseInfoResponseType();
        List<FieldDataItem> fieldDataItemList = new ArrayList<>();
        response.setFieldDataItemList(fieldDataItemList);
        Boolean needSearch = request.isNeedSearch();
        if (GeneralUtil.isEmpty(needSearch)) {
            request.setNeedSearch(false);
            needSearch = false;
        }
        List<String> fieldList = new ArrayList<>();
        if (needSearch) {
            fieldList.add(MetricHelper.getDrillDownColumnName(request.getSearchField()));
        } else {
            //根据考核层级确定，考核层级为国内，展示大区、省份；为大区，若范围为多个，展示大区，省份；若范围为一个，展示省份；若考核层级为
            fieldList.addAll(Bus11Helper.getDrillDownFieldList(metricInfoBean, request.getSubMetric()));
            response.setDefaultChosenField(MetricHelper.getDataBaseColumnName(fieldList.get(0)));
        }
        Map<String, Future<GetRawDataResponseType>> fieldMap = new HashMap<>();
        for (String field : fieldList) {
            SqlParamterBean sqlParamterBean = Bus11Helper.getDrillDownBaseInfoSqlBean(field, request, d, metricInfoBean, remoteConfig, request.getSubMetric());
            GetRawDataRequestType fieldRequest = sqlParamterBean.convertBeanToRequest(false);
            fieldMap.put(field, switchNewTableHelper.switchRemoteDatabaseAsync(fieldRequest));
        }

        for (String field : fieldList) {
            Bus11Helper.processDrillDownBaseInfo(field, fieldMap.get(field).get(), fieldDataItemList);
        }

        return response;

    }

    @Override
    public String getMetricName() {
        return "11";
    }
}
