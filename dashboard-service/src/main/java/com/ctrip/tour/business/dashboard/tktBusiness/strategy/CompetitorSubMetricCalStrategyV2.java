package com.ctrip.tour.business.dashboard.tktBusiness.strategy;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ExamineConfigBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBeanV2;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBeanV3;
import org.springframework.scheduling.annotation.Async;

import java.util.List;
import java.util.concurrent.Future;

public interface CompetitorSubMetricCalStrategyV2 {

    //获取竞品子指标指标卡数据
    @Async("subMetricCardExecutor")
    Future<OveaseaSubMetric> getBus105106107SubMetricCardData(TimeFilter timeFilter,
                                                              OverseaMetricInfoBeanV2 metricInfoBean,
                                                              String d,
                                                              String metric,
                                                              String subMetric) throws Exception;


    //获取竞品子指标趋势线数据
    GetOverseaTrendLineDataV2ResponseType getBus105106107SubTrendLineData(GetOverseaTrendLineDataV2RequestType request,
                                                                          String d,
                                                                          List<String> timeList) throws Exception;


    //获取竞品子指标下钻基础数据
    GetOverseaDrillDownBaseInfoV2ResponseType getBus105106107SubDrillDownBaseInfo(GetOverseaDrillDownBaseInfoV2RequestType request,
                                                                                String d,
                                                                                  OverseaMetricInfoBean metricInfoBean) throws Exception;


    //获取竞品子指标下钻数据
    GetOverseaTableDataV2ResponseType getBus105106107SubTableData(GetOverseaTableDataV2RequestType request,
                                                                String d,
                                                                  OverseaMetricInfoBean metricInfoBean) throws Exception;


    //获取子指标的名称
    String getSubMetricName();


}
