package com.ctrip.tour.business.dashboard.grpBusiness.domain.process.data;

import com.ctrip.soa._24922.DSLRequestType;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.AbstractAfterDataProcess;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.model.ResultData;
import com.ctrip.tour.business.dashboard.grpBusiness.service.DepTreeCache;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class AfterDataProcessFilterAchieve extends AbstractAfterDataProcess {
    private static volatile AfterDataProcessFilterAchieve instance;

    public static AbstractAfterDataProcess getInstance() {
        if (instance == null) { // 第一次检查
            synchronized (AfterDataProcessFilterAchieve.class) { // 加锁
                if (instance == null) { // 第二次检查
                    instance = new AfterDataProcessFilterAchieve();
                }
            }
        }
        return instance;
    }


    // 过滤的指标
    static Set<String> checkList = new HashSet<>(Arrays.asList(
            "good_guide_sev_ord_cnt_rate_rank",
            "fitting_nps_foreign_rank",
            "fitting_nps_domestic_rank",
            "self_sev_rate_domestic_rank",
            "self_sev_rate_foreign_rank",
            "price_rate_rank",
            "weighted_platform_score_rank"
    ));

    @Override
    public ResultData process(DSLRequestType dslRequestType, ResultData data) {
        Set<String> filterSet;
        if (data.getData() != null && !data.getData().isEmpty()) {
            filterSet = checkList.stream()
                    .filter(v -> data.getData().get(0).get(v) == null)
                    .flatMap(v -> Stream.of(v, v + "_max", v + "_percentile"))
                    .collect(Collectors.toSet());
        } else {
            filterSet = null;
        }
        // 需要过滤掉
        if (filterSet != null && !filterSet.isEmpty()) {
            data.setMeta(data.getMeta().stream()
                    .filter(v -> !filterSet.contains(v.getIndicatorName()))
                    .collect(Collectors.toList()));
        }
        return data;
    }
}
