package com.ctrip.tour.business.dashboard.tktBusiness.strategy;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ExamineConfigBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBeanV3;
import org.springframework.scheduling.annotation.Async;

import java.util.List;
import java.util.concurrent.Future;

public interface QuantitySubMetricCalStrategyV2 {


    //获取质量成本子指标下钻基础数据
    GetOverseaDrillDownBaseInfoV2ResponseType getBus103SubDrillDownBaseInfo(GetOverseaDrillDownBaseInfoV2RequestType request,
                                                                          String d,
                                                                            OverseaMetricInfoBean metricInfoBean) throws Exception;

    //获取质量成本子指标下钻数据
    GetOverseaTableDataV2ResponseType getBus103SubTableData(GetOverseaTableDataV2RequestType request,
                                                          String d,
                                                            OverseaMetricInfoBean metricInfoBean) throws Exception;


    //获取子指标的名称
    String getSubMetricName();

}
