package com.ctrip.tour.business.dashboard.grpBusiness.domain.process.dsl.diy;

import com.ctrip.soa._24922.DSLRequestType;
import com.ctrip.soa._24922.EnumOperators;
import com.ctrip.soa._24922.WhereCondition;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.AbstractPreDSLProcess;

import java.util.ArrayList;
import java.util.List;


/**
 * 目的地
 */
public class DSLProcessProvNamesPares extends AbstractPreDSLProcess {
    private List<String> provNames;

    public static AbstractPreDSLProcess getInstance(List<String> provNames) {
        DSLProcessProvNamesPares dslProcessLimitSet = new DSLProcessProvNamesPares();
        dslProcessLimitSet.provNames = provNames;
        return dslProcessLimitSet;
    }

    @Override
    public DSLRequestType process(DSLRequestType dsl, EarlyReturn earlyReturn) {
        if (provNames == null || provNames.isEmpty()) {
            return dsl;
        }
        // 初始化
        if (dsl.getWhereCondition() == null) {
            dsl.setWhereCondition(new WhereCondition());
        }
        if (dsl.getWhereCondition().getSubWhereConditions() == null) {
            dsl.getWhereCondition().setSubWhereConditions(new ArrayList<>());
        }

        // 目的地
        WhereCondition provNamesCondition = new WhereCondition();
        dsl.getWhereCondition().getSubWhereConditions().add(provNamesCondition);

        provNamesCondition.setFilterName("dest_area");
        provNamesCondition.setOperators(EnumOperators.IN);
        provNamesCondition.setFilterValues(provNames);
        return dsl;
    }
}
