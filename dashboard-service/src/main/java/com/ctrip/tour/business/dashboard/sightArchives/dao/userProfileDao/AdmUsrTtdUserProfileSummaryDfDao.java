package com.ctrip.tour.business.dashboard.sightArchives.dao.userProfileDao;

import com.ctrip.soa._24922.UserProfileHistogramItem;
import com.ctrip.soa._24922.UserProfilePieChartSegment;
import com.ctrip.soa._24922.UserResidenceItem;
import com.ctrip.soa._27181.PreparedParameterBean;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.TktStarRocksDao;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.ScenicLanguageHelper;
import com.ctrip.tour.business.dashboard.utils.UserUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.Types;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Repository
public class AdmUsrTtdUserProfileSummaryDfDao {

    //用户画像   数仓侧设计文档：http://conf.ctripcorp.com/pages/viewpage.action?pageId=3668691112
    //dw_ticketdb.adm_usr_ttd_viewspot_archives_user_profile_summary_df

    @Autowired
    private TktStarRocksDao tktStarRocksDao;

    public List<Map<String, Object>> queryAdvanceBookingDayList(String queryD,String startDate, String endDate, Long sightId, Boolean needSubSight, Integer dateType) {
        ArrayList<UserProfileHistogramItem> res = new ArrayList<>();

        //group by advance_day_tag  当年
        StringBuilder sql = new StringBuilder("select advance_day_tag," +
                "cast(sum(ttd_ord_suc_cnt) as Integer) as ttd_ord_suc_cnt from adm_usr_ttd_viewspot_archives_user_profile_summary_df");
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendSightId(parameters, sql, sightId, needSubSight);
        appendQueryD(parameters, sql, queryD);
        sql.append(" and advance_day_tag !='unkwn' ");
        appendDateRange(parameters, sql, dateType,startDate, endDate);
        //分组
        sql.append(" group by advance_day_tag");
        //按"当天"、"提前1天"、"提前2天"、"提前3天"、"提前4-7天"、"提前8-14天"、"提前15-31天"、"提前31天以上"排序
        sql.append(" order by case advance_day_tag " +
                "when '当天' then 1 " +  //NOSONAR
                "when '提前1天' then 2 " +  //NOSONAR
                "when '提前2天' then 3 " +//NOSONAR
                "when '提前3天' then 4 " +//NOSONAR
                "when '提前4-7天' then 5 " +//NOSONAR
                "when '提前8-14天' then 6 " +//NOSONAR
                "when '提前15-31天' then 7 " +//NOSONAR
                "when '提前31天以上' then 8 end asc");//NOSONAR
        List<Map<String, Object>> groupbyres = new ArrayList<>();
        try {
            groupbyres = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        if (groupbyres.size() == 0) {
            return new ArrayList<>();
        }
        return groupbyres;
    }

    public List<Map<String,Object>> queryAdvanceBookingDayListOfLastYear(String queryD ,String startDate, String endDate, Long sightId, Boolean needSubSight, Integer dateType) {
        //去年
        StringBuilder lysql = new StringBuilder("select advance_day_tag," +
                "cast(sum(ttd_ord_suc_cnt) as Integer) as ttd_ord_suc_cnt from adm_usr_ttd_viewspot_archives_user_profile_summary_df t1" +
                " inner join v_dim_date t2");
        if(dateType == 2){
            lysql.append(" on t1.order_date = t2.date_lastyear");
        }else {
            lysql.append(" on t1.use_date = t2.date_lastyear");
        }
        List<PreparedParameterBean> lyparameters = new ArrayList<>();
        appendSightId(lyparameters, lysql, sightId, needSubSight);
        lysql.append(" and t1.d = ?");
        lyparameters.add(new PreparedParameterBean(queryD, Types.VARCHAR));
        appendDateRangeOfLastYear(lyparameters, lysql, startDate, endDate);
        //分组
        lysql.append(" group by advance_day_tag");
        List<Map<String,Object>> lygroupbyres = new ArrayList<>();
        try {
            lygroupbyres = tktStarRocksDao.getListResultNew(lysql.toString(), lyparameters);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return lygroupbyres;

    }

    public Integer getTotalttdOrdSucCntLastYear(String queryD, String startDate, String endDate, Long sightId, Boolean needSubSight,
                                                Integer dateType) {
        StringBuilder sql = new StringBuilder("select " +
                "cast(sum(ttd_ord_suc_cnt) as Integer) as ttd_ord_suc_cnt from adm_usr_ttd_viewspot_archives_user_profile_summary_df t1" +
                " inner join v_dim_date t2");//done
        if(dateType == 2){
            sql.append(" on t1.order_date = t2.date_lastyear");
        }else {
            sql.append(" on t1.use_date = t2.date_lastyear");
        }
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendSightId(parameters, sql, sightId, needSubSight);
        sql.append(" and t1.d = ?");
        parameters.add(new PreparedParameterBean(queryD, Types.VARCHAR));
        appendDateRangeOfLastYear(parameters, sql, startDate, endDate);
        List<Map<String,Object>> totalSum = new ArrayList<>();
        try {
            totalSum = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        if(totalSum.size() == 0){
            return 0;
        }
        return (Integer)totalSum.get(0).get("ttd_ord_suc_cnt");
    }

    public static List<Map<String, Object>> mergeLists(List<Map<String, Object>> currentYearList, List<Map<String, Object>> lastYearList) {
        List<Map<String, Object>> mergedList = new ArrayList<>();

        for (Map<String, Object> currentYearMap : currentYearList) {
            String currentName = (String) currentYearMap.get("advance_day_tag");

            for (Map<String, Object> lastYearMap : lastYearList) {
                String lastYearName = (String) lastYearMap.get("advance_day_tag");

                if (currentName.equals(lastYearName)) {
                    Map<String, Object> mergedMap = new HashMap<>();
                    mergedMap.put("advance_day_tag", lastYearName);
                    // 复制当前年份的指标
                    mergedMap.put("ttd_ord_suc_cnt",currentYearMap.get("ttd_ord_suc_cnt"));
                    mergedMap.put("ttd_ord_suc_cnt_last_year",lastYearMap.get("ttd_ord_suc_cnt"));
                    mergedList.add(mergedMap);
                    break;
                }
            }
        }

        return mergedList;
    }

    private void appendDateRangeOfLastYear(List<PreparedParameterBean> lyparameters, StringBuilder sql, String startDate, String endDate) {
        sql.append(" and t2.date_solar between ? and ?");
        lyparameters.add(new PreparedParameterBean(startDate, Types.VARCHAR));
        lyparameters.add(new PreparedParameterBean(endDate, Types.VARCHAR));
    }


    public List<Map<String, Object>> queryUserAgeGroupList(String queryD,String startDate, String endDate, Long sightId, Boolean needSubSight,Integer dateType) {

        //group by user_age_group
        StringBuilder sql = new StringBuilder("select user_age_tag," +
                "cast(sum(ttd_ord_suc_cnt) as Integer) as ttd_ord_suc_cnt from adm_usr_ttd_viewspot_archives_user_profile_summary_df");
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendSightId(parameters, sql, sightId, needSubSight);
        appendQueryD(parameters, sql, queryD);
        sql.append(" and user_age_tag !='unkwn' ");
        appendDateRange(parameters, sql, dateType,startDate, endDate);
        //分组
        sql.append(" group by user_age_tag");
//        sql.append(" order by user_age_tag asc");
        //按"18岁以下"、"18-24岁"、"25-29岁"、"30-34岁"、"35-39岁"、"40-44岁"、"45-49岁"、"50-59岁"、"60岁以上"排序
        sql.append(" order by case user_age_tag " +
                "when '18岁以下' then 1 " + //NOSONAR
                "when '18-24岁' then 2 " + //NOSONAR
                "when '25-29岁' then 3 " + //NOSONAR
                "when '30-34岁' then 4 " + //NOSONAR
                "when '35-39岁' then 5 " + //NOSONAR
                "when '40-44岁' then 6 " + //NOSONAR
                "when '45-49岁' then 7 " + //NOSONAR
                "when '50-59岁' then 8 " + //NOSONAR
                "when '60岁以上' then 9 end asc"); //NOSONAR

        List<Map<String, Object>> groupbyres = new ArrayList<>();
        try {
            groupbyres = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        if (groupbyres.size() == 0) {
            return new ArrayList<>();
        }

        return groupbyres;
    }



    public List<UserProfilePieChartSegment> querycustomerLevelSegment(String queryD,String startDate, String endDate, Long sightId, Boolean needSubSight, Integer dateType) {
        StringBuilder sql = new StringBuilder("select user_vip_level_id,user_vip_level_name," +
                "cast(sum(ttd_ord_suc_cnt) as Integer) as ttd_ord_suc_cnt from adm_usr_ttd_viewspot_archives_user_profile_summary_df");
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendSightId(parameters, sql, sightId, needSubSight);
        appendQueryD(parameters, sql, queryD);
        appendDateRange(parameters, sql,dateType, startDate, endDate);
        sql.append(" and user_vip_level_name !='unkwn' ");
        sql.append(" group by user_vip_level_id,user_vip_level_name");
        sql.append(" order by user_vip_level_id asc");
        List<Map<String,Object>> groupbyres = new ArrayList<>();
        try {
            groupbyres = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        if(groupbyres.size() == 0){
            return new ArrayList<>();
        }

        ///////////总数
        Integer totalNums = getTotalttdOrdSucCnt(queryD,startDate, endDate, sightId, needSubSight,dateType,"user_vip_level_name");

        List<UserProfilePieChartSegment> res = new ArrayList<>();
        for(Map<String,Object> map : groupbyres){
            UserProfilePieChartSegment userProfilePieChartSegment = new UserProfilePieChartSegment();
            userProfilePieChartSegment.setName(ScenicLanguageHelper.getMultiLanguage((String) map.get("user_vip_level_name"), UserUtil.getVbkLocaleForScenic()));
            userProfilePieChartSegment.setValue((Integer)map.get("ttd_ord_suc_cnt")*1.0/totalNums);
            userProfilePieChartSegment.setCount(((Integer)map.get("ttd_ord_suc_cnt")).longValue());
            res.add(userProfilePieChartSegment);
        }
        return res;

    }

    public List<UserProfilePieChartSegment> queryUserTypeSegment(String queryD,String startDate, String endDate, Long sightId, Boolean needSubSight, Integer dateType) {
        StringBuilder sql = new StringBuilder("select user_travel_type_tag," +
                "cast(sum(ttd_ord_suc_cnt) as Integer) as ttd_ord_suc_cnt from adm_usr_ttd_viewspot_archives_user_profile_summary_df");
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendSightId(parameters, sql, sightId, needSubSight);
        appendQueryD(parameters, sql, queryD);
        appendDateRange(parameters, sql,dateType, startDate, endDate);
        sql.append(" and user_travel_type_tag !='unkwn' ");
        sql.append(" group by user_travel_type_tag");
        List<Map<String,Object>> groupbyres = new ArrayList<>();
        try {
            groupbyres = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        if(groupbyres.size() == 0){
            return new ArrayList<>();
        }

        ///////////总数
        Integer totalNums = getTotalttdOrdSucCnt(queryD,startDate, endDate, sightId, needSubSight,dateType,"user_travel_type_tag");

        List<UserProfilePieChartSegment> res = new ArrayList<>();
        for(Map<String,Object> map : groupbyres){
            UserProfilePieChartSegment userProfilePieChartSegment = new UserProfilePieChartSegment();
            userProfilePieChartSegment.setName(ScenicLanguageHelper.getMultiLanguage((String) map.get("user_travel_type_tag"), UserUtil.getVbkLocaleForScenic()));
            userProfilePieChartSegment.setValue((Integer)map.get("ttd_ord_suc_cnt")*1.0/totalNums);
            userProfilePieChartSegment.setCount(((Integer)map.get("ttd_ord_suc_cnt")).longValue());
            res.add(userProfilePieChartSegment);
        }
        return res;
    }

    public List<UserProfilePieChartSegment> queryLocalAnalysisSegment(String queryD,String startDate, String endDate, Long sightId, Boolean needSubSight, Integer dateType) {
        StringBuilder sql = new StringBuilder("select this_place_type," +
                "cast(sum(ttd_ord_suc_cnt) as Integer) as ttd_ord_suc_cnt from adm_usr_ttd_viewspot_archives_user_profile_summary_df");
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendSightId(parameters, sql, sightId, needSubSight);
        appendQueryD(parameters, sql, queryD);
        appendDateRange(parameters, sql,dateType, startDate, endDate);
        sql.append(" and this_place_type !='unkwn' ");
        sql.append(" group by this_place_type");
        List<Map<String,Object>> groupbyres = new ArrayList<>();
        try {
            groupbyres = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        if(groupbyres.size() == 0){
            return new ArrayList<>();
        }

        ///////////总数
        Integer totalNums = getTotalttdOrdSucCnt(queryD,startDate, endDate, sightId, needSubSight,dateType,"this_place_type");

        List<UserProfilePieChartSegment> res = new ArrayList<>();
        for(Map<String,Object> map : groupbyres){
            UserProfilePieChartSegment userProfilePieChartSegment = new UserProfilePieChartSegment();
            userProfilePieChartSegment.setName(ScenicLanguageHelper.getMultiLanguage((String) map.get("this_place_type"), UserUtil.getVbkLocaleForScenic()));
            userProfilePieChartSegment.setValue((Integer)map.get("ttd_ord_suc_cnt")*1.0/totalNums);
            userProfilePieChartSegment.setCount(((Integer)map.get("ttd_ord_suc_cnt")).longValue());
            res.add(userProfilePieChartSegment);
        }
        return res;
    }

    public List<UserProfilePieChartSegment> queryDomesticForeignSegment(String queryD, String startDate, String endDate, Long sightId, Boolean needSubSight, Integer dateType) {
        StringBuilder sql = new StringBuilder("select permanent_domain," +
                "cast(sum(ttd_ord_suc_cnt) as Integer) as ttd_ord_suc_cnt from adm_usr_ttd_viewspot_archives_user_profile_summary_df");
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendSightId(parameters, sql, sightId, needSubSight);
        appendQueryD(parameters, sql, queryD);
        appendDateRange(parameters, sql,dateType, startDate, endDate);
        sql.append(" and permanent_domain !='unkwn' ");
        sql.append(" group by permanent_domain");
        List<Map<String,Object>> groupbyres = new ArrayList<>();
        try {
            groupbyres = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        if(groupbyres.size() == 0){
            return new ArrayList<>();
        }

        ///////////总数
        Integer totalNums = getTotalttdOrdSucCnt(queryD,startDate, endDate, sightId, needSubSight,dateType,"permanent_domain");

        List<UserProfilePieChartSegment> res = new ArrayList<>();
        for(Map<String,Object> map : groupbyres){
            UserProfilePieChartSegment userProfilePieChartSegment = new UserProfilePieChartSegment();
            userProfilePieChartSegment.setName(ScenicLanguageHelper.getMultiLanguage((String) map.get("permanent_domain"), UserUtil.getVbkLocaleForScenic()));
            userProfilePieChartSegment.setValue((Integer)map.get("ttd_ord_suc_cnt")*1.0/totalNums);
            userProfilePieChartSegment.setCount(((Integer)map.get("ttd_ord_suc_cnt")).longValue());
            res.add(userProfilePieChartSegment);
        }
        return res;
    }

    public Integer getTotalttdOrdSucCnt(String queryD,String startDate, String endDate, Long sightId, Boolean needSubSight,
                                        Integer dateType, String dim) {
        StringBuilder sql = new StringBuilder("select " +
                "cast(sum(ttd_ord_suc_cnt) as Integer) as ttd_ord_suc_cnt from adm_usr_ttd_viewspot_archives_user_profile_summary_df");
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendSightId(parameters, sql, sightId, needSubSight);
        appendQueryD(parameters, sql, queryD);
        appendDateRange(parameters, sql,dateType, startDate, endDate);
        if (StringUtils.isNotBlank(dim)){
            sql.append(" and ").append(dim).append(" !='unkwn' ");
        }
        List<Map<String,Object>> totalSum = new ArrayList<>();
        try {
            totalSum = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        if(totalSum.size() == 0){
            return 0;
        }
        return (Integer)totalSum.get(0).get("ttd_ord_suc_cnt");
    }

    private void appendSightId(List<PreparedParameterBean> parameters, StringBuilder sql, Long sightId, Boolean needSubSight){
        if(needSubSight){
            sql.append(" where parent_viewspot_id = ?");
        }else {
            sql.append(" where viewspot_id = ?");
        }
        parameters.add(new PreparedParameterBean(String.valueOf(sightId), Types.BIGINT));
    }

    private void appendQueryD(List<PreparedParameterBean> parameters, StringBuilder sql, String queryD){
        sql.append(" and d = ?");
        parameters.add(new PreparedParameterBean(queryD, Types.VARCHAR));
    }

    private void appendDateRange(List<PreparedParameterBean> parameters, StringBuilder sql, Integer dateType, String startDate, String endDate){

        if(dateType == 2){
            sql.append(" and order_date between ? and ?");
        }else {
            sql.append(" and use_date between ? and ?");
        }
        parameters.add(new PreparedParameterBean(startDate, Types.VARCHAR));
        parameters.add(new PreparedParameterBean(endDate, Types.VARCHAR));

    }

    public List<UserResidenceItem> queryCityList(String queryD,String startDate, String endDate, Long sightId, Boolean needSubSight,
                                                 Integer dateType) {
        StringBuilder sql = new StringBuilder("select permanent_city_id,permanent_province_id,permanent_city_name,permanent_province_name," +
                "cast(sum(ttd_ord_suc_cnt) as Integer) as ttd_ord_suc_cnt from adm_usr_ttd_viewspot_archives_user_profile_summary_df");
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendSightId(parameters, sql, sightId, needSubSight);
        appendQueryD(parameters, sql, queryD);
        appendDateRange(parameters, sql,dateType, startDate, endDate);
        sql.append(" and permanent_city_name !='unkwn' ");
        sql.append(" group by permanent_city_id,permanent_province_id,permanent_city_name,permanent_province_name");
        sql.append(" order by ttd_ord_suc_cnt desc");
        List<Map<String,Object>> groupbyres = new ArrayList<>();
        try {
            groupbyres = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        if(groupbyres.size() == 0){
            return new ArrayList<>();
        }

        ///////////总数
        Integer totalNums = getTotalttdOrdSucCnt(queryD,startDate, endDate, sightId, needSubSight,dateType,"permanent_city_name");

        List<UserResidenceItem> res = new ArrayList<>();
        for(Map<String,Object> map : groupbyres){
            UserResidenceItem userResidenceItem = new UserResidenceItem();
            userResidenceItem.setCityId((Long) map.get("permanent_city_id"));
            userResidenceItem.setProvinceId((Long) map.get("permanent_province_id"));
            userResidenceItem.setCityName((String) map.get("permanent_city_name"));
            userResidenceItem.setTranslateCityName((String) map.get("permanent_city_name"));
            userResidenceItem.setProvinceName((String) map.get("permanent_province_name"));
            userResidenceItem.setTranslateProvinceName((String) map.get("permanent_province_name"));
            userResidenceItem.setOrderCountPercentage((Integer)map.get("ttd_ord_suc_cnt")*1.0/totalNums);
            res.add(userResidenceItem);
        }
        return res;

    }

    public List<UserResidenceItem> queryProvinceList(String queryD, String startDate, String endDate, Long sightId, Boolean needSubSight,
                                                     Integer dateType) {
        StringBuilder sql = new StringBuilder("select permanent_province_id,permanent_province_name," +
                "cast(sum(ttd_ord_suc_cnt) as Integer) as ttd_ord_suc_cnt from adm_usr_ttd_viewspot_archives_user_profile_summary_df");
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendSightId(parameters, sql, sightId, needSubSight);
        appendQueryD(parameters, sql, queryD);
        appendDateRange(parameters, sql,dateType, startDate, endDate);
        sql.append(" and permanent_province_name !='unkwn' ");
        sql.append(" group by permanent_province_id,permanent_province_name");
        sql.append(" order by ttd_ord_suc_cnt desc");
        List<Map<String,Object>> groupbyres = new ArrayList<>();
        try {
            groupbyres = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        if(groupbyres.size() == 0){
            return new ArrayList<>();
        }

        ///////////总数
        Integer totalNums = getTotalttdOrdSucCnt(queryD,startDate, endDate, sightId, needSubSight,dateType,"permanent_province_name");
        List<UserResidenceItem> res = new ArrayList<>();
        for(Map<String,Object> map : groupbyres) {
            UserResidenceItem userResidenceItem = new UserResidenceItem();
            userResidenceItem.setProvinceId((Long) map.get("permanent_province_id"));
            userResidenceItem.setProvinceName((String) map.get("permanent_province_name"));
            userResidenceItem.setTranslateProvinceName((String) map.get("permanent_province_name"));
            userResidenceItem.setOrderCountPercentage((Integer) map.get("ttd_ord_suc_cnt") * 1.0 / totalNums);
            res.add(userResidenceItem);
        }
        return res;
    }


    public List<UserResidenceItem> queryCountryList(String queryD,String startDate, String endDate, Long sightId, Boolean needSubSight,
                                                    Integer dateType) {
        StringBuilder sql = new StringBuilder("select permanent_country_id,permanent_country_name," +
                "cast(sum(ttd_ord_suc_cnt) as Integer) as ttd_ord_suc_cnt from adm_usr_ttd_viewspot_archives_user_profile_summary_df");
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendSightId(parameters, sql, sightId, needSubSight);
        appendQueryD(parameters, sql, queryD);
        appendDateRange(parameters, sql,dateType, startDate, endDate);
        sql.append(" and permanent_country_name !='unkwn' ");
        sql.append(" group by permanent_country_id,permanent_country_name");
        sql.append(" order by ttd_ord_suc_cnt desc");
        List<Map<String,Object>> groupbyres = new ArrayList<>();
        try {
            groupbyres = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        if(groupbyres.size() == 0){
            return new ArrayList<>();
        }

        ///////////总数
        Integer totalNums = getTotalttdOrdSucCnt(queryD,startDate, endDate, sightId, needSubSight,dateType,"permanent_country_name");
        List<UserResidenceItem> res = new ArrayList<>();
        for(Map<String,Object> map : groupbyres) {
            UserResidenceItem userResidenceItem = new UserResidenceItem();
            userResidenceItem.setCountryId((Long) map.get("permanent_country_id"));
            userResidenceItem.setCountryName((String) map.get("permanent_country_name"));
            userResidenceItem.setTranslateCountryName((String) map.get("permanent_country_name"));
            userResidenceItem.setOrderCountPercentage((Integer) map.get("ttd_ord_suc_cnt") * 1.0 / totalNums);
            res.add(userResidenceItem);
        }
        return res;
    }

    public List<UserProfilePieChartSegment> queryLocalAnalysisSegmentOfLastYear(String queryD ,String startDate, String endDate, Long sightId, Boolean needSubSight,
                                                                                   Integer dateType) {
        StringBuilder sql = new StringBuilder("select this_place_type," +
                "cast(sum(ttd_ord_suc_cnt) as Integer) as ttd_ord_suc_cnt from adm_usr_ttd_viewspot_archives_user_profile_summary_df t1" +
                " inner join v_dim_date t2");
        if(dateType == 2){
            sql.append(" on t1.order_date = t2.date_lastyear");
        }else {
            sql.append(" on t1.use_date = t2.date_lastyear");
        }
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendSightId(parameters, sql, sightId, needSubSight);
        sql.append(" and t1.d = ?");
        parameters.add(new PreparedParameterBean(queryD, Types.VARCHAR));
        appendDateRangeOfLastYear(parameters, sql, startDate, endDate);
        sql.append(" group by this_place_type");
        List<Map<String,Object>> groupbyres = new ArrayList<>();
        try {
            groupbyres = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        if(groupbyres.size() == 0){
            return new ArrayList<>();
        }

        ///////////总数
        Integer totalNums = getTotalttdOrdSucCntLastYear(queryD,startDate, endDate, sightId, needSubSight,dateType);

        List<UserProfilePieChartSegment> res = new ArrayList<>();
        for(Map<String,Object> map : groupbyres){
            UserProfilePieChartSegment userProfilePieChartSegment = new UserProfilePieChartSegment();
            userProfilePieChartSegment.setName(ScenicLanguageHelper.getMultiLanguage((String) map.get("this_place_type"), UserUtil.getVbkLocaleForScenic()));
            if(map.get("ttd_ord_suc_cnt")!=null && totalNums>0) {
                userProfilePieChartSegment.setValue((Integer) map.get("ttd_ord_suc_cnt") * 1.0 / totalNums);
            }
            res.add(userProfilePieChartSegment);
        }
        return res;
    }

    public List<UserProfilePieChartSegment> queryDomesticForeignSegmentOfLastYear(String queryD,String startDate, String endDate, Long sightId, Boolean needSubSight,
                                                                                     Integer dateType) {
        StringBuilder sql = new StringBuilder("select permanent_domain," +
                "cast(sum(ttd_ord_suc_cnt) as Integer) as ttd_ord_suc_cnt from adm_usr_ttd_viewspot_archives_user_profile_summary_df t1" +
                " inner join v_dim_date t2");
        if(dateType == 2){
            sql.append(" on t1.order_date = t2.date_lastyear");
        }else {
            sql.append(" on t1.use_date = t2.date_lastyear");
        }
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendSightId(parameters, sql, sightId, needSubSight);
        sql.append(" and t1.d = ?");
        parameters.add(new PreparedParameterBean(queryD, Types.VARCHAR));
        appendDateRangeOfLastYear(parameters, sql, startDate, endDate);
        sql.append(" group by permanent_domain");
        List<Map<String,Object>> groupbyres = new ArrayList<>();
        try {
            groupbyres = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        if(groupbyres.size() == 0){
            return new ArrayList<>();
        }

        ///////////总数
        Integer totalNums = getTotalttdOrdSucCntLastYear(queryD,startDate, endDate, sightId, needSubSight,dateType);

        List<UserProfilePieChartSegment> res = new ArrayList<>();
        for(Map<String,Object> map : groupbyres){
            UserProfilePieChartSegment userProfilePieChartSegment = new UserProfilePieChartSegment();
            userProfilePieChartSegment.setName(ScenicLanguageHelper.getMultiLanguage((String) map.get("permanent_domain"), UserUtil.getVbkLocaleForScenic()));
            if (map.get("ttd_ord_suc_cnt")!=null && totalNums>0) {
                userProfilePieChartSegment.setValue((Integer) map.get("ttd_ord_suc_cnt") * 1.0 / totalNums);
            }
            res.add(userProfilePieChartSegment);
        }
        return res;
    }



}
