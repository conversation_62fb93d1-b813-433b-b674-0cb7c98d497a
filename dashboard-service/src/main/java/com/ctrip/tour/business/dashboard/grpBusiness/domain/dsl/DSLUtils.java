package com.ctrip.tour.business.dashboard.grpBusiness.domain.dsl;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.model.ResultData;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class DSLUtils {
    /**
     * 初始化whereCondition
     *
     * @param key       whereCondition filterName
     * @param operators whereCondition operators
     * @param v         whereCondition filterValue
     * @return whereCondition
     */
    public static WhereCondition initWhereCondition(String key, EnumOperators operators, Object v) {
        WhereCondition whereCondition = new WhereCondition();
        whereCondition.setFilterName(key);
        if (v instanceof List) {
            List<String> sValues = ((List<?>) v).stream().map(Object::toString).collect(Collectors.toList());
            whereCondition.setFilterValues(sValues);
        } else if (v instanceof Object[]) {
            List<String> sValues = Arrays.stream((Object[]) v).map(Object::toString).collect(Collectors.toList());
            whereCondition.setFilterValues(sValues);
        } else {
            List<String> sValues = Stream.of(v).map(Object::toString).collect(Collectors.toList());
            whereCondition.setFilterValues(sValues);
        }
        whereCondition.setOperators(operators);
        return whereCondition;
    }


    /**
     * 获取whereConditionMap
     *
     * @param whereCondition dsl whereCondition输入
     * @return map
     */
    public static Map<String, WhereCondition> getWhereConditionMap(WhereCondition whereCondition) {
        if (whereCondition == null) {
            return new HashMap<>();
        }
        Map<String, WhereCondition> ret = new HashMap<>();
        if (whereCondition.getSubWhereConditions() == null || whereCondition.getSubWhereConditions().isEmpty()) {
            ret.put(whereCondition.getFilterName(), whereCondition);
            return ret;
        }
        for (WhereCondition subWhereCondition : whereCondition.getSubWhereConditions()) {
            ret.putAll(getWhereConditionMap(subWhereCondition));
        }
        return ret;
    }

    /**
     * 打平where condition
     *
     * @param dsl dsl 输入
     * @return map
     */
    public static Map<String, Object> getWhereConditionFlatmap(DSLRequestType dsl) {
        if (dsl.getWhereCondition() == null) {
            return new HashMap<>();
        }
        Map<String, WhereCondition> conditionMap = getWhereConditionMap(dsl.getWhereCondition());
        Map<String, Object> ret = new HashMap<>();
        for (Map.Entry<String, WhereCondition> item : conditionMap.entrySet()) {
            HashMap<String, Object> currentMap = new HashMap<>();
            currentMap.put("filterName", item.getValue().getFilterName());
            currentMap.put("filterValues", item.getValue().getFilterValues());
            currentMap.put("operators", item.getValue().getSubWhereConditions());
            ret.put(item.getKey(), currentMap);
        }
        return ret;
    }

    /**
     * 打平where condition
     *
     * @param whereCondition dsl whereCondition输入
     * @return map
     */
    public static Map<String, Object> getWhereConditionFlatmap(WhereCondition whereCondition) {
        Map<String, WhereCondition> conditionMap = getWhereConditionMap(whereCondition);
        Map<String, Object> ret = new HashMap<>();
        for (Map.Entry<String, WhereCondition> item : conditionMap.entrySet()) {
            HashMap<String, Object> currentMap = new HashMap<>();
            currentMap.put("filterName", item.getValue().getFilterName());
            currentMap.put("filterValues", item.getValue().getFilterValues());
            currentMap.put("operators", item.getValue().getSubWhereConditions());
            ret.put(item.getKey(), currentMap);
        }
        return ret;
    }


    /**
     * 深拷贝whereCondition
     *
     * @param whereCondition dsl whereCondition输入
     * @return 输出深拷贝
     */
    public static WhereCondition copyWhereCondition(WhereCondition whereCondition) {
        WhereCondition ret = new WhereCondition();
        if (whereCondition.getFilterName() != null) {
            ret.setFilterName(whereCondition.getFilterName());
        }
        if (whereCondition.getOperators() != null) {
            ret.setOperators(whereCondition.getOperators());
        }
        if (whereCondition.getFilterValues() != null) {
            ret.setFilterValues(new ArrayList<>(whereCondition.getFilterValues()));
        }
        if (whereCondition.getSubWhereConditions() == null) {
            return ret;
        }
        List<WhereCondition> subRet = new ArrayList<>();
        ret.setWhereConditionOperator(whereCondition.getWhereConditionOperator());
        ret.setSubWhereConditions(subRet);
        for (WhereCondition subWhereCondition : whereCondition.getSubWhereConditions()) {
            subRet.add(copyWhereCondition(subWhereCondition));
        }
        return ret;
    }


    /**
     * 复制DSL
     *
     * @param dsl dsl
     * @return 复制的dsl
     */
    public static DSLRequestType copyDSL(DSLRequestType dsl) {
        DSLRequestType dslCP = new DSLRequestType();
        if (dsl.getIndicators() != null) {
            dslCP.setIndicators(new ArrayList<>(dsl.getIndicators()));
        }
        if (dsl.getWhereCondition() != null) {
            dslCP.setWhereCondition(copyWhereCondition(dsl.getWhereCondition()));
        }
        if (dsl.getGroupBy() != null) {
            dslCP.setGroupBy(new ArrayList<>(dsl.getGroupBy()));
        }
        if (dsl.getOrderBy() != null) {
            dslCP.setOrderBy(dsl.getOrderBy().stream().map(v -> new OrderBy(v.getOrderFiled(), v.getOrderType())).collect(Collectors.toList()));
        }
        if (dsl.getLimit() != null) {
            dslCP.setLimit(new Limit(dsl.getLimit().start, dsl.getLimit().size));
        }
        if (dsl.getCompareConfig() != null) {
            List<CompareConfig> compareConfigs = dsl.getCompareConfig().stream()
                    .map(
                            v -> new CompareConfig(v.getCompareIndicatorName(), v.getCompareDate(), v.getPrefix(), v.getCurDate())
                    ).collect(Collectors.toList());
            dslCP.setCompareConfig(compareConfigs);
        }
        if (dsl.getExtra() != null) {
            dslCP.setExtra(new HashMap<>(dsl.getExtra()));
        }

        return dslCP;
    }


    /**
     * 格式化标准输出
     *
     * @param resultData dsl查询输入
     * @return request type标准数据
     */
    public static DSLResponseType formatResponseType(ResultData resultData) {
        // meta
        DSLResponseType dslResponseType = new DSLResponseType();
        if (resultData == null) {
            dslResponseType.setMeta(new ArrayList<>());
            dslResponseType.setData(new ArrayList<>());
            return dslResponseType;
        }
        List<Meta> meta = resultData.getMeta().stream().map(
                v -> {
                    Meta item = new Meta();
                    item.setIndicatorName(v.getIndicatorName());
                    item.setIndicatorNameCN(v.getIndicatorNameCN());
                    if (v.getIndicatorType() != null) {
                        switch (v.getIndicatorType()) {
                            case "double":
                                item.setIndicatorType(IndicatorType.Double);
                                break;
                            case "long":
                                item.setIndicatorType(IndicatorType.Long);
                                break;
                            default:
                                item.setIndicatorType(IndicatorType.String);
                        }
                    }
                    item.setIndicatorFormat(v.getIndicatorFormat());
                    item.setIsDimension(v.isIndicatorIsDimension());
                    item.setIsSupportSorting(v.isIndicatorIsSupportSorting());
                    item.setIndicatorKey(v.getIndicatorKey());
                    return item;
                }
        ).collect(Collectors.toList());
        dslResponseType.setMeta(meta);


        ObjectMapper mapper = new ObjectMapper();
        // data
        List<Data> data = resultData.getData().stream().map(
                v -> {
                    Data item = new Data();
                    item.setData(new HashMap<>());
                    v.forEach((key, value) -> {
                        if (value == null) {
                            item.getData().put(key, null);
                        } else if (value instanceof Map || value instanceof List) {
                            String jsonString;
                            try {
                                jsonString = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(value);
                            } catch (JsonProcessingException e) {
                                throw new RuntimeException(e);
                            }
                            item.getData().put(key, jsonString);
                        } else if (value instanceof Double) {
                            item.getData().put(key, BigDecimal.valueOf((Double) value).toString());
                        } else if (value instanceof Float) {
                            item.getData().put(key, BigDecimal.valueOf((Float) value).toString());
                        } else if (value instanceof Long) {
                            item.getData().put(key, new BigDecimal((Long) value).toString());
                        } else if (value instanceof Integer) {
                            item.getData().put(key, new BigDecimal((Integer) value).toString());
                        } else {
                            item.getData().put(key, value.toString());
                        }
                    });
                    return item;
                }
        ).collect(Collectors.toList());
        dslResponseType.setData(data);
        return dslResponseType;
    }


    // 替换DSL where条件的value值
    public static void replaceCondition(WhereCondition condition, WhereCondition whereCondition) {
        if (condition == null) {
            return;
        }
        if (condition.getFilterName() != null && condition.getFilterName().equals(whereCondition.getFilterName())) {
            condition.filterValues = new ArrayList<>(whereCondition.filterValues);
            return;
        }
        for (int i = 0; condition.getSubWhereConditions() != null && i < condition.getSubWhereConditions().size(); i++) {
            replaceCondition(condition.getSubWhereConditions().get(i), whereCondition);
        }
    }


    /**
     * 获取whereCondition by filterName
     *
     * @param dsl        dsl
     * @param filterName filterName
     * @return whereCondition
     */
    public static WhereCondition getWhereConditionByFilterName(DSLRequestType dsl, String filterName) {
        Map<String, WhereCondition> whereConditionMap = getWhereConditionMap(dsl.getWhereCondition());
        return whereConditionMap.get(filterName);
    }
}
