package com.ctrip.tour.business.dashboard.biz.impl

import com.ctrip.soa._24922.PushSelfQualityOfServiceToBusinessRequestType
import com.ctrip.tour.business.dashboard.tktBusiness.biz.impl.SelfServiceQualityPushBizImpl
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardEmployeeInfoDao
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardVendorMappingDao
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardVendorSalesDao
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardEmployeeInfo
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardVendorMapping
import com.ctrip.tour.business.dashboard.utils.MapperUtil
import com.ctrip.tour.rights.client.TourRightsServiceClient
import com.ctrip.tour.rights.client.WbCreateTaskRequestType
import com.ctrip.tour.rights.client.WbCreateTaskResponseType
import com.ctrip.tour.vbkProviderSvc.service.QuerySummaryProviderListResponseType
import com.ctrip.tour.vbkProviderSvc.service.VbkProviderServiceClient
import com.ctrip.tour.vendor.providersvc.soa.v1.service.VendorProviderServiceClient
import com.ctrip.tour.vendor.providersvc.soa.v1.service.type.QuerySelfServiceKpiDataResponseType
import spock.lang.Specification

class SelfServiceQualityPushBizImplTest extends Specification {

    def pushClient = Mock(TourRightsServiceClient)

    def providerServiceClient = Mock(VendorProviderServiceClient)

    def vbkClient = Mock(VbkProviderServiceClient)

    def salesDao = Mock(BusinessDashboardVendorSalesDao)

    def mappingDao = Mock(BusinessDashboardVendorMappingDao)

    def employeeInfoDao = Mock(BusinessDashboardEmployeeInfoDao)

    def config = Mock(RemoteConfig)

    def impl = new SelfServiceQualityPushBizImpl(pushClient: pushClient, providerServiceClient: providerServiceClient, vbkClient: vbkClient, salesDao: salesDao, mappingDao: mappingDao, employeeInfoDao: employeeInfoDao, config: config)

    def "handleResponse"() {
        expect: "call && verify"
        impl.handleResponse(request, response)

        where: "多条件验证"
        response    |       request
        MapperUtil.str2Obj("{\"responseStatus\":{\"ack\":\"Success\",\"errors\":[],\"extension\":[]},\"taskId\":2674792}", WbCreateTaskResponseType.class)            |       MapperUtil.str2Obj("", WbCreateTaskRequestType.class)
        MapperUtil.str2Obj("{\"responseStatus\":{\"ack\":\"Success\",\"errors\":[],\"extension\":[]},\"taskId\":null}", WbCreateTaskResponseType.class)            |       MapperUtil.str2Obj("", WbCreateTaskRequestType.class)
    }

//    def "onFailureByCallBack"(){
//        expect: "call"
//        def request = MapperUtil.str2Obj("{\"taskName\":\"1月自服务红线考核供应商预警\",\"taskType\":\"TASK_QUALITY_SELFSERVICE_01\",\"taskPropertyList\":[{\"key\":\"EVENT_QUALITY_SELFSERVICE_01\",\"value\":\"[{\\\"processoreidlist\\\":\\\"G09488\\\",\\\"content\\\":\\\"<!DOCTYPE html>\\\\n<html lang=\\\\\\\"en\\\\\\\">\\\\n<head>\\\\n    <meta charset=\\\\\\\"UTF-8\\\\\\\">\\\\n  </head>\\\\n<body>\\\\n    <p>您名下供应商在1月份的考核中低于平台预警考核线标准，已被预警。平台预警线：供应商的120s回复率的预警线为0.8，差评率预警线为0.7</p>\\\\n    <table  style=\\\\\\\"border-collapse:collapse;\\\\\\\" border=\\\\\\\"1\\\\\\\" cellspacing=\\\\\\\"0\\\\\\\">\\\\n        <thread>\\\\n<th>供应商ID</th>\\\\n<th>供应商名称</th>\\\\n<th>120S回复率</th>\\\\n<th>差评率</th>\\\\n<th>总会话量</th>\\\\n<th>12月GMV</th></thread>\\\\n        <tbody>\\\\n<tr>\\\\n<td>15649</td>\\\\n<td>丽江携程旅行社有限公司</td>\\\\n<td>0.9975</td>\\\\n<td>0.0667</td>\\\\n<td>2535</td>\\\\n<td>1172104.96</td></tr></tbody>    </table>\\\\n</body>\\\\n</html>\\\"}]\"}]}", WbCreateTaskRequestType.class)
//
//        impl.onFailureByCallBack(request, new Throwable())
//    }



//    def "handleFutureResponse"() {
//        given: "mock param"
//        def request = MapperUtil.str2Obj("{\"taskName\":\"1月自服务红线考核供应商预警\",\"taskType\":\"TASK_QUALITY_SELFSERVICE_01\",\"taskPropertyList\":[{\"key\":\"EVENT_QUALITY_SELFSERVICE_01\",\"value\":\"[{\\\"processoreidlist\\\":\\\"G09488\\\",\\\"content\\\":\\\"<!DOCTYPE html>\\\\n<html lang=\\\\\\\"en\\\\\\\">\\\\n<head>\\\\n    <meta charset=\\\\\\\"UTF-8\\\\\\\">\\\\n  </head>\\\\n<body>\\\\n    <p>您名下供应商在1月份的考核中低于平台预警考核线标准，已被预警。平台预警线：供应商的120s回复率的预警线为0.8，差评率预警线为0.7</p>\\\\n    <table  style=\\\\\\\"border-collapse:collapse;\\\\\\\" border=\\\\\\\"1\\\\\\\" cellspacing=\\\\\\\"0\\\\\\\">\\\\n        <thread>\\\\n<th>供应商ID</th>\\\\n<th>供应商名称</th>\\\\n<th>120S回复率</th>\\\\n<th>差评率</th>\\\\n<th>总会话量</th>\\\\n<th>12月GMV</th></thread>\\\\n        <tbody>\\\\n<tr>\\\\n<td>15649</td>\\\\n<td>丽江携程旅行社有限公司</td>\\\\n<td>0.9975</td>\\\\n<td>0.0667</td>\\\\n<td>2535</td>\\\\n<td>1172104.96</td></tr></tbody>    </table>\\\\n</body>\\\\n</html>\\\"}]\"}]}", WbCreateTaskRequestType.class)
//        def response = MapperUtil.str2Obj("{\"responseStatus\":{\"ack\":\"Success\",\"errors\":[],\"extension\":[]},\"taskId\":2672167}", WbCreateTaskResponseType.class)
//        ListenableFuture<WbCreateTaskResponseType> future = Mock(ListenableFuture.class)
//        future.get() >> response
//
//        when: "call"
//        impl.handleFutureResponse(request, future)
//
//        then: "verify"
//    }


    def "GetVendorInfoList"() {
        given: "mock param"
        def vendorList = MapperUtil.str2List("[11697]", Long.class)
        def startDate = "2024-01-01"

        and: "mock dal"
        providerServiceClient.querySelfServiceKpiData(_) >> MapperUtil.str2Obj(kpiResp, QuerySelfServiceKpiDataResponseType.class)
        vbkClient.querySummaryProviderList(_) >> MapperUtil.str2Obj(infoResp, QuerySummaryProviderListResponseType.class)
        salesDao.getSalesByVendor(_) >> sales

        when: "call"
        def resp = impl.getVendorInfoList(vendorList, startDate)

        then: "verify"
        with(resp){

        }

        where: "多条件查询"
        sales   |   kpiResp     |   infoResp
        368215.11D  |   "{\"providerId\":11697,\"totalCount\":1,\"contractServiceKpiList\":[{\"selfServiceKpiDataId\":270866,\"cooperateBusinessLineIdList\":[7],\"kpiType\":\"month\",\"weekInputId\":0,\"ttdTotalVendorSessionCnt\":3018,\"ttdTimelyResponseRate\":0.8018,\"ttdTimelyResponseRateLine\":0.6,\"ttdTimelyResponseRateOperator\":\"≥\",\"ttdTimelyResponseRateIsPass\":\"PASS\",\"ttdTotalReviews\":261,\"ttdNegativeReviewsRate\":0.0421,\"ttdNegativeReviewsRateLine\":0.4,\"ttdNegativeReviewsRateOperator\":\"<\",\"ttdNegativeReviewsRateIsPass\":\"PASS\",\"wholePassStatus\":\"PASS\",\"approveStatus\":\"A\",\"approvalFormId\":0,\"rejectReason\":\"\",\"mainArea\":\"\"}],\"responseStatus\":{\"ack\":\"Success\",\"extension\":[]}}"   |   "{\"responseStatus\":{\"ack\":\"Success\",\"extension\":[]},\"pageSize\":1000,\"totalCount\":1,\"providerInfoList\":[{\"providerId\":11697,\"providerName\":\"北京环球友邻科技有限公司\",\"providerShortName\":\"环球漫游\",\"active\":\"T\",\"directPoiList\":[],\"generalAgentPoiList\":[],\"corporateIdentity\":0}]}"
        null    |   "{\"totalCount\":0}"   |    "{\"totalCount\":0}"
    }

    def "GetBusinessAndVendorMapping"() {
    }

    def "PushMessage"() {
    }

    def "PushSelfQualityOfServiceToBusiness"() {
        given: "mock request"
        def request = MapperUtil.str2Obj(String.format("{\"pushType\":%s,\"ttdTimelyResponseRateLine\":0.8,\"ttdNegativeReviewsRateLine\":0.7,\"currentDate\":\"2024-03-14\",\"closeDays\":30,\"startDate\":\"2024-01-01\",\"vendorList\":[11697,15649]}", pushType), PushSelfQualityOfServiceToBusinessRequestType.class)

        and: "mock dal"
        mappingDao.getBusinessVendorMapping(_) >> MapperUtil.str2List("[{\"vendorId\":11697,\"businessUserCode\":\"liuyixuan\"},{\"vendorId\":15649,\"businessUserCode\":\"xy.zou\"},{\"vendorId\":11697,\"businessUserCode\":\"unkwn\"},{\"vendorId\":11697,\"businessUserCode\":\"gm.he\"},{\"vendorId\":11697,\"businessUserCode\":\"sun_yi\"},{\"vendorId\":15649,\"businessUserCode\":\"mpwl\"},{\"vendorId\":11697,\"businessUserCode\":\"mpwl\"},{\"vendorId\":11697,\"businessUserCode\":\"jmxu\"},{\"vendorId\":11697,\"businessUserCode\":\"yuying_huang\"},{\"vendorId\":15649,\"businessUserCode\":\"cao_sy\"},{\"vendorId\":15649,\"businessUserCode\":\"yj.li\"},{\"vendorId\":11697,\"businessUserCode\":\"pengm\"},{\"vendorId\":15649,\"businessUserCode\":\"zy.yang\"},{\"vendorId\":11697,\"businessUserCode\":\"b.zhou\"},{\"vendorId\":15649,\"businessUserCode\":\"unkwn\"},{\"vendorId\":11697,\"businessUserCode\":\"caox\"},{\"vendorId\":11697,\"businessUserCode\":\"mhcui\"},{\"vendorId\":11697,\"businessUserCode\":\"siyaoxu\"},{\"vendorId\":15649,\"businessUserCode\":\"qkhe\"},{\"vendorId\":15649,\"businessUserCode\":\"jq_yu\"},{\"vendorId\":11697,\"businessUserCode\":\"lalita.sincharoenkun\"},{\"vendorId\":15649,\"businessUserCode\":\"yongqinhe\"},{\"vendorId\":11697,\"businessUserCode\":\"clxu\"},{\"vendorId\":15649,\"businessUserCode\":\"panting\"},{\"vendorId\":11697,\"businessUserCode\":\"husr\"},{\"vendorId\":11697,\"businessUserCode\":\"xrzhang\"},{\"vendorId\":11697,\"businessUserCode\":\"yishuwang\"}]", BusinessDashboardVendorMapping.class)
        employeeInfoDao.getEmployeeCode(_) >> MapperUtil.str2List("[{\"empCode\":\"S60254\",\"domainName\":\"sun_yi\"},{\"empCode\":\"TR000421\",\"domainName\":\"lalita.sincharoenkun\"},{\"empCode\":\"G11550\",\"domainName\":\"qkhe\"},{\"empCode\":\"TR011775\",\"domainName\":\"panting\"},{\"empCode\":\"TR005063\",\"domainName\":\"xy.zou\"},{\"empCode\":\"TR000767\",\"domainName\":\"jq_yu\"},{\"empCode\":\"D02273\",\"domainName\":\"zy.yang\"},{\"empCode\":\"G09488\",\"domainName\":\"yj.li\"},{\"empCode\":\"G14791\",\"domainName\":\"yuying_huang\"},{\"empCode\":\"A00008\",\"domainName\":\"caox\"},{\"empCode\":\"S42397\",\"domainName\":\"b.zhou\"},{\"empCode\":\"S64812\",\"domainName\":\"pengm\"},{\"empCode\":\"TR025964\",\"domainName\":\"mhcui\"},{\"empCode\":\"S32115\",\"domainName\":\"xrzhang\"},{\"empCode\":\"G13837\",\"domainName\":\"gm.he\"}]", BusinessDashboardEmployeeInfo.class)
        providerServiceClient.querySelfServiceKpiData(_) >> MapperUtil.str2Obj("{\"providerId\":11697,\"totalCount\":1,\"contractServiceKpiList\":[{\"selfServiceKpiDataId\":270866,\"cooperateBusinessLineIdList\":[7],\"kpiType\":\"month\",\"weekInputId\":0,\"ttdTotalVendorSessionCnt\":3018,\"ttdTimelyResponseRate\":0.8018,\"ttdTimelyResponseRateLine\":0.6,\"ttdTimelyResponseRateOperator\":\"≥\",\"ttdTimelyResponseRateIsPass\":\"PASS\",\"ttdTotalReviews\":261,\"ttdNegativeReviewsRate\":0.0421,\"ttdNegativeReviewsRateLine\":0.4,\"ttdNegativeReviewsRateOperator\":\"<\",\"ttdNegativeReviewsRateIsPass\":\"PASS\",\"wholePassStatus\":\"PASS\",\"approveStatus\":\"A\",\"approvalFormId\":0,\"rejectReason\":\"\",\"mainArea\":\"\"}],\"responseStatus\":{\"ack\":\"Success\",\"extension\":[]}}", QuerySelfServiceKpiDataResponseType.class)
        vbkClient.querySummaryProviderList(_) >> MapperUtil.str2Obj(String.format("{\"responseStatus\":{\"ack\":\"Success\",\"extension\":[]},\"pageSize\":1000,\"totalCount\":%s,\"providerInfoList\":[{\"providerId\":11697,\"providerName\":\"北京环球友邻科技有限公司\",\"providerShortName\":\"环球漫游\",\"active\":\"T\",\"directPoiList\":[],\"generalAgentPoiList\":[],\"corporateIdentity\":0}]}", total), QuerySummaryProviderListResponseType.class)
        salesDao.getSalesByVendor(_) >> 368215.39D

        and: "mock config"
        config.getConfigValue("vendorId") >> "供应商ID"
        config.getConfigValue("vendorName") >> "供应商名称"
        config.getConfigValue("ttdTimelyRate") >> "120S回复率"
        config.getConfigValue("ttdNegativeRate") >> "差评率"
        config.getConfigValue("totalSessionCount") >> "总会话量"
        config.getConfigValue("monthName") >> "%s月GMV"
        config.getConfigValue("warnTitle") >> "您名下供应商在%s月份的考核中低于平台预警考核线标准，已被预警。平台预警线：供应商的120s回复率的预警线为%s，差评率预警线为%s"
        config.getConfigValue("warnTaskName") >> "%s月自服务红线考核供应商预警"
        config.getConfigValue("warnTaskType") >> "TASK_QUALITY_SELFSERVICE_01"
        config.getConfigValue("warnEventType") >> "EVENT_QUALITY_SELFSERVICE_01"
        config.getConfigValue("warnServiceNumTitle") >> "您有%s家供应商当月自服务质量较差已预警，请及时查看~"

        config.getConfigValue("closeTitle") >> "您名下供应商在%s月自服务考核不达标现于%s已经关闭自服务%s天，%s天后可自助申请开通。平台考核标准：供应商的120s回复率的达标线为%s，差评率达标线为%s"
        config.getConfigValue("closeTaskName") >> "%s月自服务红线考核供应商管控"
        config.getConfigValue("closeTaskType") >> "TASK_QUALITY_SELFSERVICE_02"
        config.getConfigValue("closeEventType") >> "EVENT_QUALITY_SELFSERVICE_02"
        config.getConfigValue("closeServiceNumTitle") >> "您有%s家供应商自服务考核不达标已被关闭资格，请及时查看~"
        config.getConfigValue("isPushSelfServiceQuality") >> isPushSelfServiceQuality
        config.getConfigValue("isSendServiceNum") >> isSendServiceNum

        and: "mock response"
        pushClient.wbCreateTask(_) >> MapperUtil.str2Obj("{\"responseStatus\":{\"ack\":\"Success\",\"errors\":[],\"extension\":[]},\"taskId\":2674792}", WbCreateTaskResponseType.class)


        when: "call"
        def resp = impl.pushSelfQualityOfServiceToBusiness(request)


        then: "verify"
        with(resp) {

        }

        where: "多条件查询"
        pushType    |       total   |   isPushSelfServiceQuality | isSendServiceNum
        1           |       1   |   "true"  |   "true"
        2           |       0   |   "true"  |   "true"
        1           |       1   |   "false" |   "false"

    }


    def "getServiceNumShowInfo"() {
        when: "call"
        def resp = impl.getServiceNumShowInfo(content, title)

        then: "verify"
        with(resp){
            resp.title != null;
            resp.content != null
        }

        where: "多条件验证"
        title   |   content
        "1月自服务红线考核供应商预警"  |   "您有1家供应商自服务考核不达标已被关闭资格，请及时查看~"
        "1月自服务红线考核供应商关闭"    |   "产品：1111<br/><br/>供应商：2222"
    }
}
