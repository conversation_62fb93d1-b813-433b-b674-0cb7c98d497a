package com.ctrip.tour.business.dashboard.tktBusiness.helper;

import com.ctrip.soa._24922.*;
import com.ctrip.soa._27181.GetRawDataResponseType;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.*;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.ConfigImportException;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.ctrip.tour.business.dashboard.utils.MapperUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;

import java.text.ParseException;
import java.util.*;

/**
 * 水牌覆盖
 */

public class Bus10Helper {


    //填充指标卡基础数据
    public static void processMetricCardData(GetRawDataResponseType res,
                                             Map<String, Double> dimMap) {
        ChartHelper.fillOverallDimMap(MapperUtil.str2ListList(res.getResult(), Object.class), res.getMetricList(), dimMap);
    }


    //填充汇总趋势线数据
    public static void processTrendLineData(List<TrendLineDetailInfo> trendLineDetailInfoList,
                                            PeriodDataBean periodDataBean,
                                            List<String> timeList) {
        Map<String, Double> dimMap = new HashMap<>();
        Set<String> drillDownSet = new HashSet<>();
        List<List<Object>> reachList = periodDataBean.getReachList();
        List<String> reachHeaderList = periodDataBean.getReachHeaderList();
        List<String> reachDimList = reachHeaderList.subList(1, reachHeaderList.size());

        ChartHelper.setMonthlyMetricTrendlineActualReach(dimMap, drillDownSet, reachList, Lists.newArrayList("time"), reachDimList);


        Map<String, String> typeMap = new HashMap<>();
        typeMap.put("complete_people_cnt", "barChart");
        typeMap.put("complete_people_rate", "lineChart");
        typeMap.put("cover_people_rate", "lineChart");

        ChartHelper.fillLineChartTrendLineData(null, timeList, dimMap,
                trendLineDetailInfoList, typeMap);

    }

    //填充下钻趋势线数据
    public static void processDrilldownTrendLineData(List<TrendLineDetailInfo> trendLineDetailInfoList,
                                                     PeriodDataBean periodDataBean,
                                                     List<String> timeList) {
        Map<String, Double> dimMap = new HashMap<>();
        Set<String> drillDownSet = new HashSet<>();
        List<List<Object>> reachList = periodDataBean.getReachList();
        List<String> reachHeaderList = periodDataBean.getReachHeaderList();
        List<String> reachGroupList = reachHeaderList.subList(0, 2);
        List<String> reachDimList = reachHeaderList.subList(2, reachHeaderList.size());

        ChartHelper.setMonthlyMetricTrendlineActualReach(dimMap, drillDownSet, reachList, reachGroupList, reachDimList);


        Map<String, String> typeMap = new HashMap<>();
        typeMap.put("complete_people_rate", "lineChart");
        typeMap.put("cover_people_rate", "lineChart");

        ChartHelper.fillLineChartTrendLineDataWithDrillDown(null, timeList, dimMap, trendLineDetailInfoList,
                typeMap, drillDownSet, false);


    }

    public static void processRankData(GetRawDataResponseType res,
                                       MetricDetailInfo metricDetailInfo) {
        List<List<Object>> rawResultList = MapperUtil.str2ListList(res.getResult(), Object.class);
        ChartHelper.fillRankData(metricDetailInfo, rawResultList);
    }


    //填充下钻基础数据
    public static void processDrillDownBaseInfo(String field,
                                                GetRawDataResponseType response,
                                                List<FieldDataItem> fieldDataItemList) {
        ChartHelper.fillFieldDataItemList(field, MapperUtil.str2ListList(response.getResult(), Object.class), fieldDataItemList);
    }

    //填充表格数据
    public static void processTableData(GetRawDataResponseType res,
                                        List<TableDataItem> tableDataItemList) {
        List<List<Object>> rawResultList = MapperUtil.str2ListList(res.getResult(), Object.class);
        List<String> groupList = res.getGroupList();
        List<String> metricList = res.getMetricList();
        ChartHelper.fillCommmonTableData(tableDataItemList, groupList, metricList, new ArrayList<>(), rawResultList, new ArrayList<>());
    }


    public static SqlParamterBean getMetricCardSqlBean(TimeFilter timeFilter,
                                                       MetricInfoBean metricInfoBean,
                                                       String d) {
        SqlParamterBean bean = new SqlParamterBean();

        String level = MetricHelper.getLevelColumnName(metricInfoBean.getLevel());

        bean.setId(13L);
        if ("examinee".equals(level)) {
            bean.setId(14L);
        }

        Map<String, String> andMap = new HashMap<>();
        bean.setAndMap(andMap);
        setTimeValue(andMap, timeFilter, null, d);
        setExamineValue(andMap, level, metricInfoBean);
        return bean;
    }


    public static SqlParamterBean getDrillDownBaseInfoSqlBean(String field,
                                                              GetDrillDownBaseInfoRequestType request,
                                                              String d,
                                                              MetricInfoBean metricInfoBean,
                                                              RemoteConfig remoteConfig) {
        SqlParamterBean bean = new SqlParamterBean();

        DrillDownFieldBean configBean = remoteConfig.getDrillDownFieldBean("10", null, field);

        bean.setId(configBean.getBaseInfoId());

        List<String> baseInfoGroupList = configBean.getBaseInfoGroupList();
        bean.setGroupList(baseInfoGroupList);
        bean.setOrderList(Lists.newArrayList(baseInfoGroupList.get(0)));
        bean.setOrderTypeList(Lists.newArrayList("asc"));

        Map<String, String> andMap = new HashMap<>();
        setTimeValue(andMap, request.getTimeFilter(), null, d);

        String level = MetricHelper.getLevelColumnName(metricInfoBean.getLevel());
        setExamineValue(andMap, level, metricInfoBean);
        bean.setAndMap(andMap);

        Boolean needSearch = request.isNeedSearch();
        if (needSearch) {
            String searchWord = request.getSearchWord();
            Map<String, String> likeMap = new HashMap<>();
            List<Integer> likeIndexList = configBean.getBaseInfoLikeIndexList();
            for (Integer index : likeIndexList) {
                likeMap.put(baseInfoGroupList.get(index), searchWord);
            }
            bean.setLikeMap(likeMap);
        }

        return bean;
    }

    public static SqlParamterBean getTableSqlBean(GetTableDataRequestType request,
                                                  String d,
                                                  MetricInfoBean metricInfoBean,
                                                  RemoteConfig remoteConfig){

        SqlParamterBean bean = new SqlParamterBean();

        DrillDownFilter drillDownFilter = request.getDrillDownFilter();
        String field = MetricHelper.getDrillDownColumnName(drillDownFilter.getField());

        DrillDownFieldBean configBean = remoteConfig.getDrillDownFieldBean("10", null, field);
        bean.setId(configBean.getBaseInfoId());

        String source = "detailpage".equals(request.getSource()) ? "detailpage" : "firstpage";
        List<String> groupList = configBean.getTableGroupListMap().get(source);
        List<String> orderList = configBean.getTableOrderList();
        bean.setGroupList(groupList);
        bean.setOrderList(orderList);
        bean.setOrderTypeList(Lists.newArrayList("desc"));
        bean.setPageNo(request.getPageNo());
        bean.setPageSize(request.getPageSize());

        Map<String, String> andMap = new HashMap<>();
        setTimeValue(andMap, request.getTimeFilter(), null, d);
        String level = MetricHelper.getLevelColumnName(metricInfoBean.getLevel());
        setExamineValue(andMap, level, metricInfoBean);


        List<String> fieldValueList = drillDownFilter.getFieldValueList();
        setDrillDownValue(andMap, field, fieldValueList);
        bean.setAndMap(andMap);
        return bean;
    }


    public static SqlParamterBean getRankingSqlBean(String domainName,
                                                    TimeFilter timeFilter,
                                                    String d,
                                                    String metric) throws ParseException {
        SqlParamterBean bean = new SqlParamterBean();
        bean.setId(16L);
        Map<String, String> andMap = new HashMap<>();
        andMap.put("examinee", domainName);
        andMap.put("metric", metric);
        setRankingTimeValue(andMap, timeFilter, d);
        bean.setAndMap(andMap);
        return bean;
    }

    private static void setRankingTimeValue(Map<String, String> andMap,
                                            TimeFilter timeFilter,
                                            String d) throws ParseException {
        String year = timeFilter.getYear();
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();
        String dateType = MetricHelper.getDateTypeColumnName(timeFilter.getDateType());

        andMap.put("query_d",d);
        andMap.put("year",year);
        if ("month".equals(dateType)) {
            andMap.put("month", month);
        } else {
            String mappingMonth = DateUtil.getMappingMonthByQuarter(d, year, quarter);
            andMap.put("month", mappingMonth);
        }
    }


    public static SqlParamterBean getTrendlineSqlBean(GetTrendLineDataRequestType request,
                                                      ExamineConfigBean examineConfigBean,
                                                      MetricInfoBean metricInfoBean,
                                                      String d) {
        SqlParamterBean bean = new SqlParamterBean();

        String level = MetricHelper.getLevelColumnName(metricInfoBean.getLevel());
        DrillDownFilter drillDownFilter = request.getDrillDownFilter();


        String queryType = request.getQueryType();

        bean.setId(13L);
        if ("examinee".equals(level)) {
            bean.setId(14L);
        }

        Map<String, String> andMap = new HashMap<>();
        bean.setAndMap(andMap);
        setTimeValue(andMap, null, examineConfigBean, d);
        setExamineValue(andMap, level, metricInfoBean);
        if ("drilldown".equals(queryType)) {
            String field = MetricHelper.getDrillDownColumnName(drillDownFilter.getField());
            if ("examinee".equals(field)) {
                bean.setId(15L);
            }
            List<String> fieldValueList = drillDownFilter.getFieldValueList();
            setDrillDownValue(andMap, field, fieldValueList);
            bean.setGroupList(Lists.newArrayList(field));
        }
        return bean;
    }


    private static void setDrillDownValue(Map<String, String> andMap,
                                          String field,
                                          List<String> fieldValueList) {
        if (GeneralUtil.isNotEmpty(fieldValueList)) {
            andMap.put(field, StringUtils.join(fieldValueList, "|"));
        }
    }


    private static void setExamineValue(Map<String, String> andMap,
                                        String level,
                                        MetricInfoBean metricInfoBean) {
        List<String> regionList = metricInfoBean.getRegionList();
        List<String> bdList = metricInfoBean.getBdList();
        if (!"".equals(level)) {
            if ("region_name".equals(level) || "province_name".equals(level)) {
                andMap.put(level, StringUtils.join(regionList, "|"));
            } else {
                andMap.put(level, StringUtils.join(bdList, "|"));
            }
        }
    }

    private static void setTimeValue(Map<String, String> andMap,
                                     TimeFilter timeFilter,
                                     ExamineConfigBean examineConfigBean,
                                     String d) {
        String year;
        String month;
        String quarter;
        String dateType;
        if (GeneralUtil.isNotEmpty(timeFilter)) {
            year = timeFilter.getYear();
            month = timeFilter.getMonth();
            quarter = timeFilter.getQuarter();
            dateType = MetricHelper.getDateTypeColumnName(timeFilter.getDateType());
        } else {
            year = examineConfigBean.getYear();
            month = examineConfigBean.getMonth();
            quarter = examineConfigBean.getQuarter();
            dateType = MetricHelper.getDateTypeColumnName(examineConfigBean.getDateType());
        }


        andMap.put("year", year);
        andMap.put("date_type", dateType);
        if ("month".equals(dateType)) {
            andMap.put("month", month);
        } else {
            andMap.put("quarter", quarter);
        }
        andMap.put("query_d", d);
    }

    //获取某个考核层级对应的下钻维度
    public static List<String> getDrillDownFieldList(MetricInfoBean metricInfoBean) {
        String level = MetricHelper.getLevelColumnName(metricInfoBean.getLevel());
        switch (level) {
            case "":
                return Lists.newArrayList("region_name", "province_name", "examinee");
            case "region_name":
                return Lists.newArrayList("province_name", "examinee");
            case "province_name":
                return Lists.newArrayList("province_name", "examinee");
            case "examinee":
                return Lists.newArrayList("examinee");
            default:
                throw new ConfigImportException("metric 10 has error config:" + metricInfoBean.getLevel());
        }
    }
}
