package com.ctrip.tour.business.dashboard.tktBusiness.bo;

import com.ctrip.soa._24922.AvailableSubMetric;
import com.ctrip.soa._24922.DeliveryPage;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.DimOrdTtdTargetBO;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.*;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardOverseaExamineeConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.enums.BuTypeEnum;
import com.ctrip.tour.business.dashboard.tktBusiness.enums.ChannelSiteEnum;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.ConfigImportException;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.InputArgumentException;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static org.codehaus.groovy.runtime.DefaultGroovyMethods.collect;

public class OverSeaExamineConfigBO {

    //仪表盘海外提取指标概览数据
    public List<String> getMetricList(List<BusinessDashboardOverseaExamineeConfig> overseaConfigList) {
        Set<String> metricSet = new HashSet<>();
        for (BusinessDashboardOverseaExamineeConfig bean : overseaConfigList) {
            String[] metricArray = bean.getExamineMetric().split(";");
            for (String metric : metricArray) {
                metricSet.add(metric);
            }
        }
        List<String> metricList = new ArrayList<>();
        metricList.addAll(metricSet);
        return metricList;
    }

    //仪表盘海外提取指标概览数据
    public List<String> getMetricListV2(List<OverseaMetricInfoWithMetricBean> overseaConfigList) {
        Set<String> metricSet = new HashSet<>();
        for (OverseaMetricInfoWithMetricBean bean : overseaConfigList) {
            metricSet.add(bean.getMetric());
        }
        return new ArrayList<>(metricSet);
    }


    public List<OverseaMetricInfoBean> getMetricInfoBeanList(List<BusinessDashboardOverseaExamineeConfig> examineeConfigList,
                                                             RemoteConfig remoteConfig) {

        //海外
        String oversea = remoteConfig.getConfigValue("oversea");

        List<OverseaMetricInfoBean> metricInfoBeanList = new ArrayList<>();
        for (BusinessDashboardOverseaExamineeConfig bean : examineeConfigList) {
            String[] metricArray = bean.getExamineMetric().split(";");
            String destinationLevel = bean.getDestinationExamineLevel();
            String destinationRange = bean.getDestinationExamineRange();
            String siteRange = bean.getSiteExamineRange();
            String channelRange = bean.getChannelExamineRange();
            String domainName = bean.getDomainName();
            for (String metric : metricArray) {
                OverseaMetricInfoBean metricInfoBean = new OverseaMetricInfoBean();
                metricInfoBean.setDomainName(domainName);
                metricInfoBean.setMetric(metric);
                metricInfoBean.setDestinationLevel(destinationLevel);
                metricInfoBeanList.add(metricInfoBean);
                //需要考核目的地
                if (GeneralUtil.isNotEmpty(destinationLevel)) {
                    //海外没有任何具体配置 默认拥有全部权限
                    if (oversea.equals(destinationLevel)) {
                        continue;
                    } else {
                        //其他时候需要拿到具体的目的地范围
                        List<String> destinationRangeList = new ArrayList<>();
                        Collections.addAll(destinationRangeList, destinationRange.split(";"));
                        metricInfoBean.setDestinationRangeList(destinationRangeList);
                    }
                }

                //需要考核站点
                if (GeneralUtil.isNotEmpty(siteRange)) {
                    List<String> siteRangeList = new ArrayList<>();
                    Collections.addAll(siteRangeList, siteRange.split(";"));
                    metricInfoBean.setSiteRangeList(siteRangeList);
                }

                //需要考核渠道
                if (GeneralUtil.isNotEmpty(channelRange)) {
                    List<String> channelRangeList = new ArrayList<>();
                    Collections.addAll(channelRangeList, channelRange.split(";"));
                    metricInfoBean.setChannelRangeList(channelRangeList);
                }

            }
        }
        return metricInfoBeanList;
    }

    /**
     * 获取考核人员配置表信息（指标维度拆分）
     * @return
     */
    public List<OverseaMetricInfoBeanV2> convertToMetricInfoBeanV2(List<OverseaMetricInfoWithMetricBean> list) {
        return list.stream()
                .collect(Collectors.groupingBy(OverseaMetricInfoWithMetricBean::getMetric))
                .entrySet().stream()
                .map(entry -> {
                    List<OverseaMetricInfoWithMetricBean> group = entry.getValue();
                    OverseaMetricInfoBeanV2 v2 = new OverseaMetricInfoBeanV2();

                    // 设置唯一字段
                    v2.setYear(group.stream()
                            .map(OverseaMetricInfoWithMetricBean::getYear)
                            .filter(Objects::nonNull)
                            .findFirst()
                            .orElse(null));

                    v2.setDomainName(group.stream()
                            .map(OverseaMetricInfoWithMetricBean::getDomainName)
                            .filter(Objects::nonNull)
                            .findFirst()
                            .orElse(null));

                    v2.setMetric(entry.getKey());

                    // 收集季度并去重
                    v2.setQuarters(group.stream()
                            .map(OverseaMetricInfoWithMetricBean::getQuarter)
                            .filter(Objects::nonNull)
                            .distinct()
                            .collect(Collectors.toList()));

                    // 构建层级映射关系（每个层级下的范围值去重）
                    Map<String, List<String>> levelMap = new HashMap<>();
                    Map<String, Set<String>> tempMap = new HashMap<>();

                    for (OverseaMetricInfoWithMetricBean bean : group) {
                        String level = bean.getDestinationLevel();
                        String range = bean.getDestinationRange();

                        if (level != null && range != null) {
                            tempMap.computeIfAbsent(level, k -> new HashSet<>())
                                    .add(range);
                        }
                    }

                    // 转换Set为List
                    for (Map.Entry<String, Set<String>> e : tempMap.entrySet()) {
                        levelMap.put(e.getKey(), new ArrayList<>(e.getValue()));
                    }

                    v2.setDestinationLevelMap(levelMap);
                    v2.setDestinationLevelList(new ArrayList<>(levelMap.keySet()));
                    v2.setDestinationRangeList(levelMap.values().stream()
                            .flatMap(List::stream)
                            .collect(Collectors.toList()));

                    // 构建季度信息映射 quarterInfoMap
                    Map<String, OverseaMetricInfoBeanV2Child> quarterInfoMap = new HashMap<>();

                    // 按季度分组
                    Map<String, List<OverseaMetricInfoWithMetricBean>> quarterGroup = group.stream()
                            .filter(bean -> bean.getQuarter() != null)
                            .collect(Collectors.groupingBy(OverseaMetricInfoWithMetricBean::getQuarter));

                    for (Map.Entry<String, List<OverseaMetricInfoWithMetricBean>> quarterEntry : quarterGroup.entrySet()) {
                        String quarter = quarterEntry.getKey();
                        List<OverseaMetricInfoWithMetricBean> quarterBeans = quarterEntry.getValue();

                        OverseaMetricInfoBeanV2Child child = new OverseaMetricInfoBeanV2Child();
                        child.setQuarters(quarter);

                        // 构建季度内的层级映射
                        Map<String, Set<String>> quarterTempMap = new HashMap<>();
                        for (OverseaMetricInfoWithMetricBean bean : quarterBeans) {
                            String level = bean.getDestinationLevel();
                            String range = bean.getDestinationRange();

                            if (level != null && range != null) {
                                quarterTempMap.computeIfAbsent(level, k -> new HashSet<>())
                                        .add(range);
                            }
                        }

                        // 设置季度内的层级列表
                        child.setDestinationLevelList(new ArrayList<>(quarterTempMap.keySet()));

                        // 设置季度内的范围列表（层级内去重，层级间允许重复）
                        child.setDestinationRangeList(quarterTempMap.values().stream()
                                .flatMap(Set::stream)
                                .collect(Collectors.toList()));

                        // 构建层级映射关系（每个层级下的范围值去重）
                        Map<String, List<String>> levelInnerMap = new HashMap<>();
                        Map<String, Set<String>> tempInnerMap = new HashMap<>();
                        for (OverseaMetricInfoWithMetricBean bean : quarterBeans) {
                            String level = bean.getDestinationLevel();
                            String range = bean.getDestinationRange();

                            if (level != null && range != null) {
                                tempInnerMap.computeIfAbsent(level, k -> new HashSet<>())
                                        .add(range);
                            }
                        }
                        // 转换Set为List
                        for (Map.Entry<String, Set<String>> e : tempInnerMap.entrySet()) {
                            levelInnerMap.put(e.getKey(), new ArrayList<>(e.getValue()));
                        }

                        child.setDestinationLevelMap(levelInnerMap);

                        quarterInfoMap.put(quarter, child);
                    }

                    v2.setQuarterInfoMap(quarterInfoMap);

                    return v2;
                })
                .collect(Collectors.toList());
    }



    public OverseaMetricInfoBean getMetricInfoBean(BusinessDashboardOverseaExamineeConfig examineeConfig,
                                                   RemoteConfig remoteConfig) {
        List<OverseaMetricInfoBean> metricInfoBeanList = getMetricInfoBeanList(Lists.newArrayList(examineeConfig), remoteConfig);
        return metricInfoBeanList.get(0);
    }

    //仪表盘海外提取基础配置数据(指标卡)
    public Map<String, AvailableSubMetric> getMetricCardConfigMap(List<BusinessDashboardOverseaExamineeConfig> overseaConfigList,
                                                                  RemoteConfig remoteConfig) {
        Map<String, AvailableSubMetric> subMetricMap = new HashMap<>();

        for (BusinessDashboardOverseaExamineeConfig bean : overseaConfigList) {
            String[] metricArray = bean.getExamineMetric().split(";");
            for (String metric : metricArray) {
                switch (metric) {
                    case "101":
                    case "102":
                        subMetricMap.put(metric, getBus101102MetricCardConfig(bean, remoteConfig));
                        break;
                    case "103":
                        subMetricMap.put(metric, getBus103Config(bean, remoteConfig));
                        break;
                    case "105":
                    case "106":
                    case "107":
                        subMetricMap.put(metric, getBus105106107Config(bean));
                        break;
                    default:
                        throw new ConfigImportException("oversea metric:" + metric + " is not support");
                }
            }
        }
        return subMetricMap;
    }

    //仪表盘海外提取基础配置数据(指标卡)V2
    public Map<String, AvailableSubMetric> getMetricCardConfigMapV2(List<OverseaMetricInfoBeanV2> overseaConfigList) {
        Map<String, AvailableSubMetric> subMetricMap = new HashMap<>();

        for (OverseaMetricInfoBeanV2 bean : overseaConfigList) {
            String metric = bean.getMetric();
            switch (metric) {
                case "101":
                case "102":
                    subMetricMap.put(metric, getNewBus101102MetricCardConfig(bean));
                    break;
                case "103":
                    subMetricMap.put(metric, getNewBus103Config(bean));
                    break;
                case "105":
                case "106":
                case "107":
                    subMetricMap.put(metric, getNewBus105106107Config(bean));
                    break;
                case "110":
                    subMetricMap.put(metric, getNewBus110Config(bean));
                    break;
                default:
                    throw new ConfigImportException("oversea metric:" + metric + " is not support");
            }
        }
        return subMetricMap;
    }

    //收入力指标卡配置
    private AvailableSubMetric getBus101102MetricCardConfig(BusinessDashboardOverseaExamineeConfig bean,
                                                            RemoteConfig remoteConfig) {
        //海外
        String oversea = remoteConfig.getConfigValue("oversea");
        String destinationExamineLevel = bean.getDestinationExamineLevel();
        String siteExamineRange = bean.getSiteExamineRange();
        String channelExamineRange = bean.getChannelExamineRange();
        AvailableSubMetric availableSubMetric = new AvailableSubMetric();
        List<String> subMetricList = new ArrayList<>();
        availableSubMetric.setSubMetricList(subMetricList);
        if (GeneralUtil.isNotEmpty(destinationExamineLevel)) {
            subMetricList.add("destination");
            subMetricList.add("destination_c");
            subMetricList.add("destination_t");
        }
        if (oversea.equals(destinationExamineLevel) || GeneralUtil.isNotEmpty(siteExamineRange)) {
            subMetricList.add("site");
        }
        if (GeneralUtil.isNotEmpty(channelExamineRange) && !oversea.equals(destinationExamineLevel)) {
            subMetricList.add("site");
        }
        return availableSubMetric;
    }

    //收入力指标卡配置V2
    private AvailableSubMetric getNewBus101102MetricCardConfig(OverseaMetricInfoBeanV2 bean) {
        List<String> destinationExamineLevels = bean.getDestinationLevelList();
        AvailableSubMetric availableSubMetric = new AvailableSubMetric();
        List<String> subMetricList = new ArrayList<>();
        availableSubMetric.setSubMetricList(subMetricList);
        Set<String> subSet = new HashSet<>();
        for (String destinationLevel : destinationExamineLevels) {
            if (ChannelSiteEnum.SITE.getName().equals(destinationLevel)) {
                subSet.add("site");
            }else if(ChannelSiteEnum.CHANNEL.getName().equals(destinationLevel)) {
                subSet.add("channel");
            }else{
                subSet.add("destinationCT");
                subSet.add("destinationC");
                subSet.add("destinationT");
            }
        }
        subMetricList.addAll(subSet);
        availableSubMetric.setSubMetricList(subMetricList);
        return availableSubMetric;
    }


    //收入力趋势线配置
    private AvailableSubMetric getBus101102TrendLineConfig(BusinessDashboardOverseaExamineeConfig bean,
                                                           RemoteConfig remoteConfig) {
        //海外
        String oversea = remoteConfig.getConfigValue("oversea");
        String destinationExamineLevel = bean.getDestinationExamineLevel();
        String siteExamineRange = bean.getSiteExamineRange();
        String channelExamineRange = bean.getChannelExamineRange();
        AvailableSubMetric availableSubMetric = new AvailableSubMetric();
        List<String> subMetricList = new ArrayList<>();
        availableSubMetric.setSubMetricList(subMetricList);
        if (GeneralUtil.isNotEmpty(destinationExamineLevel)) {
            subMetricList.add("destination");
            subMetricList.add("destination_c");
            subMetricList.add("destination_t");
        }
        if (oversea.equals(destinationExamineLevel) || GeneralUtil.isNotEmpty(siteExamineRange)) {
            subMetricList.add("site");
        }
        if(oversea.equals(destinationExamineLevel) || GeneralUtil.isNotEmpty(channelExamineRange)){
            subMetricList.add("channel");
        }
        return availableSubMetric;
    }

    //质量成本指标卡/趋势线配置
    private AvailableSubMetric getBus103Config(BusinessDashboardOverseaExamineeConfig bean,
                                               RemoteConfig remoteConfig) {
        //海外
        String oversea = remoteConfig.getConfigValue("oversea");
        String destinationExamineLevel = bean.getDestinationExamineLevel();
        String siteExamineRange = bean.getSiteExamineRange();
        AvailableSubMetric availableSubMetric = new AvailableSubMetric();
        List<String> subMetricList = new ArrayList<>();
        availableSubMetric.setSubMetricList(subMetricList);
        if (GeneralUtil.isNotEmpty(destinationExamineLevel)) {
            subMetricList.add("destination_c");
            subMetricList.add("destination_t");
        }
        if (oversea.equals(destinationExamineLevel) || GeneralUtil.isNotEmpty(siteExamineRange)) {
            subMetricList.add("site");
        }
        return availableSubMetric;
    }

    //质量成本指标卡/趋势线配置V2
    private AvailableSubMetric getNewBus103Config(OverseaMetricInfoBeanV2 bean) {

        List<String> destinationExamineLevels = bean.getDestinationLevelList();
        AvailableSubMetric availableSubMetric = new AvailableSubMetric();
        List<String> subMetricList = new ArrayList<>();
        availableSubMetric.setSubMetricList(subMetricList);
        Set<String> subSet = new HashSet<>();
        for (String destinationLevel : destinationExamineLevels) {
            if (ChannelSiteEnum.SITE.getName().equals(destinationLevel) || ChannelSiteEnum.CHANNEL.getName().equals(destinationLevel)) {
                subSet.add("site");
            }else{
                subSet.add("destinationC");
                subSet.add("destinationT");
            }
        }
        subMetricList.addAll(subSet);
        availableSubMetric.setSubMetricList(subMetricList);
        return availableSubMetric;
    }

    //劣势指标卡/趋势线配置
    private AvailableSubMetric getBus105106107Config(BusinessDashboardOverseaExamineeConfig bean) {
        String destinationExamineLevel = bean.getDestinationExamineLevel();
        AvailableSubMetric availableSubMetric = new AvailableSubMetric();
        List<String> subMetricList = new ArrayList<>();
        availableSubMetric.setSubMetricList(subMetricList);
        if (GeneralUtil.isNotEmpty(destinationExamineLevel)) {
            subMetricList.add("tklk");
            subMetricList.add("tfly");
        }
        return availableSubMetric;
    }

    //劣势指标卡/趋势线配置V2
    private AvailableSubMetric getNewBus105106107Config(OverseaMetricInfoBeanV2 bean) {
        List<String> destinationExamineLevel = bean.getDestinationLevelList();
        AvailableSubMetric availableSubMetric = new AvailableSubMetric();
        List<String> subMetricList = new ArrayList<>();
        availableSubMetric.setSubMetricList(subMetricList);
        if (GeneralUtil.isNotEmpty(destinationExamineLevel)) {
            subMetricList.add("tklk");
            subMetricList.add("tfly");
        }
        return availableSubMetric;
    }



    // 票量配置V2
    public AvailableSubMetric getNewBus110Config(OverseaMetricInfoBeanV2 bean) {
        List<String> destinationExamineLevels = bean.getDestinationLevelList();
        AvailableSubMetric availableSubMetric = new AvailableSubMetric();
        List<String> subMetricList = new ArrayList<>();
        availableSubMetric.setSubMetricList(subMetricList);
        Set<String> subSet = new HashSet<>();
        for (String destinationLevel : destinationExamineLevels) {
            if (ChannelSiteEnum.SITE.getName().equals(destinationLevel)) {
                subSet.add("site");
            }else if(ChannelSiteEnum.CHANNEL.getName().equals(destinationLevel)) {
                subSet.add("channel");
            }else{
                subSet.add("destinationT");
            }
        }
        subMetricList.addAll(subSet);
        availableSubMetric.setSubMetricList(subMetricList);
        return availableSubMetric;
    }



    //仪表盘海外提取基础配置数据(趋势线)
    public Map<String, AvailableSubMetric> getTrendLineConfigMap(List<BusinessDashboardOverseaExamineeConfig> overseaConfigList,
                                                                 RemoteConfig remoteConfig) {
        Map<String, AvailableSubMetric> subMetricMap = new HashMap<>();
        for (BusinessDashboardOverseaExamineeConfig bean : overseaConfigList) {
            String[] metricArray = bean.getExamineMetric().split(";");
            for (String metric : metricArray) {
                switch (metric) {
                    case "101":
                    case "102":
                        subMetricMap.put(metric, getBus101102TrendLineConfig(bean, remoteConfig));
                        break;
                    case "103":
                        subMetricMap.put(metric, getBus103Config(bean, remoteConfig));
                        break;
                    case "105":
                    case "106":
                    case "107":
                        subMetricMap.put(metric, getBus105106107Config(bean));
                        break;
                    default:
                        throw new ConfigImportException("oversea metric:" + metric + " is not support");
                }
            }
        }
        return subMetricMap;
    }


    public OverseaMetricInfoBean getSingleMetricInfoBean(List<BusinessDashboardOverseaExamineeConfig> overseaConfigList,
                                                         RemoteConfig remoteConfig,
                                                         String metric) {
        List<OverseaMetricInfoBean> metricInfoBeanList = getMetricInfoBeanList(overseaConfigList, remoteConfig);
        Optional<OverseaMetricInfoBean> optionalMetricInfoBean = metricInfoBeanList.stream()
                .filter(i -> i.getMetric().equals(metric))
                .findFirst();
        if (optionalMetricInfoBean.isPresent()) {
            return optionalMetricInfoBean.get();
        }
        throw new InputArgumentException("input invalid metric:" + metric);
    }

    public OverseaMetricInfoBean getSingleMetricInfoBeanV2(List<DimOrdTtdTargetBO> overseaConfigList,
                                                           RemoteConfig remoteConfig,
                                                           String metricRequest,
                                                           String subMetricRequest) {
        String oversea = remoteConfig.getConfigValue("oversea");
        String locale = remoteConfig.getConfigValue("locale");
        String channel = remoteConfig.getConfigValue("channel");
        List<OverseaMetricInfoBean> metricInfoBeanList = new ArrayList<>();
        for (DimOrdTtdTargetBO bean : overseaConfigList) {
            if (!metricRequest.equalsIgnoreCase(bean.getExamineMetricType())) {
                continue;
            }
//            if ("site".equalsIgnoreCase(subMetricRequest) && !locale.equalsIgnoreCase(bean.getExamineLevel())) {
//                continue;
//            }
//            if("channel".equalsIgnoreCase(subMetricRequest) && !channel.equalsIgnoreCase(bean.getExamineLevel())) {
//                continue;
//            }
            OverseaMetricInfoBean metricInfoBean = new OverseaMetricInfoBean();
            metricInfoBean.setDomainName(bean.getDomainName());
            metricInfoBean.setMetric(bean.getExamineMetricType());
            metricInfoBeanList.add(metricInfoBean);
            if (locale.equalsIgnoreCase(bean.getExamineLevel())) {
                List<String> siteRangeList = new ArrayList<>();
                Collections.addAll(siteRangeList, bean.getExamineScope().split(";"));
                metricInfoBean.setSiteRangeList(siteRangeList);
            } else if (channel.equalsIgnoreCase(bean.getExamineLevel())) {
                List<String> channelRangeList = new ArrayList<>();
                Collections.addAll(channelRangeList, bean.getExamineScope().split(";"));
                metricInfoBean.setChannelRangeList(channelRangeList);
            } else {
                metricInfoBean.setDestinationLevel(bean.getExamineLevel());
                //海外没有任何具体配置 默认拥有全部权限
                if (oversea.equals(bean.getExamineLevel())) {
                    continue;
                } else {
                    //其他时候需要拿到具体的目的地范围
                    List<String> destinationRangeList = new ArrayList<>();
                    Collections.addAll(destinationRangeList, bean.getExamineScope().split(";"));
                    metricInfoBean.setDestinationRangeList(destinationRangeList);
                }
            }
        }
        OverseaMetricInfoBean result = metricInfoBeanList.get(0);
        for (int i = 1; i < metricInfoBeanList.size(); i++) {
            OverseaMetricInfoBean bean = metricInfoBeanList.get(i);
            //若有多条数据，取并集
            result.setMetric(bean.getMetric());
            result.setDomainName(bean.getDomainName());
            Integer useNew = calDestinationLevel(result.getDestinationLevel(), bean.getDestinationLevel());
            result.setDestinationLevel(useNew != 1 ? bean.getDestinationLevel() : result.getDestinationLevel());
            result.setDestinationRangeList(calDestinationRangeList(useNew, result.getDestinationRangeList(), bean.getDestinationRangeList()));
            result.setSiteRangeList(calSiteRangeList(result.getSiteRangeList(), bean.getSiteRangeList()));
            result.setChannelRangeList(calSiteRangeList(result.getChannelRangeList(), bean.getChannelRangeList()));
        }
        return result;
    }

    private List<String> calSiteRangeList(List<String> currentList, List<String> newList) {
        if(CollectionUtils.isEmpty(currentList)) {
            return newList;
        }
        if(CollectionUtils.isEmpty(newList)) {
            return currentList;
        }
        Set<String> set = new HashSet<>(currentList);
        set.addAll(newList);
        return new ArrayList<>(set);
    }

    private List<String> calDestinationRangeList(Integer useNew, List<String> currentList, List<String> newList) {
        switch (useNew) {
            case 0:
                return newList;
            case 1:
                return currentList;
            case 2:
                if (CollectionUtils.isEmpty(newList)) {
                    return newList;
                } else if (CollectionUtils.isEmpty(currentList)) {
                    return currentList;
                } else {
                    Set<String> set = new HashSet<>(currentList);
                    set.addAll(newList);
                    return new ArrayList<>(set);
                }
        }
        return newList;
    }

    /**
     * 0:新的范围大
     * 1：老的范围大
     * 2：一样大
     * @param currentLevel
     * @param newLevel
     * @return
     */
    private Integer calDestinationLevel(String currentLevel, String newLevel) {
        if (StringUtils.isEmpty(currentLevel)) {
            return 0;
        }
        if (StringUtils.isEmpty(newLevel)) {
            return 1;
        }
        if (destinationLevelToInt(currentLevel).equals(destinationLevelToInt(newLevel))) {
            return 2;
        }
        if (destinationLevelToInt(currentLevel) < destinationLevelToInt(newLevel)) {
            return 1;
        }
        return 0;
    }

    private Integer destinationLevelToInt(String destinationLevel) {
        switch (destinationLevel) {
            case "海外"://NOSONAR
                return 1;
            case "大区"://NOSONAR
                return 2;
            case "子区域"://NOSONAR
                return 3;
            case "国家/地区"://NOSONAR
                return 4;
            case "省份"://NOSONAR
                return 5;
            case "城市"://NOSONAR
                return 6;
            case "景点经理"://NOSONAR
                return 7;
            case "景点助理"://NOSONAR
                return 8;
            case "景点"://NOSONAR
                return 9;
        }
        //渠道站点时没有大小区分，统一返回10，取交集
        return 10;
    }


}
