package com.ctrip.tour.business.dashboard.biz.impl

import com.ctrip.soa._24922.CheckAdminPermissionRequestType
import com.ctrip.soa._24922.CheckTaskFlowPermissionRequestType
import com.ctrip.tour.business.dashboard.tktBusiness.biz.DataUpdateBiz
import com.ctrip.tour.business.dashboard.tktBusiness.biz.impl.UserPermissionBizImpl
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardEmployeeInfoDao
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardExamineeConfigV2Dao
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardOrganizationInfoDao
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardOverseaExamineeConfigDao
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardEmployeeInfo
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardExamineeConfigV2
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardOrganizationInfo
import com.ctrip.tour.business.dashboard.utils.MapperUtil
import spock.lang.Specification

import java.time.LocalDate

class UserPermissionBizImplTest extends Specification {
    def remoteConfig = Mock(RemoteConfig)
    def examineConfigV2Dao = Mock(BusinessDashboardExamineeConfigV2Dao)
    def overseaConfigDao = Mock(BusinessDashboardOverseaExamineeConfigDao)
    def employeeInfoDao = Mock(BusinessDashboardEmployeeInfoDao)
    def organizationInfoDao = Mock(BusinessDashboardOrganizationInfoDao)
    def dataUpdateBiz = Mock(DataUpdateBiz)



    def userPermissionBizImpl = new UserPermissionBizImpl(
            remoteConfig: remoteConfig,
            examineConfigV2Dao:examineConfigV2Dao,
            overseaConfigDao: overseaConfigDao,
            employeeInfoDao: employeeInfoDao,
            organizationInfoDao: organizationInfoDao,
            dataUpdateBiz: dataUpdateBiz
    )

    def "CheckAdminPermission"() {
        given: "设置请求参数"
        def request = new CheckAdminPermissionRequestType();

        and: "mock qconfig配置信息"
        remoteConfig.getExternalConfig("admin") >> "S35097"
        remoteConfig.getEmployeeRelationValue(null) >> "S35097"

        when: "调用接口"
        def response = userPermissionBizImpl.checkAdminPermission(request);

        then: "验证返回结果"
        with(response) {
            haveAdminPermission == true
        }

    }

    def "checkTaskFlowPermission"(){
        given: "mock request"
        def request = new CheckTaskFlowPermissionRequestType()
        def empCode = "D00024"  // todo 需要新增  如果是非门票活动业务部的人

        and: "mock dal"
        def employInfo = "{\"id\":452,\"empCode\":\"D00024\",\"displayName\":\"Zhe Li （李哲）\",\"domainName\":\"zheli\",\"leaderEmpCode\":\"S35097\",\"leaderEmpName\":\"JIM Ji （季毅华）\",\"teamId\":\"41999\",\"teamCname\":\"门票活动业务部\",\"orgIdPath\":\"Ctrip_CO0001 Ctrip_Board 29 SO002916 25481 41999\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部\",\"position\":\"\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:38:56 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:38:56 AM\"}"
        def orgInfoList = "[{\"id\":132,\"nodeOrgId\":\"SO001057\",\"nodeOrgType\":\"Team\",\"nodeOrgName\":\"门票活动国内业务部\",\"nodeOrgLevel\":\"7\",\"parentOrgId\":\"41999\",\"buId\":\"25481\",\"deptId\":\"41999\",\"orgIdPath\":\"Ctrip_CO0001.Ctrip_Board.29.SO002916.25481.41999.SO001057\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>门票活动国内业务部\",\"leaderEmpCode\":\"D00024\",\"leaderEmpName\":\"Zhe Li （李哲）\",\"empCnt\":\"314\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:26:08 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:26:08 AM\"},{\"id\":133,\"nodeOrgId\":\"SO002566\",\"nodeOrgType\":\"Team\",\"nodeOrgName\":\"国内业务三方供应中心\",\"nodeOrgLevel\":\"8\",\"parentOrgId\":\"SO001057\",\"buId\":\"25481\",\"deptId\":\"41999\",\"orgIdPath\":\"Ctrip_CO0001.Ctrip_Board.29.SO002916.25481.41999.SO001057.SO002566\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>门票活动国内业务部>国内业务三方供应中心\",\"leaderEmpCode\":\"D00024\",\"leaderEmpName\":\"Zhe Li （李哲）\",\"empCnt\":\"\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:26:08 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:26:08 AM\"},{\"id\":134,\"nodeOrgId\":\"62390\",\"nodeOrgType\":\"Team\",\"nodeOrgName\":\"华西大区\",\"nodeOrgLevel\":\"8\",\"parentOrgId\":\"SO001057\",\"buId\":\"25481\",\"deptId\":\"41999\",\"orgIdPath\":\"Ctrip_CO0001.Ctrip_Board.29.SO002916.25481.41999.SO001057.62390\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>门票活动国内业务部>华西大区\",\"leaderEmpCode\":\"D00024\",\"leaderEmpName\":\"Zhe Li （李哲）\",\"empCnt\":\"61\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:26:08 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:26:08 AM\"},{\"id\":135,\"nodeOrgId\":\"SO003279\",\"nodeOrgType\":\"Team\",\"nodeOrgName\":\"海外供应链运营组\",\"nodeOrgLevel\":\"9\",\"parentOrgId\":\"SO002566\",\"buId\":\"25481\",\"deptId\":\"41999\",\"orgIdPath\":\"Ctrip_CO0001.Ctrip_Board.29.SO002916.25481.41999.SO001057.SO002566.SO003279\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>门票活动国内业务部>国内业务三方供应中心>海外供应链运营组\",\"leaderEmpCode\":\"D00024\",\"leaderEmpName\":\"Zhe Li （李哲）\",\"empCnt\":\"\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:26:08 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:26:08 AM\"}]"
        employeeInfoDao.queryByEmpCode(_) >> MapperUtil.str2Obj(employInfo, BusinessDashboardEmployeeInfo.class)
        employeeInfoDao.queryByDomainName(_) >> MapperUtil.str2Obj(employInfo, BusinessDashboardEmployeeInfo.class)
        organizationInfoDao.queryByLeaderEmpCode(_) >> MapperUtil.str2List(orgInfoList, BusinessDashboardOrganizationInfo.class)
        dataUpdateBiz.getUpdateTime() >> LocalDate.now().plusDays(-1).toString()
        examineConfigV2Dao.querySpecificPeriodAllMetricConfig(_,_,_,_,_) >> MapperUtil.str2List("[{\"id\":7057086,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2023\",\"quarter\":\"Q4\",\"examineMetric\":\"1;2\",\"examineType\":15,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"国内\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2023-12-18\",\"datachangeLasttime\":\"Dec 18, 2023 1:19:55 AM\",\"odtExamineLevel\":\"国内\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"国内\",\"overseaOdtExamineRange\":\"\"},{\"id\":7057093,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2023\",\"quarter\":\"Q4\",\"examineMetric\":\"3\",\"examineType\":0,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2023-12-18\",\"datachangeLasttime\":\"Dec 18, 2023 1:19:55 AM\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"},{\"id\":7057100,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2023\",\"quarter\":\"Q4\",\"examineMetric\":\"4\",\"examineType\":0,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2023-12-18\",\"datachangeLasttime\":\"Dec 18, 2023 1:19:55 AM\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"},{\"id\":7057107,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2023\",\"quarter\":\"Q4\",\"examineMetric\":\"5;6;7\",\"examineType\":0,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2023-12-18\",\"datachangeLasttime\":\"Dec 18, 2023 1:19:55 AM\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"},{\"id\":7057114,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2023\",\"quarter\":\"Q4\",\"examineMetric\":\"9\",\"examineType\":0,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2023-12-18\",\"datachangeLasttime\":\"Dec 18, 2023 1:19:55 AM\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"},{\"id\":7057121,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2023\",\"quarter\":\"Q4\",\"examineMetric\":\"10\",\"examineType\":0,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2023-12-18\",\"datachangeLasttime\":\"Dec 18, 2023 1:19:55 AM\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"},{\"id\":7057128,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2023\",\"quarter\":\"Q4\",\"examineMetric\":\"11\",\"examineType\":0,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2023-12-18\",\"datachangeLasttime\":\"Dec 18, 2023 1:19:55 AM\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"}]", BusinessDashboardExamineeConfigV2.class)
        employeeInfoDao.getDirectSubordinateNumber(_) >> 12
        employeeInfoDao.queryByDomainName(_) >> MapperUtil.str2Obj(employInfo, BusinessDashboardEmployeeInfo.class)
        organizationInfoDao.queryByLeaderEmpCode(_) >> MapperUtil.str2List(orgInfoList, BusinessDashboardOrganizationInfo.class)
        employeeInfoDao.getAllSubordinateNumber(_) >> 361

        and: "mock qconfig"
        remoteConfig.getExternalConfig("domesticDeptId") >> "SO001057"
        remoteConfig.getExternalConfig("overseaDeptId") >> "SO003287"
        remoteConfig.getExternalConfig("specailRegionOperate") >> "wsong"
        remoteConfig.getConfigValue("domestic") >> "国内"
        remoteConfig.getConfigValue("three") >> "三方"
        remoteConfig.getConfigValue("region") >> "大区"
        remoteConfig.getConfigValue("province") >> "省份"
        remoteConfig.getConfigValue("viewspot") >> "景点"


        when: "调用接口"
        def response = userPermissionBizImpl.checkTaskFlowPermission(request, empCode)

        then: "验证reponse"
        with(response){
            havePermission == true
            subordinateTypeList.size() == 2
        }
    }
}
