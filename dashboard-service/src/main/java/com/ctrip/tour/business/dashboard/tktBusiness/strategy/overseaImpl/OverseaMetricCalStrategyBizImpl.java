package com.ctrip.tour.business.dashboard.tktBusiness.strategy.overseaImpl;



import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.StrategyException;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.OverseaMetricCalStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;

@Service
@Slf4j
public class OverseaMetricCalStrategyBizImpl implements ApplicationContextAware {


    private ApplicationContext applicationContext;

    private ConcurrentHashMap<String, OverseaMetricCalStrategy> metricCalStrategyMap = new ConcurrentHashMap<>();


    public Future<List<MetricDetailInfo>> getOverseaSingleMetricCardData(TimeFilter timeFilter,
                                                                         OverseaMetricInfoBean metricInfoBean,
                                                                         String d,
                                                                         Map<String, AvailableSubMetric> metricCardConfigMap) throws Exception {
        String metric = metricInfoBean.getMetric();
        OverseaMetricCalStrategy metricCalStrategy = metricCalStrategyMap.get(metric);
        if (metricCalStrategy == null) {
            throw new StrategyException("strategy is not found!");
        }
        return metricCalStrategy.getOverseaSingleMetricCardData(timeFilter, metricInfoBean, d, metricCardConfigMap.get(metric));
    }


    public GetOverseaTrendLineDataResponseType getOverseaSingleTrendLineData(GetOverseaTrendLineDataRequestType request,
                                                                             String d) throws Exception {
        String metric = request.getMetric();
        OverseaMetricCalStrategy metricCalStrategy = metricCalStrategyMap.get(metric);
        if (metricCalStrategy == null) {
            throw new StrategyException("strategy is not found!");
        }
        return metricCalStrategy.getOverseaSingleTrendlineData(request, d);
    }

    public GetOverseaDrillDownBaseInfoResponseType getOverseaSingleDrillDownBaseInfo(GetOverseaDrillDownBaseInfoRequestType request,
                                                                                     String d,
                                                                                     OverseaMetricInfoBean metricInfoBean) throws Exception {
        String metric = request.getMetric();
        OverseaMetricCalStrategy metricCalStrategy = metricCalStrategyMap.get(metric);
        if (metricCalStrategy == null) {
            throw new StrategyException("strategy is not found!");
        }
        return metricCalStrategy.getOverseaSingleDrillDownBaseInfo(request, d, metricInfoBean);
    }


    public GetOverseaTableDataResponseType getOverseaSingleTableData(GetOverseaTableDataRequestType request,
                                                                     String d,
                                                                     OverseaMetricInfoBean metricInfoBean) throws Exception {
        String metric = request.getMetric();
        OverseaMetricCalStrategy metricCalStrategy = metricCalStrategyMap.get(metric);
        if (metricCalStrategy == null) {
            throw new StrategyException("strategy is not found!");
        }
        return metricCalStrategy.getOverseaSingleTableData(request, d, metricInfoBean);
    }


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
        Map<String, OverseaMetricCalStrategy> strategyMap = applicationContext.getBeansOfType(OverseaMetricCalStrategy.class);
        strategyMap.values().forEach(e -> metricCalStrategyMap.put(e.getMetricName(), e));
    }
}
