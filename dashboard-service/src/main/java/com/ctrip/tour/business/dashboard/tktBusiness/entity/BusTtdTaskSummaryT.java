package com.ctrip.tour.business.dashboard.tktBusiness.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Sensitive;
import com.ctrip.platform.dal.dao.annotation.Type;
import java.sql.Types;
import java.sql.Timestamp;

import com.ctrip.platform.dal.dao.DalPojo;

/**
 * <AUTHOR>
 * @date 2023-12-06
 */
@Entity
@Database(name = "TtdReportDB_W")
@Table(name = "bus_ttd_task_summary_t")
public class BusTtdTaskSummaryT implements DalPojo {

    /**
     * 主键
     */
    @Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long id;

    /**
     * 日期
     */
	@Column(name = "the_date")
	@Type(value = Types.VARCHAR)
	private String theDate;

    /**
     * 业务类别code
     */
	@Column(name = "biz_category_code")
	@Type(value = Types.VARCHAR)
	private String bizCategoryCode;

    /**
     * 业务类别名称
     */
	@Column(name = "biz_category_name")
	@Type(value = Types.VARCHAR)
	private String bizCategoryName;

    /**
     * 任务集code
     */
	@Column(name = "task_collection_code")
	@Type(value = Types.VARCHAR)
	private String taskCollectionCode;

    /**
     * 任务集名称
     */
	@Column(name = "task_collection_name")
	@Type(value = Types.VARCHAR)
	private String taskCollectionName;

    /**
     * 任务类型code
     */
	@Column(name = "task_type_code")
	@Type(value = Types.VARCHAR)
	private String taskTypeCode;

    /**
     * 任务类型名称
     */
	@Column(name = "task_type_name")
	@Type(value = Types.VARCHAR)
	private String taskTypeName;

    /**
     * 事件处理人eid
     */
	@Column(name = "eid")
	@Type(value = Types.VARCHAR)
	private String eid;

    /**
     * 邮箱前缀
     */
	@Column(name = "domain_name")
	@Type(value = Types.VARCHAR)
	private String domainName;

    /**
     * 展示名称
     */
	@Column(name = "display_name")
	@Type(value = Types.VARCHAR)
	private String displayName;

    /**
     * 纯中文名  用展示名称截取
     */
	@Column(name = "name")
	@Type(value = Types.VARCHAR)
	private String name;

    /**
     * 大区名称
     */
	@Column(name = "region_name")
	@Type(value = Types.VARCHAR)
	private String regionName;

    /**
     * 大区id  来源为hr系统
     */
	@Column(name = "region_id")
	@Type(value = Types.VARCHAR)
	private String regionId;

    /**
     * 完成事件数
     */
	@Column(name = "ttd_eveclo_eve_cnt")
	@Type(value = Types.BIGINT)
	private Long ttdEvecloEveCnt;

    /**
     * 接收事件数
     */
	@Column(name = "ttd_evesed_eve_cnt")
	@Type(value = Types.BIGINT)
	private Long ttdEvesedEveCnt;

    /**
     * 按时完成事件数
     */
	@Column(name = "ttd_ontime_eveclo_eve_cnt")
	@Type(value = Types.BIGINT)
	private Long ttdOntimeEvecloEveCnt;

    /**
     * 超时事件数
     */
	@Column(name = "ttd_overtime_eve_cnt")
	@Type(value = Types.BIGINT)
	private Long ttdOvertimeEveCnt;

    /**
     * 事件处理时长（单位：分钟）
     */
	@Column(name = "ttd_proceve_time")
	@Type(value = Types.DOUBLE)
	private Double ttdProceveTime;

    /**
     * 分区时间
     */
	@Column(name = "query_d")
	@Type(value = Types.VARCHAR)
	private String queryD;

    /**
     * 插入时间
     */
    @Id
	@Column(name = "DataChange_LastTime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getTheDate() {
		return theDate;
	}

	public void setTheDate(String theDate) {
		this.theDate = theDate;
	}

	public String getBizCategoryCode() {
		return bizCategoryCode;
	}

	public void setBizCategoryCode(String bizCategoryCode) {
		this.bizCategoryCode = bizCategoryCode;
	}

	public String getBizCategoryName() {
		return bizCategoryName;
	}

	public void setBizCategoryName(String bizCategoryName) {
		this.bizCategoryName = bizCategoryName;
	}

	public String getTaskCollectionCode() {
		return taskCollectionCode;
	}

	public void setTaskCollectionCode(String taskCollectionCode) {
		this.taskCollectionCode = taskCollectionCode;
	}

	public String getTaskCollectionName() {
		return taskCollectionName;
	}

	public void setTaskCollectionName(String taskCollectionName) {
		this.taskCollectionName = taskCollectionName;
	}

	public String getTaskTypeCode() {
		return taskTypeCode;
	}

	public void setTaskTypeCode(String taskTypeCode) {
		this.taskTypeCode = taskTypeCode;
	}

	public String getTaskTypeName() {
		return taskTypeName;
	}

	public void setTaskTypeName(String taskTypeName) {
		this.taskTypeName = taskTypeName;
	}

	public String getEid() {
		return eid;
	}

	public void setEid(String eid) {
		this.eid = eid;
	}

	public String getDomainName() {
		return domainName;
	}

	public void setDomainName(String domainName) {
		this.domainName = domainName;
	}

	public String getDisplayName() {
		return displayName;
	}

	public void setDisplayName(String displayName) {
		this.displayName = displayName;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getRegionName() {
		return regionName;
	}

	public void setRegionName(String regionName) {
		this.regionName = regionName;
	}

	public String getRegionId() {
		return regionId;
	}

	public void setRegionId(String regionId) {
		this.regionId = regionId;
	}

	public Long getTtdEvecloEveCnt() {
		return ttdEvecloEveCnt;
	}

	public void setTtdEvecloEveCnt(Long ttdEvecloEveCnt) {
		this.ttdEvecloEveCnt = ttdEvecloEveCnt;
	}

	public Long getTtdEvesedEveCnt() {
		return ttdEvesedEveCnt;
	}

	public void setTtdEvesedEveCnt(Long ttdEvesedEveCnt) {
		this.ttdEvesedEveCnt = ttdEvesedEveCnt;
	}

	public Long getTtdOntimeEvecloEveCnt() {
		return ttdOntimeEvecloEveCnt;
	}

	public void setTtdOntimeEvecloEveCnt(Long ttdOntimeEvecloEveCnt) {
		this.ttdOntimeEvecloEveCnt = ttdOntimeEvecloEveCnt;
	}

	public Long getTtdOvertimeEveCnt() {
		return ttdOvertimeEveCnt;
	}

	public void setTtdOvertimeEveCnt(Long ttdOvertimeEveCnt) {
		this.ttdOvertimeEveCnt = ttdOvertimeEveCnt;
	}

	public Double getTtdProceveTime() {
		return ttdProceveTime;
	}

	public void setTtdProceveTime(Double ttdProceveTime) {
		this.ttdProceveTime = ttdProceveTime;
	}

	public String getQueryD() {
		return queryD;
	}

	public void setQueryD(String queryD) {
		this.queryD = queryD;
	}

	public Timestamp getDatachangeLasttime() {
		return datachangeLasttime;
	}

	public void setDatachangeLasttime(Timestamp datachangeLasttime) {
		this.datachangeLasttime = datachangeLasttime;
	}

}
