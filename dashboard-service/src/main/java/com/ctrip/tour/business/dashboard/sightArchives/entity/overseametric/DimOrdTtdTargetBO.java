package com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;

@Entity
@Getter
@Setter
public class DimOrdTtdTargetBO {
    //业务线
    private String buType;
    //邮箱前缀
    private String domainName;
    //考核年
    private String examineYear;
    //考核季
    private String examineQuarter;
    //考核指标类型
    private String examineMetricType;
    //考核层级
    private String examineLevel;
    //考核范围
    private String examineScope;
    //站点
    private String locale;
    //渠道
    private String channel;
}
