package com.ctrip.tour.business.dashboard.tktBusiness.strategy.domestic.impl;

import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.grpBusiness.enums.DomesticMetricEnum;
import com.ctrip.tour.business.dashboard.sightArchives.entity.domesticmetric.Domestic12ParamBean;
import com.ctrip.tour.business.dashboard.sightArchives.entity.domesticmetric.Domestic12ResultBean;
import com.ctrip.tour.business.dashboard.sightArchives.entity.domesticmetric.Domestic12TargetParamBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.DomesticSingelDrillBaseInfo;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ExamineConfigBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.SinglePeriodDataBean;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.SinglePeriodTrendLineBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.UserPermissionBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.bo.ExamineConfigBo;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.Bus3Dao;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardExamineeConfigV2Dao;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardExamineeConfigV2;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.*;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.domestic.DomesticMetricCalStrategy;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

@Component
public class Bus3DomesticMetricStrategy implements DomesticMetricCalStrategy {

    @Autowired
    private Bus3Dao dao;

    @Autowired
    private UserPermissionBiz userPermissionBiz;

    @Autowired
    private RemoteConfig remoteConfig;

    @Autowired
    private BusinessDashboardExamineeConfigV2Dao examineeConfigV2Dao;
    @Autowired
    private SinglePeriodTrendLineBiz singlePeriodTrendLineBiz;

    private Logger log = LoggerFactory.getLogger(Bus3DomesticMetricStrategy.class);

    @Override
    public Future<DomesticMetricDetailInfo> getSingleMetricCardData(String domainName, TimeFilter timeFilter, List<MetricInfoBean> metricInfoBeanList, String d, Boolean isFirst, Integer businessId) throws Exception {
        DomesticMetricDetailInfo metricDetailInfo = new DomesticMetricDetailInfo();

        if (DomesticMetricHelper.existBusinessLevel(businessId, metricInfoBeanList)) {
            metricDetailInfo.setTargetValue(-1.00);
            return new AsyncResult<>(new DomesticMetricDetailInfo());
        }

        metricDetailInfo.setMetricCode(DomesticMetricEnum.getCodeById(getMetricName()));
        Map<String,MetricInfoBean> metricQuarterMap = metricInfoBeanList.stream().collect(Collectors.toMap(MetricInfoBean::getQuarter, metricInfoBean -> metricInfoBean));
        if (metricQuarterMap.isEmpty()) {
            return new AsyncResult<>(new DomesticMetricDetailInfo());
        }
        double ttdWeightedDefectTarget = 0.00;
        double ttdWeightedDefectCnt = 0.00;
        double ttdPayOdrCnt = 0.00;
        double lyTtdWeightedDefectCnt = 0.00;
        double lyTtdPayOdrCnt = 0.00;
        MetricDetailInfo leastQuarterMetricInfo = new MetricDetailInfo();
        String maxQuarter = DateUtil.getMaxQuarters(new ArrayList<>(metricQuarterMap.keySet()));
        String rank = null;
        int targetCount = 0;
        //遍历每个季度累加数据重新计算
        for (Map.Entry<String, MetricInfoBean> entry : metricQuarterMap.entrySet()) {
            String quarter = entry.getKey();
            MetricInfoBean bean = entry.getValue();
            TimeFilter timeFilterWithQuarter = DomesticMetricHelper.getTimeFilterWithQuality(bean, timeFilter);
            Future<MetricDetailInfo> metricDetailInfoFuture = getOldBus3SingleMetricInfo(domainName,timeFilterWithQuarter,bean,d,true);
            MetricDetailInfo metricDetailInfoItem = metricDetailInfoFuture.get();
            if (maxQuarter.equals(quarter)){
                leastQuarterMetricInfo = metricDetailInfoItem;
            }
            if (metricDetailInfoItem != null && metricDetailInfoItem.getDimData() != null) {
                rank = metricDetailInfoItem.getRank();
                double tempTarget = metricDetailInfoItem.getDimData().getOrDefault("ttd_weighted_defect_target", -1.00);
                if (tempTarget == -1.00) {
                    continue;
                }
                targetCount++;
                ttdWeightedDefectTarget += tempTarget;
                ttdWeightedDefectCnt += metricDetailInfoItem.getDimData().getOrDefault("ttd_weighted_defect_cnt", 0.00);
                ttdPayOdrCnt += metricDetailInfoItem.getDimData().getOrDefault("ttd_pay_odr_cnt", 0.00);
                lyTtdPayOdrCnt += metricDetailInfoItem.getDimData().getOrDefault("ly_ttd_pay_odr_cnt", 0.00);
                lyTtdWeightedDefectCnt += metricDetailInfoItem.getDimData().getOrDefault("ly_ttd_weighted_defect_cnt", 0.00);
            }
        }
        ttdWeightedDefectTarget = targetCount == 0 ? 0.00 : ttdWeightedDefectTarget / targetCount;

        double ttdWeightedDefectRate = ttdPayOdrCnt == 0.00 ? 0.00 : ttdWeightedDefectCnt / ttdPayOdrCnt;
        double lyTtdWeightedDefectRate = lyTtdPayOdrCnt == 0.00 ? 0.00 : lyTtdWeightedDefectCnt / lyTtdPayOdrCnt;

        metricDetailInfo.setYoyValue(lyTtdWeightedDefectRate == 0.00 ? 0.00 : ttdWeightedDefectRate / lyTtdWeightedDefectRate - 1);

        double ttdWeightedDefectAchievedRate = 0.00;
        if (GeneralUtil.isValidDivide(ttdWeightedDefectRate,ttdWeightedDefectTarget)) {
            ttdWeightedDefectAchievedRate =(double) 1 - (ttdWeightedDefectRate - ttdWeightedDefectTarget) / ttdWeightedDefectTarget;
        }
        metricDetailInfo.setCompleteRate(ttdWeightedDefectAchievedRate);
        metricDetailInfo.setWeightedDefectRate(ttdWeightedDefectRate);
        metricDetailInfo.setTargetValue(ttdWeightedDefectTarget == 0.00 ? -1 : ttdWeightedDefectTarget);
        metricDetailInfo.setPayOrderCount(ttdPayOdrCnt);
        metricDetailInfo.setCompleteValue(ttdWeightedDefectCnt);

        //复用最近季或者当季的是否能下钻的数据
        if (leastQuarterMetricInfo != null) {
            metricDetailInfo.setDefaultField(leastQuarterMetricInfo.getDefaultField());
            metricDetailInfo.setNeedDrillDown(leastQuarterMetricInfo.isNeedDrillDown());
        }
        if (rank != null) {
            metricDetailInfo.setRank(Integer.parseInt(rank.split("/")[0]));
            metricDetailInfo.setTotalRank(Integer.parseInt(rank.split("/")[1]));
        }
        return new AsyncResult<>(metricDetailInfo);
    }




    public Future<MetricDetailInfo> getOldBus3SingleMetricInfo(String domainName,
                                                               TimeFilter timeFilter,
                                                               MetricInfoBean metricInfoBean,
                                                               String d,
                                                               Boolean needRank) throws Exception {
        MetricDetailInfo metricDetailInfo = new MetricDetailInfo();
        try {
            int y = timeFilter.getYear() != null ? Integer.parseInt(timeFilter.getYear()) : 0;
            String metric = metricInfoBean.getMetric();
            metricDetailInfo.setMetric(metric);

            // 2024年拆分国内和出境日游，所以按照子指标的格式返回
            String subMetric = "domestic";

            metricDetailInfo.setSubMetric(subMetric);
            Map<String, Double> dimMap = new HashMap<>();
            metricDetailInfo.setDimData(dimMap);
            String year = timeFilter.getYear();
            String month = timeFilter.getMonth();
            String quarter = timeFilter.getQuarter();
            String dateType = MetricHelper.getDateTypeColumnName(timeFilter.getDateType());
            List<String> timeList = DateUtil.getTimeList(dateType, month, quarter);

            String level = getLevel(metricInfoBean, subMetric);
            List<String> regionList = getRegionList(metricInfoBean, subMetric);

            DalHints rankDalHints = new DalHints().asyncExecution();
            if (needRank) {
                getRankDataAsync(dateType, year, quarter, month, d, domainName, subMetric, rankDalHints);
            }
            //由于mysql需要设置索引 因此需要保持顺序
            Map<String, List<String>> reachInMap = new HashMap<>();
            reachInMap.put("query_d", Lists.newArrayList(d));
            reachInMap.put("year", Lists.newArrayList(year));
            reachInMap.put(dateType, timeList);
            if (!"".equals(level)) {
                reachInMap.put(level, regionList);
            }
            if (y >= 2024) {
                reachInMap.put("bu_type", Lists.newArrayList(subMetric));
            }
            List<List<Object>> reachList = dao.getMetricCardReachData(reachInMap, year);
            List<String> reachDimList = Bus3Helper.getReachDimList(year);
            ChartHelper.fillOverallDimMap(reachList, reachDimList, dimMap);


            String targetQuarter = DateUtil.getQuarter(dateType, timeFilter.getMonth(), timeFilter.getQuarter());
            Map<String, List<String>> targetInMap = new LinkedHashMap<>();
            targetInMap.put("year", Lists.newArrayList(year));
            targetInMap.put("quarter", Lists.newArrayList(targetQuarter));
            //前置特殊处理  过滤掉2024年的数据
            // targetInMap.put("business",Lists.newArrayList("0"));
            if (!"".equals(level)) {
                targetInMap.put(level, regionList);
            }
            if (y >= 2024) {
                targetInMap.put("bu_type", Lists.newArrayList(subMetric));
            }
            List<List<Object>> targetList = dao.getMetricCardTargetData(targetInMap, year);
            List<String> targetDimList = Bus3Helper.getTargetDimList(year);
            ChartHelper.fillOverallDimMap(targetList, targetDimList, dimMap);
            doubleFormat(dimMap);
            Bus3Helper.makeUpMetricCardData(dimMap, year);

            //获取去年同期数据
            String lastYear = DateUtil.getLastYear(year);
            reachInMap.put("year", Lists.newArrayList(lastYear));
            List<List<Object>> lastYearReachList = dao.getMetricCardReachData(reachInMap, year);
            Map<String, Double> lastYearDimMap = new HashMap<>();
            ChartHelper.fillOverallDimMap(lastYearReachList, reachDimList, lastYearDimMap);
            doubleFormat(lastYearDimMap);
            Bus3Helper.makeUpMetricCardData(lastYearDimMap, year);
            Bus3Helper.makeUpMetricCardPopData(year, dimMap, lastYearDimMap, "");

            // MetricHelper.setMetricCardDrillDownInfo(metricInfoBean, subMetricInfo);
            setMetricCardDrillDownInfo(subMetric, metricInfoBean, metricDetailInfo);
            //获取排名数据
            if (needRank) {
                ChartHelper.fillRankData(metricDetailInfo, rankDalHints.getListResult());
            }
        } catch (Exception e) {
            log.error("metricCard metric 3 getdata fail: " + e.getMessage());
        }
        return new AsyncResult<>(metricDetailInfo);
    }

    /**
     * 获取子指标对应的考核范围
     * @param metricInfoBean
     * @param subMetric
     * @return
     */
    public List<String> getRegionList(MetricInfoBean metricInfoBean, String subMetric){
        if("domestic".equals(subMetric)){
            return metricInfoBean.getRegionList();
        }else if("overseaDayTour".equals(subMetric)){
            return metricInfoBean.getOverseaOdtRegionList();
        }
        // 如果没有子指标，返回国内数据，兼容24年之前
        return metricInfoBean.getRegionList();
    }

    /**
     * 获取子指标对应的考核层级
     * @param metricInfoBean
     * @param subMetric：checkxxx接口已经根据examineType判断了考核层级，所以只需要根据这个值获取对应的考核层级和范围即可
     * @return
     */
    public String getLevel(MetricInfoBean metricInfoBean, String subMetric){
        if("domestic".equals(subMetric)){
            return MetricHelper.getLevelColumnName(metricInfoBean.getLevel());
        }else if("overseaDayTour".equals(subMetric)){
            return MetricHelper.getLevelColumnName(metricInfoBean.getOverseaOdtLevel());
        }
        // 如果没有子指标，返回国内数据，兼容24年之前
        return MetricHelper.getLevelColumnName(metricInfoBean.getLevel());
    }

    /**
     * 对数据进行保留小数位处理
     * @param dimMap
     */
    public void doubleFormat(Map<String, Double> dimMap){
        DecimalFormat df = new DecimalFormat("#.#####");
        df.setRoundingMode(RoundingMode.UP);
        for(String dim: dimMap.keySet()){
            if("ttd_weighted_defect_rate".equals(dim) || "ttd_weighted_defect_target".equals(dim)){
                dimMap.put(dim, Double.parseDouble(df.format(dimMap.get(dim))));
            }
        }
    }

    /**
     * 兼容处理：国内和出境设置默认下钻维度的逻辑
     * @param subMetric
     * @param metricInfoBean
     * @param subMetricInfo
     */
    public void setMetricCardDrillDownInfo(String subMetric,
                                           MetricInfoBean metricInfoBean,
                                           MetricDetailInfo subMetricInfo){
        if("domestic".equals(subMetric)){
            MetricHelper.setMetricCardDrillDownInfo(metricInfoBean, subMetricInfo);
        }else if("overseaDayTour".equals(subMetric)){
            MetricHelper.setOverseaOdtMetricCardDrillDownInfo(metricInfoBean, subMetricInfo, remoteConfig);
        }
    }

    private void getRankDataAsync(String dateType,
                                  String year,
                                  String quarter,
                                  String month,
                                  String d,
                                  String domainName,
                                  String subMetric,
                                  DalHints dalHints) throws Exception {
        int y = year != null ? Integer.parseInt(year) : 0;
        Map<String, List<String>> inMap = new LinkedHashMap<>();
        inMap.put("query_d", Lists.newArrayList(d));
        inMap.put("examinee", Lists.newArrayList(domainName));
        inMap.put("year", Lists.newArrayList(year));
        if ("month".equals(dateType)) {
            inMap.put("month", Lists.newArrayList(month));
        } else {
            String mappingMonth = DateUtil.getMappingMonthByQuarter(d, year, quarter);
            inMap.put("month", Lists.newArrayList(mappingMonth));
        }
        if(y >= 2024){
            inMap.put("bu_type", Lists.newArrayList(subMetric));
        }
        dao.getRankAsync(inMap, dalHints);
    }



    @Override
    public GetDomesticMetricTrendDataResponseType getSingleTrendlineData(GetDomesticMetricTrendDataRequestType request, String d) throws Exception {
        GetDomesticMetricTrendDataResponseType response = new GetDomesticMetricTrendDataResponseType();
        List<TrendLineDetailInfo> trendLineDetailInfoList = new ArrayList<>();
        response.setTrendLineDetailInfoList(trendLineDetailInfoList);
        TimeFilter timeFilter = request.getTimeFilter();
        String domainName = request.getDomainName();

        TimeFilter timeFilterWithDomain = DateUtil.getMaxQuarterTimeFilter(timeFilter, d);
        List<ExamineConfigBean> examineConfigBeanList = MultiPeriodMappingHelper.getMultiPeriodConfigList(timeFilterWithDomain, d, examineeConfigV2Dao.queryMetricAllConfig(domainName, d, String.valueOf(getMetricName())), null);

        TrendLineDetailInfo lineChart = new TrendLineDetailInfo();

        lineChart.setType("lineChart");
        lineChart.setDim(request.getMetricCode());

        List<TrendLineDataItem> lineDataItems = new ArrayList<>();
        lineChart.setTrendLineDataItemList(lineDataItems);
        for (ExamineConfigBean examineConfigBean : examineConfigBeanList) {
            //只下发25年的
            if (Integer.parseInt(examineConfigBean.getYear()) <= 2024) {
                continue;
            }
            //获取单季或单月的数据
            List<TrendLineDataItem> singleLineDataItems = getSingleTrendLineDataWithQuarterOrMonth(request,examineConfigBean, d);
            for (TrendLineDataItem item : singleLineDataItems) {
                switch (item.getName()) {
                    case "weightedDefectRate":
                    case "targetValue":
                        lineDataItems.add(item);
                        break;
                }
            }
        }
        trendLineDetailInfoList.add(lineChart);

        return response;
    }

    public List<TrendLineDataItem> getSingleTrendLineDataWithQuarterOrMonth(GetDomesticMetricTrendDataRequestType request,
                                                                            ExamineConfigBean examineConfigBean,
                                                                            String d) throws Exception {
        List<TrendLineDataItem> trendLineDataItems = new ArrayList<>();
        String domainName = request.getDomainName();
        String timeString = DomesticMetricHelper.getTimeFormat(examineConfigBean);

        ExamineConfigBo bo = new ExamineConfigBo();
        BusinessDashboardExamineeConfigV2 examineeConfigV2 = examineConfigBean.getBusinessDashboardExamineeConfigV2();
        List<MetricInfoBean> metricInfoBeanList = bo.getMetricInfoBeanListV2(Collections.singletonList(examineeConfigV2), remoteConfig);
        if (metricInfoBeanList.size() != 1) {
            return trendLineDataItems;
        }
        TimeFilter timeFilter = DomesticMetricHelper.getTimeFilter(examineConfigBean);
        Future<MetricDetailInfo> metricDetailInfoFuture = getOldBus3SingleMetricInfo(domainName, timeFilter, metricInfoBeanList.get(0), d, true);
        MetricDetailInfo metricDetailInfoItem = metricDetailInfoFuture.get();
        if (metricDetailInfoItem == null || metricDetailInfoItem.getDimData() == null) {
            return trendLineDataItems;
        }
        double ttdWeightedDefectCnt = metricDetailInfoItem.getDimData().getOrDefault("ttd_weighted_defect_cnt", 0.00);
        double ttdPayOdrCnt = metricDetailInfoItem.getDimData().getOrDefault("ttd_pay_odr_cnt", 0.00);

        TrendLineDataItem weightedDefectRateItem = new TrendLineDataItem();
        TrendLineDataItem weightedDefectTargetItem = new TrendLineDataItem();

        weightedDefectRateItem.setTime(timeString);
        weightedDefectTargetItem.setTime(timeString);

        weightedDefectRateItem.setName("weightedDefectRate");
        weightedDefectTargetItem.setName("targetValue");

        weightedDefectRateItem.setValue(ttdPayOdrCnt == 0.00 ? 0.00 : ttdWeightedDefectCnt / ttdPayOdrCnt);
        weightedDefectTargetItem.setValue(metricDetailInfoItem.getDimData().getOrDefault("ttd_weighted_defect_target", 0.00));

        trendLineDataItems.add(weightedDefectRateItem);
        trendLineDataItems.add(weightedDefectTargetItem);

        return trendLineDataItems;
    }

    @Override
    public GetDomesticTableDataResponseType getSingleTableData(GetDomesticTableDataRequestType request, MetricInfoBean metricInfoBean, String d) throws Exception {
        GetDomesticTableDataResponseType response = new GetDomesticTableDataResponseType();
        List<DomesticTableData> tableDataItemList = new ArrayList<>();
        response.setTableDataItemList(tableDataItemList);

        TimeFilter timeFilter = request.getTimeFilter();
        String year = timeFilter.getYear();
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();
        String dateType = MetricHelper.getDateTypeColumnName(timeFilter.getDateType());
        List<String> timeList = DateUtil.getTimeList(dateType, month, quarter);
        String level = MetricHelper.getLevelColumnName(metricInfoBean.getLevel());
        List<String> regionList = metricInfoBean.getRegionList();
        Integer pageNo = request.getPageNo();
        Integer pageSize = request.getPageSize();
        if (pageSize == null) {
            pageSize = 20;
        }

        if ("trend".equalsIgnoreCase(request.getQueryType())) {
            List<TrendLineDetailInfo> trendLineDetailInfoList = getDrillThrendLineDetailInfoList(request, metricInfoBean, d);
            response.setTrendLineDetailInfoList(trendLineDetailInfoList);
            return response;
        }

        DrillDownFilter drillDownFilter = request.getDrillDownFilter();
        String field = MetricHelper.getDrillDownColumnName(drillDownFilter.getField());
        List<String> fieldValueList = drillDownFilter.getFieldValueList();

        response.setTableHeaderList(getTableHeaderList(field));
        List<String> reachGroupTagList = MetricHelper.getTableDrillDownGroupList(field);
        Map<String, List<String>> reachInMap = new LinkedHashMap<>();
        reachInMap.put("query_d", Lists.newArrayList(d));
        reachInMap.put("year", Lists.newArrayList(year));
        reachInMap.put(dateType, timeList);
        if (!"".equals(level)) {
            reachInMap.put(level, regionList);
        }
        if (!GeneralUtil.isEmpty(fieldValueList)) {
            reachInMap.put(field, fieldValueList);
        }
        reachInMap.put("bu_type", Arrays.asList("domestic"));
        Integer totalNum = dao.getTableReachDataCount(reachInMap, reachGroupTagList, year);
        response.setTotalNum(totalNum);
        if(totalNum == 0){
            return response;
        }
        List<List<Object>> currentList = dao.getTableReachData(reachInMap, reachGroupTagList, pageNo, pageSize, year);

        List<String> targetGroupTagList = Lists.newArrayList(field);
        Map<String, List<String>> targetInMap = new LinkedHashMap<>();
        targetInMap.put("year", Lists.newArrayList(year));
        if("half".equalsIgnoreCase(dateType)){
            targetInMap.put("quarter", DateUtil.getQuarterOfHalf(timeFilter.getHalf()));
        }else if("quarter".equalsIgnoreCase(dateType)){
            String targetQuarter = DateUtil.getQuarter(dateType, month, quarter);
            targetInMap.put("quarter", Lists.newArrayList(targetQuarter));
        }else if("month".equalsIgnoreCase(dateType)){
            String targetQuarter = DateUtil.getQuarter(dateType, month, quarter);
            targetInMap.put("quarter", Lists.newArrayList(targetQuarter));
        }

        if (!"".equals(level)) {
            targetInMap.put(level, regionList);
        }
        if (!GeneralUtil.isEmpty(fieldValueList)) {
            targetInMap.put(field, fieldValueList);
        }
        targetInMap.put("bu_type", Arrays.asList("domestic"));

        List<List<Object>> targetList = dao.getSpilitTargetData(targetInMap, targetGroupTagList, year);
        List<String> reachDimList = Bus3Helper.getReachDimList(year);
        List<String> targetDimList = Bus3Helper.getTargetDimList(year);
        List<TableDataItem> tableDataOldItemList = new ArrayList<>();
        ChartHelper.fillCommmonTableDataV2(tableDataOldItemList, reachGroupTagList, targetGroupTagList,
                reachDimList, targetDimList, currentList, targetList);
        Bus3Helper.makeUpTableData(tableDataOldItemList, year);
        for (TableDataItem oldItem : tableDataOldItemList) {
            Map<String, String> fieldMap = oldItem.getFieldMap();
            Map<String, Double> dimMap = oldItem.getDimMap();
            DomesticTableData domesticTableData = new DomesticTableData();
            domesticTableData.setRegionName(fieldMap.get("region_name"));
            domesticTableData.setProvinceName(fieldMap.get("province_name"));
            //目标值
            domesticTableData.setTargetRate(dimMap.get("ttd_weighted_defect_target"));
            //达成率
            domesticTableData.setCompleteRate(dimMap.get("ttd_weighted_defect_achieved_rate"));
            //支付订单量
            domesticTableData.setPayOrdCnt(dimMap.get("ttd_pay_odr_cnt"));
            //加权缺陷数
            domesticTableData.setWeightedDefectCnt(dimMap.get("ttd_weighted_defect_cnt"));
            //加权缺陷率
            domesticTableData.setWeightedDefectRate(dimMap.get("ttd_weighted_defect_rate"));
            tableDataItemList.add(domesticTableData);
        }

        return response;
    }

    private List<TrendLineDetailInfo> getDrillThrendLineDetailInfoList(GetDomesticTableDataRequestType request, MetricInfoBean metricInfoBean, String d) throws Exception {
        List<TrendLineDetailInfo> trendLineDetailInfoList = new ArrayList<>();
        TimeFilter timeFilter = request.getTimeFilter();
        DrillDownFilter drillDownFilter = request.getDrillDownFilter();
        String field = MetricHelper.getDrillDownColumnName(drillDownFilter.getField());
        Integer metricId = DomesticMetricEnum.getIdByCode(request.getMetricCode());
        String domainName = request.getDomainName();
        TimeFilter timeFilterWithDomain = DateUtil.getMaxQuarterTimeFilter(timeFilter, d);
        List<ExamineConfigBean> examineConfigBeanList = MultiPeriodMappingHelper.getMultiPeriodConfigList(timeFilterWithDomain, d, examineeConfigV2Dao.queryMetricAllConfig(domainName, d, String.valueOf(metricId)), null);

        List<Future<SinglePeriodDataBean>> futureList = new ArrayList<>();
        for (ExamineConfigBean examineConfigBean : examineConfigBeanList) {
            GetTrendLineDataRequestType requestOld=new GetTrendLineDataRequestType();
            requestOld.setTimeFilter(request.getTimeFilter());
            requestOld.setMetric(DomesticMetricEnum.getMetricByCode(request.getMetricCode()));
            requestOld.setSubMetric("domestic");
            requestOld.setQueryType("drilldown");
            requestOld.setDrillDownFilter(request.getDrillDownFilter());
            requestOld.setDomainName(request.getDomainName());
            futureList.add(singlePeriodTrendLineBiz.getBus3SinglePeriodTrendLineData(requestOld, examineConfigBean, d));
        }
        List<List<Object>> reachList = new ArrayList<>();
        List<List<Object>> targetList = new ArrayList<>();
        for (Future<SinglePeriodDataBean> futureResult : futureList) {
            SinglePeriodDataBean singlePeriodDataBean = futureResult.get();
            List<List<Object>> periodReachList = singlePeriodDataBean.getPeriodReachList();
            List<List<Object>> periodTargetList = singlePeriodDataBean.getPeriodTargetList();
            if (!GeneralUtil.isEmpty(periodReachList)) {
                reachList.addAll(periodReachList);
            }
            if (!GeneralUtil.isEmpty(periodTargetList)) {
                targetList.addAll(periodTargetList);
            }
        }

        Map<String, Double> dimMap = new HashMap<>();
        Set<String> drillDownSet = new HashSet<>();
        List<String> reachDimList = Bus3Helper.getReachDimList(timeFilter.getYear());
        List<String> targetDimList = Bus3Helper.getTargetDimList(timeFilter.getYear());
        ChartHelper.setMonthlyMetricTrendlineActualReach(dimMap, drillDownSet, reachList, Lists.newArrayList("time", field), reachDimList);
        ChartHelper.setMonthlyMetricTrendlineActualReach(dimMap, drillDownSet, targetList, Lists.newArrayList("time", field), targetDimList);

        //有效时间范围
        List<String> timeList = DateUtil.getAllSelectedTime(timeFilter, "domestic");
        ChartHelper.fillLineChartTrendLineDataWithDrillDown(null, timeList, dimMap,
                trendLineDetailInfoList, Bus3Helper.getLineChartTrendlineTypeWithDrillDown(timeFilter.getYear()), drillDownSet, false);

        return trendLineDetailInfoList;
    }

    @Override
    public GetDomesticDrillDownBaseInfoResponseType getSingleDrillDownBaseInfo(GetDomesticDrillDownBaseInfoRequestType request, MetricInfoBean metricInfoBean, String d) throws Exception {
        GetDomesticDrillDownBaseInfoResponseType response = new GetDomesticDrillDownBaseInfoResponseType();
        List<FieldDataItem> fieldDataItemList = new ArrayList<>();
        response.setFieldDataItemList(fieldDataItemList);

        TimeFilter timeFilter = request.getTimeFilter();
        String year = timeFilter.getYear();
        String dateType = timeFilter.getDateType();
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();

        String metric = metricInfoBean.getMetric();
        List<String> regionList = metricInfoBean.getRegionList();
        String level = metricInfoBean.getLevel();
        String levelColumnName = MetricHelper.getLevelColumnName(level);

        boolean needSearch = StringUtils.isNotEmpty(request.getSearchWord());
        String searchWord = request.getSearchWord();
        List<String> fieldList = Bus3Helper.getFieldList(level);

        if(needSearch){
            String searchField = request.getSearchField();//大区 省份
            if (searchField != null) {
                String searchFieldColumnName = MetricHelper.getDrillDownColumnName(searchField);
                fieldList = Lists.newArrayList(searchFieldColumnName);
            }
        }else{
            response.setDefaultChosenField(MetricHelper.getDefaultChosenField(level, metric));
        }
        String tableName = "bus_3_weighted_defect_province_finish_t";
        Map<String, List<String>> inMap = new LinkedHashMap<>();
        inMap.put("query_d", Lists.newArrayList(d));
        inMap.put("year", Lists.newArrayList(year));
        inMap.put(dateType, DateUtil.getTimeList(dateType, month, quarter));
        if (!"".equals(levelColumnName)) {
            inMap.put(levelColumnName, regionList);
        }
        inMap.put("bu_type", Lists.newArrayList("domestic"));
        for (String field : fieldList) {
            List<String> tagList = Lists.newArrayList(field);
            Map<String,String> likeMap = new HashMap<>();
            if(needSearch){
                likeMap.put(field,searchWord);
            }
            List<List<Object>> rawObjectList = dao.getFieldList(tableName, inMap, likeMap, tagList);
            ChartHelper.fillFieldDataItemList(field, rawObjectList, fieldDataItemList);
        }
        for(FieldDataItem item: fieldDataItemList ){
            item.setNeedBubble(false);
            item.setNeedLine(false);
            DomesticSingelDrillBaseInfo domesticSingelDrillBaseInfo = remoteConfig.getDrillDownFieldBeanV2(request.getMetricCode(), request.getBusinessId(), request.getSubBusinessId(), item.getField());
            if (domesticSingelDrillBaseInfo != null) {
                item.setNeedBubble(domesticSingelDrillBaseInfo.getNeedBubble());
                item.setNeedLine(domesticSingelDrillBaseInfo.getNeedTrend());
            }
        }
        return response;
    }

    public static List<String> getTableHeaderList(String field) {
        List<String> tableHeaderList = new ArrayList<>();
        switch (field) {
            case "region_name":
                tableHeaderList.add("regionName");
                break;
            case "province_name":
                tableHeaderList.add("regionName");
                tableHeaderList.add("provinceName");
                break;
        }
        tableHeaderList.add("targetRate");
        tableHeaderList.add("weightedDefectCnt");
        tableHeaderList.add("payOrdCnt");
        tableHeaderList.add("weightedDefectRate");
        tableHeaderList.add("completeRate");
        return tableHeaderList;
    }
    public static List<String> getFirstTableHeaderList(String field) {
        List<String> tableHeaderList = new ArrayList<>();
        switch (field) {
            case "region_name":
                tableHeaderList.add("regionName");
                break;
            case "province_name":
                tableHeaderList.add("regionName");
                tableHeaderList.add("provinceName");
                break;
        }
        tableHeaderList.add("completeRate");
        tableHeaderList.add("weightedDefectRate");
        tableHeaderList.add("yoyValue");
        return tableHeaderList;
    }

    @Override
    public GetFirstPageDomesticMetricCardDrillDataResponseType getFirstPageDomesticMetricCardDrillData(
            GetFirstPageDomesticMetricCardDrillDataRequestType request,MetricInfoBean metricInfoBean, String d) throws Exception{
        GetFirstPageDomesticMetricCardDrillDataResponseType response = new GetFirstPageDomesticMetricCardDrillDataResponseType();
        List<FirstPageDomesticTableData> tableDataItemList = new ArrayList<>();
        response.setTableDataItemList(tableDataItemList);
        TimeFilter timeFilter = request.getTimeFilter();
        String year = timeFilter.getYear();
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();
        String dateType = MetricHelper.getDateTypeColumnName(timeFilter.getDateType());
        List<String> timeList = DateUtil.getTimeList(dateType, month, quarter);
        String level = MetricHelper.getLevelColumnName(metricInfoBean.getLevel());
        List<String> regionList = metricInfoBean.getRegionList();
        Integer pageNo = 1;
        Integer pageSize = 20;

        DrillDownFilter drillDownFilter = new DrillDownFilter();
        drillDownFilter.setField(request.getDefaultField());
        String field = MetricHelper.getDrillDownColumnName(drillDownFilter.getField());
        List<String> fieldValueList = drillDownFilter.getFieldValueList();

        response.setTableHeaderList(getFirstTableHeaderList(field));
        List<String> reachGroupTagList = MetricHelper.getTableDrillDownGroupList(field);
        Map<String, List<String>> reachInMap = new LinkedHashMap<>();
        reachInMap.put("query_d", Lists.newArrayList(d));
        reachInMap.put("year", Lists.newArrayList(year));
        reachInMap.put(dateType, timeList);
        if (!"".equals(level)) {
            reachInMap.put(level, regionList);
        }
        if (!GeneralUtil.isEmpty(fieldValueList)) {
            reachInMap.put(field, fieldValueList);
        }
        reachInMap.put("bu_type", Arrays.asList("domestic"));

        List<List<Object>> currentList = dao.getTableReachData(reachInMap, reachGroupTagList, pageNo, pageSize, year);

        List<String> targetGroupTagList = Lists.newArrayList(field);
        Map<String, List<String>> targetInMap = new LinkedHashMap<>();
        targetInMap.put("year", Lists.newArrayList(year));
        if("half".equalsIgnoreCase(dateType)){
            targetInMap.put("quarter", DateUtil.getQuarterOfHalf(timeFilter.getHalf()));
        }else if("quarter".equalsIgnoreCase(dateType)){
            String targetQuarter = DateUtil.getQuarter(dateType, month, quarter);
            targetInMap.put("quarter", Lists.newArrayList(targetQuarter));
        }else if("month".equalsIgnoreCase(dateType)){
            String targetQuarter = DateUtil.getQuarter(dateType, month, quarter);
            targetInMap.put("quarter", Lists.newArrayList(targetQuarter));
        }

        if (!"".equals(level)) {
            targetInMap.put(level, regionList);
        }
        if (!GeneralUtil.isEmpty(fieldValueList)) {
            targetInMap.put(field, fieldValueList);
        }
        targetInMap.put("bu_type", Arrays.asList("domestic"));

        List<List<Object>> targetList = dao.getSpilitTargetData(targetInMap, targetGroupTagList, year);
        List<String> reachDimList = Bus3Helper.getReachDimList(year);
        List<String> targetDimList = Bus3Helper.getTargetDimList(year);
        List<TableDataItem> tableDataOldItemList = new ArrayList<>();
        ChartHelper.fillCommmonTableDataV2(tableDataOldItemList, reachGroupTagList, targetGroupTagList,
                reachDimList, targetDimList, currentList, targetList);
        Bus3Helper.makeUpTableData(tableDataOldItemList, year);

        //lastYear
        Integer lastYear = Integer.valueOf(year) - 1;
        reachInMap.put("year", Lists.newArrayList(year));
        List<List<Object>> lastYearList = dao.getTableReachData(reachInMap, reachGroupTagList, pageNo, pageSize, lastYear.toString());
        ChartHelper.fillTableSingleDimPopData(tableDataOldItemList, lastYearList, reachGroupTagList, "_lastyear", reachDimList, "");
        for (TableDataItem oldItem : tableDataOldItemList) {
            Map<String, String> fieldMap = oldItem.getFieldMap();
            Map<String, Double> dimMap = oldItem.getDimMap();
            FirstPageDomesticTableData domesticTableData = new FirstPageDomesticTableData();
            domesticTableData.setRegionName(fieldMap.get("region_name"));
            domesticTableData.setProvinceName(fieldMap.get("province_name"));
            //目标值
            domesticTableData.setTargetRate(dimMap.get("ttd_weighted_defect_target"));
            //达成率
            domesticTableData.setCompleteRate(dimMap.get("ttd_weighted_defect_achieved_rate"));
            //加权缺陷率
            domesticTableData.setWeightedDefectRate(dimMap.get("ttd_weighted_defect_rate"));
            //同比去年
            domesticTableData.setYoyValue(dimMap.get("ttd_weighted_defect_rate_lastyear"));
            tableDataItemList.add(domesticTableData);
        }
        return response;
    }
    @Override
    public Integer getMetricName() {
        return 3;
    }
}
