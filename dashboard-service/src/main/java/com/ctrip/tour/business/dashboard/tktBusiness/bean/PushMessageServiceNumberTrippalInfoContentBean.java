package com.ctrip.tour.business.dashboard.tktBusiness.bean;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class PushMessageServiceNumberTrippalInfoContentBean {
    int idx;   // 序号
    int type;  // 0 means TextNode, 1 means HtmlNode
    String tag;  // html标签；穷举值：http://conf.ctripcorp.com/pages/viewpage.action?pageId=1110538132
    String text; // 内容
    List<PushMessageServiceNumberTrippalInfoContentBean> children;  // 同PushMessageServiceNumberTrippalInfoContentBean
}
