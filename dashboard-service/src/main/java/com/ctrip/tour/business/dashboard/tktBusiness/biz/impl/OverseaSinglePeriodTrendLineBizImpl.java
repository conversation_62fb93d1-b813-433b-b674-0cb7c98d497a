package com.ctrip.tour.business.dashboard.tktBusiness.biz.impl;

import com.ctrip.soa._24922.DrillDownFilter;
import com.ctrip.soa._24922.GetOverseaTrendLineDataRequestType;
import com.ctrip.soa._27181.BIBaseReportQueryServiceClient;
import com.ctrip.soa._27181.GetRawDataRequestType;
import com.ctrip.soa._27181.GetRawDataResponseType;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ExamineConfigBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.SinglePeriodDataBean;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.OverseaSinglePeriodTrendLineBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.OverseaMetricHelper;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.SwitchNewTableHelper;
import com.google.common.util.concurrent.ListenableFuture;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.Future;

@Service
public class OverseaSinglePeriodTrendLineBizImpl implements OverseaSinglePeriodTrendLineBiz {


    @Autowired
    private BIBaseReportQueryServiceClient baseReportQueryServiceClient;

    @Autowired
    private RemoteConfig remoteConfig;

    @Autowired
    private SwitchNewTableHelper switchNewTableHelper;

    @Override
    public Future<SinglePeriodDataBean> getBus101102SubSinglePeriodTrendLineData(GetRawDataRequestType currentReq,
                                                                                 GetRawDataRequestType targetReq,
                                                                                 GetRawDataRequestType lastyearReq,
                                                                                 GetRawDataRequestType _2019Req,
                                                                                 ExamineConfigBean examineConfig) throws Exception {

        String time = examineConfig.getTimeMap().get("currentTime");
        SinglePeriodDataBean singlePeriodDataBean = new SinglePeriodDataBean();
        //获取当期数据
        ListenableFuture<GetRawDataResponseType> currentResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(currentReq);
        //获取目标
        ListenableFuture<GetRawDataResponseType> targetResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(targetReq);
        //获取去年数据
        ListenableFuture<GetRawDataResponseType> lastyearResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(lastyearReq);
        //获取2019年数据
        ListenableFuture<GetRawDataResponseType> _2019ResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(_2019Req);


        GetRawDataResponseType currentRes = currentResFuture.get();
        singlePeriodDataBean.setPeriodReachList(currentRes, time);
        singlePeriodDataBean.setReachHeaderList(currentRes.getMetricList());

        GetRawDataResponseType targetRes = targetResFuture.get();
        singlePeriodDataBean.setPeriodTargetList(targetRes, time);
        singlePeriodDataBean.setTargetHeaderList(targetRes.getMetricList());

        GetRawDataResponseType lastyearRes = lastyearResFuture.get();
        singlePeriodDataBean.setPeriodLastyearList(currentRes, lastyearRes, time);
        singlePeriodDataBean.setLastyearHeaderList(lastyearRes.getMetricList());

        GetRawDataResponseType _2019Res = _2019ResFuture.get();
        singlePeriodDataBean.setPeriod2019List(currentRes, _2019Res, time);
        singlePeriodDataBean.set2019HeaderList(_2019Res.getMetricList());

        return new AsyncResult<>(singlePeriodDataBean);
    }

    @Override
    public Future<SinglePeriodDataBean> getBus103SubSinglePeriodTrendLineData(GetOverseaTrendLineDataRequestType request,
                                                                              OverseaMetricInfoBean metricInfoBean,
                                                                              GetRawDataRequestType currentReq,
                                                                              GetRawDataRequestType targetReq,
                                                                              ExamineConfigBean examineConfig) throws Exception {

        String time = examineConfig.getTimeMap().get("currentTime");
        SinglePeriodDataBean singlePeriodDataBean = new SinglePeriodDataBean();

        DrillDownFilter drillDownFilter = request.getDrillDownFilter();
        String queryType = request.getQueryType();
        //下钻场景
        if ("drilldown".equals(queryType)) {
            //如果在某个考核周期都不满足下钻的基本要求 直接返回
            if (!OverseaMetricHelper.checkLine(drillDownFilter.getField(), metricInfoBean.getDestinationLevel(), request.getSubMetric(), remoteConfig)) {
                return new AsyncResult<>(singlePeriodDataBean);
            }
        }

        //获取当期数据
        ListenableFuture<GetRawDataResponseType> currentResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(currentReq);

        //下钻趋势线不需要查看目标数据
        if ("trendline".equals(queryType)) {
            //获取目标
            ListenableFuture<GetRawDataResponseType> targetResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(targetReq);
            GetRawDataResponseType targetRes = targetResFuture.get();
            singlePeriodDataBean.setPeriodTargetList(targetRes, time);
            singlePeriodDataBean.setTargetHeaderList(targetRes.getMetricList());
        }


        GetRawDataResponseType currentRes = currentResFuture.get();
        singlePeriodDataBean.setPeriodReachList(currentRes, time);
        singlePeriodDataBean.setReachHeaderList(currentRes.getMetricList(), currentRes.getGroupList());


        return new AsyncResult<>(singlePeriodDataBean);
    }

    @Override
    public Future<SinglePeriodDataBean> getBus105106107SubSinglePeriodTrendLineData(GetOverseaTrendLineDataRequestType request,
                                                                                    OverseaMetricInfoBean metricInfoBean,
                                                                                    GetRawDataRequestType currentReq,
                                                                                    ExamineConfigBean examineConfig,
                                                                                    Integer actualGapDays) throws Exception {
        String time = examineConfig.getTimeMap().get("currentTime");
        SinglePeriodDataBean singlePeriodDataBean = new SinglePeriodDataBean();

        DrillDownFilter drillDownFilter = request.getDrillDownFilter();
        String queryType = request.getQueryType();

        //下钻场景
        if ("drilldown".equals(queryType)) {
            //如果在某个考核周期都不满足下钻的基本要求 直接返回
            if (!OverseaMetricHelper.checkLine(drillDownFilter.getField(), metricInfoBean.getDestinationLevel(), request.getSubMetric(), remoteConfig)) {
                return new AsyncResult<>(singlePeriodDataBean);
            }
        }

        //获取当期数据
        GetRawDataResponseType currentRes = switchNewTableHelper.switchRemoteDatabase(currentReq);
        List<String> groupList = Optional.ofNullable(currentRes.getGroupList()).orElse(new ArrayList<>());
        singlePeriodDataBean.setPeriodWeaknessList(currentRes, actualGapDays, time, groupList);
        singlePeriodDataBean.setWeaknessHeaderList(currentRes.getMetricList(), groupList);

        return new AsyncResult<>(singlePeriodDataBean);
    }
}
