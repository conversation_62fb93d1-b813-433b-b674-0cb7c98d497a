package com.ctrip.tour.business.dashboard.grpBusiness.handler.dim;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang.StringUtils;

import com.ctrip.tour.business.dashboard.grpBusiness.common.GrpConstant;
import com.ctrip.tour.business.dashboard.grpBusiness.enums.MetricCategoryEnum;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Maps;

/**
 * <AUTHOR>
 * @Date 2024/12/23
 */
public class DimconvertHandler {

    static Map<String, String> dimEnumMap = Maps.newHashMap();

    static ImmutableList<String> localPmEidCategoryList = ImmutableList.of(MetricCategoryEnum.INCOME_CATEGORY.getEnglishName(), MetricCategoryEnum.MULTIPLE_PRICE_CATEGORY.getEnglishName(),
           MetricCategoryEnum.PROFIT_PRICE_CATEGORY.getEnglishName(), MetricCategoryEnum.INFO_SCORE_CATEGORY.getEnglishName(), MetricCategoryEnum.TOP_RATE_DRIVER_CATEGORY.getEnglishName(),
            MetricCategoryEnum.TOP_RATE_GUIDER_CATEGORY.getEnglishName(), MetricCategoryEnum.HIGH_QUA_VENDOR_COUNT_WITH_SELFOPR_PRD_CATEGORY.getEnglishName(), MetricCategoryEnum.SELF_OPR_PARENT_PRD_COUNT_CATEGORY.getEnglishName());

    private static final String OR = "OR-";
    private final static String BU_TYPE = "bu_type";
    private final static String SUB_BU_TYPE = "sub_bu_type";
    private final static String PRD_PATTERN_NAME = "prd_pattern_name";
    private final static String DEST_DOMAIN = "dest_domain";
    private final static String SALE_MODE_NAME = "sale_mode_name";
    private final static String SALE_CHANNEL_NAME = "sale_channel_name";
    private final static String TOUR_REGION_TYPE = "tour_region_type";
    private final static String PRD_REGION_NAME = "prd_region_name";
    private final static String PM_EID = "pm_eid";
    private final static String LOCAL_PM_EID = "local_pm_eid";
    private final static String DEST_FIRST_REGION = "dest_first_region";
    private final static String DEST_CITY_NAME = "dest_city_name";
    private final static String VENDOR_NAME = "vendor_name";
    private final static String IS_SEC_KILL = "is_sec_kill";
    private final static String VENDOR_ID = "vendor_id";

    private final static String DOMAIN = "domain";
    private final static String GRADE_REGION_NAME = "grade_region_name";
    private final static String DESTINATIONLINE = "destinationline";
    private final static String VBK_PROVIDER_NAME = "vbk_provider_name";
    private final static String BUSINESS_REGION_NAME = "business_region_name";
    private final static String PLC_SALE_MODE = "plc_sale_mode";
    private final static String BIZ_STAT_SALE_MODE = "bu_sale_mode";
    private final static String L1_ROUTE_NAME = "l1_route_name";
    private final static String PRD_LEVEL_NAME = "prd_level_name";
    private final static String PK_TOUR_LINE = "pk_tour_line";
    private final static String CPR_DEST_AREA = "cpr_dest_area";

    static {
        dimEnumMap.put("产线", SUB_BU_TYPE);//NOSONAR
        dimEnumMap.put("产品形态", PRD_PATTERN_NAME);//NOSONAR
        dimEnumMap.put("区域", DEST_DOMAIN);//NOSONAR
        dimEnumMap.put("销售渠道", SALE_CHANNEL_NAME);//NOSONAR
        dimEnumMap.put("销售模式", SALE_MODE_NAME);//NOSONAR
        dimEnumMap.put("客源地/目的地", TOUR_REGION_TYPE);//NOSONAR
        dimEnumMap.put("产品大区", PRD_REGION_NAME);//NOSONAR
        dimEnumMap.put("业务经理", PM_EID);//NOSONAR
        dimEnumMap.put("运营大区", DEST_FIRST_REGION);//NOSONAR
        dimEnumMap.put("目的地", DEST_CITY_NAME);//NOSONAR
        dimEnumMap.put("供应商ID", VENDOR_ID);//NOSONAR
        dimEnumMap.put("供应商名称", VENDOR_NAME);//NOSONAR
        dimEnumMap.put("是否参与秒杀", IS_SEC_KILL);//NOSONAR
        dimEnumMap.put("驻地业务经理", LOCAL_PM_EID);//NOSONAR
        dimEnumMap.put("境内外", DOMAIN);//NOSONAR
        dimEnumMap.put("大区", BUSINESS_REGION_NAME);//NOSONAR
        dimEnumMap.put("评级区域", GRADE_REGION_NAME);//NOSONAR
        dimEnumMap.put("目的地区域", DESTINATIONLINE);//NOSONAR
        dimEnumMap.put("供应商", VBK_PROVIDER_NAME);//NOSONAR
        dimEnumMap.put("下单时销售模式", PLC_SALE_MODE);//NOSONAR
        dimEnumMap.put("业务销售模式", BIZ_STAT_SALE_MODE);//NOSONAR
        dimEnumMap.put("线路玩法（L1）", L1_ROUTE_NAME);//NOSONAR
        dimEnumMap.put("钻级", PRD_LEVEL_NAME);//NOSONAR
        dimEnumMap.put("线路玩法·钻级·天数·是否拼小团", PK_TOUR_LINE);//NOSONAR
        dimEnumMap.put("竞争圈", CPR_DEST_AREA);//NOSONAR
    }

    public static String convertDimEnumName(String name, String categoryName) {
        if (StringUtils.isNotBlank(categoryName) && StringUtils.equals("驻地业务经理", name)) {//NOSONAR
            return getLocalPmEidName(categoryName);
        }
        return dimEnumMap.getOrDefault(name, "");
    }

    public static String getLocalPmEidName(String categoryName) {
        if (localPmEidCategoryList.contains(categoryName)) {
            return "local_pmeid";
        } else {
            return LOCAL_PM_EID;
        }
    }
}
