package com.ctrip.tour.business.dashboard.tktBusiness.bean;


import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class EmailGroupBean {
    @SerializedName(value = "emp_code")
    String empCode;
    @SerializedName(value = "ad_account")
    String adAccount;
    @SerializedName(value = "ad_netbs_name")
    String adNetbsName;
    @SerializedName(value = "guid")
    String guid;
    @SerializedName(value = "display_name")
    String displayName;
    @SerializedName(value = "mail_address")
    String mailAddress;
}
