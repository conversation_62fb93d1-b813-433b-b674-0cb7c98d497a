package com.ctrip.tour.business.dashboard.grpBusiness.domain.process.dsl.common;

import com.ctrip.soa._24922.DSLRequestType;
import com.ctrip.soa._24922.EnumOperators;
import com.ctrip.soa._24922.GetGrpOrgTabListResponseType;
import com.ctrip.soa._24922.WhereCondition;
import com.ctrip.tour.business.dashboard.grpBusiness.config.GrpConfig;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.AbstractPreDSLProcess;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.model.DomainConfig;
import com.ctrip.tour.business.dashboard.grpBusiness.service.HrOrgEmpInfoService;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;

public class DSLProcessBusinessLine extends AbstractPreDSLProcess {
    private Integer businessType;
    private HrOrgEmpInfoService hrOrgEmpInfoService;
    private GrpConfig grpConfig;

    public static AbstractPreDSLProcess getInstance(Integer businessType, HrOrgEmpInfoService hrOrgEmpInfoService, GrpConfig grpConfig) {
        DSLProcessBusinessLine dslProcessBusinessLine = new DSLProcessBusinessLine();
        dslProcessBusinessLine.businessType = businessType;
        dslProcessBusinessLine.hrOrgEmpInfoService = hrOrgEmpInfoService;
        dslProcessBusinessLine.grpConfig = grpConfig;
        return dslProcessBusinessLine;
    }

    @Override
    public DSLRequestType process(DSLRequestType dslRequestType, EarlyReturn earlyReturn) {
        if (businessType == null || businessType <= 0) {
            GetGrpOrgTabListResponseType grpOrgTabListV2;
            try {
                grpOrgTabListV2 = hrOrgEmpInfoService.getGrpOrgTabListV2();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            if (grpOrgTabListV2 != null) {
                businessType = grpOrgTabListV2.defaultTabList;
            }
        }
        // 默认的业务线
        if (businessType == null || businessType <= 0) {
            earlyReturn.setStatus(-1);
            return dslRequestType;
        }
        // 初始化
        if (dslRequestType.getWhereCondition() == null) {
            dslRequestType.setWhereCondition(new WhereCondition());
        }
        if (dslRequestType.getWhereCondition().getSubWhereConditions() == null) {
            dslRequestType.getWhereCondition().setSubWhereConditions(new ArrayList<>());
        }

        // 业务线
        WhereCondition whereConditionProductLine = new WhereCondition();

        dslRequestType.getWhereCondition().getSubWhereConditions().add(whereConditionProductLine);
        whereConditionProductLine.setFilterName("business_line");
        whereConditionProductLine.setOperators(EnumOperators.IN);
        whereConditionProductLine.setFilterValues(Collections.singletonList(String.valueOf(businessType)));
        // config配置
        if (dslRequestType.getExtra() == null) {
            dslRequestType.setExtra(new HashMap<>());
            String key = DomainConfig.getAndUpdateDomainWithBusinessLineWithFileName(businessType, grpConfig);
            dslRequestType.getExtra().put("domain_config", key);
        }
        return dslRequestType;
    }
}
