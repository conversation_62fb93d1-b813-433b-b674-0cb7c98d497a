package com.ctrip.tour.business.dashboard.grpBusiness.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Sensitive;
import com.ctrip.platform.dal.dao.annotation.Type;
import java.sql.Types;
import java.sql.Timestamp;
import java.util.List;

import com.ctrip.platform.dal.dao.DalPojo;

/**
 * <AUTHOR>
 * @date 2024-12-12
 */
@Entity
@Database(name = "TtdReportDB_W")
@Table(name = "dim_org_tree_vacation")
public class DimOrgTreeVacation implements DalPojo {

    /**
     * 主键
     */
    @Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long id;

    /**
     * 节点组织编码
     */
	@Column(name = "node_org_id")
	@Type(value = Types.VARCHAR)
	private String nodeOrgId;

    /**
     * 节点组织类型
     */
	@Column(name = "node_org_type")
	@Type(value = Types.VARCHAR)
	private String nodeOrgType;

    /**
     * 节点组织名称
     */
	@Column(name = "node_org_name")
	@Type(value = Types.VARCHAR)
	private String nodeOrgName;

    /**
     * 节点组织层级
     */
	@Column(name = "node_org_level")
	@Type(value = Types.VARCHAR)
	private String nodeOrgLevel;

    /**
     * 父组织编码
     */
	@Column(name = "parent_org_id")
	@Type(value = Types.VARCHAR)
	private String parentOrgId;

    /**
     * 事业部编码
     */
	@Column(name = "bu_id")
	@Type(value = Types.VARCHAR)
	private String buId;

    /**
     * 部门编码
     */
	@Column(name = "dept_id")
	@Type(value = Types.VARCHAR)
	private String deptId;

    /**
     * 组织编码全路径
     */
	@Column(name = "org_id_path")
	@Type(value = Types.VARCHAR)
	private String orgIdPath;

    /**
     * 组织名称全路径
     */
	@Column(name = "org_name_path")
	@Type(value = Types.VARCHAR)
	private String orgNamePath;

    /**
     * 领导工号
     */
	@Column(name = "leader_emp_code")
	@Type(value = Types.VARCHAR)
	private String leaderEmpCode;

    /**
     * 领导姓名
     */
	@Column(name = "leader_emp_name")
	@Type(value = Types.VARCHAR)
	private String leaderEmpName;

    /**
     * 员工数量
     */
	@Column(name = "emp_cnt")
	@Type(value = Types.VARCHAR)
	private String empCnt;

    /**
     * 分区日期
     */
	@Column(name = "partition_d")
	@Type(value = Types.VARCHAR)
	private String partitionD;

    /**
     * 更新时间
     */
	@Column(name = "datachange_lasttime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;

	// 子部门
	public List<DimOrgTreeVacation> subDept;
	public List<EdwHrEmpVacation> directEmp;
	public EdwHrEmpVacation Leader;


	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getNodeOrgId() {
		return nodeOrgId;
	}

	public void setNodeOrgId(String nodeOrgId) {
		this.nodeOrgId = nodeOrgId;
	}

	public String getNodeOrgType() {
		return nodeOrgType;
	}

	public void setNodeOrgType(String nodeOrgType) {
		this.nodeOrgType = nodeOrgType;
	}

	public String getNodeOrgName() {
		return nodeOrgName;
	}

	public void setNodeOrgName(String nodeOrgName) {
		this.nodeOrgName = nodeOrgName;
	}

	public String getNodeOrgLevel() {
		return nodeOrgLevel;
	}

	public void setNodeOrgLevel(String nodeOrgLevel) {
		this.nodeOrgLevel = nodeOrgLevel;
	}

	public String getParentOrgId() {
		return parentOrgId;
	}

	public void setParentOrgId(String parentOrgId) {
		this.parentOrgId = parentOrgId;
	}

	public String getBuId() {
		return buId;
	}

	public void setBuId(String buId) {
		this.buId = buId;
	}

	public String getDeptId() {
		return deptId;
	}

	public void setDeptId(String deptId) {
		this.deptId = deptId;
	}

	public String getOrgIdPath() {
		return orgIdPath;
	}

	public void setOrgIdPath(String orgIdPath) {
		this.orgIdPath = orgIdPath;
	}

	public String getOrgNamePath() {
		return orgNamePath;
	}

	public void setOrgNamePath(String orgNamePath) {
		this.orgNamePath = orgNamePath;
	}

	public String getLeaderEmpCode() {
		return leaderEmpCode;
	}

	public void setLeaderEmpCode(String leaderEmpCode) {
		this.leaderEmpCode = leaderEmpCode;
	}

	public String getLeaderEmpName() {
		return leaderEmpName;
	}

	public void setLeaderEmpName(String leaderEmpName) {
		this.leaderEmpName = leaderEmpName;
	}

	public String getEmpCnt() {
		return empCnt;
	}

	public void setEmpCnt(String empCnt) {
		this.empCnt = empCnt;
	}

	public String getPartitionD() {
		return partitionD;
	}

	public void setPartitionD(String partitionD) {
		this.partitionD = partitionD;
	}

	public Timestamp getDatachangeLasttime() {
		return datachangeLasttime;
	}

	public void setDatachangeLasttime(Timestamp datachangeLasttime) {
		this.datachangeLasttime = datachangeLasttime;
	}

}