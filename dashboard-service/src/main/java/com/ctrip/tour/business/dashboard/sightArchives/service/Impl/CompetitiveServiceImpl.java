package com.ctrip.tour.business.dashboard.sightArchives.service.Impl;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.sightArchives.dao.commonDao.AdmPrdTtdCpdTripDimInfoUnifiedOutputDao;
import com.ctrip.tour.business.dashboard.sightArchives.dao.commonDao.AdmPrdTtdCpdTripDimInfoUnifiedOutputSRDao;
import com.ctrip.tour.business.dashboard.sightArchives.dao.commonDao.AdmPrdTtdViewspotArchivesViewspotInfoDfDao;
import com.ctrip.tour.business.dashboard.sightArchives.dao.competitiveDao.AdmPrdTktTflyCmpInfoDfDao;
import com.ctrip.tour.business.dashboard.sightArchives.dao.competitiveDao.AdmPrdTktTklkCmpInfoDfDao;
import com.ctrip.tour.business.dashboard.sightArchives.dao.competitiveDao.AdmPrdTktTmtCmpInfoDfDao;
import com.ctrip.tour.business.dashboard.sightArchives.dao.flowDao.CdmLogTtdViewspotBenchTrafficDiDao;
import com.ctrip.tour.business.dashboard.sightArchives.dao.flowDao.CdmLogTtdViewspotBenchTrafficFromDiDao;
import com.ctrip.tour.business.dashboard.sightArchives.dao.qualityDao.AdmSevTtdViewspotDefectDetailDfDao;
import com.ctrip.tour.business.dashboard.sightArchives.dao.qualityDao.AdmSevTtdViewspotFileIndexDfDao;
import com.ctrip.tour.business.dashboard.sightArchives.dao.salesDao.CdmOrdTtdVstArchiveDfDao;
import com.ctrip.tour.business.dashboard.sightArchives.entity.commonEntity.AdmPrdTtdCpdTripDimInfoUnifiedOutput;
import com.ctrip.tour.business.dashboard.sightArchives.enums.competitive.CompetitiveItemEnumType;
import com.ctrip.tour.business.dashboard.sightArchives.enums.competitive.RankTypeEnumType;
import com.ctrip.tour.business.dashboard.sightArchives.enums.competitive.SightCoverEnumType;
import com.ctrip.tour.business.dashboard.sightArchives.proxy.GargleTranslateServiceProxy;
import com.ctrip.tour.business.dashboard.sightArchives.proxy.GlobalPoiJavaServiceProxy;
import com.ctrip.tour.business.dashboard.sightArchives.proxy.TtdProductBasicServiceProxy;
import com.ctrip.tour.business.dashboard.sightArchives.service.CommonService;
import com.ctrip.tour.business.dashboard.sightArchives.service.CompetitiveService;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardUpdatetimeDao;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.ScenicLanguageHelper;
import com.ctrip.tour.business.dashboard.utils.UserUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class CompetitiveServiceImpl implements CompetitiveService {
    //数仓侧设计文档: http://conf.ctripcorp.com/pages/viewpage.action?pageId=3661536861

    @Autowired
    private BusinessDashboardUpdatetimeDao businessDashboardUpdatetimeDao;
    @Autowired
    private AdmPrdTktTmtCmpInfoDfDao admPrdTktTmtCmpInfoDfDao;
    @Autowired
    private AdmPrdTktTflyCmpInfoDfDao admPrdTktTflyCmpInfoDfDao;
    @Autowired
    private AdmPrdTktTklkCmpInfoDfDao admPrdTktTklkCmpInfoDfDao;
    @Autowired
    private AdmPrdTtdCpdTripDimInfoUnifiedOutputSRDao admPrdTtdCpdTripDimInfoUnifiedOutputSRDao;
    @Autowired
    private AdmPrdTtdViewspotArchivesViewspotInfoDfDao admPrdTtdViewspotArchivesViewspotInfoDfDao;
    @Autowired
    private AdmSevTtdViewspotFileIndexDfDao admSevTtdViewspotFileIndexDfDao;
    @Autowired
    private CdmOrdTtdVstArchiveDfDao cdmOrdTtdVstArchiveDfDao;
    @Autowired
    private CdmLogTtdViewspotBenchTrafficFromDiDao cdmLogTtdViewspotBenchTrafficFromDiDao;
    @Autowired
    private CdmLogTtdViewspotBenchTrafficDiDao cdmLogTtdViewspotBenchTrafficDiDao;
    @Autowired
    private CommonService commonService;
    @Autowired
    private AdmSevTtdViewspotDefectDetailDfDao admSevTtdViewspotDefectDetailDfDao;
    @Autowired
    private RemoteConfig config;
    @Autowired
    private GlobalPoiJavaServiceProxy globalPoiJavaServiceProxy;
    @Autowired
    private TtdProductBasicServiceProxy ttdProductBasicServiceProxy;
    @Autowired
    private GargleTranslateServiceProxy gargleTranslateServiceProxy;

    @Override
    public GetSightComparisonResponseType getSightComparison(GetSightComparisonRequestType requestType) {

        SightArchivesCommonFilter commonFilter = requestType.getCommonFilter();
        Long sightId = commonFilter.getSightId();
        String startDate = commonFilter.getStartDate();
        String endDate = commonFilter.getEndDate();
        Integer businessType = commonFilter.getBusinessType();
        List<Long> vendorIdList = commonFilter.getVendorIdList();
        Integer dateType = commonFilter.getDateType();
        //1-同品类竞争圈、2-同地域竞争圈
        Integer competitorType = requestType.getCompetitorType();
        if(competitorType==null){
            return new GetSightComparisonResponseType();
        }

        String queryD = commonService.getQueryD();

        //竞争圈景点id
        List<Long> competitiveSightIdList = admPrdTtdViewspotArchivesViewspotInfoDfDao.queryCompetitiveSightIdList(sightId, competitorType,queryD);
        List<SightCompetitorRank> rankList = new ArrayList<>();
        for(RankTypeEnumType rankTypeEnum : RankTypeEnumType.ALL_RANK_TYPE_ENUM_LIST){
            SightCompetitorRank sightCompetitorRank = new SightCompetitorRank();

            sightCompetitorRank.setRankName(rankTypeEnum.getRankEnglishName());
            switch (rankTypeEnum) {
                case SEARCH_HEAT:
                    List<Map<String,Object>>  searchHeatList  =  cdmLogTtdViewspotBenchTrafficFromDiDao.querySightRankList(competitiveSightIdList, startDate, endDate,businessType,vendorIdList);
                    List<SightCompetitorRankItem> searchHeatItemlist = new ArrayList<>();
                    Boolean searchHeatflag = false;
                    for(Map<String,Object> map : searchHeatList){
                        SightCompetitorRankItem sightCompetitorRankItem = new SightCompetitorRankItem();
                        if(map.get("sub_viewspot_id").equals(sightId)){
                            searchHeatflag = true;
                        }
                        sightCompetitorRankItem.setSightId((Long) map.get("sub_viewspot_id"));
                        sightCompetitorRankItem.setSightName((String) map.get("sub_viewspot_name"));
                        SightCompetitorRankItemMetric sightCompetitorRankItemMetric = new SightCompetitorRankItemMetric();
                        sightCompetitorRankItemMetric.setMetricName("detailUv");//detailUv
                        sightCompetitorRankItemMetric.setMetricValue((Double) map.get("dtl_uv"));
                        List<SightCompetitorRankItemMetric> list = new ArrayList<>();
                        list.add(sightCompetitorRankItemMetric);
                        sightCompetitorRankItem.setMetricList(list);
                        searchHeatItemlist.add(sightCompetitorRankItem);
                    }
                    //如果没有覆盖景点，单独存储
                    if(!searchHeatflag){
                        List<Map<String, Object>> maps = cdmLogTtdViewspotBenchTrafficFromDiDao.querySightRankList(Arrays.asList(sightId), startDate, endDate, businessType, vendorIdList);
                        Map<String, Object> map;
                        if(maps.size() == 0){
                            map=new HashMap<>();
                        }else{
                            map = maps.get(0);
                        }
                        SightCompetitorRankItem sightCompetitorRankItem = new SightCompetitorRankItem();
                        sightCompetitorRankItem.setSightId((Long) map.get("sub_viewspot_id"));
                        sightCompetitorRankItem.setSightName((String) map.get("sub_viewspot_name"));
                        SightCompetitorRankItemMetric sightCompetitorRankItemMetric = new SightCompetitorRankItemMetric();
                        sightCompetitorRankItemMetric.setMetricName("detailUv");//
                        sightCompetitorRankItemMetric.setMetricValue(map.get("dtl_uv")==null?0.0:(Double) map.get("dtl_uv"));
                        List<SightCompetitorRankItemMetric> list = new ArrayList<>();
                        list.add(sightCompetitorRankItemMetric);
                        sightCompetitorRankItem.setMetricList(list);
                        searchHeatItemlist.add(sightCompetitorRankItem);
                    }
                    sightCompetitorRank.setRankItemList(searchHeatItemlist);
                    break;
                case USER_ATTENTION:
                    List<Map<String,Object>>  userAttentionlist  = cdmLogTtdViewspotBenchTrafficFromDiDao.queryUserAttention(competitiveSightIdList, startDate, endDate,businessType,vendorIdList);
                    List<SightCompetitorRankItem> userAttentionItemlist = new ArrayList<>();
                    Boolean userAttentionflag = false;
                    for(Map<String,Object> map : userAttentionlist){
                        SightCompetitorRankItem sightCompetitorRankItem = new SightCompetitorRankItem();
                        if(map.get("sub_viewspot_id").equals(sightId)){
                            userAttentionflag = true;
                        }
                        sightCompetitorRankItem.setSightId((Long) map.get("sub_viewspot_id"));
                        sightCompetitorRankItem.setSightName((String) map.get("sub_viewspot_name"));
                        SightCompetitorRankItemMetric sightCompetitorRankItemMetric = new SightCompetitorRankItemMetric();
                        sightCompetitorRankItemMetric.setMetricName("uv");
                        sightCompetitorRankItemMetric.setMetricValue((Double) map.get("dtl_uv"));
                        List<SightCompetitorRankItemMetric> list = new ArrayList<>();
                        list.add(sightCompetitorRankItemMetric);
                        sightCompetitorRankItem.setMetricList(list);
                        userAttentionItemlist.add(sightCompetitorRankItem);
                    }
                    //如果没有覆盖景点，单独存储
                    if(!userAttentionflag){
                        List<Map<String, Object>> maps = cdmLogTtdViewspotBenchTrafficFromDiDao.queryUserAttention(Arrays.asList(sightId), startDate, endDate, businessType, vendorIdList);
                        Map<String, Object> map;
                        if(maps.size() == 0){
                            map=new HashMap<>();
                        }else{
                            map = maps.get(0);
                        }
                        SightCompetitorRankItem sightCompetitorRankItem = new SightCompetitorRankItem();
                        sightCompetitorRankItem.setSightId((Long) map.get("sub_viewspot_id"));
                        sightCompetitorRankItem.setSightName((String) map.get("sub_viewspot_name"));
                        SightCompetitorRankItemMetric sightCompetitorRankItemMetric = new SightCompetitorRankItemMetric();
                        sightCompetitorRankItemMetric.setMetricName("uv");
                        sightCompetitorRankItemMetric.setMetricValue((Double) map.get("dtl_uv"));
                        List<SightCompetitorRankItemMetric> list = new ArrayList<>();
                        list.add(sightCompetitorRankItemMetric);
                        sightCompetitorRankItem.setMetricList(list);
                        userAttentionItemlist.add(sightCompetitorRankItem);
                    }
                    sightCompetitorRank.setRankItemList(userAttentionItemlist);
                    break;
                case USER_ATTRACTION:
                    List<Map<String,Object>>  userAttractionlist =cdmLogTtdViewspotBenchTrafficDiDao.queryUserAttraction(competitiveSightIdList, startDate, endDate,businessType,vendorIdList);
                    List<SightCompetitorRankItem> userAttractionItemlist = new ArrayList<>();
                    Boolean userAttractionflag = false;
                    for(Map<String,Object> map : userAttractionlist){
                        SightCompetitorRankItem sightCompetitorRankItem = new SightCompetitorRankItem();
                        if(map.get("sub_viewspot_id").equals(sightId)){
                            userAttractionflag = true;
                        }
                        sightCompetitorRankItem.setSightId((Long) map.get("sub_viewspot_id"));
                        sightCompetitorRankItem.setSightName((String) map.get("sub_viewspot_name"));
                        SightCompetitorRankItemMetric sightCompetitorRankItemMetric = new SightCompetitorRankItemMetric();
                        sightCompetitorRankItemMetric.setMetricName("conversionRate");
                        sightCompetitorRankItemMetric.setMetricValue((Double) map.get("userAttraction"));
                        List<SightCompetitorRankItemMetric> list = new ArrayList<>();
                        list.add(sightCompetitorRankItemMetric);
                        sightCompetitorRankItem.setMetricList(list);
                        userAttractionItemlist.add(sightCompetitorRankItem);
                    }
                    //如果没有覆盖景点，单独存储
                    if(!userAttractionflag){
                        List<Map<String, Object>> maps = cdmLogTtdViewspotBenchTrafficDiDao.queryUserAttraction(Arrays.asList(sightId), startDate, endDate, businessType, vendorIdList);
                        Map<String, Object> map;
                        if(maps.size() == 0){
                            map=new HashMap<>();
                        }else{
                            map = maps.get(0);
                        }
                        SightCompetitorRankItem sightCompetitorRankItem = new SightCompetitorRankItem();
                        sightCompetitorRankItem.setSightId((Long) map.get("sub_viewspot_id"));
                        sightCompetitorRankItem.setSightName((String) map.get("sub_viewspot_name"));
                        SightCompetitorRankItemMetric sightCompetitorRankItemMetric = new SightCompetitorRankItemMetric();
                        sightCompetitorRankItemMetric.setMetricName("conversionRate");
                        sightCompetitorRankItemMetric.setMetricValue((Double) map.get("userAttraction"));
                        List<SightCompetitorRankItemMetric> list = new ArrayList<>();
                        list.add(sightCompetitorRankItemMetric);
                        sightCompetitorRankItem.setMetricList(list);
                        userAttractionItemlist.add(sightCompetitorRankItem);
                    }
                    sightCompetitorRank.setRankItemList(userAttractionItemlist);
                    break;
                case SCENIC_SPOT_FLOW:  //票量增长率
                    List<Map<String,Object>> scenicSpotFlow  =  cdmOrdTtdVstArchiveDfDao.querySightRankListOfscenicSpotFlow(queryD,competitiveSightIdList, startDate, endDate,businessType,vendorIdList,dateType);
                    List<Long> sightidlist = new ArrayList<>();
                    for(Map<String,Object> map : scenicSpotFlow){
                       sightidlist.add((Long) map.get("vst_id"));
                    }
                    List<Map<String,Object>> scenicSpotFlowOfTenIds  =  cdmOrdTtdVstArchiveDfDao.querySightRankListOfscenicSpotFlowOfTenIds(queryD, sightidlist, startDate, endDate,businessType,vendorIdList,dateType);
                    calculateOrdTtdSucQtyAmtRate(scenicSpotFlow,scenicSpotFlowOfTenIds);
                    List<SightCompetitorRankItem> scenicSpotFlowItemlist = new ArrayList<>();
                    Boolean scenicSpotFlowflag = false;
                    for(Map<String,Object> map : scenicSpotFlow){
                        SightCompetitorRankItem sightCompetitorRankItem = new SightCompetitorRankItem();
                        if(map.get("vst_id").equals(sightId)){
                            scenicSpotFlowflag = true;
                        }
                        sightCompetitorRankItem.setSightId((Long) map.get("vst_id"));
                        sightCompetitorRankItem.setSightName((String) map.get("vst_name"));

                        SightCompetitorRankItemMetric sightCompetitorRankItemMetric = new SightCompetitorRankItemMetric();
                        sightCompetitorRankItemMetric.setMetricName("soldTicketCount");
                        sightCompetitorRankItemMetric.setMetricValue((Double) map.get("ord_ttd_suc_qty_amt"));
                        //票量增长率
                        SightCompetitorRankItemMetric sightCompetitorRankItemMetricOfRate = new SightCompetitorRankItemMetric();
                        sightCompetitorRankItemMetricOfRate.setMetricName("soldTicketCountGrowthRate");
                        sightCompetitorRankItemMetricOfRate.setMetricValue((Double) map.get("ord_ttd_suc_qty_amt_rate"));

                        List<SightCompetitorRankItemMetric> list = new ArrayList<>();
                        list.add(sightCompetitorRankItemMetric);
                        list.add(sightCompetitorRankItemMetricOfRate);

                        sightCompetitorRankItem.setMetricList(list);
                        scenicSpotFlowItemlist.add(sightCompetitorRankItem);
                    }
                    //如果没有覆盖景点，单独存储
                    if(!scenicSpotFlowflag){
                        List<Map<String, Object>> maps = cdmOrdTtdVstArchiveDfDao.querySightRankListOfscenicSpotFlow(queryD, Arrays.asList(sightId), startDate, endDate, businessType, vendorIdList,dateType);
                        List<Map<String,Object>> mapsOfLastYear  =  cdmOrdTtdVstArchiveDfDao.querySightRankListOfscenicSpotFlowOfTenIds(queryD, Arrays.asList(sightId), startDate, endDate,businessType,vendorIdList,dateType);
                        calculateOrdTtdSucQtyAmtRate(maps,mapsOfLastYear);
                        Map<String, Object> map;
                        if(maps.size() == 0){
                            map=new HashMap<>();
                        }else{
                            map = maps.get(0);
                        }
                        SightCompetitorRankItem sightCompetitorRankItem = new SightCompetitorRankItem();
                        sightCompetitorRankItem.setSightId((Long) map.get("vst_id"));
                        sightCompetitorRankItem.setSightName((String) map.get("vst_name"));
                        SightCompetitorRankItemMetric sightCompetitorRankItemMetric = new SightCompetitorRankItemMetric();
                        sightCompetitorRankItemMetric.setMetricName("soldTicketCount");
                        sightCompetitorRankItemMetric.setMetricValue((Double) map.get("ord_ttd_suc_qty_amt"));
                        //票量增长率
                        SightCompetitorRankItemMetric sightCompetitorRankItemMetricOfRate = new SightCompetitorRankItemMetric();
                        sightCompetitorRankItemMetricOfRate.setMetricName("soldTicketCountGrowthRate");
                        sightCompetitorRankItemMetricOfRate.setMetricValue((Double) map.get("ord_ttd_suc_qty_amt_rate"));

                        List<SightCompetitorRankItemMetric> list = new ArrayList<>();
                        list.add(sightCompetitorRankItemMetric);
                        list.add(sightCompetitorRankItemMetricOfRate);
                        sightCompetitorRankItem.setMetricList(list);
                        scenicSpotFlowItemlist.add(sightCompetitorRankItem);
                    }
                    sightCompetitorRank.setRankItemList(scenicSpotFlowItemlist);
                    break;
                case AVERAGE_ORDER_VALUE:
                    List<Map<String,Object>> averageOrderValuelist= cdmOrdTtdVstArchiveDfDao.querySightRankListOfaverageOrderValue(queryD, competitiveSightIdList, startDate, endDate,businessType,vendorIdList,dateType);
                    List<SightCompetitorRankItem> averageOrderValueItemlist = new ArrayList<>();
                    Boolean averageOrderValueflag = false;
                    for(Map<String,Object> map : averageOrderValuelist){
                        SightCompetitorRankItem sightCompetitorRankItem = new SightCompetitorRankItem();
                        if(map.get("vst_id").equals(sightId)){
                            averageOrderValueflag = true;
                        }
                        sightCompetitorRankItem.setSightId((Long) map.get("vst_id"));
                        sightCompetitorRankItem.setSightName((String) map.get("vst_name"));
                        SightCompetitorRankItemMetric sightCompetitorRankItemMetric = new SightCompetitorRankItemMetric();
                        sightCompetitorRankItemMetric.setMetricName("averageOrderValue");
                        sightCompetitorRankItemMetric.setMetricValue((Double) map.get("averageOrderValue"));
                        List<SightCompetitorRankItemMetric> list = new ArrayList<>();
                        list.add(sightCompetitorRankItemMetric);
                        sightCompetitorRankItem.setMetricList(list);
                        averageOrderValueItemlist.add(sightCompetitorRankItem);
                    }
                    //如果没有覆盖景点，单独存储
                    if(!averageOrderValueflag){
                        List<Map<String, Object>> maps = cdmOrdTtdVstArchiveDfDao.querySightRankListOfaverageOrderValue(queryD, Arrays.asList(sightId), startDate, endDate, businessType, vendorIdList,dateType);
                        Map<String, Object> map;
                        if(maps.size() == 0){
                            map=new HashMap<>();
                        }else{
                            map = maps.get(0);
                        }
                        SightCompetitorRankItem sightCompetitorRankItem = new SightCompetitorRankItem();
                        sightCompetitorRankItem.setSightId((Long) map.get("vst_id"));
                        sightCompetitorRankItem.setSightName((String) map.get("vst_name"));
                        SightCompetitorRankItemMetric sightCompetitorRankItemMetric = new SightCompetitorRankItemMetric();
                        sightCompetitorRankItemMetric.setMetricName("averageOrderValue");
                        sightCompetitorRankItemMetric.setMetricValue((Double) map.get("averageOrderValue"));
                        List<SightCompetitorRankItemMetric> list = new ArrayList<>();
                        list.add(sightCompetitorRankItemMetric);
                        sightCompetitorRankItem.setMetricList(list);
                        averageOrderValueItemlist.add(sightCompetitorRankItem);
                    }
                    sightCompetitorRank.setRankItemList(averageOrderValueItemlist);

                    break;
                case TOURIST_EVALUATION:
                    List<Map<String,Object>> touristevaluationlist= admSevTtdViewspotFileIndexDfDao.querySightRankListOftouristevaluation(queryD,competitiveSightIdList, startDate, endDate,businessType,vendorIdList);
                    List<SightCompetitorRankItem> sightCompetitorRankItemlist = new ArrayList<>();
                    Boolean flag = false;
                    for(Map<String,Object> map : touristevaluationlist){
                        SightCompetitorRankItem sightCompetitorRankItem = new SightCompetitorRankItem();
                        if(map.get("sub_viewspotid").equals(sightId)){
                            flag = true;
                        }
                        sightCompetitorRankItem.setSightId((Long) map.get("sub_viewspotid"));
                        sightCompetitorRankItem.setSightName((String) map.get("sub_viewspot_name"));
                        SightCompetitorRankItemMetric sightCompetitorRankItemMetric = new SightCompetitorRankItemMetric();
                        sightCompetitorRankItemMetric.setMetricName("commentScore");
                        sightCompetitorRankItemMetric.setMetricValue((Double) map.get("comment_score"));
                        List<SightCompetitorRankItemMetric> list = new ArrayList<>();
                        list.add(sightCompetitorRankItemMetric);
                        sightCompetitorRankItem.setMetricList(list);
                        sightCompetitorRankItemlist.add(sightCompetitorRankItem);
                    }
                    //如果没有覆盖景点，单独存储
                    if(!flag){
                        List<Map<String, Object>> maps = admSevTtdViewspotFileIndexDfDao.querySightRankListOftouristevaluation(queryD, Arrays.asList(sightId), startDate, endDate, businessType, vendorIdList);
                        Map<String, Object> map;
                        if(maps.size() == 0){
                            map=new HashMap<>();
                        }else{
                            map = maps.get(0);
                        }
                        SightCompetitorRankItem sightCompetitorRankItem = new SightCompetitorRankItem();
                        sightCompetitorRankItem.setSightId((Long) map.get("sub_viewspotid"));
                        sightCompetitorRankItem.setSightName((String) map.get("sub_viewspot_name"));
                        SightCompetitorRankItemMetric sightCompetitorRankItemMetric = new SightCompetitorRankItemMetric();
                        sightCompetitorRankItemMetric.setMetricName("commentScore");
                        sightCompetitorRankItemMetric.setMetricValue((Double) map.get("comment_score"));
                        List<SightCompetitorRankItemMetric> list = new ArrayList<>();
                        list.add(sightCompetitorRankItemMetric);
                        sightCompetitorRankItem.setMetricList(list);
                        sightCompetitorRankItemlist.add(sightCompetitorRankItem);
                    }
                    sightCompetitorRank.setRankItemList(sightCompetitorRankItemlist);
                    break;
                case PERFORMANCE_QUALITY:
                    List<Map<String,Object>> performanceQualitylist= admSevTtdViewspotDefectDetailDfDao.querySightRankListOfperformanceQuality(queryD, competitiveSightIdList, startDate, endDate,businessType,vendorIdList,dateType);
                    List<SightCompetitorRankItem> performanceQualityItemlist = new ArrayList<>();
                    Boolean performanceQualityflag = false;
                    for(Map<String,Object> map : performanceQualitylist){
                        SightCompetitorRankItem sightCompetitorRankItem = new SightCompetitorRankItem();
                        if(map.get("sub_viewspotid").equals(sightId)){
                            performanceQualityflag = true;
                        }
                        sightCompetitorRankItem.setSightId((Long) map.get("sub_viewspotid"));
                        sightCompetitorRankItem.setSightName((String) map.get("sub_viewspot_name"));
                        SightCompetitorRankItemMetric sightCompetitorRankItemMetric = new SightCompetitorRankItemMetric();
                        sightCompetitorRankItemMetric.setMetricName("weightedDefectRate");
                        sightCompetitorRankItemMetric.setMetricValue((Double) map.get("weightedDefectRate"));
                        List<SightCompetitorRankItemMetric> list = new ArrayList<>();
                        list.add(sightCompetitorRankItemMetric);
                        sightCompetitorRankItem.setMetricList(list);
                        performanceQualityItemlist.add(sightCompetitorRankItem);
                    }
                    //如果没有覆盖景点，单独存储
                    if(!performanceQualityflag){
                        List<Map<String, Object>> maps = admSevTtdViewspotDefectDetailDfDao.querySightRankListOfperformanceQuality(queryD, Arrays.asList(sightId), startDate, endDate, businessType, vendorIdList,dateType);
                        Map<String, Object> map;
                        if(maps.size() == 0){
                            map=new HashMap<>();
                        }else{
                            map = maps.get(0);
                        }
                        SightCompetitorRankItem sightCompetitorRankItem = new SightCompetitorRankItem();
                        sightCompetitorRankItem.setSightId((Long) map.get("sub_viewspotid"));
                        sightCompetitorRankItem.setSightName((String) map.get("sub_viewspot_name"));
                        SightCompetitorRankItemMetric sightCompetitorRankItemMetric = new SightCompetitorRankItemMetric();
                        sightCompetitorRankItemMetric.setMetricName("weightedDefectRate");
                        sightCompetitorRankItemMetric.setMetricValue((Double) map.get("weightedDefectRate"));
                        List<SightCompetitorRankItemMetric> list = new ArrayList<>();
                        list.add(sightCompetitorRankItemMetric);
                        sightCompetitorRankItem.setMetricList(list);
                        performanceQualityItemlist.add(sightCompetitorRankItem);
                    }
                    sightCompetitorRank.setRankItemList(performanceQualityItemlist);
                    break;
                case REFUND_RATE:
                    List<Map<String,Object>> refundRatelist= cdmOrdTtdVstArchiveDfDao.querySightRankListOfrefundRate(queryD,competitiveSightIdList, startDate, endDate,businessType,vendorIdList,dateType);
                    List<SightCompetitorRankItem> refundRateItemlist = new ArrayList<>();
                    Boolean refundRateflag = false;
                    for(Map<String,Object> map : refundRatelist){
                        SightCompetitorRankItem sightCompetitorRankItem = new SightCompetitorRankItem();
                        if(map.get("vst_id").equals(sightId)){
                            refundRateflag = true;
                        }
                        sightCompetitorRankItem.setSightId((Long) map.get("vst_id"));
                        sightCompetitorRankItem.setSightName((String) map.get("vst_name"));
                        SightCompetitorRankItemMetric sightCompetitorRankItemMetric = new SightCompetitorRankItemMetric();
                        sightCompetitorRankItemMetric.setMetricName("fullyRefundedOrderRate");
                        sightCompetitorRankItemMetric.setMetricValue((Double) map.get("fullyRefundedOrderRate"));
                        List<SightCompetitorRankItemMetric> list = new ArrayList<>();
                        list.add(sightCompetitorRankItemMetric);
                        sightCompetitorRankItem.setMetricList(list);
                        refundRateItemlist.add(sightCompetitorRankItem);
                    }
                    //如果没有覆盖景点，单独存储
                    if(!refundRateflag){
                        List<Map<String, Object>> maps = cdmOrdTtdVstArchiveDfDao.querySightRankListOfrefundRate(queryD,Arrays.asList(sightId), startDate, endDate, businessType, vendorIdList,dateType);
                        Map<String, Object> map;
                        if(maps.size() == 0){
                            map=new HashMap<>();
                        }else{
                            map = maps.get(0);
                        }
                        SightCompetitorRankItem sightCompetitorRankItem = new SightCompetitorRankItem();
                        sightCompetitorRankItem.setSightId((Long) map.get("vst_id"));
                        sightCompetitorRankItem.setSightName((String) map.get("vst_name"));
                        SightCompetitorRankItemMetric sightCompetitorRankItemMetric = new SightCompetitorRankItemMetric();
                        sightCompetitorRankItemMetric.setMetricName("fullyRefundedOrderRate");
                        sightCompetitorRankItemMetric.setMetricValue((Double) map.get("fullyRefundedOrderRate"));
                        List<SightCompetitorRankItemMetric> list = new ArrayList<>();
                        list.add(sightCompetitorRankItemMetric);
                        sightCompetitorRankItem.setMetricList(list);
                        refundRateItemlist.add(sightCompetitorRankItem);
                    }
                    sightCompetitorRank.setRankItemList(refundRateItemlist);
                    break;
                case COMPLAINT_RATE:
                    List<Map<String,Object>> complaintRatelist= admSevTtdViewspotFileIndexDfDao.querySightRankListOfcomplaintRate(queryD,competitiveSightIdList, startDate, endDate,businessType,vendorIdList);
                    List<SightCompetitorRankItem> complaintRateItemlist = new ArrayList<>();
                    Boolean complaintRateflag = false;
                    for(Map<String,Object> map : complaintRatelist){
                        SightCompetitorRankItem sightCompetitorRankItem = new SightCompetitorRankItem();
                        if(map.get("sub_viewspotid").equals(sightId)){
                            complaintRateflag = true;
                        }
                        sightCompetitorRankItem.setSightId((Long) map.get("sub_viewspotid"));
                        sightCompetitorRankItem.setSightName((String) map.get("sub_viewspot_name"));
                        SightCompetitorRankItemMetric sightCompetitorRankItemMetric = new SightCompetitorRankItemMetric();
                        sightCompetitorRankItemMetric.setMetricName("complaintRate");
                        sightCompetitorRankItemMetric.setMetricValue((Double) map.getOrDefault("complaintRate",0.0));
                        List<SightCompetitorRankItemMetric> list = new ArrayList<>();
                        list.add(sightCompetitorRankItemMetric);
                        sightCompetitorRankItem.setMetricList(list);
                        complaintRateItemlist.add(sightCompetitorRankItem);
                    }
                    //如果没有覆盖景点，单独存储
                    if(!complaintRateflag){
                        List<Map<String, Object>> maps = admSevTtdViewspotFileIndexDfDao.querySightRankListOfcomplaintRate(queryD,Arrays.asList(sightId), startDate, endDate, businessType, vendorIdList);
                        Map<String, Object> map;
                        if(maps.size() == 0){
                            map=new HashMap<>();
                        }else{
                            map = maps.get(0);
                        }
                        SightCompetitorRankItem sightCompetitorRankItem = new SightCompetitorRankItem();
                        sightCompetitorRankItem.setSightId((Long) map.get("sub_viewspotid"));
                        sightCompetitorRankItem.setSightName((String) map.get("sub_viewspot_name"));
                        SightCompetitorRankItemMetric sightCompetitorRankItemMetric = new SightCompetitorRankItemMetric();
                        sightCompetitorRankItemMetric.setMetricName("complaintRate");
                        sightCompetitorRankItemMetric.setMetricValue((Double) map.getOrDefault("complaintRate",0.0));
                        List<SightCompetitorRankItemMetric> list = new ArrayList<>();
                        list.add(sightCompetitorRankItemMetric);
                        sightCompetitorRankItem.setMetricList(list);
                        complaintRateItemlist.add(sightCompetitorRankItem);
                    }
                    sightCompetitorRank.setRankItemList(complaintRateItemlist);
                    break;
                default:
                    break;
            }
            rankList.add(sightCompetitorRank);
        }
        if ("T".equals(config.getConfigValue("languageSwitch")) && "en-US".equals(UserUtil.getVbkLocale())){
            List<Long> sightIdList = new ArrayList<>();
            for(SightCompetitorRank sightCompetitorRank : rankList){
                for (SightCompetitorRankItem sightCompetitorRankItem : sightCompetitorRank.getRankItemList()) {
                    sightIdList.add(sightCompetitorRankItem.getSightId());
                }
            }

            //景点名称翻译
            Map<Long,String> sightNameTranslateResultMap = globalPoiJavaServiceProxy.getSightNameTranslateResult(sightIdList);

            for(SightCompetitorRank sightCompetitorRank : rankList){
                for (SightCompetitorRankItem sightCompetitorRankItem : sightCompetitorRank.getRankItemList()) {
                    if(sightNameTranslateResultMap.containsKey(sightCompetitorRankItem.getSightId())
                            && sightNameTranslateResultMap.get(sightCompetitorRankItem.getSightId())!=null){
                        sightCompetitorRankItem.setSightName(sightNameTranslateResultMap.get(sightCompetitorRankItem.getSightId()));
                    }
                }
            }

        }
        GetSightComparisonResponseType responseType = new GetSightComparisonResponseType();
        responseType.setRankList(rankList);
        return responseType;
    }

    //景点VS竞争圈
    @Override
    public GetSightCompetitiveResponseType getSightCompetitive(GetSightCompetitiveRequestType requestType) {
        SightArchivesCommonFilter commonFilter = requestType.getCommonFilter();
        Long sightId = commonFilter.getSightId();
        String startDate = commonFilter.getStartDate();
        String endDate = commonFilter.getEndDate();

        String queryD = null;
        try {
            queryD = businessDashboardUpdatetimeDao.getSightArchivesUpdateTime();
        } catch (SQLException e) {
            return new GetSightCompetitiveResponseType();
        }

        //1-同品类竞争圈、2-同地域竞争圈
        Integer competitorType = requestType.getCompetitorType();
        if(competitorType==null){
            return new GetSightCompetitiveResponseType();
        }

        AdmPrdTtdCpdTripDimInfoUnifiedOutput sightInfo = admPrdTtdCpdTripDimInfoUnifiedOutputSRDao.querySightInfo(sightId,queryD);
        if(sightInfo==null){
            return new GetSightCompetitiveResponseType();
        }

        //自身覆盖情况
        Map<String, Object> selfResultMap = admPrdTtdViewspotArchivesViewspotInfoDfDao.querySightCompetitiveInfo(sightId,queryD,startDate,endDate);

        //竞争圈景点id
        List<Long> competitiveSightIdList = admPrdTtdViewspotArchivesViewspotInfoDfDao.queryCompetitiveSightIdList(sightId, competitorType,queryD);
        Map<String, Object> competitiveResultMap = new HashMap<>();
        if(competitiveSightIdList.size() > 0){
            //竞争圈中覆盖景点数
            competitiveResultMap = admPrdTtdViewspotArchivesViewspotInfoDfDao.querySightCoverCount(competitiveSightIdList,startDate,endDate);
        }

        List<SightCompetitiveItem> coveredUserGroupList = new ArrayList<>();
        for(SightCoverEnumType sightCoverEnumType : SightCoverEnumType.userGroupCoverEnumList){
            SightCompetitiveItem sightCompetitiveItem = new SightCompetitiveItem();
            sightCompetitiveItem.setName(sightCoverEnumType.getChineseName());
            if (selfResultMap.get(sightCoverEnumType.getEnglishName())!=null){
                sightCompetitiveItem.setSelfPerformance(((Long) selfResultMap.get(sightCoverEnumType.getEnglishName())>0));  //自身是否覆盖
            }
            sightCompetitiveItem.setCoveredSightCount(Math.toIntExact((Long) competitiveResultMap.getOrDefault(sightCoverEnumType.getEnglishName(),0L)));   //竞争圈内覆盖景点数
            sightCompetitiveItem.setCompetitiveSightCount(competitiveSightIdList.size());
            sightCompetitiveItem.setCoveredSightRate( sightCompetitiveItem.getCompetitiveSightCount()==0? null:(double)sightCompetitiveItem.getCoveredSightCount()/sightCompetitiveItem.getCompetitiveSightCount());
            coveredUserGroupList.add(sightCompetitiveItem);
        }

        List<SightCompetitiveItem> competiveItemList = new ArrayList<>();
        for (SightCoverEnumType sightCoverEnumType : SightCoverEnumType.competiveCoverEnumList) {
            SightCompetitiveItem sightCompetitiveItem = new SightCompetitiveItem();
            sightCompetitiveItem.setName(sightCoverEnumType.getChineseName());
            if (selfResultMap.get(sightCoverEnumType.getEnglishName())!=null) {
                sightCompetitiveItem.setSelfPerformance(((Long) selfResultMap.get(sightCoverEnumType.getEnglishName()) > 0));  //自身是否覆盖
            }
            sightCompetitiveItem.setCoveredSightCount(Math.toIntExact((Long) competitiveResultMap.getOrDefault(sightCoverEnumType.getEnglishName(),0L)));   //竞争圈内覆盖景点数
            sightCompetitiveItem.setCompetitiveSightCount(competitiveSightIdList.size());
            sightCompetitiveItem.setCoveredSightRate(sightCompetitiveItem.getCompetitiveSightCount()==0?null:(double)sightCompetitiveItem.getCoveredSightCount()/sightCompetitiveItem.getCompetitiveSightCount());
            competiveItemList.add(sightCompetitiveItem);
        }


        GetSightCompetitiveResponseType responseType = new GetSightCompetitiveResponseType();
        responseType.setCompetiveItemList(competiveItemList);
        responseType.setCoveredUserGroupList(coveredUserGroupList);

        if ("T".equals(config.getConfigValue("languageSwitch"))) {
            setMultiLanguage(responseType);
        }
        return responseType;
    }

    private void setMultiLanguage(GetSightCompetitiveResponseType responseType) {
        List<SightCompetitiveItem> sightCompetitiveItemList = responseType.getCoveredUserGroupList();
        if (CollectionUtils.isNotEmpty(sightCompetitiveItemList)) {
            for (SightCompetitiveItem sightCompetitiveItem : sightCompetitiveItemList) {
                if (StringUtils.isNotBlank(sightCompetitiveItem.getName())) {
                    sightCompetitiveItem.setName(ScenicLanguageHelper.getMultiLanguage(sightCompetitiveItem.getName(), UserUtil.getVbkLocaleForScenic()));
                }
            }
        }

        List<SightCompetitiveItem> sightCompetitiveItemList1 = responseType.getCompetiveItemList();
        if (CollectionUtils.isNotEmpty(sightCompetitiveItemList1)) {
            for (SightCompetitiveItem sightCompetitiveItem : sightCompetitiveItemList1) {
                if (StringUtils.isNotBlank(sightCompetitiveItem.getName())) {
                    sightCompetitiveItem.setName(ScenicLanguageHelper.getMultiLanguage(sightCompetitiveItem.getName(), UserUtil.getVbkLocaleForScenic()));
                }
            }
        }

    }


    //携程VS竞对 未覆盖票种
    @Override
    public GetUncoveredTicketTypeResponseType getUncoveredTicketType(GetUncoveredTicketTypeRequestType requestType) {

        SightArchivesCommonFilter commonFilter = requestType.getCommonFilter();
        Long sightId = commonFilter.getSightId();
        String startDate = commonFilter.getStartDate();
        String endDate = commonFilter.getEndDate();
        Integer pageNo = requestType.getPageNo();
        Integer pageSize = requestType.getPageSize();


        //竞对: 1-美团、2-飞猪、3-客路
        Integer competitor = requestType.getCompetitor();
        if(competitor==null){
            return new GetUncoveredTicketTypeResponseType();
        }

        List<UnCoveredTicketType> unCoveredTicketTypeList = new ArrayList<>();
        Integer totalNum = 0;
        switch (competitor) {
            case 1:
                unCoveredTicketTypeList = admPrdTktTmtCmpInfoDfDao.queryUncoveredTicketType(sightId, startDate, endDate, pageNo, pageSize);
                totalNum = admPrdTktTmtCmpInfoDfDao.queryUncoveredTicketTypeTotalNum(sightId, startDate, endDate);
                break;
            case 2:
                unCoveredTicketTypeList = admPrdTktTflyCmpInfoDfDao.queryUncoveredTicketType(sightId, startDate, endDate, pageNo, pageSize);
                totalNum = admPrdTktTflyCmpInfoDfDao.queryUncoveredTicketTypeTotalNum(sightId, startDate, endDate);
                break;
            case 3:
                unCoveredTicketTypeList = admPrdTktTklkCmpInfoDfDao.queryUncoveredTicketType(sightId, startDate, endDate, pageNo, pageSize);
                totalNum = admPrdTktTklkCmpInfoDfDao.queryUncoveredTicketTypeTotalNum(sightId, startDate, endDate);
                break;
        }
        //翻译竞对的二级票种名称
        if ("T".equals(config.getConfigValue("languageSwitch"))
                && "en-US".equals(UserUtil.getVbkLocale())){
            List<String> ticketTypeNameList = new ArrayList<>();
            for(UnCoveredTicketType unCoveredTicketType : unCoveredTicketTypeList){
                ticketTypeNameList.add(unCoveredTicketType.getTicketTypeName());
            }
            if(CollectionUtils.isNotEmpty(ticketTypeNameList)) {
                Map<String,String> translateResultMap = gargleTranslateServiceProxy.googleBatchTranslate(ticketTypeNameList);
                for (UnCoveredTicketType unCoveredTicketType : unCoveredTicketTypeList) {
                    if (translateResultMap.containsKey(unCoveredTicketType.getTicketTypeName())
                            && translateResultMap.get(unCoveredTicketType.getTicketTypeName()) != null) {
                        unCoveredTicketType.setTicketTypeName(translateResultMap.get(unCoveredTicketType.getTicketTypeName()));
                    }
                }
            }
        }


        GetUncoveredTicketTypeResponseType responseType = new GetUncoveredTicketTypeResponseType();
        responseType.setUnCoveredTicketTypeList(unCoveredTicketTypeList);
        responseType.setTotalNum(totalNum);
        return responseType;
    }


    @Override
    public GetCoreTicketTypeCompetitiveResponseType getCoreTicketTypeCompetitive(GetCoreTicketTypeCompetitiveRequestType requestType) {
        SightArchivesCommonFilter commonFilter = requestType.getCommonFilter();
        Long sightId = commonFilter.getSightId();
//        //竞对: 1-美团、2-飞猪、3-客路
//        Integer competitor = requestType.getCompetitor();
        String startDate = commonFilter.getStartDate();
        String endDate = commonFilter.getEndDate();
        Integer pageNo = requestType.getPageNo();
        Integer pageSize = requestType.getPageSize();

        List<CoreTicketTypeCompetitive> coreTicketTypeCompetitiveList = new ArrayList<>();
        Integer totalNum = 0;
//        if(competitor==null){
//            return new GetCoreTicketTypeCompetitiveResponseType();
//        }

        String queryD = null;
        try {
            queryD = businessDashboardUpdatetimeDao.getSightArchivesUpdateTime();
        } catch (SQLException e) {
            return new GetCoreTicketTypeCompetitiveResponseType();
        }
        AdmPrdTtdCpdTripDimInfoUnifiedOutput sightInfo = admPrdTtdCpdTripDimInfoUnifiedOutputSRDao.querySightInfo(sightId, queryD);
        if(sightInfo==null || sightInfo.getIsDomestic()==null){
            return new GetCoreTicketTypeCompetitiveResponseType();
        }
        if(sightInfo.getIsDomestic()==1){
            coreTicketTypeCompetitiveList = admPrdTktTmtCmpInfoDfDao.queryCoreTicketType(sightId, startDate, endDate, pageNo, pageSize);
            totalNum = admPrdTktTmtCmpInfoDfDao.queryCoreTicketTypeTotalNum(sightId, startDate, endDate);
        }else {
            coreTicketTypeCompetitiveList = admPrdTktTflyCmpInfoDfDao.queryCoreTicketType(sightId, startDate, endDate, pageNo, pageSize);
            totalNum = admPrdTktTflyCmpInfoDfDao.queryCoreTicketTypeTotalNum(sightId, startDate, endDate);
//            List<Map<String, Object>> klResult = admPrdTktTklkCmpInfoDfDao.queryCoreTicketType(sightId, startDate, endDate,coreTicketTypeCompetitiveList);
//
//            for(CoreTicketTypeCompetitive coreTicketTypeCompetitive : coreTicketTypeCompetitiveList){
//                for(Map<String, Object> map : klResult){
//                    if(map.getOrDefault("trip_lv2_saleunit_id","0").equals(coreTicketTypeCompetitive.getTicketTypeId())){
//                        try {
//                            coreTicketTypeCompetitive.setDisadvantageDaysToKL(Math.toIntExact((Long) map.getOrDefault("comm_inferior_count", "0")));
//                        }catch (Exception e){
//                            coreTicketTypeCompetitive.setDisadvantageDaysToKL(null);
//                        }
//                        coreTicketTypeCompetitive.setTotalDaysToKL(Math.toIntExact((Long) map.getOrDefault("days", "0")));
//                    }
//                }
//            }
        }
//        Collections.sort(coreTicketTypeCompetitiveList, new Comparator<CoreTicketTypeCompetitive>() {
//            @Override
//            public int compare(CoreTicketTypeCompetitive o1, CoreTicketTypeCompetitive o2) {
//                return o1.getTicketTypeId().compareTo(o2.getTicketTypeId());
//            }
//        });
//        if(pageNo==null || pageSize==null || pageNo<=0 || pageSize<=0){
//            pageNo = 1;
//            pageSize = 10;
//        }
//        //分页
//        int startIndex = (pageNo - 1) * pageSize;
//        int endIndex = Math.min(startIndex + pageSize, coreTicketTypeCompetitiveList.size());
//        if (startIndex < coreTicketTypeCompetitiveList.size()) {
//            coreTicketTypeCompetitiveList = coreTicketTypeCompetitiveList.subList(startIndex, endIndex);
//        } else {
//            coreTicketTypeCompetitiveList = new ArrayList<>();
//        }

        //翻译二级票种名称
        if ("T".equals(config.getConfigValue("languageSwitch"))
                && "en-US".equals(UserUtil.getVbkLocale())){
            List<Long> ticketTypeIdList = new ArrayList<>();
            for (CoreTicketTypeCompetitive coreTicketTypeCompetitive : coreTicketTypeCompetitiveList) {
                ticketTypeIdList.add(coreTicketTypeCompetitive.getTicketTypeId());
            }
            //todo 票种翻译相关
            Map<Long, String> translateResultMap = ttdProductBasicServiceProxy.getSaleUnitInfo(ticketTypeIdList);
            for (CoreTicketTypeCompetitive coreTicketTypeCompetitive : coreTicketTypeCompetitiveList) {
                if (translateResultMap.containsKey(coreTicketTypeCompetitive.getTicketTypeId())) {
                    coreTicketTypeCompetitive.setTicketTypeName(translateResultMap.get(coreTicketTypeCompetitive.getTicketTypeId()));
                }
            }
        }

        GetCoreTicketTypeCompetitiveResponseType responseType = new GetCoreTicketTypeCompetitiveResponseType();
        responseType.setCoreTicketTypeCompetitiveList(coreTicketTypeCompetitiveList);
        responseType.setTotalNum(totalNum);
        return responseType;

    }

    @Override
    public GetCoreTicketTypeCompetitiveDetailResponseType getCoreTicketTypeCompetitiveDetail(GetCoreTicketTypeCompetitiveDetailRequestType requestType) {
        
        SightArchivesCommonFilter commonFilter = requestType.getCommonFilter();
        Long sightId = commonFilter.getSightId();
        String startDate = commonFilter.getStartDate();
        String endDate = commonFilter.getEndDate();
        Long ticketTypeId = requestType.getTicketTypeId();

        String queryD = null;
        try {
            queryD = businessDashboardUpdatetimeDao.getSightArchivesUpdateTime();
        } catch (SQLException e) {
            return new GetCoreTicketTypeCompetitiveDetailResponseType();
        }

        AdmPrdTtdCpdTripDimInfoUnifiedOutput sightInfo = admPrdTtdCpdTripDimInfoUnifiedOutputSRDao.querySightInfo(sightId,queryD);
        if(sightInfo==null){
            return new GetCoreTicketTypeCompetitiveDetailResponseType();
        }
        boolean domestic = sightInfo.getIsDomestic()==null || sightInfo.getIsDomestic()==1;

        GetCoreTicketTypeCompetitiveDetailResponseType responseType = new GetCoreTicketTypeCompetitiveDetailResponseType();
        List<CoreTicketTypeCompetitiveDetail> coreTicketTypeCompetitiveDetailList = new ArrayList<>();
        responseType.setCoreTicketTypeCompetitiveDetailList(coreTicketTypeCompetitiveDetailList);
        String language = UserUtil.getVbkLocaleForScenic();
       if(domestic) {
           Map<String, Object> resultMap = admPrdTktTmtCmpInfoDfDao.queryCoreTicketTypeDetail(sightId, startDate, endDate,ticketTypeId);

           for(CompetitiveItemEnumType competitiveItemEnumType : CompetitiveItemEnumType.MeiTuanCompetitiveItemList){
               CoreTicketTypeCompetitiveDetail coreTicketTypeCompetitiveDetail = new CoreTicketTypeCompetitiveDetail();
               coreTicketTypeCompetitiveDetail.setCompetitiveItemName("en-US".equals(language) ? competitiveItemEnumType.getEnglishName() : competitiveItemEnumType.getChineseName());
               coreTicketTypeCompetitiveDetail.setDisadvantageDaysToMT((Integer) resultMap.getOrDefault(competitiveItemEnumType.getQueryResultName(),0));
               coreTicketTypeCompetitiveDetail.setTotalDaysToMT((Integer) resultMap.getOrDefault("days",0));
               coreTicketTypeCompetitiveDetailList.add(coreTicketTypeCompetitiveDetail);
           }

           responseType.setXcPrice((Double) resultMap.get("trip_sale_price"));
           responseType.setMtPrice((Double) resultMap.get("rival_sale_price"));
       }else {
           Map<String, Object> fzResultMap = admPrdTktTflyCmpInfoDfDao.queryCoreTicketTypeDetail(sightId, startDate, endDate, ticketTypeId);
           Map<String, Object> klResultMap = admPrdTktTklkCmpInfoDfDao.queryCoreTicketTypeDetail(sightId, startDate, endDate, ticketTypeId);
           for (CompetitiveItemEnumType competitiveItemEnumType : CompetitiveItemEnumType.FeiZhuCompetitiveItemList) {
               CoreTicketTypeCompetitiveDetail coreTicketTypeCompetitiveDetail = new CoreTicketTypeCompetitiveDetail();
               coreTicketTypeCompetitiveDetail.setCompetitiveItemName("en-US".equals(language) ? competitiveItemEnumType.getEnglishName() : competitiveItemEnumType.getChineseName());
               coreTicketTypeCompetitiveDetail.setDisadvantageDaysToFZ((Integer) fzResultMap.get(competitiveItemEnumType.getQueryResultName()));
               coreTicketTypeCompetitiveDetail.setTotalDaysToFZ((Integer) fzResultMap.get("days"));

               coreTicketTypeCompetitiveDetail.setDisadvantageDaysToKL((Integer) klResultMap.get(competitiveItemEnumType.getQueryResultName()));
               coreTicketTypeCompetitiveDetail.setTotalDaysToKL((Integer) klResultMap.get("days"));
               coreTicketTypeCompetitiveDetailList.add(coreTicketTypeCompetitiveDetail);
           }
           responseType.setXcPrice((Double) fzResultMap.get("trip_sale_price"));
           responseType.setFzPrice((Double) fzResultMap.get("rival_sale_price"));
           responseType.setKlPrice((Double) klResultMap.get("rival_sale_price"));

        }

        return responseType;

    }

    public static void calculateOrdTtdSucQtyAmtRate(List<Map<String, Object>> scenicSpotFlow, List<Map<String, Object>> scenicSpotFlowOflastyear) {
        for (Map<String, Object> currentYearMap : scenicSpotFlow) {
            String currentVstId = currentYearMap.get("vst_id").toString();
            String currentVstName = currentYearMap.get("vst_name").toString();
            Double currentOrdTtdSucQtyAmt = Double.parseDouble(currentYearMap.get("ord_ttd_suc_qty_amt").toString());

            for (Map<String, Object> lastYearMap : scenicSpotFlowOflastyear) {
                String lastYearVstId = lastYearMap.get("vst_id").toString();
                String lastYearVstName = lastYearMap.get("vst_name").toString();

                if (currentVstId.equals(lastYearVstId) && currentVstName.equals(lastYearVstName)) {
                    Double lastYearOrdTtdSucQtyAmt = Double.parseDouble(lastYearMap.get("ord_ttd_suc_qty_amt").toString());

                    // 计算同比增长率
                    if (lastYearOrdTtdSucQtyAmt != 0) {
                        double rate = (currentOrdTtdSucQtyAmt - lastYearOrdTtdSucQtyAmt) / lastYearOrdTtdSucQtyAmt;
                        currentYearMap.put("ord_ttd_suc_qty_amt_rate", rate);
                    } else {
                        currentYearMap.put("ord_ttd_suc_qty_amt_rate", 0.0);
                    }
                    break; // 找到匹配项后可以跳出内层循环
                }
            }
        }
    }

}
