package com.ctrip.tour.business.dashboard.tktBusiness.biz.impl;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.sightArchives.dao.overseametric.DimOrdTtdTargetConfigDao;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBeanV2;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoWithMetricBean;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.DataUpdateBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.UserPermissionBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.bo.ExamineConfigBo;
import com.ctrip.tour.business.dashboard.tktBusiness.bo.OverSeaExamineConfigBO;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardEmployeeInfoDao;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardExamineeConfigV2Dao;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardOrganizationInfoDao;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardOverseaExamineeConfigDao;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardEmployeeInfo;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardExamineeConfigV2;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardOrganizationInfo;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardOverseaExamineeConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.TaskFlowHelper;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.ctrip.tour.business.dashboard.utils.UserUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tour.auth.soa.model.SessionUserInfo;
import tour.auth.soa.session.SessionContext;

import java.sql.SQLException;
import java.text.ParseException;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/7/25
 */
@Service
@Slf4j
public class UserPermissionBizImpl implements UserPermissionBiz {

    @Autowired
    private BusinessDashboardExamineeConfigV2Dao examineConfigV2Dao;

    @Autowired
    private BusinessDashboardOverseaExamineeConfigDao overseaConfigDao;

    @Autowired
    private BusinessDashboardEmployeeInfoDao employeeInfoDao;

    @Autowired
    private BusinessDashboardOrganizationInfoDao organizationInfoDao;

    @Autowired
    private RemoteConfig remoteConfig;

    @Autowired
    private DataUpdateBiz dataUpdateBiz;

    @Autowired
    private DimOrdTtdTargetConfigDao dimOrdTtdTargetConfigDao;


    @Override
    public CheckAdminPermissionResponseType checkAdminPermission(CheckAdminPermissionRequestType checkAdminPermissionRequestType) throws Exception {

        CheckAdminPermissionResponseType response = new CheckAdminPermissionResponseType();

        String empCode = UserUtil.getMappingEmpCode(remoteConfig);
        String admin = remoteConfig.getExternalConfig("admin");

        response.setHaveAdminPermission(admin.equals(empCode));

        return response;
    }

    @Override
    public CheckTaskFlowPermissionResponseType checkTaskFlowPermission(CheckTaskFlowPermissionRequestType checkTaskFlowPermissionRequestType,
                                                                       String empCode) throws Exception {

        CheckTaskFlowPermissionResponseType response = new CheckTaskFlowPermissionResponseType();

        BusinessDashboardEmployeeInfo employeeInfo = employeeInfoDao.queryByEmpCode(empCode);

        //如果不在全量人员表里
        if (GeneralUtil.isEmpty(employeeInfo)) {
            response.setHavePermission(false);
            response.setStatus("other");
            return response;
        }
        String domainName = employeeInfo.getDomainName();


        CheckUserPermissionRequestType checkUserPermissionRequestType = new CheckUserPermissionRequestType();
        checkUserPermissionRequestType.setDomainName(domainName);

        //如果是非门票活动业务部的人
        if (!isInDepartment41999(checkUserPermissionRequestType)) {
            response.setHavePermission(false);
            response.setStatus("other");
            return response;
        }


        String deptType = getDeptType(domainName);

        if ("otherEmployee".equals(deptType)) {
            response.setHavePermission(false);
            response.setStatus("other");
            return response;
        }

//        if ("overseaEmployee".equals(deptType)) {   //11期项目添加了海外数据
//            response.setHavePermission(false);
//            response.setStatus("oversea");
//            return response;
//        }


        String d = dataUpdateBiz.getUpdateTime();

        response.setHavePermission(true);
        setTaskStatisticalScope(response, deptType);
        setSubordinateTypeList(response, d, domainName);
        return response;

    }

    private void setTaskStatisticalScope(CheckTaskFlowPermissionResponseType response, String deptType) {
        List<TaskStatisticalScope> statisticalScopeList = new ArrayList<>();
        response.setStatisticalScopeList(statisticalScopeList);

        statisticalScopeList.add(TaskFlowHelper.getTaskStatisticalScopeMap().get(deptType));

    }


    private void setSubordinateTypeList(CheckTaskFlowPermissionResponseType response,
                                        String d,
                                        String domainName) throws SQLException, ParseException {

        List<String> subordinateTypeList = new ArrayList<>();
        response.setSubordinateTypeList(subordinateTypeList);
        //华西大区运营权限
        String specailRegionOperate = remoteConfig.getExternalConfig("specailRegionOperate");
        if (specailRegionOperate.equals(domainName)) {
            setSpecailRegionOperateSubordinateTypeList(subordinateTypeList);
            return;
        }


        String year = DateUtil.getActualYearOfD(d);
        String quarter = DateUtil.getActualQuarterOfD(d);

        //获取国内最新配置(可能是取不到的)
        List<BusinessDashboardExamineeConfigV2> examineeConfigV2List = examineConfigV2Dao.querySpecificPeriodAllMetricConfig(domainName, d, year, quarter, null);
        domainName = TaskFlowHelper.getActualDomainName(domainName, examineeConfigV2List, remoteConfig, employeeInfoDao);



        int directSubordinateNumber = employeeInfoDao.getDirectSubordinateNumber(domainName);
        //没有直接下属 直接g
        if (directSubordinateNumber == 0) {
            return;
        }
        BusinessDashboardEmployeeInfo employeeInfo = employeeInfoDao.queryByDomainName(domainName);
        List<BusinessDashboardOrganizationInfo> organizationInfoList = organizationInfoDao.queryByLeaderEmpCode(employeeInfo.getEmpCode());
        List<String> orgIdList = organizationInfoList.stream().map(BusinessDashboardOrganizationInfo::getNodeOrgId).collect(Collectors.toList());
        int allSubordinateNumber = employeeInfoDao.getAllSubordinateNumber(orgIdList);
        if (directSubordinateNumber == allSubordinateNumber) {
            subordinateTypeList.add("allSubordinate");
            return;
        }
        subordinateTypeList.add("directSubordinate");
        subordinateTypeList.add("allSubordinate");
    }


    /**
     * 特殊逻辑处理华西大区大区运营权限
     * @param subordinateTypeList
     */
    private void setSpecailRegionOperateSubordinateTypeList(List<String> subordinateTypeList) throws SQLException {
        String domesticDeptId = remoteConfig.getExternalConfig("domesticDeptId");
        String specialRegionId = remoteConfig.getExternalConfig("specialRegionId");
        BusinessDashboardOrganizationInfo organizationInfo = organizationInfoDao.queryByOrgId(domesticDeptId);
        String leaderEmpCode = organizationInfo.getLeaderEmpCode();
        int directSubordinateNumber = employeeInfoDao.getDirectSubordinateNumber(leaderEmpCode, specialRegionId);
        //没有直接下属 直接g
        if (directSubordinateNumber == 0) {
            return;
        }
        int allSubordinateNumber = employeeInfoDao.getAllSubordinateNumber(Lists.newArrayList(specialRegionId));
        if (directSubordinateNumber == allSubordinateNumber) {
            subordinateTypeList.add("allSubordinate");
            return;
        }
        subordinateTypeList.add("directSubordinate");
        subordinateTypeList.add("allSubordinate");
    }


//    public String getActualDomainName(String domainName,
//                                      List<BusinessDashboardExamineeConfigV2> examineeConfigV2List) throws SQLException {
//
//        //兜底 如果没有配置业绩看板权限 直接返回
//        if (GeneralUtil.isEmpty(examineeConfigV2List)) {
//            return domainName;
//        }
//
//        ExamineConfigBo bo = new ExamineConfigBo();
//        //去捞最新季度gmv的配置读取角色(所有的人都会配置收入力)
//        MetricInfoBean metricInfoBean = bo.getSingleMetricInfoBean(examineeConfigV2List, remoteConfig, "1");
//        //角色明细 from 张景昌
//        //1 一线运营   2  一线BD   3 省长  4 区域经理
//        //5 大区总    6  大区运营  7  中心人员
//        Integer role = metricInfoBean.getRole();
//        //非华西大区的大区运营需要转化为该大区的大区总
//        if (role == 6) {
//            BusinessDashboardEmployeeInfo leaderEmployeeInfo = employeeInfoDao.getLeaderEmployeeInfo(domainName);
//            return leaderEmployeeInfo.getDomainName();
//        }
//        //其他人不需要处理
//        return domainName;
//    }


    public CheckUserPermissionResponseType checkUserPermission(CheckUserPermissionRequestType request) throws Exception {

        CheckUserPermissionResponseType response = new CheckUserPermissionResponseType();
        //这个接口同时可能会改写request中domainName的值
        if (!isInDepartment41999(request)) {
            response.setHavePermission(false);
            return response;
        }

        String domainName = request.getDomainName();

        TimeFilter timeFilter = request.getTimeFilter();
        String d = dataUpdateBiz.getUpdateTime();
//        String d = "2025-07-30";
//        String d = LocalDate.now().toString();
        //兼容一期的情况 如果没有传入时间  则默认置为当前最新时间周期
        if (GeneralUtil.isEmpty(timeFilter)) {
            timeFilter = new TimeFilter();
            timeFilter.setDateType("quarter");
            timeFilter.setQuarter(DateUtil.getActualQuarterOfD(d));
            timeFilter.setYear(DateUtil.getActualYearOfD(d));
        }

        //由于目标是统一按季度配置的 将所有传入的时间统一归到季度
        String year = timeFilter.getYear();

        String quarter = DateUtil.getQuarterV2(timeFilter.getDateType(), timeFilter.getMonth(), timeFilter.getQuarter(), timeFilter.getHalf());


        List<BusinessDashboardExamineeConfigV2> examineeConfigV2List = examineConfigV2Dao.querySpecificPeriodAllMetricConfig(domainName, d, year, quarter, null);
        List<OverseaMetricInfoWithMetricBean> overseaConfigList = dimOrdTtdTargetConfigDao.queryOverseaPersonInfo(Collections.singletonList(quarter), year, null, null, domainName,null, d);

        //一个在门票活动业务部范围内的人可以是不参加考核的
        if (GeneralUtil.isEmpty(examineeConfigV2List) && GeneralUtil.isEmpty(overseaConfigList)) {
            response.setHavePermission(false);
            return response;
        }

        //从组织架构的层面判断一个人应该看国内看板还是海外看板
        Boolean isOverseaboard = isOverseaboard(domainName);

        //国内的情况
        if(!isOverseaboard && GeneralUtil.isNotEmpty(examineeConfigV2List)){
            // 24年
            checkDomesticUserPermission(response,examineeConfigV2List, timeFilter, remoteConfig);
            //25年之后走的新逻辑
            if ("2025".equals(year)) {

                try{
                    SessionUserInfo sessionUserInfo = SessionContext.getInstance().getUserInfo();
                    List<String> permissionList = sessionUserInfo.getPermissionList();
                    boolean hasIamPermission = permissionList.contains("bussines_dashboard");
                    response.setHavePermission(hasIamPermission);  //经营业绩看板权限FP17656
                    if (!hasIamPermission) {
                        return response;
                    }
                } catch (Exception e){
                    log.error("checkUserPermission error,orgEmoCode:{}", MDC.get("empCode"),e);
                }

                String dateType = timeFilter.getDateType();
                List<String> quarters;
                switch (dateType) {
                    case "half":
                        quarters = DateUtil.getQuarterListOfHalf(timeFilter.getHalf());
                        break;
                    case "month":
                    case "quarter":
                        quarters = Collections.singletonList(DateUtil.getQuarter(timeFilter.getDateType(), timeFilter.getMonth(), timeFilter.getQuarter(), timeFilter.getHalf()));
                        break;
                    case "year":
                        quarters = DateUtil.getQuarterListOfYear(timeFilter.getYear(), d);
                        break;
                    default:
                        throw new IllegalArgumentException("Unsupported date type: " + dateType);
                }
                Map<String, List<String>> inMap = new HashMap<>();
                inMap.put("quarter", quarters);
                List<BusinessDashboardExamineeConfigV2> domesticExamineConfig = examineConfigV2Dao.queryExamineConfig(domainName, d, year, inMap, null);
                if (GeneralUtil.isEmpty(domesticExamineConfig)) {
                    response.setHavePermission(false);
                    return response;
                }
                ExamineConfigBo bo = new ExamineConfigBo();
                List<MetricInfoBean> metricInfoBeanList = bo.getMetricInfoBeanListV2(domesticExamineConfig, remoteConfig);
                Map<String, List<MetricInfoBean>> metricInfoBeanMap = metricInfoBeanList.stream()
                        .collect(Collectors.groupingBy(MetricInfoBean::getMetric));

                checkDomesticUserPermissionV2(response,metricInfoBeanMap);
                sortBusinessMap(response.getBusinessMetricMapList());
            }
            return response;
        }
        //海外的情况
        if(isOverseaboard && GeneralUtil.isNotEmpty(overseaConfigList)){
            return checkOverseaUserPermission(domainName, overseaConfigList);
        }
        //其他未考虑的特殊情况 无权限兜底
        response.setHavePermission(false);
        return response;
    }

    public void sortBusinessMap(List<BusinessMetricMap> mapList) {
        if (mapList == null || mapList.isEmpty()) {
            return;
        }
        // 指标固定顺序
        List<String> metricOrder = Arrays.asList("tic", "play", "dayTour", "act", "gmv", "profit", "quality", "directSign", "sightCover", "typeCover", "ticketBooking", "categoryCover", "dayTourCover", "dayTourBooking");
        // 指标自定义比较器
        Comparator<BusinessMetricMap> comparatorMetric = (m1, m2) -> {
            int index1 = metricOrder.indexOf(m1.getCode());
            int index2 = metricOrder.indexOf(m2.getCode());

            // 处理不在列表中的 metric（排到最后）
            if (index1 == -1) index1 = Integer.MAX_VALUE;
            if (index2 == -1) index2 = Integer.MAX_VALUE;

            return Integer.compare(index1, index2);
        };
        for (BusinessMetricMap bean1 : mapList) {
            for (BusinessMetricMap bean2 : bean1.getMetricList()) {
                if (bean2.getMetricList() != null) {
                    bean2.getMetricList().sort(comparatorMetric);
                }
            }
            if (bean1.getMetricList() != null) {
                bean1.getMetricList().sort(comparatorMetric);
            }
        }
        mapList.sort(comparatorMetric);
    }




    public Boolean isOverseaboard(String domainName) throws SQLException {
        String domesticDeptId = remoteConfig.getExternalConfig("domesticDeptId");
        String overseaDeptId = remoteConfig.getExternalConfig("overseaDeptId");

        BusinessDashboardEmployeeInfo employeeInfo = employeeInfoDao.queryByDomainName(domainName);

        List<BusinessDashboardOrganizationInfo> organizationInfoList = organizationInfoDao.queryByLeaderEmpCode(employeeInfo.getEmpCode());
        List<String> nodeOrgIdList = organizationInfoList.stream().map(BusinessDashboardOrganizationInfo::getNodeOrgId).collect(Collectors.toList());
        //说明是国内leader
        if (nodeOrgIdList.contains(domesticDeptId)) {
            return false;
        }
        //说明是海外leader
        if (nodeOrgIdList.contains(overseaDeptId)) {
            return true;
        }
        //不属于这两种情况 则一定是下属
        //因为不属于门票活动业务部 或者 属于门票活动业务部但是不考核的情况都已经被过滤了
        String orgIdPath = employeeInfo.getOrgIdPath();
        return !orgIdPath.contains(domesticDeptId);
    }


    private String getDeptType(String domainName) throws SQLException {
        String domesticDeptId = remoteConfig.getExternalConfig("domesticDeptId");
        String overseaDeptId = remoteConfig.getExternalConfig("overseaDeptId");
        //添加信息运营的判断逻辑
        String infoOperaDeptId = remoteConfig.getExternalConfig("infoOperaDeptId");    //判断出陈丽平chenlp S05180 及其全部下属

        BusinessDashboardEmployeeInfo employeeInfo = employeeInfoDao.queryByDomainName(domainName);

        List<BusinessDashboardOrganizationInfo> organizationInfoList = organizationInfoDao.queryByLeaderEmpCode(employeeInfo.getEmpCode());
        List<String> nodeOrgIdList = organizationInfoList.stream().map(BusinessDashboardOrganizationInfo::getNodeOrgId).collect(Collectors.toList());


        //说明是国内leader
        if (nodeOrgIdList.contains(domesticDeptId)) {
            return "domesticEmployee";
        }

        //说明是海外leader
        if (nodeOrgIdList.contains(overseaDeptId)) {
            return "overseaEmployee";
        }

        //说明是信息运营leader
        if(nodeOrgIdList.contains(infoOperaDeptId)){
            return "infoOperaEmployee";
        }

        //不属于这三种情况 则要么是下属  要么既不是国内业务  也不是海外业务 也不是信息运营
        String orgIdPath = employeeInfo.getOrgIdPath();
        if (orgIdPath.contains(domesticDeptId)) {
            return "domesticEmployee";
        }

        if (orgIdPath.contains(overseaDeptId)) {
            return "overseaEmployee";
        }

        if (orgIdPath.contains(infoOperaDeptId)) {
            return "infoOperaEmployee";
        }


        return "otherEmployee";

    }

    /**
     * 判断当前需要查询的domainName是否在门票活动业务部范围内
     *
     * @return request
     */
    public Boolean isInDepartment41999(CheckUserPermissionRequestType request) throws Exception {

        String domainName = request.getDomainName();

        //domainName为null时此时为默认请求 校验的是当前登录用户是否在门票活动业务部中
        //domainName不为null时校验的是当前选中用户是否在门票活动业务部中
        if (GeneralUtil.isEmpty(domainName)) {
            String empCode = UserUtil.getMappingEmpCode(remoteConfig);
            //灰度测试阶段特殊逻辑  只有在特殊权限表里配置的人才能看到仪表盘
//            if ("".equals(empCode)) {
//                response.setHavePermission(false);
//                return response;
//            }
            //获取老季的账号 如果登录用户为老季 走特殊处理
            //老季默认查看的是李哲(adminInferior的第一个元素)
            String admin = remoteConfig.getExternalConfig("admin");
            if (admin.equals(empCode)) {
                String adminInferior = remoteConfig.getExternalConfig("adminInferior");
                empCode = adminInferior.split("\\|")[0];
            }

            BusinessDashboardEmployeeInfo employeeInfo = employeeInfoDao.queryByEmpCode(empCode);
            //不在门票活动业务部范围内
            if (GeneralUtil.isEmpty(employeeInfo)) {
                return false;
            }
            domainName = employeeInfo.getDomainName();
        } else {
            BusinessDashboardEmployeeInfo employeeInfo = employeeInfoDao.queryByDomainName(domainName);
            //不在门票活动业务部范围内
            if (GeneralUtil.isEmpty(employeeInfo)) {
                return false;
            }
        }
        request.setDomainName(domainName);
        return true;
    }

    /**
     * 校验国内业务权限
     * @param examineeConfigV2List
     * @return
     */
    private void checkDomesticUserPermission(CheckUserPermissionResponseType response,
                                                                        List<BusinessDashboardExamineeConfigV2> examineeConfigV2List,
                                                                        TimeFilter timeFilter,
                                                                        RemoteConfig remoteConfig) {
        response.setHavePermission(true);
        ExamineConfigBo bo = new ExamineConfigBo();
        response.setMetricList(bo.getMetricList(examineeConfigV2List));
        response.setBoardType("domestic");
        DomesticBasicConfig domesticBasicConfig = new DomesticBasicConfig();
        response.setDomesticBasicConfig(domesticBasicConfig);

        Map<String, AvailableSubMetric> trendLineConfigMap = bo.getTrendLineConfigMap(examineeConfigV2List, timeFilter, remoteConfig);
        Map<String, AvailableSubMetric> metricCardConfigMap = bo.getMetricCardConfigMap(examineeConfigV2List, timeFilter, remoteConfig);

        // 对于除了1；2指标的指标卡tab和下钻tab的处理
        bo.setOtherConfigMap(examineeConfigV2List, trendLineConfigMap, metricCardConfigMap, remoteConfig);

        domesticBasicConfig.setMetricCardConfigMap(metricCardConfigMap);
        domesticBasicConfig.setTrendLineConfigMap(trendLineConfigMap);
    }

    /**
     * 校验国内业务权限V2
     *
     * @param examineeConfigV2Map
     * @return
     */
    private void checkDomesticUserPermissionV2(CheckUserPermissionResponseType response,Map<String, List<MetricInfoBean>> examineeConfigV2Map) {
        response.setHavePermission(true);
        ExamineConfigBo bo = new ExamineConfigBo();
        int businessType = bo.getBusinessType(examineeConfigV2Map);
        if (businessType == -1){
            response.setHavePermission(false);
            return;
        }
        List<BusinessMetricMap> metricList = new ArrayList<>();
        switch (businessType) {
            case 0:
                metricList.add(bo.getBusinessMetricMapWithTic(examineeConfigV2Map));
                metricList.add(bo.getBusinessMetricMapWithAct(examineeConfigV2Map));
                break;
            case 1:
                metricList.add(bo.getBusinessMetricMapWithTic(examineeConfigV2Map));
                break;
            case 2:
                metricList.add(bo.getBusinessMetricMapWithAct(examineeConfigV2Map));
                break;
            default:
                response.setHavePermission(false);
                return;
        }
        response.setBusinessMetricMapList(metricList);
    }

    /**
     * 校验海外业务权限
     * @param domainName
     * @param overseaConfigList
     * @return
     */
    private CheckUserPermissionResponseType checkOverseaUserPermission(String domainName,
                                                                       List<OverseaMetricInfoWithMetricBean> overseaConfigList) {
        CheckUserPermissionResponseType response = new CheckUserPermissionResponseType();

        response.setHavePermission(true);
        response.setBoardType("oversea");

        OverSeaExamineConfigBO bo = new OverSeaExamineConfigBO();
        response.setMetricList(bo.getMetricListV2(overseaConfigList));

        OverseaBasicConfig overseaBasicConfig = new OverseaBasicConfig();
        response.setOverseaBasicConfig(overseaBasicConfig);
        String halfDomainName = remoteConfig.getExternalConfig("halfDomainName");
//        String oversea = remoteConfig.getConfigValue("oversea");
//        if (oversea.equals(overseaConfigList.get(0).getDestinationExamineLevel())) {
//            overseaBasicConfig.setDateType("half");
//        } else {
//            List<String> domainNameList = Lists.newArrayList(halfDomainName.split("\\|"));
//            overseaBasicConfig.setDateType(domainNameList.contains(domainName) ? "half" : "quarter");
//        }
        //只有孙磊和刘畅能按半年查看数据
        List<String> domainNameList = Lists.newArrayList(halfDomainName.split("\\|"));
        overseaBasicConfig.setDateType(domainNameList.contains(domainName) ? "half" : "quarter");

        List<OverseaMetricInfoBeanV2> metricInfoBeanList = bo.convertToMetricInfoBeanV2(overseaConfigList);
        overseaBasicConfig.setMetricCardConfigMap(bo.getMetricCardConfigMapV2(metricInfoBeanList));
        overseaBasicConfig.setTrendLineConfigMap(bo.getMetricCardConfigMapV2(metricInfoBeanList));

        return response;
    }
}
