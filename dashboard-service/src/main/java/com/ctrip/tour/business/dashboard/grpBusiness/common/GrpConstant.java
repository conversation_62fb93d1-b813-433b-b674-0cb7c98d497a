package com.ctrip.tour.business.dashboard.grpBusiness.common;

/**
 * <AUTHOR>
 * @Date 2024/12/13
 */
public interface GrpConstant {


    String SELF_SERVICE_COVERAGE_RATE = "selfServiceCoverageRate";

    String PROFIT_PRICE = "profitPrice";

    String INCOME = "income";

    String UV = "uv";

    String CONVERSION_RATE = "conversionRate";

    String MULTIPLE_PRICE = "multiplePrice";

    String MULTIPLE_PRICE_MORE_RATE = "multiplePriceMoreRate";

    String MULTIPLE_PRICE_LESS_RATE = "multiplePriceLessRate";

    String SELF_SERVICE_CONVERSION_RATE = "selfServiceConversionRate";

    String FITTING_NPS = "fittingNps";

    String RECOMMENDATION_RATE = "recommendationRate";

    String SLANDER_RATE = "slanderRate";

    String ORDER_COVERAGE_RATE = "orderCoverageRate";

    String PLATFORM_INFO_SCORE = "platformInfoScore";

    String WEIGHTED_PLATFORM_INFO_SCORE = "weightedPlatformInfoScore";

    String PRD_INFO_SCORE = "prdInfoScore";

    String WEIGHTED_PRD_INFO_SCORE = "weightedPrdInfoScore";

    String SELF_CR_RANK = "selfCrRank";

    String MULT_PRICE_LESS_THAN_SETTING = "mult_price_less_than_setting";

    String MULT_PRICE_GREAT_THAN_SETTING = "mult_price_great_than_setting";

    String SELF_SERVICE_COVERAGE_RATE_SETTING = "self_service_coverage_rate_setting";

    String DIY_PROFIT_ACHIEVE_RATE_SETTING = "diy_profit_achieve_rate_setting";

    String SELF_SERVICE_RATE_SETTING = "self_service_rate_setting";

    String GRP_GUIDER_DISPATCH_RATE = "grpGuiderDispatchRate";

    String GRP_GUIDER_ACTUAL_DISPACTH_RATE = "grpGuiderActualDispacthRate";

    String GRP_GUIDER_CHECKIN_RATE = "grpGuiderCheckInRate";

    String DIY_GUIDER_DISPATCH_RATE = "diyGuiderDispatchRate";

    String GRP_DRIVER_DISPATCH_RATE = "grpDriverDispatchRate";

    String GRP_DRIVER_EXECUTION_RATE = "grpDriverExecutionRate";

    String GRP_DRIVER_CHECKIN_RATE = "grpDriverCheckInRate";

    String DIY_DRIVER_DISPATCH_RATE = "diyDriverDispatchRate";

    String DIY_DRIVER_EXECUTION_RATE = "diyDriverExecutionRate";

    String DIY_DRIVER_ACTUAL_DISPACTH_RATE = "diyDriverActualDispacthRate";

    String WEIGHTED_ANOMALY_RATE = "weightedAnomalyRate";

    String UNCOMPARE_RATE = "unCompareRate";

    String TIMELY_RESPONSE_RATE = "timelyResponseRate";

    String AVG_TRANSLATION_REVIEW_SCORE = "avgTranslationReviewScore";

    String DISP_FAIL_RATE = "dispFailRate";

    String AVA_ORDER_CAP = "avaOrderCap";

    String VENDOR_ORD_ACCEPT_RATE = "vendorOrdAcceptRate";

    String COMPLETED_ORDERS = "completedOrders";

    String COMPLETED_ORDERS_RATE = "completedOrdersRate";

    String ORD_AVG_PRICE = "ordAvgPrice";

    String CPR_COMPLETED_ORDERS_RATE = "cprCompletedOrdersRate";

    String GRP_INCOME_ACHIEVE_RATE = "grpIncomeAchieveRate";

    String DIY_INCOME_ACHIEVE_RATE = "diyIncomeAchieveRate";

    String DIY_INCOME = "diyIncome";

    String GRP_PROFIT_ACHIEVE_RATE = "grpProfitAchieveRate";

    String DIY_PROFIT_ACHIEVE_RATE = "diyProfitAchieveRate";

    String DIY_PROFIT = "diyProfit";

    String DIY_FITTINGNPS = "diyFittingNps";

    String DIY_RECOMMENDATION_RATE = "diyRecommendationRate";

    String DIY_SLANDER_RATE = "diySlanderRate";

    String CALC_DATE_NAME = "calcDateName";

    String SELF_OPR_INCOME = "selfOprIncome";

    String SELF_OPR_INCOME_RATIO = "selfOprIncomeRatio";

    String SELF_OPR_PROFIT_RATE = "selfOprProfitRate";

    String SELF_OPR_PROFIT = "selfOprProfit";

    String SELF_OPR_PROFIT_RATIO = "selfOprProfitRatio";

    String SELF_OPR_CONVERSION_RATE = "selfOprConversionRate";

    String AGENT_CONVERSION_RATE = "agentConversionRate";

    String SELF_OPR_SINGLE_UV_VAL_PRD = "selfOprSingleUVValPrd";

    String AGENT_SINGLE_UV_VAL_PRD = "agentSingleUVValPrd";

    String SELF_OPR_PARENT_PRD_COUNT = "selfOprParentPrdCount";

    String ACTIVE_SELF_OPR_PARENT_PRD_COUNT = "activeSelfOprParentPrdCount";

    String ACTIVE_SELF_OPR_PARENT_PRD_RATE = "activeSelfOprParentPrdRate";

    String SRV_FREQUENCY_INLST30D = "srvFrequencyInLst30d";

    String HIGH_QUA_VENDOR_COUNT_WITH_SELFOPRPRD = "highQuaVendorCountWithSelfOprPrd";

    String HIGH_QUA_VENDOR_COUNT_WITH_SELFOPR_PRD_FAILTOMEET = "highQuaVendorCountWithSelfOprPrdFailToMeet";

    String SELF_OPR_UV = "selfOprUv";



}
