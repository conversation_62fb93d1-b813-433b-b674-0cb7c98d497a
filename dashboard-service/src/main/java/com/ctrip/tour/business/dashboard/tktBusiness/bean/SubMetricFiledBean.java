package com.ctrip.tour.business.dashboard.tktBusiness.bean;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
public class SubMetricFiledBean {

    //归属的子指标列表
    List<String> subMetricList;
    //归属的下钻维度
    String field;
    //获取下钻基础信息时需要的id
    Long baseInfoId;
    //获取下钻基础信息时需要的分组字段
    List<String> baseInfoGroupList;
    //获取下钻基础信息时需要查询的条件对于的index 与baseInfoGroupList字段相对应(并需要考虑多语言)
    List<Integer> baseInfoLikeIndexList;
    //是否需要气泡图
    Boolean needBubble;
    //是否需要折线图
    Boolean needLine;
    //获取下钻数据id对应map
    Map<String,Long> tableDataIdMap;
    //获取气泡图数据分组字段对应的map
    Map<String,List<String>> bubbleGroupListMap;
    //获取折线图数据分组字段对应的map
    Map<String,List<String>> lineGroupListMap;
    //获取下钻数据时传入的条件对应的数据库字段
    String conditionColumn;
    //下钻时是否需要目标
    Boolean needTarget;
    //表头
    List<String> headerFieldList;
    ///获取下钻表格数据分组字段对应的map
    Map<String,List<String>> tableGroupListMap;
    //需要特殊下钻处理的标识
    String pagingConditionColumn;
    //气泡图或者首页前端需要锚定的维度字段名称
    String showField;
    //气泡图或者首页前端需要锚定的维度字段名称id
    String showFieldId;
}
