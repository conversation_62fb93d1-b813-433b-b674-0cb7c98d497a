package com.ctrip.tour.business.dashboard.tktBusiness.strategy.domestic.impl;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.grpBusiness.enums.DomesticMetricEnum;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.domestic.DomesticMetricCalStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

@Component
public class Bus13DomesticMetricStrategy implements DomesticMetricCalStrategy {

    @Autowired
    @Qualifier("bus4DomesticMetricStrategy")
    private DomesticMetricCalStrategy bus4DomesticMetricStrategy;

    @Autowired
    @Qualifier("bus5DomesticMetricStrategy")
    private DomesticMetricCalStrategy bus5DomesticMetricStrategy;

    @Autowired
    @Qualifier("bus6DomesticMetricStrategy")
    private DomesticMetricCalStrategy bus6DomesticMetricStrategy;

    @Autowired
    @Qualifier("bus7DomesticMetricStrategy")
    private  DomesticMetricCalStrategy bus7DomesticMetricStrategy;

    @Override
    public Future<DomesticMetricDetailInfo> getSingleMetricCardData(String domainName, TimeFilter timeFilter, List<MetricInfoBean> metricInfoBean, String d, Boolean isFirst, Integer businessId) throws Exception {
        DomesticMetricDetailInfo metricDetailInfo = new DomesticMetricDetailInfo();
        metricDetailInfo.setMetricCode(DomesticMetricEnum.getCodeById(getMetricName()));
        List<DomesticMetricDetailInfo> subList = new ArrayList<>();
        subList.add(bus4DomesticMetricStrategy.getSingleMetricCardData(domainName, timeFilter, metricInfoBean.stream().filter(e -> "4".equals(e.getMetric())).collect(Collectors.toList()), d, isFirst, businessId).get());
        subList.add(bus5DomesticMetricStrategy.getSingleMetricCardData(domainName, timeFilter, metricInfoBean.stream().filter(e -> "5".equals(e.getMetric())).collect(Collectors.toList()), d, isFirst, businessId).get());
        subList.add(bus6DomesticMetricStrategy.getSingleMetricCardData(domainName, timeFilter, metricInfoBean.stream().filter(e -> "6".equals(e.getMetric())).collect(Collectors.toList()), d, isFirst, businessId).get());
        subList.add(bus7DomesticMetricStrategy.getSingleMetricCardData(domainName, timeFilter, metricInfoBean.stream().filter(e -> "7".equals(e.getMetric())).collect(Collectors.toList()), d, isFirst, businessId).get());
        metricDetailInfo.setSubMetricDetailInfoList(subList);
        return new AsyncResult<>(metricDetailInfo);
    }

    @Override
    public GetDomesticMetricTrendDataResponseType getSingleTrendlineData(GetDomesticMetricTrendDataRequestType request, String d) throws Exception {
        return null;
    }

    @Override
    public GetDomesticTableDataResponseType getSingleTableData(GetDomesticTableDataRequestType request, MetricInfoBean metricInfoBean, String d) throws Exception {
        return null;
    }

    @Override
    public GetDomesticDrillDownBaseInfoResponseType getSingleDrillDownBaseInfo(GetDomesticDrillDownBaseInfoRequestType request, MetricInfoBean metricInfoBean, String d) throws Exception {
        return null;
    }
    @Override
    public GetFirstPageDomesticMetricCardDrillDataResponseType getFirstPageDomesticMetricCardDrillData(
            GetFirstPageDomesticMetricCardDrillDataRequestType request,MetricInfoBean metricInfoBean, String d) throws Exception{
        return null;
    }
    @Override
    public Integer getMetricName() {
        return 13;
    }
}
