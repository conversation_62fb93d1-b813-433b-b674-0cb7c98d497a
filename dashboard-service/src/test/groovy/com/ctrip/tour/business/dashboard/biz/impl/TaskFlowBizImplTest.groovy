package com.ctrip.tour.business.dashboard.biz.impl


import com.ctrip.soa._27181.BIBaseReportQueryServiceClient
import com.ctrip.tour.business.dashboard.tktBusiness.biz.impl.DataUpdateBizImpl
import com.ctrip.tour.business.dashboard.tktBusiness.biz.impl.TaskFlowBizImpl
import com.ctrip.tour.business.dashboard.tktBusiness.biz.impl.TaskFlowMetricCardBizImpl
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardEmployeeInfoDao
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardExamineeConfigV2Dao
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardOrganizationInfoDao
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardEmployeeInfo
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardExamineeConfigV2
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardOrganizationInfo
import com.ctrip.tour.business.dashboard.tktBusiness.impl.TaskFlowSyncJobImpl
import com.ctrip.tour.business.dashboard.utils.MapperUtil
import spock.lang.Specification

import java.time.LocalDate

class TaskFlowBizImplTest extends Specification {
    def employeeInfoDao = Mock(BusinessDashboardEmployeeInfoDao)
    def organizationInfoDao = Mock(BusinessDashboardOrganizationInfoDao)
    def examineConfigV2Dao = Mock(BusinessDashboardExamineeConfigV2Dao)
    def remoteConfig = Mock(RemoteConfig)
//    def remoteConfig = MapperUtil.str2Obj("{\"configMap\":{\"targetManageUrlPreffix\":\"http://product.activity.fat29.qa.nt.ctripcorp.com/ttd-operation/targetManage\",\"province\":\"省份\",\"universalStudios\":\"环球影城\",\"region\":\"大区\",\"bd\":\"商拓\",\"kaRegion\":\"KA大区\",\"three\":\"三方\",\"destinationT\":\"T站\",\"excludeOrgId\":\"62431\",\"provinceId\":\"-9999|5001|6001|7001|8001\",\"subRegion\":\"子区域\",\"oversea\":\"海外\",\"scenario\":\"tour-bi-business-dashboard-log\",\"childrenOrgIdList\":\"62337|62355|62390|SO001665|62402|SO002565|SO004005|SO002566|SO005325\",\"destinationC\":\"C站\",\"profileRedisName\":\"ttd_bi_profile_redis\",\"viewspot\":\"景点\",\"domestic\":\"国内\",\"parentOrgId\":\"SO001057\"},\"employeeRelationMap\":{\"S68368\":\"D00024\",\"TR019390\":\"D00024\",\"S79530\":\"D01984\",\"S22035\":\"S52754\",\"TR015896\":\"D00024\",\"S68332\":\"D00024\",\"TR009198\":\"D01984\",\"S58662\":\"D00024\",\"S78157\":\"D00024\",\"S28153\":\"S08033\",\"TR021161\":\"D00024\",\"TR015613\":\"S04850\",\"S52246\":\"D00024\",\"TR032346\":\"D03977\",\"S58720\":\"D00024\",\"S79743\":\"S08033\",\"TR018838\":\"S35097\",\"TR019403\":\"S35097\",\"S75483\":\"D00024\",\"S76285\":\"D00024\",\"S20149\":\"S08033\",\"S41524\":\"S08033\",\"S79971\":\"D00024\",\"S74773\":\"S08033\",\"TR013600\":\"S35097\",\"TR003932\":\"S08033\",\"TR032913\":\"S35097\",\"S74975\":\"S08033\",\"TR005148\":\"S35097\",\"hr003\":\"D01984\",\"S78177\":\"D00024\",\"S39497\":\"D00024\"},\"externalConfigMap\":{\"adminInferior\":\"D00024|S52754\",\"adminOrgId\":\"41999\",\"regionRankId\":\"62402|62355|62337|SO002322|62390|SO001665\",\"specailRegionOperate\":\"wsong\",\"overseaDeptId\":\"SO003287\",\"regionalManager\":\"{\\\"D00024\\\":\\\"KA大区|62402\\\"}\",\"halfDomainName\":\"chang.liu|leisun\",\"admin\":\"S35097\",\"domesticDeptId\":\"SO001057\",\"specialRegionId\":\"62390\"},\"organizationOrderMap\":{\"SO001057\":\"62337|62355|62390|SO001665|62402|SO002322|SO002565|SO004005|SO002566|SO005325\",\"SO002322\":\"SO002326|SO002328|SO002325|SO002323|SO002324\"},\"overseaDrillDownMetadata\":{\"metricMetadataList\":[{\"metricList\":[\"101\",\"102\"],\"subMetricFieldList\":[{\"subMetricList\":[\"destination\",\"destination_c\",\"destination_t\"],\"field\":\"region\",\"baseInfoId\":6,\"baseInfoGroupList\":[\"region_id\",\"region_name\"],\"baseInfoLikeIndexList\":[1],\"needBubble\":true,\"tableDataIdMap\":{\"other\":6,\"target\":1},\"bubbleGroupListMap\":{\"other\":[\"region_id\",\"region_name\"],\"target\":[\"region_id\"]},\"conditionColumn\":\"region_id\",\"needTarget\":true,\"headerFieldList\":[\"region_name\"],\"tableGroupListMap\":{\"other\":[\"region_id\",\"region_name\"],\"target\":[\"region_id\"]},\"showField\":\"region_name\",\"showFieldId\":\"region_id\"},{\"subMetricList\":[\"destination\",\"destination_c\",\"destination_t\"],\"field\":\"province\",\"baseInfoId\":6,\"baseInfoGroupList\":[\"province_id\",\"province_name\"],\"baseInfoLikeIndexList\":[1],\"needBubble\":true,\"tableDataIdMap\":{\"other\":6,\"target\":1},\"bubbleGroupListMap\":{\"other\":[\"province_id\",\"province_name\"],\"target\":[\"province_id\"]},\"conditionColumn\":\"province_id\",\"needTarget\":true,\"headerFieldList\":[\"region_name\",\"province_name\"],\"tableGroupListMap\":{\"other\":[\"region_id\",\"region_name\",\"province_id\",\"province_name\"],\"target\":[\"region_id\",\"province_id\"]},\"showField\":\"province_name\",\"showFieldId\":\"province_id\"},{\"subMetricList\":[\"destination\",\"destination_c\",\"destination_t\"],\"field\":\"country\",\"baseInfoId\":6,\"baseInfoGroupList\":[\"country_id\",\"country_name\"],\"baseInfoLikeIndexList\":[1],\"needBubble\":false,\"tableDataIdMap\":{\"other\":6},\"conditionColumn\":\"country_id\",\"needTarget\":false,\"headerFieldList\":[\"region_name\",\"province_name\",\"country_name\"],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\",\"country_id\",\"country_name\"]}},{\"subMetricList\":[\"destination\",\"destination_c\",\"destination_t\"],\"field\":\"examinee\",\"baseInfoId\":12,\"baseInfoGroupList\":[\"examinee\",\"examinee_display\"],\"baseInfoLikeIndexList\":[1],\"needBubble\":false,\"tableDataIdMap\":{\"2019\":6,\"target\":4,\"lastyear\":6,\"current\":12},\"conditionColumn\":\"examinee\",\"needTarget\":true,\"headerFieldList\":[\"region_name\",\"province_name\",\"examinee_display\"],\"tableGroupListMap\":{\"2019\":[\"region_id\",\"province_id\"],\"target\":[\"region_id\",\"province_id\",\"examinee\"],\"current\":[\"region_name\",\"region_id\",\"province_name\",\"province_id\",\"examinee_display\",\"examinee\"],\"lastyear\":[\"region_id\",\"province_id\"]},\"pagingConditionColumn\":\"province_id\"},{\"subMetricList\":[\"destination\",\"destination_c\",\"destination_t\"],\"field\":\"viewspot\",\"baseInfoId\":10,\"baseInfoGroupList\":[\"viewspot_id\",\"viewspot_name\"],\"baseInfoLikeIndexList\":[0,1],\"needBubble\":false,\"tableDataIdMap\":{\"current\":10,\"other\":11},\"conditionColumn\":\"viewspot_id\",\"needTarget\":false,\"headerFieldList\":[\"region_name\",\"province_name\",\"country_name\",\"viewspot_id\",\"viewspot_name\"],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\",\"country_name\",\"viewspot_id\",\"viewspot_name\"]}},{\"subMetricList\":[\"site\"],\"field\":\"site\",\"baseInfoId\":9,\"baseInfoGroupList\":[\"site_code\",\"site\"],\"baseInfoLikeIndexList\":[1],\"needBubble\":true,\"tableDataIdMap\":{\"other\":9,\"target\":2},\"bubbleGroupListMap\":{\"other\":[\"site_code\",\"site\"],\"target\":[\"site_code\"]},\"conditionColumn\":\"site_code\",\"needTarget\":true,\"headerFieldList\":[\"site\"],\"tableGroupListMap\":{\"other\":[\"site_code\",\"site\"],\"target\":[\"site_code\"]},\"showField\":\"site\",\"showFieldId\":\"site_code\"},{\"subMetricList\":[\"site\"],\"field\":\"locale\",\"baseInfoId\":9,\"baseInfoGroupList\":[\"locale_code\",\"locale\"],\"baseInfoLikeIndexList\":[1],\"needBubble\":false,\"tableDataIdMap\":{\"other\":9},\"conditionColumn\":\"locale_code\",\"needTarget\":false,\"headerFieldList\":[\"site\",\"locale\"],\"tableGroupListMap\":{\"other\":[\"site\",\"locale_code\",\"locale\"]}},{\"subMetricList\":[\"channel\"],\"field\":\"channel\",\"baseInfoId\":9,\"baseInfoGroupList\":[\"channel_code\",\"channel_name\"],\"baseInfoLikeIndexList\":[1],\"needBubble\":true,\"tableDataIdMap\":{\"other\":9,\"target\":3},\"bubbleGroupListMap\":{\"other\":[\"channel_code\",\"channel_name\"],\"target\":[\"channel_code\"]},\"conditionColumn\":\"channel_code\",\"needTarget\":true,\"headerFieldList\":[\"channel_name\"],\"tableGroupListMap\":{\"other\":[\"channel_code\",\"channel_name\"],\"target\":[\"channel_code\"]},\"showField\":\"channel_name\",\"showFieldId\":\"channel_code\"},{\"subMetricList\":[\"channel\"],\"field\":\"site\",\"baseInfoId\":9,\"baseInfoGroupList\":[\"site_code\",\"site\"],\"baseInfoLikeIndexList\":[1],\"needBubble\":false,\"tableDataIdMap\":{\"other\":9},\"conditionColumn\":\"site_code\",\"needTarget\":false,\"headerFieldList\":[\"channel_name\",\"site\"],\"tableGroupListMap\":{\"other\":[\"channel_name\",\"site\",\"site_code\"]}},{\"subMetricList\":[\"channel\"],\"field\":\"locale\",\"baseInfoId\":9,\"baseInfoGroupList\":[\"locale_code\",\"locale\"],\"baseInfoLikeIndexList\":[1],\"needBubble\":false,\"tableDataIdMap\":{\"other\":9},\"conditionColumn\":\"locale_code\",\"needTarget\":false,\"headerFieldList\":[\"channel_name\",\"site\",\"locale\"],\"tableGroupListMap\":{\"other\":[\"channel_name\",\"site\",\"locale_code\",\"locale\"]}}]},{\"metricList\":[\"103\"],\"subMetricFieldList\":[{\"subMetricList\":[\"destination_c\",\"destination_t\"],\"field\":\"region\",\"baseInfoId\":32,\"baseInfoGroupList\":[\"region_id\",\"region_name\"],\"baseInfoLikeIndexList\":[1],\"needLine\":true,\"tableDataIdMap\":{\"other\":32,\"target\":38},\"lineGroupListMap\":{\"current\":[\"region_name\"]},\"conditionColumn\":\"region_id\",\"needTarget\":true,\"headerFieldList\":[\"region_name\"],\"tableGroupListMap\":{\"other\":[\"region_id\",\"region_name\"],\"target\":[\"region_id\"]},\"showField\":\"region_name\",\"showFieldId\":\"region_id\"},{\"subMetricList\":[\"destination_c\",\"destination_t\"],\"field\":\"province\",\"baseInfoId\":32,\"baseInfoGroupList\":[\"province_id\",\"province_name\"],\"baseInfoLikeIndexList\":[1],\"needLine\":true,\"tableDataIdMap\":{\"other\":32,\"target\":38},\"lineGroupListMap\":{\"current\":[\"province_name\"]},\"conditionColumn\":\"province_id\",\"needTarget\":true,\"headerFieldList\":[\"region_name\",\"province_name\"],\"tableGroupListMap\":{\"other\":[\"region_id\",\"region_name\",\"province_id\",\"province_name\"],\"target\":[\"region_id\",\"province_id\"]},\"showField\":\"province_name\",\"showFieldId\":\"province_id\"},{\"subMetricList\":[\"destination_c\",\"destination_t\"],\"field\":\"country\",\"baseInfoId\":32,\"baseInfoGroupList\":[\"country_id\",\"country_name\"],\"baseInfoLikeIndexList\":[1],\"needLine\":false,\"tableDataIdMap\":{\"other\":32},\"conditionColumn\":\"country_id\",\"needTarget\":false,\"headerFieldList\":[\"region_name\",\"province_name\",\"country_name\"],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\",\"country_id\",\"country_name\"]}},{\"subMetricList\":[\"destination_c\",\"destination_t\"],\"field\":\"examinee\",\"baseInfoId\":33,\"baseInfoGroupList\":[\"examinee\",\"examinee_display\"],\"baseInfoLikeIndexList\":[1],\"needLine\":false,\"tableDataIdMap\":{\"other\":33,\"target\":38},\"conditionColumn\":\"examinee\",\"needTarget\":true,\"headerFieldList\":[\"region_name\",\"province_name\",\"examinee_display\"],\"tableGroupListMap\":{\"other\":[\"region_name\",\"region_id\",\"province_name\",\"province_id\",\"examinee_display\",\"examinee\"],\"target\":[\"region_id\",\"province_id\"]}},{\"subMetricList\":[\"destination_c\",\"destination_t\"],\"field\":\"viewspot\",\"baseInfoId\":34,\"baseInfoGroupList\":[\"viewspot_id\",\"viewspot_name\"],\"baseInfoLikeIndexList\":[0,1],\"needLine\":false,\"tableDataIdMap\":{\"other\":34},\"conditionColumn\":\"viewspot_id\",\"needTarget\":false,\"headerFieldList\":[\"region_name\",\"province_name\",\"country_name\",\"viewspot_id\",\"viewspot_name\"],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\",\"country_name\",\"viewspot_id\",\"viewspot_name\"]}},{\"subMetricList\":[\"destination_c\",\"destination_t\"],\"field\":\"vendor\",\"baseInfoId\":35,\"baseInfoGroupList\":[\"vendor_id\",\"vendor_name\"],\"baseInfoLikeIndexList\":[0,1],\"needLine\":false,\"tableDataIdMap\":{\"other\":35},\"conditionColumn\":\"vendor_id\",\"needTarget\":false,\"headerFieldList\":[\"region_name\",\"province_name\",\"vendor_id\",\"vendor_name\"],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\",\"vendor_id\",\"vendor_name\"]}},{\"subMetricList\":[\"site\"],\"field\":\"site\",\"baseInfoId\":37,\"baseInfoGroupList\":[\"site_code\",\"site\"],\"baseInfoLikeIndexList\":[1],\"needLine\":true,\"tableDataIdMap\":{\"other\":37,\"target\":39},\"lineGroupListMap\":{\"current\":[\"site\"]},\"conditionColumn\":\"site_code\",\"needTarget\":true,\"headerFieldList\":[\"site\"],\"tableGroupListMap\":{\"other\":[\"site_code\",\"site\"],\"target\":[\"site_code\"]},\"showField\":\"site\",\"showFieldId\":\"site_code\"},{\"subMetricList\":[\"site\"],\"field\":\"viewspot\",\"baseInfoId\":36,\"baseInfoGroupList\":[\"viewspot_id\",\"viewspot_name\"],\"baseInfoLikeIndexList\":[0,1],\"needLine\":false,\"tableDataIdMap\":{\"other\":36},\"conditionColumn\":\"viewspot_id\",\"needTarget\":false,\"headerFieldList\":[\"site\",\"viewspot_id\",\"viewspot_name\"],\"tableGroupListMap\":{\"other\":[\"site\",\"viewspot_id\",\"viewspot_name\"]}},{\"subMetricList\":[\"site\"],\"field\":\"vendor\",\"baseInfoId\":37,\"baseInfoGroupList\":[\"vendor_id\",\"vendor_name\"],\"baseInfoLikeIndexList\":[0,1],\"needLine\":false,\"tableDataIdMap\":{\"other\":37},\"conditionColumn\":\"vendor_id\",\"needTarget\":false,\"headerFieldList\":[\"site\",\"vendor_id\",\"vendor_name\"],\"tableGroupListMap\":{\"other\":[\"site\",\"vendor_id\",\"vendor_name\"]}}]},{\"metricList\":[\"105\",\"106\",\"107\"],\"subMetricFieldList\":[{\"subMetricList\":[\"tklk\",\"tfly\"],\"field\":\"region\",\"baseInfoGroupList\":[\"region_id\",\"region_name\"],\"baseInfoLikeIndexList\":[1],\"needLine\":true,\"lineGroupListMap\":{\"current\":[\"region_name\"]},\"conditionColumn\":\"region_id\",\"headerFieldList\":[\"region_name\"],\"tableGroupListMap\":{\"other\":[\"region_id\",\"region_name\"]},\"showField\":\"region_name\",\"showFieldId\":\"region_id\"},{\"subMetricList\":[\"tklk\",\"tfly\"],\"field\":\"province\",\"baseInfoGroupList\":[\"province_id\",\"province_name\"],\"baseInfoLikeIndexList\":[1],\"needLine\":true,\"lineGroupListMap\":{\"current\":[\"province_name\"]},\"conditionColumn\":\"province_id\",\"headerFieldList\":[\"region_name\",\"province_name\"],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\"]},\"showField\":\"province_name\",\"showFieldId\":\"province_id\"},{\"subMetricList\":[\"tklk\",\"tfly\"],\"field\":\"country\",\"baseInfoGroupList\":[\"country_id\",\"country_name\"],\"baseInfoLikeIndexList\":[1],\"needLine\":false,\"conditionColumn\":\"country_id\",\"headerFieldList\":[\"region_name\",\"province_name\",\"country_name\"],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\",\"country_name\"]}},{\"subMetricList\":[\"tklk\",\"tfly\"],\"field\":\"examinee\",\"baseInfoGroupList\":[\"examinee_display\",\"examinee\"],\"baseInfoLikeIndexList\":[1],\"needLine\":false,\"conditionColumn\":\"examinee\",\"headerFieldList\":[\"region_name\",\"province_name\",\"examinee_display\"],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\",\"examinee_display\"]}}]}]},\"domesticDrillDownMetadata\":{\"metricMetadataList\":[{\"metricList\":[\"10\"],\"drillDownFieldList\":[{\"field\":\"region_name\",\"baseInfoId\":13,\"baseInfoGroupList\":[\"region_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"firstpage\":[\"region_name\"],\"detailpage\":[\"region_name\"]},\"tableOrderList\":[\"complete_people_rate\"]},{\"field\":\"province_name\",\"baseInfoId\":13,\"baseInfoGroupList\":[\"province_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"firstpage\":[\"province_name\"],\"detailpage\":[\"region_name\",\"province_name\"]},\"tableOrderList\":[\"complete_people_rate\"]},{\"field\":\"examinee\",\"baseInfoId\":15,\"baseInfoGroupList\":[\"examinee\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"firstpage\":[\"examinee\",\"examinee_name\"],\"detailpage\":[\"region_name\",\"province_name\",\"examinee\",\"examinee_name\"]},\"tableOrderList\":[\"complete_people_rate\"]}]},{\"metricList\":[\"1\",\"2\"],\"drillDownFieldList\":[{\"subMetric\":\"domesticDayTour\",\"field\":\"region_name\",\"baseInfoId\":17,\"baseInfoGroupList\":[\"region_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"other\":[\"region_name\"]},\"tableDataIdMap\":{\"current\":17,\"other\":18,\"target\":19},\"bubbleGroupListMap\":{\"other\":[\"region_name\"]}},{\"subMetric\":\"domesticDayTour\",\"field\":\"province_name\",\"baseInfoId\":17,\"baseInfoGroupList\":[\"province_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\"]},\"tableDataIdMap\":{\"current\":17,\"other\":18,\"target\":19},\"bubbleGroupListMap\":{\"other\":[\"province_name\"]}},{\"subMetric\":\"domesticDayTour\",\"field\":\"examinee\",\"baseInfoId\":21,\"baseInfoGroupList\":[\"examinee\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"current\":[\"region_name\",\"province_name\",\"examinee_name\",\"examinee\"],\"other\":[\"examinee\"]},\"tableDataIdMap\":{\"current\":21,\"other\":22,\"target\":23},\"bubbleGroupListMap\":{\"other\":[\"examinee\"]}},{\"subMetric\":\"overseaDayTour\",\"field\":\"region_name\",\"baseInfoId\":42,\"baseInfoGroupList\":[\"region_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"other\":[\"region_name\"]},\"tableDataIdMap\":{\"current\":42,\"other\":43,\"target\":44},\"bubbleGroupListMap\":{\"other\":[\"region_name\"]}},{\"subMetric\":\"overseaDayTour\",\"field\":\"province_name\",\"baseInfoId\":42,\"baseInfoGroupList\":[\"province_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\"]},\"tableDataIdMap\":{\"current\":42,\"other\":43,\"target\":44},\"bubbleGroupListMap\":{\"other\":[\"province_name\"]}},{\"subMetric\":\"overseaDayTour\",\"field\":\"examinee\",\"baseInfoId\":45,\"baseInfoGroupList\":[\"examinee\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"current\":[\"region_name\",\"province_name\",\"examinee_name\",\"examinee\"],\"other\":[\"examinee\"]},\"tableDataIdMap\":{\"current\":45,\"other\":46,\"target\":47},\"bubbleGroupListMap\":{\"other\":[\"examinee\"]}},{\"subMetric\":\"ticketActivity+domesticDayTour\",\"field\":\"region_name\",\"baseInfoId\":24,\"baseInfoGroupList\":[\"region_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"other\":[\"region_name\"]},\"tableDataIdMap\":{\"current\":24,\"other\":25,\"target\":26},\"bubbleGroupListMap\":{\"other\":[\"region_name\"]}},{\"subMetric\":\"ticketActivity+domesticDayTour\",\"field\":\"province_name\",\"baseInfoId\":24,\"baseInfoGroupList\":[\"province_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\"]},\"tableDataIdMap\":{\"current\":24,\"other\":25,\"target\":26},\"bubbleGroupListMap\":{\"other\":[\"province_name\"]}},{\"subMetric\":\"ticketActivity+domesticDayTour\",\"field\":\"examinee\",\"baseInfoId\":54,\"baseInfoGroupList\":[\"examinee\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"current\":[\"region_name\",\"ttd_province_name\",\"odt_province_name\",\"examinee_name\",\"examinee\"],\"other\":[\"examinee\"]},\"tableDataIdMap\":{\"currentmonth\":54,\"currentquarter\":55,\"otherttd\":31,\"otherodt\":22},\"bdSpecificCondition\":\"ttd_province_name|odt_province_name\",\"bdSpecificList\":[\"ttd\",\"odt\"]},{\"subMetric\":\"ticketActivity+domesticDayTour+overseaDayTour\",\"field\":\"region_name\",\"baseInfoId\":49,\"baseInfoGroupList\":[\"region_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"other\":[\"region_name\"]},\"tableDataIdMap\":{\"current\":49,\"other\":50,\"target\":51},\"bubbleGroupListMap\":{\"other\":[\"region_name\"]}},{\"subMetric\":\"ticketActivity+domesticDayTour+overseaDayTour\",\"field\":\"province_name\",\"baseInfoId\":49,\"baseInfoGroupList\":[\"province_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\"]},\"tableDataIdMap\":{\"current\":49,\"other\":50,\"target\":51},\"bubbleGroupListMap\":{\"other\":[\"province_name\"]}},{\"subMetric\":\"ticketActivity+domesticDayTour+overseaDayTour\",\"field\":\"examinee\",\"baseInfoId\":56,\"baseInfoGroupList\":[\"examinee\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"current\":[\"region_name\",\"ttd_province_name\",\"odt_province_name\",\"odt_ob_province_name\",\"examinee_name\",\"examinee\"],\"other\":[\"examinee\"]},\"tableDataIdMap\":{\"currentmonth\":56,\"currentquarter\":57,\"otherttd\":31,\"otherodt\":22,\"otherodtob\":46},\"bdSpecificCondition\":\"ttd_province_name|odt_province_name|odt_ob_province_name\",\"bdSpecificList\":[\"ttd\",\"odt\",\"odtob\"]}]},{\"metricList\":[\"11\"],\"drillDownFieldList\":[{\"field\":\"region_name\",\"baseInfoId\":27,\"baseInfoGroupList\":[\"region_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"firstpage\":[\"region_name\"],\"detailpage\":[\"region_name\"]}},{\"field\":\"province_name\",\"baseInfoId\":27,\"baseInfoGroupList\":[\"province_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"firstpage\":[\"province_name\"],\"detailpage\":[\"region_name\",\"province_name\"]}},{\"field\":\"examinee\",\"baseInfoId\":27,\"baseInfoGroupList\":[\"examinee\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"firstpage\":[\"province_name\"],\"detailpage\":[\"region_name\",\"province_name\",\"examinee_name\",\"examinee\"]}}]}]},\"domesticExamineTypeConfigData\":{\"mergedSubMetricDataList\":[[\"ticketActivity\",\"domesticDayTour\",\"overseaDayTour\"],[\"ticketActivity\",\"domesticDayTour\"]],\"examineTypeMetadataList\":[{\"examineTypeList\":[1,2,3],\"subMetricList\":[\"ticketActivity\"]},{\"examineTypeList\":[4,5,6],\"subMetricList\":[\"ticketActivity\",\"domesticDayTour\"]},{\"examineTypeList\":[7],\"subMetricList\":[\"domesticDayTour\"]},{\"examineTypeList\":[8],\"subMetricList\":[\"overseaDayTour\"]},{\"examineTypeList\":[9,10,12],\"subMetricList\":[\"ticketActivity\",\"overseaDayTour\"]},{\"examineTypeList\":[11],\"subMetricList\":[\"domesticDayTour\",\"overseaDayTour\"]},{\"examineTypeList\":[13,14,15],\"subMetricList\":[\"ticketActivity\",\"domesticDayTour\",\"overseaDayTour\"]}]}}", RemoteConfig.class)
    def dataUpdateBiz = Mock(DataUpdateBizImpl)
    def client = Mock(BIBaseReportQueryServiceClient)
    def taskFlowSyncJob = Mock(TaskFlowSyncJobImpl)
    def taskFlowMetricCardBiz = new TaskFlowMetricCardBizImpl(
            employeeInfoDao: employeeInfoDao,
            organizationInfoDao: organizationInfoDao,
            examineConfigV2Dao: examineConfigV2Dao,
            remoteConfig: remoteConfig,
            dataUpdateBiz: dataUpdateBiz,
            client: client
    )

    // 伪造数据
    static D00024EmployeeInfoDaoQueryByEmpCode = MapperUtil.str2Obj("{\"id\":452,\"empCode\":\"D00024\",\"displayName\":\"Zhe Li （李哲）\",\"domainName\":\"zheli\",\"leaderEmpCode\":\"S35097\",\"leaderEmpName\":\"JIM Ji （季毅华）\",\"teamId\":\"41999\",\"teamCname\":\"门票活动业务部\",\"orgIdPath\":\"Ctrip_CO0001 Ctrip_Board 29 SO002916 25481 41999\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部\",\"position\":\"\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:38:56 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:38:56 AM\"}", BusinessDashboardEmployeeInfo.class)
    static S52754EmployeeInfoDaoQueryByEmpCode = MapperUtil.str2Obj("{\"id\":171,\"empCode\":\"S52754\",\"displayName\":\"Chase Liu （刘畅）\",\"domainName\":\"chang.liu\",\"leaderEmpCode\":\"S35097\",\"leaderEmpName\":\"JIM Ji （季毅华）\",\"teamId\":\"41999\",\"teamCname\":\"门票活动业务部\",\"orgIdPath\":\"Ctrip_CO0001 Ctrip_Board 29 SO002916 25481 41999\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部\",\"position\":\"\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:38:56 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:38:56 AM\"}", BusinessDashboardEmployeeInfo.class)
    static S08033EmployeeInfoDaoQueryByEmpCode = MapperUtil.str2Obj("{\"id\":21,\"empCode\":\"S08033\",\"displayName\":\"Lei Sun （孙磊）\",\"domainName\":\"leisun\",\"leaderEmpCode\":\"S52754\",\"leaderEmpName\":\"Chase Liu （刘畅）\",\"teamId\":\"SO006478\",\"teamCname\":\"亚洲大区\",\"orgIdPath\":\"Ctrip_CO0001 Ctrip_Board 29 SO002916 25481 41999 SO003287 SO004444 SO006478\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>国际化业务部>国际化业务中心>亚洲大区\",\"position\":\"\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:38:56 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:38:56 AM\"}", BusinessDashboardEmployeeInfo.class)
    static S04850EmployeeInfoDaoQueryByEmpCode = MapperUtil.str2Obj("{\"id\":122,\"empCode\":\"S04850\",\"displayName\":\"Hang Yin （殷航）\",\"domainName\":\"hyin\",\"leaderEmpCode\":\"D00024\",\"leaderEmpName\":\"Zhe Li （李哲）\",\"teamId\":\"SO001057\",\"teamCname\":\"门票活动国内业务部\",\"orgIdPath\":\"Ctrip_CO0001 Ctrip_Board 29 SO002916 25481 41999 SO001057\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>门票活动国内业务部\",\"position\":\"\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:38:56 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:38:56 AM\"}", BusinessDashboardEmployeeInfo.class)

    static D00024QuerySpecificPeriodAllMetricConfig = MapperUtil.str2List("[{\"id\":7057086,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2023\",\"quarter\":\"Q4\",\"examineMetric\":\"1;2\",\"examineType\":15,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"国内\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2023-12-18\",\"datachangeLasttime\":\"Dec 18, 2023 1:19:55 AM\",\"odtExamineLevel\":\"国内\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"国内\",\"overseaOdtExamineRange\":\"\"},{\"id\":7057093,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2023\",\"quarter\":\"Q4\",\"examineMetric\":\"3\",\"examineType\":0,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2023-12-18\",\"datachangeLasttime\":\"Dec 18, 2023 1:19:55 AM\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"},{\"id\":7057100,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2023\",\"quarter\":\"Q4\",\"examineMetric\":\"4\",\"examineType\":0,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2023-12-18\",\"datachangeLasttime\":\"Dec 18, 2023 1:19:55 AM\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"},{\"id\":7057107,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2023\",\"quarter\":\"Q4\",\"examineMetric\":\"5;6;7\",\"examineType\":0,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2023-12-18\",\"datachangeLasttime\":\"Dec 18, 2023 1:19:55 AM\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"},{\"id\":7057114,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2023\",\"quarter\":\"Q4\",\"examineMetric\":\"9\",\"examineType\":0,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2023-12-18\",\"datachangeLasttime\":\"Dec 18, 2023 1:19:55 AM\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"},{\"id\":7057121,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2023\",\"quarter\":\"Q4\",\"examineMetric\":\"10\",\"examineType\":0,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2023-12-18\",\"datachangeLasttime\":\"Dec 18, 2023 1:19:55 AM\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"},{\"id\":7057128,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2023\",\"quarter\":\"Q4\",\"examineMetric\":\"11\",\"examineType\":0,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2023-12-18\",\"datachangeLasttime\":\"Dec 18, 2023 1:19:55 AM\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"}]", BusinessDashboardExamineeConfigV2.class)
    static S52754QuerySpecificPeriodAllMetricConfig = MapperUtil.str2List("[]", BusinessDashboardExamineeConfigV2.class)
    static S08033QuerySpecificPeriodAllMetricConfig = MapperUtil.str2List("[]", BusinessDashboardExamineeConfigV2.class)
    static S04850QuerySpecificPeriodAllMetricConfig = MapperUtil.str2List("[{\"id\":7052375,\"domainName\":\"hyin\",\"role\":5,\"year\":\"2023\",\"quarter\":\"Q4\",\"examineMetric\":\"1;2\",\"examineType\":2,\"examineLevel\":\"大区\",\"examineRange\":\"KA大区\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2023-12-18\",\"datachangeLasttime\":\"Dec 18, 2023 1:19:55 AM\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"},{\"id\":7052382,\"domainName\":\"hyin\",\"role\":5,\"year\":\"2023\",\"quarter\":\"Q4\",\"examineMetric\":\"3\",\"examineType\":0,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2023-12-18\",\"datachangeLasttime\":\"Dec 18, 2023 1:19:55 AM\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"},{\"id\":7052389,\"domainName\":\"hyin\",\"role\":5,\"year\":\"2023\",\"quarter\":\"Q4\",\"examineMetric\":\"5;6;7\",\"examineType\":0,\"examineLevel\":\"大区\",\"examineRange\":\"KA大区\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2023-12-18\",\"datachangeLasttime\":\"Dec 18, 2023 1:19:55 AM\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"},{\"id\":7052396,\"domainName\":\"hyin\",\"role\":5,\"year\":\"2023\",\"quarter\":\"Q4\",\"examineMetric\":\"9\",\"examineType\":0,\"examineLevel\":\"大区\",\"examineRange\":\"KA大区\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2023-12-18\",\"datachangeLasttime\":\"Dec 18, 2023 1:19:55 AM\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"}]", BusinessDashboardExamineeConfigV2.class)


    static D00024GetLeaderEmployeeInfo = MapperUtil.str2Obj("", BusinessDashboardEmployeeInfo.class)
    static S52754GetLeaderEmployeeInfo = MapperUtil.str2Obj("", BusinessDashboardEmployeeInfo.class)
    static S08033GetLeaderEmployeeInfo = MapperUtil.str2Obj("{\"id\":171,\"empCode\":\"S52754\",\"displayName\":\"Chase Liu （刘畅）\",\"domainName\":\"chang.liu\",\"leaderEmpCode\":\"S35097\",\"leaderEmpName\":\"JIM Ji （季毅华）\",\"teamId\":\"41999\",\"teamCname\":\"门票活动业务部\",\"orgIdPath\":\"Ctrip_CO0001 Ctrip_Board 29 SO002916 25481 41999\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部\",\"position\":\"\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:38:56 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:38:56 AM\"}", BusinessDashboardEmployeeInfo.class)
    static S04850GetLeaderEmployeeInfo = MapperUtil.str2Obj("{\"id\":452,\"empCode\":\"D00024\",\"displayName\":\"Zhe Li （李哲）\",\"domainName\":\"zheli\",\"leaderEmpCode\":\"S35097\",\"leaderEmpName\":\"JIM Ji （季毅华）\",\"teamId\":\"41999\",\"teamCname\":\"门票活动业务部\",\"orgIdPath\":\"Ctrip_CO0001 Ctrip_Board 29 SO002916 25481 41999\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部\",\"position\":\"\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:38:56 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:38:56 AM\"}", BusinessDashboardEmployeeInfo.class)


    static D00024QueryByDomainName = MapperUtil.str2Obj("{\"id\":452,\"empCode\":\"D00024\",\"displayName\":\"Zhe Li （李哲）\",\"domainName\":\"zheli\",\"leaderEmpCode\":\"S35097\",\"leaderEmpName\":\"JIM Ji （季毅华）\",\"teamId\":\"41999\",\"teamCname\":\"门票活动业务部\",\"orgIdPath\":\"Ctrip_CO0001 Ctrip_Board 29 SO002916 25481 41999\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部\",\"position\":\"\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:38:56 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:38:56 AM\"}", BusinessDashboardEmployeeInfo.class)
    static S52754QueryByDomainName = MapperUtil.str2Obj("{\"id\":171,\"empCode\":\"S52754\",\"displayName\":\"Chase Liu （刘畅）\",\"domainName\":\"chang.liu\",\"leaderEmpCode\":\"S35097\",\"leaderEmpName\":\"JIM Ji （季毅华）\",\"teamId\":\"41999\",\"teamCname\":\"门票活动业务部\",\"orgIdPath\":\"Ctrip_CO0001 Ctrip_Board 29 SO002916 25481 41999\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部\",\"position\":\"\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:38:56 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:38:56 AM\"}", BusinessDashboardEmployeeInfo.class)
    static S08033QueryByDomainName = MapperUtil.str2Obj("{\"id\":21,\"empCode\":\"S08033\",\"displayName\":\"Lei Sun （孙磊）\",\"domainName\":\"leisun\",\"leaderEmpCode\":\"S52754\",\"leaderEmpName\":\"Chase Liu （刘畅）\",\"teamId\":\"SO006478\",\"teamCname\":\"亚洲大区\",\"orgIdPath\":\"Ctrip_CO0001 Ctrip_Board 29 SO002916 25481 41999 SO003287 SO004444 SO006478\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>国际化业务部>国际化业务中心>亚洲大区\",\"position\":\"\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:38:56 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:38:56 AM\"}", BusinessDashboardEmployeeInfo.class)
    static S04850QueryByDomainName = MapperUtil.str2Obj("{\"id\":122,\"empCode\":\"S04850\",\"displayName\":\"Hang Yin （殷航）\",\"domainName\":\"hyin\",\"leaderEmpCode\":\"D00024\",\"leaderEmpName\":\"Zhe Li （李哲）\",\"teamId\":\"SO001057\",\"teamCname\":\"门票活动国内业务部\",\"orgIdPath\":\"Ctrip_CO0001 Ctrip_Board 29 SO002916 25481 41999 SO001057\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>门票活动国内业务部\",\"position\":\"\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:38:56 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:38:56 AM\"}", BusinessDashboardEmployeeInfo.class)


    static D00024QueryByLeaderEmpCode = MapperUtil.str2List("[{\"id\":132,\"nodeOrgId\":\"SO001057\",\"nodeOrgType\":\"Team\",\"nodeOrgName\":\"门票活动国内业务部\",\"nodeOrgLevel\":\"7\",\"parentOrgId\":\"41999\",\"buId\":\"25481\",\"deptId\":\"41999\",\"orgIdPath\":\"Ctrip_CO0001.Ctrip_Board.29.SO002916.25481.41999.SO001057\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>门票活动国内业务部\",\"leaderEmpCode\":\"D00024\",\"leaderEmpName\":\"Zhe Li （李哲）\",\"empCnt\":\"314\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:26:08 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:26:08 AM\"},{\"id\":133,\"nodeOrgId\":\"SO002566\",\"nodeOrgType\":\"Team\",\"nodeOrgName\":\"国内业务三方供应中心\",\"nodeOrgLevel\":\"8\",\"parentOrgId\":\"SO001057\",\"buId\":\"25481\",\"deptId\":\"41999\",\"orgIdPath\":\"Ctrip_CO0001.Ctrip_Board.29.SO002916.25481.41999.SO001057.SO002566\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>门票活动国内业务部>国内业务三方供应中心\",\"leaderEmpCode\":\"D00024\",\"leaderEmpName\":\"Zhe Li （李哲）\",\"empCnt\":\"\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:26:08 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:26:08 AM\"},{\"id\":134,\"nodeOrgId\":\"62390\",\"nodeOrgType\":\"Team\",\"nodeOrgName\":\"华西大区\",\"nodeOrgLevel\":\"8\",\"parentOrgId\":\"SO001057\",\"buId\":\"25481\",\"deptId\":\"41999\",\"orgIdPath\":\"Ctrip_CO0001.Ctrip_Board.29.SO002916.25481.41999.SO001057.62390\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>门票活动国内业务部>华西大区\",\"leaderEmpCode\":\"D00024\",\"leaderEmpName\":\"Zhe Li （李哲）\",\"empCnt\":\"61\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:26:08 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:26:08 AM\"},{\"id\":135,\"nodeOrgId\":\"SO003279\",\"nodeOrgType\":\"Team\",\"nodeOrgName\":\"海外供应链运营组\",\"nodeOrgLevel\":\"9\",\"parentOrgId\":\"SO002566\",\"buId\":\"25481\",\"deptId\":\"41999\",\"orgIdPath\":\"Ctrip_CO0001.Ctrip_Board.29.SO002916.25481.41999.SO001057.SO002566.SO003279\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>门票活动国内业务部>国内业务三方供应中心>海外供应链运营组\",\"leaderEmpCode\":\"D00024\",\"leaderEmpName\":\"Zhe Li （李哲）\",\"empCnt\":\"\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:26:08 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:26:08 AM\"}]", BusinessDashboardOrganizationInfo.class)
    static S52754QueryByLeaderEmpCode = MapperUtil.str2List("[{\"id\":5,\"nodeOrgId\":\"62997\",\"nodeOrgType\":\"Team\",\"nodeOrgName\":\"国际化业务职能中心\",\"nodeOrgLevel\":\"8\",\"parentOrgId\":\"SO003287\",\"buId\":\"25481\",\"deptId\":\"41999\",\"orgIdPath\":\"Ctrip_CO0001.Ctrip_Board.29.SO002916.25481.41999.SO003287.62997\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>国际化业务部>国际化业务职能中心\",\"leaderEmpCode\":\"S52754\",\"leaderEmpName\":\"Chase Liu （刘畅）\",\"empCnt\":\"26\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:26:08 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:26:08 AM\"},{\"id\":6,\"nodeOrgId\":\"SO003886\",\"nodeOrgType\":\"Team\",\"nodeOrgName\":\"东北亚大区\",\"nodeOrgLevel\":\"10\",\"parentOrgId\":\"SO006478\",\"buId\":\"25481\",\"deptId\":\"41999\",\"orgIdPath\":\"Ctrip_CO0001.Ctrip_Board.29.SO002916.25481.41999.SO003287.SO004444.SO006478.SO003886\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>国际化业务部>国际化业务中心>亚洲大区>东北亚大区\",\"leaderEmpCode\":\"S52754\",\"leaderEmpName\":\"Chase Liu （刘畅）\",\"empCnt\":\"\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:26:08 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:26:08 AM\"},{\"id\":7,\"nodeOrgId\":\"SO004444\",\"nodeOrgType\":\"Team\",\"nodeOrgName\":\"国际化业务中心\",\"nodeOrgLevel\":\"8\",\"parentOrgId\":\"SO003287\",\"buId\":\"25481\",\"deptId\":\"41999\",\"orgIdPath\":\"Ctrip_CO0001.Ctrip_Board.29.SO002916.25481.41999.SO003287.SO004444\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>国际化业务部>国际化业务中心\",\"leaderEmpCode\":\"S52754\",\"leaderEmpName\":\"Chase Liu （刘畅）\",\"empCnt\":\"64\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:26:08 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:26:08 AM\"},{\"id\":8,\"nodeOrgId\":\"SO003287\",\"nodeOrgType\":\"Team\",\"nodeOrgName\":\"国际化业务部\",\"nodeOrgLevel\":\"7\",\"parentOrgId\":\"41999\",\"buId\":\"25481\",\"deptId\":\"41999\",\"orgIdPath\":\"Ctrip_CO0001.Ctrip_Board.29.SO002916.25481.41999.SO003287\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>国际化业务部\",\"leaderEmpCode\":\"S52754\",\"leaderEmpName\":\"Chase Liu （刘畅）\",\"empCnt\":\"92\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:26:08 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:26:08 AM\"},{\"id\":9,\"nodeOrgId\":\"SO006478\",\"nodeOrgType\":\"Team\",\"nodeOrgName\":\"亚洲大区\",\"nodeOrgLevel\":\"9\",\"parentOrgId\":\"SO004444\",\"buId\":\"25481\",\"deptId\":\"41999\",\"orgIdPath\":\"Ctrip_CO0001.Ctrip_Board.29.SO002916.25481.41999.SO003287.SO004444.SO006478\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>国际化业务部>国际化业务中心>亚洲大区\",\"leaderEmpCode\":\"S52754\",\"leaderEmpName\":\"Chase Liu （刘畅）\",\"empCnt\":\"50\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:26:08 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:26:08 AM\"}]", BusinessDashboardOrganizationInfo.class)
    static S08033QueryByLeaderEmpCode = MapperUtil.str2List("[{\"id\":151,\"nodeOrgId\":\"SO008891\",\"nodeOrgType\":\"Team\",\"nodeOrgName\":\"泰国DMC\",\"nodeOrgLevel\":\"10\",\"parentOrgId\":\"SO006478\",\"buId\":\"25481\",\"deptId\":\"41999\",\"orgIdPath\":\"Ctrip_CO0001.Ctrip_Board.29.SO002916.25481.41999.SO003287.SO004444.SO006478.SO008891\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>国际化业务部>国际化业务中心>亚洲大区>泰国DMC\",\"leaderEmpCode\":\"S08033\",\"leaderEmpName\":\"Lei Sun （孙磊）\",\"empCnt\":\"9\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:26:08 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:26:08 AM\"}]", BusinessDashboardOrganizationInfo.class)
    static S04850QueryByLeaderEmpCode = MapperUtil.str2List("[{\"id\":25,\"nodeOrgId\":\"62402\",\"nodeOrgType\":\"Team\",\"nodeOrgName\":\"KA大区\",\"nodeOrgLevel\":\"8\",\"parentOrgId\":\"SO001057\",\"buId\":\"25481\",\"deptId\":\"41999\",\"orgIdPath\":\"Ctrip_CO0001.Ctrip_Board.29.SO002916.25481.41999.SO001057.62402\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>门票活动国内业务部>KA大区\",\"leaderEmpCode\":\"S04850\",\"leaderEmpName\":\"Hang Yin （殷航）\",\"empCnt\":\"1\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:26:08 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:26:08 AM\"}]", BusinessDashboardOrganizationInfo.class)


    static D00024QueryByOrgId = MapperUtil.str2Obj("{\"id\":132,\"nodeOrgId\":\"SO001057\",\"nodeOrgType\":\"Team\",\"nodeOrgName\":\"门票活动国内业务部\",\"nodeOrgLevel\":\"7\",\"parentOrgId\":\"41999\",\"buId\":\"25481\",\"deptId\":\"41999\",\"orgIdPath\":\"Ctrip_CO0001.Ctrip_Board.29.SO002916.25481.41999.SO001057\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>门票活动国内业务部\",\"leaderEmpCode\":\"D00024\",\"leaderEmpName\":\"Zhe Li （李哲）\",\"empCnt\":\"314\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:26:08 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:26:08 AM\"}", BusinessDashboardOrganizationInfo.class)
    static S52754QueryByOrgId = MapperUtil.str2Obj("{\"id\":5,\"nodeOrgId\":\"62997\",\"nodeOrgType\":\"Team\",\"nodeOrgName\":\"国际化业务职能中心\",\"nodeOrgLevel\":\"8\",\"parentOrgId\":\"SO003287\",\"buId\":\"25481\",\"deptId\":\"41999\",\"orgIdPath\":\"Ctrip_CO0001.Ctrip_Board.29.SO002916.25481.41999.SO003287.62997\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>国际化业务部>国际化业务职能中心\",\"leaderEmpCode\":\"S52754\",\"leaderEmpName\":\"Chase Liu （刘畅）\",\"empCnt\":\"26\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:26:08 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:26:08 AM\"}", BusinessDashboardOrganizationInfo.class)
    static S08033QueryByOrgId = MapperUtil.str2Obj("{\"id\":151,\"nodeOrgId\":\"SO008891\",\"nodeOrgType\":\"Team\",\"nodeOrgName\":\"泰国DMC\",\"nodeOrgLevel\":\"10\",\"parentOrgId\":\"SO006478\",\"buId\":\"25481\",\"deptId\":\"41999\",\"orgIdPath\":\"Ctrip_CO0001.Ctrip_Board.29.SO002916.25481.41999.SO003287.SO004444.SO006478.SO008891\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>国际化业务部>国际化业务中心>亚洲大区>泰国DMC\",\"leaderEmpCode\":\"S08033\",\"leaderEmpName\":\"Lei Sun （孙磊）\",\"empCnt\":\"9\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:26:08 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:26:08 AM\"}", BusinessDashboardOrganizationInfo.class)
    static S04850QueryByOrgId = MapperUtil.str2Obj("{\"id\":25,\"nodeOrgId\":\"62402\",\"nodeOrgType\":\"Team\",\"nodeOrgName\":\"KA大区\",\"nodeOrgLevel\":\"8\",\"parentOrgId\":\"SO001057\",\"buId\":\"25481\",\"deptId\":\"41999\",\"orgIdPath\":\"Ctrip_CO0001.Ctrip_Board.29.SO002916.25481.41999.SO001057.62402\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>门票活动国内业务部>KA大区\",\"leaderEmpCode\":\"S04850\",\"leaderEmpName\":\"Hang Yin （殷航）\",\"empCnt\":\"1\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:26:08 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:26:08 AM\"}", BusinessDashboardOrganizationInfo.class)

    static d = LocalDate.now().plusDays(-1).toString()


    def taskBiz = new TaskFlowBizImpl(
            employeeInfoDao: employeeInfoDao,
            organizationInfoDao: organizationInfoDao,
            examineConfigV2Dao: examineConfigV2Dao,
            remoteConfig: remoteConfig,
            dataUpdateBiz: dataUpdateBiz,
            taskFlowMetricCardBiz: taskFlowMetricCardBiz,
            client: client,
            taskFlowSyncJob: taskFlowSyncJob
    )


//    def "GetTaskLevelDim"() {
//        given: "构造入参"
//        def request = new GetTaskLevelDimRequestType()
//
//        and: "内部调用mock"
//        dataUpdateBiz.getUpdateTime() >> d
//        employeeInfoDao.queryByEmpCode(empCode) >> employeeInfoDaoQueryByEmpCode
//        examineConfigV2Dao.querySpecificPeriodAllMetricConfig(employeeInfoDaoQueryByEmpCode.getDomainName(), _, _, _, _) >> querySpecificPeriodAllMetricConfig
//        employeeInfoDao.getLeaderEmployeeInfo(employeeInfoDaoQueryByEmpCode.getDomainName()) >> getLeaderEmployeeInfo
//        employeeInfoDao.queryByDomainName(employeeInfoDaoQueryByEmpCode.getDomainName()) >> queryByDomainName
//        organizationInfoDao.queryByLeaderEmpCode(queryByDomainName.getEmpCode()) >> queryByLeaderEmpCode
//        employeeInfoDao.getAllSubordinateDomainNameList(_) >> MapperUtil.str2List("[]", String.class)
//        taskFlowSyncJob.getEumnDataTypeMap() >> MapperUtil.str2Obj("{\"biz_category_code\":{\"BOARD\":\"业绩看板\",\"ORDER\":\"订单\",\"QUALITY\":\"质量\",\"MARKET\":\"营销\",\"DISTRIBUTION\":\"分销\",\"PRODUCT\":\"商品\",\"POI\":\"POI\",\"SALEUNIT_SHELF\":\"票种&货架\",\"JIANGHULING\":\"江湖令\"},\"task_collection_code\":{\"COLLECTION_PRODUCT_CAPABILITY\":\"江湖令国内-门票\",\"COLLECTION_MKT_BUYOUT_VOUCHER\":\"批量凭证审核\",\"COLLECTION_TICKET_CAPABILITY_ABROAD\":\"江湖令海外-门票\",\"COLLECTION_DAYTOUR_CAPABILITY_OVERSEA\":\"江湖令出境-日游\",\"COLLECTION_BIZ_BOARD\":\"经营业绩看板任务\",\"COLLECTION_POI_AUDIT_OVERSEA\":\"POI（海外）\",\"COLLECTION_LOWER_COMMISSION\":\"低佣商品审核\",\"COLLECTION_SALE_UNIT_INFO_OVERSEA\":\"票种信息申请（海外）\",\"COLLECTION_POI_AUDIT\":\"POI（国内）\",\"COLLECTION_VENDOR_CONFIRM\":\"系统对接\",\"COLLECTION_ORDER_BATCH\":\"订单批量处理\",\"COLLECTION_DAYTOUR_CAPABILITY\":\"江湖令国内-日游\",\"COLLECTION_MKT_ACTIVITY\":\"营销活动\",\"COLLECTION_MKT_BUYOUT_STOCKTICKET\":\"买断囤票\",\"COLLECTION_ORDER_NOTICE\":\"订单通知\",\"COLLECTION_PRODUCT_NOTICE\":\"商品系统通知\",\"COLLECTION_TNT_DISTRIBUTION\":\"分销申请\",\"COLLECTION_TICKET_CAPABILITY_OVERSEA\":\"江湖令出境-门票\",\"COLLECTION_STOCKUP\":\"票种管控\",\"COLLECTION_IBU_TRANS\":\"翻译项目\",\"COLLECTION_SALE_UNIT_INFO\":\"票种信息申请\",\"COLLECTION_PRICE_CHANGED\":\"价格变更\",\"COLLECTION_STOCKUP_OVERSEA\":\"票种管控（海外）\"},\"task_type_code\":{\"TASK_SALE_UNIT_ACTIVITY\":\"活动票种申请\",\"TASK_POI_UPDATE_INTERN\":\"修改POI信息（用户）\",\"TASK_PRODUCT_OFFLINE_NOTICE\":\"商品下线通知\",\"TASK_POI_UPDATE\":\"修改POI信息\",\"TASK_BIZ_TARGET\":\"业务目标调整\",\"TASK_POI_CREATE_OVERSEA\":\"新增POI信息（海外）\",\"TASK_COST_CORRECTION\":\"费用包含纠错\",\"TASK_ORDER_BATCH_CTRIP_DISTRIBUTOR\":\"订单批量处理-ctrip对分销商\",\"TASK_TICKET_SALEUNIT_UNMATCHED_OVERSEA\":\"票种未匹配（出境）\",\"TASK_ACT_NOTICE\":\"营销活动通知\",\"TASK_TNT_DIS_PRICE_RULE\":\"分销-改价/专享\",\"TASK_STOCKUP_EXPIRY_NOTICE_OVERSEA\":\"上货管控到期提醒-海外\",\"TASK_ORDER_BATCH_CTRIP_PASSENGER\":\"订单批量处理-ctrip对客\",\"TASK_SALE_PROPERTY\":\"销售属性申请\",\"TASK_WATER_CARD\":\"直签/水牌覆盖指标组织目标变更\",\"TASK_TNT_DIS_PRICE_PROTECTED\":\"分销-价格保护\",\"TASK_VENDOR_CONFIRM_05\":\"系统对接-资源当日首次对接失败\",\"TASK_STOCK_WARNING_NOTICE\":\"库存预警通知\",\"TASK_VENDOR_CONFIRM_04\":\"系统对接-关键词未匹配超阈值预警\",\"TASK_TICKET_PRODUCT_WEAKNESS\":\"江湖令商品力劣势\",\"TASK_MKT_BUYOUT_STOCKTICKET_NOTICE\":\"买断囤票通知\",\"TASK_LINES_TREASURE_COVERAGE_OVERSEA\":\"线路未覆盖（出境藏宝图）\",\"TASK_SALE_UNIT_OVERSEA\":\"票种申请（海外）\",\"TASK_STOCKUP_PROTECTED_CLOSE\":\"上货保护关闭申请\",\"TASK_ORDER_BATCH_TRIP\":\"订单批量处理-trip对客/分销商\",\"TASK_SALE_UNIT_OTHER_OVERSEA\":\"修改票种信息（海外）\",\"TASK_TNT_DIS_RESOURCE_REBATE\":\"分销-开放平台调用\",\"TASK_PRODUCT_OFFLINE_NOTICE_OVERSEA\":\"商品下线通知-海外业务\",\"TASK_PRODUCT_SALEUNIT_UNMATCHED\":\"票种未匹配\",\"TASK_FLIGGY_PRODUCT_WEAKNESS_OVERSEA\":\"江湖令-商品覆盖缺失（海外飞猪）\",\"TASK_DAYTOUR_WHITELIST\":\"白名单申请\",\"TASK_PEOPLE_LIMIT_OVERSEA\":\"人群修改申请（海外）\",\"TASK_VENDOR_CONFIRM_03\":\"系统对接-对接失败通知\",\"TASK_VENDOR_CONFIRM_02\":\"系统对接-对接失败超阈值预警\",\"TASK_TNT_DIS_POI_UPD\":\"分销商挂接信息修改\",\"TASK_VENDOR_CONFIRM_01\":\"系统对接熔断\",\"TASK_TNT_DIS_SALE_RULE\":\"开关分销\",\"TASK_STOCKUP_PROTECTED_APPLY\":\"上货保护申请\",\"TASK_ORDER_NOTICE\":\"订单系统通知\",\"TASK_STOCKUP_EXPIRY_NOTICE\":\"上货管控到期提醒\",\"TASK_PRODUCT_SCENICSPOT_WEAKNESS\":\"景点劣势\",\"TASK_PRODUCT_SCENICSPOT_UNMATCHED\":\"景点未匹配\",\"TASK_POI_UPDATE_OVERSEA\":\"修改POI信息（海外）\",\"TASK_FLIGGY_SALEUNIT_UNMATCHED_OVERSEA\":\"江湖令-票种未匹配（海外飞猪）\",\"TASK_POI_SEASON_IMG\":\"POI时令封图\",\"TASK_PRODUCT_COVERAGE\":\"商品覆盖\",\"TASK_ACT_PRODUCT_FINAL_AUDIT\":\"活动商品终审\",\"TASK_PRICE_CHANGED\":\"对接价格变更\",\"TASK_TICKET_COVERAGE_OVERSEA\":\"商品覆盖缺失（出境）\",\"TASK_PEOPLE_GROUP_OVERSEA\":\"人群申请（海外）\",\"TASK_FLIGGY_SCENICSPOT_WEAKNESS_OVERSEA\":\"江湖令-景点劣势（海外飞猪）\",\"TASK_POI_CREATE\":\"新增POI信息\",\"TASK_LINES_COVERAGE\":\"线路覆盖劣势\",\"TASK_ACT_PRODUCT_PRE_AUDIT\":\"活动商品初审\",\"TASK_LINES_REFINE\":\"线路提炼有误\",\"TASK_STOCKUP_PROTECTED_CLOSE_OVERSEA\":\"上货保护关闭申请-海外\",\"TASK_TICKET_SALEUNIT_IMPROVE_OVERSEA\":\"江湖令-票种信息完善（出境）\",\"TASK_ACT_PRODUCT_APPLY\":\"商品提报\",\"TASK_STOCKUP_PROTECTED_APPLY_OVERSEA\":\"上货保护申请-海外\",\"TASK_SALE_PROPERTY_OVERSEA\":\"销售属性申请（海外）\",\"TASK_SALE_PRICE\":\"门市价申请\",\"TASK_IBU_TRANS\":\"门票活动翻译申请\",\"TASK_PEOPLE_GROUP\":\"人群申请\",\"TASK_LOWER_COMMISSION\":\"低佣商品审核\",\"TASK_FLIGGY_SCENICSPOT_UNMATCHED_OVERSEA\":\"江湖令-景点未匹配（海外飞猪）\",\"TASK_POI_SEASON_IMG_NOTICE\":\"POI时令封图通知\",\"TASK_STOCK_OUT_NOTICE\":\"库存售罄通知\",\"TASK_STOCKUP_APPLY_OVERSEA\":\"管控白名单修改-海外\",\"TASK_PEOPLE_LIMIT\":\"人群修改申请\",\"TASK_SALE_PRICE_OVERSEA\":\"门市价申请（海外）\",\"TASK_STOCKUP_APPLY\":\"管控白名单修改申请\",\"TASK_SALE_UNIT_OTHER\":\"修改票种信息\",\"TASK_TICKET_SCENICSPOT_UNMATCHED_OVERSEA\":\"景点未匹配（出境）\",\"TASK_TICKET_SCENICSPOT_WEAKNESS_OVERSEA\":\"景点劣势（出境）\",\"TASK_TICKET_PRODUCT_WEAKNESS_OVERSEA\":\"江湖令商品力劣势（出境）\",\"TASK_TICKET_WHITELIST\":\"江湖令门票白名单申请\",\"TASK_TICKET_WHITELIST_OVERSEA\":\"江湖令门票白名单申请（出境）\",\"TASK_TICKET_TREASURE_COVERAGE_OVERSEA\":\"商品覆盖缺失（出境藏宝图）\",\"TASK_SALE_UNIT\":\"票种申请\",\"TASK_COST_CORRECTION_OVERSEA\":\"费用包含纠错（海外）\",\"TASK_PRODUCT_NOTICE\":\"商品系统通知\",\"TASK_MKT_BUYOUT_VOUCHER\":\"买断下载凭证\"}}", Map.class)
//        def str = "{\"result\":\"[[\\\"BOARD\\\",\\\"业绩看板\\\",\\\"COLLECTION_MKT_BUYOUT_VOUCHER\\\",\\\"批量凭证审核\\\",\\\"TASK_ACT_PRODUCT_PRE_AUDIT\\\",\\\"活动商品初审\\\"],[\\\"JIANGHULING\\\",\\\"江湖令\\\",\\\"COLLECTION_TICKET_CAPABILITY_ABROAD\\\",\\\"江湖令海外-门票\\\",\\\"TASK_TICKET_WHITELIST\\\",\\\"江湖令门票白名单申请\\\"],[\\\"PRODUCT\\\",\\\"商品\\\",\\\"TASK_COST_CORRECTION\\\",\\\"费用包含纠错\\\",\\\"TASK_POI_SEASON_IMG\\\",\\\"POI时令封图\\\"],[\\\"DISTRIBUTION\\\",\\\"分销\\\",\\\"COLLECTION_VENDOR_CONFIRM\\\",\\\"系统对接\\\",\\\"TASK_ACT_NOTICE\\\",\\\"营销活动通知\\\"],[\\\"SALEUNIT_SHELF\\\",\\\"票种&货架\\\",\\\"TASK_ORDER_BATCH_TRIP\\\",\\\"订单批量处理-trip对客/分销商\\\",\\\"TASK_STOCKUP_APPLY_OVERSEA\\\",\\\"管控白名单修改-海外\\\"],[\\\"ORDER\\\",\\\"订单\\\",\\\"COLLECTION_STOCKUP\\\",\\\"票种管控\\\",\\\"TASK_PEOPLE_GROUP\\\",\\\"人群申请\\\"],[\\\"POI\\\",\\\"POI\\\",\\\"COLLECTION_MKT_BUYOUT_STOCKTICKET\\\",\\\"买断囤票\\\",\\\"TASK_ACT_PRODUCT_APPLY\\\",\\\"商品提报\\\"]]\",\"groupList\":[\"biz_category_code\",\"biz_category_name\",\"task_collection_code\",\"task_collection_name\",\"task_type_code\",\"task_type_name\"],\"metricList\":[]}"
//        client.getRawData(_) >> MapperUtil.str2Obj(str, GetRawDataResponseType.class)
//
//        and: "mock remoteConfig"
//        remoteConfig.getExternalConfig("regionalManager") >> "{\"S04850\":\"KA大区|62402\"}"
//        remoteConfig.getExternalConfig("specailRegionOperate") >> "wsong"
//        remoteConfig.getConfigValue("domestic") >> "国内"
//        remoteConfig.getConfigValue("three") >> "三方"
//        remoteConfig.getConfigValue("region") >> "大区"
//        remoteConfig.getConfigValue("province") >> "省份"
//        remoteConfig.getConfigValue("viewspot") >> "景点"
//        remoteConfig.getExternalConfig("domesticDeptId") >> "SO001057"
//        remoteConfig.getExternalConfig("overseaDeptId") >> "SO003287"
//        remoteConfig.getExternalConfig("regionRankId") >> "62402|62355|62337|SO002322|62390|SO001665"
//
//        when: "调用接口"
//        def response = taskBiz.getTaskLevelDim(request, empCode)
//
//        then: "验证返回结果"
//        with(response) {
//            taskDimInfoList.size() == 7
//            taskDimInfoList.get(0).code == "DISTRIBUTION"
//            taskDimInfoList.get(0).value == "分销"
//            taskDimInfoList.get(0).type == "biz_category_code"
//            taskDimInfoList.get(0).childrenList.size() == 1
//            taskDimInfoList.get(0).childrenList.get(0).getCode() == "COLLECTION_VENDOR_CONFIRM"
//            taskDimInfoList.get(6).code == "BOARD"
//        }
//
//        where: "用表格方式验证多种返回结果"
//        d            | empCode  | employeeInfoDaoQueryByEmpCode       | querySpecificPeriodAllMetricConfig       | getLeaderEmployeeInfo       | queryByDomainName       | queryByLeaderEmpCode
//        "2023-12-12" | "D00024" | D00024EmployeeInfoDaoQueryByEmpCode | D00024QuerySpecificPeriodAllMetricConfig | D00024GetLeaderEmployeeInfo | D00024QueryByDomainName | D00024QueryByLeaderEmpCode
//        "2023-12-12" | "S52754" | S52754EmployeeInfoDaoQueryByEmpCode | S52754QuerySpecificPeriodAllMetricConfig | S52754GetLeaderEmployeeInfo | S52754QueryByDomainName | S52754QueryByLeaderEmpCode
//        "2023-12-12" | "S08033" | S08033EmployeeInfoDaoQueryByEmpCode | S08033QuerySpecificPeriodAllMetricConfig | S08033GetLeaderEmployeeInfo | S08033QueryByDomainName | S08033QueryByLeaderEmpCode
//        "2023-12-12" | "S04850" | S04850EmployeeInfoDaoQueryByEmpCode | S04850QuerySpecificPeriodAllMetricConfig | S04850GetLeaderEmployeeInfo | S04850QueryByDomainName | S04850QueryByLeaderEmpCode
//    }

//    def "GetTaskFlowTableData"() {
//        given: "构造入参"
//        def r = String.format("{\"dateType\":\"current_quarter\",\"taskDimInfoList\":[{\"code\":\"JIANGHULING\",\"value\":\"\",\"type\":\"biz_category_code\"},{\"code\":\"BOARD\",\"value\":\"\",\"type\":\"biz_category_code\"},{\"code\":\"TASK_TICKET_WHITELIST\",\"value\":\"\",\"type\":\"task_type_code\"}],\"drillDownType\":\"%s\",\"subordinateType\":\"%s\",\"drilldownMetric\":\"average_process_time\",\"pageNo\":1,\"pageSize\":10}", drillDownType, subordinateType)
//        def request = MapperUtil.str2Obj(r, GetTaskFlowTableDataRequestType.class)
//        def empCode = "D00024"
//
//        and: "内部调用mock"
//        dataUpdateBiz.getUpdateTime() >> d
//
//
//        and: "mock employeeInfoDao"
//        employeeInfoDao.queryByEmpCode("D00024") >> MapperUtil.str2Obj("{\"id\":452,\"empCode\":\"D00024\",\"displayName\":\"Zhe Li （李哲）\",\"domainName\":\"zheli\",\"leaderEmpCode\":\"S35097\",\"leaderEmpName\":\"JIM Ji （季毅华）\",\"teamId\":\"41999\",\"teamCname\":\"门票活动业务部\",\"orgIdPath\":\"Ctrip_CO0001 Ctrip_Board 29 SO002916 25481 41999\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部\",\"position\":\"\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:38:56 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:38:56 AM\"}", BusinessDashboardEmployeeInfo.class)
//        employeeInfoDao.queryByDomainName("zheli") >>  MapperUtil.str2Obj("{\"id\":452,\"empCode\":\"D00024\",\"displayName\":\"Zhe Li （李哲）\",\"domainName\":\"zheli\",\"leaderEmpCode\":\"S35097\",\"leaderEmpName\":\"JIM Ji （季毅华）\",\"teamId\":\"41999\",\"teamCname\":\"门票活动业务部\",\"orgIdPath\":\"Ctrip_CO0001 Ctrip_Board 29 SO002916 25481 41999\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部\",\"position\":\"\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:38:56 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:38:56 AM\"}", BusinessDashboardEmployeeInfo.class)
//        employeeInfoDao.getDirectSubordinateEmpCodeList("zheli") >> MapperUtil.str2List("[\"D01981\"]", String.class)
//        employeeInfoDao.queryByEmpCode("D01981") >> MapperUtil.str2Obj("{\"id\":65,\"empCode\":\"D01981\",\"displayName\":\"Yue Wu （邬阅）\",\"domainName\":\"yue.wu\",\"leaderEmpCode\":\"D00024\",\"leaderEmpName\":\"Zhe Li （李哲）\",\"teamId\":\"62390\",\"teamCname\":\"华西大区\",\"orgIdPath\":\"Ctrip_CO0001 Ctrip_Board 29 SO002916 25481 41999 SO001057 62390\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>门票活动国内业务部>华西大区\",\"position\":\"\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:38:56 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:38:56 AM\"}", BusinessDashboardEmployeeInfo.class)
//        employeeInfoDao.queryByDomainName("yue.wu") >> MapperUtil.str2Obj("{\"id\":65,\"empCode\":\"D01981\",\"displayName\":\"Yue Wu （邬阅）\",\"domainName\":\"yue.wu\",\"leaderEmpCode\":\"D00024\",\"leaderEmpName\":\"Zhe Li （李哲）\",\"teamId\":\"62390\",\"teamCname\":\"华西大区\",\"orgIdPath\":\"Ctrip_CO0001 Ctrip_Board 29 SO002916 25481 41999 SO001057 62390\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>门票活动国内业务部>华西大区\",\"position\":\"\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:38:56 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:38:56 AM\"}", BusinessDashboardEmployeeInfo.class)
//        employeeInfoDao.getAllSubordinateDomainNameList(_) >> MapperUtil.str2List("[\"li.xy\",\"sipeiliu\",\"zx_liu\",\"c.wang29\",\"ytwen\",\"y.leng\",\"yayuantang\",\"tting_wu\",\"krtan\",\"yelin.liu\",\"yq_huang\",\"zxu5\",\"jl_wei\"]", String.class)
//        employeeInfoDao.queryByEmpCodeList(_) >> MapperUtil.str2List("[{\"id\":317,\"empCode\":\"D01981\",\"displayName\":\"Wei Song （宋薇）\",\"domainName\":\"wsong\",\"leaderEmpCode\":\"D00024\",\"leaderEmpName\":\"Zhe Li （李哲）\",\"teamId\":\"62390\",\"teamCname\":\"华西大区\",\"orgIdPath\":\"Ctrip_CO0001 Ctrip_Board 29 SO002916 25481 41999 SO001057 62390\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>门票活动国内业务部>华西大区\",\"position\":\"\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:38:56 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:38:56 AM\"},{\"id\":373,\"empCode\":\"D02018\",\"displayName\":\"Erica Zhao （赵蓓蕾）\",\"domainName\":\"blzhao\",\"leaderEmpCode\":\"D00024\",\"leaderEmpName\":\"Zhe Li （李哲）\",\"teamId\":\"62390\",\"teamCname\":\"华西大区\",\"orgIdPath\":\"Ctrip_CO0001 Ctrip_Board 29 SO002916 25481 41999 SO001057 62390\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>门票活动国内业务部>华西大区\",\"position\":\"\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:38:56 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:38:56 AM\"},{\"id\":469,\"empCode\":\"S61016\",\"displayName\":\"Min Song （宋旻）\",\"domainName\":\"m_song\",\"leaderEmpCode\":\"D00024\",\"leaderEmpName\":\"Zhe Li （李哲）\",\"teamId\":\"SO001057\",\"teamCname\":\"门票活动国内业务部\",\"orgIdPath\":\"Ctrip_CO0001 Ctrip_Board 29 SO002916 25481 41999 SO001057\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>门票活动国内业务部\",\"position\":\"\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:38:56 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:38:56 AM\"}]", BusinessDashboardEmployeeInfo.class)
//        employeeInfoDao.queryByEmpCode("S04850") >> MapperUtil.str2Obj("{\"id\":122,\"empCode\":\"S04850\",\"displayName\":\"Hang Yin （殷航）\",\"domainName\":\"hyin\",\"leaderEmpCode\":\"D00024\",\"leaderEmpName\":\"Zhe Li （李哲）\",\"teamId\":\"SO001057\",\"teamCname\":\"门票活动国内业务部\",\"orgIdPath\":\"Ctrip_CO0001 Ctrip_Board 29 SO002916 25481 41999 SO001057\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>门票活动国内业务部\",\"position\":\"\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:38:56 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:38:56 AM\"}", BusinessDashboardEmployeeInfo.class)
//        employeeInfoDao.queryByDomainName("hyin") >> MapperUtil.str2Obj("{\"id\":122,\"empCode\":\"S04850\",\"displayName\":\"Hang Yin （殷航）\",\"domainName\":\"hyin\",\"leaderEmpCode\":\"D00024\",\"leaderEmpName\":\"Zhe Li （李哲）\",\"teamId\":\"SO001057\",\"teamCname\":\"门票活动国内业务部\",\"orgIdPath\":\"Ctrip_CO0001 Ctrip_Board 29 SO002916 25481 41999 SO001057\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>门票活动国内业务部\",\"position\":\"\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:38:56 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:38:56 AM\"}", BusinessDashboardEmployeeInfo.class)
//
//        and: "mock examineConfigV2Dao"
//        examineConfigV2Dao.querySpecificPeriodAllMetricConfig("zheli", d, "2023", "Q4", null) >> MapperUtil.str2List("[{\"id\":7057086,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2023\",\"quarter\":\"Q4\",\"examineMetric\":\"1;2\",\"examineType\":15,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"国内\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2023-12-18\",\"datachangeLasttime\":\"Dec 18, 2023 1:19:55 AM\",\"odtExamineLevel\":\"国内\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"国内\",\"overseaOdtExamineRange\":\"\"},{\"id\":7057093,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2023\",\"quarter\":\"Q4\",\"examineMetric\":\"3\",\"examineType\":0,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2023-12-18\",\"datachangeLasttime\":\"Dec 18, 2023 1:19:55 AM\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"},{\"id\":7057100,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2023\",\"quarter\":\"Q4\",\"examineMetric\":\"4\",\"examineType\":0,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2023-12-18\",\"datachangeLasttime\":\"Dec 18, 2023 1:19:55 AM\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"},{\"id\":7057107,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2023\",\"quarter\":\"Q4\",\"examineMetric\":\"5;6;7\",\"examineType\":0,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2023-12-18\",\"datachangeLasttime\":\"Dec 18, 2023 1:19:55 AM\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"},{\"id\":7057114,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2023\",\"quarter\":\"Q4\",\"examineMetric\":\"9\",\"examineType\":0,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2023-12-18\",\"datachangeLasttime\":\"Dec 18, 2023 1:19:55 AM\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"},{\"id\":7057121,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2023\",\"quarter\":\"Q4\",\"examineMetric\":\"10\",\"examineType\":0,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2023-12-18\",\"datachangeLasttime\":\"Dec 18, 2023 1:19:55 AM\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"},{\"id\":7057128,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2023\",\"quarter\":\"Q4\",\"examineMetric\":\"11\",\"examineType\":0,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2023-12-18\",\"datachangeLasttime\":\"Dec 18, 2023 1:19:55 AM\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"}]", BusinessDashboardExamineeConfigV2.class)
////        examineConfigV2Dao.querySpecificPeriodAllMetricConfig("hyin", d, "2023", "Q4", null) >> MapperUtil.str2List("", BusinessDashboardExamineeConfigV2.class)
//
//        and: "mock organizationInfoDao"
//        organizationInfoDao.queryByOrgId("SO001057") >> MapperUtil.str2Obj("{\"id\":132,\"nodeOrgId\":\"SO001057\",\"nodeOrgType\":\"Team\",\"nodeOrgName\":\"门票活动国内业务部\",\"nodeOrgLevel\":\"7\",\"parentOrgId\":\"41999\",\"buId\":\"25481\",\"deptId\":\"41999\",\"orgIdPath\":\"Ctrip_CO0001.Ctrip_Board.29.SO002916.25481.41999.SO001057\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>门票活动国内业务部\",\"leaderEmpCode\":\"D00024\",\"leaderEmpName\":\"Zhe Li （李哲）\",\"empCnt\":\"314\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:26:08 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:26:08 AM\"}", BusinessDashboardOrganizationInfo.class)
//        organizationInfoDao.queryByLeaderEmpCode("D00024") >> MapperUtil.str2List("[{\"id\":132,\"nodeOrgId\":\"SO001057\",\"nodeOrgType\":\"Team\",\"nodeOrgName\":\"门票活动国内业务部\",\"nodeOrgLevel\":\"7\",\"parentOrgId\":\"41999\",\"buId\":\"25481\",\"deptId\":\"41999\",\"orgIdPath\":\"Ctrip_CO0001.Ctrip_Board.29.SO002916.25481.41999.SO001057\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>门票活动国内业务部\",\"leaderEmpCode\":\"D00024\",\"leaderEmpName\":\"Zhe Li （李哲）\",\"empCnt\":\"314\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:26:08 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:26:08 AM\"},{\"id\":133,\"nodeOrgId\":\"SO002566\",\"nodeOrgType\":\"Team\",\"nodeOrgName\":\"国内业务三方供应中心\",\"nodeOrgLevel\":\"8\",\"parentOrgId\":\"SO001057\",\"buId\":\"25481\",\"deptId\":\"41999\",\"orgIdPath\":\"Ctrip_CO0001.Ctrip_Board.29.SO002916.25481.41999.SO001057.SO002566\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>门票活动国内业务部>国内业务三方供应中心\",\"leaderEmpCode\":\"D00024\",\"leaderEmpName\":\"Zhe Li （李哲）\",\"empCnt\":\"\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:26:08 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:26:08 AM\"},{\"id\":134,\"nodeOrgId\":\"62390\",\"nodeOrgType\":\"Team\",\"nodeOrgName\":\"华西大区\",\"nodeOrgLevel\":\"8\",\"parentOrgId\":\"SO001057\",\"buId\":\"25481\",\"deptId\":\"41999\",\"orgIdPath\":\"Ctrip_CO0001.Ctrip_Board.29.SO002916.25481.41999.SO001057.62390\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>门票活动国内业务部>华西大区\",\"leaderEmpCode\":\"D00024\",\"leaderEmpName\":\"Zhe Li （李哲）\",\"empCnt\":\"61\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:26:08 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:26:08 AM\"},{\"id\":135,\"nodeOrgId\":\"SO003279\",\"nodeOrgType\":\"Team\",\"nodeOrgName\":\"海外供应链运营组\",\"nodeOrgLevel\":\"9\",\"parentOrgId\":\"SO002566\",\"buId\":\"25481\",\"deptId\":\"41999\",\"orgIdPath\":\"Ctrip_CO0001.Ctrip_Board.29.SO002916.25481.41999.SO001057.SO002566.SO003279\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>门票活动国内业务部>国内业务三方供应中心>海外供应链运营组\",\"leaderEmpCode\":\"D00024\",\"leaderEmpName\":\"Zhe Li （李哲）\",\"empCnt\":\"\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:26:08 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:26:08 AM\"}]", BusinessDashboardOrganizationInfo.class)
//        organizationInfoDao.queryByLeaderEmpCode("D01981") >> MapperUtil.str2List("[{\"id\":35,\"nodeOrgId\":\"62393\",\"nodeOrgType\":\"Team\",\"nodeOrgName\":\"重庆\",\"nodeOrgLevel\":\"10\",\"parentOrgId\":\"62391\",\"buId\":\"25481\",\"deptId\":\"41999\",\"orgIdPath\":\"Ctrip_CO0001.Ctrip_Board.29.SO002916.25481.41999.SO001057.62390.62391.62393\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>门票活动国内业务部>华西大区>西南区域>重庆\",\"leaderEmpCode\":\"D01981\",\"leaderEmpName\":\"Yue Wu （邬阅）\",\"empCnt\":\"\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:26:08 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:26:08 AM\"},{\"id\":36,\"nodeOrgId\":\"62394\",\"nodeOrgType\":\"Team\",\"nodeOrgName\":\"重庆TTD\",\"nodeOrgLevel\":\"10\",\"parentOrgId\":\"62391\",\"buId\":\"25481\",\"deptId\":\"41999\",\"orgIdPath\":\"Ctrip_CO0001.Ctrip_Board.29.SO002916.25481.41999.SO001057.62390.62391.62394\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>门票活动国内业务部>华西大区>西南区域>重庆TTD\",\"leaderEmpCode\":\"D01981\",\"leaderEmpName\":\"Yue Wu （邬阅）\",\"empCnt\":\"\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:26:08 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:26:08 AM\"},{\"id\":37,\"nodeOrgId\":\"62391\",\"nodeOrgType\":\"Team\",\"nodeOrgName\":\"西南区域\",\"nodeOrgLevel\":\"9\",\"parentOrgId\":\"62390\",\"buId\":\"25481\",\"deptId\":\"41999\",\"orgIdPath\":\"Ctrip_CO0001.Ctrip_Board.29.SO002916.25481.41999.SO001057.62390.62391\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>门票活动国内业务部>华西大区>西南区域\",\"leaderEmpCode\":\"D01981\",\"leaderEmpName\":\"Yue Wu （邬阅）\",\"empCnt\":\"13\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:26:08 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:26:08 AM\"}]", BusinessDashboardOrganizationInfo.class)
//        organizationInfoDao.queryByLeaderEmpCode("S04850") >> MapperUtil.str2List("[{\"id\":25,\"nodeOrgId\":\"62402\",\"nodeOrgType\":\"Team\",\"nodeOrgName\":\"KA大区\",\"nodeOrgLevel\":\"8\",\"parentOrgId\":\"SO001057\",\"buId\":\"25481\",\"deptId\":\"41999\",\"orgIdPath\":\"Ctrip_CO0001.Ctrip_Board.29.SO002916.25481.41999.SO001057.62402\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>门票活动国内业务部>KA大区\",\"leaderEmpCode\":\"S04850\",\"leaderEmpName\":\"Hang Yin （殷航）\",\"empCnt\":\"1\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:26:08 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:26:08 AM\"}]", BusinessDashboardOrganizationInfo.class)
//
//
//
//        and: "mock remoteConfig"
//        remoteConfig.getExternalConfig("regionalManager") >> "{\"S04850\":\"KA大区|62402\"}"
//        remoteConfig.getExternalConfig("specailRegionOperate") >> "wsong"
//        remoteConfig.getConfigValue("domestic") >> "国内"
//        remoteConfig.getConfigValue("three") >> "三方"
//        remoteConfig.getConfigValue("region") >> "大区"
//        remoteConfig.getConfigValue("province") >> "省份"
//        remoteConfig.getConfigValue("viewspot") >> "景点"
//        remoteConfig.getExternalConfig("domesticDeptId") >> "SO001057"
//        remoteConfig.getExternalConfig("overseaDeptId") >> "SO003287"
//        remoteConfig.getExternalConfig("regionRankId") >> "62402|62355|62337|SO002322|62390|SO001665"
//
//
//        and: "mock client"
//        def str = "{\"result\":\"[[\\\"华西大区\\\",\\\"62390\\\",0.5714,1.4762,41,4.25]]\",\"groupList\":[\"region_name\",\"region_id\"],\"metricList\":[\"complete_rate\",\"ontime_complete_rate\",\"overtime_event_cnt\",\"average_process_time\"],\"totalNum\":1}"
//        def str1 = "{\"result\":\"[[0.6491,1.3509,97,3.162162]]\",\"metricList\":[\"complete_rate\",\"ontime_complete_rate\",\"overtime_event_cnt\",\"average_process_time\"]}"
//        def str2 = "{\"result\":\"[[\\\"jiang_jy\\\",\\\"Jiaye Jiang （姜嘉晔）\\\",\\\"管理中心\\\",\\\"SO002565\\\",0.6552,1.3448,49,3.105263],[\\\"tncai\\\",\\\"Tianning Cai （蔡天宁）\\\",\\\"管理中心\\\",\\\"SO002565\\\",0.6429,1.3571,48,3.222222],[\\\"blzhao\\\",\\\"Erica Zhao （赵蓓蕾）\\\",\\\"华西大区\\\",\\\"62390\\\",0.5714,1.4762,41,4.25],[\\\"zheli\\\",\\\"Zhe Li (李哲)\\\",\\\"其他\\\",\\\"其他\\\",0.5000,1.5000,4,5.0]]\",\"groupList\":[\"domain_name\",\"display_name\",\"region_name\",\"region_id\"],\"metricList\":[\"complete_rate\",\"ontime_complete_rate\",\"overtime_event_cnt\",\"average_process_time\"],\"totalNum\":4}"
//
//        client.getRawData({ i -> i.andMap.region_id != null }) >> MapperUtil.str2Obj(str, GetRawDataResponseType.class)
//        client.getRawData({ i -> i.andMap.domain_name != null}) >> MapperUtil.str2Obj(str1, GetRawDataResponseType.class)
//        client.getRawData({i->i.orderList != null && i.groupList != null}) >> MapperUtil.str2Obj(str2, GetRawDataResponseType.class)
//        client.getRawData({ i -> i.queryId == 58}) >> MapperUtil.str2Obj(str1, GetRawDataResponseType.class)
//
//        and: "mock taskFlowSyncJob.getEumnDataTypeMap"
//        taskFlowSyncJob.getEumnDataTypeMap() >> MapperUtil.str2Obj("{\"biz_category_code\":{\"BOARD\":\"业绩看板\",\"ORDER\":\"订单\",\"QUALITY\":\"质量\",\"MARKET\":\"营销\",\"DISTRIBUTION\":\"分销\",\"PRODUCT\":\"商品\",\"POI\":\"POI\",\"SALEUNIT_SHELF\":\"票种&货架\",\"JIANGHULING\":\"江湖令\"},\"task_collection_code\":{\"COLLECTION_PRODUCT_CAPABILITY\":\"江湖令国内-门票\",\"COLLECTION_MKT_BUYOUT_VOUCHER\":\"批量凭证审核\",\"COLLECTION_TICKET_CAPABILITY_ABROAD\":\"江湖令海外-门票\",\"COLLECTION_DAYTOUR_CAPABILITY_OVERSEA\":\"江湖令出境-日游\",\"COLLECTION_BIZ_BOARD\":\"经营业绩看板任务\",\"COLLECTION_POI_AUDIT_OVERSEA\":\"POI（海外）\",\"COLLECTION_LOWER_COMMISSION\":\"低佣商品审核\",\"COLLECTION_SALE_UNIT_INFO_OVERSEA\":\"票种信息申请（海外）\",\"COLLECTION_POI_AUDIT\":\"POI（国内）\",\"COLLECTION_VENDOR_CONFIRM\":\"系统对接\",\"COLLECTION_ORDER_BATCH\":\"订单批量处理\",\"COLLECTION_DAYTOUR_CAPABILITY\":\"江湖令国内-日游\",\"COLLECTION_MKT_ACTIVITY\":\"营销活动\",\"COLLECTION_MKT_BUYOUT_STOCKTICKET\":\"买断囤票\",\"COLLECTION_ORDER_NOTICE\":\"订单通知\",\"COLLECTION_PRODUCT_NOTICE\":\"商品系统通知\",\"COLLECTION_TNT_DISTRIBUTION\":\"分销申请\",\"COLLECTION_TICKET_CAPABILITY_OVERSEA\":\"江湖令出境-门票\",\"COLLECTION_STOCKUP\":\"票种管控\",\"COLLECTION_IBU_TRANS\":\"翻译项目\",\"COLLECTION_SALE_UNIT_INFO\":\"票种信息申请\",\"COLLECTION_PRICE_CHANGED\":\"价格变更\",\"COLLECTION_STOCKUP_OVERSEA\":\"票种管控（海外）\"},\"task_type_code\":{\"TASK_SALE_UNIT_ACTIVITY\":\"活动票种申请\",\"TASK_POI_UPDATE_INTERN\":\"修改POI信息（用户）\",\"TASK_PRODUCT_OFFLINE_NOTICE\":\"商品下线通知\",\"TASK_POI_UPDATE\":\"修改POI信息\",\"TASK_BIZ_TARGET\":\"业务目标调整\",\"TASK_POI_CREATE_OVERSEA\":\"新增POI信息（海外）\",\"TASK_COST_CORRECTION\":\"费用包含纠错\",\"TASK_ORDER_BATCH_CTRIP_DISTRIBUTOR\":\"订单批量处理-ctrip对分销商\",\"TASK_TICKET_SALEUNIT_UNMATCHED_OVERSEA\":\"票种未匹配（出境）\",\"TASK_ACT_NOTICE\":\"营销活动通知\",\"TASK_TNT_DIS_PRICE_RULE\":\"分销-改价/专享\",\"TASK_STOCKUP_EXPIRY_NOTICE_OVERSEA\":\"上货管控到期提醒-海外\",\"TASK_ORDER_BATCH_CTRIP_PASSENGER\":\"订单批量处理-ctrip对客\",\"TASK_SALE_PROPERTY\":\"销售属性申请\",\"TASK_WATER_CARD\":\"直签/水牌覆盖指标组织目标变更\",\"TASK_TNT_DIS_PRICE_PROTECTED\":\"分销-价格保护\",\"TASK_VENDOR_CONFIRM_05\":\"系统对接-资源当日首次对接失败\",\"TASK_STOCK_WARNING_NOTICE\":\"库存预警通知\",\"TASK_VENDOR_CONFIRM_04\":\"系统对接-关键词未匹配超阈值预警\",\"TASK_TICKET_PRODUCT_WEAKNESS\":\"江湖令商品力劣势\",\"TASK_MKT_BUYOUT_STOCKTICKET_NOTICE\":\"买断囤票通知\",\"TASK_LINES_TREASURE_COVERAGE_OVERSEA\":\"线路未覆盖（出境藏宝图）\",\"TASK_SALE_UNIT_OVERSEA\":\"票种申请（海外）\",\"TASK_STOCKUP_PROTECTED_CLOSE\":\"上货保护关闭申请\",\"TASK_ORDER_BATCH_TRIP\":\"订单批量处理-trip对客/分销商\",\"TASK_SALE_UNIT_OTHER_OVERSEA\":\"修改票种信息（海外）\",\"TASK_TNT_DIS_RESOURCE_REBATE\":\"分销-开放平台调用\",\"TASK_PRODUCT_OFFLINE_NOTICE_OVERSEA\":\"商品下线通知-海外业务\",\"TASK_PRODUCT_SALEUNIT_UNMATCHED\":\"票种未匹配\",\"TASK_FLIGGY_PRODUCT_WEAKNESS_OVERSEA\":\"江湖令-商品覆盖缺失（海外飞猪）\",\"TASK_DAYTOUR_WHITELIST\":\"白名单申请\",\"TASK_PEOPLE_LIMIT_OVERSEA\":\"人群修改申请（海外）\",\"TASK_VENDOR_CONFIRM_03\":\"系统对接-对接失败通知\",\"TASK_VENDOR_CONFIRM_02\":\"系统对接-对接失败超阈值预警\",\"TASK_TNT_DIS_POI_UPD\":\"分销商挂接信息修改\",\"TASK_VENDOR_CONFIRM_01\":\"系统对接熔断\",\"TASK_TNT_DIS_SALE_RULE\":\"开关分销\",\"TASK_STOCKUP_PROTECTED_APPLY\":\"上货保护申请\",\"TASK_ORDER_NOTICE\":\"订单系统通知\",\"TASK_STOCKUP_EXPIRY_NOTICE\":\"上货管控到期提醒\",\"TASK_PRODUCT_SCENICSPOT_WEAKNESS\":\"景点劣势\",\"TASK_PRODUCT_SCENICSPOT_UNMATCHED\":\"景点未匹配\",\"TASK_POI_UPDATE_OVERSEA\":\"修改POI信息（海外）\",\"TASK_FLIGGY_SALEUNIT_UNMATCHED_OVERSEA\":\"江湖令-票种未匹配（海外飞猪）\",\"TASK_POI_SEASON_IMG\":\"POI时令封图\",\"TASK_PRODUCT_COVERAGE\":\"商品覆盖\",\"TASK_ACT_PRODUCT_FINAL_AUDIT\":\"活动商品终审\",\"TASK_PRICE_CHANGED\":\"对接价格变更\",\"TASK_TICKET_COVERAGE_OVERSEA\":\"商品覆盖缺失（出境）\",\"TASK_PEOPLE_GROUP_OVERSEA\":\"人群申请（海外）\",\"TASK_FLIGGY_SCENICSPOT_WEAKNESS_OVERSEA\":\"江湖令-景点劣势（海外飞猪）\",\"TASK_POI_CREATE\":\"新增POI信息\",\"TASK_LINES_COVERAGE\":\"线路覆盖劣势\",\"TASK_ACT_PRODUCT_PRE_AUDIT\":\"活动商品初审\",\"TASK_LINES_REFINE\":\"线路提炼有误\",\"TASK_STOCKUP_PROTECTED_CLOSE_OVERSEA\":\"上货保护关闭申请-海外\",\"TASK_TICKET_SALEUNIT_IMPROVE_OVERSEA\":\"江湖令-票种信息完善（出境）\",\"TASK_ACT_PRODUCT_APPLY\":\"商品提报\",\"TASK_STOCKUP_PROTECTED_APPLY_OVERSEA\":\"上货保护申请-海外\",\"TASK_SALE_PROPERTY_OVERSEA\":\"销售属性申请（海外）\",\"TASK_SALE_PRICE\":\"门市价申请\",\"TASK_IBU_TRANS\":\"门票活动翻译申请\",\"TASK_PEOPLE_GROUP\":\"人群申请\",\"TASK_LOWER_COMMISSION\":\"低佣商品审核\",\"TASK_FLIGGY_SCENICSPOT_UNMATCHED_OVERSEA\":\"江湖令-景点未匹配（海外飞猪）\",\"TASK_POI_SEASON_IMG_NOTICE\":\"POI时令封图通知\",\"TASK_STOCK_OUT_NOTICE\":\"库存售罄通知\",\"TASK_STOCKUP_APPLY_OVERSEA\":\"管控白名单修改-海外\",\"TASK_PEOPLE_LIMIT\":\"人群修改申请\",\"TASK_SALE_PRICE_OVERSEA\":\"门市价申请（海外）\",\"TASK_STOCKUP_APPLY\":\"管控白名单修改申请\",\"TASK_SALE_UNIT_OTHER\":\"修改票种信息\",\"TASK_TICKET_SCENICSPOT_UNMATCHED_OVERSEA\":\"景点未匹配（出境）\",\"TASK_TICKET_SCENICSPOT_WEAKNESS_OVERSEA\":\"景点劣势（出境）\",\"TASK_TICKET_PRODUCT_WEAKNESS_OVERSEA\":\"江湖令商品力劣势（出境）\",\"TASK_TICKET_WHITELIST\":\"江湖令门票白名单申请\",\"TASK_TICKET_WHITELIST_OVERSEA\":\"江湖令门票白名单申请（出境）\",\"TASK_TICKET_TREASURE_COVERAGE_OVERSEA\":\"商品覆盖缺失（出境藏宝图）\",\"TASK_SALE_UNIT\":\"票种申请\",\"TASK_COST_CORRECTION_OVERSEA\":\"费用包含纠错（海外）\",\"TASK_PRODUCT_NOTICE\":\"商品系统通知\",\"TASK_MKT_BUYOUT_VOUCHER\":\"买断下载凭证\"}}", Map.class)
//
//
//        when: "调用接口"
//        def response = taskBiz.getTaskFlowTableData(request, empCode)
//
//        then: "验证返回结果"
//        with(response) {
//
//        }
//
//        where: "表格形式验证多种情况"
//        drillDownType   |   subordinateType
//        "region"        |   "directorSubordinate"
//        "subordinate"   |   "directorSubordinate"
//        "subordinate"   |   "allSubordinate"
//        "task"          |   "allSubordinate"
//    }
}
