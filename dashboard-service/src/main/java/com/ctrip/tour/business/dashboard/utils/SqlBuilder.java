package com.ctrip.tour.business.dashboard.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @Date 2024/11/14
 */

public class SqlBuilder {

    public static final String PRIMARY_ID = "id";
    public static final String ORDER_ASC = "asc";

    private StringBuilder sql;
    private List<Object> params;
    private int pageSize;
    private int currentPage;
    private boolean isDistinct;
    private String selectClause;
    private String fromClause;
    private String whereClause;
    private String joinClause;
    private String groupByClause;
    private String havingClause;
    private String orderByClause;
    private boolean hasLimit;
    private boolean hasOffset;
    private boolean isCount;
    private boolean isCountDistinct;

    public SqlBuilder() {
        this.sql = new StringBuilder();
        this.params = new ArrayList<>();
        this.selectClause = "";
        this.fromClause = "";
        this.whereClause = "";
        this.joinClause = "";
        this.groupByClause = "";
        this.havingClause = "";
        this.orderByClause = "";
        this.hasLimit = false;
        this.hasOffset = false;
        this.isCount = false;
        this.isCountDistinct = false;
    }

    // 设置每页的记录数
    public SqlBuilder setPageSize(int pageSize) {
        this.pageSize = pageSize;
        this.hasLimit = true;
        return this;
    }

    // 设置当前页码
    public SqlBuilder setCurrentPage(int currentPage) {
        this.currentPage = currentPage;
        this.hasOffset = true;
        return this;
    }

    // 设置是否去重
    public SqlBuilder distinct() {
        this.isDistinct = true;
        return this;
    }

    // 添加 SELECT 子句
    public SqlBuilder select(String columns) {
        this.selectClause = "SELECT " + (isDistinct ? "DISTINCT " : "") + columns + " ";
        return this;
    }

    // 添加 COUNT 子句
    public SqlBuilder count(String column, boolean distinct) {
        this.selectClause = "SELECT COUNT(" + (distinct ? "DISTINCT " : "") + column + ") AS count ";
        this.isCount = true;
        this.isCountDistinct = distinct;
        return this;
    }

    // 添加 FROM 子句
    public SqlBuilder from(String table) {
        this.fromClause = "FROM " + table + " ";
        return this;
    }

    // 添加 LEFT JOIN 子句
    public SqlBuilder leftJoin(String table, Condition condition) {
        this.joinClause += "LEFT JOIN " + table + " ON " + condition.toSql() + " ";
        params.addAll(condition.getParams());
        return this;
    }

    public SqlBuilder innerJoin(String table, Condition condition) {
        this.joinClause += "INNER JOIN " + table + " ON " + condition.toSql() + " ";
        params.addAll(condition.getParams());
        return this;
    }


    // 添加 WHERE 子句
    public SqlBuilder where(Condition condition) {
        this.whereClause = "WHERE " + condition.toSql() + " ";
        params.addAll(condition.getParams());
        return this;
    }

    public SqlBuilder whereWithCondition(Condition condition) {

        if (Objects.isNull(condition)) {
            return this;
        }
        if (this.whereClause.isEmpty()) {
            this.whereClause = "WHERE " + condition.getSqlWithParams() + " ";
        } else {
            this.whereClause += "AND " + condition.getSqlWithParams() + " ";
        }
        params.addAll(condition.getParams());
        return this;
    }

    // 添加 AND 子句
    public SqlBuilder and(Condition condition) {
        if (Objects.isNull(condition)) {
            return this;
        }
        if (this.whereClause.isEmpty()) {
            this.whereClause = "WHERE " + condition.toSql() + " ";
        } else {
            this.whereClause += "AND " + condition.toSql() + " ";
        }
        params.addAll(condition.getParams());
        return this;
    }

    public SqlBuilder and(String subSql) {
        if (this.whereClause.isEmpty()) {
            this.whereClause = "WHERE " + subSql + " ";
        } else {
            this.whereClause += "AND " + subSql + " ";
        }
        return this;
    }

    // 添加 AND 子句
    public SqlBuilder and(Condition condition, Supplier<Boolean> supplier) {
        if (supplier.get()) {
            if (this.whereClause.isEmpty()) {
                this.whereClause = "WHERE " + condition.toSql() + " ";
            } else {
                this.whereClause += "AND " + condition.toSql() + " ";
            }
            params.addAll(condition.getParams());
        }
        return this;
    }

    // 添加 GROUP BY 子句
    public SqlBuilder groupBy(String columns) {
        if (StringUtils.isNotBlank(columns)) {
            this.groupByClause = "GROUP BY " + columns + " ";
        }
        return this;
    }

    // 添加 HAVING 子句
    public SqlBuilder having(Condition condition) {
        this.havingClause = "HAVING " + condition.toSql() + " ";
        params.addAll(condition.getParams());
        return this;
    }

    // 添加 ORDER BY 子句
    public SqlBuilder orderBy(String column, String order) {
        this.orderByClause = "ORDER BY " + column + " " + order + " ";
        return this;
    }

    // 添加 LIMIT 子句
    public SqlBuilder limit(int limit) {
        this.pageSize = limit;
        this.hasLimit = true;
        return this;
    }

    // 获取最终的 SQL 语句
    public String getSql() {
        sql.setLength(0);

        if (!selectClause.isEmpty()) {
            sql.append(selectClause);
        }

        if (!fromClause.isEmpty()) {
            sql.append(fromClause);
        }

        if (!joinClause.isEmpty()) {
            sql.append(joinClause);
        }

        if (!whereClause.isEmpty()) {
            sql.append(whereClause);
        }

        if (!groupByClause.isEmpty()) {
            sql.append(groupByClause);
        }

        if (!havingClause.isEmpty()) {
            sql.append(havingClause);
        }

        if (!orderByClause.isEmpty()) {
            sql.append(orderByClause);
        }

        if (hasLimit && !isCount) {
            sql.append("LIMIT ").append(pageSize);
            if (hasOffset) {
                int offset = (currentPage - 1) * pageSize;
                sql.append(" OFFSET ").append(offset);
            }
        }

        return sql.toString();
    }

    // 获取最终的参数列表
    public List<Object> getParams() {
        return params;
    }

    // 清空当前构建的 SQL 语句
    public void clear() {
        sql.setLength(0);
        params.clear();
        selectClause = "";
        fromClause = "";
        whereClause = "";
        joinClause = "";
        groupByClause = "";
        havingClause = "";
        orderByClause = "";
        isDistinct = false;
        hasLimit = false;
        hasOffset = false;
        isCount = false;
        isCountDistinct = false;
    }

    // 条件类
    public static class Condition {

        private StringBuilder sql;
        private List<Object> params;

        public Condition() {
            this.sql = new StringBuilder();
            this.params = new ArrayList<>();
        }

        public Condition notEquals(String column, Object value) {
            sql.append(column).append(" != ? ");
            params.add(value);
            return this;
        }

        public Condition gt(String column, Object value) {
            sql.append(column).append(" > ? ");
            params.add(value);
            return this;
        }

        public Condition gte(String column, Object value) {
            sql.append(column).append(" >= ? ");
            params.add(value);
            return this;
        }

        public Condition lt(String column, Object value) {
            sql.append(column).append(" < ? ");
            params.add(value);
            return this;
        }

        public Condition lte(String column, Object value) {
            sql.append(column).append(" <= ? ");
            params.add(value);
            return this;
        }


        public Condition eq(String column, Object value) {
            sql.append(column).append(" = ? ");
            params.add(value);
            return this;
        }

        public Condition in(String column, List<?> values) {
            sql.append(column).append(" IN (");
            for (int i = 0; i < values.size(); i++) {
                if (i > 0) {
                    sql.append(", ");
                }
                sql.append("?");
            }
            sql.append(") ");

            if (values.get(0) instanceof String) {
                values = values.stream().map(v -> "'" + (String)v + "'").collect(Collectors.toList());
            }

            params.addAll(values);
            return this;
        }

        public Condition notIn(String column, List<?> values) {
            sql.append(column).append(" NOT IN (");
            for (int i = 0; i < values.size(); i++) {
                if (i > 0) {
                    sql.append(", ");
                }
                sql.append("?");
            }
            sql.append(") ");
            params.addAll(values);
            return this;
        }

        public Condition like(String column, String pattern) {
            sql.append(column).append(" LIKE ? ");
            params.add(pattern);
            return this;
        }

        public Condition notLike(String column, String pattern) {
            sql.append(column).append(" NOT LIKE ? ");
            params.add(pattern);
            return this;
        }

        public Condition between(String column, Object start, Object end) {
            sql.append(column).append(" BETWEEN ? AND ? ");
            params.add(start);
            params.add(end);
            return this;
        }

        public Condition and(Condition condition) {

            if (Objects.nonNull(condition)) {
                if (StringUtils.isNotBlank(sql.toString())) {
                    sql.append("AND ");
                }
                sql.append(condition.toSql());
                params.addAll(condition.getParams());
            }
            return this;

        }


        public Condition and(Condition condition, Supplier<Boolean> supplier) {
            if (supplier.get()) {
                if (Objects.nonNull(condition)) {
                    if (StringUtils.isNotBlank(sql.toString())) {
                        sql.append("AND ");
                    }
                    sql.append(condition.toSql());
                    params.addAll(condition.getParams());
                }
            }
            return this;
        }

        public Condition and(String subSql) {
                sql.append("AND ").append(subSql);
            return this;
        }

        public Condition or(Condition condition, Supplier<Boolean> supplier) {
            if (supplier.get()) {
                sql.append("OR ").append(condition.toSql());
                params.addAll(condition.getParams());
            }
            return this;
        }

        public Condition or(Condition condition) {
            if (Objects.nonNull(condition)) {
                if (StringUtils.isNotBlank(sql.toString())) {
                    sql.append("OR ");
                }
                sql.append(condition.toSql());
                params.addAll(condition.getParams());
            }
            return this;
        }

        public Condition exists(SqlBuilder subQuery) {
            sql.append("EXISTS (").append(subQuery.getSql()).append(")");
            params.addAll(subQuery.getParams());
            return this;
        }

        public String toSql() {
            return sql.toString();
        }


        public String getSqlWithParams() {
            String sqlStr = sql.toString();
            for (Object param : params) {
                sqlStr = sqlStr.replaceFirst("\\?", String.valueOf(param));
            }
            return sqlStr;
        }

        public List<Object> getParams() {
            return params;
        }
    }


}
