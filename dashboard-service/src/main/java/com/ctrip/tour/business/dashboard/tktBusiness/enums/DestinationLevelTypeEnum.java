package com.ctrip.tour.business.dashboard.tktBusiness.enums;

public enum DestinationLevelTypeEnum {
    OVERSEA("海外"),//NOSONAR
    REGION("大区"),//NOSONAR
    SUB_REGION("子区域");//NOSONAR

    private String name;

    DestinationLevelTypeEnum(String name) {
        this.name = name;
    }


    public String getName() {
        return name;
    }

    public static DestinationLevelTypeEnum getByCode(String code) {
        for (DestinationLevelTypeEnum buType : DestinationLevelTypeEnum.values()) {
            if (buType.getName().equals(code)) {
                return buType;
            }
        }
        return null;
    }
}
