package com.ctrip.tour.business.dashboard.tktBusiness.configuration;


import com.ctrip.sysdev.herald.tokenlib.Token;
import com.ctrip.tour.business.dashboard.utils.DownloadUtil;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

@Configuration
public class KmsConfiguration {

    @Autowired
    private RemoteConfig remoteConfig;

    /*public LoadingCache<String, String> cache = Caffeine.newBuilder()
            .expireAfterWrite(1, TimeUnit.MINUTES)
            .build(new CacheLoader<String, String>() {
                @Override
                @Nullable
                public String load(@NonNull String code) {
                    String url = remoteConfig.getConfigValue("hufuUrl");

                    String tokenString = Token.getTokenString();

                    String key = code + "Secret";

                    String path = url + "/query-pwd?token=" + remoteConfig.getConfigValue(key) + "&appid=100038120&herald-token=" + tokenString + "&type=JSON";
                    String data = DownloadUtil.get(path);
                    if (data != null) {
                        JSONObject result = JSONObject.fromObject(data);
                        JSONObject info = result.getJSONObject("result");
                        if (info != null && info.getString("pwdValue") != null) {
                            return info.getString("pwdValue");
                        }
                    }
                    return "";
                }
            });*/

    public String getKmsValue(String code) {
        String url = remoteConfig.getConfigValue("hufuUrl");
        String tokenString = Token.getTokenString();
        String key = code + "Secret";
        String path = url + "/query-pwd?token=" + remoteConfig.getConfigValue(key) + "&appid=100038120&herald-token=" + tokenString + "&type=JSON";
        String data = DownloadUtil.get(path);
        if (data != null) {
            JSONObject result = JSONObject.fromObject(data);
            JSONObject info = result.getJSONObject("result");
            if (info != null && info.getString("pwdValue") != null) {
                return info.getString("pwdValue");
            }
        }
        return "";
    }


}
