package com.ctrip.tour.business.dashboard.utils

import com.ctrip.soa._24922.DrillDownFilter
import spock.lang.Specification
import spock.lang.Unroll

class ParamterCheckUtilTest extends Specification{

    @Unroll
    def "checkDrillDownFilter"(){
        expect: "call & verify"
        result == ParamterCheckUtil.checkDrillDownFilter(filter)

        where: "多条件验证"
        result  |   filter
        false   |   null
        true | new DrillDownFilter(field: "大区")

    }
}
