package com.ctrip.tour.business.dashboard.utils.time;

import lombok.AllArgsConstructor;
import lombok.Data;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 周计算工具类，用于处理周相关的日期计算、转换和比较
 */
@Data
@AllArgsConstructor
public class WeekCalculator implements Cloneable, Comparable<WeekCalculator> {
    private Integer year;
    private Integer weekNum;
    private static final DateTimeFormatter YYYY_MM_DD_FORMATTER = DateTimeFormat.forPattern("yyyy-MM-dd");

    /**
     * 构造当前周的WeekCalculator实例
     */
    public WeekCalculator() {
        DateTime now = DateTime.now();
        year = now.getWeekyear();
        weekNum = now.getWeekOfWeekyear();
    }

    /**
     * 根据周字符串创建WeekCalculator实例
     * 周字符串格式支持"yyyyWww"或"yyyywWw"（W不区分大小写）
     *
     * @param weekString 周字符串，如"2023W01"或"2023w01"
     * @return WeekCalculator实例
     */
    public static WeekCalculator getByWeekString(String weekString) {
        WeekCalculator weekCalculator = new WeekCalculator();
        weekCalculator.setWeekString(weekString);
        return weekCalculator;
    }

    /**
     * 根据日期字符串创建WeekCalculator实例
     * 日期字符串格式为"yyyy-MM-dd"
     *
     * @param date 日期字符串，如"2023-12-31"
     * @return WeekCalculator实例
     */
    public static WeekCalculator getByDate(String date) {
        WeekCalculator weekCalculator = new WeekCalculator();
        DateTime dateTime = YYYY_MM_DD_FORMATTER.parseDateTime(date);
        weekCalculator.setYear(dateTime.getWeekyear());
        weekCalculator.setWeekNum(dateTime.getWeekOfWeekyear());
        return weekCalculator;
    }

    /**
     * 获取当前周的WeekCalculator实例
     *
     * @return 当前周的WeekCalculator实例
     */
    public static WeekCalculator getCurrentWeek() {
        String date = DateTime.now().toString(YYYY_MM_DD_FORMATTER);
        return getByDate(date);
    }

    /**
     * 获取两个周之间的所有周列表
     *
     * @param start        开始周
     * @param end          结束周
     * @param includeStart 是否包含开始周
     * @param includeEnd   是否包含结束周
     * @return 周列表（按时间升序排列）
     */
    public static List<WeekCalculator> getBetween(WeekCalculator start, WeekCalculator end, boolean includeStart, boolean includeEnd) {
        List<WeekCalculator> result = new ArrayList<>();

        while (end.compareTo(start) >= 0) {
            result.add(end);
            end = end.getLastWeek();
        }

        Collections.reverse(result);

        if (!includeStart) {
            result.remove(0);
        }
        if (!includeEnd && result.size() > 0) {
            result.remove(result.size() - 1);
        }
        return result;
    }

    /**
     * 获取周字符串（格式：yyyyWww）
     * 周数不足10时补零，如"2023W01"
     *
     * @return 周字符串，若年或周数为null则返回null
     */
    public String getWeekString() {
        if (year != null && weekNum != null) {
            if (weekNum < 10) {
                return year + "W0" + weekNum;
            }
            return year + "W" + weekNum;
        } else {
            return null;
        }
    }

    /**
     * 获取带连字符的周字符串（格式：yyyy-Www）
     * 周数不足10时补零，如"2023-W01"
     *
     * @return 带连字符的周字符串，若年或周数为null则返回null
     */
    public String getWeekStringWithDash() {
        if (year != null && weekNum != null) {
            if (weekNum < 10) {
                return year + "-W0" + weekNum;
            }
            return year + "-W" + weekNum;
        } else {
            return null;
        }
    }

    /**
     * 设置周字符串，支持格式"yyyyWww"或"yyyywWw"（W不区分大小写）
     *
     * @param weekString 周字符串，如"2023W01"或"2023w01"
     * @throws IllegalArgumentException 若周字符串格式不合法或周数不在1-53范围内
     */
    public void setWeekString(String weekString) {
        try {
            String[] qs = null;
            if (weekString.contains("W")) {
                qs = weekString.split("W");
            } else if (weekString.contains("w")) {
                qs = weekString.split("w");
            } else {
                throw new IllegalArgumentException("Invalid week parameter!");
            }


            this.year = Integer.parseInt(qs[0]);
            this.weekNum = Integer.parseInt(qs[1]);
            Assert.isTrue(weekNum > 0 && weekNum <= 53, "Invalid week parameter!"+weekString);
        } catch (Exception e) {
            throw new IllegalArgumentException("Incorrect parameter:" + weekString, e); // 不正确的参数: → Incorrect parameter:
        }
    }

    /**
     * 获取当前周的起始日期（周一），格式为"yyyy-MM-dd"
     *
     * @return 周起始日期字符串
     */
    public String getDateStart() {
        DateTime dateTime = YYYY_MM_DD_FORMATTER.parseDateTime(year + "-02-01").withWeekOfWeekyear(weekNum);
        return dateTime.withDayOfWeek(1).toString(YYYY_MM_DD_FORMATTER);
    }

    /**
     * 获取当前周的结束日期（周日），格式为"yyyy-MM-dd"
     *
     * @return 周结束日期字符串
     */
    public String getDateEnd() {
        DateTime dateTime = YYYY_MM_DD_FORMATTER.parseDateTime(year + "-02-01").withWeekOfWeekyear(weekNum);
        return dateTime.withDayOfWeek(7).toString(YYYY_MM_DD_FORMATTER);
    }

    /**
     * 获取当前周的结束日期（若结束日期在未来则返回今天），格式为"yyyy-MM-dd"
     *
     * @return 周结束日期（不超过今天）字符串
     */
    public String getDateEndWithoutFuture() {
        String dateEnd = getDateEnd();
        DateTime now = DateTime.now();
        if (YYYY_MM_DD_FORMATTER.parseDateTime(dateEnd).isAfter(now)) {
            return now.toString(YYYY_MM_DD_FORMATTER);
        } else {
            return dateEnd;
        }
    }

    /**
     * 根据对比周获取同期结束日期（若对比周结束日期在过去则按同期天数计算当前周结束日期）
     *
     * @param needCompareWeek 对比周
     * @return 调整后的结束日期字符串，格式为"yyyy-MM-dd"
     */
    public String getDateEndWithSamePeriod(WeekCalculator needCompareWeek) {
        DateTime now = DateTime.now();
        String dateEnd = needCompareWeek.getDateEnd();
        if (YYYY_MM_DD_FORMATTER.parseDateTime(dateEnd).isBefore(now)) {
            String dateStart = needCompareWeek.getDateStart();
            int days = Days.daysBetween(YYYY_MM_DD_FORMATTER.parseDateTime(dateStart),
                    YYYY_MM_DD_FORMATTER.parseDateTime(dateEnd)).getDays();
            return YYYY_MM_DD_FORMATTER.parseDateTime(getDateStart()).plusDays(days).toString(YYYY_MM_DD_FORMATTER);
        } else {
            return getDateEnd();
        }
    }

    /**
     * 获取包含当前周在内的最近N周列表（按时间升序排列）
     *
     * @param lastWeekCount 需要获取的周数
     * @return 最近N周的WeekCalculator列表
     */
    public List<WeekCalculator> getLastWeekIncludeThisByCount(int lastWeekCount) {
        List<WeekCalculator> weekCalculators = new ArrayList<>(lastWeekCount);
        weekCalculators.add(this);
        WeekCalculator weekCalculator = this;
        for (int i = 0; i < lastWeekCount - 1; i++) {
            weekCalculator = weekCalculator.getLastWeek();
            weekCalculators.add(weekCalculator);
        }
        Collections.reverse(weekCalculators);
        return weekCalculators;
    }

    /**
     * 获取上一周的WeekCalculator实例
     *
     * @return 上一周的WeekCalculator实例
     */
    public WeekCalculator getLastWeek() {
        WeekCalculator clone = this.clone();
        return WeekCalculator.getByDate(YYYY_MM_DD_FORMATTER.parseDateTime(clone.getDateStart())
                .minusWeeks(1).toString(YYYY_MM_DD_FORMATTER));
    }

    /**
     * 获取下一周的WeekCalculator实例
     *
     * @return 下一周的WeekCalculator实例
     */
    public WeekCalculator getNextWeek() {
        WeekCalculator clone = this.clone();
        return WeekCalculator.getByDate(YYYY_MM_DD_FORMATTER.parseDateTime(clone.getDateStart())
                .plusWeeks(1).toString(YYYY_MM_DD_FORMATTER));
    }

    /**
     * 获取去年同期周的WeekCalculator实例（若去年周数超过52则调整为52周）
     *
     * @return 去年同期周的WeekCalculator实例
     */
    public WeekCalculator getLastYearSameWeek() {
        WeekCalculator clone = this.clone();
        clone.setYear(clone.getYear() - 1);
        if (clone.getWeekNum() > 52) {
            clone.setWeekNum(52);
        }
        return clone;
    }

    /**
     * 克隆当前WeekCalculator实例
     *
     * @return 克隆后的WeekCalculator实例
     */
    @Override
    protected WeekCalculator clone() {
        return new WeekCalculator(year, weekNum);
    }

    /**
     * 比较两个WeekCalculator实例，先比较年份，年份相同则比较周数
     *
     * @param o 待比较的WeekCalculator实例
     * @return 若当前实例在参数实例之前则返回负数，相同返回0，之后返回正数
     */
    @Override
    public int compareTo(WeekCalculator o) {
        int yearCompare = this.year - o.getYear();
        if (yearCompare == 0) {
            return this.weekNum - o.getWeekNum();
        } else {
            return yearCompare;
        }
    }

    /**
     * 返回周字符串表示（格式：yyyyWww）
     *
     * @return 周字符串
     */
    @Override
    public String toString() {
        return getWeekString();
    }

    /**
     * 判断两个WeekCalculator实例是否相等（年份和周数均相等则认为相等）
     *
     * @param o 待比较的对象
     * @return 若相等则返回true，否则返回false
     */
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof WeekCalculator)) return false;

        WeekCalculator weekCalculator = (WeekCalculator) o;

        if (getYear() != null ? !getYear().equals(weekCalculator.getYear()) : weekCalculator.getYear() != null)
            return false;
        return getWeekNum() != null ? getWeekNum().equals(weekCalculator.getWeekNum()) : weekCalculator.getWeekNum() == null;
    }

    /**
     * 计算哈希码，基于年份和周数
     *
     * @return 哈希码值
     */
    @Override
    public int hashCode() {
        int result = getYear() != null ? getYear().hashCode() : 0;
        result = 31 * result + (getWeekNum() != null ? getWeekNum().hashCode() : 0);
        return result;
    }


    /**
     * 获取年同比日期范围（不考虑时间窗口概念）
     * 直接返回去年同期周的完整日期范围（周一至周日）
     *
     * @return 年同比日期范围
     */
    public DateRange getYoYDateRange() {
        WeekCalculator lastYearSameWeek = getLastYearSameWeek();
        return new DateRange(lastYearSameWeek.getDateStart(), lastYearSameWeek.getDateEnd());
    }

    /**
     * 获取年同比日期范围（考虑时间窗口概念）
     * 根据窗口日期对齐去年同期周的时间窗口，避免天数溢出
     *
     * @param windowDate 窗口日期，格式：yyyy-MM-dd
     * @return 对齐时间窗口的年同比日期范围
     */
    public DateRange getYoYDateRangeWithWindow(String windowDate) {
        WeekCalculator lastYearSameWeek = getLastYearSameWeek();
        DateTime windowDateTime = YYYY_MM_DD_FORMATTER.parseDateTime(windowDate);
        DateTime currentWeekEnd = YYYY_MM_DD_FORMATTER.parseDateTime(getDateEnd());

        // 窗口日期在当前周结束后，返回完整周范围
        if (windowDateTime.isAfter(currentWeekEnd)) {
            return new DateRange(lastYearSameWeek.getDateStart(), lastYearSameWeek.getDateEnd());
        }

        // 计算当前周已过天数（从周一到窗口日期）
        DateTime currentWeekStart = YYYY_MM_DD_FORMATTER.parseDateTime(getDateStart());
        int daysPassed = Days.daysBetween(currentWeekStart, windowDateTime).getDays() + 1;

        // 计算去年同期周的对应日期范围
        DateTime yoyStartDate = YYYY_MM_DD_FORMATTER.parseDateTime(lastYearSameWeek.getDateStart());
        DateTime yoyEndDate = yoyStartDate.plusDays(daysPassed - 1);

        // 处理天数溢出：确保不超过去年同期周的最后一天
        DateTime targetWeekEnd = YYYY_MM_DD_FORMATTER.parseDateTime(lastYearSameWeek.getDateEnd());
        if (yoyEndDate.isAfter(targetWeekEnd)) {
            yoyEndDate = targetWeekEnd;
        }

        return new DateRange(lastYearSameWeek.getDateStart(), yoyEndDate.toString(YYYY_MM_DD_FORMATTER));
    }

    /**
     * 获取周环比日期范围（不考虑时间窗口概念）
     * 直接返回上一周的完整日期范围（周一至周日）
     *
     * @return 周环比日期范围
     */
    public DateRange getWoWDateRange() {
        WeekCalculator lastWeek = getLastWeek();
        return new DateRange(lastWeek.getDateStart(), lastWeek.getDateEnd());
    }

    /**
     * 获取周环比日期范围（考虑时间窗口概念）
     * 根据窗口日期对齐上一周的时间窗口，避免天数溢出
     *
     * @param windowDate 窗口日期，格式：yyyy-MM-dd
     * @return 对齐时间窗口的周环比日期范围
     */
    public DateRange getWoWDateRangeWithWindow(String windowDate) {
        WeekCalculator lastWeek = getLastWeek();
        DateTime windowDateTime = YYYY_MM_DD_FORMATTER.parseDateTime(windowDate);
        DateTime currentWeekEnd = YYYY_MM_DD_FORMATTER.parseDateTime(getDateEnd());

        // 窗口日期在当前周结束后，返回完整上一周范围
        if (windowDateTime.isAfter(currentWeekEnd)) {
            return new DateRange(lastWeek.getDateStart(), lastWeek.getDateEnd());
        }

        // 计算当前周已过天数（从周一到窗口日期）
        DateTime currentWeekStart = YYYY_MM_DD_FORMATTER.parseDateTime(getDateStart());
        int daysPassed = Days.daysBetween(currentWeekStart, windowDateTime).getDays() + 1;

        // 计算上一周的对应日期范围
        DateTime wowStartDate = YYYY_MM_DD_FORMATTER.parseDateTime(lastWeek.getDateStart());
        DateTime wowEndDate = wowStartDate.plusDays(daysPassed - 1);

        // 处理天数溢出：确保不超过上一周的最后一天
        DateTime targetWeekEnd = YYYY_MM_DD_FORMATTER.parseDateTime(lastWeek.getDateEnd());
        if (wowEndDate.isAfter(targetWeekEnd)) {
            wowEndDate = targetWeekEnd;
        }

        return new DateRange(lastWeek.getDateStart(), wowEndDate.toString(YYYY_MM_DD_FORMATTER));
    }
}