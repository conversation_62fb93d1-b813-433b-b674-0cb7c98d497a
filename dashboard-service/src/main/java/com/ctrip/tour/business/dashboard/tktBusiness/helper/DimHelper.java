package com.ctrip.tour.business.dashboard.tktBusiness.helper;

import com.ctrip.tour.business.dashboard.utils.GeneralUtil;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/8/5
 */
public class DimHelper {


    public static Double getSpecialDimValue(String dim,
                                            String preffix,
                                            Map<String, Double> dimMap,
                                            Map<String, Double> popDimMap) {
        Double result = null;
        if("ttd_weighted_defect_achieved_rate".equals(dim)){
            Double rate = dimMap.get("ttd_weighted_defect_rate");
            Double target = dimMap.get("ttd_weighted_defect_target");
            if (GeneralUtil.isValidDivide(rate, target)) {
                result = 1 - (dimMap.get("ttd_weighted_defect_rate") - dimMap.get("ttd_weighted_defect_target")) / dimMap.get("ttd_weighted_defect_target");
            }
        }
        if ("ttd_qa_cost_rate_gap".equals(dim)) {
            Double fenzi = dimMap.get(preffix + "ttd_qa_cost_rate");
            Double fenmu = dimMap.get(preffix + "ttd_trgt_qa_cost_rate");
            if (GeneralUtil.isValidDivide(fenzi, fenmu)) {
                result = fenzi / fenmu - 1;
            }
        }
        if (dim.endsWith("_lastyear") || dim.endsWith("_lasthalf")) {
            String actualDim = dim.substring(0, dim.length() - 9);
            Double currentValue = dimMap.get(preffix + actualDim);
            Double popValue = popDimMap.get(preffix + actualDim);
            dimMap.put("ly_ttd_weighted_defect_cnt", popDimMap.get("ttd_weighted_defect_cnt"));
            dimMap.put("ly_ttd_pay_odr_cnt", popDimMap.get("ttd_pay_odr_cnt"));
            if (GeneralUtil.isValidDivide(currentValue, popValue)) {
                result = currentValue / popValue - 1;
            }
        }
        if (dim.endsWith("_2019")) {
            String actualDim = dim.substring(0, dim.length() - 5);
            Double currentValue = dimMap.get(preffix + actualDim);
            Double popValue = popDimMap.get(preffix + actualDim);
            if (GeneralUtil.isValidDivide(currentValue, popValue)) {
                result = currentValue / popValue - 1;
            }
        }
        if (dim.endsWith("_7days")) {
            String actualDim = dim.substring(0, dim.length() - 6);
            Double currentValue = dimMap.get(preffix + actualDim);
            Double popValue = popDimMap.get(preffix + actualDim);
            if (GeneralUtil.isValidDivide(currentValue, popValue)) {
                result = currentValue / popValue - 1;
            }
        }
        if (dim.endsWith("_30days")) {
            String actualDim = dim.substring(0, dim.length() - 7);
            Double currentValue = dimMap.get(preffix + actualDim);
            Double popValue = popDimMap.get(preffix + actualDim);
            if (GeneralUtil.isValidDivide(currentValue, popValue)) {
                result = currentValue / popValue - 1;
            }
        }
        if (dim.endsWith("_lastquarter")) {
            String actualDim = dim.substring(0, dim.length() - 12);
            Double currentValue = dimMap.get(preffix + actualDim);
            Double popValue = popDimMap.get(preffix + actualDim);
            if (GeneralUtil.isValidDivide(currentValue, popValue)) {
                result = currentValue / popValue - 1;
            }
        }
        if (dim.equals("ttd_weight1_sign_score|ttd_trgt_weight_sign|/")) {
            result = dimMap.get(preffix + dim);
        }
        if (dim.equals("ttd_suc_income_complete_rate")) {
            Double fenzi = dimMap.get(preffix + "ttd_suc_income");
            Double fenmu = dimMap.get(preffix + "ttd_trgt_income");
            if (GeneralUtil.isValidDivide(fenzi, fenmu)) {
                result = fenzi / fenmu;
            }
        }
        if (dim.equals("ttd_suc_subsidy_rebate_profit_complete_rate")) {
            Double fenzi = dimMap.get(preffix + "ttd_suc_subsidy_rebate_profit");
            Double fenmu = dimMap.get(preffix + "ttd_trgt_profit");
            if (GeneralUtil.isValidDivide(fenzi, fenmu)) {
                result = fenzi / fenmu;
            }
        }
        return result;
    }


    public static Boolean isSpecialDim(String dim) {
        return dim.endsWith("_gap") || dim.equals("ttd_weight1_sign_score|ttd_trgt_weight_sign|/") || dim.equals("ttd_suc_income_complete_rate") || dim.equals("ttd_suc_subsidy_rebate_profit_complete_rate");
    }
}
