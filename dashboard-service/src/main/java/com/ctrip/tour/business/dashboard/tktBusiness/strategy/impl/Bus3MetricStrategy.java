package com.ctrip.tour.business.dashboard.tktBusiness.strategy.impl;

import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ExamineConfigBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.SinglePeriodDataBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.SubMetricPermissonMappingBean;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.SinglePeriodTrendLineBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.UserPermissionBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.Bus3Dao;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardExamineeConfigV2Dao;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardExamineeConfigV2;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.Bus3Helper;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.ChartHelper;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.MetricHelper;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.MultiPeriodMappingHelper;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.MetricCalStrategy;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;


import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/8/5
 */
@Component
public class Bus3MetricStrategy implements MetricCalStrategy {

    @Autowired
    private Bus3Dao dao;

    @Autowired
    private UserPermissionBiz userPermissionBiz;

    @Autowired
    private BusinessDashboardExamineeConfigV2Dao examineeConfigV2Dao;

    @Autowired
    private SinglePeriodTrendLineBiz singlePeriodTrendLineBiz;

    @Autowired
    private RemoteConfig remoteConfig;

    private Logger log = LoggerFactory.getLogger(Bus3MetricStrategy.class);

    @Override
    public Future<MetricDetailInfo> getSingleMetricCardData(String domainName,
                                                            TimeFilter timeFilter,
                                                            MetricInfoBean metricInfoBean,
                                                            String d,
                                                            Boolean needRank) throws Exception {
        // 2024年开始拆分子指标
        MetricDetailInfo metricDetailInfo = new MetricDetailInfo();
        try{
            List<String> subMetricList = new ArrayList<>();
            int y = timeFilter.getYear() != null ? Integer.parseInt(timeFilter.getYear()) : 0;
            if(y >= 2024){
                CheckUserPermissionResponseType checkUserPermissionReq = userPermissionBiz.checkUserPermission(new CheckUserPermissionRequestType(timeFilter, domainName, null, null));
                subMetricList = checkUserPermissionReq.getDomesticBasicConfig()
                        .getMetricCardConfigMap()
                        .get(getMetricName())
                        .getSubMetricList();
            }else{
                subMetricList.add("domestic");
            }

            String metric = metricInfoBean.getMetric();
            metricDetailInfo.setMetric(metric);
            List<MetricDetailInfo> subMetricDetailInfoList = new ArrayList<>();
            metricDetailInfo.setSubMetricDetailInfoList(subMetricDetailInfoList);

            // 2024年拆分国内和出境日游，所以按照子指标的格式返回
            if(subMetricList != null && subMetricList.size() > 0){
                for(String subMetric: subMetricList){
                    MetricDetailInfo subMetricInfo = new MetricDetailInfo();
                    subMetricInfo.setSubMetric(subMetric);
                    Map<String, Double> dimMap = new HashMap<>();
                    subMetricInfo.setDimData(dimMap);
                    String year = timeFilter.getYear();
                    String month = timeFilter.getMonth();
                    String quarter = timeFilter.getQuarter();
                    String dateType = MetricHelper.getDateTypeColumnName(timeFilter.getDateType());
                    List<String> timeList = DateUtil.getTimeList(dateType, month, quarter);

                    String level = getLevel(metricInfoBean, subMetric);
                    List<String> regionList = getRegionList(metricInfoBean, subMetric);

                    DalHints rankDalHints = new DalHints().asyncExecution();
                    if (needRank) {
                        getRankDataAsync(dateType, year, quarter, month, d, domainName, subMetric, rankDalHints);
                    }
                    //由于mysql需要设置索引 因此需要保持顺序
                    Map<String, List<String>> reachInMap = new HashMap<>();
                    reachInMap.put("query_d", Lists.newArrayList(d));
                    reachInMap.put("year", Lists.newArrayList(year));
                    reachInMap.put(dateType, timeList);
                    if (!"".equals(level)) {
                        reachInMap.put(level, regionList);
                    }
                    if(y >= 2024){
                        reachInMap.put("bu_type", Lists.newArrayList(subMetric));
                    }
                    List<List<Object>> reachList = dao.getMetricCardReachData(reachInMap, year);
                    List<String> reachDimList = Bus3Helper.getReachDimList(year);
                    ChartHelper.fillOverallDimMap(reachList, reachDimList, dimMap);


                    String targetQuarter = DateUtil.getQuarter(dateType, timeFilter.getMonth(), timeFilter.getQuarter());
                    Map<String, List<String>> targetInMap = new LinkedHashMap<>();
                    targetInMap.put("year", Lists.newArrayList(year));
                    targetInMap.put("quarter", Lists.newArrayList(targetQuarter));
                    //前置特殊处理  过滤掉2024年的数据
                    // targetInMap.put("business",Lists.newArrayList("0"));
                    if (!"".equals(level)) {
                        targetInMap.put(level, regionList);
                    }
                    if(y >= 2024){
                        targetInMap.put("bu_type", Lists.newArrayList(subMetric));
                    }
                    List<List<Object>> targetList = dao.getMetricCardTargetData(targetInMap, year);
                    List<String> targetDimList = Bus3Helper.getTargetDimList(year);
                    ChartHelper.fillOverallDimMap(targetList, targetDimList, dimMap);
                    doubleFormat(dimMap);
                    Bus3Helper.makeUpMetricCardData(dimMap, year);

                    //获取去年同期数据
                    String lastYear = DateUtil.getLastYear(year);
                    reachInMap.put("year", Lists.newArrayList(lastYear));
                    List<List<Object>> lastYearReachList = dao.getMetricCardReachData(reachInMap, year);
                    Map<String, Double> lastYearDimMap = new HashMap<>();
                    ChartHelper.fillOverallDimMap(lastYearReachList, reachDimList, lastYearDimMap);
                    doubleFormat(lastYearDimMap);
                    Bus3Helper.makeUpMetricCardData(lastYearDimMap, year);
                    Bus3Helper.makeUpMetricCardPopData(year, dimMap, lastYearDimMap, "");

                    // MetricHelper.setMetricCardDrillDownInfo(metricInfoBean, subMetricInfo);
                    setMetricCardDrillDownInfo(subMetric, metricInfoBean, subMetricInfo);
                    //获取排名数据
                    if (needRank) {
                        ChartHelper.fillRankData(subMetricInfo, rankDalHints.getListResult());
                    }
                    subMetricDetailInfoList.add(subMetricInfo);
                }
            }
        }catch (Exception e){
            log.warn("metricCard metric 3 getdata fail ", e);
        }
        return new AsyncResult<>(metricDetailInfo);
    }

    /**
     * 兼容处理：国内和出境设置默认下钻维度的逻辑
     * @param subMetric
     * @param metricInfoBean
     * @param subMetricInfo
     */
    public void setMetricCardDrillDownInfo(String subMetric,
                                           MetricInfoBean metricInfoBean,
                                           MetricDetailInfo subMetricInfo){
        if("domestic".equals(subMetric)){
            MetricHelper.setMetricCardDrillDownInfo(metricInfoBean, subMetricInfo);
        }else if("overseaDayTour".equals(subMetric)){
            MetricHelper.setOverseaOdtMetricCardDrillDownInfo(metricInfoBean, subMetricInfo, remoteConfig);
        }
    }

    /**
     * 对数据进行保留小数位处理
     * @param dimMap
     */
    public void doubleFormat(Map<String, Double> dimMap){
        DecimalFormat df = new DecimalFormat("#.#####");
        df.setRoundingMode(RoundingMode.UP);
        for(String dim: dimMap.keySet()){
            if("ttd_weighted_defect_rate".equals(dim) || "ttd_weighted_defect_target".equals(dim)){
                dimMap.put(dim, Double.parseDouble(df.format(dimMap.get(dim))));
            }
        }
    }

    /**
     * 获取子指标对应的考核范围
     * @param metricInfoBean
     * @param subMetric
     * @return
     */
    public List<String> getRegionList(MetricInfoBean metricInfoBean, String subMetric){
        if("domestic".equals(subMetric)){
            return metricInfoBean.getRegionList();
        }else if("overseaDayTour".equals(subMetric)){
            return metricInfoBean.getOverseaOdtRegionList();
        }
        // 如果没有子指标，返回国内数据，兼容24年之前
        return metricInfoBean.getRegionList();
    }

    /**
     * 获取子指标对应的考核层级
     * @param metricInfoBean
     * @param subMetric：checkxxx接口已经根据examineType判断了考核层级，所以只需要根据这个值获取对应的考核层级和范围即可
     * @return
     */
    public String getLevel(MetricInfoBean metricInfoBean, String subMetric){
        if("domestic".equals(subMetric)){
            return MetricHelper.getLevelColumnName(metricInfoBean.getLevel());
        }else if("overseaDayTour".equals(subMetric)){
            return MetricHelper.getLevelColumnName(metricInfoBean.getOverseaOdtLevel());
        }
        // 如果没有子指标，返回国内数据，兼容24年之前
        return MetricHelper.getLevelColumnName(metricInfoBean.getLevel());
    }


    public String getLevel1(MetricInfoBean metricInfoBean, String subMetric){
        if("domestic".equals(subMetric)){
            return metricInfoBean.getLevel();
        }else if("overseaDayTour".equals(subMetric)){
            return metricInfoBean.getOverseaOdtLevel();
        }
        // 如果没有子指标，返回国内数据，兼容24年之前
        return metricInfoBean.getLevel();
    }


    @Override
    public GetTrendLineDataResponseType getSingleTrendlineData(GetTrendLineDataRequestType request,
                                                               MetricInfoBean metricInfoBean,
                                                               String d) throws Exception {
        String queryType = request.getQueryType();
        if ("trendline".equals(queryType)) {
            return getSingleTrendlineDataWithoutDrillDown(request, d);
        } else {
            return getSingleTrendlineDataWithDrillDown(request, metricInfoBean, d);
        }
    }

    @Override
    public GetTableDataResponseType getSingleTableData(GetTableDataRequestType request,
                                                       MetricInfoBean metricInfoBean,
                                                       String d) throws Exception {
        GetTableDataResponseType response = new GetTableDataResponseType();
        List<TableDataItem> tableDataItemList = new ArrayList<>();
        response.setTableDataItemList(tableDataItemList);

        TimeFilter timeFilter = request.getTimeFilter();
        String year = timeFilter.getYear();
        int y = year != null ? Integer.parseInt(year) : 0;
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();
        String dateType = MetricHelper.getDateTypeColumnName(timeFilter.getDateType());
        List<String> timeList = DateUtil.getTimeList(dateType, month, quarter);
        // String level = MetricHelper.getLevelColumnName(metricInfoBean.getLevel());
        // List<String> regionList = metricInfoBean.getRegionList();
        String level = getLevel(metricInfoBean, request.getSubMetric());
        List<String> regionList = getRegionList(metricInfoBean, request.getSubMetric());
        Integer pageNo = request.getPageNo();
        Integer pageSize = request.getPageSize();
        if (pageSize == null) {
            pageSize = 20;
        }

        DrillDownFilter drillDownFilter = request.getDrillDownFilter();
        String field = MetricHelper.getDrillDownColumnName(drillDownFilter.getField());
        List<String> fieldValueList = drillDownFilter.getFieldValueList();

        List<String> reachGroupTagList = MetricHelper.getTableDrillDownGroupList(field);
        Map<String, List<String>> reachInMap = new LinkedHashMap<>();
        reachInMap.put("query_d", Lists.newArrayList(d));
        reachInMap.put("year", Lists.newArrayList(year));
        reachInMap.put(dateType, timeList);
        if (!"".equals(level)) {
            reachInMap.put(level, regionList);
        }
        if (!GeneralUtil.isEmpty(fieldValueList)) {
            reachInMap.put(field, fieldValueList);
        }
        if(y >= 2024){
            reachInMap.put("bu_type", Lists.newArrayList(request.getSubMetric()));
        }
        List<List<Object>> reachList = dao.getTableReachData(reachInMap, reachGroupTagList, pageNo, pageSize, year);
        Integer totalNum = dao.getTableReachDataCount(reachInMap, reachGroupTagList, year);
        response.setTotalNum(totalNum);


        String targetQuarter = DateUtil.getQuarter(dateType, month, quarter);
        List<String> targetGroupTagList = Lists.newArrayList(field);
        Map<String, List<String>> targetInMap = new LinkedHashMap<>();
        targetInMap.put("year", Lists.newArrayList(year));
        targetInMap.put("quarter", Lists.newArrayList(targetQuarter));
        //前置特殊处理  过滤掉2024年的数据
        // targetInMap.put("business",Lists.newArrayList("0"));
        if (!"".equals(level)) {
            targetInMap.put(level, regionList);
        }
        if (!GeneralUtil.isEmpty(fieldValueList)) {
            targetInMap.put(field, fieldValueList);
        }
        if(y >= 2024){
            targetInMap.put("bu_type", Lists.newArrayList(request.getSubMetric()));
        }


        List<List<Object>> targetList = dao.getSpilitTargetData(targetInMap, targetGroupTagList, year);
        List<String> reachDimList = Bus3Helper.getReachDimList(year);
        List<String> targetDimList = Bus3Helper.getTargetDimList(year);
//        ChartHelper.fillCommmonTableData(tableDataItemList, reachGroupTagList, reachDimList, targetDimList, reachList, targetList);
        ChartHelper.fillCommmonTableDataV2(tableDataItemList, reachGroupTagList, targetGroupTagList,
                reachDimList, targetDimList, reachList, targetList);
        Bus3Helper.makeUpTableData(tableDataItemList, year);

        String source = request.getSource();
        //给首页添加去年同比
        if ("firstpage".equals(source)) {
            //收集下钻字段的值
            List<String> actualFieldList = tableDataItemList.stream()
                    .map(i -> i.getFieldMap().get(field))
                    .collect(Collectors.toList());
            if(!GeneralUtil.isEmpty(actualFieldList)){
                reachInMap.put("year", Lists.newArrayList(DateUtil.getLastYear(year)));
                reachInMap.put(field, actualFieldList);
                List<List<Object>> popReachList = dao.getTableReachData(reachInMap, reachGroupTagList, null, null, year);
//                ChartHelper.fillTableComplexDimPopData(tableDataItemList, popReachList, reachGroupTagList,
//                        Bus3Helper.getReachDimList(), Bus3Helper.getComplexDimList(), "_lastyear");
                ChartHelper.fillTableSingleDimPopData(tableDataItemList, popReachList, reachGroupTagList, "_lastyear", reachDimList, "");
            }
        }
        return response;
    }


    /**
     * 查指标的下钻内容；其实就是查数仓的表对field[根据目标表里的配置拿]进行group
     * @param request
     * @param metricInfoBean
     * @param d
     * @return
     * @throws Exception
     */
    @Override
    public GetDrillDownBaseInfoResponseType getSingleDrillDownBaseInfo(GetDrillDownBaseInfoRequestType request,
                                                                       MetricInfoBean metricInfoBean,
                                                                       String d) throws Exception {
        GetDrillDownBaseInfoResponseType response = new GetDrillDownBaseInfoResponseType();
        List<FieldDataItem> fieldDataItemList = new ArrayList<>();
        response.setFieldDataItemList(fieldDataItemList);

        TimeFilter timeFilter = request.getTimeFilter();
        String year = timeFilter.getYear();
        int y = year != null ? Integer.parseInt(year) : 0;
        String dateType = timeFilter.getDateType();
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();

        String metric = metricInfoBean.getMetric();

        // List<String> regionList = metricInfoBean.getRegionList();
        // String level = metricInfoBean.getLevel();//国内  三方  大区  省份
        List<String> regionList = getRegionList(metricInfoBean, request.getSubMetric());
        String level = getLevel1(metricInfoBean, request.getSubMetric());
        String levelColumnName = MetricHelper.getLevelColumnName(level);

        boolean needSearch = request.isNeedSearch();
        String searchWord = request.getSearchWord();
        List<String> fieldList = Bus3Helper.getFieldList(level);

        if(needSearch){
            String searchField = request.getSearchField();//大区 省份

            if (searchField != null) {
                String searchFieldColumnName = MetricHelper.getDrillDownColumnName(searchField);
                fieldList = Lists.newArrayList(searchFieldColumnName);
            }
        }else{
            response.setDefaultChosenField(MetricHelper.getDefaultChosenField(level, metric));
        }
        String tableName = "bus_3_qacost_gmv_orders_finish_t";
        Map<String, List<String>> inMap = new LinkedHashMap<>();
        inMap.put("query_d", Lists.newArrayList(d));
        inMap.put("year", Lists.newArrayList(year));
        inMap.put(dateType, DateUtil.getTimeList(dateType, month, quarter));
        if (!"".equals(levelColumnName)) {
            inMap.put(levelColumnName, regionList);
        }
        // 拆分子业务线后的筛选条件
        if(StringUtils.isNotBlank(request.getSubMetric()) && y >= 2024){
            inMap.put("bu_type", Lists.newArrayList(request.getSubMetric()));
            tableName = "bus_3_weighted_defect_province_finish_t";
        }
        for (String field : fieldList) {
            List<String> tagList = Lists.newArrayList(field);
            Map<String,String> likeMap = new HashMap<>();
            if(needSearch){
                likeMap.put(field,searchWord);
            }
            List<List<Object>> rawObjectList = dao.getFieldList(tableName, inMap, likeMap, tagList);
            ChartHelper.fillFieldDataItemList(field, rawObjectList, fieldDataItemList);
        }
        return response;
    }

    private void getRankDataAsync(String dateType,
                                  String year,
                                  String quarter,
                                  String month,
                                  String d,
                                  String domainName,
                                  String subMetric,
                                  DalHints dalHints) throws Exception {
        int y = year != null ? Integer.parseInt(year) : 0;
        Map<String, List<String>> inMap = new LinkedHashMap<>();
        inMap.put("query_d", Lists.newArrayList(d));
        inMap.put("examinee", Lists.newArrayList(domainName));
        inMap.put("year", Lists.newArrayList(year));
        if ("month".equals(dateType)) {
            inMap.put("month", Lists.newArrayList(month));
        } else {
            String mappingMonth = DateUtil.getMappingMonthByQuarter(d, year, quarter);
            inMap.put("month", Lists.newArrayList(mappingMonth));
        }
        if(y >= 2024){
            inMap.put("bu_type", Lists.newArrayList(subMetric));
        }
        dao.getRankAsync(inMap, dalHints);
    }


    private GetTrendLineDataResponseType getSingleTrendlineDataWithDrillDown(GetTrendLineDataRequestType request,
                                                                             MetricInfoBean metricInfoBean,
                                                                             String d) throws Exception {
        GetTrendLineDataResponseType response = new GetTrendLineDataResponseType();
        List<TrendLineDetailInfo> trendLineDetailInfoList = new ArrayList<>();
        response.setTrendLineDetailInfoList(trendLineDetailInfoList);

        TimeFilter timeFilter = request.getTimeFilter();
        DrillDownFilter drillDownFilter = request.getDrillDownFilter();
        String field = MetricHelper.getDrillDownColumnName(drillDownFilter.getField());

        String domainName = request.getDomainName();
        List<ExamineConfigBean> examineConfigNoFilterBeanList = MultiPeriodMappingHelper.getMultiPeriodConfigList(timeFilter, d, examineeConfigV2Dao.queryMetricAllConfig(domainName, d, getMetricName()), null);
        // 根据subMetric和examine_type的对应关系过滤符合subMetric的考核数据
        List<ExamineConfigBean> examineConfigBeanList = filterExamineConfigList(examineConfigNoFilterBeanList, request.getSubMetric());

        List<Future<SinglePeriodDataBean>> futureList = new ArrayList<>();
        for (ExamineConfigBean examineConfigBean : examineConfigBeanList) {
            futureList.add(singlePeriodTrendLineBiz.getBus3SinglePeriodTrendLineData(request, examineConfigBean, d));
        }
        List<List<Object>> reachList = new ArrayList<>();
        List<List<Object>> targetList = new ArrayList<>();
        for (Future<SinglePeriodDataBean> futureResult : futureList) {
            SinglePeriodDataBean singlePeriodDataBean = futureResult.get();
            List<List<Object>> periodReachList = singlePeriodDataBean.getPeriodReachList();
            List<List<Object>> periodTargetList = singlePeriodDataBean.getPeriodTargetList();
            if (!GeneralUtil.isEmpty(periodReachList)) {
                reachList.addAll(periodReachList);
            }
            if (!GeneralUtil.isEmpty(periodTargetList)) {
                targetList.addAll(periodTargetList);
            }
        }

        Map<String, Double> dimMap = new HashMap<>();
        Set<String> drillDownSet = new HashSet<>();
        List<String> reachDimList = Bus3Helper.getReachDimList(timeFilter.getYear());
        List<String> targetDimList = Bus3Helper.getTargetDimList(timeFilter.getYear());
        ChartHelper.setMonthlyMetricTrendlineActualReach(dimMap, drillDownSet, reachList, Lists.newArrayList("time", field), reachDimList);
        // 因为出境日游不需要考核到省份 & 目标表没有省份数据，所以下钻时不需要目标数据，否则页面展示有小瑕疵
        if(request.getSubMetric().equals("domestic")){
            ChartHelper.setMonthlyMetricTrendlineActualReach(dimMap, drillDownSet, targetList, Lists.newArrayList("time", field), targetDimList);
        }
        //有效时间范围
        List<String> timeList = DateUtil.getAllSelectedTime(timeFilter, "domestic");
        ChartHelper.fillLineChartTrendLineDataWithDrillDown(null, timeList, dimMap,
                trendLineDetailInfoList, Bus3Helper.getLineChartTrendlineTypeWithDrillDown(timeFilter.getYear()), drillDownSet, false);
        return response;

    }

    private GetTrendLineDataResponseType getSingleTrendlineDataWithoutDrillDown(GetTrendLineDataRequestType request,
                                                                                String d) throws Exception {

        GetTrendLineDataResponseType response = new GetTrendLineDataResponseType();
        List<TrendLineDetailInfo> trendLineDetailInfoList = new ArrayList<>();
        response.setTrendLineDetailInfoList(trendLineDetailInfoList);

        TimeFilter timeFilter = request.getTimeFilter();

        String domainName = request.getDomainName();
        List<ExamineConfigBean> examineConfigNoFilterBeanList = MultiPeriodMappingHelper.getMultiPeriodConfigList(timeFilter, d, examineeConfigV2Dao.queryMetricAllConfig(domainName, d, getMetricName()), null);
        // 根据subMetric和examine_type的对应关系过滤符合subMetric的考核数据
        List<ExamineConfigBean> examineConfigBeanList = filterExamineConfigList(examineConfigNoFilterBeanList, request.getSubMetric());

        List<Future<SinglePeriodDataBean>> futureList = new ArrayList<>();
        for (ExamineConfigBean examineConfigBean : examineConfigBeanList) {
            futureList.add(singlePeriodTrendLineBiz.getBus3SinglePeriodTrendLineData(request, examineConfigBean, d));
        }
        List<List<Object>> reachList = new ArrayList<>();
        List<List<Object>> targetList = new ArrayList<>();
        for (Future<SinglePeriodDataBean> futureResult : futureList) {
            SinglePeriodDataBean singlePeriodDataBean = futureResult.get();
            List<List<Object>> periodReachList = singlePeriodDataBean.getPeriodReachList();
            List<List<Object>> periodTargetList = singlePeriodDataBean.getPeriodTargetList();
            if (!GeneralUtil.isEmpty(periodReachList)) {
                reachList.addAll(periodReachList);
            }
            if (!GeneralUtil.isEmpty(periodTargetList)) {
                targetList.addAll(periodTargetList);
            }
        }


        Map<String, Double> dimMap = new HashMap<>();
        Set<String> drillDownSet = new HashSet<>();
        List<String> reachDimList = Bus3Helper.getReachDimList(timeFilter.getYear());
        List<String> targetDimList = Bus3Helper.getTargetDimList(timeFilter.getYear());
        ChartHelper.setMonthlyMetricTrendlineActualReach(dimMap, drillDownSet, reachList, Lists.newArrayList("time"), reachDimList);
        ChartHelper.setMonthlyMetricTrendlineActualReach(dimMap, drillDownSet, targetList, Lists.newArrayList("time"), targetDimList, false);
        //有效时间范围
        List<String> timeList = DateUtil.getAllSelectedTime(timeFilter, "domestic");

        ChartHelper.fillLineChartTrendLineData(null, timeList, dimMap,
                trendLineDetailInfoList, Bus3Helper.getLineChartTrendlineType(timeFilter.getYear()));
        return response;
    }

    /**
     * 根据subMetric和examine_type的对应关系过滤符合subMetric的考核数据
     * @param examineConfigBeanList
     * @param subMetric
     * @return
     */
    public List<ExamineConfigBean> filterExamineConfigList(List<ExamineConfigBean> examineConfigBeanList, String subMetric){
        List<ExamineConfigBean> examineList = new ArrayList<>();

        List<SubMetricPermissonMappingBean> subMetricPermissonMappingBeanList = remoteConfig.getSubMetricPermissonMapping(getMetricName());
        Map<String, SubMetricPermissonMappingBean> subMetricPermissonMappingBeanMap = subMetricPermissonMappingBeanList.stream().collect(Collectors.toMap(SubMetricPermissonMappingBean::getExamineMetric, // 分组依据
                SubMetricPermissonMappingBean -> SubMetricPermissonMappingBean, // 分组后的元素处理
                (v1, v2) -> v1));

        for (ExamineConfigBean examineConfigBean : examineConfigBeanList) {
            BusinessDashboardExamineeConfigV2 configV2 = examineConfigBean.getBusinessDashboardExamineeConfigV2();
            String examineType = String.valueOf(configV2.getExamineType());
            List<String> subMetricMappingList = subMetricPermissonMappingBeanMap.get(examineType).getSubmetric();
            if(subMetricMappingList.contains(subMetric)){
                examineList.add(examineConfigBean);
            }
        }
        return examineList;
    }



    @Override
    public String getMetricName() {
        return "3";
    }
}
