package com.ctrip.tour.business.dashboard.grpBusiness.job;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;
import qunar.tc.qschedule.config.QSchedule;

/**
 * <AUTHOR>
 * @Date 2025/4/11
 */
@Slf4j
@Service
public class AlertNotifyV2Service {

    @Autowired
    private DiyNpsAlertNotifyService diyNpsAlertNotifyService;
    @Autowired
    private DiyProfitAlertNotifyService diyProfitAlertNotifyService;
    @Autowired
    private FittingNpsV2AlertNotifyService fittingNpsV2AlertNotifyService;
    @Autowired
    private GmvAchieveRateAlertNotifyService gmvAchieveRateAlertNotifyService;
    @Autowired
    private MultiPriceNewAlertNotifyService multiPriceNewAlertNotifyService;
    @Autowired
    private ProfitAchieveRateAlertNotifyService profitAchieveRateAlertNotifyService;
    @Autowired
    private SlfsrvAlertNotifyService slfsrvAlertNotifyService;
    @Autowired
    private DiyCompleteOrdRateAlertNotifyService diyCompleteOrdRateAlertNotifyService;
    @Autowired
    ActiveSelfOprPrdRateAlertNotifyService activeSelfOprPrdRateAlertNotifyService;
    @Autowired
    Pre30dDensityAlertNotifyService pre30dDensityAlertNotifyService;

    @QSchedule("com.ctrip.tour.business.dashboard.grpBusiness.job.actSelfOprAlert")
    public void actSelfOprAlert() {

        try {
            activeSelfOprPrdRateAlertNotifyService.handleMultipriceNotify();
        } catch (Exception e) {
            log.warn("grpNotify error", e);
        }

    }
    @QSchedule("com.ctrip.tour.business.dashboard.grpBusiness.job.pre30DDesityAlert")
    public void pre30DDesityAlert() {

        try {
            pre30dDensityAlertNotifyService.handleMultipriceNotify();
        } catch (Exception e) {
            log.warn("grpNotify error", e);
        }

    }
    @QSchedule("com.ctrip.tour.business.dashboard.grpBusiness.job.diyNpsAlertNotify")
    public void diyNpsAlertNotify() {

        try {
            diyNpsAlertNotifyService.handleSelfSrvCov();
        } catch (Exception e) {
            log.warn("grpNotify error", e);
        }

    }

    @QSchedule("com.ctrip.tour.business.dashboard.grpBusiness.job.diyProfitAlertNotify")
    public void diyProfitAlertNotify() {

        try {
            diyProfitAlertNotifyService.handleSelfSrvCov();
        } catch (Exception e) {
            log.warn("grpNotify error", e);
        }

    }
    @QSchedule("com.ctrip.tour.business.dashboard.grpBusiness.job.fittingNpsV2AlertNotify")
    public void fittingNpsV2AlertNotify() {

        try {
            fittingNpsV2AlertNotifyService.handleMultipriceNotify();
        } catch (Exception e) {
            log.warn("grpNotify error", e);
        }

    }
    @QSchedule("com.ctrip.tour.business.dashboard.grpBusiness.job.gmvAchieveRateAlertNotify")
    public void gmvAchieveRateAlertNotify() {

        try {
            gmvAchieveRateAlertNotifyService.handleMultipriceNotify();
        } catch (Exception e) {
            log.warn("grpNotify error", e);
        }

    }
    @QSchedule("com.ctrip.tour.business.dashboard.grpBusiness.job.multiPriceNewAlertNotify")
    public void multiPriceNewAlertNotify() {

        try {

            multiPriceNewAlertNotifyService.handleMultipriceNotify(">1.1", "价格倍数大于1.1", "价格倍数大于1.1的异常预警，请关注昨日的异常产品。","跟团游");//NOSONAR
            multiPriceNewAlertNotifyService.handleMultipriceNotify("<0.5", "价格倍数小于0.5", "价格倍数小于0.5的异常预警，请关注昨日的异常产品。","跟团游");//NOSONAR
            multiPriceNewAlertNotifyService.handleMultipriceNotify(">1.0", "价格倍数大于1.0", "价格倍数大于1.0的异常预警，请关注昨日的异常产品。","独立出游");//NOSONAR
            multiPriceNewAlertNotifyService.handleMultipriceNotify("<0.6", "价格倍数小于0.6", "价格倍数小于0.6的异常预警，请关注昨日的异常产品。","独立出游");//NOSONAR
        } catch (Exception e) {
            log.warn("grpNotify error", e);
        }

    }
    @QSchedule("com.ctrip.tour.business.dashboard.grpBusiness.job.profitAchieveRateAlertNotify")
    public void profitAchieveRateAlertNotify() {

        try {
            profitAchieveRateAlertNotifyService.handleMultipriceNotify();
        } catch (Exception e) {
            log.warn("grpNotify error", e);
        }

    }
    @QSchedule("com.ctrip.tour.business.dashboard.grpBusiness.job.slfsrvAlertNotify")
    public void slfsrvAlertNotify() {

        try {
            slfsrvAlertNotifyService.handlePmSelfSrv();
        } catch (Exception e) {
            log.warn("grpNotify error", e);
        }

    }
    @QSchedule("com.ctrip.tour.business.dashboard.grpBusiness.job.diyCompleteOrdRateAlertNotify")
    public void diyCompleteOrdRateAlertNotify() {

        try {
            diyCompleteOrdRateAlertNotifyService.handleSelfSrvCov();
        } catch (Exception e) {
            log.warn("grpNotify error", e);
        }

    }


}
