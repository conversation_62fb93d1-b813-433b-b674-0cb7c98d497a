package com.ctrip.tour.business.dashboard.tktBusiness.biz.impl;

import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.soa._24922.DrillDownFilter;
import com.ctrip.soa._24922.GetTrendLineDataRequestType;
import com.ctrip.soa._27181.BIBaseReportQueryServiceClient;
import com.ctrip.soa._27181.GetRawDataRequestType;
import com.ctrip.soa._27181.GetRawDataResponseType;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ExamineConfigBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.SinglePeriodDataBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.SqlParamterBean;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.SinglePeriodTrendLineBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.bo.ExamineConfigBo;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.*;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.*;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ListenableFuture;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.Future;

@Service
public class SinglePeriodTrendLineBizImpl implements SinglePeriodTrendLineBiz {


    @Autowired
    private RemoteConfig remoteConfig;

    @Autowired
    private Bus1Dao bus1Dao;

    @Autowired
    private Bus2Dao bus2Dao;

    @Autowired
    private Bus3Dao bus3Dao;

    @Autowired
    private Bus4Dao bus4Dao;

    @Autowired
    private Bus567Dao bus567Dao;

    @Autowired
    private Bus8Dao bus8Dao;

    @Autowired
    private Bus9NewDao bus9NewDao;

    @Autowired
    private BIBaseReportQueryServiceClient baseReportQueryServiceClient;

    @Autowired
    private SwitchNewTableHelper switchNewTableHelper;
    @Override
    public Future<SinglePeriodDataBean> getBus1SinglePeriodTrendLineData(GetTrendLineDataRequestType request,
                                                                         ExamineConfigBean examineConfig,
                                                                         String d) throws Exception {
        SinglePeriodDataBean bean = new SinglePeriodDataBean();

        ExamineConfigBo bo = new ExamineConfigBo();
        MetricInfoBean metricInfoBean = bo.getMetricInfoBean(examineConfig.getBusinessDashboardExamineeConfigV2(), remoteConfig);

        //单考核国内日游和出境日游的场景
        if(metricInfoBean.getExamineType() == 7 || metricInfoBean.getExamineType() == 8 || metricInfoBean.getExamineType() == 11){
            return new AsyncResult<>(bean);
        }

        String originLevel = metricInfoBean.getLevel();
        String mappingLevel = MetricHelper.getLevelColumnName(originLevel);
        List<String> regionList = metricInfoBean.getRegionList();
        String needUniversalStudios = metricInfoBean.getNeedUniversalStudios();
        String domainName = request.getDomainName();


        String year = examineConfig.getYear();
        String month = examineConfig.getMonth();
        String quarter = examineConfig.getQuarter();
        //该指标为月度指标  直接累加
        String dateType = examineConfig.getDateType();
        String lastDay = DateUtil.getDayOfInterval(d, -1);
        String time = examineConfig.getTimeMap().get("currentTime");

        //获取当期数据
        Map<String, List<String>> inMap = new HashMap<>();
        Map<String, List<String>> notInMap = new HashMap<>();
        Map<String, List<String>> targetInMap = new HashMap<>();
        inMap.put("query_d", Lists.newArrayList(d));
        inMap.put("year", Lists.newArrayList(year));
        targetInMap.put("year", Lists.newArrayList(year));
        if ("month".equals(dateType)) {
            inMap.put("month", Lists.newArrayList(month));
            targetInMap.put("month", Lists.newArrayList(month));
        } else {
            inMap.put("quarter", Lists.newArrayList(quarter));
            targetInMap.put("quarter", Lists.newArrayList(quarter));
        }
        if (!"".equals(mappingLevel)) {
            if ("examinee".equals(mappingLevel)) {
                inMap.put(mappingLevel, Lists.newArrayList(domainName));
                targetInMap.put(mappingLevel, Lists.newArrayList(domainName));
            } else {
                inMap.put(mappingLevel, regionList);
                targetInMap.put(mappingLevel, regionList);
                //如果考核层级是省份并且不需要看环球影城的业绩
                //则把环球影城的业绩直接过滤掉
                if ("province_name".equals(mappingLevel) && "0".equals(needUniversalStudios)) {
                    notInMap.put("region_name", Lists.newArrayList("环球影城"));
                }
            }
        } else {
            //KA大区 业绩双记  对于考核国内的人员添加额外的过滤条件
            String domestic = remoteConfig.getConfigValue("domestic");
            if (domestic.equals(metricInfoBean.getLevel())) {
                String kaRegion = remoteConfig.getConfigValue("kaRegion");
                notInMap.put("region_name", Lists.newArrayList(kaRegion));
            }
        }

        DalHints currentDataHints = new DalHints().asyncExecution();
        //对于单点而言 指标卡==趋势线
        bus1Dao.getMetricCardDataAsync(originLevel, inMap, notInMap, currentDataHints);

        DalHints targetHints = new DalHints().asyncExecution();
        bus1Dao.getOveralltargetDataAsync(originLevel, targetInMap, notInMap, new ArrayList<>(), targetHints);

        if ("景点".equals(originLevel) && !DateUtil.isLastestQuarter(year, month, quarter, dateType, lastDay)) {
            bean.setPeriodReachList(currentDataHints.getListResult(), time);
            bean.setPeriodTargetList(targetHints.getListResult(), time);
        } else {
            //需要计算同比数据
            setBus1TrendlinePopData(examineConfig, inMap, notInMap, bean, dateType, lastDay, originLevel);
            bean.setPeriodReachList(currentDataHints.getListResult(), time);
            bean.setPeriodTargetList(targetHints.getListResult(), time);
        }



        return new AsyncResult<>(bean);
    }



    @Override
    public Future<SinglePeriodDataBean> getBus2SinglePeriodTrendLineData(GetTrendLineDataRequestType request,
                                                                         ExamineConfigBean examineConfig,
                                                                         String d) throws Exception {
        SinglePeriodDataBean bean = new SinglePeriodDataBean();

        ExamineConfigBo bo = new ExamineConfigBo();
        MetricInfoBean metricInfoBean = bo.getMetricInfoBean(examineConfig.getBusinessDashboardExamineeConfigV2(), remoteConfig);

        //单考核国内日游和出境日游的场景
        if(metricInfoBean.getExamineType() == 7 || metricInfoBean.getExamineType() == 8 || metricInfoBean.getExamineType() == 11){
            return new AsyncResult<>(bean);
        }

        String originLevel = metricInfoBean.getLevel();
        String mappingLevel = MetricHelper.getLevelColumnName(originLevel);
        List<String> regionList = metricInfoBean.getRegionList();
        String needUniversalStudios = metricInfoBean.getNeedUniversalStudios();
        String domainName = request.getDomainName();


        String year = examineConfig.getYear();
        String month = examineConfig.getMonth();
        String quarter = examineConfig.getQuarter();
        //该指标为月度指标  直接累加
        String dateType = examineConfig.getDateType();
        String lastDay = DateUtil.getDayOfInterval(d, -1);
        String time = examineConfig.getTimeMap().get("currentTime");

        //获取当期数据
        Map<String, List<String>> inMap = new HashMap<>();
        Map<String, List<String>> notInMap = new HashMap<>();
        Map<String, List<String>> targetInMap = new HashMap<>();
        inMap.put("query_d", Lists.newArrayList(d));
        inMap.put("year", Lists.newArrayList(year));
        targetInMap.put("year", Lists.newArrayList(year));
        if ("month".equals(dateType)) {
            inMap.put("month", Lists.newArrayList(month));
            targetInMap.put("month", Lists.newArrayList(month));
        } else {
            inMap.put("quarter", Lists.newArrayList(quarter));
            targetInMap.put("quarter", Lists.newArrayList(quarter));
        }
        if (!"".equals(mappingLevel)) {
            if ("examinee".equals(mappingLevel)) {
                inMap.put(mappingLevel, Lists.newArrayList(domainName));
                targetInMap.put(mappingLevel, Lists.newArrayList(domainName));
            } else {
                inMap.put(mappingLevel, regionList);
                targetInMap.put(mappingLevel, regionList);
                //如果考核层级是省份并且不需要看环球影城的业绩
                //则把环球影城的业绩直接过滤掉
                if ("province_name".equals(mappingLevel) && "0".equals(needUniversalStudios)) {
                    notInMap.put("region_name", Lists.newArrayList("环球影城"));
                }
            }
        } else {
            //KA大区 业绩双记  对于考核国内的人员添加额外的过滤条件
            String domestic = remoteConfig.getConfigValue("domestic");
            if (domestic.equals(metricInfoBean.getLevel())) {
                String kaRegion = remoteConfig.getConfigValue("kaRegion");
                notInMap.put("region_name", Lists.newArrayList(kaRegion));
            }
        }

        DalHints currentDataHints = new DalHints().asyncExecution();
        //对于单点而言 指标卡==趋势线
        bus2Dao.getMetricCardDataAsync(originLevel, inMap, notInMap, currentDataHints);

        DalHints targetHints = new DalHints().asyncExecution();
        bus2Dao.getOveralltargetDataAsync(originLevel, targetInMap, notInMap, new ArrayList<>(), targetHints);

        if ("景点".equals(originLevel) && !DateUtil.isLastestQuarter(year, month, quarter, dateType, lastDay)) {
            bean.setPeriodReachList(currentDataHints.getListResult(), time);
            bean.setPeriodTargetList(targetHints.getListResult(), time);
        } else {
            //需要计算同比数据
            setBus2TrendlinePopData(examineConfig, inMap, notInMap, bean, dateType, lastDay, originLevel);
            bean.setPeriodReachList(currentDataHints.getListResult(), time);
            bean.setPeriodTargetList(targetHints.getListResult(), time);
        }


        return new AsyncResult<>(bean);
    }

    @Override
    public Future<SinglePeriodDataBean> getBus1And2DayTourPeriodTrendLineData(GetRawDataRequestType currentReq,
                                                                              GetRawDataRequestType targetReq,
                                                                              GetRawDataRequestType lastyearReq,
                                                                              GetRawDataRequestType _2019Req,
                                                                              ExamineConfigBean examineConfig,
                                                                              MetricInfoBean metricInfoBean) throws Exception {
        String time = examineConfig.getTimeMap().get("currentTime");
        SinglePeriodDataBean singlePeriodDataBean = new SinglePeriodDataBean();
        //仅考核门票活动  直接返回
        if (metricInfoBean.getExamineType() <= 3) {
            return new AsyncResult<>(singlePeriodDataBean);
        }


        //获取当期数据
        ListenableFuture<GetRawDataResponseType> currentResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(currentReq);
        //获取目标
        ListenableFuture<GetRawDataResponseType> targetResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(targetReq);
        //获取去年数据
        ListenableFuture<GetRawDataResponseType> lastyearResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(lastyearReq);
        //获取2019年数据
        ListenableFuture<GetRawDataResponseType> _2019ResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(_2019Req);

        GetRawDataResponseType currentRes = currentResFuture.get();
        singlePeriodDataBean.setPeriodReachList(currentRes, time);
        singlePeriodDataBean.setReachHeaderList(currentRes.getMetricList());

        GetRawDataResponseType targetRes = targetResFuture.get();
        singlePeriodDataBean.setPeriodTargetList(targetRes, time);
        singlePeriodDataBean.setTargetHeaderList(targetRes.getMetricList());

        GetRawDataResponseType lastyearRes = lastyearResFuture.get();
        singlePeriodDataBean.setPeriodLastyearList(currentRes, lastyearRes, time);
        singlePeriodDataBean.setLastyearHeaderList(lastyearRes.getMetricList());

        GetRawDataResponseType _2019Res = _2019ResFuture.get();
        singlePeriodDataBean.setPeriod2019List(currentRes, _2019Res, time);
        singlePeriodDataBean.set2019HeaderList(_2019Res.getMetricList());


        return new AsyncResult<>(singlePeriodDataBean);
    }


    @Override
    public Future<SinglePeriodDataBean> getBus3SinglePeriodTrendLineData(GetTrendLineDataRequestType request,
                                                                         ExamineConfigBean examineConfig,
                                                                         String d) throws SQLException {

        String queryType = request.getQueryType();
        if ("trendline".equals(queryType)) {
            return new AsyncResult<>(getBus3DataWithoutDrillDown(request, examineConfig, d));
        }
        return new AsyncResult<>(getBus3DataWithDrillDown(request, examineConfig, d));
    }

    @Override
    public Future<SinglePeriodDataBean> getBus4SinglePeriodTrendLineData(GetTrendLineDataRequestType request,
                                                                         ExamineConfigBean examineConfigBean,
                                                                         String d) throws SQLException {
        String queryType = request.getQueryType();
        if ("trendline".equals(queryType)) {
            return new AsyncResult<>(getBus4DataWithoutDrillDown(examineConfigBean, d));
        }
        return new AsyncResult<>(getBus4DataWithDrillDown(request, examineConfigBean, d));
    }

    @Override
    public Future<SinglePeriodDataBean> getBus567SinglePeriodTrendLineData(GetTrendLineDataRequestType request,
                                                                           ExamineConfigBean examineConfigBean,
                                                                           String d,
                                                                           String metric) throws SQLException, ParseException {
        String queryType = request.getQueryType();
        if ("trendline".equals(queryType)) {
            return new AsyncResult<>(getBus567DataWithOutDrillDown(request, examineConfigBean, d, metric));
        }
        return new AsyncResult<>(getBus567DataWithDrillDown(request, examineConfigBean, d, metric));
    }


    @Override
    public Future<SinglePeriodDataBean> getBus8SinglePeriodTrendLineData(GetTrendLineDataRequestType request,
                                                                         ExamineConfigBean examineConfig,
                                                                         String d) throws SQLException {
        SinglePeriodDataBean bean = new SinglePeriodDataBean();

        ExamineConfigBo bo = new ExamineConfigBo();
        MetricInfoBean metricInfoBean = bo.getMetricInfoBean(examineConfig.getBusinessDashboardExamineeConfigV2(), remoteConfig);

        String level = MetricHelper.getLevelColumnName(metricInfoBean.getLevel());

        DrillDownFilter drillDownFilter = request.getDrillDownFilter();


        String queryType = request.getQueryType();
        if ("drilldown".equals(queryType)) {
            String field = MetricHelper.getDrillDownColumnName(drillDownFilter.getField());
            //如果在某个考核周期都不满足下钻的基本要求 直接返回
            if (!MetricHelper.canDrillDown(level, field)) {
                return new AsyncResult<>(bean);
            }
        }

        String year = examineConfig.getYear();
        String month = examineConfig.getMonth();
        String quarter = examineConfig.getQuarter();
        //虽然是季度指标 但该指标数仓直接给了累计值 所以可以像正常月度指标一样取数据
        String dateType = examineConfig.getDateType();
        String time = examineConfig.getTimeMap().get("currentTime");


        List<String> regionList = metricInfoBean.getRegionList();

        Map<String, List<String>> inMap = new HashMap<>();
        inMap.put("query_d", Lists.newArrayList(d));
        inMap.put("year", Lists.newArrayList(year));
        inMap.put("date_type", Lists.newArrayList(dateType));
        if ("month".equals(dateType)) {
            inMap.put("month", Lists.newArrayList(month));
        } else {
            inMap.put("quarter", Lists.newArrayList(quarter));
        }
        if (!"".equals(level)) {
            inMap.put(level, regionList);
        }
        List<String> groupTagList = new ArrayList<>();

        if ("drilldown".equals(queryType)) {
            String field = MetricHelper.getDrillDownColumnName(drillDownFilter.getField());
            List<String> fieldValueList = drillDownFilter.getFieldValueList();
            if (!GeneralUtil.isEmpty(fieldValueList)) {
                inMap.put(field, fieldValueList);
            }
            groupTagList.add(field);
        }

        List<List<Object>> reachList = bus8Dao.getTrendlineData(inMap, groupTagList);
        bean.setPeriodReachList(reachList, time);
        return new AsyncResult<>(bean);
    }
    @Override
    public Future<SinglePeriodDataBean> getBus9SinglePeriodTrendLineData(GetTrendLineDataRequestType request,
                                                                         ExamineConfigBean examineConfig,
                                                                         String d) throws SQLException {
        SinglePeriodDataBean bean = new SinglePeriodDataBean();

        ExamineConfigBo bo = new ExamineConfigBo();
        MetricInfoBean metricInfoBean = bo.getMetricInfoBean(examineConfig.getBusinessDashboardExamineeConfigV2(), remoteConfig);

        String level = MetricHelper.getLevelColumnName(metricInfoBean.getLevel());

        DrillDownFilter drillDownFilter = request.getDrillDownFilter();


        String queryType = request.getQueryType();
        if ("drilldown".equals(queryType)) {
            String field = MetricHelper.getDrillDownColumnName(drillDownFilter.getField());
            //如果在某个考核周期都不满足下钻的基本要求 直接返回
            if (!MetricHelper.canDrillDown(level, field)) {
                return new AsyncResult<>(bean);
            }
        }

        String year = examineConfig.getYear();
        String month = examineConfig.getMonth();
        String quarter = examineConfig.getQuarter();
        //虽然是季度指标 但该指标数仓直接给了累计值 所以可以像正常月度指标一样取数据
        String dateType = examineConfig.getDateType();
        String time = examineConfig.getTimeMap().get("currentTime");


        List<String> regionList = metricInfoBean.getRegionList();

        List<String> innerGroupTagList = Lists.newArrayList("viewspot_type_name");
        List<String> outerGroupTagList = new ArrayList<>();

        Map<String, List<String>> inMap = new HashMap<>();
        inMap.put("query_d", Lists.newArrayList(d));
        inMap.put("year", Lists.newArrayList(year));
        inMap.put("date_type", Lists.newArrayList(dateType));
        if ("month".equals(dateType)) {
            inMap.put("month", Lists.newArrayList(month));
        } else {
            inMap.put("quarter", Lists.newArrayList(quarter));
        }
        if (!"".equals(level)) {
            inMap.put(level, regionList);
        }

        if ("drilldown".equals(queryType)) {
            String field = MetricHelper.getDrillDownColumnName(drillDownFilter.getField());
            List<String> fieldValueList = drillDownFilter.getFieldValueList();

            if (!GeneralUtil.isEmpty(fieldValueList)) {
                inMap.put(field, fieldValueList);
            }
            innerGroupTagList = Lists.newArrayList(field, "viewspot_type_name");
            outerGroupTagList = Lists.newArrayList(field);
        }

        //获取品类的综合完成数据
        List<List<Object>> periodReachList = bus9NewDao.getMultiCategoryTrendlineData(inMap, innerGroupTagList, outerGroupTagList);
        bean.setPeriodReachList(periodReachList, time);

        if ("trendline".equals(queryType)) {
            //获取单品类加权的堆叠柱状图数据
            List<List<Object>> periodStackList = bus9NewDao.getSingleCategoryTrendlineData(inMap, innerGroupTagList);
            bean.setPeriodStackList(periodStackList, time);
        }
        return new AsyncResult<>(bean);
    }


    private void setBus1TrendlinePopData(ExamineConfigBean examineConfig,
                                         Map<String, List<String>> compareMap,
                                         Map<String, List<String>> notInMap,
                                         SinglePeriodDataBean bean,
                                         String dateType,
                                         String lastDay,
                                         String originLevel) throws Exception {
        Boolean isLastest = examineConfig.getIsLastestPeriod();
        List<String> lineList = Lists.newArrayList("current", "lastyear", "2019");
        Map<String, DalHints> dalHintsMap = new HashMap<>();
        for (String line : lineList) {
            DalHints dalHints = new DalHints().asyncExecution();
            String time = examineConfig.getTimeMap().get(line + "Time");
            Map<String, List<String>> inMap = new HashMap<>(compareMap);
            Map<String, List<String>> betweenMap = new HashMap<>();
            String year = time.split("-")[0];
            String monthOrQuarter = time.split("-")[1];
            inMap.put("year", Lists.newArrayList(year));
            if (isLastest) {
                betweenMap.put("the_date", DateUtil.getPopTimeRange(dateType, lastDay, monthOrQuarter, monthOrQuarter, year, false));
            }
            bus1Dao.getMetricCardMomDataAsync(originLevel, inMap, notInMap, betweenMap, dalHints);
            dalHintsMap.put(line, dalHints);
        }
        List<String> dimList = Bus1TicketActivityHelper.getSingleDimList();
        String currentTime = examineConfig.getTimeMap().get("currentTime");
        List<List<Object>> currentList = dalHintsMap.get("current").getListResult();
        List<List<Object>> lastyearList = dalHintsMap.get("lastyear").getListResult();
        List<List<Object>> _2019List = dalHintsMap.get("2019").getListResult();
        bean.setPeriodLastyearList(currentList, lastyearList, dimList, currentTime);
        bean.setPeriod2019List(currentList, _2019List, dimList, currentTime);
    }


    private void setBus2TrendlinePopData(ExamineConfigBean examineConfig,
                                         Map<String, List<String>> compareMap,
                                         Map<String, List<String>> notInMap,
                                         SinglePeriodDataBean bean,
                                         String dateType,
                                         String lastDay,
                                         String originLevel) throws Exception {
        Boolean isLastest = examineConfig.getIsLastestPeriod();
        List<String> lineList = Lists.newArrayList("current", "lastyear", "2019");
        Map<String, DalHints> dalHintsMap = new HashMap<>();
        for (String line : lineList) {
            DalHints dalHints = new DalHints().asyncExecution();
            String time = examineConfig.getTimeMap().get(line + "Time");
            Map<String, List<String>> inMap = new HashMap<>(compareMap);
            Map<String, List<String>> betweenMap = new HashMap<>();
            String year = time.split("-")[0];
            String monthOrQuarter = time.split("-")[1];
            inMap.put("year", Lists.newArrayList(year));
            if (isLastest) {
                betweenMap.put("the_date", DateUtil.getPopTimeRange(dateType, lastDay, monthOrQuarter, monthOrQuarter, year, false));
            }
            bus2Dao.getMetricCardMomDataAsync(originLevel, inMap, notInMap, betweenMap, dalHints);
            dalHintsMap.put(line, dalHints);
        }
        List<String> dimList = Bus1TicketActivityHelper.getSingleDimList();
        String currentTime = examineConfig.getTimeMap().get("currentTime");
        List<List<Object>> currentList = dalHintsMap.get("current").getListResult();
        List<List<Object>> lastyearList = dalHintsMap.get("lastyear").getListResult();
        List<List<Object>> _2019List = dalHintsMap.get("2019").getListResult();
        bean.setPeriodLastyearList(currentList, lastyearList, dimList, currentTime);
        bean.setPeriod2019List(currentList, _2019List, dimList, currentTime);
    }


    private SinglePeriodDataBean getBus3DataWithDrillDown(GetTrendLineDataRequestType request,
                                                          ExamineConfigBean examineConfig,
                                                          String d) throws SQLException {
        //前端选中的时间对应的年
        String chosenYear = request.getTimeFilter().getYear();

        SinglePeriodDataBean bean = new SinglePeriodDataBean();

        ExamineConfigBo bo = new ExamineConfigBo();
        MetricInfoBean metricInfoBean = bo.getMetricInfoBean(examineConfig.getBusinessDashboardExamineeConfigV2(), remoteConfig);

        // String level = MetricHelper.getLevelColumnName(metricInfoBean.getLevel());
        String level = getLevel(metricInfoBean, request.getSubMetric());

        DrillDownFilter drillDownFilter = request.getDrillDownFilter();
        String field = MetricHelper.getDrillDownColumnName(drillDownFilter.getField());

        //如果在某个考核周期都不满足下钻的基本要求 直接返回
        if (!MetricHelper.canDrillDown(level, field)) {
            return bean;
        }

        String year = examineConfig.getYear();
        String month = examineConfig.getMonth();
        String quarter = examineConfig.getQuarter();
        //当是季度指标 且时间类型为月时  将月设定为累计值(这样在后续处理中季度指标可以直接看做月度指标)
        String dateType = examineConfig.getDateType();
        String time = examineConfig.getTimeMap().get("currentTime");
        // List<String> regionList = metricInfoBean.getRegionList();
        List<String> regionList = getRegionList(metricInfoBean, request.getSubMetric());

        List<String> fieldValueList = drillDownFilter.getFieldValueList();


        Map<String, List<String>> reachInMap = new HashMap<>();
        reachInMap.put("query_d", Lists.newArrayList(d));
        reachInMap.put("year", Lists.newArrayList(year));
        reachInMap.put(dateType, DateUtil.getTimeList(dateType, month, quarter));

        if (!"".equals(level)) {
            reachInMap.put(level, regionList);
        }
        if (!GeneralUtil.isEmpty(fieldValueList)) {
            reachInMap.put(field, fieldValueList);
        }
        // 2024年拆分子业务线
        int y = chosenYear != null ? Integer.parseInt(chosenYear) : 0;
        if(y >= 2024){
        // if("2024".equals(chosenYear)){
            reachInMap.put("bu_type", Lists.newArrayList(request.getSubMetric()));
        }
        List<List<Object>> reachList = bus3Dao.getTrendlineReachData(reachInMap, Lists.newArrayList(field),chosenYear);


        Map<String, List<String>> targetInMap = new HashMap<>();
        targetInMap.put("year", Lists.newArrayList(year));
        targetInMap.put("quarter", Lists.newArrayList(DateUtil.getQuarter(dateType, month, quarter)));
        //前置特殊处理  过滤掉2024年的数据
        // targetInMap.put("business",Lists.newArrayList("0"));
        if (!"".equals(level)) {
            targetInMap.put(level, regionList);
        }
        if (!GeneralUtil.isEmpty(fieldValueList)) {
            targetInMap.put(field, fieldValueList);
        }
        // 24年指标拆分
        if(y >= 2024){
        //if("2024".equals(chosenYear)){
            targetInMap.put("bu_type", Lists.newArrayList(request.getSubMetric()));
        }
        List<List<Object>> targetList = bus3Dao.getSpilitTargetData(targetInMap, Lists.newArrayList(field), chosenYear);

        bean.setPeriodReachList(reachList, time);
        bean.setPeriodTargetList(targetList, time);

        return bean;
    }

    /**
     * 获取子指标对应的考核范围
     * @param metricInfoBean
     * @param subMetric
     * @return
     */
    public List<String> getRegionList(MetricInfoBean metricInfoBean, String subMetric){
        if("domestic".equals(subMetric)){
            return metricInfoBean.getRegionList();
        }else if("overseaDayTour".equals(subMetric)){
            return metricInfoBean.getOverseaOdtRegionList();
        }
        // 如果没有子指标，返回国内数据，兼容24年之前
        return metricInfoBean.getRegionList();
    }

    /**
     * 获取子指标对应的考核层级
     * @param metricInfoBean
     * @param subMetric：checkxxx接口已经根据examineType判断了考核层级，所以只需要根据这个值获取对应的考核层级和范围即可
     * @return
     */
    public String getLevel(MetricInfoBean metricInfoBean, String subMetric){
        if("domestic".equals(subMetric)){
            return MetricHelper.getLevelColumnName(metricInfoBean.getLevel());
        }else if("overseaDayTour".equals(subMetric)){
            return MetricHelper.getLevelColumnName(metricInfoBean.getOverseaOdtLevel());
        }
        // 如果没有子指标，返回国内数据，兼容24年之前
        return MetricHelper.getLevelColumnName(metricInfoBean.getLevel());
    }



    private SinglePeriodDataBean getBus3DataWithoutDrillDown(GetTrendLineDataRequestType request,
                                                             ExamineConfigBean examineConfig,
                                                             String d) throws SQLException {

        //前端选中的时间对应的年
        String chosenYear = request.getTimeFilter().getYear();

        SinglePeriodDataBean bean = new SinglePeriodDataBean();
        String year = examineConfig.getYear();
        String month = examineConfig.getMonth();
        String quarter = examineConfig.getQuarter();
        //当是季度指标 且时间类型为月时  将月设定为累计值(这样在后续处理中季度指标可以直接看做月度指标)
        String dateType = examineConfig.getDateType();
        String time = examineConfig.getTimeMap().get("currentTime");

        ExamineConfigBo bo = new ExamineConfigBo();
        MetricInfoBean metricInfoBean = bo.getMetricInfoBean(examineConfig.getBusinessDashboardExamineeConfigV2(), remoteConfig);

        // String level = MetricHelper.getLevelColumnName(metricInfoBean.getLevel());
        // List<String> regionList = metricInfoBean.getRegionList();
        String level = getLevel(metricInfoBean, request.getSubMetric());
        List<String> regionList = getRegionList(metricInfoBean, request.getSubMetric());

        Map<String, List<String>> reachInMap = new HashMap<>();
        reachInMap.put("query_d", Lists.newArrayList(d));
        reachInMap.put("year", Lists.newArrayList(year));
        reachInMap.put(dateType, DateUtil.getTimeList(dateType, month, quarter));

        if (!"".equals(level)) {
            reachInMap.put(level, regionList);
        }
        // 24年拆分子指标
        int y = year != null ? Integer.parseInt(year) : 0;
        if(y >= 2024){
        // if("2024".equals(year)){
            reachInMap.put("bu_type", Lists.newArrayList(request.getSubMetric()));
        }
        List<List<Object>> reachList = bus3Dao.getTrendlineReachData(reachInMap, new ArrayList<>(),chosenYear);


        Map<String, List<String>> targetInMap = new HashMap<>();
        targetInMap.put("year", Lists.newArrayList(year));
        targetInMap.put("quarter", Lists.newArrayList(DateUtil.getQuarter(dateType, month, quarter)));
        //前置特殊处理  过滤掉2024年的数据
        // targetInMap.put("business",Lists.newArrayList("0"));
        if (!"".equals(level)) {
            targetInMap.put(level, regionList);
        }
        // 24年拆分子指标
        if(y >= 2024){
        // if("2024".equals(year)){
            targetInMap.put("bu_type", Lists.newArrayList(request.getSubMetric()));
        }
        List<List<Object>> targetList = bus3Dao.getSpilitTargetData(targetInMap, new ArrayList<>(), chosenYear);

        bean.setPeriodReachList(reachList, time);
        bean.setPeriodTargetList(targetList, time);

        return bean;
    }


    private SinglePeriodDataBean getBus4DataWithoutDrillDown(ExamineConfigBean examineConfig,
                                                             String d) throws SQLException {
        SinglePeriodDataBean bean = new SinglePeriodDataBean();
        String year = examineConfig.getYear();
        String month = examineConfig.getMonth();
        String quarter = examineConfig.getQuarter();
        //当是季度指标 但该指标数仓直接给了累计值 所以可以像正常月度指标一样取数据
        String dateType = examineConfig.getDateType();
        String time = examineConfig.getTimeMap().get("currentTime");

        ExamineConfigBo bo = new ExamineConfigBo();
        MetricInfoBean metricInfoBean = bo.getMetricInfoBean(examineConfig.getBusinessDashboardExamineeConfigV2(), remoteConfig);

        String level = MetricHelper.getLevelColumnName(metricInfoBean.getLevel());
        List<String> regionList = metricInfoBean.getRegionList();

        Map<String, List<String>> inMap = new HashMap<>();
        inMap.put("query_d", Lists.newArrayList(d));
        inMap.put("year", Lists.newArrayList(year));
        inMap.put("date_type", Lists.newArrayList(dateType));
        if ("month".equals(dateType)) {
            inMap.put("month", Lists.newArrayList(month));
        } else {
            inMap.put("quarter", Lists.newArrayList(quarter));
        }
        if (!"".equals(level)) {
            inMap.put(level, regionList);
        }
        List<String> groupTagList = Lists.newArrayList("scenic_class_name");
        List<String> orderTagList = Lists.newArrayList("scenic_class_id");

        List<List<Object>> reachList = bus4Dao.getScenicSignData(inMap, new ArrayList<>());


        List<List<Object>> stackList = bus4Dao.getTrendlineSignScenicCnt(inMap, groupTagList, orderTagList);


        bean.setPeriodReachList(reachList, time);
        bean.setPeriodStackList(stackList, time);

        return bean;
    }


    private SinglePeriodDataBean getBus4DataWithDrillDown(GetTrendLineDataRequestType request,
                                                          ExamineConfigBean examineConfig,
                                                          String d) throws SQLException {

        SinglePeriodDataBean bean = new SinglePeriodDataBean();

        ExamineConfigBo bo = new ExamineConfigBo();
        MetricInfoBean metricInfoBean = bo.getMetricInfoBean(examineConfig.getBusinessDashboardExamineeConfigV2(), remoteConfig);

        String level = MetricHelper.getLevelColumnName(metricInfoBean.getLevel());

        DrillDownFilter drillDownFilter = request.getDrillDownFilter();
        String field = MetricHelper.getDrillDownColumnName(drillDownFilter.getField());

        //如果在某个考核周期都不满足下钻的基本要求 直接返回
        if (!MetricHelper.canDrillDown(level, field)) {
            return bean;
        }

        String year = examineConfig.getYear();
        String month = examineConfig.getMonth();
        String quarter = examineConfig.getQuarter();
        //虽然是季度指标 但该指标数仓直接给了累计值 所以可以像正常月度指标一样取数据
        String dateType = examineConfig.getDateType();
        String time = examineConfig.getTimeMap().get("currentTime");

        List<String> fieldValueList = drillDownFilter.getFieldValueList();
        List<String> regionList = metricInfoBean.getRegionList();

        Map<String, List<String>> inMap = new HashMap<>();
        inMap.put("query_d", Lists.newArrayList(d));
        inMap.put("year", Lists.newArrayList(year));
        inMap.put("date_type", Lists.newArrayList(dateType));
        if ("month".equals(dateType)) {
            inMap.put("month", Lists.newArrayList(month));
        } else {
            inMap.put("quarter", Lists.newArrayList(quarter));
        }
        if (!"".equals(level)) {
            inMap.put(level, regionList);
        }
        if (!GeneralUtil.isEmpty(fieldValueList)) {
            inMap.put(field, fieldValueList);
        }

        List<List<Object>> reachList = bus4Dao.getScenicSignData(inMap, Lists.newArrayList(field));

        bean.setPeriodReachList(reachList, time);
        return bean;
    }

    private SinglePeriodDataBean getBus567DataWithOutDrillDown(GetTrendLineDataRequestType request,
                                                               ExamineConfigBean examineConfig,
                                                               String d,
                                                               String metric) throws ParseException, SQLException {
        SinglePeriodDataBean bean = new SinglePeriodDataBean();

        String year = examineConfig.getYear();
        String month = examineConfig.getMonth();
        String quarter = examineConfig.getQuarter();
        //当是季度指标 且时间类型为月时  将月设定为累计值(这样在后续处理中季度指标可以直接看做月度指标)
        String dateType = examineConfig.getDateType();
        List<String> timeList = DateUtil.getTimeList(dateType, month, quarter);


        String domainName = request.getDomainName();
        ExamineConfigBo bo = new ExamineConfigBo();
        MetricInfoBean metricInfoBean = bo.getMetricInfoBean(examineConfig.getBusinessDashboardExamineeConfigV2(), remoteConfig);
        String originLevel = metricInfoBean.getLevel();

        Map<String, List<String>> inMap = new HashMap<>();
        inMap.put("query_d", Lists.newArrayList(d));
        inMap.put("year", Lists.newArrayList(year));
        inMap.put(dateType, timeList);
        inMap.put("statistics_dim_id", Bus567Helper.getStatisticsDimIdList("withoutDrillDown",null));
        inMap.put("examine_level", Lists.newArrayList(originLevel));
        inMap.put("examinee", Lists.newArrayList(domainName));

        String time = examineConfig.getTimeMap().get("currentTime");

        Integer additionalGapDay = Bus567Helper.getAddtionalGapDay(inMap, null, year, dateType, month, quarter, "", d);
//        Integer gapDays = DateUtil.getOverallDaysOfMonthOrQuarter(year, dateType, month, quarter, d);
        Integer gapDays = bus567Dao.getGapDays("5,6,7", year, dateType, d, timeList);
        List<List<Object>> weaknessList = bus567Dao.getDataWithOutDrillDown(inMap, metric);
        bean.setPeriodWeaknessList(weaknessList, gapDays, additionalGapDay, time, new ArrayList<>());

        return bean;
    }


    private SinglePeriodDataBean getBus567DataWithDrillDown(GetTrendLineDataRequestType request,
                                                            ExamineConfigBean examineConfig,
                                                            String d,
                                                            String metric) throws ParseException, SQLException {
        SinglePeriodDataBean bean = new SinglePeriodDataBean();

        ExamineConfigBo bo = new ExamineConfigBo();
        MetricInfoBean metricInfoBean = bo.getMetricInfoBean(examineConfig.getBusinessDashboardExamineeConfigV2(), remoteConfig);

        String level = MetricHelper.getLevelColumnName(metricInfoBean.getLevel());

        DrillDownFilter drillDownFilter = request.getDrillDownFilter();
        String field = MetricHelper.getDrillDownColumnName(drillDownFilter.getField());

        //如果在某个考核周期都不满足下钻的基本要求 直接返回
        if (!MetricHelper.canDrillDown(level, field)) {
            return bean;
        }

        String year = examineConfig.getYear();
        String month = examineConfig.getMonth();
        String quarter = examineConfig.getQuarter();
        //当是季度指标 且时间类型为月时  将月设定为累计值(这样在后续处理中季度指标可以直接看做月度指标)
        String dateType = examineConfig.getDateType();
        String time = examineConfig.getTimeMap().get("currentTime");
        List<String> regionList = metricInfoBean.getRegionList();
        List<String> bdList = metricInfoBean.getBdList();

        List<String> fieldValueList = drillDownFilter.getFieldValueList();


        Map<String, List<String>> inMap = new HashMap<>();
        inMap.put("query_d", Lists.newArrayList(d));
        inMap.put("year", Lists.newArrayList(year));
        inMap.put(dateType, DateUtil.getTimeList(dateType, month, quarter));
        inMap.put("statistics_dim_id", Bus567Helper.getStatisticsDimIdList("drillDown", field));
        inMap.put("examine_level", Lists.newArrayList(MetricHelper.getFieldChineseName(field)));
        if (!"".equals(level)) {
            if ("examinee".equals(level)) {
                inMap.put(level, bdList);
            } else {
                inMap.put(level, regionList);
            }
        }
        if (GeneralUtil.isNotEmpty(fieldValueList)) {
            inMap.put(field, fieldValueList);
        }

        List<String> groupTagList = Lists.newArrayList(field);
        Integer additionalGapDay = Bus567Helper.getAddtionalGapDay(inMap, null, year, dateType, month, quarter, "", d);
//        Integer gapDays = DateUtil.getOverallDaysOfMonthOrQuarter(year, dateType, month, quarter, d);
        Integer gapDays = bus567Dao.getGapDays("5,6,7", year, dateType, d, DateUtil.getTimeList(dateType, month, quarter));

        List<List<Object>> weaknessList = bus567Dao.getDataWithDrillDown(inMap, groupTagList, metric);
        bean.setPeriodWeaknessList(weaknessList, gapDays, additionalGapDay, time, groupTagList);

        return bean;
    }


    @Override
    public Future<SinglePeriodDataBean> getBus10SinglePeriodTrendLineData(GetTrendLineDataRequestType request,
                                                                          GetRawDataRequestType req,
                                                                          ExamineConfigBean examineConfigBean,
                                                                          MetricInfoBean metricInfoBean) throws Exception {

        SinglePeriodDataBean bean = new SinglePeriodDataBean();

        String level = MetricHelper.getLevelColumnName(metricInfoBean.getLevel());

        DrillDownFilter drillDownFilter = request.getDrillDownFilter();


        String queryType = request.getQueryType();
        if ("drilldown".equals(queryType)) {
            String field = MetricHelper.getDrillDownColumnName(drillDownFilter.getField());
            //如果在某个考核周期都不满足下钻的基本要求 直接返回
            if (!MetricHelper.canDrillDown(level, field)) {
                return new AsyncResult<>(bean);
            }
        }

        GetRawDataResponseType res = switchNewTableHelper.switchRemoteDatabase(req);

        String time = examineConfigBean.getTimeMap().get("currentTime");
        bean.setPeriodReachList(res, time);
        bean.setReachHeaderList(res.getMetricList(), res.getGroupList());
        return new AsyncResult<>(bean);
    }

    @Override
    public Future<SinglePeriodDataBean> getBus11SinglePeriodTrendLineData(GetTrendLineDataRequestType request,
                                                                          MetricInfoBean metricInfoBean,
                                                                          String d,
                                                                          GetRawDataRequestType req,
                                                                          ExamineConfigBean examineConfigBean) throws Exception {
        String queryType = request.getQueryType();
        if ("trendline".equals(queryType)) {
            return new AsyncResult<>(getBus11DataWithOutDrillDown(d, req, examineConfigBean));
        }
        return new AsyncResult<>(getBus11DataWithDrillDown(request, metricInfoBean, d, req, examineConfigBean));
    }


    @Override
    public Future<SinglePeriodDataBean> getBus12SinglePeriodTrendLineData(GetTrendLineDataRequestType request,
                                                                          MetricInfoBean metricInfoBean,
                                                                          String d,
                                                                          GetRawDataRequestType req,
                                                                          ExamineConfigBean examineConfigBean) throws Exception {
        String queryType = request.getQueryType();
        if ("trendline".equals(queryType)) {
            return new AsyncResult<>(getBus12DataWithOutDrillDown(d, req, examineConfigBean));
        }
        return new AsyncResult<>(getBus12DataWithDrillDown(request, metricInfoBean, d, req, examineConfigBean));
    }


    private SinglePeriodDataBean getBus12DataWithOutDrillDown(String d,
                                                              GetRawDataRequestType req,
                                                              ExamineConfigBean examineConfigBean) throws Exception {

        SinglePeriodDataBean bean = new SinglePeriodDataBean();

        Integer additionalGapDay = Bus12Helper.getAddtionalGapDay(null, examineConfigBean, d, req.getAndMap());

        GetRawDataResponseType res = switchNewTableHelper.switchRemoteDatabase(req);

        String time = examineConfigBean.getTimeMap().get("currentTime");

//        Integer gapDays = Bus11Helper.getGapDays(null, examineConfigBean, d);
        Integer gapDays = getGapDays(examineConfigBean, d,"12");


        bean.setPeriodWeaknessList(res, gapDays, additionalGapDay, time, new ArrayList<>());
        bean.setWeaknessHeaderList(res.getMetricList(), res.getGroupList());

        return bean;
    }


    private SinglePeriodDataBean getBus12DataWithDrillDown(GetTrendLineDataRequestType request,
                                                           MetricInfoBean metricInfoBean,
                                                           String d,
                                                           GetRawDataRequestType req,
                                                           ExamineConfigBean examineConfigBean) throws Exception {

        SinglePeriodDataBean bean = new SinglePeriodDataBean();

        String level = MetricHelper.getLevelColumnName(metricInfoBean.getOdtLevel());

        DrillDownFilter drillDownFilter = request.getDrillDownFilter();


        String field = MetricHelper.getDrillDownColumnName(drillDownFilter.getField());
        //如果在某个考核周期都不满足下钻的基本要求 直接返回
        if (!MetricHelper.canDrillDown(level, field)) {
            return bean;
        }

        Integer additionalGapDay = Bus12Helper.getAddtionalGapDay(null, examineConfigBean, d, req.getAndMap());

        GetRawDataResponseType res = switchNewTableHelper.switchRemoteDatabase(req);

        String time = examineConfigBean.getTimeMap().get("currentTime");

//        Integer gapDays = Bus11Helper.getGapDays(null, examineConfigBean, d);
        Integer gapDays = getGapDays(examineConfigBean, d,"12");


        bean.setPeriodWeaknessList(res, gapDays, additionalGapDay, time, res.getGroupList());
        bean.setWeaknessHeaderList(res.getMetricList(), res.getGroupList());

        return bean;
    }





    private SinglePeriodDataBean getBus11DataWithOutDrillDown(String d,
                                                              GetRawDataRequestType req,
                                                              ExamineConfigBean examineConfigBean) throws Exception {

        SinglePeriodDataBean bean = new SinglePeriodDataBean();

        Integer additionalGapDay = Bus11Helper.getAddtionalGapDay(null, examineConfigBean, d, req.getAndMap());

        GetRawDataResponseType res = switchNewTableHelper.switchRemoteDatabase(req);

        String time = examineConfigBean.getTimeMap().get("currentTime");

//        Integer gapDays = Bus11Helper.getGapDays(null, examineConfigBean, d);
        Integer gapDays = getGapDays(examineConfigBean, d,"11");


        bean.setPeriodWeaknessList(res, gapDays, additionalGapDay, time, new ArrayList<>());
        bean.setWeaknessHeaderList(res.getMetricList(), res.getGroupList());

        return bean;
    }


    private SinglePeriodDataBean getBus11DataWithDrillDown(GetTrendLineDataRequestType request,
                                                           MetricInfoBean metricInfoBean,
                                                           String d,
                                                           GetRawDataRequestType req,
                                                           ExamineConfigBean examineConfigBean) throws Exception {

        SinglePeriodDataBean bean = new SinglePeriodDataBean();

        String subMetric = request.getSubMetric();
        String level = "domesticDayTour".equals(subMetric) ? MetricHelper.getLevelColumnName(metricInfoBean.getOdtLevel()) : MetricHelper.getLevelColumnName(metricInfoBean.getOverseaOdtLevel());


        DrillDownFilter drillDownFilter = request.getDrillDownFilter();


        String field = MetricHelper.getDrillDownColumnName(drillDownFilter.getField());
        //如果在某个考核周期都不满足下钻的基本要求 直接返回
        if (!MetricHelper.canDrillDown(level, field)) {
            return bean;
        }

        Integer additionalGapDay = Bus11Helper.getAddtionalGapDay(null, examineConfigBean, d, req.getAndMap());

        GetRawDataResponseType res = switchNewTableHelper.switchRemoteDatabase(req);

        String time = examineConfigBean.getTimeMap().get("currentTime");

//        Integer gapDays = Bus11Helper.getGapDays(null, examineConfigBean, d);
        Integer gapDays = getGapDays(examineConfigBean, d,"11");


        bean.setPeriodWeaknessList(res, gapDays, additionalGapDay, time, res.getGroupList());
        bean.setWeaknessHeaderList(res.getMetricList(), res.getGroupList());

        return bean;
    }

    public Integer getGapDays(ExamineConfigBean examineConfigBean,
                              String d,
                              String metric) throws Exception {
        SqlParamterBean gapDaysSqlBean = Bus11Helper.getGapDaysSqlBean(examineConfigBean.getYear(), examineConfigBean.getDateType(),
                examineConfigBean.getMonth(), examineConfigBean.getQuarter(), metric, d);
        GetRawDataRequestType gapDaysReq = gapDaysSqlBean.convertBeanToRequest(true);
        GetRawDataResponseType gapDaysRes = switchNewTableHelper.switchRemoteDatabase(gapDaysReq);
        return MetricHelper.getGapDays(gapDaysRes);
    }

}
