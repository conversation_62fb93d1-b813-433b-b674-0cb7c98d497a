package com.ctrip.tour.business.dashboard.helper

import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean
import com.ctrip.tour.business.dashboard.tktBusiness.helper.Bus1TicketActivityHelper
import spock.lang.Specification
import spock.lang.Unroll

class Bus2TicketActivityHelperTest extends Specification {

    def setup() {
    }

    @Unroll
    def "获取可下钻维度数据接口 输入的level为 #level regionList为 #regionList"() {
        given: "设定请求参数"
        def metricInfoBean = new MetricInfoBean(regionList: regionList)


        when:
        def resultList = Bus1TicketActivityHelper.getFieldList(level, metricInfoBean)

        then: "验证多种返回结果"
        resultList == actualResultList

        where: "用表格方式验证多种返回结果"
        level  | regionList       | actualResultList
        "国内" | []               | ["region_name", "province_name", "examinee", "viewspotid"]
        "三方" | []               | ["region_name", "province_name", "examinee", "viewspotid"]
        "大区" | ["华东大区"]     | ["province_name", "examinee", "viewspotid"]
        "省份" | ["江苏", "上海"] | ["province_name", "examinee", "viewspotid"]
        "省份" | ["江苏"]         | ["examinee", "viewspotid"]
    }
}
