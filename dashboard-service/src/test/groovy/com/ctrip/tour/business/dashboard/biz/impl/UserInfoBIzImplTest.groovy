package com.ctrip.tour.business.dashboard.biz.impl

import com.ctrip.soa._24922.GetUserInfoRequestType
import com.ctrip.tour.business.dashboard.tktBusiness.biz.impl.UserInfoBizImpl
import spock.lang.Specification

class UserInfoBIzImplTest extends Specification {

    def "getUserInfo"(){

        def userInfoBiz = new UserInfoBizImpl()

        given: "设置请求参数"
        def request = new GetUserInfoRequestType()

//        and: "mock MDC"
//        MDC.put("empCode","S35097")
//        MDC.put("empName","张三")

        when: "调用接口"
        def response = userInfoBiz.getUserInfo(request);

        then: "验证返回结果"
        with(response) {
            userInfoMap.get("empCode") == null
            userInfoMap.get("empName") == null
        }


    }
}
