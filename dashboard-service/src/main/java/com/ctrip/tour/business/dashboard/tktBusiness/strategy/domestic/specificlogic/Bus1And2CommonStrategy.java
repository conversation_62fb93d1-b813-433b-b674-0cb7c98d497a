package com.ctrip.tour.business.dashboard.tktBusiness.strategy.domestic.specificlogic;

import com.ctrip.framework.ucs.common.util.StringUtils;
import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.grpBusiness.enums.DomesticBusinessTypeEnum;
import com.ctrip.tour.business.dashboard.grpBusiness.enums.DomesticMetricEnum;
import com.ctrip.tour.business.dashboard.sightArchives.dao.domesticmetric.CdmOrdTtdDashboardBdDayPerfDfDao;
import com.ctrip.tour.business.dashboard.sightArchives.dao.domesticmetric.CdmOrdTtdDashboardExamLevelPerfDfDao;
import com.ctrip.tour.business.dashboard.sightArchives.dao.domesticmetric.CdmOrdTtdFlbdRankTotalDfDao;
import com.ctrip.tour.business.dashboard.sightArchives.dao.domesticmetric.BoardBiGmvProfitExamineeTargetDao;
import com.ctrip.tour.business.dashboard.sightArchives.entity.domesticMetricEntity.BiGmvProfitTargetBO;
import com.ctrip.tour.business.dashboard.sightArchives.entity.domesticMetricEntity.CdmOrdTtdDashboardExamLevelPerfDfBO;
import com.ctrip.tour.business.dashboard.sightArchives.entity.domesticMetricEntity.GmvProfitTargetRegionBO;
import com.ctrip.tour.business.dashboard.sightArchives.entity.domesticMetricEntity.bean.DomesticMetricParamBean;
import com.ctrip.tour.business.dashboard.sightArchives.entity.domesticmetric.*;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.DomesticSingelDrillBaseInfo;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ExamineConfigBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.SinglePeriodTrendLineBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.bo.ExamineConfigBo;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.Bus1Dao;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardExamineeConfigV2Dao;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardExamineeConfigV2;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.*;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 包含国内日游和出境日游的策略
 */
@Component
public class Bus1And2CommonStrategy {

    private static List<String> monthList = Lists.newArrayList("1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12");

    @Autowired
    private Bus1Dao dao;

    @Autowired
    private CdmOrdTtdDashboardBdDayPerfDfDao cdmOrdTtdDashboardBdDayPerfDfDao;

    @Autowired
    private BusinessDashboardExamineeConfigV2Dao examineeConfigV2Dao;

    @Autowired
    private SinglePeriodTrendLineBiz singlePeriodTrendLineBiz;

    @Autowired
    private CdmOrdTtdFlbdRankTotalDfDao cdmOrdTtdFlbdRankTotalDfDao;

    @Autowired
    private RemoteConfig remoteConfig;
    @Autowired
    private CdmOrdTtdDashboardExamLevelPerfDfDao cdmOrdTtdDashboardExamLevelPerfDfdao;

    @Autowired
    private BoardBiGmvProfitExamineeTargetDao boardBiGmvProfitExamineeTargetDao;

    /**
     * 指标卡首页——区分不同指标和业务线
     */
    public DomesticMetricDetailInfo getMetricCardDataWithDiffBusinessLine(String domainName, TimeFilter timeFilter, List<MetricInfoBean> metricInfoBeanList, String d, Integer metricId, Integer businessId, Boolean isFirst) throws Exception {
        DomesticMetricDetailInfo metricDetailInfo = new DomesticMetricDetailInfo();
        Set<Integer> businessLineSet = new HashSet<>();
        if (isFirst) {
            // 首页
            for (MetricInfoBean metricInfoBean : metricInfoBeanList) {
                if (DomesticBusinessTypeEnum.PLAY.getId() == metricInfoBean.getBusinessType()
                        || DomesticBusinessTypeEnum.ACTIVITY.getId() == metricInfoBean.getBusinessType()
                        || DomesticBusinessTypeEnum.DAY_TOUR.getId() == metricInfoBean.getBusinessType()
                        || DomesticBusinessTypeEnum.ALL.getId() == metricInfoBean.getBusinessType()){
                    businessLineSet.add(2);
                }
                if (metricInfoBean.getBusinessType() == DomesticBusinessTypeEnum.TICKET.getId()
                        || DomesticBusinessTypeEnum.ALL.getId() == metricInfoBean.getBusinessType()){
                    businessLineSet.add(1);
                }
            }
            if (businessLineSet.size() == 1) {
                //只考一个业务线
                boolean isDayTour = false;
                boolean isAct = false;
                for(MetricInfoBean bean : metricInfoBeanList) {
                    if (DomesticBusinessTypeEnum.PLAY.getId() == bean.getBusinessType()){
                        isAct = true;
                        isDayTour = true;
                    }
                    if (DomesticBusinessTypeEnum.ACTIVITY.getId() == bean.getBusinessType())
                        isAct = true;
                    if (DomesticBusinessTypeEnum.DAY_TOUR.getId() == bean.getBusinessType())
                        isDayTour = true;
                }
                int realBuLine;
                if (isDayTour && isAct) {
                    realBuLine = 2;
                }else if (isAct) {
                    realBuLine = 3;
                }else if (isDayTour){
                    realBuLine = 4;
                }else{
                    realBuLine = 1;
                }
                metricDetailInfo = getSingleMetricCardData(domainName, timeFilter, metricInfoBeanList, d,realBuLine, metricId,1);
            }else if (businessLineSet.size() == 2){
                //两个业务线都要考
                metricDetailInfo = getSingleMetricCardData(domainName, timeFilter, metricInfoBeanList, d,DomesticBusinessTypeEnum.ALL.getId(),metricId,1);
                metricDetailInfo.setSubMetricDetailInfoList(Arrays.asList(getSingleMetricCardData(domainName, timeFilter, metricInfoBeanList, d,DomesticBusinessTypeEnum.PLAY.getId(),metricId,2),
                        getSingleMetricCardData(domainName, timeFilter, metricInfoBeanList, d,DomesticBusinessTypeEnum.TICKET.getId(),metricId,2)));
            }else{
                return new DomesticMetricDetailInfo();
            }
        }else{
            // 详情页
            if (businessId == 1){
                // 门票
                businessLineSet.add(1);
                metricDetailInfo = getSingleMetricCardData(domainName, timeFilter, metricInfoBeanList, d,DomesticBusinessTypeEnum.TICKET.getId(), metricId,1);
            }else{
                // 玩乐
                for (MetricInfoBean metricInfoBean : metricInfoBeanList) {
                    if (DomesticBusinessTypeEnum.ACTIVITY.getId() == metricInfoBean.getBusinessType()){
                        businessLineSet.add(3);
                    }
                    if (DomesticBusinessTypeEnum.DAY_TOUR.getId() == metricInfoBean.getBusinessType()){
                        businessLineSet.add(4);
                    }
                }
                if (businessLineSet.size() == 1) {
                    //只考活动或者日游
                    Integer businessLine = businessLineSet.iterator().next();
                    metricDetailInfo = getSingleMetricCardData(domainName, timeFilter, metricInfoBeanList, d,businessLine, metricId,1);
                }else if (businessLineSet.size() == 2 || businessLineSet.isEmpty()){
                    //活动日游都考
                    metricDetailInfo = getSingleMetricCardData(domainName, timeFilter, metricInfoBeanList, d,DomesticBusinessTypeEnum.PLAY.getId(),metricId,1);
                    metricDetailInfo.setSubMetricDetailInfoList(Arrays.asList(getSingleMetricCardData(domainName, timeFilter, metricInfoBeanList, d,DomesticBusinessTypeEnum.ACTIVITY.getId(),metricId,2),
                            getSingleMetricCardData(domainName, timeFilter, metricInfoBeanList, d,DomesticBusinessTypeEnum.DAY_TOUR.getId(),metricId,2)));
                }else{
                    return new DomesticMetricDetailInfo();
                }
            }
        }
        return metricDetailInfo;
    }


    public DomesticMetricDetailInfo getSingleMetricCardData(String domainName,
                                                            TimeFilter timeFilter,
                                                            List<MetricInfoBean> metricInfoBeanList,
                                                            String d,
                                                            Integer businessLine,
                                                            Integer metricType,
                                                            Integer busLevel) throws Exception {
        DomesticMetricDetailInfo response = new DomesticMetricDetailInfo();
        if (busLevel == 1) {
            // 一级考核指标需要展示指标值
            response.setMetricCode(DomesticMetricEnum.getCodeById(metricType));
        } else {
            // 二级考核指标需要展示业务线
            switch (businessLine) {
                case 1:
                    response.setMetricCode("tic");
                    break;
                case 2:
                    response.setMetricCode("play");
                    break;
                case 3:
                    response.setMetricCode("act");
                    break;
                case 4:
                    response.setMetricCode("dayTour");
                    break;
                default:
                    response.setMetricCode(DomesticMetricEnum.getCodeById(metricType));
            }
        }

        String year = timeFilter.getYear();
        String month = timeFilter.getMonth();
        Map<String, MetricInfoBean> metricQuarterMap = metricInfoBeanList.stream().collect(Collectors.toMap(MetricInfoBean::getQuarter, metricInfoBean -> metricInfoBean));
        if (metricQuarterMap.isEmpty()) {
            return new DomesticMetricDetailInfo();
        }
        String maxQuarter = DateUtil.getMaxQuarters(new ArrayList<>(metricQuarterMap.keySet()));

        List<String> quarters = new ArrayList<>(metricQuarterMap.keySet());

        //获取目标值
        Domestic12TargetParamBean targetParamBean = generateDomesticGmvProfitTargetParamBean(domainName, year, businessLine, DomesticMetricEnum.getCodeById(metricType), quarters, month, d, timeFilter.getDateType());
        double targetValue = boardBiGmvProfitExamineeTargetDao.getGmvProfitTarget(targetParamBean);

        //获取完成值
        Domestic12ParamBean paramComplete = generateDomesticGmvProfitParamBean(domainName, year, month, quarters, null, null, businessLine, metricType, d);
        Domestic12ResultBean resultBean = cdmOrdTtdDashboardBdDayPerfDfDao.getProfitCompleteValueQuery(paramComplete);
        double completeValue = resultBean.getCompleteValue();

        //获取去年完成值
        double completeLastYear = 0.00;
        for (Map.Entry<String, MetricInfoBean> entry : metricQuarterMap.entrySet()) {
            Domestic12MomParamBean paramLastYear = generateMomParamBean(year, month, entry.getKey(), businessLine, metricType, d, entry.getValue(), null, DateUtil.getLastYearEndDate(d), "lastyear");
            completeLastYear += cdmOrdTtdDashboardExamLevelPerfDfdao.getMomInfo(paramLastYear);
        }

        //获取环比数据
        MomInfoBean momInfoBean = getMomInfo(quarters, d, timeFilter, businessLine, metricType, metricQuarterMap.get(maxQuarter));
        double momData = momInfoBean.getMomData();
        String momType = momInfoBean.getMomType();

        Domestic12RankParamBean rankParamBean = generateDomesticGmvProfitRankParamBean(domainName, year, month, quarters, metricType, d, businessLine, timeFilter);
        String rankInfo = cdmOrdTtdFlbdRankTotalDfDao.getGmvProfitRankInfo(rankParamBean);

        response.setCompleteRate(targetValue == 0.00 ? 0.00 : completeValue / targetValue);
        response.setCompleteValue(completeValue);
        response.setTargetValue(targetValue);
        response.setYoyValue(completeLastYear == 0.00 ? 0.00 : (completeValue - completeLastYear) / completeLastYear);
        response.setMomType(momType);
        response.setMomValue(momData);
        if (rankInfo != null) {
            response.setRank(Integer.parseInt(rankInfo.split("/")[0]));
            response.setTotalRank(Integer.parseInt(rankInfo.split("/")[1]));
        }


        if (DomesticMetricEnum.PROFIT.getId() == metricType) {
            response.setInSystemCompleteValue(resultBean.getSysInnerCompleteValue());
            response.setOutSystemCompleteValue(resultBean.getSysOuterCompleteValue());
        }

        MetricHelper.setCommonMetricCardDrillDownInfo(response, metricQuarterMap.get(maxQuarter), businessLine);
        return response;
    }

    /**
     * 查询国内gmv毛利目标数据
     */
    public Domestic12TargetParamBean generateDomesticGmvProfitTargetParamBean(String domainName,
                                                                              String year,
                                                                              Integer businessId,
                                                                              String metric,
                                                                              List<String> quarters,
                                                                              String month,
                                                                              String d,
                                                                              String dateType) {
        Domestic12TargetParamBean param = new Domestic12TargetParamBean();
        param.setDomainName(domainName);
        param.setYear(year);
        switch (businessId) {
            case 0:
                param.setBusinessLines(Arrays.asList("101", "102", "103"));
                break;
            case 1:
                param.setBusinessLines(Collections.singletonList("101"));
                break;
            case 2:
                param.setBusinessLines(Arrays.asList("102", "103"));
                break;
            case 3:
                param.setBusinessLines(Collections.singletonList("102"));
                break;
            case 4:
                param.setBusinessLines(Collections.singletonList("103"));
                break;
        }
        param.setMetric(metric);
        if (month == null) {
            List<String> monthList = new ArrayList<>();
            quarters.forEach(e -> monthList.addAll(DateUtil.getMonthsBeforeDate(Integer.parseInt(year), e, d)));
            param.setMonthList(monthList.stream().map(Integer::parseInt).map(String::valueOf).collect(Collectors.toList()));
        }else{
            param.setMonthList(Collections.singletonList(String.valueOf(Integer.parseInt(month))));
        }

        if ("year".equals(dateType)) {
            param.setMonthList(monthList);
        }

        param.setD(d);
        return param;
    }

    /**
     * 查询国内gmv毛利等级数据
     */
    public Domestic12RankParamBean generateDomesticGmvProfitRankParamBean(String domainName,
                                                                          String year,
                                                                          String month,
                                                                          List<String> quarters,
                                                                          Integer metricId,
                                                                          String d,
                                                                          Integer businessLine,
                                                                          TimeFilter timeFilter) {
        Domestic12RankParamBean param = new Domestic12RankParamBean();
        param.setD(d);
        param.setYear(year);
        param.setDomain_name(domainName);
        param.setMetricId(metricId);
        switch (timeFilter.getDateType()) {
            case "month":
                param.setTag("M");
                param.setPeriod(timeFilter.getMonth());
                break;
            case "quarter":
                param.setTag("Q");
                param.setPeriod(timeFilter.getQuarter());
                break;
            case "half":
                param.setTag("H");
                param.setPeriod(timeFilter.getHalf());
                break;
            case "year":
                param.setTag("Y");
                param.setPeriod(timeFilter.getYear());
                break;
        }
        switch (businessLine) {
            case 0:
                param.setBusinessLine("all");
                break;
            case 1:
                param.setBusinessLine("tkt");
                break;
            case 2:
                param.setBusinessLine("act-odt");
                break;
            case 3:
                param.setBusinessLine("act");
                break;
            case 4:
                param.setBusinessLine("odt");
                break;
        }

        return param;
    }

    /**
     * 查询国内gmv毛利数据的参数
     */
    public Domestic12ParamBean generateDomesticGmvProfitParamBean(String domainName,
                                                                  String year,
                                                                  String month,
                                                                  List<String> quarters,
                                                                  String startDate,
                                                                  String endDate,
                                                                  Integer businessLine,
                                                                  Integer metricType,
                                                                  String d) {
        Domestic12ParamBean param = new Domestic12ParamBean();
        param.setD(d);
        param.setDomainName(domainName);
        param.setYear(year);
        param.setBusiness(transformBusinessType(businessLine));
        param.setMouth(month);
        param.setQuarters(quarters);
        param.setStartDate(startDate);
        param.setEndDate(endDate);
        param.setMetricType(metricType);
        param.setBusinessId(businessLine);
        return param;
    }

    /**
     * 查询国内gmv毛利组织维度的数据
     */
    public Domestic12MomParamBean generateMomParamBean(String year,
                                                       String month,
                                                       String quarter,
                                                       Integer businessLine,
                                                       Integer metricType,
                                                       String d,
                                                       MetricInfoBean bean,
                                                       String startDate,
                                                       String endDate,
                                                       String momType) throws ParseException {
        Domestic12MomParamBean param = new Domestic12MomParamBean();
        param.setD(d);
        param.setMouth(month);
        param.setQuarters(quarter == null ? null : Collections.singletonList(quarter));
        param.setMetricType(metricType);
        param.setBusinessId(businessLine);
        param.setYear(year);
        param.setStartDate(startDate);
        param.setEndDate(endDate);
        List<Domestic12BusinessLevelBean> businessInfoList = new ArrayList<>();
        switch (businessLine) {
            case 0:
                int[] businessTypes = {1, 3, 4};  // 定义业务类型数组
                for (int type : businessTypes) {
                    Domestic12BusinessLevelBean info = getLevelAndRangesInfo(bean, type);
                    if (info != null) {
                        businessInfoList.add(info);
                    }
                }
                break;
            case 1:
                Domestic12BusinessLevelBean tktBusinessInfo1 = getLevelAndRangesInfo(bean,1);
                if (tktBusinessInfo1 != null) {
                    businessInfoList.add(tktBusinessInfo1);
                }
                break;
            case 2:
                int[] businessTypes2 = {3, 4};  // 定义业务类型数组
                for (int type : businessTypes2) {
                    Domestic12BusinessLevelBean info = getLevelAndRangesInfo(bean, type);
                    if (info != null) {
                        businessInfoList.add(info);
                    }
                }
                break;
            case 3:
                Domestic12BusinessLevelBean actBusinessInfo3 = getLevelAndRangesInfo(bean,3);
                if (actBusinessInfo3 != null) {
                    businessInfoList.add(actBusinessInfo3);
                }
                break;
            case 4:
                Domestic12BusinessLevelBean odtBusinessInfo4 = getLevelAndRangesInfo(bean,4);
                if (odtBusinessInfo4 != null) {
                    businessInfoList.add(odtBusinessInfo4);
                }
                break;
            default:
                break;
        }
        param.setBusinessList(businessInfoList);
        if (momType != null) {
            switch (momType) {
                case "7days":
                case "30days":
                    param.setMouth(null);
                    param.setQuarters(null);
                    break;
                case "lastmonth":
                    param.setQuarters(null);
                    param.setStartDate(null);
                    param.setEndDate(null);
                    break;
                case "lastyear":
                    param.setYear(String.valueOf(Integer.parseInt(year) - 1));
                    param.setEndDate(DateUtil.getDayOfInterval(DateUtil.getLastYearEndDate(d), -1));
                    if (month != null) {
                        param.setQuarters(null);
                    }
            }
        }
        return param;
    }

    /**
     * 创建考核层级和考核范围的对象区分业务线
     * businessId 只会出现1,3,4表示门票，活动，日游
     */
    public Domestic12BusinessLevelBean getLevelAndRangesInfo(MetricInfoBean bean, Integer businessId) {
        if(bean == null) {
            return null;
        }
        Domestic12BusinessLevelBean levelBean = new Domestic12BusinessLevelBean();
        switch (businessId) {
            case 1:
                levelBean.setLevel(bean.getLevel());
                levelBean.setBuType("tkt");
                levelBean.setRanges(getBusinessRanges(bean.getLevel(), bean.getRegionList(), bean.getBdList()));
                if (bean.getLevel() == null || StringUtils.isBlank(bean.getLevel())) {
                    return null;
                }
                return levelBean;
            case 3:
                levelBean.setLevel(bean.getActLevel());
                levelBean.setBuType("act");
                levelBean.setRanges(getBusinessRanges(bean.getActLevel(), bean.getActRegionList(), bean.getActBdList()));
                if (bean.getActLevel() == null || StringUtils.isBlank(bean.getActLevel())) {
                    return null;
                }
                return levelBean;
            case 4:
                levelBean.setLevel(bean.getOdtLevel());
                levelBean.setBuType("odt");
                levelBean.setRanges(getBusinessRanges(bean.getOdtLevel(), bean.getOdtRegionList(), null));
                if (bean.getOdtLevel() == null || StringUtils.isBlank(bean.getOdtLevel())) {
                    return null;
                }
                return levelBean;
        }
        return null;
    }

    /**
     * 设置不同的范围
     */
    public List<String> getBusinessRanges(String level, List<String> ranges, List<String> bdList) {
        if (level == null){
            return null;
        }
        if (ranges != null && ranges.isEmpty()) {
            ranges = null;
        }
        if (bdList != null && bdList.isEmpty()) {
            bdList = null;
        }
        switch (level) {
            case "国内"://NOSONAR
            case "大区"://NOSONAR
            case "省份"://NOSONAR
                return ranges;
            case "景点"://NOSONAR
                return bdList;
        }
        return null;
    }

    public static List<String> mergeAndDistinct(List<String> list1, List<String> list2) {
        if (list1 == null || list1.isEmpty()) {
            return list2;
        }
        if (list2 == null || list2.isEmpty()) {
            return list1;
        }
        return Stream.concat(list1.stream(), list2.stream())
                .distinct()
                .collect(Collectors.toList());
    }


    /**
     * 获取环比信息
     */
    public MomInfoBean getMomInfo(List<String> quarters, String d, TimeFilter timeFilter, Integer businessLine, Integer metricType, MetricInfoBean bean) throws ParseException {
        MomInfoBean response = new MomInfoBean();

        //数据整体是T+1的
        //获取最新数据的时间
        //例如 d=2022-08-15  则数仓给出的最新数据是2022-08-14
        String lastDay = DateUtil.getDayOfInterval(d, -1);
        String year = timeFilter.getYear();
        String month = timeFilter.getMonth();

        if ("month".equals(timeFilter.getDateType())) {
            if (DateUtil.isCurrentMonthV2(lastDay, year, month)) {
                //当月、取近7天环比
                response.setMomType("7days");
                //近七天
                String startDate = DateUtil.getDayOfInterval(lastDay, -6);
                Domestic12MomParamBean paramFront = generateMomParamBean(null, null, null, businessLine, metricType, d, bean, startDate, lastDay, "7days");
                double numerator = cdmOrdTtdDashboardExamLevelPerfDfdao.getMomInfo(paramFront);

                //上七天
                String momEndDate = DateUtil.getDayOfInterval(startDate, -1);
                String momStartDate = DateUtil.getDayOfInterval(momEndDate, -6);
                Domestic12MomParamBean paramBack = generateMomParamBean(null, null, null, businessLine, metricType, d, bean, momStartDate, momEndDate, "7days");
                double denominator = cdmOrdTtdDashboardExamLevelPerfDfdao.getMomInfo(paramBack);

                response.setMomData(denominator == 0.00 ? 0.00 : (numerator - denominator) / denominator);
            } else {
                //非当月、取近月环比
                response.setMomType("lastmonth");

                //当月
                Domestic12MomParamBean paramFront = generateMomParamBean(year, month, bean.getQuarter(), businessLine, metricType, d, bean, null, null, "lastmonth");
                double numerator = cdmOrdTtdDashboardExamLevelPerfDfdao.getMomInfo(paramFront);

                // 上月
                List<String> lastTimeInfo = DateUtil.getLastTimeInfo(timeFilter.getDateType(), year, null, month);
                String secondYear = lastTimeInfo.get(0);
                String secondMonth = lastTimeInfo.get(1);
                Domestic12MomParamBean paramBack = generateMomParamBean(secondYear, secondMonth, null, businessLine, metricType, d, bean, null, null, "lastmonth");
                double denominator = cdmOrdTtdDashboardExamLevelPerfDfdao.getMomInfo(paramBack);

                response.setMomData(denominator == 0.00 ? 0.00 : (numerator - denominator) / denominator);

            }
        } else {
            if (DateUtil.findLatestJudgeCurrentQuarter(lastDay, year, quarters)) {
                //当季、取近30天环比
                response.setMomType("30days");
                //近30天
                String startDate = DateUtil.getDayOfInterval(lastDay, -29);
                Domestic12MomParamBean paramFront = generateMomParamBean(null, null, null, businessLine, metricType, d, bean, startDate, lastDay, "30days");
                double numerator = cdmOrdTtdDashboardExamLevelPerfDfdao.getMomInfo(paramFront);

                //上30天
                String momEndDate = DateUtil.getDayOfInterval(startDate, -1);
                String momStartDate = DateUtil.getDayOfInterval(momEndDate, -29);
                Domestic12MomParamBean paramBack = generateMomParamBean(null, null, null, businessLine, metricType, d, bean, momStartDate, momEndDate, "30days");
                double denominator = cdmOrdTtdDashboardExamLevelPerfDfdao.getMomInfo(paramBack);

                response.setMomData(denominator == 0.00 ? 0.00 : (numerator - denominator) / denominator);

            } else {
                //非当季、取上季环比
                response.setMomType("lastquarter");

                //当季
                String currentQuarter = bean.getQuarter();
                Domestic12MomParamBean paramFront = generateMomParamBean(year, null, currentQuarter, businessLine, metricType, d, bean, null, null, "lastquarter");
                double numerator = cdmOrdTtdDashboardExamLevelPerfDfdao.getMomInfo(paramFront);

                // 上季
                List<String> lastTimeInfo = DateUtil.getLastTimeInfoV2(timeFilter.getDateType(), year, currentQuarter, month);
                String secondYear = lastTimeInfo.get(0);
                String secondQuarter = lastTimeInfo.get(1);
                Domestic12MomParamBean paramBack = generateMomParamBean(secondYear, null, secondQuarter, businessLine, metricType, d, bean, null, null, "lastquarter");
                double denominator = cdmOrdTtdDashboardExamLevelPerfDfdao.getMomInfo(paramBack);

                response.setMomData(denominator == 0.00 ? 0.00 : (numerator - denominator) / denominator);
            }
        }
        return response;
    }


    /**
     * 业务线转化为gmv毛利库里的字段值
     * @param businessType
     * @return
     */
    public List<String> transformBusinessType(int businessType) {
        List<String> buTypeList = new ArrayList<>();
        switch (businessType) {
            case 1:
                buTypeList.add("tkt");
                break;
            case 2:
                buTypeList.add("act");
                buTypeList.add("odt");
                break;
            case 3:
                buTypeList.add("act");
                break;
            case 4:
                buTypeList.add("odt");
                break;
            default:
                buTypeList.add("tkt");
                buTypeList.add("act");
                buTypeList.add("odt");
                break;
        }
        return buTypeList;
    }

    public GetDomesticMetricTrendDataResponseType getSingleTrendlineData(GetDomesticMetricTrendDataRequestType request,
                                                                         String d,
                                                                         Integer metricId) throws Exception {
        GetDomesticMetricTrendDataResponseType response = new GetDomesticMetricTrendDataResponseType();
        List<TrendLineDetailInfo> trendLineDetailInfoList = new ArrayList<>();
        response.setTrendLineDetailInfoList(trendLineDetailInfoList);
        TimeFilter timeFilter = request.getTimeFilter();
        String domainName = request.getDomainName();

        TimeFilter timeFilterWithDomain = DateUtil.getMaxQuarterTimeFilter(timeFilter, d);
        List<ExamineConfigBean> examineConfigBeanList = MultiPeriodMappingHelper.getMultiPeriodConfigList(timeFilterWithDomain, d, examineeConfigV2Dao.queryMetricAllConfig(domainName, d, String.valueOf(metricId)), null);

        TrendLineDetailInfo lineChart = new TrendLineDetailInfo();
        TrendLineDetailInfo barChart = new TrendLineDetailInfo();

        lineChart.setType("lineChart");
        barChart.setType("barChart");
        lineChart.setDim(request.getMetricCode());
        barChart.setDim(request.getMetricCode());

        List<TrendLineDataItem> lineDataItems = new ArrayList<>();
        List<TrendLineDataItem> barDataItems = new ArrayList<>();
        lineChart.setTrendLineDataItemList(lineDataItems);
        barChart.setTrendLineDataItemList(barDataItems);
        for (ExamineConfigBean examineConfigBean : examineConfigBeanList) {
            //只下发25年的
            if (Integer.parseInt(examineConfigBean.getYear()) <= 2024) {
                continue;
            }
            //获取单季或单月的数据
            List<TrendLineDataItem> singleLineDataItems = getSingleTrendLineDataWithQuarterOrMonth(request,examineConfigBean, d);
            for (TrendLineDataItem item : singleLineDataItems) {
                switch (item.getName()) {
                    case "completeValue":
                        barDataItems.add(item);
                        break;
                    case "yoyValue":
                    case "completeRate":
                        lineDataItems.add(item);
                        break;
                }
            }
        }
        trendLineDetailInfoList.add(lineChart);
        trendLineDetailInfoList.add(barChart);

        return response;
    }

    public List<TrendLineDataItem> getSingleTrendLineDataWithQuarterOrMonth(GetDomesticMetricTrendDataRequestType request,
                                                                            ExamineConfigBean examineConfigBean,
                                                                            String d) throws Exception {
        List<TrendLineDataItem> trendLineDataItems = new ArrayList<>();
        String domainName = request.getDomainName();

        Integer businessLine = DomesticMetricHelper.transformBusinessInfo(request.getBusinessId(), request.getSubBusinessId());
        String month = examineConfigBean.getMonth();
        List<String> quarters = examineConfigBean.getQuarter() == null ? null : Collections.singletonList(examineConfigBean.getQuarter());

        String timeString = DomesticMetricHelper.getTimeFormat(examineConfigBean);

        Domestic12TargetParamBean targetParamBean = generateDomesticGmvProfitTargetParamBean(domainName, examineConfigBean.getYear(), businessLine, request.getMetricCode(), quarters, month, d, "other");
        double targetValue = boardBiGmvProfitExamineeTargetDao.getGmvProfitTarget(targetParamBean);

        double realTargetValue = targetValue;
        if (targetValue == -1.00) {
            realTargetValue = 0.00;
        }

        //获取完成值
        Domestic12ParamBean paramComplete = generateDomesticGmvProfitParamBean(domainName, examineConfigBean.getYear(), month, quarters, null, null, businessLine, DomesticMetricEnum.getIdByCode(request.getMetricCode()), d);
        Domestic12ResultBean resultBean = cdmOrdTtdDashboardBdDayPerfDfDao.getProfitCompleteValueQuery(paramComplete);
        double completeValue = resultBean.getCompleteValue();

        //获取去年完成值
        ExamineConfigBo bo = new ExamineConfigBo();
        List<MetricInfoBean> metricInfoBeanList = bo.getMetricInfoBeanListV2(Collections.singletonList(examineConfigBean.getBusinessDashboardExamineeConfigV2()), remoteConfig);
        MetricInfoBean metricInfoBean = metricInfoBeanList.stream().filter(e -> String.valueOf(DomesticMetricEnum.getIdByCode(request.getMetricCode())).equals(e.getMetric())).findFirst().orElse(null);
        Domestic12MomParamBean paramLastYear = generateMomParamBean(examineConfigBean.getYear(), month, examineConfigBean.getQuarter(), businessLine, DomesticMetricEnum.getIdByCode(request.getMetricCode()), d, metricInfoBean, null, DateUtil.getLastYearEndDate(d), "lastyear");
        double completeLastYear = cdmOrdTtdDashboardExamLevelPerfDfdao.getMomInfo(paramLastYear);

        double completeRate = realTargetValue == 0.00 ? 0.00 : completeValue / realTargetValue;

        TrendLineDataItem completeValueItem = new TrendLineDataItem();
        TrendLineDataItem yoyValueItem = new TrendLineDataItem();
        TrendLineDataItem CompleteRateItem = new TrendLineDataItem();

        completeValueItem.setTime(timeString);
        yoyValueItem.setTime(timeString);
        CompleteRateItem.setTime(timeString);

        completeValueItem.setValue(completeValue);
        yoyValueItem.setValue(completeLastYear == 0.00 ? 0.00 : (completeValue - completeLastYear) / completeLastYear);
        CompleteRateItem.setValue(completeRate);

        completeValueItem.setName("completeValue");
        yoyValueItem.setName("yoyValue");
        CompleteRateItem.setName("completeRate");

        trendLineDataItems.add(completeValueItem);
        trendLineDataItems.add(yoyValueItem);
        trendLineDataItems.add(CompleteRateItem);

        return trendLineDataItems;
    }




    public GetDomesticTableDataResponseType getSingleTableData(GetDomesticTableDataRequestType request,
                                                               MetricInfoBean metricInfoBean, String d) throws Exception {
        GetDomesticTableDataResponseType response = new GetDomesticTableDataResponseType();
        List<DomesticTableData> tableDataItemList = new ArrayList<>();
        response.setTableDataItemList(tableDataItemList);
        //若为玩乐+玩乐总计 ，计算两遍，不同考核范围计算出来的值加总
        Boolean isPlayAll = 2 == request.getBusinessId() && 5 == request.getSubBusinessId();

        if(isPlayAll){
            getPlayAllBusinessTableData(request,response,metricInfoBean,d);
        }else {
            getSingleBusinessTableData(request,response,metricInfoBean,d);
        }
        return response;
    }

    private void getPlayAllBusinessTableData(GetDomesticTableDataRequestType request, GetDomesticTableDataResponseType response,
                                             MetricInfoBean metricInfoBean, String d ) throws Exception {
        DrillDownFilter drillDownFilter = request.getDrillDownFilter();
        String field = MetricHelper.getDrillDownColumnName(drillDownFilter.getField());
        String metric = DomesticMetricEnum.getMetricByCode(request.getMetricCode());

        Integer businessId = request.getBusinessId();
        Integer subBusinessId = request.getSubBusinessId();
        response.setTableHeaderList(Bus1Helper.getPlayAllTableHeaderList(field, metric));
        //计算玩乐总计，玩乐分业务线
        List<String> buTypes = new ArrayList<>();
        buTypes.add("odt");
        buTypes.add("act");
        String levelAll = Bus1And2DayTourHelper.compareLevel(metricInfoBean.getActLevel(), metricInfoBean.getOdtLevel());
        List<String> rangeListAll = Bus1And2DayTourHelper.compareRangeList(metricInfoBean.getActLevel(), metricInfoBean.getActRegionList(), metricInfoBean.getOdtLevel(), metricInfoBean.getOdtRegionList());
        DomesticMetricParamBean currentParam = get1And2Param("current", buTypes,
                request.getTimeFilter(), levelAll, rangeListAll, drillDownFilter, d, null, request.getPageNo(), request.getPageSize());
        currentParam.setMetric(metric);
        Integer totalCount = null;
        if ("examinee".equals(field)) {
            currentParam.setExamineLevel(levelAll);
            totalCount = cdmOrdTtdDashboardBdDayPerfDfDao.getTableTotalCountByParam(currentParam);
        } else {
            totalCount = cdmOrdTtdDashboardExamLevelPerfDfdao.getTableDataCountByParam(currentParam);
        }
        response.setTotalNum(totalCount);
        if (response.getTotalNum() == 0) {
            return;
        }
        List<CdmOrdTtdDashboardExamLevelPerfDfBO> currentResult = new ArrayList<>();
        if ("examinee".equals(field)) {
            currentParam.setIsPalyAll(true);
            currentResult = cdmOrdTtdDashboardBdDayPerfDfDao.getTableDataByParam(currentParam);
        } else {
            currentResult = cdmOrdTtdDashboardExamLevelPerfDfdao.getTableDataByParam(currentParam);
        }
        //lastYear
        List<String> currentResultList = new ArrayList<>();
        if ("大区".equalsIgnoreCase(drillDownFilter.getField())) {//NOSONAR
            currentResultList = currentResult.stream().map(x -> x.getBusinessRegionName()).collect(Collectors.toList());
        } else if ("省份".equalsIgnoreCase(drillDownFilter.getField())) {//NOSONAR
            currentResultList = currentResult.stream().map(x -> x.getBusinessSubRegionName()).collect(Collectors.toList());
        } else if ("商拓".equalsIgnoreCase(drillDownFilter.getField())) {//NOSONAR
            currentResultList = currentResult.stream().map(x -> x.getPrdMeid()).collect(Collectors.toList());
        } else if ("POI".equalsIgnoreCase(drillDownFilter.getField())) {//NOSONAR
            currentResultList = currentResult.stream().map(x -> x.getVstName()).collect(Collectors.toList());
        }
        List<CdmOrdTtdDashboardExamLevelPerfDfBO> lastYearResult = new ArrayList<>();
        DomesticMetricParamBean lastYearParam = get1And2Param("lastYear",buTypes,
                request.getTimeFilter(), levelAll, rangeListAll, drillDownFilter, d,currentResultList,request.getPageNo(),request.getPageSize());
        currentParam.setMetric(metric);
        if ("examinee".equals(field)) {
            for(String domainName:currentResultList){
                CdmOrdTtdDashboardExamLevelPerfDfBO lastYearByDomain=
                        getLastYearByDomain(buTypes, d,domainName,request,metric,remoteConfig);
                lastYearResult.add(lastYearByDomain);
            }
//            lastYearResult = cdmOrdTtdDashboardBdDayPerfDfDao.getTableDataByParam(lastYearParam);
        } else {
            lastYearResult = cdmOrdTtdDashboardExamLevelPerfDfdao.getTableDataByParam(lastYearParam);
        }
        List<String> tagList = new ArrayList<>();
        List<BiGmvProfitTargetBO> targetResult = null;
        //目标值
        if ("examinee".equals(field)) {
            List<String> domainList = currentResult == null ? null : currentResult.stream().map((x -> x.getPrdMeid())).collect(Collectors.toList());
            Domestic12TargetParamBean targetParamBean = get1And2TargetExamineParam("examinee", domainList, d,metricInfoBean, request);
            targetParamBean.setBusinessLines(Arrays.asList("103","102"));

            targetParamBean.setD(d);
            targetResult = boardBiGmvProfitExamineeTargetDao.getExamineeTarget(targetParamBean);
        } else if (!"viewspotid".equalsIgnoreCase(field)) {
            List<String> currentList = new ArrayList<>();
            if ("region_name".equalsIgnoreCase(field) && CollectionUtils.isNotEmpty(currentResult)) {
                currentList = currentResult.stream().map((x -> x.getBusinessRegionName())).collect(Collectors.toList());
            } else if ("province_name".equalsIgnoreCase(field) && CollectionUtils.isNotEmpty(currentResult)) {
                currentList = currentResult.stream().map((x -> x.getBusinessSubRegionName())).collect(Collectors.toList());
            }
            Domestic12TargetParamBean targetParamBean = get1And2TargetExamineParam("region", currentList, d, metricInfoBean, request);
            targetParamBean.setBusinessLines(Arrays.asList("103","102"));
            List<GmvProfitTargetRegionBO> targetResults = boardBiGmvProfitExamineeTargetDao.getGmvRegionTarget(targetParamBean);
            targetResult = buildByGmvProfitTargetRegionBO(targetParamBean.getMonthList(), targetResults);
        }
//组装数据
        List<DomesticTableData> tableDataItemList = response.getTableDataItemList();
        ChartHelper.buildBus1And2DomesticTableData(currentResult, lastYearResult,
                targetResult, tagList, tableDataItemList, metric, field, response.getTableHeaderList());

        //计算日游
        buildTableByBuType(tableDataItemList,"act",metricInfoBean,d,request,response.getTableHeaderList());
        buildTableByBuType(tableDataItemList,"odt",metricInfoBean,d,request,response.getTableHeaderList());
    }

    private void buildTableByBuType(List<DomesticTableData> tableDataItemList, String buType, MetricInfoBean metricInfoBean,
                                    String d, GetDomesticTableDataRequestType request,List<String> tableHeaderList) throws Exception{
        DrillDownFilter drillDownFilter = request.getDrillDownFilter();
        String field = MetricHelper.getDrillDownColumnName(drillDownFilter.getField());
        String metric = DomesticMetricEnum.getMetricByCode(request.getMetricCode());

        Integer businessId = request.getBusinessId();
        Integer subBusinessId = request.getSubBusinessId();

        //计算玩乐总计，玩乐分业务线
        List<String> buTypes = new ArrayList<>();
        buTypes.add(buType);
        String level = "";
        List<String> rangeListAll = null;
        if ("act".equalsIgnoreCase(buType)) {
            level = metricInfoBean.getActLevel();
            rangeListAll = metricInfoBean.getActRegionList();
        } else {
            level = metricInfoBean.getOdtLevel();
            rangeListAll = metricInfoBean.getOdtRegionList();
        }

        DomesticMetricParamBean currentParam = get1And2Param("current", buTypes,
                request.getTimeFilter(), level, rangeListAll, drillDownFilter, d, null, request.getPageNo(), request.getPageSize());
        currentParam.setMetric(metric);
        List<String> currentResultList = new ArrayList<>();
        if ("大区".equalsIgnoreCase(drillDownFilter.getField())) {//NOSONAR
            currentResultList = tableDataItemList.stream().map(x -> x.getRegionName()).collect(Collectors.toList());
            currentParam.setBusinessRegionName(currentResultList);
        } else if ("省份".equalsIgnoreCase(drillDownFilter.getField())) {//NOSONAR
            currentResultList = tableDataItemList.stream().map(x -> x.getProvinceName()).collect(Collectors.toList());
            currentParam.setBusinessSubRegionName(currentResultList);
        } else if ("商拓".equalsIgnoreCase(drillDownFilter.getField())) {//NOSONAR
            currentResultList = tableDataItemList.stream().map(x -> x.getExaminee()).collect(Collectors.toList());
            currentParam.setDomainNames(currentResultList);
        } else if ("POI".equalsIgnoreCase(drillDownFilter.getField())) {//NOSONAR
            currentResultList = tableDataItemList.stream().map(x -> x.getViewspot()).collect(Collectors.toList());
            currentParam.setVstName(currentResultList);
        }
        currentParam.setPageIndex(null);
        currentParam.setPageSize(null);

        List<CdmOrdTtdDashboardExamLevelPerfDfBO> currentResult = new ArrayList<>();
        if ("examinee".equals(field)) {
            currentParam.setExamineLevel(level);
//            currentParam.setIsPalyAll(true);
            currentResult = cdmOrdTtdDashboardBdDayPerfDfDao.getTableDataByParam(currentParam);
        } else {
            currentResult = cdmOrdTtdDashboardExamLevelPerfDfdao.getTableDataByParam(currentParam);
        }
        //lastYear

        List<CdmOrdTtdDashboardExamLevelPerfDfBO> lastYearResult = new ArrayList<>();
        DomesticMetricParamBean lastYearParam = get1And2Param("lastYear",buTypes,
                request.getTimeFilter(), level, rangeListAll, drillDownFilter, d,currentResultList,request.getPageNo(),request.getPageSize());
        currentParam.setMetric(metric);
        if ("examinee".equals(field)) {
            for(String domainName:currentResultList){
                CdmOrdTtdDashboardExamLevelPerfDfBO lastYearByDomain=
                        getLastYearByDomain(buTypes, d,domainName,request,metric,remoteConfig);
                lastYearResult.add(lastYearByDomain);
            }
//            lastYearResult = cdmOrdTtdDashboardBdDayPerfDfDao.getTableDataByParam(lastYearParam);
        } else {
            lastYearResult = cdmOrdTtdDashboardExamLevelPerfDfdao.getTableDataByParam(lastYearParam);
        }
        List<String> tagList = new ArrayList<>();
        List<BiGmvProfitTargetBO> targetResult = null;
        List<String> businessLine=new ArrayList<>();
        if("act".equalsIgnoreCase(buType)){
            businessLine.add("102");
        }else {
            businessLine.add("103");
        }
        //目标值
        if ("examinee".equals(field)) {
            List<String> domainList = currentResult == null ? null : currentResult.stream().map((x -> x.getPrdMeid())).collect(Collectors.toList());
            Domestic12TargetParamBean targetParamBean = get1And2TargetExamineParam("examinee", domainList, d,metricInfoBean, request);
            targetParamBean.setBusinessLines(businessLine);

            targetParamBean.setD(d);
            targetResult = boardBiGmvProfitExamineeTargetDao.getExamineeTarget(targetParamBean);
        } else if (!"viewspotid".equalsIgnoreCase(field)) {
            List<String> currentList = new ArrayList<>();
            if ("region_name".equalsIgnoreCase(field) && CollectionUtils.isNotEmpty(currentResult)) {
                currentList = currentResult.stream().map((x -> x.getBusinessRegionName())).collect(Collectors.toList());
            } else if ("province_name".equalsIgnoreCase(field) && CollectionUtils.isNotEmpty(currentResult)) {
                currentList = currentResult.stream().map((x -> x.getBusinessSubRegionName())).collect(Collectors.toList());
            }
            Domestic12TargetParamBean targetParamBean = get1And2TargetExamineParam("region", currentList, d, metricInfoBean, request);
            targetParamBean.setBusinessLines(businessLine);
            List<GmvProfitTargetRegionBO> targetResults = boardBiGmvProfitExamineeTargetDao.getGmvRegionTarget(targetParamBean);
            targetResult = buildByGmvProfitTargetRegionBO(targetParamBean.getMonthList(), targetResults);
        }
//组装数据
        ChartHelper.buildBus1And2ButypeTableData(currentResult, lastYearResult,
                targetResult, buType, tableDataItemList, metric, field, tableHeaderList);

    }

    private void getSingleBusinessTableData(GetDomesticTableDataRequestType request, GetDomesticTableDataResponseType response,
                                            MetricInfoBean metricInfoBean, String d)  throws Exception{
        List<DomesticTableData> tableDataItemList = response.getTableDataItemList();

        DrillDownFilter drillDownFilter = request.getDrillDownFilter();
        String field = MetricHelper.getDrillDownColumnName(drillDownFilter.getField());
        String metric = DomesticMetricEnum.getMetricByCode(request.getMetricCode());
        Integer businessId = request.getBusinessId();
        Integer subBusinessId = request.getSubBusinessId();

        response.setTableHeaderList(Bus1Helper.getTableHeaderList(field,metric));

        List<String> buTypes = Bus1And2DayTourHelper.getDomesticBuTypeByBusinessId(businessId, subBusinessId);
        String level = MetricHelper.getLevelByBusinessId(metricInfoBean, request.getBusinessId(), request.getSubBusinessId());
        List<String> rangeList = MetricHelper.getRangeListByBusinessId(metricInfoBean, request.getBusinessId(), request.getSubBusinessId());
        if (StringUtils.isEmpty(level)) {
            return;
        }
        DomesticMetricParamBean currentParam = get1And2Param("current",buTypes,
                request.getTimeFilter(), level, rangeList, drillDownFilter, d,null,request.getPageNo(),request.getPageSize());
        currentParam.setMetric(metric);
        Integer totalCount = null;
        if ("examinee".equals(field)) {
            currentParam.setExamineLevel(level);
            if ("商拓".equalsIgnoreCase(drillDownFilter.getField()) && "景点".equalsIgnoreCase(level)) {//NOSONAR
                currentParam.setExamineLevel(null);
            }
            totalCount = cdmOrdTtdDashboardBdDayPerfDfDao.getTableTotalCountByParam(currentParam);
        } else {
            totalCount = cdmOrdTtdDashboardExamLevelPerfDfdao.getTableDataCountByParam(currentParam);
        }
        response.setTotalNum(totalCount);
        if (response.getTotalNum() == 0) {
            return ;
        }

        //获取当前数据
        List<CdmOrdTtdDashboardExamLevelPerfDfBO> currentResult = null;
        if ("examinee".equals(field)) {
            List<String> domainList = cdmOrdTtdDashboardBdDayPerfDfDao.getDstinctExamineeByParam(currentParam);
            currentParam.setDomainNames(domainList);
            currentParam.setPageIndex(null);
            currentParam.setPageSize(null);
            currentResult = cdmOrdTtdDashboardBdDayPerfDfDao.getTableDataByParam(currentParam);
        } else {
            currentResult = cdmOrdTtdDashboardExamLevelPerfDfdao.getTableDataByParam(currentParam);
        }

        //lastYear
        List<String> currentResultList = new ArrayList<>();
        if ("大区".equalsIgnoreCase(drillDownFilter.getField())) {//NOSONAR
            currentResultList = currentResult.stream().map(x -> x.getBusinessRegionName()).collect(Collectors.toList());
        } else if ("省份".equalsIgnoreCase(drillDownFilter.getField())) {//NOSONAR
            currentResultList = currentResult.stream().map(x -> x.getBusinessSubRegionName()).collect(Collectors.toList());
        } else if ("商拓".equalsIgnoreCase(drillDownFilter.getField())) {//NOSONAR
            currentResultList = currentResult.stream().map(x -> x.getPrdMeid()).collect(Collectors.toList());
        } else if ("POI".equalsIgnoreCase(drillDownFilter.getField())) {//NOSONAR
            currentResultList = currentResult.stream().map(x -> x.getVstName()).collect(Collectors.toList());
        }
        List<CdmOrdTtdDashboardExamLevelPerfDfBO> lastYearResult = new ArrayList<>() ;
        DomesticMetricParamBean lastYearParam = get1And2Param("lastYear",buTypes,
                request.getTimeFilter(), level, rangeList, drillDownFilter, d,currentResultList,request.getPageNo(),request.getPageSize());
        lastYearParam.setMetric(metric);
        if ("examinee".equals(field)) {
            //获取所有商拓名称，获取商拓对应的考核范围，获取考核范围对应的组织完成值。
            for(String domainName:currentResultList){
                CdmOrdTtdDashboardExamLevelPerfDfBO lastYearByDomain=
                        getLastYearByDomain(buTypes, d,domainName,request,metric,remoteConfig);
                lastYearResult.add(lastYearByDomain);
            }
//            lastYearResult = cdmOrdTtdDashboardBdDayPerfDfDao.getTableDataByParam(lastYearParam);
        } else {
            lastYearResult = cdmOrdTtdDashboardExamLevelPerfDfdao.getTableDataByParam(lastYearParam);
        }
        List<String> tagList = new ArrayList<>();
        List<BiGmvProfitTargetBO> targetResult = null;
        //目标值
        if ("examinee".equals(field)) {
            List<String> domainList = currentResult.stream().map((x -> x.getPrdMeid())).collect(Collectors.toList());
            Domestic12TargetParamBean targetParamBean = get1And2TargetExamineParam("examinee", domainList, d,metricInfoBean, request);

            targetParamBean.setD(d);
            targetResult = boardBiGmvProfitExamineeTargetDao.getExamineeTarget(targetParamBean);
        } else if (!"viewspotid".equalsIgnoreCase(field)) {
            List<String> currentList = new ArrayList<>();
            if ("region_name".equalsIgnoreCase(field)) {
                currentList = currentResult.stream().map((x -> x.getBusinessRegionName())).collect(Collectors.toList());
            } else if ("province_name".equalsIgnoreCase(field)) {
                currentList = currentResult.stream().map((x -> x.getBusinessSubRegionName())).collect(Collectors.toList());
            }
            Domestic12TargetParamBean targetParamBean = get1And2TargetExamineParam("region", currentList, d, metricInfoBean, request);
            List<GmvProfitTargetRegionBO> targetResults = boardBiGmvProfitExamineeTargetDao.getGmvRegionTarget(targetParamBean);
            targetResult = buildByGmvProfitTargetRegionBO(targetParamBean.getMonthList(), targetResults);
        }
//组装数据
        ChartHelper.buildBus1And2DomesticTableData(currentResult, lastYearResult,
                targetResult, tagList, tableDataItemList, metric, field, response.getTableHeaderList());
    }

    private CdmOrdTtdDashboardExamLevelPerfDfBO getLastYearByDomain(List<String> buTypes,
                                                                    String d,
                                                                    String domainName,
                                                                    GetDomesticTableDataRequestType request,
                                                                    String metric,
                                                                    RemoteConfig remoteConfig)  throws Exception{
        TimeFilter timeFilter = request.getTimeFilter();
        DrillDownFilter drillDownFilter = request.getDrillDownFilter();

        String dateType = timeFilter.getDateType();
        List<String> quarter = null;
        if ("quarter".equals(dateType)) {
            quarter = Arrays.asList(timeFilter.getQuarter());
        } else if ("month".equals(dateType)) {
            quarter = Arrays.asList(DateUtil.getQuarterOfMonth(timeFilter.getMonth()));
        } else if ("half".equals(dateType)) {
            quarter = DateUtil.getQuarterOfHalf(timeFilter.getHalf());
        }
//        d="2025-07-30";
        List<BusinessDashboardExamineeConfigV2> examineeConfigV2List =
                examineeConfigV2Dao.querySpecificPeriodAllMetricConfig(domainName, d,
                        request.getTimeFilter().getYear(), quarter, null);
        ExamineConfigBo bo = new ExamineConfigBo();
        MetricInfoBean metricInfoBeanByDomain =
                bo.getSingleMetricInfoBeanV2(examineeConfigV2List, remoteConfig, metric);
        String level = MetricHelper.getLevelByBusinessId(metricInfoBeanByDomain, request.getBusinessId(), request.getSubBusinessId());
        List<String> rangeList = MetricHelper.getRangeListByBusinessId(metricInfoBeanByDomain, request.getBusinessId(), request.getSubBusinessId());

        DomesticMetricParamBean lastYearParam = get1And2Param("lastYear",buTypes,
                request.getTimeFilter(), level, rangeList, drillDownFilter, d,Arrays.asList(domainName),request.getPageNo(),request.getPageSize());
lastYearParam.setExamineLevel(level);
        List<CdmOrdTtdDashboardExamLevelPerfDfBO> result = cdmOrdTtdDashboardExamLevelPerfDfdao.getTableDataByParam(lastYearParam);
        CdmOrdTtdDashboardExamLevelPerfDfBO resultFromDb = result.get(0);
        CdmOrdTtdDashboardExamLevelPerfDfBO lastYearByExamineConfig = new CdmOrdTtdDashboardExamLevelPerfDfBO();
        lastYearByExamineConfig.setPrdMeid(domainName);
        lastYearByExamineConfig.setBusinessRegionName(resultFromDb.getBusinessRegionName());
        lastYearByExamineConfig.setBusinessSubRegionName(resultFromDb.getBusinessSubRegionName());
        lastYearByExamineConfig.setTtdSysInnerProfit(resultFromDb.getTtdSysInnerProfit());
        lastYearByExamineConfig.setTtdSucProfit(resultFromDb.getTtdSucProfit());
        lastYearByExamineConfig.setTtdSucIncome(resultFromDb.getTtdSucIncome());
        lastYearByExamineConfig.setTtdSysOuterProfit(resultFromDb.getTtdSysOuterProfit());
        return lastYearByExamineConfig;

    }

    private List<BiGmvProfitTargetBO> buildByGmvProfitTargetRegionBO(List<String> monthList, List<GmvProfitTargetRegionBO> targetResults) {
        //一个区一个目标，目标值为month指定值相加
        if (CollectionUtils.isEmpty(targetResults)) {
            return null;
        }
        List<BiGmvProfitTargetBO> result = new ArrayList<>();
        for (GmvProfitTargetRegionBO bo : targetResults) {
            BiGmvProfitTargetBO targetBO = new BiGmvProfitTargetBO();
            targetBO.setBusinessRegionName(bo.getBusinessRegionName());
            targetBO.setBusinessSubRegionName(bo.getBusinessSubRegionName());
            Long value = 0L;
            if(CollectionUtils.isNotEmpty(monthList)) {
                for (String month : monthList) {
                    switch (month) {
                        case "1":
                            value = value + bo.getTtdTrgt1();
                            break;
                        case "2":
                            value = value + bo.getTtdTrgt2();
                            break;
                        case "3":
                            value = value + bo.getTtdTrgt3();
                            break;
                        case "4":
                            value = value + bo.getTtdTrgt4();
                            break;
                        case "5":
                            value = value + bo.getTtdTrgt5();
                            break;
                        case "6":
                            value = value + bo.getTtdTrgt6();
                            break;
                        case "7":
                            value = value + bo.getTtdTrgt7();
                            break;
                        case "8":
                            value = value + bo.getTtdTrgt8();
                            break;
                        case "9":
                            value = value + bo.getTtdTrgt9();
                            break;
                        case "10":
                            value = value + bo.getTtdTrgt10();
                            break;
                        case "11":
                            value = value + bo.getTtdTrgt11();
                            break;
                        case "12":
                            value = value + bo.getTtdTrgt12();
                            break;
                    }
                }
            }else {
                value = bo.getTtdTrgt1() + bo.getTtdTrgt2() + bo.getTtdTrgt3() + bo.getTtdTrgt4() + bo.getTtdTrgt5() + bo.getTtdTrgt6()
                        + bo.getTtdTrgt7() + bo.getTtdTrgt8() + bo.getTtdTrgt9() + bo.getTtdTrgt10() + bo.getTtdTrgt11() + bo.getTtdTrgt12();
            }
            targetBO.setTtdTrgtIncome(value.doubleValue());
            targetBO.setTtdTrgtProfit(value.doubleValue());
            result.add(targetBO);
        }
        return result;
    }

    private Domestic12TargetParamBean get1And2TargetExamineParam(String targetType,List<String> currentList,
                                                                 String d,MetricInfoBean metricInfoBean,
                                                                 GetDomesticTableDataRequestType request) {
        Domestic12TargetParamBean targetParam = new Domestic12TargetParamBean();

        targetParam.setD(d);
        targetParam.setField(request.getDrillDownFilter().getField());
        Integer businessId = request.getBusinessId() == null ? 0 : request.getBusinessId();
        Integer subBusinessId = request.getSubBusinessId() == null ? 5 : request.getSubBusinessId();

        if("examinee".equalsIgnoreCase(targetType)) {
            buildTargetParamBusineeLine(targetParam,businessId,subBusinessId);
            targetParam.setDomainNames(currentList);
        }else {
         //todo 组织目标业务线待加
            buildTargetParamBusineeLine(targetParam,businessId,subBusinessId);
            if("gmv".equalsIgnoreCase(request.getMetricCode())){
                targetParam.setType("1");
            }else {
                targetParam.setType("2");
            }
            if("大区".equals(request.getDrillDownFilter().getField())) {//NOSONAR
                targetParam.setBusinessRegionnameTgt(currentList);
            }else if("省份".equals(request.getDrillDownFilter().getField())) {//NOSONAR
                targetParam.setBusinessProvincenameTgt(currentList);
            }
        }
        targetParam.setYear(request.getTimeFilter().getYear());
        if (request.getTimeFilter().getDateType().equalsIgnoreCase("quarter")) {
            targetParam.setMonthList(DateUtil.getMonthByDateType(request.getTimeFilter().getDateType(), request.getTimeFilter().getQuarter()));
        } else if (request.getTimeFilter().getDateType().equalsIgnoreCase("half")) {
            targetParam.setMonthList(DateUtil.getMonthByDateType(request.getTimeFilter().getDateType(), request.getTimeFilter().getHalf()));
        } else if (request.getTimeFilter().getDateType().equalsIgnoreCase("month")) {
            targetParam.setMonthList(DateUtil.getMonthByMonth(request.getTimeFilter().getMonth()));
        }
        return targetParam;
    }

    private void buildTargetParamBusineeLine(Domestic12TargetParamBean targetParam,Integer businessId, Integer subBusinessId) {
        switch (businessId) {
            case 1:
                targetParam.setBusinessLines(Collections.singletonList("101"));
                break;
            case 2:
                switch (subBusinessId) {
                    case 3:
                        targetParam.setBusinessLines(Arrays.asList("102"));
                        break;
                    case 4:
                        targetParam.setBusinessLines(Arrays.asList("103"));
                        break;
                    case 5:
                        targetParam.setBusinessLines(Arrays.asList("102"));
                        break;
                }
                break;
            case 0:
                targetParam.setBusinessLines(Collections.singletonList("101"));
                targetParam.getBusinessLines().add("102");
                targetParam.getBusinessLines().add("103");
                break;
        }
    }


    private DomesticMetricParamBean get1And2Param(String year,
                                                  List<String> buTypes,
                                                  TimeFilter timeFilter,
                                                  String level, List<String> rangeList,
                                                  DrillDownFilter drillDownFilter, String d,
                                                  List<String> currentResulteList,
                                                  Integer pageIndex,
                                                  Integer pageSize) {
        Boolean isCurrentDate = false;
        LocalDate currentDate = LocalDate.now();
        if (timeFilter.getDateType().equalsIgnoreCase("quarter")) {
            isCurrentDate = DateUtil.isCurrentPeriod(timeFilter.getQuarter());
        } else if (timeFilter.getDateType().equalsIgnoreCase("half")) {
            isCurrentDate = DateUtil.isCurrentPeriod(timeFilter.getHalf());
        } else if (timeFilter.getDateType().equalsIgnoreCase("month")) {
            isCurrentDate = DateUtil.isCurrentPeriod(timeFilter.getMonth());
        }
        DomesticMetricParamBean gmvFieldParam = new DomesticMetricParamBean();
        gmvFieldParam.setBuTypes(buTypes);

        gmvFieldParam.setD(d);
        gmvFieldParam.setPageIndex(pageIndex);
        gmvFieldParam.setPageSize(pageSize);
        gmvFieldParam.setField(drillDownFilter.getField());
        if ("current".equalsIgnoreCase(year)) {
            gmvFieldParam.setUseYear(timeFilter.getYear());
        } else if ("lastYear".equalsIgnoreCase(year)) {
            Integer yearValue = Integer.parseInt(timeFilter.getYear()) - 1;
            gmvFieldParam.setUseYear(String.valueOf(yearValue));
            gmvFieldParam.setPageIndex(null);
            gmvFieldParam.setPageSize(null);
        }

        if (isCurrentDate && "lastYear".equalsIgnoreCase(year)) {
            LocalDate lastYearDate = currentDate.minusYears(1);
            if (timeFilter.getDateType().equalsIgnoreCase("quarter")) {
                gmvFieldParam.setUseDate(DateUtil.getDateRange(timeFilter.getQuarter(), lastYearDate));
            } else if (timeFilter.getDateType().equalsIgnoreCase("half")) {
                gmvFieldParam.setUseDate(DateUtil.getDateRange(timeFilter.getHalf(), lastYearDate));
            } else if (timeFilter.getDateType().equalsIgnoreCase("month")) {
                gmvFieldParam.setUseDate(DateUtil.getDateRange(timeFilter.getMonth(), lastYearDate));
            }else {
                gmvFieldParam.setUseDate(DateUtil.generateDateList(lastYearDate));
            }
        } else {
            if (timeFilter.getDateType().equalsIgnoreCase("quarter")) {
                gmvFieldParam.setUseQuarter(timeFilter.getQuarter());
            } else if (timeFilter.getDateType().equalsIgnoreCase("half")) {
                gmvFieldParam.setUseHalfYear(timeFilter.getHalf());
            } else if (timeFilter.getDateType().equalsIgnoreCase("month")) {
                gmvFieldParam.setUseMonth(timeFilter.getMonth());
            }
        }
        //根据考核范围来
        if ("国内".equalsIgnoreCase(level)) {//NOSONAR
            gmvFieldParam.setBusinessDomainName(level);
        } else if ("大区".equalsIgnoreCase(level)) {//NOSONAR
            gmvFieldParam.setBusinessRegionName(rangeList);
        } else if ("省份".equalsIgnoreCase(level)) {//NOSONAR
            gmvFieldParam.setBusinessSubRegionName(rangeList);
        } else if ("景点".equalsIgnoreCase(level)) {//NOSONAR
            gmvFieldParam.setPrdMeid(rangeList);
            gmvFieldParam.setDomainNames(rangeList);
        }
        if (CollectionUtils.isNotEmpty(drillDownFilter.getFieldValueList())) {
            if ("大区".equalsIgnoreCase(drillDownFilter.getField())) {//NOSONAR
                gmvFieldParam.setBusinessRegionName(drillDownFilter.getFieldValueList());
            } else if ("省份".equalsIgnoreCase(drillDownFilter.getField())) {//NOSONAR
                gmvFieldParam.setBusinessSubRegionName(drillDownFilter.getFieldValueList());
            } else if ("商拓".equalsIgnoreCase(drillDownFilter.getField())) {//NOSONAR
                gmvFieldParam.setDomainNames(drillDownFilter.getFieldValueList());
            } else if ("POI".equalsIgnoreCase(drillDownFilter.getField())) {//NOSONAR
                gmvFieldParam.setVstName(drillDownFilter.getFieldValueList());
            }
        }

        if ("lastYear".equalsIgnoreCase(year) && CollectionUtils.isNotEmpty(currentResulteList)) {
            if ("大区".equalsIgnoreCase(drillDownFilter.getField())) {//NOSONAR
                gmvFieldParam.setBusinessRegionName(currentResulteList);
            } else if ("省份".equalsIgnoreCase(drillDownFilter.getField())) {//NOSONAR
                gmvFieldParam.setBusinessSubRegionName(currentResulteList);
            } else if ("商拓".equalsIgnoreCase(drillDownFilter.getField())) {//NOSONAR
                gmvFieldParam.setDomainNames(currentResulteList);
            } else if ("POI".equalsIgnoreCase(drillDownFilter.getField())) {//NOSONAR
                gmvFieldParam.setVstName(currentResulteList);
            }
        }
        //根据下钻维度来:大区、省份、商拓、POI（仅景点有）
        gmvFieldParam.setExamineLevel(drillDownFilter.getField().toLowerCase());

        return gmvFieldParam;
    }


    public GetDomesticDrillDownBaseInfoResponseType getSingleDrillDownBaseInfo(GetDomesticDrillDownBaseInfoRequestType request,
                                                                               MetricInfoBean metricInfoBean, String d) throws Exception {
        GetDomesticDrillDownBaseInfoResponseType response = new GetDomesticDrillDownBaseInfoResponseType();
        Integer businessId = request.getBusinessId();
        Integer subBusinessId = request.getSubBusinessId();
        List<String> buTypes = new ArrayList<>();
        String level = "";
        List<String> rangeList = null;
        switch (businessId) {
            case 1:
                level = metricInfoBean.getLevel();
                rangeList = metricInfoBean.getRegionList();
                buTypes.add("tkt");
                break;
            case 2:
                switch (subBusinessId) {
                    case 3:
                        buTypes.add("act");
                        level = metricInfoBean.getActLevel();
                        rangeList = metricInfoBean.getActRegionList();
                        break;
                    case 4:
                        buTypes.add("odt");
                        level = metricInfoBean.getOdtLevel();
                        rangeList = metricInfoBean.getOdtRegionList();
                        break;
                    case 5:
                        buTypes.add("act");
                        buTypes.add("odt");
                        level = Bus1And2DayTourHelper.compareLevel(metricInfoBean.getActLevel(), metricInfoBean.getOdtLevel());
                        rangeList = Bus1And2DayTourHelper.compareRangeList(metricInfoBean.getActLevel(), metricInfoBean.getActRegionList(), metricInfoBean.getOdtLevel(), metricInfoBean.getOdtRegionList());
                        break;
                }
        }
        Boolean needSearch = StringUtils.isNotEmpty(request.getSearchField());
        List<String> fieldList = new ArrayList<>();
        if (needSearch) {
            fieldList.add(MetricHelper.getDrillDownColumnName(request.getSearchField()));
        } else {
            fieldList.addAll(Bus1And2DayTourHelper.getDemosticDrillDownFieldList(metricInfoBean, remoteConfig,
                    request.getBusinessId(),level,rangeList));
            response.setDefaultChosenField(MetricHelper.getDataBaseColumnName(fieldList.get(0)));
        }
        List<FieldDataItem> fieldDataItemList = new ArrayList<>();
        for (String field : fieldList) {
            List<String> valueList = null;
            if ("examinee".equalsIgnoreCase(field)) {
                DomesticMetricParamBean gmvFieldParam = get1And2DrillParam(request.getBusinessId(), request.getSubBusinessId(),
                        request.getTimeFilter(), metricInfoBean, MetricHelper.getDataBaseColumnName(field), d);
                gmvFieldParam.setExamineLevel(level);
                gmvFieldParam.setBuTypes(buTypes);
                valueList = cdmOrdTtdDashboardBdDayPerfDfDao.getFieldNameByParam(gmvFieldParam);
            } else {
                DomesticMetricParamBean gmvFieldParam = get1And2DrillParam(request.getBusinessId(), request.getSubBusinessId(),
                        request.getTimeFilter(), metricInfoBean, MetricHelper.getDataBaseColumnName(field), d);
                gmvFieldParam.setBuTypes(buTypes);
                valueList = cdmOrdTtdDashboardExamLevelPerfDfdao.getFieldNameByParam(gmvFieldParam);
            }
            FieldDataItem item = new FieldDataItem();
            item.setNeedBubble(false);
            item.setNeedLine(false);
            item.setField(MetricHelper.getDataBaseColumnName(field));
            DomesticSingelDrillBaseInfo domesticSingelDrillBaseInfo = remoteConfig.getDrillDownFieldBeanV2(request.getMetricCode(), request.getBusinessId(), request.getSubBusinessId(), item.getField());
            if (domesticSingelDrillBaseInfo != null) {
                item.setNeedBubble(domesticSingelDrillBaseInfo.getNeedBubble());
                item.setNeedLine(domesticSingelDrillBaseInfo.getNeedTrend());
            }

            List<FieldValueItem> fieldValueItemList = new ArrayList<>();
            item.setFieldValueItemList(fieldValueItemList);
            valueList.stream().forEach(x -> {
                FieldValueItem fieldValueItem = new FieldValueItem();
                fieldValueItem.setValue(x);
                fieldValueItemList.add(fieldValueItem);
            });
            fieldDataItemList.add(item);

        }
        response.setFieldDataItemList(fieldDataItemList);
        return response;
    }

    private DomesticMetricParamBean get1And2DrillParam(Integer businessId,Integer subBusinessId, TimeFilter timeFilter,MetricInfoBean metricInfoBean ,
                                                       String field, String d) {
        DomesticMetricParamBean gmvFieldParam = new DomesticMetricParamBean();
        switch (businessId) {
            case 1:
                gmvFieldParam.setBuTypes(Arrays.asList("tkt"));
                break;
            case 2:
                switch (subBusinessId) {
                    case 1:
                        gmvFieldParam.setBuTypes(Arrays.asList("act"));
                        break;
                    case 2:
                        gmvFieldParam.setBuTypes(Arrays.asList("odt"));
                        break;
                    case 3:
                        gmvFieldParam.setBuTypes(Arrays.asList("act","odt"));
                        break;
                }
        }

        gmvFieldParam.setD(d);
        gmvFieldParam.setField(field);
        gmvFieldParam.setUseYear(timeFilter.getYear());
        if (timeFilter.getDateType().equalsIgnoreCase("quarter")) {
            gmvFieldParam.setUseQuarter(timeFilter.getQuarter());
        } else if (timeFilter.getDateType().equalsIgnoreCase("half")) {
            gmvFieldParam.setUseHalfYear(timeFilter.getHalf());
        } else if (timeFilter.getDateType().equalsIgnoreCase("month")) {
            gmvFieldParam.setUseMonth(timeFilter.getMonth());
        }
        String level = "";
        List<String> rangeList = new ArrayList<>();
        switch (businessId) {
            case 1:
                level = metricInfoBean.getLevel();
                rangeList = metricInfoBean.getRegionList();
                break;
            case 2:
                switch (subBusinessId) {
                    case 3:
                        level = metricInfoBean.getActLevel();
                        rangeList = metricInfoBean.getActRegionList();
                        break;
                    case 4:
                        level = metricInfoBean.getOdtLevel();
                        rangeList = metricInfoBean.getOdtRegionList();
                        break;
                    case 5:
                        level = Bus1And2DayTourHelper.compareLevel(metricInfoBean.getActLevel(),metricInfoBean.getOdtLevel());
                        rangeList = Bus1And2DayTourHelper.compareRangeList(metricInfoBean.getActLevel(),metricInfoBean.getActRegionList(),metricInfoBean.getOdtLevel(),metricInfoBean.getOdtRegionList());
                        break;
                }
        }
        //根据下钻维度来:大区、省份、商拓、POI（仅景点有）
        gmvFieldParam.setExamineLevel(field.toLowerCase());
        //根据考核范围来
        if ("国内".equalsIgnoreCase(level)) {//NOSONAR
            gmvFieldParam.setBusinessDomainName(level);
        } else if ("大区".equalsIgnoreCase(level)) {//NOSONAR
            gmvFieldParam.setBusinessRegionName(rangeList);
        } else if ("省份".equalsIgnoreCase(level)) {//NOSONAR
            gmvFieldParam.setBusinessSubRegionName(rangeList);
        } else if ("景点".equalsIgnoreCase(level)) {//NOSONAR
            gmvFieldParam.setPrdMeid(rangeList);
            //根据下钻维度来:大区、省份、商拓、POI（仅景点有）
            gmvFieldParam.setExamineLevel("poi");
        }
        return gmvFieldParam;
    }








    public GetFirstPageDomesticMetricCardDrillDataResponseType getFirstPageDomesticMetricCardDrillData(
            GetFirstPageDomesticMetricCardDrillDataRequestType request,MetricInfoBean metricInfoBean, String d) throws Exception{
        GetFirstPageDomesticMetricCardDrillDataResponseType response = new GetFirstPageDomesticMetricCardDrillDataResponseType();
        List<FirstPageDomesticTableData> tableDataItemList = new ArrayList<>();
        response.setTableDataItemList(tableDataItemList);
        List<String> tableHeaderList = Bus1Helper.getCommonHeaderList(request.getDefaultField());
        tableHeaderList.add("completeRate");
        tableHeaderList.add("yoyValue");
        response.setTableHeaderList(tableHeaderList);

        String metric = DomesticMetricEnum.getMetricByCode(request.getMetricCode());
        DrillDownFilter drillDownFilter = new DrillDownFilter();
        drillDownFilter.setField(request.getDefaultField());
        List<String> butypeList = new ArrayList<>();
        String level = "";
        List<String> regionList = new ArrayList<>();
        if (request.getBusinessId() != null) {
            switch (request.getBusinessId()) {
                case 1:
                    butypeList.add("tkt");
                    level = metricInfoBean.getLevel();
                    regionList = metricInfoBean.getRegionList();
                    break;
                case 2:
                    butypeList.add("act");
                    butypeList.add("odt");
                    level = Bus1And2DayTourHelper.compareLevel(metricInfoBean.getActLevel(),metricInfoBean.getOdtLevel());
                    regionList = Bus1And2DayTourHelper.compareRangeList(metricInfoBean.getActLevel(),metricInfoBean.getActRegionList(),metricInfoBean.getOdtLevel(),metricInfoBean.getOdtRegionList());
                    break;
            }
        } else {
            butypeList.add("tkt");
            butypeList.add("act");
            butypeList.add("odt");
            level = Bus1And2DayTourHelper.compareLevel(metricInfoBean.getActLevel(),metricInfoBean.getOdtLevel());
            level = Bus1And2DayTourHelper.compareLevel(level,metricInfoBean.getLevel());
            regionList = Bus1And2DayTourHelper.compareRangeList(metricInfoBean.getActLevel(),metricInfoBean.getActRegionList(),metricInfoBean.getOdtLevel(),metricInfoBean.getOdtRegionList());
            regionList = Bus1And2DayTourHelper.compareRangeList(level,regionList,metricInfoBean.getLevel(),metricInfoBean.getRegionList());
        }

        //获取当前数据
        DomesticMetricParamBean currentParam = get1And2Param("current", butypeList,
                request.getTimeFilter(), level, regionList, drillDownFilter, d, null,null,null);
        currentParam.setMetric(metric);
        List<CdmOrdTtdDashboardExamLevelPerfDfBO> currentResult = cdmOrdTtdDashboardExamLevelPerfDfdao.getTableDataByParam(currentParam);
        List<String> currentResultList = new ArrayList<>();
        if ("大区".equalsIgnoreCase(drillDownFilter.getField())) {//NOSONAR
            currentResultList = currentResult.stream().map(x -> x.getBusinessRegionName()).collect(Collectors.toList());
        } else if ("省份".equalsIgnoreCase(drillDownFilter.getField())) {//NOSONAR
            currentResultList = currentResult.stream().map(x -> x.getBusinessSubRegionName()).collect(Collectors.toList());
        } else if ("商拓".equalsIgnoreCase(drillDownFilter.getField())) {//NOSONAR
            currentResultList = currentResult.stream().map(x -> x.getPrdMeid()).collect(Collectors.toList());
        } else if ("POI".equalsIgnoreCase(drillDownFilter.getField())) {//NOSONAR
            currentResultList = currentResult.stream().map(x -> x.getVstName()).collect(Collectors.toList());
        }
        DomesticMetricParamBean lastYearParam = get1And2Param("lastYear", butypeList,
                request.getTimeFilter(), level, regionList, drillDownFilter, d, currentResultList,null,null);
        currentParam.setMetric(metric);
        List<CdmOrdTtdDashboardExamLevelPerfDfBO> lastYearResult = cdmOrdTtdDashboardExamLevelPerfDfdao.getTableDataByParam(lastYearParam);

        String field = MetricHelper.getDrillDownColumnName(drillDownFilter.getField());

        List<String> tagList = new ArrayList<>();
        List<BiGmvProfitTargetBO> targetResult = null;
        //目标值
        Domestic12TargetParamBean targetParam = new Domestic12TargetParamBean();

        targetParam.setD(d);
        targetParam.setField(drillDownFilter.getField());
        Integer businessId = request.getBusinessId() == null ? 0 : request.getBusinessId();
        List<String> currentList = new ArrayList<>();
        if ("examinee".equals(field)) {
            currentList = currentResult.stream().map((x -> x.getPrdMeid())).collect(Collectors.toList());
        } else if (!"viewspotid".equalsIgnoreCase(field)) {
            if ("region_name".equalsIgnoreCase(field)) {
                currentList = currentResult.stream().map((x -> x.getBusinessRegionName())).collect(Collectors.toList());
            } else if ("province_name".equalsIgnoreCase(field)) {
                currentList = currentResult.stream().map((x -> x.getBusinessSubRegionName())).collect(Collectors.toList());
            }
        }
        targetParam.setBusinessLines(new ArrayList<>());
        switch (businessId) {
            case 1:
                targetParam.setBusinessLines(Collections.singletonList("101"));
                break;
            case 2:
                targetParam.getBusinessLines().add("102");
                targetParam.getBusinessLines().add("103");
                break;
        }
        if("examinee".equalsIgnoreCase(field)) {
            targetParam.setDomainNames(currentList);
        }else {
            if("gmv".equalsIgnoreCase(request.getMetricCode())){
                targetParam.setType("1");
            }else {
                targetParam.setType("2");
            }
            if("大区".equals(drillDownFilter.getField())) {//NOSONAR
                targetParam.setBusinessRegionnameTgt(currentList);
            }else if("省份".equals(drillDownFilter.getField())) {//NOSONAR
                targetParam.setBusinessProvincenameTgt(currentList);
            }
        }
        targetParam.setYear(request.getTimeFilter().getYear());
        if (request.getTimeFilter().getDateType().equalsIgnoreCase("quarter")) {
            targetParam.setMonthList(DateUtil.getMonthByDateType(request.getTimeFilter().getDateType(), request.getTimeFilter().getQuarter()));
        } else if (request.getTimeFilter().getDateType().equalsIgnoreCase("half")) {
            targetParam.setMonthList(DateUtil.getMonthByDateType(request.getTimeFilter().getDateType(), request.getTimeFilter().getHalf()));
        } else if (request.getTimeFilter().getDateType().equalsIgnoreCase("month")) {
            targetParam.setMonthList(DateUtil.getMonthByMonth(request.getTimeFilter().getMonth()));
        }
        if ("examinee".equals(field)) {

            targetParam.setD(d);
             targetResult = boardBiGmvProfitExamineeTargetDao.getExamineeTarget(targetParam);
        } else if (!"viewspotid".equalsIgnoreCase(field)) {
            List<GmvProfitTargetRegionBO> targetResults = boardBiGmvProfitExamineeTargetDao.getGmvRegionTarget(targetParam);
            targetResult = buildByGmvProfitTargetRegionBO(targetParam.getMonthList(), targetResults);
        }

        ChartHelper.buildBus1And2DomesticFirstPageTableData(currentResult, lastYearResult, targetResult, tagList,
                metric, field, response.getTableHeaderList(), tableDataItemList);

        return response;
    }
}
