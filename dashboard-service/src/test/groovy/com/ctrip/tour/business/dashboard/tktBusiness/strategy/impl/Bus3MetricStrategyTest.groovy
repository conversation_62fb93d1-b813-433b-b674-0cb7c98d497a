package com.ctrip.tour.business.dashboard.tktBusiness.strategy.impl

import com.ctrip.platform.dal.dao.DalHints
import com.ctrip.soa._24922.CheckUserPermissionResponseType
import com.ctrip.soa._24922.GetDrillDownBaseInfoRequestType
import com.ctrip.soa._24922.GetTableDataRequestType
import com.ctrip.soa._24922.MetricDetailInfo
import com.ctrip.soa._24922.TimeFilter
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ExamineConfigBean
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean
import com.ctrip.tour.business.dashboard.tktBusiness.biz.SinglePeriodTrendLineBiz
import com.ctrip.tour.business.dashboard.tktBusiness.biz.UserPermissionBiz
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig
import com.ctrip.tour.business.dashboard.tktBusiness.dao.Bus3Dao
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardExamineeConfigV2Dao
import com.ctrip.tour.business.dashboard.utils.MapperUtil
import spock.lang.Specification

class Bus3MetricStrategyTest extends Specification {
    def dao = Mock(Bus3Dao)
    def userPermissionBiz = Mock(UserPermissionBiz)
    def examineeConfigV2Dao = Mock(BusinessDashboardExamineeConfigV2Dao)
    def singlePeriodTrendLineBiz = Mock(SinglePeriodTrendLineBiz)
    def remoteConfig = MapperUtil.str2Obj("{\"configMap\":{\"ttdNegativeRate\":\"差评率\",\"hostName\":\"ws.uploadfile.fx.fws.qa.nt.ctripcorp.com\",\"bd\":\"商拓\",\"destinationT\":\"T站\",\"closeEventType\":\"EVENT_QUALITY_SELFSERVICE_02\",\"vendorId\":\"供应商ID\",\"universalStudios\":\"环球影城\",\"three\":\"三方\",\"childrenOrgIdList\":\"62337|62355|62390|SO001665|SO002322|62402|SO002565|SO004005|SO002566|SO005325\",\"oversea\":\"海外\",\"domestic\":\"国内\",\"province\":\"省份\",\"scenario\":\"tour-bi-business-dashboard-log\",\"closeTaskType\":\"TASK_QUALITY_SELFSERVICE_02\",\"scheduleConfig\":\"[{\\\"sceneType\\\":\\\"PRODUCT_INFO_RECTIFICATION\\\",\\\"isSplitScene\\\":true,\\\"className\\\":\\\"com.ctrip.tour.business.dashboard.bean.ProductInfoRecificationBean\\\",\\\"splitType\\\":1,\\\"splitSceneMap\\\":{\\\"supplier\\\":{\\\"sceneType\\\":\\\"PRODUCT_INFO_RECTIFICATION_SUPPLIER_DELIVERY\\\",\\\"className\\\":\\\"com.ctrip.tour.business.dashboard.bean.ProductInfoRecificationBean\\\",\\\"messageType\\\":1,\\\"title\\\":\\\"供应商产品信息整改\\\",\\\"taskType\\\":\\\"TASK_SUPPLIER_REWARD_PUNISHMENT_01\\\",\\\"taskName\\\":\\\"商品质量信息整改\\\",\\\"eventType\\\":\\\"EVENT_SUPPLIER_REWARD_PUNISHMENT_01\\\",\\\"serviceNumInfo\\\":{\\\"contentType\\\":1,\\\"length\\\":4000,\\\"templateStr\\\":\\\"您有%s家供应商产品信息需整改，可点击查看详情\\\",\\\"isplace\\\":true,\\\"templateList\\\":[\\\"count\\\"]},\\\"detailInfo\\\":{\\\"contentType\\\":2,\\\"length\\\":4000,\\\"templateStr\\\":\\\"您名下供应商有产品将被处理，如有异议请联系供应商及时申诉\\\",\\\"isplace\\\":false,\\\"headers\\\":{\\\"供应商\\\":[\\\"vendorId\\\",\\\"vendorName\\\"],\\\"违规原因\\\":[\\\"problemType\\\"],\\\"产品\\\":[\\\"productId\\\",\\\"productName\\\"],\\\"最晚整改完成时间\\\":[\\\"rectificationDeadline\\\"],\\\"申诉链接\\\":[\\\"problemUrl\\\"]}}},\\\"business\\\":{\\\"messageType\\\":1,\\\"sceneType\\\":\\\"PRODUCT_INFO_RECTIFICATION_BUSINESS_DELIVERY\\\",\\\"className\\\":\\\"com.ctrip.tour.business.dashboard.bean.ProductInfoRecificationBean\\\",\\\"title\\\":\\\"产品信息整改\\\",\\\"taskType\\\":\\\"TASK_SUPPLIER_REWARD_PUNISHMENT_01\\\",\\\"taskName\\\":\\\"商品质量信息整改\\\",\\\"eventType\\\":\\\"EVENT_SUPPLIER_REWARD_PUNISHMENT_04\\\",\\\"serviceNumInfo\\\":{\\\"contentType\\\":1,\\\"length\\\":4000,\\\"templateStr\\\":\\\"您有%s家供应商产品信息需整改，可点击查看详情\\\",\\\"isplace\\\":true,\\\"templateList\\\":[\\\"count\\\"]},\\\"detailInfo\\\":{\\\"contentType\\\":2,\\\"length\\\":4000,\\\"templateStr\\\":\\\"您名下产品将被处理，如有异议请及时申诉\\\",\\\"isplace\\\":false,\\\"headers\\\":{\\\"供应商\\\":[\\\"vendorId\\\",\\\"vendorName\\\"],\\\"违规原因\\\":[\\\"problemType\\\"],\\\"产品\\\":[\\\"productId\\\",\\\"productName\\\"],\\\"最晚整改完成时间\\\":[\\\"rectificationDeadline\\\"],\\\"申诉链接\\\":[\\\"problemUrl\\\"]}}}}},{\\\"sceneType\\\":\\\"PRODUCT_INFO_APPEAL_PASSED\\\",\\\"isSplitScene\\\":true,\\\"className\\\":\\\"com.ctrip.tour.business.dashboard.bean.ProductInfoAppealPassedBean\\\",\\\"splitType\\\":1,\\\"splitSceneMap\\\":{\\\"supplier\\\":{\\\"sceneType\\\":\\\"PRODUCT_INFO_APPEAL_PASSED_SUPPLIER_DELIVERY\\\",\\\"className\\\":\\\"com.ctrip.tour.business.dashboard.bean.ProductInfoAppealPassedBean\\\",\\\"messageType\\\":1,\\\"title\\\":\\\"供应商产品信息申诉通过\\\",\\\"taskType\\\":\\\"TASK_SUPPLIER_REWARD_PUNISHMENT_02\\\",\\\"taskName\\\":\\\"商品质量信息申诉\\\",\\\"eventType\\\":\\\"EVENT_SUPPLIER_REWARD_PUNISHMENT_02\\\",\\\"serviceNumInfo\\\":{\\\"contentType\\\":1,\\\"length\\\":4000,\\\"templateStr\\\":\\\"您有%s家供应商产品信息整改申诉通过，可点击查看详情\\\",\\\"isplace\\\":true,\\\"templateList\\\":[\\\"count\\\"]},\\\"detailInfo\\\":{\\\"contentType\\\":2,\\\"length\\\":4000,\\\"templateStr\\\":\\\"您名下供应商产品信息申诉已通过，如供应商重新编辑产品，该产品将从白名单中剔除，重新触发数据监控\\\",\\\"isplace\\\":false,\\\"headers\\\":{\\\"供应商\\\":[\\\"vendorId\\\",\\\"vendorName\\\"],\\\"产品\\\":[\\\"productId\\\",\\\"productName\\\"]}}},\\\"business\\\":{\\\"messageType\\\":1,\\\"sceneType\\\":\\\"PRODUCT_INFO_APPEAL_PASSED_BUSINESS_DELIVERY\\\",\\\"className\\\":\\\"com.ctrip.tour.business.dashboard.bean.ProductInfoAppealPassedBean\\\",\\\"title\\\":\\\"产品信息申诉通过\\\",\\\"taskType\\\":\\\"TASK_SUPPLIER_REWARD_PUNISHMENT_02\\\",\\\"taskName\\\":\\\"商品质量信息申诉\\\",\\\"eventType\\\":\\\"EVENT_SUPPLIER_REWARD_PUNISHMENT_05\\\",\\\"serviceNumInfo\\\":{\\\"contentType\\\":1,\\\"length\\\":4000,\\\"templateStr\\\":\\\"您有%s家供应商产品信息整改申诉通过，可点击查看详情\\\",\\\"isplace\\\":true,\\\"templateList\\\":[\\\"count\\\"]},\\\"detailInfo\\\":{\\\"contentType\\\":2,\\\"length\\\":4000,\\\"templateStr\\\":\\\"您名下产品信息申诉已通过，如果您重新编辑产品，该产品将从白名单中剔除，重新触发数据监控\\\",\\\"isplace\\\":false,\\\"headers\\\":{\\\"供应商\\\":[\\\"vendorId\\\",\\\"vendorName\\\"],\\\"产品\\\":[\\\"productId\\\",\\\"productName\\\"]}}}}},{\\\"sceneType\\\":\\\"PRODUCT_INFO_APPEAL_DENIED\\\",\\\"isSplitScene\\\":true,\\\"className\\\":\\\"com.ctrip.tour.business.dashboard.bean.ProductInfoAppealDeniedBean\\\",\\\"splitType\\\":1,\\\"splitSceneMap\\\":{\\\"supplier\\\":{\\\"sceneType\\\":\\\"PRODUCT_INFO_APPEAL_DENIED_SUPPLIER_DELIVERY\\\",\\\"className\\\":\\\"com.ctrip.tour.business.dashboard.bean.ProductInfoAppealDeniedBean\\\",\\\"messageType\\\":1,\\\"title\\\":\\\"供应商产品信息申诉驳回\\\",\\\"taskType\\\":\\\"TASK_SUPPLIER_REWARD_PUNISHMENT_02\\\",\\\"taskName\\\":\\\"商品质量信息申诉\\\",\\\"eventType\\\":\\\"EVENT_SUPPLIER_REWARD_PUNISHMENT_03\\\",\\\"serviceNumInfo\\\":{\\\"contentType\\\":1,\\\"length\\\":4000,\\\"templateStr\\\":\\\"您有%s家供应商产品信息整改申诉驳回，可点击查看详情\\\",\\\"isplace\\\":true,\\\"templateList\\\":[\\\"count\\\"]},\\\"detailInfo\\\":{\\\"contentType\\\":2,\\\"length\\\":4000,\\\"templateStr\\\":\\\"您名下供应商产品信息申诉已驳回，请提醒供应商在改正期限内修改完成\\\",\\\"isplace\\\":false,\\\"headers\\\":{\\\"供应商\\\":[\\\"vendorId\\\",\\\"vendorName\\\"],\\\"产品\\\":[\\\"productId\\\",\\\"productName\\\"],\\\"驳回理由\\\":[\\\"rejectReason\\\"],\\\"改正期限\\\":[\\\"rectificationDeadline\\\"]}}},\\\"business\\\":{\\\"messageType\\\":2,\\\"sceneType\\\":\\\"PRODUCT_INFO_APPEAL_DENIED_BUSINESS_DELIVERY\\\",\\\"className\\\":\\\"com.ctrip.tour.business.dashboard.bean.ProductInfoAppealDeniedBean\\\",\\\"title\\\":\\\"产品信息申诉驳回\\\",\\\"taskType\\\":\\\"TASK_SUPPLIER_REWARD_PUNISHMENT_02\\\",\\\"taskName\\\":\\\"商品质量信息申诉\\\",\\\"eventType\\\":\\\"EVENT_SUPPLIER_REWARD_PUNISHMENT_06\\\",\\\"serviceNumInfo\\\":{\\\"contentType\\\":1,\\\"length\\\":4000,\\\"templateStr\\\":\\\"产品：%s<br/><br/>改正期限：%s<br/><br/>驳回原因：%s\\\",\\\"isplace\\\":true,\\\"templateList\\\":[\\\"product[ID+Name]\\\",\\\"changeTimeLimit\\\",\\\"rejectReason\\\"]},\\\"detailInfo\\\":{\\\"contentType\\\":1,\\\"length\\\":4000,\\\"templateStr\\\":\\\"您名下产品信息申诉已驳回，请提醒供应商在改正期限内修改完成<br/><br/>产品：%s<br/><br/>改正期限：%s<br/><br/>驳回原因：%s\\\",\\\"isplace\\\":true,\\\"templateList\\\":[\\\"product[ID+Name]\\\",\\\"changeTimeLimit\\\",\\\"rejectReason\\\"]}}}}]\",\"warnTitle\":\"您名下供应商在%s月份的考核中低于平台预警考核线标准，已被预警。平台预警线：供应商的120s回复率需大于等于%s，差评率需小于等于%s。请在%s之前及时提醒供应商提升服务质量，否则供应商将会被关停自服务。\",\"isPushSelfServiceQuality\":\"true\",\"isPushSceneMessage\":\"true\",\"fileChannel\":\"tour_business_dashboard\",\"warnEventType\":\"EVENT_QUALITY_SELFSERVICE_01\",\"closeTaskName\":\"%s月自服务红线考核供应商管控\",\"subRegion\":\"子区域\",\"closeServiceNumTitle\":\"您有%s家供应商自服务考核不达标已被关闭资格，请及时查看~\",\"warnServiceNumTitle\":\"您有%s家供应商当月自服务质量较差已预警，请及时查看~\",\"targetManageUrlPreffix\":\"http://product.activity.fat29.qa.nt.ctripcorp.com/ttd-operation/targetManage\",\"monthName\":\"%s月GMV\",\"warnTaskName\":\"%s月自服务红线考核供应商预警\",\"vendorName\":\"供应商名称\",\"provinceId\":\"-9999|5001|6001|7001|8001\",\"kaRegion\":\"KA大区\",\"isSendServiceNum\":\"true\",\"ttdTimelyRate\":\"120S回复率\",\"hufuUrl\":\"https://kms-server.fat70.tripqate.com\",\"viewspot\":\"景点\",\"profileRedisName\":\"ttd_bi_profile_redis\",\"getMemberInfoUrlSecret\":\"f2d14fc36fbc1d44e394d1a2854a4aa3d8\",\"parentOrgId\":\"SO001057\",\"excludeOrgId\":\"62431\",\"getMemberInfoTokenSecret\":\"f23fd1b7bffa784765af536ac01f984973\",\"totalSessionCount\":\"总会话量\",\"redisNameSpace\":\"tour.dashboardservice.qmq\",\"destinationC\":\"C站\",\"warnTaskType\":\"TASK_QUALITY_SELFSERVICE_01\",\"region\":\"大区\",\"closeTitle\":\"您名下供应商在%s月自服务考核不达标，现于%s起关闭自服务%s天，%s天后可自助申请开通。平台考核标准：供应商的120s回复率需大于等于%s，差评率需小于等于%s。\"},\"employeeRelationMap\":{\"TR837942\":\"S35097\",\"TR018471\":\"S35097\",\"TR003932\":\"S35097\",\"hr003\":\"D03807\",\"TR016434\":\"D03807\",\"TR019403\":\"S35097\",\"TR015613\":\"S04850\",\"S58720\":\"D00024\",\"TR005148\":\"S35097\",\"S51074\":\"D03807\",\"TR032913\":\"S35097\",\"S58662\":\"D00024\",\"S75483\":\"D00024\",\"S78157\":\"D00024\",\"S72490\":\"S35097\",\"S78177\":\"D00024\",\"S52246\":\"D00024\",\"TR020683\":\"S35097\",\"S74773\":\"S08033\",\"S79743\":\"S08033\",\"S22035\":\"S52754\",\"S74975\":\"S08033\",\"TR019390\":\"D00024\",\"S68332\":\"D00024\",\"TR015896\":\"D00024\",\"S28153\":\"S35097\",\"TR020409\":\"S35097\",\"TR034098\":\"D03807\",\"S41524\":\"S08033\",\"S39497\":\"D00024\",\"TR018838\":\"S35097\",\"TR032346\":\"D03977\",\"S68368\":\"D00024\",\"TR013600\":\"G03351\",\"S64787\":\"S35097\",\"TR021161\":\"D00024\",\"S79971\":\"D00024\",\"S20149\":\"S08033\",\"TR009198\":\"S35097\",\"S79530\":\"S35097\",\"S76285\":\"D00024\"},\"pkEmployeeRelationMap\":{\"TR003932\":\"TR018551\",\"TR016434\":\"TR018551\",\"TR009198\":\"TR018551\",\"S79530\":\"TR018551\"},\"externalConfigMap\":{\"adminOrgId\":\"41999\",\"overseaDeptId\":\"SO003287\",\"specailRegionOperate\":\"wsong\",\"regionRankId\":\"62402|62355|62337|SO002322|62390|SO001665\",\"halfDomainName\":\"chang.liu|leisun\",\"admin\":\"S35097\",\"adminInferior\":\"D00024|S52754\",\"specialRegionId\":\"62390\",\"domesticDeptId\":\"SO001057\",\"regionalManager\":\"{\\\"S04850\\\":\\\"KA大区|62402\\\",\\\"S21558\\\":\\\"云南大区|SO002322\\\",\\\"D01984\\\":\\\"华西大区|62390\\\",\\\"D02161\\\":\\\"华东大区|62337\\\",\\\"B00304\\\":\\\"华北大区|SO001665\\\",\\\"G03351\\\":\\\"华南大区|62355\\\"}\"},\"organizationOrderMap\":{\"SO001057\":\"62337|62355|62390|SO001665|SO002322|62402|SO002565|SO004005|SO002566|SO005325\",\"SO002322\":\"SO002326|SO002328|SO002325|SO002323|SO002324\"},\"overseaDrillDownMetadata\":{\"metricMetadataList\":[{\"metricList\":[\"101\",\"102\"],\"subMetricFieldList\":[{\"subMetricList\":[\"destination\",\"destination_c\",\"destination_t\"],\"field\":\"region\",\"baseInfoId\":6,\"baseInfoGroupList\":[\"region_id\",\"region_name\"],\"baseInfoLikeIndexList\":[1],\"needBubble\":true,\"tableDataIdMap\":{\"other\":6,\"target\":1},\"bubbleGroupListMap\":{\"other\":[\"region_id\",\"region_name\"],\"target\":[\"region_id\"]},\"conditionColumn\":\"region_id\",\"needTarget\":true,\"headerFieldList\":[\"region_name\"],\"tableGroupListMap\":{\"other\":[\"region_id\",\"region_name\"],\"target\":[\"region_id\"]},\"showField\":\"region_name\",\"showFieldId\":\"region_id\"},{\"subMetricList\":[\"destination\",\"destination_c\",\"destination_t\"],\"field\":\"province\",\"baseInfoId\":6,\"baseInfoGroupList\":[\"province_id\",\"province_name\"],\"baseInfoLikeIndexList\":[1],\"needBubble\":true,\"tableDataIdMap\":{\"other\":6,\"target\":1},\"bubbleGroupListMap\":{\"other\":[\"province_id\",\"province_name\"],\"target\":[\"province_id\"]},\"conditionColumn\":\"province_id\",\"needTarget\":true,\"headerFieldList\":[\"region_name\",\"province_name\"],\"tableGroupListMap\":{\"other\":[\"region_id\",\"region_name\",\"province_id\",\"province_name\"],\"target\":[\"region_id\",\"province_id\"]},\"showField\":\"province_name\",\"showFieldId\":\"province_id\"},{\"subMetricList\":[\"destination\",\"destination_c\",\"destination_t\"],\"field\":\"country\",\"baseInfoId\":6,\"baseInfoGroupList\":[\"country_id\",\"country_name\"],\"baseInfoLikeIndexList\":[1],\"needBubble\":false,\"tableDataIdMap\":{\"other\":6},\"conditionColumn\":\"country_id\",\"needTarget\":false,\"headerFieldList\":[\"region_name\",\"province_name\",\"country_name\"],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\",\"country_id\",\"country_name\"]}},{\"subMetricList\":[\"destination\",\"destination_c\",\"destination_t\"],\"field\":\"examinee\",\"baseInfoId\":12,\"baseInfoGroupList\":[\"examinee\",\"examinee_display\"],\"baseInfoLikeIndexList\":[1],\"needBubble\":false,\"tableDataIdMap\":{\"2019\":6,\"target\":4,\"lastyear\":6,\"current\":12},\"conditionColumn\":\"examinee\",\"needTarget\":true,\"headerFieldList\":[\"region_name\",\"province_name\",\"examinee_display\"],\"tableGroupListMap\":{\"2019\":[\"region_id\",\"province_id\"],\"target\":[\"region_id\",\"province_id\",\"examinee\"],\"current\":[\"region_name\",\"region_id\",\"province_name\",\"province_id\",\"examinee_display\",\"examinee\"],\"lastyear\":[\"region_id\",\"province_id\"]},\"pagingConditionColumn\":\"province_id\"},{\"subMetricList\":[\"destination\",\"destination_c\",\"destination_t\"],\"field\":\"viewspot\",\"baseInfoId\":10,\"baseInfoGroupList\":[\"viewspot_id\",\"viewspot_name\"],\"baseInfoLikeIndexList\":[0,1],\"needBubble\":false,\"tableDataIdMap\":{\"current\":10,\"other\":11},\"conditionColumn\":\"viewspot_id\",\"needTarget\":false,\"headerFieldList\":[\"region_name\",\"province_name\",\"country_name\",\"viewspot_id\",\"viewspot_name\"],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\",\"country_name\",\"viewspot_id\",\"viewspot_name\"]}},{\"subMetricList\":[\"site\"],\"field\":\"site\",\"baseInfoId\":9,\"baseInfoGroupList\":[\"site_code\",\"site\"],\"baseInfoLikeIndexList\":[1],\"needBubble\":true,\"tableDataIdMap\":{\"other\":9,\"target\":2},\"bubbleGroupListMap\":{\"other\":[\"site_code\",\"site\"],\"target\":[\"site_code\"]},\"conditionColumn\":\"site_code\",\"needTarget\":true,\"headerFieldList\":[\"site\"],\"tableGroupListMap\":{\"other\":[\"site_code\",\"site\"],\"target\":[\"site_code\"]},\"showField\":\"site\",\"showFieldId\":\"site_code\"},{\"subMetricList\":[\"site\"],\"field\":\"locale\",\"baseInfoId\":9,\"baseInfoGroupList\":[\"locale_code\",\"locale\"],\"baseInfoLikeIndexList\":[1],\"needBubble\":false,\"tableDataIdMap\":{\"other\":9},\"conditionColumn\":\"locale_code\",\"needTarget\":false,\"headerFieldList\":[\"site\",\"locale\"],\"tableGroupListMap\":{\"other\":[\"site\",\"locale_code\",\"locale\"]}},{\"subMetricList\":[\"channel\"],\"field\":\"channel\",\"baseInfoId\":9,\"baseInfoGroupList\":[\"channel_code\",\"channel_name\"],\"baseInfoLikeIndexList\":[1],\"needBubble\":true,\"tableDataIdMap\":{\"other\":9,\"target\":3},\"bubbleGroupListMap\":{\"other\":[\"channel_code\",\"channel_name\"],\"target\":[\"channel_code\"]},\"conditionColumn\":\"channel_code\",\"needTarget\":true,\"headerFieldList\":[\"channel_name\"],\"tableGroupListMap\":{\"other\":[\"channel_code\",\"channel_name\"],\"target\":[\"channel_code\"]},\"showField\":\"channel_name\",\"showFieldId\":\"channel_code\"},{\"subMetricList\":[\"channel\"],\"field\":\"site\",\"baseInfoId\":9,\"baseInfoGroupList\":[\"site_code\",\"site\"],\"baseInfoLikeIndexList\":[1],\"needBubble\":false,\"tableDataIdMap\":{\"other\":9},\"conditionColumn\":\"site_code\",\"needTarget\":false,\"headerFieldList\":[\"channel_name\",\"site\"],\"tableGroupListMap\":{\"other\":[\"channel_name\",\"site\",\"site_code\"]}},{\"subMetricList\":[\"channel\"],\"field\":\"locale\",\"baseInfoId\":9,\"baseInfoGroupList\":[\"locale_code\",\"locale\"],\"baseInfoLikeIndexList\":[1],\"needBubble\":false,\"tableDataIdMap\":{\"other\":9},\"conditionColumn\":\"locale_code\",\"needTarget\":false,\"headerFieldList\":[\"channel_name\",\"site\",\"locale\"],\"tableGroupListMap\":{\"other\":[\"channel_name\",\"site\",\"locale_code\",\"locale\"]}}]},{\"metricList\":[\"103\"],\"subMetricFieldList\":[{\"subMetricList\":[\"destination_c\",\"destination_t\"],\"field\":\"region\",\"baseInfoId\":32,\"baseInfoGroupList\":[\"region_id\",\"region_name\"],\"baseInfoLikeIndexList\":[1],\"needLine\":true,\"tableDataIdMap\":{\"other\":32,\"target\":38},\"lineGroupListMap\":{\"current\":[\"region_name\"]},\"conditionColumn\":\"region_id\",\"needTarget\":true,\"headerFieldList\":[\"region_name\"],\"tableGroupListMap\":{\"other\":[\"region_id\",\"region_name\"],\"target\":[\"region_id\"]},\"showField\":\"region_name\",\"showFieldId\":\"region_id\"},{\"subMetricList\":[\"destination_c\",\"destination_t\"],\"field\":\"province\",\"baseInfoId\":32,\"baseInfoGroupList\":[\"province_id\",\"province_name\"],\"baseInfoLikeIndexList\":[1],\"needLine\":true,\"tableDataIdMap\":{\"other\":32,\"target\":38},\"lineGroupListMap\":{\"current\":[\"province_name\"]},\"conditionColumn\":\"province_id\",\"needTarget\":true,\"headerFieldList\":[\"region_name\",\"province_name\"],\"tableGroupListMap\":{\"other\":[\"region_id\",\"region_name\",\"province_id\",\"province_name\"],\"target\":[\"region_id\",\"province_id\"]},\"showField\":\"province_name\",\"showFieldId\":\"province_id\"},{\"subMetricList\":[\"destination_c\",\"destination_t\"],\"field\":\"country\",\"baseInfoId\":32,\"baseInfoGroupList\":[\"country_id\",\"country_name\"],\"baseInfoLikeIndexList\":[1],\"needLine\":false,\"tableDataIdMap\":{\"other\":32},\"conditionColumn\":\"country_id\",\"needTarget\":false,\"headerFieldList\":[\"region_name\",\"province_name\",\"country_name\"],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\",\"country_id\",\"country_name\"]}},{\"subMetricList\":[\"destination_c\",\"destination_t\"],\"field\":\"examinee\",\"baseInfoId\":33,\"baseInfoGroupList\":[\"examinee\",\"examinee_display\"],\"baseInfoLikeIndexList\":[1],\"needLine\":false,\"tableDataIdMap\":{\"other\":33,\"target\":38},\"conditionColumn\":\"examinee\",\"needTarget\":true,\"headerFieldList\":[\"region_name\",\"province_name\",\"examinee_display\"],\"tableGroupListMap\":{\"other\":[\"region_name\",\"region_id\",\"province_name\",\"province_id\",\"examinee_display\",\"examinee\"],\"target\":[\"region_id\",\"province_id\"]}},{\"subMetricList\":[\"destination_c\",\"destination_t\"],\"field\":\"viewspot\",\"baseInfoId\":34,\"baseInfoGroupList\":[\"viewspot_id\",\"viewspot_name\"],\"baseInfoLikeIndexList\":[0,1],\"needLine\":false,\"tableDataIdMap\":{\"other\":34},\"conditionColumn\":\"viewspot_id\",\"needTarget\":false,\"headerFieldList\":[\"region_name\",\"province_name\",\"country_name\",\"viewspot_id\",\"viewspot_name\"],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\",\"country_name\",\"viewspot_id\",\"viewspot_name\"]}},{\"subMetricList\":[\"destination_c\",\"destination_t\"],\"field\":\"vendor\",\"baseInfoId\":35,\"baseInfoGroupList\":[\"vendor_id\",\"vendor_name\"],\"baseInfoLikeIndexList\":[0,1],\"needLine\":false,\"tableDataIdMap\":{\"other\":35},\"conditionColumn\":\"vendor_id\",\"needTarget\":false,\"headerFieldList\":[\"region_name\",\"province_name\",\"vendor_id\",\"vendor_name\"],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\",\"vendor_id\",\"vendor_name\"]}},{\"subMetricList\":[\"site\"],\"field\":\"site\",\"baseInfoId\":37,\"baseInfoGroupList\":[\"site_code\",\"site\"],\"baseInfoLikeIndexList\":[1],\"needLine\":true,\"tableDataIdMap\":{\"other\":37,\"target\":39},\"lineGroupListMap\":{\"current\":[\"site\"]},\"conditionColumn\":\"site_code\",\"needTarget\":true,\"headerFieldList\":[\"site\"],\"tableGroupListMap\":{\"other\":[\"site_code\",\"site\"],\"target\":[\"site_code\"]},\"showField\":\"site\",\"showFieldId\":\"site_code\"},{\"subMetricList\":[\"site\"],\"field\":\"viewspot\",\"baseInfoId\":36,\"baseInfoGroupList\":[\"viewspot_id\",\"viewspot_name\"],\"baseInfoLikeIndexList\":[0,1],\"needLine\":false,\"tableDataIdMap\":{\"other\":36},\"conditionColumn\":\"viewspot_id\",\"needTarget\":false,\"headerFieldList\":[\"site\",\"viewspot_id\",\"viewspot_name\"],\"tableGroupListMap\":{\"other\":[\"site\",\"viewspot_id\",\"viewspot_name\"]}},{\"subMetricList\":[\"site\"],\"field\":\"vendor\",\"baseInfoId\":37,\"baseInfoGroupList\":[\"vendor_id\",\"vendor_name\"],\"baseInfoLikeIndexList\":[0,1],\"needLine\":false,\"tableDataIdMap\":{\"other\":37},\"conditionColumn\":\"vendor_id\",\"needTarget\":false,\"headerFieldList\":[\"site\",\"vendor_id\",\"vendor_name\"],\"tableGroupListMap\":{\"other\":[\"site\",\"vendor_id\",\"vendor_name\"]}}]},{\"metricList\":[\"105\",\"106\",\"107\"],\"subMetricFieldList\":[{\"subMetricList\":[\"tklk\",\"tfly\"],\"field\":\"region\",\"baseInfoGroupList\":[\"region_id\",\"region_name\"],\"baseInfoLikeIndexList\":[1],\"needLine\":true,\"lineGroupListMap\":{\"current\":[\"region_name\"]},\"conditionColumn\":\"region_id\",\"headerFieldList\":[\"region_name\"],\"tableGroupListMap\":{\"other\":[\"region_id\",\"region_name\"]},\"showField\":\"region_name\",\"showFieldId\":\"region_id\"},{\"subMetricList\":[\"tklk\",\"tfly\"],\"field\":\"province\",\"baseInfoGroupList\":[\"province_id\",\"province_name\"],\"baseInfoLikeIndexList\":[1],\"needLine\":true,\"lineGroupListMap\":{\"current\":[\"province_name\"]},\"conditionColumn\":\"province_id\",\"headerFieldList\":[\"region_name\",\"province_name\"],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\"]},\"showField\":\"province_name\",\"showFieldId\":\"province_id\"},{\"subMetricList\":[\"tklk\",\"tfly\"],\"field\":\"country\",\"baseInfoGroupList\":[\"country_id\",\"country_name\"],\"baseInfoLikeIndexList\":[1],\"needLine\":false,\"conditionColumn\":\"country_id\",\"headerFieldList\":[\"region_name\",\"province_name\",\"country_name\"],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\",\"country_name\"]}},{\"subMetricList\":[\"tklk\",\"tfly\"],\"field\":\"examinee\",\"baseInfoGroupList\":[\"examinee_display\",\"examinee\"],\"baseInfoLikeIndexList\":[1],\"needLine\":false,\"conditionColumn\":\"examinee\",\"headerFieldList\":[\"region_name\",\"province_name\",\"examinee_display\"],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\",\"examinee_display\"]}}]},{\"year\":\"2024\",\"metricList\":[\"103\"],\"subMetricFieldList\":[{\"subMetricList\":[\"destination_c\",\"destination_t\"],\"field\":\"region\",\"baseInfoId\":62,\"baseInfoGroupList\":[\"region_id\",\"region_name\"],\"baseInfoLikeIndexList\":[1],\"needLine\":true,\"tableDataIdMap\":{\"other\":62,\"target\":70},\"lineGroupListMap\":{\"current\":[\"region_name\"]},\"conditionColumn\":\"region_id\",\"needTarget\":true,\"headerFieldList\":[\"region_name\"],\"tableGroupListMap\":{\"other\":[\"region_id\",\"region_name\"],\"target\":[\"region_id\"]},\"showField\":\"region_name\",\"showFieldId\":\"region_id\"},{\"subMetricList\":[\"destination_c\",\"destination_t\"],\"field\":\"province\",\"baseInfoId\":62,\"baseInfoGroupList\":[\"province_id\",\"province_name\"],\"baseInfoLikeIndexList\":[1],\"needLine\":true,\"tableDataIdMap\":{\"other\":62,\"target\":70},\"lineGroupListMap\":{\"current\":[\"province_name\"]},\"conditionColumn\":\"province_id\",\"needTarget\":true,\"headerFieldList\":[\"region_name\",\"province_name\"],\"tableGroupListMap\":{\"other\":[\"region_id\",\"region_name\",\"province_id\",\"province_name\"],\"target\":[\"region_id\",\"province_id\"]},\"showField\":\"province_name\",\"showFieldId\":\"province_id\"},{\"subMetricList\":[\"destination_c\",\"destination_t\"],\"field\":\"country\",\"baseInfoId\":62,\"baseInfoGroupList\":[\"country_id\",\"country_name\"],\"baseInfoLikeIndexList\":[1],\"needLine\":false,\"tableDataIdMap\":{\"other\":62},\"conditionColumn\":\"country_id\",\"needTarget\":false,\"headerFieldList\":[\"region_name\",\"province_name\",\"country_name\"],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\",\"country_id\",\"country_name\"]}},{\"subMetricList\":[\"destination_c\",\"destination_t\"],\"field\":\"examinee\",\"baseInfoId\":63,\"baseInfoGroupList\":[\"examinee\",\"examinee_display\"],\"baseInfoLikeIndexList\":[1],\"needLine\":false,\"tableDataIdMap\":{\"other\":63,\"target\":70},\"conditionColumn\":\"examinee\",\"needTarget\":true,\"headerFieldList\":[\"region_name\",\"province_name\",\"examinee_display\"],\"tableGroupListMap\":{\"other\":[\"region_name\",\"region_id\",\"province_name\",\"province_id\",\"examinee_display\",\"examinee\"],\"target\":[\"region_id\",\"province_id\"]}},{\"subMetricList\":[\"destination_c\",\"destination_t\"],\"field\":\"viewspot\",\"baseInfoId\":64,\"baseInfoGroupList\":[\"viewspot_id\",\"viewspot_name\"],\"baseInfoLikeIndexList\":[0,1],\"needLine\":false,\"tableDataIdMap\":{\"other\":64},\"conditionColumn\":\"viewspot_id\",\"needTarget\":false,\"headerFieldList\":[\"region_name\",\"province_name\",\"country_name\",\"viewspot_id\",\"viewspot_name\"],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\",\"country_name\",\"viewspot_id\",\"viewspot_name\"]}},{\"subMetricList\":[\"destination_c\",\"destination_t\"],\"field\":\"vendor\",\"baseInfoId\":65,\"baseInfoGroupList\":[\"vendor_id\",\"vendor_name\"],\"baseInfoLikeIndexList\":[0,1],\"needLine\":false,\"tableDataIdMap\":{\"other\":65},\"conditionColumn\":\"vendor_id\",\"needTarget\":false,\"headerFieldList\":[\"region_name\",\"province_name\",\"vendor_id\",\"vendor_name\"],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\",\"vendor_id\",\"vendor_name\"]}},{\"subMetricList\":[\"site\"],\"field\":\"site\",\"baseInfoId\":67,\"baseInfoGroupList\":[\"site_code\",\"site\"],\"baseInfoLikeIndexList\":[1],\"needLine\":true,\"tableDataIdMap\":{\"other\":67,\"target\":71},\"lineGroupListMap\":{\"current\":[\"site\"]},\"conditionColumn\":\"site_code\",\"needTarget\":true,\"headerFieldList\":[\"site\"],\"tableGroupListMap\":{\"other\":[\"site_code\",\"site\"],\"target\":[\"site_code\"]},\"showField\":\"site\",\"showFieldId\":\"site_code\"},{\"subMetricList\":[\"site\"],\"field\":\"viewspot\",\"baseInfoId\":66,\"baseInfoGroupList\":[\"viewspot_id\",\"viewspot_name\"],\"baseInfoLikeIndexList\":[0,1],\"needLine\":false,\"tableDataIdMap\":{\"other\":66},\"conditionColumn\":\"viewspot_id\",\"needTarget\":false,\"headerFieldList\":[\"site\",\"viewspot_id\",\"viewspot_name\"],\"tableGroupListMap\":{\"other\":[\"site\",\"viewspot_id\",\"viewspot_name\"]}},{\"subMetricList\":[\"site\"],\"field\":\"vendor\",\"baseInfoId\":67,\"baseInfoGroupList\":[\"vendor_id\",\"vendor_name\"],\"baseInfoLikeIndexList\":[0,1],\"needLine\":false,\"tableDataIdMap\":{\"other\":67},\"conditionColumn\":\"vendor_id\",\"needTarget\":false,\"headerFieldList\":[\"site\",\"vendor_id\",\"vendor_name\"],\"tableGroupListMap\":{\"other\":[\"site\",\"vendor_id\",\"vendor_name\"]}}]}]},\"domesticDrillDownMetadata\":{\"metricMetadataList\":[{\"metricList\":[\"10\"],\"drillDownFieldList\":[{\"field\":\"region_name\",\"baseInfoId\":13,\"baseInfoGroupList\":[\"region_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"firstpage\":[\"region_name\"],\"detailpage\":[\"region_name\"]},\"tableOrderList\":[\"complete_people_rate\"]},{\"field\":\"province_name\",\"baseInfoId\":13,\"baseInfoGroupList\":[\"province_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"firstpage\":[\"province_name\"],\"detailpage\":[\"region_name\",\"province_name\"]},\"tableOrderList\":[\"complete_people_rate\"]},{\"field\":\"examinee\",\"baseInfoId\":15,\"baseInfoGroupList\":[\"examinee\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"firstpage\":[\"examinee\",\"examinee_name\"],\"detailpage\":[\"region_name\",\"province_name\",\"examinee\",\"examinee_name\"]},\"tableOrderList\":[\"complete_people_rate\"]}]},{\"metricList\":[\"1\",\"2\"],\"drillDownFieldList\":[{\"subMetric\":\"domesticDayTour\",\"field\":\"region_name\",\"baseInfoId\":17,\"baseInfoGroupList\":[\"region_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"other\":[\"region_name\"]},\"tableDataIdMap\":{\"current\":17,\"other\":18,\"target\":19},\"bubbleGroupListMap\":{\"other\":[\"region_name\"]}},{\"subMetric\":\"domesticDayTour\",\"field\":\"province_name\",\"baseInfoId\":17,\"baseInfoGroupList\":[\"province_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\"]},\"tableDataIdMap\":{\"current\":17,\"other\":18,\"target\":19},\"bubbleGroupListMap\":{\"other\":[\"province_name\"]}},{\"subMetric\":\"domesticDayTour\",\"field\":\"examinee\",\"baseInfoId\":21,\"baseInfoGroupList\":[\"examinee\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"current\":[\"region_name\",\"province_name\",\"examinee_name\",\"examinee\"],\"other\":[\"examinee\"]},\"tableDataIdMap\":{\"current\":21,\"other\":22,\"target\":23},\"bubbleGroupListMap\":{\"other\":[\"examinee\"]}},{\"subMetric\":\"overseaDayTour\",\"field\":\"region_name\",\"baseInfoId\":42,\"baseInfoGroupList\":[\"region_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"other\":[\"region_name\"]},\"tableDataIdMap\":{\"current\":42,\"other\":43,\"target\":44},\"bubbleGroupListMap\":{\"other\":[\"region_name\"]}},{\"subMetric\":\"overseaDayTour\",\"field\":\"province_name\",\"baseInfoId\":42,\"baseInfoGroupList\":[\"province_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\"]},\"tableDataIdMap\":{\"current\":42,\"other\":43,\"target\":44},\"bubbleGroupListMap\":{\"other\":[\"province_name\"]}},{\"subMetric\":\"overseaDayTour\",\"field\":\"examinee\",\"baseInfoId\":45,\"baseInfoGroupList\":[\"examinee\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"current\":[\"region_name\",\"province_name\",\"examinee_name\",\"examinee\"],\"other\":[\"examinee\"]},\"tableDataIdMap\":{\"current\":45,\"other\":46,\"target\":47},\"bubbleGroupListMap\":{\"other\":[\"examinee\"]}},{\"subMetric\":\"ticketActivity+domesticDayTour\",\"field\":\"region_name\",\"baseInfoId\":24,\"baseInfoGroupList\":[\"region_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"other\":[\"region_name\"]},\"tableDataIdMap\":{\"current\":24,\"other\":25,\"target\":26},\"bubbleGroupListMap\":{\"other\":[\"region_name\"]}},{\"subMetric\":\"ticketActivity+domesticDayTour\",\"field\":\"province_name\",\"baseInfoId\":24,\"baseInfoGroupList\":[\"province_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\"]},\"tableDataIdMap\":{\"current\":24,\"other\":25,\"target\":26},\"bubbleGroupListMap\":{\"other\":[\"province_name\"]}},{\"subMetric\":\"ticketActivity+domesticDayTour\",\"field\":\"examinee\",\"baseInfoId\":54,\"baseInfoGroupList\":[\"examinee\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"current\":[\"region_name\",\"ttd_province_name\",\"odt_province_name\",\"examinee_name\",\"examinee\"],\"other\":[\"examinee\"]},\"tableDataIdMap\":{\"currentmonth\":54,\"currentquarter\":55,\"otherttd\":31,\"otherodt\":22},\"bdSpecificCondition\":\"ttd_province_name|odt_province_name\",\"bdSpecificList\":[\"ttd\",\"odt\"]},{\"subMetric\":\"ticketActivity+domesticDayTour+overseaDayTour\",\"field\":\"region_name\",\"baseInfoId\":49,\"baseInfoGroupList\":[\"region_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"other\":[\"region_name\"]},\"tableDataIdMap\":{\"current\":49,\"other\":50,\"target\":51},\"bubbleGroupListMap\":{\"other\":[\"region_name\"]}},{\"subMetric\":\"ticketActivity+domesticDayTour+overseaDayTour\",\"field\":\"province_name\",\"baseInfoId\":49,\"baseInfoGroupList\":[\"province_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\"]},\"tableDataIdMap\":{\"current\":49,\"other\":50,\"target\":51},\"bubbleGroupListMap\":{\"other\":[\"province_name\"]}},{\"subMetric\":\"ticketActivity+domesticDayTour+overseaDayTour\",\"field\":\"examinee\",\"baseInfoId\":56,\"baseInfoGroupList\":[\"examinee\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"current\":[\"region_name\",\"ttd_province_name\",\"odt_province_name\",\"odt_ob_province_name\",\"examinee_name\",\"examinee\"],\"other\":[\"examinee\"]},\"tableDataIdMap\":{\"currentmonth\":56,\"currentquarter\":57,\"otherttd\":31,\"otherodt\":22,\"otherodtob\":46},\"bdSpecificCondition\":\"ttd_province_name|odt_province_name|odt_ob_province_name\",\"bdSpecificList\":[\"ttd\",\"odt\",\"odtob\"]}]},{\"metricList\":[\"11\"],\"drillDownFieldList\":[{\"subMetric\":\"domesticDayTour\",\"field\":\"region_name\",\"baseInfoId\":68,\"baseInfoGroupList\":[\"region_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"firstpage\":[\"region_name\"],\"detailpage\":[\"region_name\"]},\"examineTypeList\":[1,3]},{\"subMetric\":\"domesticDayTour\",\"field\":\"province_name\",\"baseInfoId\":68,\"baseInfoGroupList\":[\"province_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"firstpage\":[\"province_name\"],\"detailpage\":[\"region_name\",\"province_name\"]},\"examineTypeList\":[1,3]},{\"subMetric\":\"domesticDayTour\",\"field\":\"examinee\",\"baseInfoId\":68,\"baseInfoGroupList\":[\"examinee\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"firstpage\":[\"province_name\"],\"detailpage\":[\"region_name\",\"province_name\",\"examinee_name\",\"examinee\"]},\"examineTypeList\":[1,3]},{\"subMetric\":\"overseaDayTour\",\"field\":\"region_name\",\"baseInfoId\":68,\"baseInfoGroupList\":[\"region_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"firstpage\":[\"region_name\"],\"detailpage\":[\"region_name\"]},\"examineTypeList\":[2,3]},{\"subMetric\":\"overseaDayTour\",\"field\":\"province_name\",\"baseInfoId\":68,\"baseInfoGroupList\":[\"province_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"firstpage\":[\"province_name\"],\"detailpage\":[\"region_name\",\"province_name\"]},\"examineTypeList\":[2,3]},{\"subMetric\":\"overseaDayTour\",\"field\":\"examinee\",\"baseInfoId\":68,\"baseInfoGroupList\":[\"examinee\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"firstpage\":[\"province_name\"],\"detailpage\":[\"region_name\",\"province_name\",\"examinee_name\",\"examinee\"]},\"examineTypeList\":[2,3]}]},{\"metricList\":[\"12\"],\"drillDownFieldList\":[{\"field\":\"region_name\",\"baseInfoId\":69,\"baseInfoGroupList\":[\"region_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"firstpage\":[\"region_name\"],\"detailpage\":[\"region_name\"]}},{\"field\":\"province_name\",\"baseInfoId\":69,\"baseInfoGroupList\":[\"province_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"firstpage\":[\"province_name\"],\"detailpage\":[\"region_name\",\"province_name\"]}},{\"field\":\"examinee\",\"baseInfoId\":69,\"baseInfoGroupList\":[\"examinee\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"firstpage\":[\"province_name\"],\"detailpage\":[\"region_name\",\"province_name\",\"examinee_name\",\"examinee\"]}}]}]},\"domesticExamineTypeConfigData\":{\"mergedSubMetricDataList\":[[\"ticketActivity\",\"domesticDayTour\",\"overseaDayTour\"],[\"ticketActivity\",\"domesticDayTour\"]],\"examineTypeMetadataList\":[{\"examineTypeList\":[1,2,3],\"subMetricList\":[\"ticketActivity\"]},{\"examineTypeList\":[4,5,6],\"subMetricList\":[\"ticketActivity\",\"domesticDayTour\"]},{\"examineTypeList\":[7],\"subMetricList\":[\"domesticDayTour\"]},{\"examineTypeList\":[8],\"subMetricList\":[\"overseaDayTour\"]},{\"examineTypeList\":[9,10,12],\"subMetricList\":[\"ticketActivity\",\"overseaDayTour\"]},{\"examineTypeList\":[11],\"subMetricList\":[\"domesticDayTour\",\"overseaDayTour\"]},{\"examineTypeList\":[13,14,15],\"subMetricList\":[\"ticketActivity\",\"domesticDayTour\",\"overseaDayTour\"]}],\"subMetricPermissonEnumMap\":{\"3\":[{\"examineMetric\":\"1\",\"submetric\":[\"domestic\"]},{\"examineMetric\":\"2\",\"submetric\":[\"overseaDayTour\"]},{\"examineMetric\":\"3\",\"submetric\":[\"domestic\",\"overseaDayTour\"]}],\"11\":[{\"examineMetric\":\"1\",\"submetric\":[\"domesticDayTour\"]},{\"examineMetric\":\"2\",\"submetric\":[\"overseaDayTour\"]},{\"examineMetric\":\"3\",\"submetric\":[\"domesticDayTour\",\"overseaDayTour\"]}]}}}", RemoteConfig.class)
    def bus3 = new Bus3MetricStrategy(dao: dao, userPermissionBiz: userPermissionBiz, examineeConfigV2Dao: examineeConfigV2Dao, singlePeriodTrendLineBiz: singlePeriodTrendLineBiz, remoteConfig: remoteConfig)


    def "getSingleTableData"(){
        given: "mock param"
        def request = MapperUtil.str2Obj("{\"source\":\"detailpage\",\"metric\":\"3\",\"subMetric\":\"domestic\",\"domainName\":\"zheli\",\"timeFilter\":{\"dateType\":\"month\",\"year\":\"2024\",\"month\":\"05\"},\"drillDownFilter\":{\"field\":\"大区\",\"fieldValueList\":[]},\"pageNo\":1,\"pageSize\":20}", GetTableDataRequestType.class)
        def metricInfoBean = MapperUtil.str2Obj("{\"metric\":\"3\",\"level\":\"国内\",\"needUniversalStudios\":\"\",\"odtLevel\":\"\",\"overseaOdtLevel\":\"国内\",\"examineType\":3,\"role\":7}", MetricInfoBean.class)
        def d = "2024-07-16"

        and: "mock dal"
        dao.getTableReachData(_,_,_,_,_) >> MapperUtil.str2List("[[\"云南大区\",706.0,250544,0.002818],[\"华东大区\",11385.7,8418725,0.001352],[\"华北大区\",12527.4,3267596,0.003834],[\"华南大区\",5526.8,4743889,0.001165],[\"华西大区\",7900.2,4289913,0.001842]]", List.class)
        dao.getTableReachDataCount(_,_,_) >> 5
        dao.getSpilitTargetData(_,_,_) >> MapperUtil.str2List("[[\"华南大区\",0.001473],[\"华西大区\",0.002121],[\"华北大区\",0.003148],[\"云南大区\",0.003782],[\"华东大区\",0.001719]]", List.class)

        when: "call"
        def resp = bus3.getSingleTableData(request, metricInfoBean, d)

        then: "verify"
        with(resp){
            resp.totalNum == 5
        }

    }


    def "getRankDataAsync"(){
        given: "mock param"
        def dateType = "month"
        def year = "2024"
        def quarter = null
        def month = "05"
        def d = "2024-07-16"
        def domainName = "zheli"
        def subMetric = "domestic"
        def dalHints = new DalHints()

        expect: "call"
        bus3.getRankDataAsync(dateType, year, quarter, month, d, domainName, subMetric, dalHints)

    }

    /*def "getSingleTrendlineDataWithoutDrillDown"(){
        given: "mock param"
        def request = MapperUtil.str2Obj("{\"metric\":\"3\",\"subMetric\":\"domestic\",\"domainName\":\"zheli\",\"timeFilter\":{\"dateType\":\"month\",\"year\":\"2024\",\"month\":\"05\",\"timeFrame\":6},\"queryType\":\"trendline\"}", GetTrendLineDataRequestType.class)
        def d = "2024-07-16"

        and: "mock dal"
        examineeConfigV2Dao.queryMetricAllConfig(_,_,_) >> MapperUtil.str2List("[{\"id\":36861721,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2022\",\"quarter\":\"Q2\",\"examineMetric\":\"3\",\"examineType\":1,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2024-07-16\",\"datachangeLasttime\":\"Jul 16, 2024, 1:19:02 AM\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"},{\"id\":36879410,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2023\",\"quarter\":\"Q2\",\"examineMetric\":\"3\",\"examineType\":1,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2024-07-16\",\"datachangeLasttime\":\"Jul 16, 2024, 1:19:03 AM\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"},{\"id\":36882791,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2023\",\"quarter\":\"Q3\",\"examineMetric\":\"3\",\"examineType\":1,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2024-07-16\",\"datachangeLasttime\":\"Jul 16, 2024, 1:19:04 AM\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"},{\"id\":36887999,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2023\",\"quarter\":\"Q4\",\"examineMetric\":\"3\",\"examineType\":1,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2024-07-16\",\"datachangeLasttime\":\"Jul 16, 2024, 1:19:04 AM\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"},{\"id\":36889980,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2024\",\"quarter\":\"Q1\",\"examineMetric\":\"3\",\"examineType\":3,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2024-07-16\",\"datachangeLasttime\":\"Jul 16, 2024, 1:19:04 AM\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"国内\",\"overseaOdtExamineRange\":\"\"},{\"id\":36894159,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2024\",\"quarter\":\"Q2\",\"examineMetric\":\"3\",\"examineType\":3,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2024-07-16\",\"datachangeLasttime\":\"Jul 16, 2024, 1:19:04 AM\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"国内\",\"overseaOdtExamineRange\":\"\"},{\"id\":36903784,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2022\",\"quarter\":\"Q1\",\"examineMetric\":\"3\",\"examineType\":1,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2024-07-16\",\"datachangeLasttime\":\"Jul 16, 2024, 1:19:05 AM\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"},{\"id\":36916888,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2022\",\"quarter\":\"Q4\",\"examineMetric\":\"3\",\"examineType\":1,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2024-07-16\",\"datachangeLasttime\":\"Jul 16, 2024, 1:19:06 AM\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"},{\"id\":36950523,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2022\",\"quarter\":\"Q3\",\"examineMetric\":\"3\",\"examineType\":1,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2024-07-16\",\"datachangeLasttime\":\"Jul 16, 2024, 1:19:08 AM\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"},{\"id\":36957124,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2023\",\"quarter\":\"Q1\",\"examineMetric\":\"3\",\"examineType\":1,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2024-07-16\",\"datachangeLasttime\":\"Jul 16, 2024, 1:19:09 AM\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"},{\"id\":36980819,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2024\",\"quarter\":\"Q3\",\"examineMetric\":\"3\",\"examineType\":3,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2024-07-16\",\"datachangeLasttime\":\"Jul 16, 2024, 1:19:10 AM\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"国内\",\"overseaOdtExamineRange\":\"\"}]", BusinessDashboardExamineeConfigV2.class)
        singlePeriodTrendLineBiz.getBus3SinglePeriodTrendLineData(_,_,_) >>

        when: "call"
        def resp = bus3.getSingleTrendlineDataWithoutDrillDown(request, d)

        then: "verify"
        with(resp){
            resp != null
        }

    }*/


    def "getSingleDrillDownBaseInfo"() {
        given:
        def request = MapperUtil.str2Obj("{\"timeFilter\":{\"dateType\":\"month\",\"year\":\"2024\",\"month\":\"06\"},\"metric\":\"3\",\"subMetric\":\"domestic\",\"domainName\":\"zheli\",\"needSearch\":false}", GetDrillDownBaseInfoRequestType.class)
        def metricInfoBean = MapperUtil.str2Obj("{\"metric\":\"3\",\"level\":\"国内\",\"needUniversalStudios\":\"\",\"odtLevel\":\"\",\"overseaOdtLevel\":\"大区\",\"overseaOdtRegionList\":[\"海长大区\"],\"examineType\":3,\"role\":7}", MetricInfoBean.class)
        def d = "2024-06-26"

        and: "mock dao"
        dao.getFieldList(_,_,_,_) >> MapperUtil.str2List("[[\"华西大区\"],[\"华东大区\"],[\"华北大区\"],[\"华南大区\"],[\"云南大区\"]]", List.class)

        when:
        def resp = bus3.getSingleDrillDownBaseInfo(request, metricInfoBean, d)

        then:
        with(resp){
            resp.defaultChosenField == "大区"
            resp.fieldDataItemList.size() == 2
        }
    }


    def "getSingleMetricCardData"(){
        given: "mock param"
        def timeFilter = MapperUtil.str2Obj(timeFilterStr, TimeFilter.class)
        def metricInfoBean = MapperUtil.str2Obj("{\"metric\":\"3\",\"level\":\"国内\",\"needUniversalStudios\":\"\",\"odtLevel\":\"\",\"overseaOdtLevel\":\"大区\",\"overseaOdtRegionList\":[\"海长大区\"],\"examineType\":3,\"role\":7}", MetricInfoBean.class)
        def d = "2024-06-26"

        and: "mock dao"
        userPermissionBiz.checkUserPermission(_) >> MapperUtil.str2Obj("{\"havePermission\":true,\"metricList\":[\"11\",\"1\",\"12\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"9\"],\"boardType\":\"domestic\",\"domesticBasicConfig\":{\"metricCardConfigMap\":{\"11\":{\"subMetricList\":[\"domesticDayTour\",\"overseaDayTour\"]},\"1\":{\"subMetricList\":[\"ticketActivity+domesticDayTour+overseaDayTour\",\"ticketActivity\",\"domesticDayTour\",\"overseaDayTour\"]},\"2\":{\"subMetricList\":[\"ticketActivity+domesticDayTour+overseaDayTour\",\"ticketActivity\",\"domesticDayTour\",\"overseaDayTour\"]},\"3\":{\"subMetricList\":[\"domestic\",\"overseaDayTour\"]}},\"trendLineConfigMap\":{\"11\":{\"subMetricList\":[\"domesticDayTour\",\"overseaDayTour\"]},\"1\":{\"subMetricList\":[\"ticketActivity+domesticDayTour+overseaDayTour\",\"ticketActivity+domesticDayTour\",\"ticketActivity\",\"domesticDayTour\",\"overseaDayTour\"]},\"2\":{\"subMetricList\":[\"ticketActivity+domesticDayTour+overseaDayTour\",\"ticketActivity+domesticDayTour\",\"ticketActivity\",\"domesticDayTour\",\"overseaDayTour\"]},\"3\":{\"subMetricList\":[\"domestic\",\"overseaDayTour\"]}}}}", CheckUserPermissionResponseType.class)
        dao.getMetricCardReachData(_, _) >> MapperUtil.str2List("[[52878.9,29349905,0.001802,0]]", List.class)
        dao.getMetricCardTargetData(_, _) >> MapperUtil.str2List("[[0.009135]]", List.class)

        when: "call"
        def resp = bus3.getSingleMetricCardData(domainName, timeFilter, metricInfoBean, d, needRank)

        then: "verify"
        with(resp){
            resp != null
        }


        where: "多条件验证"
        domainName  |   needRank    |   timeFilterStr
        "zheli" | false     |   "{\"dateType\":\"month\",\"year\":\"2024\",\"month\":\"06\"}"
        "zheli" | false     |   "{\"dateType\":\"month\",\"year\":\"2023\",\"month\":\"06\"}"
    }


    def "filterExamineConfigList"(){
        given: "mock param"
        def examineConfigBeanList = MapperUtil.str2List("[{\"year\":\"2024\",\"month\":\"01\",\"timeMap\":{\"currentTime\":\"2024-01\",\"lastyearTime\":\"2023-01\",\"2019Time\":\"2019-01\"},\"isLastestPeriod\":false,\"limitTimeMap\":{},\"businessDashboardExamineeConfigV2\":{\"id\":34113388,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2024\",\"quarter\":\"Q1\",\"examineMetric\":\"3\",\"examineType\":3,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2024-06-26\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"大区\",\"overseaOdtExamineRange\":\"海长大区\"},\"dateType\":\"month\"},{\"year\":\"2024\",\"month\":\"02\",\"timeMap\":{\"currentTime\":\"2024-02\",\"lastyearTime\":\"2023-02\",\"2019Time\":\"2019-02\"},\"isLastestPeriod\":false,\"limitTimeMap\":{},\"businessDashboardExamineeConfigV2\":{\"id\":34113388,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2024\",\"quarter\":\"Q1\",\"examineMetric\":\"3\",\"examineType\":3,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2024-06-26\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"大区\",\"overseaOdtExamineRange\":\"海长大区\"},\"dateType\":\"month\"},{\"year\":\"2024\",\"month\":\"03\",\"timeMap\":{\"currentTime\":\"2024-03\",\"lastyearTime\":\"2023-03\",\"2019Time\":\"2019-03\"},\"isLastestPeriod\":false,\"limitTimeMap\":{},\"businessDashboardExamineeConfigV2\":{\"id\":34113388,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2024\",\"quarter\":\"Q1\",\"examineMetric\":\"3\",\"examineType\":3,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2024-06-26\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"大区\",\"overseaOdtExamineRange\":\"海长大区\"},\"dateType\":\"month\"},{\"year\":\"2024\",\"month\":\"04\",\"timeMap\":{\"currentTime\":\"2024-04\",\"lastyearTime\":\"2023-04\",\"2019Time\":\"2019-04\"},\"isLastestPeriod\":false,\"limitTimeMap\":{},\"businessDashboardExamineeConfigV2\":{\"id\":34169850,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2024\",\"quarter\":\"Q2\",\"examineMetric\":\"3\",\"examineType\":3,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2024-06-26\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"大区\",\"overseaOdtExamineRange\":\"海长大区\"},\"dateType\":\"month\"},{\"year\":\"2024\",\"month\":\"05\",\"timeMap\":{\"currentTime\":\"2024-05\",\"lastyearTime\":\"2023-05\",\"2019Time\":\"2019-05\"},\"isLastestPeriod\":false,\"limitTimeMap\":{},\"businessDashboardExamineeConfigV2\":{\"id\":34169850,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2024\",\"quarter\":\"Q2\",\"examineMetric\":\"3\",\"examineType\":3,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2024-06-26\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"大区\",\"overseaOdtExamineRange\":\"海长大区\"},\"dateType\":\"month\"},{\"year\":\"2024\",\"month\":\"06\",\"timeMap\":{\"currentTime\":\"2024-06\",\"lastyearTime\":\"2023-06\",\"2019Time\":\"2019-06\"},\"isLastestPeriod\":true,\"limitTimeMap\":{\"popYearTime\":\"2019-06-25\",\"time\":\"2024-06-25\",\"lastYearTime\":\"2023-06-25\"},\"businessDashboardExamineeConfigV2\":{\"id\":34169850,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2024\",\"quarter\":\"Q2\",\"examineMetric\":\"3\",\"examineType\":3,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2024-06-26\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"大区\",\"overseaOdtExamineRange\":\"海长大区\"},\"dateType\":\"month\"}]", ExamineConfigBean.class)
        def subMetric = "domestic"

        when: "call"
        def resp = bus3.filterExamineConfigList(examineConfigBeanList, subMetric)

        then: "verify"
        with(resp){
            resp.size() > 0
        }




    }


    def "getRegionList"(){
        expect: "call"
        bus3.getRegionList(metricInfoBean, subMetric)

        where: "多条件验证"
        subMetric       |       metricInfoBean
        "overseaDayTour"    |   MapperUtil.str2Obj("{\"metric\":\"3\",\"level\":\"国内\",\"needUniversalStudios\":\"\",\"odtLevel\":\"\",\"overseaOdtLevel\":\"大区\",\"overseaOdtRegionList\":[\"海长大区\"],\"examineType\":3,\"role\":7}", MetricInfoBean.class)
    }


    def "getLevel"(){
        expect: "call"
        bus3.getLevel(metricInfoBean, subMetric)

        where: "多条件验证"
        subMetric       |       metricInfoBean
        "overseaDayTour"    |   MapperUtil.str2Obj("{\"metric\":\"3\",\"level\":\"国内\",\"needUniversalStudios\":\"\",\"odtLevel\":\"\",\"overseaOdtLevel\":\"大区\",\"overseaOdtRegionList\":[\"海长大区\"],\"examineType\":3,\"role\":7}", MetricInfoBean.class)
    }


    def "getLevel1"(){
        expect: "call"
        bus3.getLevel1(metricInfoBean, subMetric)

        where: "多条件验证"
        subMetric       |       metricInfoBean
        "overseaDayTour"    |   MapperUtil.str2Obj("{\"metric\":\"3\",\"level\":\"国内\",\"needUniversalStudios\":\"\",\"odtLevel\":\"\",\"overseaOdtLevel\":\"大区\",\"overseaOdtRegionList\":[\"海长大区\"],\"examineType\":3,\"role\":7}", MetricInfoBean.class)
    }


    def "setMetricCardDrillDownInfo"(){
        given: "mock param"
        def b = String.format("{\"metric\":\"3\",\"level\":\"国内\",\"needUniversalStudios\":\"\",\"odtLevel\":\"\",\"overseaOdtLevel\":%s,\"examineType\":3,\"role\":7}", overseaOdtLevel)
        def metricInfoBean = MapperUtil.str2Obj(b, MetricInfoBean.class)
        def subMetricInfo = MapperUtil.str2Obj("{\"subMetric\":\"domestic\",\"dimData\":{\"ttd_weighted_defect_rate_lastyear\":-0.3840579710144928,\"ttd_weighted_defect_achieved_rate\":1.2410714285714286,\"ttd_weighted_defect_cnt\":43272.5,\"ttd_pay_odr_cnt\":2.5523095E7,\"ttd_weighted_defect_target\":0.00224,\"ttd_weighted_defect_rate\":0.0017}}", MetricDetailInfo.class)

        expect: "call & verify"
        bus3.setMetricCardDrillDownInfo(subMetric, metricInfoBean, subMetricInfo)

        where: "多条件"
        overseaOdtLevel     |       subMetric
        "国内"                 |       "domestic"
        "国内"                 |       "overseaDayTour"
        "大区"                 |       "overseaDayTour"
//        "省份"                 |       "overseaDayTour"

    }








}
