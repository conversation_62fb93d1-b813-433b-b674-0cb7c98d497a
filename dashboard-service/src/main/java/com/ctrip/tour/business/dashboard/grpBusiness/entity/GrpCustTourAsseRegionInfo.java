package com.ctrip.tour.business.dashboard.grpBusiness.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Sensitive;
import com.ctrip.platform.dal.dao.annotation.Type;
import java.sql.Types;
import java.math.BigDecimal;
import java.sql.Timestamp;

import com.ctrip.platform.dal.dao.DalPojo;

/**
 * <AUTHOR>
 * @date 2025-03-19
 */
@Entity
@Database(name = "ttdreportdb_dalcluster")
@Table(name = "grp_cust_tour_asse_region_info")
public class GrpCustTourAsseRegionInfo implements DalPojo {

    /**
     * 主键
     */
    @Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long id;

    /**
     * 一级区域id
     */
	@Column(name = "region_id")
	@Type(value = Types.BIGINT)
	private Long regionId;

    /**
     * 一级区域名称
     */
	@Column(name = "region_name")
	@Type(value = Types.VARCHAR)
	private String regionName;

    /**
     * 二级区域id
     */
	@Column(name = "area_id")
	@Type(value = Types.BIGINT)
	private Long areaId;

    /**
     * 二级区域名称
     */
	@Column(name = "area_name")
	@Type(value = Types.VARCHAR)
	private String areaName;

    /**
     * 二级区域负责人id
     */
	@Column(name = "area_manager_id")
	@Type(value = Types.VARCHAR)
	private String areaManagerId;

    /**
     * 二级区域负责人名称
     */
	@Column(name = "area_manager_name")
	@Type(value = Types.VARCHAR)
	private String areaManagerName;

    /**
     * 一级区域负责人id
     */
	@Column(name = "region_manager_id")
	@Type(value = Types.VARCHAR)
	private String regionManagerId;

    /**
     * 一级区域负责人名称
     */
	@Column(name = "region_manager_name")
	@Type(value = Types.VARCHAR)
	private String regionManagerName;

    /**
     * 指标类型 gmv,profit,nps,comporder
     */
	@Column(name = "index_type")
	@Type(value = Types.VARCHAR)
	private String indexType;

    /**
     * 考核月份 yyyy-MM
     */
	@Column(name = "asse_month")
	@Type(value = Types.VARCHAR)
	private String asseMonth;

    /**
     * 目标值
     */
	@Column(name = "target_value")
	@Type(value = Types.DECIMAL)
	private BigDecimal targetValue;

    /**
     * 更新时间
     */
	@Column(name = "datachange_lasttime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getRegionId() {
		return regionId;
	}

	public void setRegionId(Long regionId) {
		this.regionId = regionId;
	}

	public String getRegionName() {
		return regionName;
	}

	public void setRegionName(String regionName) {
		this.regionName = regionName;
	}

	public Long getAreaId() {
		return areaId;
	}

	public void setAreaId(Long areaId) {
		this.areaId = areaId;
	}

	public String getAreaName() {
		return areaName;
	}

	public void setAreaName(String areaName) {
		this.areaName = areaName;
	}

	public String getAreaManagerId() {
		return areaManagerId;
	}

	public void setAreaManagerId(String areaManagerId) {
		this.areaManagerId = areaManagerId;
	}

	public String getAreaManagerName() {
		return areaManagerName;
	}

	public void setAreaManagerName(String areaManagerName) {
		this.areaManagerName = areaManagerName;
	}

	public String getRegionManagerId() {
		return regionManagerId;
	}

	public void setRegionManagerId(String regionManagerId) {
		this.regionManagerId = regionManagerId;
	}

	public String getRegionManagerName() {
		return regionManagerName;
	}

	public void setRegionManagerName(String regionManagerName) {
		this.regionManagerName = regionManagerName;
	}

	public String getIndexType() {
		return indexType;
	}

	public void setIndexType(String indexType) {
		this.indexType = indexType;
	}

	public String getAsseMonth() {
		return asseMonth;
	}

	public void setAsseMonth(String asseMonth) {
		this.asseMonth = asseMonth;
	}

	public BigDecimal getTargetValue() {
		return targetValue;
	}

	public void setTargetValue(BigDecimal targetValue) {
		this.targetValue = targetValue;
	}

	public Timestamp getDatachangeLasttime() {
		return datachangeLasttime;
	}

	public void setDatachangeLasttime(Timestamp datachangeLasttime) {
		this.datachangeLasttime = datachangeLasttime;
	}

}