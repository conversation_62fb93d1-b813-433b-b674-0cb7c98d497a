package com.ctrip.tour.business.dashboard.grpBusiness.entity;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/4/16
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GrpUserMappingConfDto {

    List<MappingConf> mappingConfs;


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MappingConf {
        private String orginEmpCode;
        private String mappingEmpCode;
        private String empChineseName;
    }

}
