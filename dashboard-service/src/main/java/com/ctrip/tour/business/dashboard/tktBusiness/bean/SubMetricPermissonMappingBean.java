package com.ctrip.tour.business.dashboard.tktBusiness.bean;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class SubMetricPermissonMappingBean {
    // 指标类型，对于3 11指标，该值枚举：1[只考核国内]|2[只考核出境日游]|3[同时考核国内+出境日游]
    String examineMetric;
    // 子指标枚举值
    List<String> submetric;

}
