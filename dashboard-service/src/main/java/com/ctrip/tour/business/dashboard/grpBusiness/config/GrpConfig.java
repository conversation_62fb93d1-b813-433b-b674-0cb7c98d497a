package com.ctrip.tour.business.dashboard.grpBusiness.config;

import com.alibaba.fastjson.JSON;
import com.ctrip.tour.business.dashboard.ServiceInitializer;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.model.DomainConfig;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import org.springframework.stereotype.Component;
import org.yaml.snakeyaml.representer.Representer;
import qunar.tc.qconfig.client.spring.QConfig;

import org.yaml.snakeyaml.Yaml;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Getter
@Setter
public class GrpConfig {

    @QConfig("grp_config.yml")
    private String grpConfigMap;

    @QConfig("domain_model_gentuan.yml")
    private String domainModelGentuan;
    @QConfig("domain_model_sijiatuan.yml")
    private String domainModelSijiatuan;
    @QConfig("domain_model_sijiatuan_dingzhi.yml")
    private String domainModelSijiatuanDingzhi;

    @JsonIgnore
    private static Representer representer = new Representer();

    static {
        representer.getPropertyUtils().setSkipMissingProperties(true);
    }

//    public DomainConfig getDomainConfig(String fileName) {
//        InputStream inputStream;
//        File file = new File("/Users/<USER>/IdeaProjects/business-dashboard/dashboard-service/src/main/resources/" + fileName);
//        if (file.exists()) {
//            try {
//                inputStream = new FileInputStream(file);
//            } catch (FileNotFoundException e) {
//                throw new RuntimeException(e);
//            }
//        } else {
//            inputStream = ServiceInitializer.class.getClassLoader().getResourceAsStream(fileName);
//        }
//        return new Yaml(representer).loadAs(inputStream, DomainConfig.class);
//
////        switch (fileName) {
////            case "domain_model_gentuan.yml":
////                return new Yaml(representer).loadAs(domainModelGentuan, DomainConfig.class);
////            case "domain_model_sijiatuan.yml":
////                return new Yaml(representer).loadAs(domainModelSijiatuan, DomainConfig.class);
////            case "domain_model_sijiatuan_dingzhi.yml":
////                return new Yaml(representer).loadAs(domainModelSijiatuanDingzhi, DomainConfig.class);
////        }
////        return null;
//    }

    public String getDomainConfigStr(String fileName) {
        InputStream inputStream;
        File file = new File("D:\\work\\business-dashboard\\dashboard-service\\src\\main\\resources\\" + fileName);
        if (file.exists()) {
            try {
                inputStream = new FileInputStream(file);
            } catch (FileNotFoundException e) {
                throw new RuntimeException(e);
            }
        } else {
            inputStream = ServiceInitializer.class.getClassLoader().getResourceAsStream(fileName);
        }
        if(inputStream == null){
            return null;
        }
        return new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))
                .lines()
                .collect(Collectors.joining("\n"));

      /*  switch (fileName) {
            case "domain_model_gentuan.yml":
                return domainModelGentuan;
            case "domain_model_sijiatuan.yml":
                return domainModelSijiatuan;
            case "domain_model_sijiatuan_dingzhi.yml":
                return domainModelSijiatuanDingzhi;
        }
        return null;*/
    }

    public GrpConfigEntity getGrpConfig() {
//        Yaml yaml = new Yaml();
//        InputStream inputStream;
//
//        File file = new File("/Users/<USER>/IdeaProjects/business-dashboard/dashboard-service/src/main/resources/grp_config.yml");
//        if (file.exists()) {
//            try {
//                inputStream = new FileInputStream(file);
//            } catch (FileNotFoundException e) {
//                throw new RuntimeException(e);
//            }
//        } else {
//            inputStream = ServiceInitializer.class.getClassLoader().getResourceAsStream("grp_config.yml");
//        }
//        return yaml.loadAs(inputStream, GrpConfigEntity.class);
        return new Yaml(representer).loadAs(grpConfigMap, GrpConfigEntity.class);
    }
}

