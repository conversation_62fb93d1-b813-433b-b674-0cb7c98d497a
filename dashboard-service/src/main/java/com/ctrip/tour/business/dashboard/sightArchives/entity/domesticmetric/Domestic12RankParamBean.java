package com.ctrip.tour.business.dashboard.sightArchives.entity.domesticmetric;

import lombok.Data;

import java.util.List;

@Data
public class Domestic12RankParamBean {
    // 指标类型，1: GMV, 2: 毛利
    Integer metricId;
    // bd名称
    String domain_name;
    // 年份
    String year;
    // 标签分类，m/q/h/y
    String tag;
    // 时间类别
    String period;
    // 日期
    String d;
    // 业务线
    String businessLine;
}
