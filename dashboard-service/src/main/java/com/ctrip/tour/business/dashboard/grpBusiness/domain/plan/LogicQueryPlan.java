package com.ctrip.tour.business.dashboard.grpBusiness.domain.plan;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.AbstractReduceFunction;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.dsl.SQLTemplateUtils;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.dsl.DSLUtils;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.model.DomainConfig;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.model.LogicModel;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.model.Indicator;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.model.ResultData;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.query.AbstractDataQuery;
import freemarker.template.TemplateException;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;


@Slf4j
@Getter
@Setter
public class LogicQueryPlan {
    private Map<Integer, List<Indicator>> splitIndicaterMap; // 指标
    private DomainConfig domainConfig; // 模型配置
    private DSLRequestType dsl; // 查询dsl

    private Map<Integer, DSLRequestType> dslRequestTypeMap = new ConcurrentHashMap<>(); // 每个查询有不同的dsl
    private Map<Integer, ResultData> queryDataResult = new ConcurrentHashMap<>(); // 结果集
    private ResultData reduceQueryDataResult = new ResultData(); // reduce后的结果集

    // 数据源
    private Map<String, AbstractDataQuery> abstractOLAPQueryMap;
    private static int idx = 0;

    public LogicQueryPlan(DSLRequestType dsl, DomainConfig domainConfig, Map<String, AbstractDataQuery> abstractOLAPQueryMap) {
        this.domainConfig = domainConfig;
        this.dsl = dsl;
        this.abstractOLAPQueryMap = abstractOLAPQueryMap;
        this.splitIndicaterMap = explainLogicIndicator(dsl);
    }

    // 解析模型指标
    private Map<Integer, List<Indicator>> explainLogicIndicator(DSLRequestType dsl) {
        Map<Integer, List<Indicator>> domainLogicSplit = new HashMap<>();
        List<String> indicators = new ArrayList<>(dsl.getIndicators() == null ? new ArrayList<>() : dsl.getIndicators()).stream().distinct().collect(Collectors.toList());
        // 纬度
        Map<Integer, List<Indicator>> domainLogicSplitGroupSplit = new HashMap<>();
        // 指标纬度聚合查询
        for (String indicator : indicators) {
            Integer domainID = domainConfig.getLogicModelIDByIndicatorName(indicator);
            Indicator indicatorsItemIndicatorName;
            if (domainID == null) {
                List<Integer> logicModelIDByDimName = domainConfig.getLogicModelIDByDimName(indicator);
                if (logicModelIDByDimName != null && !logicModelIDByDimName.isEmpty()) {
                    domainID = logicModelIDByDimName.get(0);
                }
                indicatorsItemIndicatorName = domainConfig.getDimByIndicatorName(indicator);
            } else {
                indicatorsItemIndicatorName = domainConfig.getIndicatorByIndicatorName(indicator);
            }
            if (domainID == null) {
                log.warn("not found indicators: {}", indicator);
                continue;
            }
            // 纬度是否存在
            if (indicatorsItemIndicatorName.getIndicator_is_dimension()) {
                if (!domainLogicSplitGroupSplit.containsKey(domainID)) {
                    domainLogicSplitGroupSplit.put(domainID, new ArrayList<>());
                }
                domainLogicSplitGroupSplit.get(domainID).add(indicatorsItemIndicatorName);
            } else {
                if (!domainLogicSplit.containsKey(domainID)) {
                    domainLogicSplit.put(domainID, new ArrayList<>());
                }
                domainLogicSplit.get(domainID).add(indicatorsItemIndicatorName);
            }
        }
        // 纬度是否在指标表里面
        for (Integer dimDomainID : domainLogicSplitGroupSplit.keySet()) {
            Set<Indicator> groupIndicator = new HashSet<>(domainLogicSplitGroupSplit.get(dimDomainID));
            domainLogicSplit.keySet().forEach(
                    domainID -> {
                        LogicModel logicDomain = domainConfig.getLogicModelByLogicModelID(domainID);
                        logicDomain.getIndicators().forEach(
                                groupIndicator::remove
                        );
                    }
            );
            if (groupIndicator.isEmpty()) {
                domainLogicSplitGroupSplit.remove(dimDomainID);
            }
        }
        domainLogicSplit.putAll(domainLogicSplitGroupSplit);
        // 纬度查询
        return domainLogicSplit;
    }

    // 执行物理计划
    // indicator：查询聚合的指标；
    // group by：聚合纬度，会随指标一起返回
    public void doLogicPlan() {
        // 并行执行模板查询
        splitIndicaterMap.keySet()
                .parallelStream()
                .forEach(domainID -> {
                    Map<String, Object> rootParam = new HashMap<>();
                    Map<String, Object> filters = DSLUtils.getWhereConditionFlatmap(dsl); // 打平where condition
                    // where 条件都打平放在filter_flat中
                    rootParam.put("filter_flat", filters);
                    // 当前模版信息
                    LogicModel domainInfoItem = domainConfig.getLogicModelByLogicModelID(domainID);
                    Map<String, Indicator> containsMap = domainInfoItem.getIndicators()
                            .stream()
                            .collect(Collectors.toMap(Indicator::getIndicator_name, v -> v, (oldKey, newKey) -> newKey));
                    // 模板参数
                    rootParam.put("indicators", splitIndicaterMap.get(domainID).stream().map(Indicator::getIndicator_name).collect(Collectors.toList()));
                    Map<String, Indicator> groupMap = new HashMap<>();
                    if (dsl.getGroupBy() != null) {
                        // 过滤掉不存在的纬度
                        List<String> groupBy = dsl.getGroupBy().stream()
                                .filter(containsMap::containsKey)
                                .distinct()
                                .collect(Collectors.toList());
                        // groupMap
                        groupBy.forEach(v -> groupMap.put(v, containsMap.get(v)));
                        rootParam.put("group_by", groupBy);
                    }
                    if (dsl.getOrderBy() != null) {
                        // 过滤掉不存在的排序条件
                        List<OrderBy> orderBy = dsl.getOrderBy().stream()
                                .filter(v -> containsMap.containsKey(v.getOrderFiled()))
                                .distinct()
                                .collect(Collectors.toList());
                        rootParam.put("order_by", orderBy.stream().map(v -> {
                            HashMap<String, String> order = new HashMap<>();
                            order.put("order_field", v.getOrderFiled());
                            order.put("order_type", v.getOrderType());
                            return order;
                        }).collect(Collectors.toList()));
                    }
                    if (dsl.getLimit() != null) {
                        HashMap<String, Integer> limit = new HashMap<>();
                        limit.put("limit_start", dsl.getLimit().getStart());
                        limit.put("limit_size", dsl.getLimit().getSize());
                        rootParam.put("limit", limit);
                    }
                    // sql查询模版生成
                    int idxCP = idx++;
                    String sql;
                    try {
                        sql = SQLTemplateUtils.prepareSQLWithParam(domainInfoItem.getTemplate(), rootParam);
                    } catch (IOException | TemplateException e) {
                        throw new RuntimeException(e);
                    }
                    // log.error("sql parse [{}]: \n===================before===================\n,param: {}\n {}\n===================after===================\n{}\n===================finish===================", idxCP, param, domainInfoItem.getTemplate(), sql);
                    // 数据查询
                    List<Map<String, Object>> queryData;
                    log.info("start query sql [{}]: {}", idxCP, sql.replaceAll("\n", "\t"));
                    try {
                        queryData = abstractOLAPQueryMap.get(domainInfoItem.getSource_type()).Query(sql);
                    } catch (SQLException e) {
                        log.error("error query sql: {}", sql, e);
                        throw new RuntimeException(e);
                    }
                    log.info("finish query sql [{}]: {}", idxCP, sql.replaceAll("\n", "\t"));
                    // 生成结果集
                    ResultData resultData = new ResultData();
                    splitIndicaterMap.get(domainID).forEach(resultData::addMeta);
                    groupMap.values().forEach(resultData::addMeta);
                    resultData.setData(queryData);
                    resultData.setMeta(resultData.getMeta().stream().distinct().collect(Collectors.toList()));
                    queryDataResult.put(domainID, resultData);
                });
        // 模板查询聚合
        this.reduce();
    }

    // 多数据源聚合
    AbstractReduceFunction reduceFunction;

    public LogicQueryPlan withReduceFunction(AbstractReduceFunction reduceFunction) {
        this.reduceFunction = reduceFunction;
        return this;
    }

    private void reduce() {
        reduceFunction.reduce(this);
    }
}
