package com.ctrip.tour.business.dashboard.tktBusiness.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Sensitive;
import com.ctrip.platform.dal.dao.annotation.Type;
import java.sql.Types;
import java.sql.Timestamp;

import com.ctrip.platform.dal.dao.DalPojo;

/**
 * <AUTHOR>
 * @date 2023-03-30
 */
@Entity
@Database(name = "TtdReportDB_W")
@Table(name = "business_dashboard_oversea_examinee_config")
public class BusinessDashboardOverseaExamineeConfig implements DalPojo {

    /**
     * 主键
     */
    @Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long id;

    /**
     * 邮箱前缀
     */
	@Column(name = "domain_name")
	@Type(value = Types.VARCHAR)
	private String domainName;

    /**
     * 目标年
     */
	@Column(name = "year")
	@Type(value = Types.VARCHAR)
	private String year;

    /**
     * 考核周期
     */
	@Column(name = "quarter")
	@Type(value = Types.VARCHAR)
	private String quarter;

    /**
     * 考核指标（多个逗号分割1；2；3）指标类型 1-GMV 2-毛利 3-质量成本 4-直签 5-6-7- 8-活动覆盖 9-品类覆盖
     */
	@Column(name = "examine_metric")
	@Type(value = Types.VARCHAR)
	private String examineMetric;

    /**
     * 目的地考核层级
     */
	@Column(name = "destination_examine_level")
	@Type(value = Types.VARCHAR)
	private String destinationExamineLevel;

    /**
     * 目的地考核范围
     */
	@Column(name = "destination_examine_range")
	@Type(value = Types.VARCHAR)
	private String destinationExamineRange;

    /**
     * 站点考核范围
     */
	@Column(name = "site_examine_range")
	@Type(value = Types.VARCHAR)
	private String siteExamineRange;

    /**
     * 渠道考核范围
     */
	@Column(name = "channel_examine_range")
	@Type(value = Types.VARCHAR)
	private String channelExamineRange;

    /**
     * 分区
     */
	@Column(name = "query_d")
	@Type(value = Types.VARCHAR)
	private String queryD;

    /**
     * 最后更新时间
     */
    @Id
	@Column(name = "DataChange_LastTime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getDomainName() {
		return domainName;
	}

	public void setDomainName(String domainName) {
		this.domainName = domainName;
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year;
	}

	public String getQuarter() {
		return quarter;
	}

	public void setQuarter(String quarter) {
		this.quarter = quarter;
	}

	public String getExamineMetric() {
		return examineMetric;
	}

	public void setExamineMetric(String examineMetric) {
		this.examineMetric = examineMetric;
	}

	public String getDestinationExamineLevel() {
		return destinationExamineLevel;
	}

	public void setDestinationExamineLevel(String destinationExamineLevel) {
		this.destinationExamineLevel = destinationExamineLevel;
	}

	public String getDestinationExamineRange() {
		return destinationExamineRange;
	}

	public void setDestinationExamineRange(String destinationExamineRange) {
		this.destinationExamineRange = destinationExamineRange;
	}

	public String getSiteExamineRange() {
		return siteExamineRange;
	}

	public void setSiteExamineRange(String siteExamineRange) {
		this.siteExamineRange = siteExamineRange;
	}

	public String getChannelExamineRange() {
		return channelExamineRange;
	}

	public void setChannelExamineRange(String channelExamineRange) {
		this.channelExamineRange = channelExamineRange;
	}

	public String getQueryD() {
		return queryD;
	}

	public void setQueryD(String queryD) {
		this.queryD = queryD;
	}

	public Timestamp getDatachangeLasttime() {
		return datachangeLasttime;
	}

	public void setDatachangeLasttime(Timestamp datachangeLasttime) {
		this.datachangeLasttime = datachangeLasttime;
	}

}
