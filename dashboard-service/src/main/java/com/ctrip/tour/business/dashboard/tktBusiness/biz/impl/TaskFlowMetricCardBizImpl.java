package com.ctrip.tour.business.dashboard.tktBusiness.biz.impl;

import com.ctrip.soa._24922.*;
import com.ctrip.soa._27181.BIBaseReportQueryServiceClient;
import com.ctrip.soa._27181.GetRawDataRequestType;
import com.ctrip.soa._27181.GetRawDataResponseType;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.SqlParamterBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.UploadResult;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.DataUpdateBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.TaskFlowMetricCardBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardEmployeeInfoDao;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardExamineeConfigV2Dao;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardOrganizationInfoDao;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardEmployeeInfo;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardOrganizationInfo;
import com.ctrip.tour.business.dashboard.tktBusiness.enums.FileTypeEnum;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.TaskFlowHelper;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.TaskFlowSelect43556Helper;
import com.ctrip.tour.business.dashboard.tktBusiness.impl.TaskFlowSyncJobImpl;
import com.ctrip.tour.business.dashboard.utils.*;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TaskFlowMetricCardBizImpl implements TaskFlowMetricCardBiz {


    @Autowired
    private BusinessDashboardEmployeeInfoDao employeeInfoDao;
    @Autowired
    private BusinessDashboardOrganizationInfoDao organizationInfoDao;
    @Autowired
    private BusinessDashboardExamineeConfigV2Dao examineConfigV2Dao;
    @Autowired
    private RemoteConfig remoteConfig;
    @Autowired
    private DataUpdateBiz dataUpdateBiz;
    @Autowired
    private BIBaseReportQueryServiceClient client;
    @Autowired
    private TaskFlowSelect43556Helper taskFlowSelect43556Helper;
    @Autowired
    private TaskFlowSyncJobImpl taskFlowSyncJob;

    @Autowired
    private ApplicationContext ac;

    @Override
    public Future<GetTaskFlowMetricCardDataResponseType> getTaskFlowMetricCardData(GetTaskFlowMetricCardDataRequestType request,
                                                                                   String empCode,
                                                                                   Boolean needLastPeriod) throws Exception {
        String d = dataUpdateBiz.getTaskBoardUpdateTime();  //数据更新时间

        return getTaskFlowMetricCardData(request, empCode, needLastPeriod, d);

    }

    @Override
    public Future<GetTaskFlowMetricCardDataResponseType> getTaskFlowMetricCardData(GetTaskFlowMetricCardDataRequestType request,
                                                                                   String empCode,
                                                                                   Boolean needLastPeriod,String d) throws Exception {


        GetTaskFlowMetricCardDataResponseType response = new GetTaskFlowMetricCardDataResponseType();

        BusinessDashboardEmployeeInfo employeeInfo = TaskFlowHelper.getActualEmployeeInfo(empCode, d, remoteConfig, examineConfigV2Dao, employeeInfoDao);    //获取员工信息
        if(GeneralUtil.isEmpty(employeeInfo)){
            response.setStatus("noData");
            return new AsyncResult<>(response);
        }

        Map<String, String> andMap = new HashMap<>();
        TaskFlowHelper.setAllSubordinateCondition(andMap, employeeInfo, remoteConfig, organizationInfoDao, employeeInfoDao); //把下属员工拼到andMap中

        SqlParamterBean currentBean = TaskFlowHelper.getMetricCardSqlBean(request, andMap, d, false);     //拼接当期sql条件
        GetRawDataRequestType currenRequest = currentBean.convertBeanToRequest(true);
        GetRawDataResponseType currentResponse = taskFlowSelect43556Helper.getRawDataBy43556(currenRequest); //调43556接口获取指标名称和值

        Map<String, Double> dimMap = new HashMap<>();
        response.setDimData(dimMap);

        TaskFlowHelper.processMetricCardData(currentResponse, dimMap, "");
        String status = dimMap.isEmpty()||checkNoData(dimMap) ? "noData" : "normal";
        response.setStatus(status);

        if (needLastPeriod && "normal".equals(status)) {
            SqlParamterBean lastBean = TaskFlowHelper.getMetricCardSqlBean(request, andMap, d, true);   //拼接上期sql条件
            GetRawDataRequestType lastRequest = lastBean.convertBeanToRequest(true);
            GetRawDataResponseType lastResponse = taskFlowSelect43556Helper.getRawDataBy43556(lastRequest);
            TaskFlowHelper.processMetricCardData(lastResponse, dimMap, "_lastperiod");
        }

        return new AsyncResult<>(response);
    }


    private boolean checkNoData(Map<String, Double> dimMap) {

        if(MapUtils.isEmpty(dimMap)){
            return true;
        }

        for(Double value : dimMap.values()){
            boolean havaData = ((value!=null) && Double.compare(value,0.0)!=0); //有数据
            if(havaData){
                return false;   //若遍历完了 都走不到这里  说明dimMap里面没有数据
            }
        }
        return true;
    }


    static Map<String,String> statisticalScopeMap = new HashMap<>();
    static {
        statisticalScopeMap.put("domestic", "国内业务"); //NOSONAR
        statisticalScopeMap.put("oversea", "海外业务");  //NOSONAR
        statisticalScopeMap.put("operate", "信息运营");  //NOSONAR
        statisticalScopeMap.put("1", "国内业务");  //NOSONAR
        statisticalScopeMap.put("2", "海外业务");  //NOSONAR
        statisticalScopeMap.put("3", "信息运营");  //NOSONAR
    }


    public DownloadTaskFlowDataResponseType downloadTaskFlowData(GetTaskFlowMetricCardDataRequestType requestType, String empCode) throws Exception {

        //获取数据更新时间
        String queryD = dataUpdateBiz.getTaskBoardUpdateTime();

        String dateRange = DateUtil.getTimeRange(requestType.getDateType(), queryD, false, requestType.getStartDate(), requestType.getEndDate());  //时间范围拼接
        String newDateRange = dateRange.replace("-","").replace("|","-");
        String statisticalScope = statisticalScopeMap.get(requestType.getStatisticalScope());
        TaskLevelScoreRange taskLevelScoreRange = requestType.getTaskLevelScoreRange();
        String scoreRange = "";
        if(taskLevelScoreRange!=null && taskLevelScoreRange.getMinScore()!=null && taskLevelScoreRange.getMaxScore()!=null){
            scoreRange = taskLevelScoreRange.getMinScore()+"-"+taskLevelScoreRange.getMaxScore()+"分"; //NOSONAR
        }else {
            scoreRange = "0-10分"; //NOSONAR
        }
        BusinessDashboardEmployeeInfo employeeInfo = TaskFlowHelper.getActualEmployeeInfo(empCode, queryD, remoteConfig, examineConfigV2Dao, employeeInfoDao);
        String chineseName = "";
        if(employeeInfo==null || StringUtils.isBlank(employeeInfo.getDisplayName())){
            employeeInfo = new BusinessDashboardEmployeeInfo();
        }else{
            int startIndex = employeeInfo.getDisplayName().indexOf("（");
            int endIndex = employeeInfo.getDisplayName().indexOf("）");
            if (startIndex != -1 && endIndex != -1 && endIndex > startIndex) {
                chineseName = employeeInfo.getDisplayName().substring(startIndex + 1, endIndex);
            }
        }

        String fileName = newDateRange+"任务看板数据_"+statisticalScope+"_"+scoreRange+"_"+chineseName+"_"+System.currentTimeMillis()+"."+FileTypeEnum.XLSX.getCode(); //NOSONAR

        byte[] bytes = null;
        List<ExcelSheetData> sheetDataList = null;
        try {
            sheetDataList = getTaskFlowDetailExcelAsync(requestType, employeeInfo, queryD);
            bytes = ExcelUtil.outputExcel(sheetDataList);
            sheetDataList = null;
            UploadResult uploadResult = FileServerUtil.upload(bytes, FileTypeEnum.XLSX, remoteConfig.getConfigValue("fileChannel"), remoteConfig.getConfigValue("hostName"),fileName);
            if (Objects.nonNull(uploadResult)) {
                DownloadTaskFlowDataResponseType responseType = new DownloadTaskFlowDataResponseType();
                responseType.setFileUrl(uploadResult.getFileUrl());
                return responseType;
            }
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }finally {
            sheetDataList = null;
            bytes = null;
        }
        return new DownloadTaskFlowDataResponseType();

    }


    private List<ExcelSheetData>  getTaskFlowDetailExcelAsync(GetTaskFlowMetricCardDataRequestType request, BusinessDashboardEmployeeInfo employeeInfo,String queryD) {
        ExecutorService executor = Executors.newFixedThreadPool(3);

        Future<ExcelSheetData> future1 = executor.submit(new ExcelSheetDataCallable(request, employeeInfo, queryD,1));
        Future<ExcelSheetData> future2 = executor.submit(new ExcelSheetDataCallable(request, employeeInfo, queryD,2));
        Future<ExcelSheetData> future3 = executor.submit(new ExcelSheetDataCallable(request, employeeInfo, queryD,3));

        List<ExcelSheetData> sheetDataList = new ArrayList<>();
        ExcelSheetData sheet1 = null;
        try {
            sheet1 = future1.get();
            sheetDataList.add(sheet1);
        } catch (Exception e) {
            sheet1 = null;
            throw new RuntimeException(e);
        }
        ExcelSheetData sheet2 = null;
        try {
            sheet2 = future2.get();
            sheetDataList.add(sheet2);
        } catch (Exception e) {
            sheet2 = null;
            throw new RuntimeException(e);
        }
        ExcelSheetData sheet3 = null;
        try {
            sheet3 = future3.get();
            sheetDataList.add(sheet3);
        } catch (Exception e) {
            sheet3 = null;
            throw new RuntimeException(e);
        }

        return sheetDataList;

     }

     private class ExcelSheetDataCallable implements Callable<ExcelSheetData> {
         private GetTaskFlowMetricCardDataRequestType request;
         private BusinessDashboardEmployeeInfo employeeInfo;
         private String queryD;
         private int sheetIndex;

         public ExcelSheetDataCallable(GetTaskFlowMetricCardDataRequestType request, BusinessDashboardEmployeeInfo employeeInfo, String queryD, int sheetIndex) {
             this.request = request;
             this.employeeInfo = employeeInfo;
             this.queryD = queryD;
             this.sheetIndex = sheetIndex;
         }

         @Override
         public ExcelSheetData call() {
             if (sheetIndex == 1) {
                 try {
                     return sheet1New(request, employeeInfo, queryD);
                 } catch (Exception e) {
                     log.error("sheet1 error,request:{}",MapperUtil.obj2Str(request), e);
                     return new ExcelSheetData();
                 }
             } else if (sheetIndex == 2) {
                 try {
                     return sheet2(request, employeeInfo, queryD);
                 } catch (Exception e) {
                     log.error("sheet2 error,request:{}",MapperUtil.obj2Str(request), e);
                     return new ExcelSheetData();
                 }
             } else if (sheetIndex == 3) {
                 try {
                     return sheet3(request, employeeInfo, queryD);
                 } catch (Exception e) {
                     log.error("sheet3 error,request:{}",MapperUtil.obj2Str(request), e);
                     return new ExcelSheetData();
                 }
             }
             return new ExcelSheetData();
         }
     }

    //查询sheet1数据
    private ExcelSheetData sheet1New(GetTaskFlowMetricCardDataRequestType request, BusinessDashboardEmployeeInfo employeeInfo,String d) throws Exception {

        GetTaskFlowTableDataResponseType response = new GetTaskFlowTableDataResponseType();
        List<TableDataItem> tableDataItemList = new ArrayList<>();
        response.setTableDataItemList(tableDataItemList);

        Map<String, String> andMap = new HashMap<>();
        TaskFlowHelper.setAllSubordinateCondition(andMap, employeeInfo, remoteConfig, organizationInfoDao, employeeInfoDao);
        SqlParamterBean bean = TaskFlowHelper.getAllSubordinateTableDownloadBean(request, andMap, d);

        GetRawDataRequestType tableRequest = bean.convertBeanToRequest(true);
        GetRawDataResponseType tableResponse = taskFlowSelect43556Helper.getRawDataBy43556(tableRequest, "");

        TaskFlowHelper.processTableData(tableResponse, tableDataItemList);


        String sheetName = "员工指标情况"; //NOSONAR
        List<String> heads = Lists.newArrayList("员工姓名", "按时完成率", "完成率", "按时完成任务数", "完成任务数", "接收任务数" ,"超时任务数" ,"平均处理时长(分钟)" ,"新增任务数"); //NOSONAR
        List<List<String>> resultList = new ArrayList<>();
        //转格式
        for(TableDataItem tableDataItem : tableDataItemList){
            List<String> row = new ArrayList<>();
            row.add(tableDataItem.getFieldMap().get("display_name"));

            //按时完成率
            String ontimeCompleteRateStr = tableDataItem.getDimMap().get("ontime_complete_rate")==null?"":String.valueOf(tableDataItem.getDimMap().get("ontime_complete_rate"));
            if(StringUtils.isNotBlank(ontimeCompleteRateStr)){
                int percentage = (int)Math.round(Double.parseDouble(ontimeCompleteRateStr) * 100);
                ontimeCompleteRateStr = percentage+"%";
            }else{
                ontimeCompleteRateStr = "-";
            }
            row.add(ontimeCompleteRateStr);

            //完成率
            String completeRateStr = tableDataItem.getDimMap().get("complete_rate")==null?"":String.valueOf(tableDataItem.getDimMap().get("complete_rate"));
            if(StringUtils.isNotBlank(completeRateStr)){
                int percentage = (int)Math.round(Double.parseDouble(completeRateStr) * 100);
                completeRateStr = percentage+"%";
            }else{
                completeRateStr = "-";
            }
            row.add(completeRateStr);

            //按时完成任务数
            String ontimeCompleteTaskCntStr = tableDataItem.getDimMap().get("ontime_complete_task_cnt")==null?"":String.valueOf(tableDataItem.getDimMap().get("ontime_complete_task_cnt"));
            if(StringUtils.isNotBlank(ontimeCompleteTaskCntStr)){
                ontimeCompleteTaskCntStr = String.valueOf((int)Double.parseDouble(ontimeCompleteTaskCntStr));
            }else{
                ontimeCompleteTaskCntStr = "-";
            }
            row.add(ontimeCompleteTaskCntStr);

            //完成任务数
            String completeTaskCntStr = tableDataItem.getDimMap().get("complete_task_cnt")==null?"":String.valueOf(tableDataItem.getDimMap().get("complete_task_cnt"));
            if(StringUtils.isNotBlank(completeTaskCntStr)){
                completeTaskCntStr = String.valueOf((int)Double.parseDouble(completeTaskCntStr));
            }else{
                completeTaskCntStr = "-";
            }
            row.add(completeTaskCntStr);

            //接收任务数
            String recieveTaskCntStr = tableDataItem.getDimMap().get("recieve_task_cnt")==null?"":String.valueOf(tableDataItem.getDimMap().get("recieve_task_cnt"));
            if(StringUtils.isNotBlank(recieveTaskCntStr)){
                recieveTaskCntStr = String.valueOf((int)Double.parseDouble(recieveTaskCntStr));
            }else{
                recieveTaskCntStr = "-";
            }
            row.add(recieveTaskCntStr);

            //超时任务数
            String overtimeEventCntStr = tableDataItem.getDimMap().get("overtime_event_cnt")==null?"":String.valueOf(tableDataItem.getDimMap().get("overtime_event_cnt"));
            if(StringUtils.isNotBlank(overtimeEventCntStr)){
                overtimeEventCntStr = String.valueOf((int)Double.parseDouble(overtimeEventCntStr));
            }else{
                overtimeEventCntStr = "-";
            }
            row.add(overtimeEventCntStr);

            //平均处理时长(分钟)
            String averageProcessTimeStr = tableDataItem.getDimMap().get("average_process_time")==null?"":String.valueOf(tableDataItem.getDimMap().get("average_process_time"));
            if(StringUtils.isNotBlank(averageProcessTimeStr)){
                averageProcessTimeStr = String.valueOf((int)Double.parseDouble(averageProcessTimeStr));
            }else{
                averageProcessTimeStr = "-";
            }
            row.add(averageProcessTimeStr);

            //新增任务数
            String newTaskCntStr = tableDataItem.getDimMap().get("new_task_cnt")==null?"":String.valueOf(tableDataItem.getDimMap().get("new_task_cnt"));
            if(StringUtils.isNotBlank(newTaskCntStr)){
                newTaskCntStr = String.valueOf((int)Double.parseDouble(newTaskCntStr));
            }else{
                newTaskCntStr = "-";
            }
            row.add(newTaskCntStr);

            resultList.add(row);
        }

        return new ExcelSheetData(sheetName, heads, resultList);

    }

    //查询sheet1数据
    private ExcelSheetData sheet1(GetTaskFlowMetricCardDataRequestType request, BusinessDashboardEmployeeInfo employeeInfo,String d) throws Exception {

        //查出所有下属
        List<BusinessDashboardEmployeeInfo> employeeInfoList = new ArrayList<>();
        //获取当前员工信息
//        BusinessDashboardEmployeeInfo selfEmployeeInfo = employeeInfoDao.queryByEmpCode(empCode);
        employeeInfoList.add(employeeInfo);
        //查出其所有下属
        List<BusinessDashboardOrganizationInfo> organizationInfoList = organizationInfoDao.queryByLeaderEmpCode(employeeInfo.getEmpCode());
        List<String> nodeOrgIdList = organizationInfoList.stream().map(BusinessDashboardOrganizationInfo::getNodeOrgId).collect(Collectors.toList());
        List<BusinessDashboardEmployeeInfo> subordinateList = employeeInfoDao.getAllSubordinateList(nodeOrgIdList);
        if(CollectionUtils.isNotEmpty(subordinateList)){
            employeeInfoList.addAll(subordinateList);
        }
        //查出特殊场景的下属
        String specailRegionOperate = remoteConfig.getExternalConfig("specailRegionOperate");
        if (specailRegionOperate.equals(employeeInfo.getDomainName())) {
            String specialRegionId = remoteConfig.getExternalConfig("specialRegionId");
            List<BusinessDashboardEmployeeInfo> specialSubordinateList = employeeInfoDao.getAllSubordinateList(Lists.newArrayList(specialRegionId));
            if(CollectionUtils.isNotEmpty(specialSubordinateList)){
                employeeInfoList.addAll(specialSubordinateList);
            }
        }

        List<TableDataItem> tableDataItemList = batchQuerySheet1(employeeInfoList,request,d);

        String sheetName = "员工指标情况"; //NOSONAR
        List<String> heads = Lists.newArrayList("员工姓名", "按时完成率", "完成率", "按时完成任务数", "完成任务数", "接收任务数" ,"超时任务数" ,"平均处理时长(分钟)" ,"新增任务数"); //NOSONAR
        List<List<String>> resultList = new ArrayList<>();
        //转格式
        for(TableDataItem tableDataItem : tableDataItemList){
            List<String> row = new ArrayList<>();
            row.add(tableDataItem.getFieldMap().get("display_name"));

            //按时完成率
            String ontimeCompleteRateStr = tableDataItem.getDimMap().get("ontime_complete_rate")==null?"":String.valueOf(tableDataItem.getDimMap().get("ontime_complete_rate"));
            if(StringUtils.isNotBlank(ontimeCompleteRateStr)){
                int percentage = (int)Math.round(Double.parseDouble(ontimeCompleteRateStr) * 100);
                ontimeCompleteRateStr = percentage+"%";
            }else{
                ontimeCompleteRateStr = "-";
            }
            row.add(ontimeCompleteRateStr);

            //完成率
            String completeRateStr = tableDataItem.getDimMap().get("complete_rate")==null?"":String.valueOf(tableDataItem.getDimMap().get("complete_rate"));
            if(StringUtils.isNotBlank(completeRateStr)){
                int percentage = (int)Math.round(Double.parseDouble(completeRateStr) * 100);
                completeRateStr = percentage+"%";
            }else{
                completeRateStr = "-";
            }
            row.add(completeRateStr);

            //按时完成任务数
            String ontimeCompleteTaskCntStr = tableDataItem.getDimMap().get("ontime_complete_task_cnt")==null?"":String.valueOf(tableDataItem.getDimMap().get("ontime_complete_task_cnt"));
            if(StringUtils.isNotBlank(ontimeCompleteTaskCntStr)){
                ontimeCompleteTaskCntStr = String.valueOf((int)Double.parseDouble(ontimeCompleteTaskCntStr));
            }else{
                ontimeCompleteTaskCntStr = "-";
            }
            row.add(ontimeCompleteTaskCntStr);

            //完成任务数
            String completeTaskCntStr = tableDataItem.getDimMap().get("complete_task_cnt")==null?"":String.valueOf(tableDataItem.getDimMap().get("complete_task_cnt"));
            if(StringUtils.isNotBlank(completeTaskCntStr)){
                completeTaskCntStr = String.valueOf((int)Double.parseDouble(completeTaskCntStr));
            }else{
                completeTaskCntStr = "-";
            }
            row.add(completeTaskCntStr);

            //接收任务数
            String recieveTaskCntStr = tableDataItem.getDimMap().get("recieve_task_cnt")==null?"":String.valueOf(tableDataItem.getDimMap().get("recieve_task_cnt"));
            if(StringUtils.isNotBlank(recieveTaskCntStr)){
                recieveTaskCntStr = String.valueOf((int)Double.parseDouble(recieveTaskCntStr));
            }else{
                recieveTaskCntStr = "-";
            }
            row.add(recieveTaskCntStr);

            //超时任务数
            String overtimeEventCntStr = tableDataItem.getDimMap().get("overtime_event_cnt")==null?"":String.valueOf(tableDataItem.getDimMap().get("overtime_event_cnt"));
            if(StringUtils.isNotBlank(overtimeEventCntStr)){
                overtimeEventCntStr = String.valueOf((int)Double.parseDouble(overtimeEventCntStr));
            }else{
                overtimeEventCntStr = "-";
            }
            row.add(overtimeEventCntStr);

            //平均处理时长(分钟)
            String averageProcessTimeStr = tableDataItem.getDimMap().get("average_process_time")==null?"":String.valueOf(tableDataItem.getDimMap().get("average_process_time"));
            if(StringUtils.isNotBlank(averageProcessTimeStr)){
                averageProcessTimeStr = String.valueOf((int)Double.parseDouble(averageProcessTimeStr));
            }else{
                averageProcessTimeStr = "-";
            }
            row.add(averageProcessTimeStr);

            //新增任务数
            String newTaskCntStr = tableDataItem.getDimMap().get("new_task_cnt")==null?"":String.valueOf(tableDataItem.getDimMap().get("new_task_cnt"));
            if(StringUtils.isNotBlank(newTaskCntStr)){
                newTaskCntStr = String.valueOf((int)Double.parseDouble(newTaskCntStr));
            }else{
                newTaskCntStr = "-";
            }
            row.add(newTaskCntStr);

            resultList.add(row);
        }

        return new ExcelSheetData(sheetName, heads, resultList);

//        return new ExcelSheetData(sheetName, heads, Lists.newArrayList(Lists.newArrayList("c", "d"), Lists.newArrayList("e", "f")));

    }

    private List<TableDataItem> batchQuerySheet1(List<BusinessDashboardEmployeeInfo> employeeInfoList, GetTaskFlowMetricCardDataRequestType request, String d) throws Exception {
        TaskFlowMetricCardBiz bean = ac.getBean(TaskFlowMetricCardBiz.class);
        //分别查出所有下属的指标卡数据
        Map<String,BusinessDashboardEmployeeInfo> empInfoMap = new HashMap<>();
        for(BusinessDashboardEmployeeInfo employeeInfo : employeeInfoList){
            empInfoMap.put(employeeInfo.getEmpCode(),employeeInfo);
        }

        Map<String, Future<GetTaskFlowMetricCardDataResponseType>> futureMap = new HashMap<>();
        for (BusinessDashboardEmployeeInfo employeeInfo : empInfoMap.values()) {
            GetTaskFlowMetricCardDataRequestType metricCardRequest = new GetTaskFlowMetricCardDataRequestType();
            metricCardRequest.setDateType(request.getDateType());
            metricCardRequest.setStartDate(request.getStartDate());
            metricCardRequest.setEndDate(request.getEndDate());
            metricCardRequest.setStatisticalScope(request.getStatisticalScope());
            metricCardRequest.setTaskDimInfoList(request.getTaskDimInfoList());
            metricCardRequest.setTaskLevelScoreRange(request.getTaskLevelScoreRange());
            futureMap.put(employeeInfo.getEmpCode(), bean.getTaskFlowMetricCardData(request, employeeInfo.getEmpCode(), false,d));
        }

        List<TableDataItem> tableDataItemList = new ArrayList<>();
        for (Map.Entry<String, Future<GetTaskFlowMetricCardDataResponseType>> entry : futureMap.entrySet()) {
            GetTaskFlowMetricCardDataResponseType metricCardResponse = entry.getValue().get();
            if ("noData".equals(metricCardResponse.getStatus())) {
                continue;
            }
            TableDataItem tableDataItem = new TableDataItem();
            Map<String, String> fieldMap = new HashMap<>();
            fieldMap.put("emp_code", entry.getKey());
            fieldMap.put("display_name", empInfoMap.get(entry.getKey()).getDisplayName());
            tableDataItem.setFieldMap(fieldMap);
            tableDataItem.setDimMap(metricCardResponse.getDimData());
            tableDataItemList.add(tableDataItem);
        }
        return tableDataItemList;
    }


    //查询sheet2数据:超时任务明细
    private ExcelSheetData sheet2(GetTaskFlowMetricCardDataRequestType request, BusinessDashboardEmployeeInfo employeeInfo, String d) throws Exception {

        Map<String, String> andMap = new HashMap<>();
//        BusinessDashboardEmployeeInfo employeeInfo = TaskFlowHelper.getActualEmployeeInfo(empCode, d, remoteConfig, examineConfigV2Dao, employeeInfoDao);
        TaskFlowHelper.setAllSubordinateCondition(andMap, employeeInfo, remoteConfig, organizationInfoDao, employeeInfoDao);
        andMap.put("is_overtime", "1");
        SqlParamterBean bean = TaskFlowHelper.getTaskDetailBean(request, andMap, d);
        GetRawDataRequestType tableRequest = bean.convertBeanToRequest(true);
        List<Map<String, Object>> rowMapList = taskFlowSelect43556Helper.getStarRocksData(tableRequest,"task_id");

        List<Map<String, String>> rowStrMapList = rowMapList.stream()
                .map(rowMap -> rowMap.entrySet().stream()
                        .filter(entry -> StringUtils.isNotBlank(String.valueOf(entry.getValue())))
                        .collect(Collectors.toMap(Map.Entry::getKey, entry -> String.valueOf(entry.getValue()), (a, b) -> b))
                )
                .collect(Collectors.toList());
        rowMapList = null;

        String sheetName = "超时任务明细"; //NOSONAR
        List<String> heads = Lists.newArrayList("任务ID","任务标题", "任务节点ID", "业务类别", "任务大类", "任务类型", "任务得分", "接收员工姓名", "接收时间", "指标时间", "完成时间"); //NOSONAR

        Map<String, Map<String, String>> dataMap = taskFlowSyncJob.getEumnDataTypeMap();
        Map<String, String> bizCategoryCodeMap = dataMap.getOrDefault("biz_category_code",new HashMap<>());
        Map<String, String> taskCollectionCodeMap = dataMap.getOrDefault("task_collection_code",new HashMap<>());
        Map<String, String> taskTypeCodeMap = dataMap.getOrDefault("task_type_code", new HashMap<>());

        List<List<String>> resultList = new ArrayList<>();
        for(Map<String,String> rowMap :rowStrMapList){
            List<String> resultRow = new ArrayList<>();
            resultRow.add(rowMap.getOrDefault("task_id","-")); //任务ID
            resultRow.add(rowMap.getOrDefault("task_name","-"));// 任务标题
            resultRow.add(rowMap.getOrDefault("event_id","-"));//任务节点ID/(事件id)

            String bizCategoryName = bizCategoryCodeMap.getOrDefault(rowMap.getOrDefault("biz_category_code","-"),"-");  // "业务类别"
            resultRow.add(bizCategoryName);
            String taskCollectionName = taskCollectionCodeMap.getOrDefault(rowMap.getOrDefault("task_collection_code","-"),"-");  // "任务大类"
            resultRow.add(taskCollectionName);
            String taskTypeName = taskTypeCodeMap.getOrDefault(rowMap.getOrDefault("task_type_code","-"),"-");  // "任务类型"
            resultRow.add(taskTypeName);


            resultRow.add(rowMap.getOrDefault("task_level_score","-"));// "任务得分"
            resultRow.add(rowMap.getOrDefault("display_name","-"));// "接收员工姓名"
            resultRow.add(rowMap.getOrDefault("send_time","-"));// "接收时间"
            resultRow.add(rowMap.getOrDefault("the_date","-"));// "指标时间"
            resultRow.add(rowMap.getOrDefault("complete_time","-"));// "完成时间"
            resultList.add(resultRow);

        }
        rowStrMapList = null;

        //转格式
//        return new ExcelSheetData("sheetName", Lists.newArrayList("c", "d"), Lists.newArrayList(Lists.newArrayList("c", "d"), Lists.newArrayList("e", "f")));
        return new ExcelSheetData(sheetName, heads, resultList);
    }


    //查询sheet3数据:全量任务明细
    private ExcelSheetData sheet3(GetTaskFlowMetricCardDataRequestType request, BusinessDashboardEmployeeInfo employeeInfo, String d) throws Exception {

        Map<String, String> andMap = new HashMap<>();
//        BusinessDashboardEmployeeInfo employeeInfo = TaskFlowHelper.getActualEmployeeInfo(empCode, d, remoteConfig, examineConfigV2Dao, employeeInfoDao);
        TaskFlowHelper.setAllSubordinateCondition(andMap, employeeInfo, remoteConfig, organizationInfoDao, employeeInfoDao);

        SqlParamterBean bean = TaskFlowHelper.getTaskDetailBean(request, andMap, d);
        GetRawDataRequestType tableRequest = bean.convertBeanToRequest(true);
        List<Map<String, Object>> rowMapList = taskFlowSelect43556Helper.getStarRocksData(tableRequest,"task_id");
        List<Map<String, String>> rowStrMapList = rowMapList.stream()
                .map(rowMap -> rowMap.entrySet().stream()
                        .filter(entry -> StringUtils.isNotBlank(String.valueOf(entry.getValue())))
                        .collect(Collectors.toMap(Map.Entry::getKey, entry -> String.valueOf(entry.getValue()), (a, b) -> b))
                )
                .collect(Collectors.toList());
        rowMapList = null;

        String sheetName = "全量明细"; //NOSONAR
        List<String> heads = Lists.newArrayList("任务ID","任务标题", "任务节点ID", "业务类别", "任务大类", "任务类型", "任务得分", "接收员工姓名", "接收时间", "指标时间", "完成时间", "任务节点状态", "是否超时"); //NOSONAR

        Map<String, Map<String, String>> dataMap = taskFlowSyncJob.getEumnDataTypeMap();
        Map<String, String> bizCategoryCodeMap = dataMap.getOrDefault("biz_category_code",new HashMap<>());
        Map<String, String> taskCollectionCodeMap = dataMap.getOrDefault("task_collection_code",new HashMap<>());
        Map<String, String> taskTypeCodeMap = dataMap.getOrDefault("task_type_code", new HashMap<>());

        List<List<String>> resultList = new ArrayList<>();
        for(Map<String,String> rowMap :rowStrMapList){
            List<String> resultRow = new ArrayList<>();
            resultRow.add(rowMap.getOrDefault("task_id","-")); //任务ID
            resultRow.add(rowMap.getOrDefault("task_name","-"));// 任务标题
            resultRow.add(rowMap.getOrDefault("event_id","-"));//任务节点ID/(事件id)

            String bizCategoryName = bizCategoryCodeMap.getOrDefault(rowMap.getOrDefault("biz_category_code","-"),"-");  // "业务类别"
            resultRow.add(bizCategoryName);
            String taskCollectionName = taskCollectionCodeMap.getOrDefault(rowMap.getOrDefault("task_collection_code","-"),"-");  // "任务大类"
            resultRow.add(taskCollectionName);
            String taskTypeName = taskTypeCodeMap.getOrDefault(rowMap.getOrDefault("task_type_code","-"),"-");  // "任务类型"
            resultRow.add(taskTypeName);


            resultRow.add(rowMap.getOrDefault("task_level_score","-"));// "任务得分"
            resultRow.add(rowMap.getOrDefault("display_name","-"));// "接收员工姓名"
            resultRow.add(rowMap.getOrDefault("send_time","-"));// "接收时间"
            resultRow.add(rowMap.getOrDefault("the_date","-"));// "指标时间"
            resultRow.add(rowMap.getOrDefault("complete_time","-"));// "完成时间"
            resultRow.add(rowMap.getOrDefault("status_name","-"));// "任务节点状态"
            String isOvertime = rowMap.get("is_overtime");  // "是否超时"
            resultRow.add(StringUtils.isBlank(isOvertime)?"-":("1".equals(isOvertime)?"是":"否")); //NOSONAR
            resultList.add(resultRow);

        }
        rowStrMapList = null;

        //转格式
        return new ExcelSheetData(sheetName, heads, resultList);
    }

    //    @Override
//    public void test(GetTaskFlowMetricCardDataRequestType requestType,String empCode) throws UnsupportedEncodingException, ParseException, SQLException {
//
//        //获取数据更新时间
//        String queryD = null;
//        try {
//            queryD = dataUpdateBiz.getTaskBoardUpdateTime();
//        } catch (Exception e) {
//            return;
//        }
//
//        String dateRange = DateUtil.getTimeRange(requestType.getDateType(), queryD, false, requestType.getStartDate(), requestType.getEndDate());  //时间范围拼接
//        String newDateRange = dateRange.replace("-","").replace("|","-");
//        String statisticalScope = statisticalScopeMap.get(requestType.getStatisticalScope());
//        TaskLevelScoreRange taskLevelScoreRange = requestType.getTaskLevelScoreRange();
//        String scoreRange = "";
//        if(taskLevelScoreRange!=null && taskLevelScoreRange.getMinScore()!=null && taskLevelScoreRange.getMaxScore()!=null){
//            scoreRange = taskLevelScoreRange.getMinScore()+"-"+taskLevelScoreRange.getMaxScore()+"分";
//        }else {
//            scoreRange = "0-10分";
//        }
//        BusinessDashboardEmployeeInfo employeeInfo = TaskFlowHelper.getActualEmployeeInfo(empCode, queryD, remoteConfig, examineConfigV2Dao, employeeInfoDao);
//        if(employeeInfo==null || StringUtils.isBlank(employeeInfo.getDisplayName())){
//            employeeInfo = new BusinessDashboardEmployeeInfo();
//        }
//        String fileName = UriUtils.encode(newDateRange+"任务看板数据_"+statisticalScope+"_"+scoreRange+"_"+employeeInfo.getDisplayName(), "UTF-8");
//        HttpResponseWrapper responseWrapper = HttpRequestContext.getInstance().response();
//        HttpRequestWrapper requestWrapper = HttpRequestContext.getInstance().request();
//        requestWrapper.setResponseContentType("application/octet-stream");
//        responseWrapper.setHeader("Content-Disposition", "attachment;filename="+fileName+".xlsx");
//        responseWrapper.setHeader("Access-Control-Allow-Origin","*");
//        responseWrapper.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
//
//        byte[] bytes = null;
//        List<ExcelSheetData> sheetDataList = null;
//        try {
//            sheetDataList = getTaskFlowDetailExcelAsync(requestType, employeeInfo, queryD);
//            bytes = ExcelUtil.outputExcel(sheetDataList);
//            responseWrapper.setHeader("Content-Length",String.valueOf(bytes.length));
//            responseWrapper.getResponseStream().write(bytes);
//        } catch (IOException e) {
//            sheetDataList = null;
//            bytes = null;
//            throw new RuntimeException(e);
//        }
//
//
//
//    }

}