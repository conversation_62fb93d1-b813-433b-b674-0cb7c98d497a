package com.ctrip.tour.business.dashboard.grpBusiness.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @Date 2024/12/9
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface MetricData {

    String value();

    /**
     * 日期名称
     * @return
     */
    boolean needWeekOverWeek() default false;

    /**
     * 计算公式
     *
     * 直接写要取值的key如 sql为select xx as a, xx as query_d from table,
     * 则公式可以写 a/b,支持加减乘除及括号
     * @return
     */
    boolean needMonthOveMonth() default false;

    boolean needYearOverYear() default false;
    /**
     * 需要计算的属性名，就是公式中参与计算的名称，不做按周按月聚合可为空
     * @return
     */
    boolean isRatio() default false;


}
