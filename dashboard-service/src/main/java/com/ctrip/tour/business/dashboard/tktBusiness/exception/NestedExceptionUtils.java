package com.ctrip.tour.business.dashboard.tktBusiness.exception;

import com.ctriposs.baiji.rpc.common.types.AckCodeType;
import com.ctriposs.baiji.rpc.common.types.ErrorDataType;
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType;
import com.ctriposs.baiji.rpc.common.types.SeverityCodeType;

import java.util.Collections;

public abstract class NestedExceptionUtils {
    /**
     * Build a message for the given dto message and root cause.
     *
     * @param message the dto message
     * @param cause the root cause
     * @return the full exception message
     */
    public static String buildMessage(String message, Throwable cause) {
        if (cause != null) {
            StringBuilder sb = new StringBuilder();
            if (message != null) {
                sb.append(message).append("; ");
            }
            sb.append("nested exception is ").append(cause);
            return sb.toString();
        } else {
            return message;
        }
    }
    public static ResponseStatusType responseFail(Throwable e) {
        ResponseStatusType responseStatusType = new ResponseStatusType();
        responseStatusType.ack = AckCodeType.Failure;
        ErrorDataType errorDataType = new ErrorDataType();
        errorDataType.stackTrace = NestedExceptionUtils.buildMessage(e.getMessage(), e.getCause());
        errorDataType.severityCode = SeverityCodeType.Error;
        if (e instanceof NestedRuntimeException) {
            NestedRuntimeException exception = (NestedRuntimeException) e;
            errorDataType.errorCode = exception.getCode();
            errorDataType.message = exception.getRootCause() == null ? e.getMessage() : exception.getRootCause().getMessage();
        } else {
            if (null == e.getCause()) {
                errorDataType.message = e.getMessage();
            } else {
                while (null != e.getCause()) {
                    e = e.getCause();
                    errorDataType.message = e.getMessage();
                }
            }
            errorDataType.errorCode = "500";
        }
        responseStatusType.errors = Collections.singletonList(errorDataType);
        return responseStatusType;
    }
}
