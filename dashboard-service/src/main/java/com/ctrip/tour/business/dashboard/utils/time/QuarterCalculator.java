package com.ctrip.tour.business.dashboard.utils.time;

import lombok.AllArgsConstructor;
import lombok.Data;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Data
@AllArgsConstructor
/**
 * 季度计算器，用于处理季度相关的日期计算、转换和比较
 */
public class QuarterCalculator implements Cloneable, Comparable<QuarterCalculator> {
    private Integer year;
    private Integer quarterNum;


    /**
     * 初始化当前季度的QuarterCalculator实例
     */
    public QuarterCalculator() {
        QuarterCalculator quarterCalculator = QuarterCalculator.getByDate(DateTime.now().toString("yyyy-MM-dd"));
        year = quarterCalculator.getYear();
        quarterNum = quarterCalculator.getQuarterNum();
    }

    /**
     * 根据季度字符串创建QuarterCalculator实例（如"2023Q1"或"2023q1"）
     *
     * @param quarterString 季度字符串
     * @return 对应的QuarterCalculator实例
     */
    public static QuarterCalculator getByQuarterString(String quarterString) {
        QuarterCalculator quarterCalculator = new QuarterCalculator();
        quarterCalculator.setQuarterString(quarterString);
        return quarterCalculator;
    }

    /**
     * 根据日期字符串获取对应的季度信息
     *
     * @param date 日期字符串，格式为yyyy-MM-dd
     * @return 对应的QuarterCalculator实例
     */
    public static QuarterCalculator getByDate(String date) {
        QuarterCalculator clone = new QuarterCalculator(0, 0);
        DateTime now = DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(date);
        clone.setQuarterString(now.getYear() + "Q" + ((now.getMonthOfYear() - 1) / 3 + 1));
        return clone;
    }

    /**
     * 获取季度字符串（格式：yyyyQn）
     *
     * @return 季度字符串，如"2023Q1"
     */
    public String getQuarterString() {
        if (year != null && quarterNum != null) {
            return year + "Q" + quarterNum;
        } else {
            return null;
        }
    }

    /**
     * 获取带连字符的季度字符串（格式：yyyy-Qn）
     *
     * @return 带连字符的季度字符串，如"2023-Q1"
     */
    public String getQuarterStringWithDash() {
        if (year != null && quarterNum != null) {
            return year + "-Q" + quarterNum;
        } else {
            return null;
        }
    }

    /**
     * 设置季度字符串（支持格式：yyyyQn或yyyyqn）
     *
     * @param quarterString 季度字符串
     */
    public void setQuarterString(String quarterString) {
        try {
            String[] qs = null;
            if (quarterString.contains("Q")) {
                qs = quarterString.split("Q");
            } else if (quarterString.contains("q")) {
                qs = quarterString.split("q");
            } else {
                throw new IllegalArgumentException("Invalid quarter expression"); // 不合法的季度表达 → Invalid quarter expression
            }

            this.year = Integer.parseInt(qs[0]);
            this.quarterNum = Integer.parseInt(qs[1]);
            Assert.isTrue(quarterNum > 0 && quarterNum < 5, "Invalid quarter parameter!"); // 不合法的季度参数！ → Invalid quarter parameter!
        } catch (Exception e) {
            throw new IllegalArgumentException("Incorrect parameter:" + quarterString, e); // 不正确的参数: → Incorrect parameter:
        }
    }

    /**
     * 获取当前季度的开始日期（格式：yyyy-MM-dd）
     *
     * @return 季度开始日期
     */
    public String getDateStart() {
        DateTime dateTime;
        switch (quarterNum) {
            case 1:
                dateTime = new DateTime(year, 1, 1, 0, 0);
                break;
            case 2:
                dateTime = new DateTime(year, 4, 1, 0, 0);
                break;
            case 3:
                dateTime = new DateTime(year, 7, 1, 0, 0);
                break;
            case 4:
                dateTime = new DateTime(year, 10, 1, 0, 0);
                break;
            default:
                throw new IllegalArgumentException("Invalid quarter parameter!"); // 不合法的季度参数！ → Invalid quarter parameter!
        }
        return dateTime.toString("yyyy-MM-dd");
    }

    /**
     * 获取当前季度的结束日期（格式：yyyy-MM-dd）
     *
     * @return 季度结束日期
     */
    public String getDateEnd() {
        DateTime dateTime;
        switch (quarterNum) {
            case 1:
                dateTime = new DateTime(year, 3, 31, 0, 0);
                break;
            case 2:
                dateTime = new DateTime(year, 6, 30, 0, 0);
                break;
            case 3:
                dateTime = new DateTime(year, 9, 30, 0, 0);
                break;
            case 4:
                dateTime = new DateTime(year, 12, 31, 0, 0);
                break;
            default:
                throw new IllegalArgumentException("Invalid quarter parameter!"); // 不合法的季度参数！ → Invalid quarter parameter!
        }
        return dateTime.toString("yyyy-MM-dd");
    }

    /**
     * 获取当前季度的结束日期，若结束日期在未来则返回当前日期
     *
     * @return 调整后的季度结束日期（格式：yyyy-MM-dd）
     */
    public String getDateEndWithoutFuture() {
        String dateEnd = getDateEnd();
        DateTime now = DateTime.now();
        DateTimeFormatter formatter = DateTimeFormat.forPattern("yyyy-MM-dd");
        if (formatter.parseDateTime(dateEnd).isAfter(now)) {
            return now.toString(formatter);
        } else {
            return dateEnd;
        }
    }

    /**
     * 根据对比季度获取同期结束日期
     *
     * @param needCompareQuarter 对比季度
     * @return 同期结束日期（格式：yyyy-MM-dd）
     */
    public String getDateEndWithSamePeriod(QuarterCalculator needCompareQuarter) {
        DateTime now = DateTime.now();
        String dateEnd = needCompareQuarter.getDateEnd();
        DateTimeFormatter formatter = DateTimeFormat.forPattern("yyyy-MM-dd");
        if (formatter.parseDateTime(dateEnd).isBefore(now)) {
            String dateStart = needCompareQuarter.getDateStart();
            int days = Days.daysBetween(formatter.parseDateTime(dateStart), formatter.parseDateTime(dateEnd)).getDays();
            return formatter.parseDateTime(getDateStart()).plusDays(days).toString(formatter);
        } else {
            return getDateEnd();
        }
    }

    /**
     * 根据日期获取上一季度的结束日期
     *
     * @param date 日期字符串（格式：yyyy-MM-dd）
     * @return 上一季度结束日期（格式：yyyy-MM-dd）
     */
    public static String getDateEndOfLastQuarterByDate(String date) {
        DateTimeFormatter df = DateTimeFormat.forPattern("yyyy-MM-dd");
        return date.equals(DateTime.parse(date, df).dayOfMonth().withMaximumValue().toString(df))
                ? DateTime.parse(date, df).minusMonths(3).dayOfMonth().withMaximumValue().toString(df)
                : DateTime.parse(date, df).minusMonths(3).toString(df);
    }

    /**
     * 根据日期获取上一年同期的结束日期
     *
     * @param date 日期字符串（格式：yyyy-MM-dd）
     * @return 上一年同期结束日期（格式：yyyy-MM-dd）
     */
    public static String getDateEndOfLastYearByDate(String date) {
        DateTimeFormatter df = DateTimeFormat.forPattern("yyyy-MM-dd");
        return date.equals(DateTime.parse(date, df).dayOfMonth().withMaximumValue().toString(df))
                ? DateTime.parse(date, df).minusYears(1).dayOfMonth().withMaximumValue().toString(df)
                : DateTime.parse(date, df).minusYears(1).toString(df);
    }

    /**
     * 获取包含当前季度在内的前N个季度
     *
     * @param lastQuarterCount 需要获取的季度数量
     * @return 季度列表（按时间升序排列）
     */
    public List<QuarterCalculator> getLastQuarterIncludeThisByCount(int lastQuarterCount) {
        List<QuarterCalculator> quarterCalculators = new ArrayList<>(lastQuarterCount);
        quarterCalculators.add(this);
        QuarterCalculator quarterCalculator = this;
        for (int i = 0; i < lastQuarterCount - 1; i++) {
            quarterCalculator = quarterCalculator.getLastQuarter();
            quarterCalculators.add(quarterCalculator);
        }
        Collections.reverse(quarterCalculators);
        return quarterCalculators;
    }

    /**
     * 获取上一个季度
     *
     * @return 上一季度的QuarterCalculator实例
     */
    public QuarterCalculator getLastQuarter() {
        QuarterCalculator clone = this.clone();
        if (clone.quarterNum == 1) {
            clone.year -= 1;
            clone.quarterNum = 4;
        } else {
            clone.quarterNum -= 1;
        }
        return clone;
    }

    /**
     * 获取去年同期季度
     *
     * @return 去年同期季度的QuarterCalculator实例
     */
    public QuarterCalculator getLastYearSameQuarter() {
        QuarterCalculator clone = this.clone();
        clone.year -= 1;
        return clone;
    }

    /**
     * 获取同比日期范围（不考虑时间窗口概念）
     *
     * @return 同比日期范围
     */
    public DateRange getYoYDateRange() {
        QuarterCalculator lastYearSameQuarter = getLastYearSameQuarter();
        return new DateRange(lastYearSameQuarter.getDateStart(), lastYearSameQuarter.getDateEnd());
    }

    /**
     * 获取同比日期范围（考虑时间窗口概念）
     *
     * @return 对齐时间窗口的同比日期范围
     */
    public DateRange getYoYDateRangeWithWindow(String windowDate) {
        QuarterCalculator lastYearSameQuarter = getLastYearSameQuarter();
        DateTime now = DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(windowDate);
        DateTime currentQuarterEnd = DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(getDateEnd());

        // 如果当前季度已结束，返回完整季度
        if (now.isAfter(currentQuarterEnd)) {
            return new DateRange(lastYearSameQuarter.getDateStart(), lastYearSameQuarter.getDateEnd());
        }

        // 计算当前季度已过去的天数
        DateTime currentQuarterStart = DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(getDateStart());
        int daysPassed = Days.daysBetween(currentQuarterStart, now).getDays() + 1;

        // 计算同比季度对应的结束日期
        DateTime yoyStartDate = DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(lastYearSameQuarter.getDateStart());
        DateTime yoyEndDate = yoyStartDate.plusDays(daysPassed - 1);

        return new DateRange(lastYearSameQuarter.getDateStart(), yoyEndDate.toString("yyyy-MM-dd"));
    }

    /**
     * 获取环比日期范围（不考虑时间窗口概念）
     *
     * @return 环比日期范围
     */
    public DateRange getQoQDateRange() {
        QuarterCalculator lastQuarter = getLastQuarter();
        return new DateRange(lastQuarter.getDateStart(), lastQuarter.getDateEnd());
    }

    /**
     * 获取环比日期范围（考虑时间窗口概念）
     *
     * @return 对齐时间窗口的环比日期范围
     */
    public DateRange getQoQDateRangeWithWindow(String windowDate) {
        QuarterCalculator lastQuarter = getLastQuarter();
        DateTime now = DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(windowDate);
        DateTime currentQuarterEnd = DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(getDateEnd());

        // 如果当前季度已结束，返回完整季度
        if (now.isAfter(currentQuarterEnd)) {
            return new DateRange(lastQuarter.getDateStart(), lastQuarter.getDateEnd());
        }

        // 计算当前季度已过去的天数
        DateTime currentQuarterStart = DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(getDateStart());
        int daysPassed = Days.daysBetween(currentQuarterStart, now).getDays() + 1;

        // 计算环比季度对应的结束日期
        DateTime qoqStartDate = DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(lastQuarter.getDateStart());
        DateTime qoqEndDate = qoqStartDate.plusDays(daysPassed - 1);

        return new DateRange(lastQuarter.getDateStart(), qoqEndDate.toString("yyyy-MM-dd"));
    }

    /**
     * 获取下一个季度
     *
     * @return 下一季度的QuarterCalculator实例
     */
    public QuarterCalculator getNextQuarter() {
        QuarterCalculator clone = this.clone();
        if (4 == quarterNum) {
            clone.setYear(clone.getYear() + 1);
            clone.setQuarterNum(1);
        } else {
            clone.setQuarterNum(getQuarterNum() + 1);
        }
        return clone;
    }

    /**
     * 获取上一半年同期季度（当前季度对应上一半年的同期季度）
     * 例：Q2 2023（H1）的上一半年同期为 Q4 2022（H2 2022）
     *
     * @return 上一半年同期季度实例
     */
    public QuarterCalculator getLastHalfYearSameQuarter() {
        QuarterCalculator clone = this.clone();
        int newQuarterNum = clone.getQuarterNum() - 2;
        // 跨年度处理（如Q1 - 2 quarters → Q3 of last year）
        if (newQuarterNum < 1) {
            clone.setYear(clone.getYear() - 1);
            newQuarterNum += 4; // 全年4个季度，补全跨年度的季度数
        }
        clone.setQuarterNum(newQuarterNum);
        return clone;
    }

    /**
     * 获取上一半年同期日期范围（不考虑时间窗口）
     *
     * @return 上一半年同期季度的完整日期范围
     */
    public DateRange getLastHalfYearDateRange() {
        QuarterCalculator lastHalfYearSameQuarter = getLastHalfYearSameQuarter();
        return new DateRange(lastHalfYearSameQuarter.getDateStart(), lastHalfYearSameQuarter.getDateEnd());
    }

    /**
     * 获取上一半年同期日期范围（考虑时间窗口）
     *
     * @param windowDate 窗口日期，格式：yyyy-MM-dd
     * @return 对齐时间窗口的上一半年同期日期范围
     */
    public DateRange getLastHalfYearDateRangeWithWindow(String windowDate) {
        QuarterCalculator lastHalfYearSameQuarter = getLastHalfYearSameQuarter();
        DateTime windowDateTime = DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(windowDate);
        DateTime currentQuarterEnd = DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(getDateEnd());

        // 窗口日期在当前季度结束后，返回完整季度范围
        if (windowDateTime.isAfter(currentQuarterEnd)) {
            return new DateRange(lastHalfYearSameQuarter.getDateStart(), lastHalfYearSameQuarter.getDateEnd());
        }

        // 计算当前季度已过天数
        DateTime currentQuarterStart = DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(getDateStart());
        int daysPassed = Days.daysBetween(currentQuarterStart, windowDateTime).getDays() + 1;

        // 计算上一半年同期季度的对应日期范围
        DateTime compareStartDate = DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(lastHalfYearSameQuarter.getDateStart());
        DateTime compareEndDate = compareStartDate.plusDays(daysPassed - 1);

        // 处理天数溢出：不超过目标季度的最后一天
        DateTime targetQuarterEnd = DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(lastHalfYearSameQuarter.getDateEnd());
        if (compareEndDate.isAfter(targetQuarterEnd)) {
            compareEndDate = targetQuarterEnd;
        }

        return new DateRange(lastHalfYearSameQuarter.getDateStart(), compareEndDate.toString("yyyy-MM-dd"));
    }

    /**
     * 获取当前季度包含的月份（格式：yyyy-MM）
     *
     * @return 月份列表，如["2023-01", "2023-02", "2023-03"]
     */
    public List<String> getMonths() {
        List<String> months = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            months.add(year + "-" + String.format("%02d", ((quarterNum - 1) * 3 + 1 + i)));
        }
        return months;
    }

    /**
     * 获取两个季度之间的所有季度
     *
     * @param start        开始季度
     * @param end          结束季度
     * @param includeStart 是否包含开始季度
     * @param includeEnd   是否包含结束季度
     * @return 季度列表（按时间升序排列）
     */
    public static List<QuarterCalculator> getBetween(QuarterCalculator start, QuarterCalculator end, boolean includeStart, boolean includeEnd) {
        List<QuarterCalculator> result = new ArrayList<>();

        while (end.compareTo(start) >= 0) {
            result.add(end);
            end = end.getLastQuarter();
        }

        Collections.reverse(result);

        if (!includeStart) {
            result.remove(0);
        }
        if (!includeEnd && result.size() > 0) {
            result.remove(result.size() - 1);
        }
        return result;
    }

    @Override
    protected QuarterCalculator clone() {
        return new QuarterCalculator(year, quarterNum);
    }

    @Override
    public int compareTo(QuarterCalculator o) {
        int yearCompare = this.year - o.getYear();
        if (yearCompare == 0) {
            return this.quarterNum - o.getQuarterNum();
        } else {
            return yearCompare;
        }
    }

    @Override
    public String toString() {
        return getQuarterString();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof QuarterCalculator)) return false;

        QuarterCalculator that = (QuarterCalculator) o;

        if (getYear() != null ? !getYear().equals(that.getYear()) : that.getYear() != null) return false;
        return getQuarterNum() != null ? getQuarterNum().equals(that.getQuarterNum()) : that.getQuarterNum() == null;
    }

    @Override
    public int hashCode() {
        int result = getYear() != null ? getYear().hashCode() : 0;
        result = 31 * result + (getQuarterNum() != null ? getQuarterNum().hashCode() : 0);
        return result;
    }
}