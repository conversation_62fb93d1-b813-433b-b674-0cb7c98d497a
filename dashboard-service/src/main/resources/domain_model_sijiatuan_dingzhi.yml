domain_info:
  - db_name:
    id: 1
    table_name: adm_ord_grp_work_platform_prdt_df
    source_type: starrocks
    indicators:
      - indicator_name: suc_income
        indicator_name_cn: 销售额
        indicator_type: double
        indicator_format: double
        indicator_key: v.page.workbench.diyIncome.tips
        indicator_key_name: diyIncome
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: suc_profit
        indicator_name_cn: 毛利
        indicator_type: double
        indicator_format: double
        indicator_key: v.page.workbench.diyPorfit.tips
        indicator_key_name: diyProfit
        indicator_is_dimension: false
        indicator_is_support_sorting: true

      - indicator_name: ord_avg_price
        indicator_name_cn: 单均价
        indicator_type: double
        indicator_format: double
        indicator_key: v.page.workbench.ordAvgPrice.tips
        indicator_key_name: ordAvgPrice
        indicator_is_dimension: false
        indicator_is_support_sorting: true

      - indicator_name: date
        indicator_name_cn: 日期
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: true
      - indicator_name: date_week
        indicator_name_cn: 日期
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: true
      - indicator_name: date_month
        indicator_name_cn: 日期
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: true
      
      - indicator_name: grade_region_name
        indicator_name_cn: 评级区域
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: true
      - indicator_name: domain
        indicator_name_cn: 境内外
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: true
      - indicator_name: business_region_name
        indicator_name_cn: 大区
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: true

    template: |
      <#macro date_type_switch date_type>
          ${date_type.filterValues[0]?switch('PREORDER_DATE', 'order_date', 'TRIP_DATE', 'tour_depart_date', 'RETURN_DATE', 'tour_enddate')}
      </#macro>
      
      select 
              *
      from (
              SELECT  
                      1 as default_group
                      <#if indicators?seq_contains("suc_income")> ,sum(cus_suc_income) as suc_income </#if>
                      <#if indicators?seq_contains("suc_profit")> ,sum(cus_suc_profit) as suc_profit </#if>
                      <#if indicators?seq_contains("ord_avg_price")> ,sum(cus_suc_income) / sum(cus_suc_ord_cnt) as ord_avg_price </#if>
                      <#if group_by??>
                          <#list group_by as value>

                              <#if group_by?seq_contains("date")> ,date_format(to_date(<@date_type_switch date_type=filter_flat.date_type!{"filterValues": [""]}/>), '%Y-%m-%d') as date
                              <#elseif group_by?seq_contains("date_week")> ,date_format(date_sub(to_date(<@date_type_switch date_type=filter_flat.date_type!{"filterValues": [""]}/>), interval (dayofweek(to_date(<@date_type_switch date_type=filter_flat.date_type!{"filterValues": [""]}/>)) + 5) % 7  day), '%Y-%m-%d') as date_week
                              <#elseif group_by?seq_contains("date_month")> ,date_format(date_sub(to_date(<@date_type_switch date_type=filter_flat.date_type!{"filterValues": [""]}/>), interval dayofmonth(to_date(<@date_type_switch date_type=filter_flat.date_type!{"filterValues": [""]}/>)) -1  day), '%Y-%m-%d') as date_month
                              <#else> ,${value}
                              </#if>
                          </#list>
                      </#if>
              FROM 
                      adm_ord_cus_work_platform_prdt_df 
              WHERE   partition_d = (select max(partition_d) from adm_ord_cus_work_platform_prdt_df)
              <#if filter_flat.date??> AND <@date_type_switch date_type=filter_flat.date_type!{"filterValues": [""]}/> between '${filter_flat.date.filterValues[0]}' and '${filter_flat.date.filterValues[1]}' </#if>
              <#if filter_flat.area??> AND grade_region_name IN ('${filter_flat.area.filterValues?join("', '")}')</#if>

              <#if filter_flat.cur_filter_str??> AND ${filter_flat.cur_filter_str.filterValues[0]} </#if>

              <#if group_by??>group by default_group <#list group_by as value> ,${value} </#list></#if>
      
      ) as t
      <#if order_by??>
          order by <#list order_by as item>${item.order_field} ${item.order_type}, </#list> 1
      </#if>
      <#if limit??>
          limit ${limit.limit_start},${limit.limit_size}
      </#if>
      


  - db_name:
    id: 2
    table_name: adm_ord_grp_work_platform_prdt_df
    source_type: starrocks
    indicators:
      - indicator_name: suc_income_rate
        indicator_name_cn: 销售额达成率
        indicator_type: double
        indicator_format: percent
        indicator_key: v.page.workbench.diyIncomeAch.tips
        indicator_key_name: diyIncomeAchieveRate
        indicator_is_dimension: false
        indicator_is_support_sorting: false
      - indicator_name: suc_income_target
        indicator_name_cn: 销售额目标
        indicator_type: double
        indicator_format: double
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: false
        indicator_is_support_sorting: false
      - indicator_name: suc_profit_rate
        indicator_name_cn: 毛利达成率
        indicator_type: double
        indicator_format: percent
        indicator_key: v.page.workbench.diyPorfitAch.tips
        indicator_key_name: diyProfitAchieveRate
        indicator_is_dimension: false
        indicator_is_support_sorting: false
      - indicator_name: suc_profit_target
        indicator_name_cn: 毛利达目标
        indicator_type: double
        indicator_format: double
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: false
        indicator_is_support_sorting: false

      - indicator_name: date
        indicator_name_cn: 日期
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: true
      - indicator_name: date_week
        indicator_name_cn: 日期
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: true
      - indicator_name: date_month
        indicator_name_cn: 日期
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: true


    template: |
      SELECT  
              sum(depat_gmv_target) as suc_income_target,
              sum(depat_profit_target) as suc_profit_target,
              sum(dep_est_bsin_icm_deal_amt) / sum(depat_gmv_target) as suc_income_rate,
              sum(dep_est_deal_rev) / sum(depat_profit_target) as suc_profit_rate 
      FROM    adm_ord_cus_work_platform_prdt_target_achieve_df  
      WHERE   partition_d = (select max(partition_d) from adm_ord_cus_work_platform_prdt_target_achieve_df)
      AND     tou_date = substr('${filter_flat.date.filterValues[1]}', 1, 7)
      <#if filter_flat.area??> AND grade_region_name IN ('${filter_flat.area.filterValues?join("', '")}')</#if>

  - db_name:
    id: 21
    table_name: adm_ord_grp_work_platform_prdt_df
    source_type: starrocks
    indicators:

      - indicator_name: suc_income_rate_rank
        indicator_name_cn: 销售额达成率排名
        indicator_type: long
        indicator_format: long
        indicator_key: v.page.workbench.sucIncomeRateRank.tips
        indicator_key_name: sucIncomeRateRank
        indicator_is_dimension: false
        indicator_is_support_sorting: false

      - indicator_name: suc_profit_rate_rank
        indicator_name_cn: 毛利达成率排名
        indicator_type: long
        indicator_format: long
        indicator_key: v.page.workbench.sucProfitRateRank.tips
        indicator_key_name: sucProfitRateRank
        indicator_is_dimension: false
        indicator_is_support_sorting: false

      - indicator_name: suc_income_rate_rank_percentile
        indicator_name_cn: 销售额达成率排名
        indicator_type: long
        indicator_format: long
        indicator_key: v.page.workbench.sucIncomeRateRankPercentile.tips
        indicator_key_name: sucIncomeRateRankPercentile
        indicator_is_dimension: false
        indicator_is_support_sorting: false

      - indicator_name: suc_profit_rate_rank_percentile
        indicator_name_cn: 毛利达成率排名
        indicator_type: long
        indicator_format: long
        indicator_key: v.page.workbench.sucProfitRateRankPercentile.tips
        indicator_key_name: sucProfitRateRankPercentile
        indicator_is_dimension: false
        indicator_is_support_sorting: false

      - indicator_name: date
        indicator_name_cn: 日期
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: true
      - indicator_name: date_week
        indicator_name_cn: 日期
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: true
      - indicator_name: date_month
        indicator_name_cn: 日期
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: true


    template: |
      select 
              suc_income_rate_rank,
              suc_profit_rate_rank,
              suc_income_rate_rank_max,
              suc_profit_rate_rank_max,
              suc_income_rate_rank / suc_income_rate_rank_max as suc_income_rate_rank_percentile,
              suc_profit_rate_rank / suc_profit_rate_rank_max as suc_profit_rate_rank_percentile
      from  (
                  select 
                          grade_region_name,
                          rank() over(order by suc_income_rate desc) as suc_income_rate_rank,
                          rank() over(order by suc_profit_rate desc) as suc_profit_rate_rank,
                          count() over() as suc_income_rate_rank_max,
                          count() over() as suc_profit_rate_rank_max,
                          suc_income_rate,
                          suc_profit_rate,
                          suc_income_target,
                          suc_profit_target
                  from (
                          SELECT  
                                  1 as default_group,
                                  grade_region_name,
                                  sum(depat_gmv_target) as suc_income_target,
                                  sum(depat_profit_target) as suc_profit_target,
                                  sum(dep_est_bsin_icm_deal_amt) / sum(depat_gmv_target) as suc_income_rate,
                                  sum(dep_est_deal_rev) / sum(depat_profit_target) as suc_profit_rate 
                          FROM    adm_ord_cus_work_platform_prdt_target_achieve_df  
                          WHERE   partition_d = (select max(partition_d) from adm_ord_cus_work_platform_prdt_target_achieve_df)
                          AND     tou_date = substr('${filter_flat.date.filterValues[1]}', 1, 7)
                          group by 
                                  grade_region_name
                  ) as t
      ) as t
      where 1 = 1
      <#if filter_flat.area?? && filter_flat.area.filterValues?size == 1> AND grade_region_name IN ('${filter_flat.area.filterValues?join("', '")}') <#else> and 1 = 0</#if>

  - db_name:
    id: 3
    table_name: edw_log_grp_cpr_platform_flow_cr_di
    source_type: starrocks
    indicators:
      - indicator_name: ava_order_cap
        indicator_name_cn: 可接单量
        indicator_type: double
        indicator_format: long
        indicator_key: v.page.workbench.diyAvaOrderCap.category.tips
        indicator_key_name: avaOrderCap
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: vendor_ord_accept_rate
        indicator_name_cn: CPR供应商接单率
        indicator_type: double
        indicator_format: percent
        indicator_key: v.page.workbench.venderacctRate.tips
        indicator_key_name: vendorOrdAcceptRate
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: completed_orders
        indicator_name_cn: 成交订单
        indicator_type: long
        indicator_format: long
        indicator_key: v.page.workbench.completedOrd.tips
        indicator_key_name: completedOrders
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: completed_orders_rate
        indicator_name_cn: 成单率
        indicator_type: double
        indicator_format: percent
        indicator_key: v.page.workbench.completedOrdersRate.tips
        indicator_key_name: completedOrdersRate
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: cpr_completed_orders_rate
        indicator_name_cn: CPR成单率
        indicator_type: double
        indicator_format: percent
        indicator_key: v.page.workbench.cprCompletedOrdRate.tips
        indicator_key_name: cprCompletedOrdersRate
        indicator_is_dimension: false
        indicator_is_support_sorting: true

      - indicator_name: date
        indicator_name_cn: 日期
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: true
      - indicator_name: date_week
        indicator_name_cn: 日期
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: true
      - indicator_name: date_month
        indicator_name_cn: 日期
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: true

      - indicator_name: grade_region_name
        indicator_name_cn: 评级区域
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: true
      - indicator_name: destinationline
        indicator_name_cn: 目的地区域
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: true
      - indicator_name: domain
        indicator_name_cn: 境内外
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: true
      - indicator_name: business_region_name
        indicator_name_cn: 大区
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: true
      - indicator_name: vbk_provider_name
        indicator_name_cn: 供应商
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: true

    template: |
      <#macro date_type_switch date_type>
          ${date_type.filterValues[0]?switch('PREORDER_DATE', 'order_date', 'TRIP_DATE', 'tour_depart_date', 'RETURN_DATE', 'tour_enddate')}
      </#macro>
      
      select 
              *
      from (
              SELECT  
                      1 as default_group
                      <#if indicators?seq_contains("ava_order_cap")> ,sum(cus_taken_ord_cnt) as ava_order_cap </#if>
                      <#if indicators?seq_contains("vendor_ord_accept_rate")> ,sum(cus_cpr1_taken_ord_cnt) / sum(cus_taken_ord_cnt) as vendor_ord_accept_rate </#if>
                      <#if indicators?seq_contains("completed_orders")> ,sum(cus_taken_deal_ord_cnt) as completed_orders </#if>
                      <#if indicators?seq_contains("completed_orders_rate")> ,sum(cus_taken_ord_cnt) / sum(cus_taken_deal_ord_cnt) as completed_orders_rate </#if>
                      <#if indicators?seq_contains("cpr_completed_orders_rate")> ,sum(cpr1_taken_deal_ord_cnt) / sum(cus_cpr1_taken_ord_cnt) as cpr_completed_orders_rate </#if>
      
                      <#if group_by??>
                          <#list group_by as value>
                              <#if group_by?seq_contains("date")> ,date_format(to_date(<@date_type_switch date_type=filter_flat.date_type!{"filterValues": [""]}/>), '%Y-%m-%d') as date
                              <#elseif group_by?seq_contains("date_week")> ,date_format(date_sub(to_date(<@date_type_switch date_type=filter_flat.date_type!{"filterValues": [""]}/>), interval (dayofweek(to_date(<@date_type_switch date_type=filter_flat.date_type!{"filterValues": [""]}/>)) + 5) % 7  day), '%Y-%m-%d') as date_week
                              <#elseif group_by?seq_contains("date_month")> ,date_format(date_sub(to_date(<@date_type_switch date_type=filter_flat.date_type!{"filterValues": [""]}/>), interval dayofmonth(to_date(<@date_type_switch date_type=filter_flat.date_type!{"filterValues": [""]}/>)) -1  day), '%Y-%m-%d') as date_month
                              <#else> ,${value}
                              </#if>
                          </#list>
                      </#if>
              FROM adm_ord_cus_work_platform_prdt_prvdr_df 
              WHERE partition_d = (select max(partition_d) from adm_ord_cus_work_platform_prdt_prvdr_df)
              <#if filter_flat.date??> AND <@date_type_switch date_type=filter_flat.date_type!{"filterValues": [""]}/> between '${filter_flat.date.filterValues[0]}' and '${filter_flat.date.filterValues[1]}' </#if>
              <#if filter_flat.area??> AND grade_region_name IN ('${filter_flat.area.filterValues?join("', '")}')</#if>
              <#if filter_flat.cur_filter_str??> AND ${filter_flat.cur_filter_str.filterValues[0]} </#if>
      
              <#if group_by??>group by default_group <#list group_by as value> ,${value} </#list></#if>
      ) as t
      <#if order_by??>
          order by <#list order_by as item>${item.order_field} ${item.order_type}, </#list> 1
      </#if>
      <#if limit??>
          limit ${limit.limit_start},${limit.limit_size}
      </#if>


  - db_name:
    id: 4
    table_name: adm_sev_cus_metrics_tag_to_workbench_df
    source_type: starrocks
    indicators:
      - indicator_name: fitting_nps
        indicator_name_cn: 当月拟合NPS
        indicator_type: double
        indicator_format: double
        indicator_key: v.page.workbench.diyFittingNps.tips
        indicator_key_name: diyFittingNps
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: recommend_rate
        indicator_name_cn: 推荐率
        indicator_type: double
        indicator_format: percent
        indicator_key: v.page.workbench.diyRecommend.tips
        indicator_key_name: diyRecommendationRate
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: slander_weight_rate
        indicator_name_cn: 诋毁率
        indicator_type: double
        indicator_format: percent
        indicator_key: v.page.workbench.diySladerRate.tips
        indicator_key_name: diySlanderRate
        indicator_is_dimension: false
        indicator_is_support_sorting: false

      - indicator_name: send_good_guide_ord_cnt_rate
        indicator_name_cn: 金牌导游派遣率
        indicator_type: double
        indicator_format: percent
        indicator_key: v.page.workbench.grpGuiderDisp.tips
        indicator_key_name: grpGuiderDispatchRate
        indicator_is_dimension: false
        indicator_is_support_sorting: true


      - indicator_name: send_good_driver_real_ord_cnt_rate
        indicator_name_cn: 金牌司机真实派遣率
        indicator_type: double
        indicator_format: percent
        indicator_key: v.page.workbench.diyDriverActDisp.tips
        indicator_key_name: diyDriverActualDispacthRate
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: good_driver_sev_ord_sop_cnt_rate
        indicator_name_cn: 金牌司机SOP执行率
        indicator_type: double
        indicator_format: percent
        indicator_key: v.page.workbench.diyDriverExe.tips
        indicator_key_name: diyDriverExecutionRate
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: send_good_driver_ord_cnt_rate
        indicator_name_cn: 金牌司机派遣率
        indicator_type: double
        indicator_format: percent
        indicator_key: v.page.workbench.diyDriverDisp.tips
        indicator_key_name: diyDriverDispatchRate
        indicator_is_dimension: false
        indicator_is_support_sorting: true


      - indicator_name: date
        indicator_name_cn: 日期
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: true
      - indicator_name: date_week
        indicator_name_cn: 日期
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: true
      - indicator_name: date_month
        indicator_name_cn: 日期
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: true
  
      - indicator_name: grade_region_name
        indicator_name_cn: 评级区域
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: true
      - indicator_name: destinationline
        indicator_name_cn: 目的地区域
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: true
      - indicator_name: domain
        indicator_name_cn: 境内外
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: true
      - indicator_name: business_region_name
        indicator_name_cn: 大区
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: true
      - indicator_name: vbk_provider_name
        indicator_name_cn: 供应商
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: true

    template: |
      <#macro date_type_switch date_type>
          ${date_type.filterValues[0]?switch('PREORDER_DATE', 'order_date', 'TRIP_DATE', 'tour_depart_date', 'RETURN_DATE', 'tour_enddate')}
      </#macro>
      
      select 
              *
      from (
              SELECT  
                  1 as default_group
                  <#if indicators?seq_contains("fitting_nps")> ,sum(nps_supplier_recommend_v5 - nps_supplier_detract_v5) / sum(nps_denominator_v5) as fitting_nps </#if>
                  <#if indicators?seq_contains("recommend_rate")> ,sum(nps_supplier_recommend_v5) / sum(nps_denominator_v5) as recommend_rate </#if>
                  <#if indicators?seq_contains("slander_weight_rate")> ,sum(nps_supplier_detract_v5) / sum(nps_denominator_v5) as slander_weight_rate </#if>
      
                  <#if indicators?seq_contains("send_good_driver_real_ord_cnt_rate")> ,sum(real_gold_driver_disp) / sum(driver_platform_ord) as send_good_driver_real_ord_cnt_rate </#if>
                  <#if indicators?seq_contains("send_good_driver_ord_cnt_rate")> ,sum(gold_driver_disp) /  sum(driver_platform_disp) as send_good_driver_ord_cnt_rate </#if>
                  <#if indicators?seq_contains("good_driver_sev_ord_sop_cnt_rate")> ,sum(gold_driver_sop_pass) /  sum(gold_driver_plat_ord) as good_driver_sev_ord_sop_cnt_rate </#if>
      
                  <#if indicators?seq_contains("send_good_guide_ord_cnt_rate")> ,sum(gold_guide_disp) / sum(guide_disp) as send_good_guide_ord_cnt_rate </#if>
      
                  <#if group_by??>
                      <#list group_by as value>
                          <#if group_by?seq_contains("date")> ,date_format(to_date(<@date_type_switch date_type=filter_flat.date_type!{"filterValues": [""]}/>), '%Y-%m-%d') as date
                          <#elseif group_by?seq_contains("date_week")> ,date_format(date_sub(to_date(<@date_type_switch date_type=filter_flat.date_type!{"filterValues": [""]}/>), interval (dayofweek(to_date(<@date_type_switch date_type=filter_flat.date_type!{"filterValues": [""]}/>)) + 5) % 7  day), '%Y-%m-%d') as date_week
                          <#elseif group_by?seq_contains("date_month")> ,date_format(date_sub(to_date(<@date_type_switch date_type=filter_flat.date_type!{"filterValues": [""]}/>), interval dayofmonth(to_date(<@date_type_switch date_type=filter_flat.date_type!{"filterValues": [""]}/>)) -1  day), '%Y-%m-%d') as date_month
                          <#else> ,${value}
                          </#if>
                      </#list>
                  </#if>
              FROM 
                    adm_sev_cus_metrics_tag_to_workbench_df
              WHERE partition_d = (select max(partition_d) from adm_sev_cus_metrics_tag_to_workbench_df)
              <#if filter_flat.date??> AND <@date_type_switch date_type=filter_flat.date_type!{"filterValues": [""]}/> between '${filter_flat.date.filterValues[0]}' and '${filter_flat.date.filterValues[1]}' </#if>
              <#if filter_flat.area??> AND grade_region_name IN ('${filter_flat.area.filterValues?join("', '")}')</#if>
              <#if filter_flat.cur_filter_str??> AND ${filter_flat.cur_filter_str.filterValues[0]} </#if>
      
              <#if group_by??>group by default_group <#list group_by as value> ,${value} </#list></#if>
      ) as t
      <#if order_by??>
          order by <#list order_by as item>${item.order_field} ${item.order_type}, </#list> 1
      </#if>
      <#if limit??>
          limit ${limit.limit_start},${limit.limit_size}
      </#if>
      


      
