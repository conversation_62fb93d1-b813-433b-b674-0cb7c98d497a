package com.ctrip.tour.business.dashboard.tktBusiness.bean;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;



/**
 * 照妖镜 产品信息整改场景的json格式
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ProductInfoRecificationBean {
    Long productId;   // 产品id
    String productName; // 产品名字
    String productUrl;  // 产品url
    String vendorId; // 供应商id
    String vendorName; // 供应商名字
    String problemType;  // 违规原因
    String problemDesc;  // 问题描述
    String rectificationDeadline; // 整改最后完成时间
    String problemUrl;// 申诉链接
}
