package com.ctrip.tour.business.dashboard.tktBusiness.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import java.sql.Types;
import java.sql.Timestamp;

import com.ctrip.platform.dal.dao.DalPojo;

/**
 * <AUTHOR>
 * @date 2022-07-21
 */
@Entity
@Database(name = "TtdReportDB_W")
@Table(name = "business_dashboard_examine_config")
public class BusinessDashboardExamineConfig implements DalPojo {

    /**
     * 主键
     */
    @Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long id;

    /**
     * 邮箱前缀
     */
	@Column(name = "domain_name")
	@Type(value = Types.VARCHAR)
	private String domainName;

    /**
     * 考核指标
     */
	@Column(name = "examine_metric")
	@Type(value = Types.VARCHAR)
	private String examineMetric;

    /**
     * 考核层级(枚举值 大区 省份 景点 三方 国内)
     */
	@Column(name = "examine_level")
	@Type(value = Types.VARCHAR)
	private String examineLevel;

    /**
     * 对应考核BD(当考核层级是景点时关联)
     */
	@Column(name = "examine_bd")
	@Type(value = Types.VARCHAR)
	private String examineBd;

    /**
     * 对应考核省或者大区(当考核层级是大区或者省份时关联)
     */
	@Column(name = "examine_region_range")
	@Type(value = Types.VARCHAR)
	private String examineRegionRange;

    /**
     * 是否需要环球影城的GMV/毛利 当考核层级是省份 时需要实际考虑
     */
	@Column(name = "need_universal_studios")
	@Type(value = Types.VARCHAR)
	private String needUniversalStudios;

    /**
     * 最后更新时间
     */
	@Column(name = "DataChange_LastTime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getDomainName() {
		return domainName;
	}

	public void setDomainName(String domainName) {
		this.domainName = domainName;
	}

	public String getExamineMetric() {
		return examineMetric;
	}

	public void setExamineMetric(String examineMetric) {
		this.examineMetric = examineMetric;
	}

	public String getExamineLevel() {
		return examineLevel;
	}

	public void setExamineLevel(String examineLevel) {
		this.examineLevel = examineLevel;
	}

	public String getExamineBd() {
		return examineBd;
	}

	public void setExamineBd(String examineBd) {
		this.examineBd = examineBd;
	}

	public String getExamineRegionRange() {
		return examineRegionRange;
	}

	public void setExamineRegionRange(String examineRegionRange) {
		this.examineRegionRange = examineRegionRange;
	}

	public String getNeedUniversalStudios() {
		return needUniversalStudios;
	}

	public void setNeedUniversalStudios(String needUniversalStudios) {
		this.needUniversalStudios = needUniversalStudios;
	}

	public Timestamp getDatachangeLasttime() {
		return datachangeLasttime;
	}

	public void setDatachangeLasttime(Timestamp datachangeLasttime) {
		this.datachangeLasttime = datachangeLasttime;
	}

}
