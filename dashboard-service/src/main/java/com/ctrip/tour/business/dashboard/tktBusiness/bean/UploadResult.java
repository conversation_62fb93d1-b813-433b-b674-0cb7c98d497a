package com.ctrip.tour.business.dashboard.tktBusiness.bean;

import com.google.gson.annotations.SerializedName;

public class UploadResult {
    @SerializedName(value = "filename", alternate = {"file_name"})
    public String fileName;
    @SerializedName(value = "fileurl", alternate = {"url"})
    public String fileUrl;

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }
}