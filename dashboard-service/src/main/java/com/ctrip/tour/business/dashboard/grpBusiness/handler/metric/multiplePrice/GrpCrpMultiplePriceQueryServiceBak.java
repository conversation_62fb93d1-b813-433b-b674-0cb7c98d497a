//package com.ctrip.tour.business.dashboard.grpBusiness.handler.metric.multiplePrice;
//
//
//import static com.ctrip.tour.business.dashboard.grpBusiness.exception.ExceptionEnum.EMP_CAN_NOT_FIND;
//
//import com.ctrip.soa._24922.GetGrpMetricDataRequestType;
//import com.ctrip.tour.business.dashboard.grpBusiness.annotation.MetricData;
//import com.ctrip.tour.business.dashboard.grpBusiness.common.GrpConstant;
//import com.ctrip.tour.business.dashboard.grpBusiness.handler.metric.GrpBussinessAbstractMetricService;
//import com.ctrip.tour.business.dashboard.grpBusiness.metrics.multiplePrice.GrpCprMultiplePriceService;
//import com.ctrip.tour.business.dashboard.grpBusiness.service.HrOrgEmpInfoService;
//import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
//import com.ctrip.tour.business.dashboard.tktBusiness.exception.ServiceException;
//import com.google.common.base.Joiner;
//import com.google.common.collect.Lists;
//import com.google.common.collect.Maps;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.ApplicationContext;
//import org.springframework.stereotype.Service;
//
//import java.sql.SQLException;
//import java.time.LocalDate;
//import java.time.format.DateTimeFormatter;
//import java.util.List;
//import java.util.Map;
//import java.util.Objects;
//
////@Service(value = GrpConstant.MULTIPLE_PRICE)
////@Slf4j
////@MetricData(value = GrpConstant.MULTIPLE_PRICE, needWeekOverWeek = true)
//public class GrpCrpMultiplePriceQueryServiceBak extends GrpBussinessAbstractMetricService {
//
//    private final static String PRODUCT_ID = "productid";
//    private final static String SUB_BU_TYPE = "sub_bu_type";
//    private final static String PM_EID = "pm_eid";
//    private final static String LOCAL_PM_EID = "local_pmeid";
//    private final static String AVG_MULTIPLE_PRICE = "avg_multiple_price";
//    private final static String CLICK_PV = "click_pv";
//    private final static String VIEW_DATE = "view_date";
//    private final static String PRD_REGION_ID = "prd_region_id";
//    private final static String PRD_PATTERN_ID = "prd_pattern_name";
//    private final static String BIZ_MODE = "biz_mode";
//    private final static String EMP_CODES = "empCodes";
//    private final static String OR = "OR";
//    private final static String ADMIN_EID = "grp.admin.eid";
//    private final static String GRP_GRP_ADMIN_EID = "grp.grp.admin.eid";
//    private final static String GRP_PRV_ADMIN_EID = "grp.prv.admin.eid";
//
//    @Autowired
//    private RemoteConfig remoteConfig;
//    @Autowired
//    private GrpCprMultiplePriceService grpCprMultiplePriceService;
//    @Autowired
//    private HrOrgEmpInfoService hrOrgEmpInfoService;
//
//    public GrpCrpMultiplePriceQueryServiceBak(ApplicationContext ac) {
//        super(ac);
//    }
//
//    @Override
//    protected Map<String, Object> request2Param(GetGrpMetricDataRequestType requestType, int bizMode) {
//        Map<String, Object> param = Maps.newHashMap();
//
//        List<String> empCodes = Lists.newArrayList(requestType.getEmpCode());
//        try {
//
//            String adminEid = remoteConfig.getExternalConfig(ADMIN_EID);
//
//            if (StringUtils.equals(requestType.getEmpCode(), adminEid)) {
//                String grpAdminEid = remoteConfig.getExternalConfig(GRP_GRP_ADMIN_EID);
//                String prvAdminEid = remoteConfig.getExternalConfig(GRP_PRV_ADMIN_EID);
//                if (Objects.equals(bizMode, 0)) {
//                    empCodes.addAll(hrOrgEmpInfoService.getAllSubordinateEmpCode(grpAdminEid));
//                    empCodes.addAll(hrOrgEmpInfoService.getAllSubordinateEmpCode(prvAdminEid));
//                } else if (Objects.equals(bizMode, 1) || Objects.equals(bizMode, 3)) {
//                    empCodes.addAll(hrOrgEmpInfoService.getAllSubordinateEmpCode(grpAdminEid));
//                } else if (Objects.equals(bizMode, 2) || Objects.equals(bizMode, 4)) {
//                    empCodes.addAll(hrOrgEmpInfoService.getAllSubordinateEmpCode(prvAdminEid));
//                }
//            } else {
//                empCodes.addAll(hrOrgEmpInfoService.getAllSubordinateEmpCode(requestType.getEmpCode()));
//            }
//        } catch (SQLException e) {
//            log.warn("query empcode error" + requestType.getEmpCode(), e);
//            throw new ServiceException(EMP_CAN_NOT_FIND.getCode(),EMP_CAN_NOT_FIND.getMsg());
//        }
//
//        Integer businessLine = requestType.getBusinessLine();
//
//        if (Objects.equals(0, businessLine)) {
//            param.put(BIZ_MODE, 1);
//            param.put(EMP_CODES, empCodes);
//        } else if (Objects.equals(1, businessLine)) { //跟团
//            param.put(Joiner.on("-").join(OR, PM_EID, LOCAL_PM_EID), new List[]{empCodes, empCodes});
//        } else if (Objects.equals(2, businessLine)) {//私家团
//            param.put(PM_EID, empCodes);
//        }
//
//
//        param.put(PRD_PATTERN_ID, requestType.getProductPattern());
//        param.put(VIEW_DATE, new String[]{requestType.getStartDate(), requestType.getEndDate()});
//        if (businessLine == 1 || businessLine == 2) {
//            param.put(SUB_BU_TYPE, businessLine == 1?"跟团游":"独立出游");//NOSONAR
//        }
//        return param;
//    }
//
//
//
//    private String[] calcOverDate(String startDate, String endDate, int offset) {
//        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
//        LocalDate hisStartDate = LocalDate.parse(startDate, dtf).minusDays(offset);
//        LocalDate hisEndDate = LocalDate.parse(endDate, dtf).minusDays(offset);
//        return new String[]{dtf.format(hisStartDate), dtf.format(hisEndDate)};
//    }
//
//    @Override
//    protected Map<String, Object> replaceQueryDate4Week(Map<String, Object> param , String startDate, String endDate) {
//        param.put(VIEW_DATE, calcOverDate(startDate, endDate, 7));
//        return param;
//    }
//
//    @Override
//    protected Map<String, Object> replaceQueryDate4Month(Map<String, Object> param, String startDate, String endDate) {
//        param.put(VIEW_DATE, calcOverDate(startDate, endDate, 30));
//        return param;
//    }
//
//    @Override
//    protected Map<String, Object> replaceQueryDate4Year(Map<String, Object> param, String startDate, String endDate) {
//        param.put(VIEW_DATE, calcOverYearDate(startDate, endDate));
//        return param;
//    }
//
//
//    @Override
//    public Map<String, Object> queryMetricTrendLine(Map<String, Object> param, GetGrpMetricDataRequestType requestType) {
//
//        String aggregationGranularity = requestType.getAggregationGranularity();
//
//        Map<String, Object> result = grpCprMultiplePriceService.queryTrendLineData(grpCprMultiplePriceService, param,
//                Lists.newArrayList(VIEW_DATE), aggregationGranularity);
//
//        return result;
//    }
//
//    @Override
//    public List<Map<String, Object>> queryMetricDillDown(Map<String, Object> param, GetGrpMetricDataRequestType requestType) {
//
//        String drillDownDim = requestType.getDrillDownDim();
//
//        List<Map<String, Object>> result = grpCprMultiplePriceService.queryDillDownData(grpCprMultiplePriceService, param,
//                Lists.newArrayList(drillDownDim));
//
//        return result;
//    }
//
//    @Override
//    public List<Map<String, Object>> queryMetricCardData(Map<String, Object> param, GetGrpMetricDataRequestType requestType) {
//        List<Map<String, Object>> result = grpCprMultiplePriceService.queryCardData(grpCprMultiplePriceService, param,
//                null);
//        return result;
//    }
//
//
//    private List<String> queryAllTargetEmpCodes(String empCode) {
//        return null;
//    }
//
//}
