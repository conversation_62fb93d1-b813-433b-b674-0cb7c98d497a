package com.ctrip.tour.business.dashboard.tktBusiness.dao;

import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.StatementParameters;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.ctrip.tour.business.dashboard.utils.SqlUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/8/3
 *
 * 对于二期的新表  取数逻辑参考以下的conf
 * http://conf.ctripcorp.com/pages/viewpage.action?pageId=1468536116
 *
 */
@Repository
public class Bus567Dao {

    @Autowired
    private GeneralDao generalDao;


    private String getBusMetricSql(String metric){
        String sql = " sum(cw_viewspot_num) as ci_viewspot_num,\n" +
                " sum(cw_viewspot_rate) as ci_viewspot_ratio,\n" +
                " sum(fw_viewspot_num) as fi_viewspot_num,\n" +
                " sum(fw_viewspot_rate) as fi_viewspot_ratio,\n" +
                " sum(w_viewspot_num) as mi_viewspot_num,\n" +
                " sum(w_viewspot_rate) as mi_viewspot_ratio ";
        if("6".equals(metric)){
            sql = " sum(cw_saleunit_num) as ci_viewspot_num,\n" +
                    " sum(cw_saleunit_rate) as ci_viewspot_ratio,\n" +
                    " sum(fw_saleunit_num) as fi_viewspot_num,\n" +
                    " sum(fw_saleunit_rate) as fi_viewspot_ratio,\n" +
                    " sum(w_saleunit_num) as mi_viewspot_num,\n" +
                    " sum(w_saleunit_rate) as mi_viewspot_ratio ";
        }else if("7".equals(metric)){
            sql = " sum(cw_comm_num) as ci_viewspot_num,\n" +
                    " sum(cw_comm_rate) as ci_viewspot_ratio,\n" +
                    " sum(fw_comm_num) as fi_viewspot_num,\n" +
                    " sum(fw_comm_rate) as fi_viewspot_ratio,\n" +
                    " sum(w_comm_num) as mi_viewspot_num,\n" +
                    " sum(w_comm_rate) as mi_viewspot_ratio ";
        }
        return sql;
    }


    public List<List<Object>> getDataWithOutDrillDown(Map<String, List<String>> inMap,
                                                      String metric) throws SQLException {

        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        sb.append(getBusMetricSql(metric));
        sb.append(" from bus_5_6_7_cpd_dashboard_weakness_statistics_t ");
        StatementParameters parameters = new StatementParameters();
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        return generalDao.getListResult(sb.toString(), parameters);
    }


    public List<List<Object>> getDataWithDrillDown(Map<String, List<String>> inMap,
                                                   List<String> groupTagList,
                                                   String metric) throws SQLException {

        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        SqlUtil.jointGroupCondition(sb, false, groupTagList);
        sb.append(",");
        sb.append(getBusMetricSql(metric));
        sb.append(" from bus_5_6_7_cpd_dashboard_weakness_statistics_t ");
        StatementParameters parameters = new StatementParameters();
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        SqlUtil.jointGroupCondition(sb, true, groupTagList);
        return generalDao.getListResult(sb.toString(), parameters);
    }


    public List<List<Object>> getOverallData(String originLevel,
                                             Map<String, List<String>> inMap,
                                             List<String> groupTagList) throws SQLException {
        if ("景点".equals(originLevel)) {
            return getViewSpotLevelOverallData(inMap, groupTagList);
        } else {
            return getOtherLevelOverallData(inMap, groupTagList);
        }
    }


    public List<List<Object>> getDillDownData(String field,
                                              Map<String, List<String>> inMap,
                                              List<String> groupTagList) throws SQLException {
        if ("examinee".equals(field)) {
            return getViewSpotLevelDrillDownData(inMap, groupTagList);
        } else {
            return getOtherLevelOverallData(inMap, groupTagList);
        }
    }




    private List<List<Object>> getViewSpotLevelOverallData(Map<String, List<String>> inMap,
                                                           List<String> groupTagList) throws SQLException {
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        if (!GeneralUtil.isEmpty(groupTagList)) {
            SqlUtil.jointGroupCondition(sb, false, groupTagList);
            sb.append(",");
        }
        sb.append("sum(examine_cw_num) as ci_viewspot_num,\n" +
                "  COALESCE(sum(examine_cw_num)/sum(examine_c_num),0) as ci_viewspot_ratio,\n" +
                "  sum(examine_fw_num) as fi_viewspot_num,\n" +
                "  COALESCE(sum(examine_fw_num)/sum(examine_f_num),0) as fi_viewspot_ratio,\n" +
                "  sum(examine_mw_num) as mi_viewspot_num,\n" +
                "  COALESCE(sum(examine_mw_num)/sum(examine_m_num),0) as mi_viewspot_ratio,");
        sb.append(" the_date");
        sb.append(" from bus_5_6_7_viewspot_weakness_statistics_t ");
        StatementParameters parameters = new StatementParameters();
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        sb.append(" group by the_date");
        if (!GeneralUtil.isEmpty(groupTagList)) {
            sb.append(",");
            SqlUtil.jointGroupCondition(sb, false, groupTagList);
        }
        String finalSql = generateFinalSql(sb, groupTagList);
        return generalDao.getListResult(finalSql, parameters);
    }

    private List<List<Object>> getViewSpotLevelDrillDownData(Map<String, List<String>> inMap,
                                                             List<String> groupTagList) throws SQLException {
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        if (!GeneralUtil.isEmpty(groupTagList)) {
            SqlUtil.jointGroupCondition(sb, false, groupTagList);
            sb.append(",");
        }
        sb.append("sum(bd_cw_num) as ci_viewspot_num,\n" +
                "  COALESCE(sum(bd_cw_num)/sum(bd_c_num),0) as ci_viewspot_ratio,\n" +
                "  sum(bd_fw_num) as fi_viewspot_num,\n" +
                "  COALESCE(sum(bd_fw_num)/sum(bd_f_num),0) as fi_viewspot_ratio,\n" +
                "  sum(bd_mw_num) as mi_viewspot_num,\n" +
                "  COALESCE(sum(bd_mw_num)/sum(bd_m_num),0) as mi_viewspot_ratio,");
        sb.append(" the_date");
        sb.append(" from bus_5_6_7_viewspot_weakness_statistics_t ");
        StatementParameters parameters = new StatementParameters();
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        sb.append(" group by the_date");
        if (!GeneralUtil.isEmpty(groupTagList)) {
            sb.append(",");
            SqlUtil.jointGroupCondition(sb, false, groupTagList);
        }
        String finalSql = generateFinalSql(sb, groupTagList);
        return generalDao.getListResult(finalSql, parameters);
    }

    private List<List<Object>> getOtherLevelOverallData(Map<String, List<String>> inMap,
                                                        List<String> groupTagList) throws SQLException {
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        if (!GeneralUtil.isEmpty(groupTagList)) {
            SqlUtil.jointGroupCondition(sb, false, groupTagList);
            sb.append(",");
        }
        sb.append("sum(cw_num) as ci_viewspot_num,\n" +
                "  COALESCE(sum(cw_num)/sum(c_num),0) as ci_viewspot_ratio,\n" +
                "  sum(fw_num) as fi_viewspot_num,\n" +
                "  COALESCE(sum(fw_num)/sum(f_num),0) as fi_viewspot_ratio,\n" +
                "  sum(mw_num) as mi_viewspot_num,\n" +
                "  COALESCE(sum(mw_num)/sum(m_num),0) as mi_viewspot_ratio, ");
        sb.append(" the_date");
        sb.append(" from bus_5_6_7_region_province_weakness_statistics_t ");
        StatementParameters parameters = new StatementParameters();
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        sb.append(" group by the_date");
        if (!GeneralUtil.isEmpty(groupTagList)) {
            sb.append(",");
            SqlUtil.jointGroupCondition(sb, false, groupTagList);
        }
        String finalSql = generateFinalSql(sb, groupTagList);
        return generalDao.getListResult(finalSql, parameters);
    }


    private String generateFinalSql(StringBuilder sb,
                                    List<String> groupTagList) {

        StringBuilder finalSb = new StringBuilder();
        finalSb.append("select ");
        if(!GeneralUtil.isEmpty(groupTagList)){
            SqlUtil.jointGroupCondition(finalSb,false,groupTagList);
            finalSb.append(",");
        }
        finalSb.append("sum(ci_viewspot_num) as ci_viewsport_num,\n" +
                "       sum(ci_viewspot_ratio) as ci_viewspot_ratio,\n" +
                "       sum(fi_viewspot_num) as fi_viewspot_num,\n" +
                "       sum(fi_viewspot_ratio) as fi_viewspot_ratio,\n" +
                "       sum(mi_viewspot_num) as mi_viewspot_num,\n" +
                "       sum(mi_viewspot_ratio) as mi_viewspot_ratio ");
        finalSb.append(" from( ");
        finalSb.append(sb);
        finalSb.append(") aa");
        if(!GeneralUtil.isEmpty(groupTagList)){
            SqlUtil.jointGroupCondition(finalSb,true,groupTagList);
        }
        return finalSb.toString();
    }


    public List<List<Object>> getFieldListNew(Map<String, List<String>> inMap,
                                              Map<String, List<String>> notInMap,
                                              Map<String, String> likeMap,
                                              List<String> groupTagList,
                                              Integer pageNo,
                                              Integer pageSize) throws SQLException {
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        SqlUtil.jointGroupCondition(sb, false, groupTagList);
        sb.append(" from bus_5_6_7_cpd_dashboard_weakness_statistics_t ");
        StatementParameters parameters = new StatementParameters();
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        SqlUtil.jointNotInCondition(sb, parameters, notInMap);
        if (!GeneralUtil.isEmpty(likeMap)) {
            SqlUtil.jointLikeCondition(sb, parameters, likeMap, true);
        }
        SqlUtil.jointGroupCondition(sb, true, groupTagList);
        SqlUtil.jointOrderCondition(sb, groupTagList, "asc");
        if (GeneralUtil.isNotEmpty(pageNo) && GeneralUtil.isNotEmpty(pageSize)) {
            SqlUtil.jointPagingCondition(sb, parameters, pageNo, pageSize);
        }
        return generalDao.getListResult(sb.toString(), parameters);
    }


    public List<List<Object>> getFieldList(String field,
                                           Map<String, List<String>> inMap,
                                           Map<String, String> likeMap,
                                           List<String> tagList,
                                           Integer pageNo,
                                           Integer pageSize) throws SQLException {
        String tableName = "bus_5_6_7_region_province_weakness_statistics_t";
        if ("商拓".equals(field)) {
            tableName = "bus_5_6_7_viewspot_weakness_statistics_t";
        }
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        SqlUtil.jointGroupCondition(sb, false, tagList);
        sb.append(" from ").append(tableName).append(" ");
        StatementParameters parameters = new StatementParameters();
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        if (!GeneralUtil.isEmpty(likeMap)) {
            SqlUtil.jointLikeCondition(sb, parameters, likeMap, true);
        }
        SqlUtil.jointGroupCondition(sb, true, tagList);
        SqlUtil.jointOrderCondition(sb, tagList, "asc");
        if (pageNo != null && pageSize != null) {
            SqlUtil.jointPagingCondition(sb, parameters, pageNo, pageSize);
        }
        return generalDao.getListResult(sb.toString(), parameters);
    }

    public void getRankAsync(Map<String, List<String>> inMap,
                             DalHints dalHints) throws SQLException {
        StringBuilder sb = new StringBuilder();
        sb.append("select ranking from bus_5_6_7_bd_rank_t_new ");
        StatementParameters parameters = new StatementParameters();
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        generalDao.getListResultAsync(sb.toString(), parameters, dalHints);
    }


    public Integer getGapDays(String metric,
                              String year,
                              String dateType,
                              String queryD,
                              List<String> timeList) throws SQLException {
        StringBuilder sb = new StringBuilder();
        sb.append("select sum(total_time) from bus_competitor_metric_date_dim where ");
        sb.append("metric=? and year = ? and date_type = ? and query_d = ? ");
        if ("quarter".equals(dateType)) {
            sb.append(" and quarter in (?)");
        } else {
            sb.append(" and month in (?)");
        }
        StatementParameters parameters = new StatementParameters();
        parameters.set(1, metric);
        parameters.set(2, year);
        parameters.set(3, dateType);
        parameters.set(4, queryD);
        parameters.setInParameter(5, timeList);
        List<List<Object>> resultList = generalDao.getListResult(sb.toString(), parameters);
        //判空处理  这种情况可能发生在季或者月的前几天是节假日的时候  此时会取不到数据
        //例如元旦和10.1
        if(GeneralUtil.isEmpty(resultList.get(0)) || GeneralUtil.isEmpty(resultList.get(0).get(0))){
            return null;
        }
        return Integer.valueOf(resultList.get(0).get(0).toString());
    }

    public Integer getGapDaysV2(String metric,
                              String year,
                              String dateType,
                              String queryD,
                              List<String> timeList) throws SQLException {
        if (!"month".equals(dateType)) {
            dateType = "quarter";
        }
        StringBuilder sb = new StringBuilder();
        sb.append("select sum(total_time) from bus_competitor_metric_date_dim where ");
        sb.append("metric=? and year = ? and date_type = ? and query_d = ? ");
        if ("month".equals(dateType)) {
            sb.append(" and month in (?)");
        } else {
            sb.append(" and quarter in (?)");
        }
        StatementParameters parameters = new StatementParameters();
        parameters.set(1, metric);
        parameters.set(2, year);
        parameters.set(3, dateType);
        parameters.set(4, queryD);
        parameters.setInParameter(5, timeList);
        List<List<Object>> resultList = generalDao.getListResult(sb.toString(), parameters);
        //判空处理  这种情况可能发生在季或者月的前几天是节假日的时候  此时会取不到数据
        //例如元旦和10.1
        if(GeneralUtil.isEmpty(resultList.get(0)) || GeneralUtil.isEmpty(resultList.get(0).get(0))){
            return null;
        }
        return Integer.valueOf(resultList.get(0).get(0).toString());
    }
}
