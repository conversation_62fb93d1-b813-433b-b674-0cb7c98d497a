package com.ctrip.tour.business.dashboard.grpBusiness.enums;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/3/19
 */
public enum DomesticBusinessTypeEnum {
    ALL(0, "全部"),//NOSONAR
    TICKET(1, "门票"),//NOSONAR
    PLAY(2, "玩乐"),//NOSONAR
    ACTIVITY(3, "活动"),//NOSONAR
    DAY_TOUR(4, "日游") //NOSONAR
    ;

    int id;
    String code;

    public int getId() {
        return id;
    }

    public String getCode() {
        return code;
    }

    DomesticBusinessTypeEnum(int id, String code) {
        this.id = id;
        this.code = code;
    }

    // 静态映射表用于快速查找
    private static final Map<Integer, DomesticBusinessTypeEnum> idToEnum = new HashMap<>();
    private static final Map<String, DomesticBusinessTypeEnum> codeToEnum = new HashMap<>();

    static {
        // 初始化时遍历所有枚举值，填充映射表
        for (DomesticBusinessTypeEnum metric : values()) {
            idToEnum.put(metric.id, metric);
            codeToEnum.put(metric.code, metric);
        }
    }
    // 根据id获取code
    public static String getCodeById(int id) {
        DomesticBusinessTypeEnum metric = idToEnum.get(id);
        return metric != null ? metric.code : null;
    }

    // 根据code获取id
    public static Integer getIdByCode(String code) {
        DomesticBusinessTypeEnum metric = codeToEnum.get(code);
        return metric != null ? metric.id : null;
    }
}
