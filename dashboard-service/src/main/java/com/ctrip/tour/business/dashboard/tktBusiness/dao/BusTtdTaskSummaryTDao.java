package com.ctrip.tour.business.dashboard.tktBusiness.dao;



import com.ctrip.platform.dal.dao.*;

import com.ctrip.platform.dal.common.enums.DatabaseCategory;
import com.ctrip.platform.dal.dao.base.DalTableOperations;
import com.ctrip.platform.dal.dao.base.SQLResultSpec;
import com.ctrip.platform.dal.dao.client.DalOperationsFactory;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusTtdTaskSummaryT;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-12-06
 * @API DOC: http://pages.release.ctripcorp.com/framework/dal-client-trip/#/3/3.2/3.2
 */
@Repository
public class BusTtdTaskSummaryTDao {
    private final DalTableOperations<BusTtdTaskSummaryT> DAL_TABLE_OPERATIONS = DalOperationsFactory.getDalTableOperations(BusTtdTaskSummaryT.class);

    /**
     * Get a DalClient to access the database. It maybe your customDalClient or a dal internal Client
     *
     * @return DalClient
     */
    public DalClient getClient() {
        return DAL_TABLE_OPERATIONS.getClient();
    }

    /**
     * Get the category of the Database.
     *
     * @return DatabaseCategory
     */
    public DatabaseCategory getDatabaseCategory() {
        return DAL_TABLE_OPERATIONS.getDatabaseCategory();
    }

    /**
     * Execute query by the given sql with args. The result will be the list of instance of
     * the given class which you set in getDalTableOperations() method.
     *
     * @param sql:     The sql can be a standard Query sql statement or a where condition.
     *                 If it is a where condition, please don't contains `where`, dal will set it.
     *                 E.g. the sql can like "select * from xxxtable where id = ?" or just "id = ?".
     * @param dalHints Additional parameters that instruct how DAL Client perform database operation.
     *                 You can set freeSql in dalHints to tell dal the param `sql` is a standard sql.
     * @param args     Arguments used to bind with the sql conditions.
     * @return List of instance of T that represent the query result. If there is no result found, it will return a empty list.
     * T is the class which you set in getDalTableOperations() method.
     * @throws SQLException When things going wrong during the execution
     */
	public List<BusTtdTaskSummaryT> query(String sql, DalHints dalHints, Object... args) throws SQLException {
		return DAL_TABLE_OPERATIONS.query(sql, dalHints, args);
	}

	/**
	 * Execute query by the given sql with args. If you specified the type or mapper in SQLResultSpec,
	 * the result will be the list of instance of the given class specified in SQLResultSpec,
	 * else the result will be the list of instance of the given class which you set in getDalTableOperations() method.
	 *
	 * @param sql:     The sql can be a standard Query sql statement or a where condition.
	 *                 If it is a where condition, please don't contains `where`, dal will set it.
	 *                 E.g. the sql can like "select * from xxxtable where id = ?" or just "id = ?".
	 * @param dalHints Additional parameters that instruct how DAL Client perform database operation.
	 *                 You can set freeSql in dalHints to tell dal the param `sql` is a standard sql.
	 * @param result   Specify the requirements for the returned result(e.g. Result class type, DalRowMapper).
	 * @param args     Arguments used to bind with the sql conditions.
	 * @return List of instance of K that represent the query result. If there is no result found, it will return a empty list.
	 * K is the class which you set in SQLResultSpec or getDalTableOperations() method.
	 * @throws SQLException When things going wrong during the execution
	*/
	public <K> List<K> query(String sql, DalHints dalHints, SQLResultSpec result, Object... args) throws SQLException {
		return DAL_TABLE_OPERATIONS.query(sql, dalHints, result, args);
	}

	/**
	 * Execute query by the given sql with args. It is expected that there is only one result should be found.
	 * The result will be an instance of the given class which you set in getDalTableOperations() method.
	 *
	 * @param sql:     The sql can be a standard Query sql statement or a where condition.
	 *                 If it is a where condition, please don't contains `where`, dal will set it.
	 *                 E.g. the sql can like "select * from xxxtable where id = ?" or just "id = ?".
	 * @param dalHints Additional parameters that instruct how DAL Client perform database operation.
	 *                 You can set freeSql in dalHints to tell dal the param `sql` is a standard sql.
	 * @param args     Arguments used to bind with the sql conditions.
	 * @return Entity that represent the query result. If there is no result found, it will return null.
	 * T is the class which you set in getDalTableOperations() method.
	 * @throws SQLException When things going wrong during the execution, or if there are more than one result found.
	*/
	public BusTtdTaskSummaryT queryObject(String sql, DalHints dalHints, Object... args) throws SQLException {
	    return DAL_TABLE_OPERATIONS.queryObject(sql, dalHints, args);
	}

	/**
	 * Execute query by the given sql with args. It is expected that there is only one result should be found.
	 * If you specified the type or mapper in SQLResultSpec, the result will be an instance of the given class specified in SQLResultSpec,
	 * else the result will be an instance of the given class which you set in getDalTableOperations() method.
	 *
	 * @param sql:     The sql can be a standard Query sql statement or a where condition.
	 *                 If it is a where condition, please don't contains `where`, dal will set it.
	 *                 E.g. the sql can like "select * from xxxtable where id = ?" or just "id = ?".
	 * @param dalHints Additional parameters that instruct how DAL Client perform database operation.
	 *                 You can set freeSql in dalHints to tell dal the param `sql` is a standard sql.
	 * @param args     Arguments used to bind with the sql conditions.
	 * @param result   Specify the requirements for the returned result(e.g. Result class type, DalRowMapper).
	 * @return Entity that represent the query result. If there is no result found, it will return null.
	 * @throws SQLException When things going wrong during the execution, or if there are more than one result found.
	*/
	public <K> K queryObject(String sql, DalHints dalHints, SQLResultSpec result, Object... args) throws SQLException {
		return DAL_TABLE_OPERATIONS.queryObject(sql, dalHints, result, args);
	}

    /**
     * Query by Primary key. The key column type should be Integer, Long, etc. For table that the primary key is not of
     * Integer type, this method will fail.
     *
     * @param id    The primary key in number format
     * @param hints Additional parameters that instruct how DAL Client perform database operation.
     * @return entity of this table. Null if no result found.
     * @throws SQLException When things going wrong during the execution
     */
    public BusTtdTaskSummaryT queryByPk(Number id, DalHints hints) throws SQLException {
        return DAL_TABLE_OPERATIONS.queryByPk(id, hints);
    }

    /**
     * Query by Primary key, the key columns are pass in the pojo.
     *
     * @param pk    The pojo used to represent primary key(s)
     * @param hints Additional parameters that instruct how DAL Client perform database operation.
     * @return entity of this table. Null if no result found.
     * @throws SQLException When things going wrong during the execution
     */
    public BusTtdTaskSummaryT queryByPk(BusTtdTaskSummaryT pk, DalHints hints) throws SQLException {
        return DAL_TABLE_OPERATIONS.queryByPk(pk, hints);
    }

    /**
     * Query against sample pojo. All not null attributes of the passed in pojo will be used as search criteria. If all
     * attributes in pojo are null,an exception will be thrown, you can set ignoreAllNullFields in DalHints to avoid this.
     *
     * @param sample The pojo used for sampling
     * @param hints  Additional parameters that instruct how DAL Client perform database operation.
     * @return List of pojos that have the same attributes like in the sample
     * @throws SQLException When things going wrong during the execution
     */
    public List<BusTtdTaskSummaryT> queryBy(BusTtdTaskSummaryT sample, DalHints hints) throws SQLException {
        return DAL_TABLE_OPERATIONS.queryBy(sample, hints);
    }

    /**
     * Query against sample pojo. All not null attributes of the passed in pojo will be used as search criteria. If all
     * attributes in pojo are null,an exception will be thrown, you can set ignoreAllNullFields in DalHints to avoid this.
     *
     * @param sample The pojo used for sampling
     * @param hints  Additional parameters that instruct how DAL Client perform database operation.
     * @param result Specify the requirements for the returned result(e.g. ShardExecutionCallback, sortBy).
     * @return List of pojos that have the same attributes like in the sample
     * @throws SQLException When things going wrong during the execution
     */
    public <K> List<K> queryBy(BusTtdTaskSummaryT sample, DalHints hints, SQLResultSpec result) throws SQLException {
        return DAL_TABLE_OPERATIONS.queryBy(sample, hints, result);
    }

    /**
     * Query the first row of the given sql.
     *
     * @param sql    The sql can be a standard Query sql statement or a where condition.
     *               If it is a where condition, please don't contains `where`, dal will set it.
     *               E.g. For MySQL, the sql can like "select * from xxxtable where id = ? limit 1" or just "id = ?".
     * @param hints  Additional parameters that instruct how DAL Client perform database operation.
     *               You can set freeSql in dalHints to tell dal the param `sql` is a standard sql.
     * @param result Specify the requirements for the returned result(e.g. ShardExecutionCallback, sortBy).
     *               For this method, page is not support, it will throw exception when you set it.
     * @param args   Arguments used to bind with the sql conditions.
     * @return Entity that represent the query result. If there is no result found, it will return null.
     * K is the class which you set in SQLResultSpec or getDalTableOperations() method.
     * @throws SQLException When things going wrong during the execution
     */
    public <K> K queryFirst(String sql, DalHints hints, SQLResultSpec result, Object... args) throws SQLException {
        return DAL_TABLE_OPERATIONS.queryFirst(sql, hints, result, args);
    }

    /**
     * Query range of result for the given sql and args.
     *
     * @param sql      The sql can be a standard Query sql statement or a where condition.
     *                 If it is a where condition, please don't contains `where`, dal will set it.
     *                 E.g. For MySQL, the sql can like "select * from xxxtable where id = ? limit 1, 10" or just "id = ?".
     * @param dalHints Additional parameters that instruct how DAL Client perform database operation.
     *                 You can set freeSql in dalHints to tell dal the param `sql` is a standard sql.
     * @param start    the start number. It is zero(0) based, means the index is from 0. 1 will be the 2nd row.
     * @param count    how may rows to return
     * @param result   Specify the requirements for the returned result(e.g. ShardExecutionCallback, sortBy).
     *                 For this method, page is not support, it will throw exception when you set it.
     * @param args     Arguments used to bind with the sql conditions.
     * @return Entity that represent the query result. If there is no result found, it will return null.
     * K is the class which you set in SQLResultSpec or getDalTableOperations() method.
     * @throws SQLException When things going wrong during the execution
     */
    public <K> List<K> queryFrom(String sql, DalHints dalHints, int start, int count, SQLResultSpec result, Object... args) throws SQLException {
        return DAL_TABLE_OPERATIONS.queryFrom(sql, dalHints, start, count, result, args);
    }

    /**
     * Execute query by the given sql with args. Return the number of records matching the query condition.
     *
     * @param sql:     The sql can be a standard Query sql statement or a where condition.
     *                 If it is a where condition, please don't contains `where`, dal will set it.
     *                 E.g. the sql can like "select count(*) from xxxtable where id = ?" or just "id = ?".
     * @param dalHints Additional parameters that instruct how DAL Client perform database operation.
     *                 You can set freeSql in dalHints to tell dal the param `sql` is a standard sql.
     * @param args     Arguments used to bind with the sql conditions.
     * @return The number of records matching the query condition.
     * @throws SQLException When things going wrong during the execution
     */
    public Number count(String sql, DalHints dalHints, Object... args) throws SQLException {
        return DAL_TABLE_OPERATIONS.count(sql, dalHints, args);
    }

    /**
     * Execute query by the given sql with args. Return the number of records matching the query condition.
     * Action: please don't set type or mapper in SQLResult when use this method, because which you set is not work
     * and it may cause exception.
     *
     * @param sql:     The sql can be a standard Query sql statement or a where condition.
     *                 If it is a where condition, please don't contains `where`, dal will set it.
     *                 E.g. the sql can like "select count(*) from xxxtable where id = ?" or just "id = ?".
     * @param dalHints Additional parameters that instruct how DAL Client perform database operation.
     *                 You can set freeSql in dalHints to tell dal the param `sql` is a standard sql.
     * @param result   Specify the requirements for the returned result(e.g. ShardExecutionCallback).
     * @param args     Arguments used to bind with the sql conditions
     * @return The number of records matching the query condition.
     * @throws SQLException When things going wrong during the execution
     */
    public Number count(String sql, DalHints dalHints, SQLResultSpec result, Object... args) throws SQLException {
        return DAL_TABLE_OPERATIONS.count(sql, dalHints, result, args);
    }

    /**
     * Query records count against sample pojo. All not null attributes of the passed in pojo will be used as search criteria. If all
     * attributes in pojo are null, an exception will be thrown, you can set ignoreAllNullFields in DalHints to avoid this.
     *
     * @param sample The pojo used for sampling
     * @param hints Additional parameters that instruct how DAL Client perform database operation.
     * @param result Specify the requirements for the returned result(e.g. ShardExecutionCallback).
     * @return The number of records matching the query condition.
     * @throws SQLException When things going wrong during the execution
     */
    public Number countBy(BusTtdTaskSummaryT sample, DalHints hints, SQLResultSpec result) throws SQLException {
        return DAL_TABLE_OPERATIONS.countBy(sample, hints, result);
    }

    /**
     * Insert pojo and get the generated PK back in keyHolder. If the "set no count on" for MS SqlServer is
     * set(currently set in Ctrip), the operation may fail. Please don't pass keyholder for MS SqlServer to avoid the
     * failure.
     *
     * @param hints   Additional parameters that instruct how DAL Client perform database operation.
     * @param daoPojo pojo to be inserted
     * @return Affected rows for MySQL. SQL Server doesn't guarantee to return the actual affected rows,
     * it may return a negative value when executed successfully but cannot fetch the actual affected rows.
     * @throws SQLException When things going wrong during the execution
     */
    public int insert(DalHints hints, BusTtdTaskSummaryT daoPojo) throws SQLException {
        return DAL_TABLE_OPERATIONS.insert(hints, daoPojo);
    }

    /**
     * Insert pojo and get the generated PK back in keyHolder. If the "set no count on" for MS SqlServer is
     * set(currently set in Ctrip), the operation may fail. Please don't pass keyholder for MS SqlServer to avoid the
     * failure.
     *
     * @param hints   Additional parameters that instruct how DAL Client perform database operation.
     * @param daoPojo pojo to be inserted
     * @param result  Specify the requirements for the returned result(e.g. KeyHolder).
     * @return Affected rows for MySQL. SQL Server doesn't guarantee to return the actual affected rows,
     * it may return a negative value when executed successfully but cannot fetch the actual affected rows.
     * @throws SQLException When things going wrong during the execution
     */
    public int insert(DalHints hints, BusTtdTaskSummaryT daoPojo, SQLResultSpec result) throws SQLException {
        return DAL_TABLE_OPERATIONS.insert(hints, daoPojo, result);
    }

    /**
     * Insert pojos one by one. If you want to inert them in the batch mode, user batchInsert instead. You can also use
     * the combinedInsert.
     *
     * @param hints    Additional parameters that instruct how DAL Client perform database operation.
     *                 DalHintEnum.continueOnError can be used to indicate that the inserting can be go on if there is any
     *                 failure.
     * @param daoPojos list of pojos to be inserted
     * @return Affected rows for MySQL. SQL Server doesn't guarantee to return the actual affected rows,
     * it may return a negative value when executed successfully but cannot fetch the actual affected rows.
     * @throws SQLException When things going wrong during the execution
     */
    public int[] insert(DalHints hints, List<BusTtdTaskSummaryT> daoPojos) throws SQLException {
        return DAL_TABLE_OPERATIONS.insert(hints, daoPojos);
    }

    /**
     * Insert pojos one by one. If you want to inert them in the batch mode, user batchInsert instead. You can also use
     * the combinedInsert.
     *
     * @param hints    Additional parameters that instruct how DAL Client perform database operation.
     *                 DalHintEnum.continueOnError can be used to indicate that the inserting can be go on if there is any
     *                 failure.
     * @param daoPojos list of pojos to be inserted
     * @param result   Specify the requirements for the returned result(e.g. KeyHolder, PojoExecutionCallback).
     * @return Affected rows for MySQL. SQL Server doesn't guarantee to return the actual affected rows,
     * it may return a negative value when executed successfully but cannot fetch the actual affected rows.
     * @throws SQLException When things going wrong during the execution
     */
    public int[] insert(DalHints hints, List<BusTtdTaskSummaryT> daoPojos, SQLResultSpec result) throws SQLException {
        return DAL_TABLE_OPERATIONS.insert(hints, daoPojos, result);
    }

    /**
     * Insert multiple pojos in one INSERT SQL and get the generated PK back in keyHolder. If the "set no count on" for
     * MS SqlServer is set(currently set in Ctrip), the operation may fail. Please don't pass keyholder for MS SqlServer
     * to avoid the failure. The DalDetailResults will be set in hints to allow client know how the operation performed
     * in each of the shard.
     *
     * @param hints    Additional parameters that instruct how DAL Client perform database operation.
     * @param daoPojos list of pojos to be inserted
     * @return Affected rows for MySQL. SQL Server doesn't guarantee to return the actual affected rows,
     * it may return a negative value when executed successfully but cannot fetch the actual affected rows.
     * @throws SQLException When things going wrong during the execution
     */
    public int combinedInsert(DalHints hints, List<BusTtdTaskSummaryT> daoPojos) throws SQLException {
        return DAL_TABLE_OPERATIONS.combinedInsert(hints, daoPojos);
    }

    /**
     * Insert multiple pojos in one INSERT SQL and get the generated PK back in keyHolder. If the "set no count on" for
     * MS SqlServer is set(currently set in Ctrip), the operation may fail. Please don't pass keyholder for MS SqlServer
     * to avoid the failure. The DalDetailResults will be set in hints to allow client know how the operation performed
     * in each of the shard.
     *
     * @param hints    Additional parameters that instruct how DAL Client perform database operation.
     * @param daoPojos list of pojos to be inserted
     * @param result   Specify the requirements for the returned result(e.g. KeyHolder, ShardExecutionCallback).
     * @return Affected rows for MySQL. SQL Server doesn't guarantee to return the actual affected rows,
     * it may return a negative value when executed successfully but cannot fetch the actual affected rows.
     * @throws SQLException When things going wrong during the execution
     */
    public int combinedInsert(DalHints hints, List<BusTtdTaskSummaryT> daoPojos, SQLResultSpec result) throws SQLException {
        return DAL_TABLE_OPERATIONS.combinedInsert(hints, daoPojos, result);
    }

    /**
     * Insert pojos in batch mode. The DalDetailResults will be set in hints to allow client know how the operation
     * performed in each of the shard.
     *
     * @param hints    Additional parameters that instruct how DAL Client perform database operation.
     * @param daoPojos list of pojos to be inserted
     * @return Affected rows for MySQL. SQL Server doesn't guarantee to return the actual affected rows,
     * it may return a negative value when executed successfully but cannot fetch the actual affected rows.
     * @throws SQLException When things going wrong during the execution
     */
    public int[] batchInsert(DalHints hints, List<BusTtdTaskSummaryT> daoPojos) throws SQLException {
        return DAL_TABLE_OPERATIONS.batchInsert(hints, daoPojos);
    }

    /**
     * Insert pojos in batch mode. The DalDetailResults will be set in hints to allow client know how the operation
     * performed in each of the shard.
     *
     * @param hints    Additional parameters that instruct how DAL Client perform database operation.
     * @param daoPojos list of pojos to be inserted
     * @param result   Specify the requirements for the returned result(e.g. ShardExecutionCallback).
     * @return Affected rows for MySQL. SQL Server doesn't guarantee to return the actual affected rows,
     * it may return a negative value when executed successfully but cannot fetch the actual affected rows.
     * @throws SQLException When things going wrong during the execution
     */
    public int[] batchInsert(DalHints hints, List<BusTtdTaskSummaryT> daoPojos, SQLResultSpec result) throws SQLException {
        return DAL_TABLE_OPERATIONS.batchInsert(hints, daoPojos, result);
    }

    /**
     * Execute delete by the given sql with args.
     *
     * @param sql:     The sql can be a standard delete sql statement or a where condition.
     *                 If it is a where condition, please don't contains `where`, dal will set it.
     *                 E.g. the sql can like "delete from xxxtable where id = ?" or just "id = ?".
     * @param dalHints Additional parameters that instruct how DAL Client perform database operation.
     *                 You can set freeSql in dalHints to tell dal the param `sql` is a standard sql.
     * @param args     Arguments used to bind with the sql conditions.
     * @return Affected rows for MySQL. SQL Server doesn't guarantee to return the actual affected rows,
     * it may return a negative value when executed successfully but cannot fetch the actual affected rows.
     * @throws SQLException When things going wrong during the execution
     */
    public int delete(String sql, DalHints dalHints, Object... args) throws SQLException {
        return DAL_TABLE_OPERATIONS.delete(sql, dalHints, args);
    }

    /**
     * Execute delete by the given sql with args.
     *
     * @param sql:     The sql can be a standard delete sql statement or a where condition.
     *                 If it is a where condition, please don't contains `where`, dal will set it.
     *                 E.g. the sql can like "delete from xxxtable where id = ?" or just "id = ?".
     * @param dalHints Additional parameters that instruct how DAL Client perform database operation.
     *                 You can set freeSql in dalHints to tell dal the param `sql` is a standard sql.
     * @param result   Specify the requirements for the returned result(e.g. ShardExecutionCallback).
     * @param args     Arguments used to bind with the sql conditions.
     * @return Affected rows for MySQL. SQL Server doesn't guarantee to return the actual affected rows,
     * it may return a negative value when executed successfully but cannot fetch the actual affected rows.
     * @throws SQLException When things going wrong during the execution
     */
    public int delete(String sql, DalHints dalHints, SQLResultSpec result, Object... args) throws SQLException {
        return DAL_TABLE_OPERATIONS.delete(sql, dalHints, result, args);
    }

    /**
     * Delete the given pojo by primary key.
     *
     * @param hints   Additional parameters that instruct how DAL Client perform database operation.
     * @param daoPojo pojo to be deleted, it need contains primary key value.
     * @return Affected rows for MySQL. SQL Server doesn't guarantee to return the actual affected rows,
     * it may return a negative value when executed successfully but cannot fetch the actual affected rows.
     * @throws SQLException When things going wrong during the execution
     */
    public int delete(DalHints hints, BusTtdTaskSummaryT daoPojo) throws SQLException {
        return DAL_TABLE_OPERATIONS.delete(hints, daoPojo);
    }

    /**
     * Delete the given pojos list one by one by primary key.
     *
     * @param hints    Additional parameters that instruct how DAL Client perform database operation.
     * @param daoPojos list of pojos to be deleted, every pojo need contains primary key value.
     * @return Affected rows for MySQL. SQL Server doesn't guarantee to return the actual affected rows,
     * it may return a negative value when executed successfully but cannot fetch the actual affected rows.
     * @throws SQLException When things going wrong during the execution
     */
    public int[] delete(DalHints hints, List<BusTtdTaskSummaryT> daoPojos) throws SQLException {
        return DAL_TABLE_OPERATIONS.delete(hints, daoPojos);
    }

    /**
     * Delete the given pojos list one by one by primary key.
     *
     * @param hints    Additional parameters that instruct how DAL Client perform database operation.
     * @param daoPojos list of pojos to be deleted, every pojo need contains primary key value.
     * @param result   Specify the requirements for the returned result(e.g. PojoExecutionCallback).
     * @return Affected rows for MySQL. SQL Server doesn't guarantee to return the actual affected rows,
     * it may return a negative value when executed successfully but cannot fetch the actual affected rows.
     * @throws SQLException When things going wrong during the execution
     */
    public int[] delete(DalHints hints, List<BusTtdTaskSummaryT> daoPojos, SQLResultSpec result) throws SQLException {
        return DAL_TABLE_OPERATIONS.delete(hints, daoPojos, result);
    }

    /**
     * Delete multiple pojos in one DELETE SQL by primary keys.
     *
     * @param hints    Additional parameters that instruct how DAL Client perform database operation.
     * @param daoPojos list of pojos to be deleted, every pojo need contains primary key value.
     * @return Affected rows for MySQL. SQL Server doesn't guarantee to return the actual affected rows,
     * it may return a negative value when executed successfully but cannot fetch the actual affected rows.
     * @throws SQLException When things going wrong during the execution
     */
    public int combinedDelete(DalHints hints, List<BusTtdTaskSummaryT> daoPojos) throws SQLException {
        return DAL_TABLE_OPERATIONS.combinedDelete(hints, daoPojos);
    }

    /**
     * Delete the given pojo list in batch. The DalDetailResults will be set in hints to allow client know how the
     * operation performed in each of the shard.
     *
     * @param hints    Additional parameters that instruct how DAL Client perform database operation.
     * @param daoPojos list of pojos to be deleted, every pojo need contains primary key value.
     * @return Affected rows for MySQL. SQL Server doesn't guarantee to return the actual affected rows,
     * it may return a negative value when executed successfully but cannot fetch the actual affected rows.
     * @throws SQLException When things going wrong during the execution
     */
    public int[] batchDelete(DalHints hints, List<BusTtdTaskSummaryT> daoPojos) throws SQLException {
        return DAL_TABLE_OPERATIONS.batchDelete(hints, daoPojos);
    }

    /**
     * Delete the given pojo list in batch. The DalDetailResults will be set in hints to allow client know how the
     * operation performed in each of the shard.
     *
     * @param hints    Additional parameters that instruct how DAL Client perform database operation.
     * @param daoPojos list of pojos to be deleted, every pojo need contains primary key value.
     * @param result   Specify the requirements for the returned result(e.g. ShardExecutionCallback).
     * @return Affected rows for MySQL. SQL Server doesn't guarantee to return the actual affected rows,
     * it may return a negative value when executed successfully but cannot fetch the actual affected rows.
     * @throws SQLException When things going wrong during the execution
     */
    public int[] batchDelete(DalHints hints, List<BusTtdTaskSummaryT> daoPojos, SQLResultSpec result) throws SQLException {
        return DAL_TABLE_OPERATIONS.batchDelete(hints, daoPojos, result);
    }

    /**
     * Update with free SQL. This API can be used for Insert/Update operations.
     * The sql must be the standard update statement. E.g. "UPDATE ABC SET ....".
     * Because it is the raw sql, table shard will not be supported if the table name is logic one, you can
     * provide real table name in sql if you want to update certain physical table.
     *
     * @param sql      The sql statement to be executed.
     * @param dalHints Additional parameters that instruct how DAL Client perform database operation.
     * @param args     Arguments used to bind with the SQL Statement.
     * @return Affected rows for MySQL. SQL Server doesn't guarantee to return the actual affected rows,
     * it may return a negative value when executed successfully but cannot fetch the actual affected rows.
     * @throws SQLException When things going wrong during the execution
     */
    public int update(String sql, DalHints dalHints, Object... args) throws SQLException {
        return DAL_TABLE_OPERATIONS.update(sql, dalHints, args);
    }

    /**
     * Update with free SQL. This API can be used for Insert/Update operations.
     * The sql must be the standard Insert/Update/Delete statement. E.g. "UPDATE ABC SET ....".
     * Because it is the raw sql, table shard will not be supported if the table name is logic one, you can
     * provide real table name in sql if you want to update certain physical table.
     *
     * @param sql      The sql statement to be executed.
     * @param dalHints Additional parameters that instruct how DAL Client perform database operation.
     * @param result   Specify the requirements for the returned result(e.g. ShardExecutionCallback).
     * @param args     Arguments used to bind with the SQL Statement.
     * @return Affected rows for MySQL. SQL Server doesn't guarantee to return the actual affected rows,
     * it may return a negative value when executed successfully but cannot fetch the actual affected rows.
     * @throws SQLException When things going wrong during the execution
     */
    public int update(String sql, DalHints dalHints, SQLResultSpec result, Object... args) throws SQLException {
        return DAL_TABLE_OPERATIONS.update(sql, dalHints, result, args);
    }

    /**
     * Update the given pojo . By default, if a field of pojo is null value, that field will be ignored, so that it will
     * not be updated. You can overwrite this by set updateNullField in hints.
     *
     * @param hints   Additional parameters that instruct how DAL Client perform database operation.
     *                DalHintEnum.updateNullField can be used to indicate that the field of pojo is null value will be update.
     * @param daoPojo pojo to be updated, pojo need contains primary key value.
     * @return Affected rows for MySQL. SQL Server doesn't guarantee to return the actual affected rows,
     * it may return a negative value when executed successfully but cannot fetch the actual affected rows.
     * @throws SQLException When things going wrong during the execution
     */
    public int update(DalHints hints, BusTtdTaskSummaryT daoPojo) throws SQLException {
        return DAL_TABLE_OPERATIONS.update(hints, daoPojo);
    }

    /**
     * Update the given pojo list one by one. By default, if a field of pojo is null value, that field will be ignored,
     * so that it will not be updated. You can overwrite this by set updateNullField in hints.
     *
     * @param hints    Additional parameters that instruct how DAL Client perform database operation.
     *                 DalHintEnum.updateNullField can be used to indicate that the field of pojo is null value will be update.
     * @param daoPojos list of pojos to be deleted, every pojo need contains primary key value.
     * @return Affected rows for MySQL. SQL Server doesn't guarantee to return the actual affected rows,
     * it may return a negative value when executed successfully but cannot fetch the actual affected rows.
     * @throws SQLException When things going wrong during the execution
     */
    public int[] update(DalHints hints, List<BusTtdTaskSummaryT> daoPojos) throws SQLException {
        return DAL_TABLE_OPERATIONS.update(hints, daoPojos);
    }

    /**
     * Update the given pojo list one by one. By default, if a field of pojo is null value, that field will be ignored,
     * so that it will not be updated. You can overwrite this by set updateNullField in hints.
     *
     * @param hints    Additional parameters that instruct how DAL Client perform database operation.
     *                 DalHintEnum.updateNullField can be used to indicate that the field of pojo is null value will be update.
     * @param daoPojos list of pojos to be deleted, every pojo need contains primary key value.
     * @param result   Specify the requirements for the returned result(e.g. PojoExecutionCallback).
     * @return Affected rows for MySQL. SQL Server doesn't guarantee to return the actual affected rows,
     * it may return a negative value when executed successfully but cannot fetch the actual affected rows.
     * @throws SQLException When things going wrong during the execution
     */
    public int[] update(DalHints hints, List<BusTtdTaskSummaryT> daoPojos, SQLResultSpec result) throws SQLException {
        return DAL_TABLE_OPERATIONS.update(hints, daoPojos, result);
    }

    /**
     * Update the given pojo list in batch. The DalDetailResults will be set in hints to allow client know how the
     * operation performed in each of the shard.
     *
     * @param hints    Additional parameters that instruct how DAL Client perform database operation.
     * @param daoPojos list of pojos to be updated, every pojo need contains primary key value.
     * @return Affected rows for MySQL. SQL Server doesn't guarantee to return the actual affected rows,
     * it may return a negative value when executed successfully but cannot fetch the actual affected rows.
     * @throws SQLException When things going wrong during the execution
     */
    public int[] batchUpdate(DalHints hints, List<BusTtdTaskSummaryT> daoPojos) throws SQLException {
        return DAL_TABLE_OPERATIONS.batchUpdate(hints, daoPojos);
    }

    /**
     * Update the given pojo list in batch. The DalDetailResults will be set in hints to allow client know how the
     * operation performed in each of the shard.
     *
     * @param hints    Additional parameters that instruct how DAL Client perform database operation.
     * @param daoPojos list of pojos to be updated, every pojo need contains primary key value.
     * @param result   Specify the requirements for the returned result(e.g. ShardExecutionCallback).
     * @return Affected rows for MySQL. SQL Server doesn't guarantee to return the actual affected rows,
     * it may return a negative value when executed successfully but cannot fetch the actual affected rows.
     * @throws SQLException When things going wrong during the execution
     */
    public int[] batchUpdate(DalHints hints, List<BusTtdTaskSummaryT> daoPojos, SQLResultSpec result) throws SQLException {
        return DAL_TABLE_OPERATIONS.batchUpdate(hints, daoPojos, result);
    }

    /**
     * Replace pojo.
     * Insert default, when the row to be inserted cause a duplicate value in a UNIQUE index or PRIMARY KEY,
     * it will delete the old row and insert a new row.
     * If your table has a auto_increment primary key and a unique key, it's suggested to bypass with insert into ... on
     * duplicate key by free sql or use insertDuplicateUpdate api. Because the auto_increment value in slave with row-based replication may not be
     * updated if you replace with a duplicate unique key but without specific the primary key column value
     *
     * @param hints   Additional parameters that instruct how DAL Client perform database operation
     * @param daoPojo pojo to be repalced
     * @return Affected rows for MySQL. SQL Server doesn't guarantee to return the actual affected rows,
     * it may return a negative value when executed successfully but cannot fetch the actual affected rows.
     * @throws SQLException When things going wrong during the execution
     */
    public int replace(DalHints hints, BusTtdTaskSummaryT daoPojo) throws SQLException {
        return DAL_TABLE_OPERATIONS.replace(hints, daoPojo);
    }

    /**
     * Replace pojo.
     * Insert default, when the row to be inserted cause a duplicate value in a UNIQUE index or PRIMARY KEY,
     * it will delete the old row and insert a new row.
     * If your table has a auto_increment primary key and a unique key, it's suggested to bypass with insert into ... on
     * duplicate key by free sql or use insertDuplicateUpdate api. Because the auto_increment value in slave with row-based replication may not be
     * updated if you replace with a duplicate unique key but without specific the primary key column value
     *
     * @param hints   Additional parameters that instruct how DAL Client perform database operation
     * @param daoPojo pojo to be repalced
     * @param result  Specify the requirements for the returned result(e.g. KeyHolder).
     * @return Affected rows for MySQL. SQL Server doesn't guarantee to return the actual affected rows,
     * it may return a negative value when executed successfully but cannot fetch the actual affected rows.
     * @throws SQLException When things going wrong during the execution
     */
    public int replace(DalHints hints, BusTtdTaskSummaryT daoPojo, SQLResultSpec result) throws SQLException {
        return DAL_TABLE_OPERATIONS.replace(hints, daoPojo, result);
    }

    /**
     * Replace multiple pojos one by one.
     * Insert default, when the row to be inserted cause a duplicate value in a UNIQUE index or PRIMARY KEY,
     * it will delete the old row and insert a new row.
     * If your table has a auto_increment primary key and a unique key, it's suggested to bypass with insert into ... on
     * duplicate key by free sql. Because the auto_increment value in slave with row-based replication may not be
     * updated if you replace with a duplicate unique key but without specific the primary key column value
     *
     * @param hints    Additional parameters that instruct how DAL Client perform database operation.
     *                 DalHintEnum.continueOnError can be used to indicate that the inserting can be go on if there is any
     *                 failure.
     * @param daoPojos list of pojos to be replaced
     * @return Affected rows for MySQL. SQL Server doesn't guarantee to return the actual affected rows,
     * it may return a negative value when executed successfully but cannot fetch the actual affected rows.
     * @throws SQLException When things going wrong during the execution
     */
    public int[] replace(DalHints hints, List<BusTtdTaskSummaryT> daoPojos) throws SQLException {
        return DAL_TABLE_OPERATIONS.replace(hints, daoPojos);
    }

    /**
     * Replace multiple pojos one by one.
     * Insert default, when the row to be inserted cause a duplicate value in a UNIQUE index or PRIMARY KEY,
     * it will delete the old row and insert a new row.
     * If your table has a auto_increment primary key and a unique key, it's suggested to bypass with insert into ... on
     * duplicate key by free sql. Because the auto_increment value in slave with row-based replication may not be
     * updated if you replace with a duplicate unique key but without specific the primary key column value
     *
     * @param hints    Additional parameters that instruct how DAL Client perform database operation.
     *                 DalHintEnum.continueOnError can be used to indicate that the inserting can be go on if there is any
     *                 failure.
     * @param daoPojos list of pojos to be replaced
     * @param result   Specify the requirements for the returned result(e.g. KeyHolder, PojoExecutionCallback).
     * @return Affected rows for MySQL. SQL Server doesn't guarantee to return the actual affected rows,
     * it may return a negative value when executed successfully but cannot fetch the actual affected rows.
     * @throws SQLException When things going wrong during the execution
     */
    public int[] replace(DalHints hints, List<BusTtdTaskSummaryT> daoPojos, SQLResultSpec result) throws SQLException {
        return DAL_TABLE_OPERATIONS.replace(hints, daoPojos, result);
    }

    /**
     * Replace multiple pojos in one replace sql.
     * Insert default, when the row to be inserted cause a duplicate value in a UNIQUE index or PRIMARY KEY,
     * it will delete the old row and insert a new row.
     * If your table has a auto_increment primary key and a unique key, it's suggested to bypass with insert into ... on
     * duplicate key by free sql or use insertDuplicateUpdate api. Because the auto_increment value in slave with row-based replication may not be
     * updated if you replace with a duplicate unique key but without specific the primary key column value
     *
     * @param hints    Additional parameters that instruct how DAL Client perform database operation
     * @param daoPojos list of pojos to be replaced
     * @return Affected rows for MySQL. SQL Server doesn't guarantee to return the actual affected rows,
     * it may return a negative value when executed successfully but cannot fetch the actual affected rows.
     * @throws SQLException When things going wrong during the execution
     */
    public int combinedReplace(DalHints hints, List<BusTtdTaskSummaryT> daoPojos) throws SQLException {
        return DAL_TABLE_OPERATIONS.combinedReplace(hints, daoPojos);
    }

    /**
     * Replace multiple pojos in one replace sql.
     * Insert default, when the row to be inserted cause a duplicate value in a UNIQUE index or PRIMARY KEY,
     * it will delete the old row and insert a new row.
     * If your table has a auto_increment primary key and a unique key, it's suggested to bypass with insert into ... on
     * duplicate key by free sql or use insertDuplicateUpdate api. Because the auto_increment value in slave with row-based replication may not be
     * updated if you replace with a duplicate unique key but without specific the primary key column value
     *
     * @param hints    Additional parameters that instruct how DAL Client perform database operation
     * @param daoPojos list of pojos to be replaced
     * @param result   Specify the requirements for the returned result(e.g. KeyHolder, ShardExecutionCallback).
     * @return Affected rows for MySQL. SQL Server doesn't guarantee to return the actual affected rows,
     * it may return a negative value when executed successfully but cannot fetch the actual affected rows.
     * @throws SQLException When things going wrong during the execution
     */
    public int combinedReplace(DalHints hints, List<BusTtdTaskSummaryT> daoPojos, SQLResultSpec result) throws SQLException {
        return DAL_TABLE_OPERATIONS.combinedReplace(hints, daoPojos, result);
    }

    /**
     * Replace multiple pojos in batch.
     * Insert default, when the row to be inserted cause a duplicate value in a UNIQUE index or PRIMARY KEY,
     * it will delete the old row and insert a new row.
     * If your table has a auto_increment primary key and a unique key, it's suggested to bypass with insert into ... on
     * duplicate key by free sql or use insertDuplicateUpdate api. Because the auto_increment value in slave with row-based replication may not be
     * updated if you replace with a duplicate unique key but without specific the primary key column value
     *
     * @param hints    Additional parameters that instruct how DAL Client perform database operation
     * @param daoPojos list of pojos to be replaced
     * @return Affected rows for MySQL. SQL Server doesn't guarantee to return the actual affected rows,
     * it may return a negative value when executed successfully but cannot fetch the actual affected rows.
     * @throws SQLException When things going wrong during the execution
     */
    public int[] batchReplace(DalHints hints, List<BusTtdTaskSummaryT> daoPojos) throws SQLException {
        return DAL_TABLE_OPERATIONS.batchReplace(hints, daoPojos);
    }

    /**
     * Replace multiple pojos in batch.
     * Insert default, when the row to be inserted cause a duplicate value in a UNIQUE index or PRIMARY KEY,
     * it will delete the old row and insert a new row.
     * If your table has a auto_increment primary key and a unique key, it's suggested to bypass with insert into ... on
     * duplicate key by free sql or use insertDuplicateUpdate api. Because the auto_increment value in slave with row-based replication may not be
     * updated if you replace with a duplicate unique key but without specific the primary key column value
     *
     * @param hints    Additional parameters that instruct how DAL Client perform database operation
     * @param daoPojos list of pojos to be replaced
     * @param result   Specify the requirements for the returned result(e.g. ShardExecutionCallback).
     * @return Affected rows for MySQL. SQL Server doesn't guarantee to return the actual affected rows,
     * it may return a negative value when executed successfully but cannot fetch the actual affected rows.
     * @throws SQLException When things going wrong during the execution
     */
    public int[] batchReplace(DalHints hints, List<BusTtdTaskSummaryT> daoPojos, SQLResultSpec result) throws SQLException {
        return DAL_TABLE_OPERATIONS.batchReplace(hints, daoPojos, result);
    }

    /**
     * Insert or Update pojo.
     * Only used for MySQL, the sql will like INSERT INTO XXXTABLE (`A`, `B`) VALUES (?, ?) ON DUPLICATE KEY UPDATE `A` = VALUES(`A`), `B` = VALUES(`B`);.
     * Insert default, when the row to be inserted cause a duplicate value in a UNIQUE index or PRIMARY KEY,
     * an UPDATE of the old row occurs.
     *
     * @param hints   Additional parameters that instruct how DAL Client perform database operation
     * @param daoPojo pojo to be Insert or Update.
     * @return Affected rows for MySQL. SQL Server doesn't guarantee to return the actual affected rows,
     * it may return a negative value when executed successfully but cannot fetch the actual affected rows.
     * @throws SQLException When things going wrong during the execution
     */
    public int insertDuplicateUpdate(DalHints hints, BusTtdTaskSummaryT daoPojo) throws SQLException {
        return DAL_TABLE_OPERATIONS.insertDuplicateUpdate(hints, daoPojo);
    }

    /**
     * Insert or Update pojo.
     * Only used for MySQL, the sql will like INSERT INTO XXXTABLE (`A`, `B`) VALUES (?, ?) ON DUPLICATE KEY UPDATE `A` = VALUES(`A`), `B` = VALUES(`B`);.
     * Insert default, when the row to be inserted cause a duplicate value in a UNIQUE index or PRIMARY KEY,
     * an UPDATE of the old row occurs.
     *
     * @param hints   Additional parameters that instruct how DAL Client perform database operation
     * @param daoPojo pojo to be Insert or Update.
     * @param result  Specify the requirements for the returned result(e.g. KeyHolder).
     * @return Affected rows for MySQL. SQL Server doesn't guarantee to return the actual affected rows,
     * it may return a negative value when executed successfully but cannot fetch the actual affected rows.
     * @throws SQLException When things going wrong during the execution
     */
    public int insertDuplicateUpdate(DalHints hints, BusTtdTaskSummaryT daoPojo, SQLResultSpec result) throws SQLException {
        return DAL_TABLE_OPERATIONS.insertDuplicateUpdate(hints, daoPojo, result);
    }

    /**
     * Insert or Update multiple pojos one by one.
     * Only used for MySQL, the sql will like INSERT INTO XXXTABLE (`A`, `B`) VALUES (?, ?) ON DUPLICATE KEY UPDATE `A` = VALUES(`A`), `B` = VALUES(`B`);.
     * Insert default, when the row to be inserted cause a duplicate value in a UNIQUE index or PRIMARY KEY,
     * an UPDATE of the old row occurs.
     *
     * @param hints    Additional parameters that instruct how DAL Client perform database operation
     * @param daoPojos list of pojos to be Insert or Update
     * @return Affected rows for MySQL. SQL Server doesn't guarantee to return the actual affected rows,
     * it may return a negative value when executed successfully but cannot fetch the actual affected rows.
     * @throws SQLException When things going wrong during the execution
     */
    public int[] insertDuplicateUpdate(DalHints hints, List<BusTtdTaskSummaryT> daoPojos) throws SQLException {
        return DAL_TABLE_OPERATIONS.insertDuplicateUpdate(hints, daoPojos);
    }

    /**
     * Insert or Update multiple pojos one by one.
     * Only used for MySQL, the sql will like INSERT INTO XXXTABLE (`A`, `B`) VALUES (?, ?) ON DUPLICATE KEY UPDATE `A` = VALUES(`A`), `B` = VALUES(`B`);.
     * Insert default, when the row to be inserted cause a duplicate value in a UNIQUE index or PRIMARY KEY,
     * an UPDATE of the old row occurs.
     *
     * @param hints    Additional parameters that instruct how DAL Client perform database operation
     * @param daoPojos list of pojos to be Insert or Update
     * @param result   Specify the requirements for the returned result(e.g. KeyHolder, PojoExecutionCallback).
     * @return Affected rows for MySQL. SQL Server doesn't guarantee to return the actual affected rows,
     * it may return a negative value when executed successfully but cannot fetch the actual affected rows.
     * @throws SQLException When things going wrong during the execution
     */
    public int[] insertDuplicateUpdate(DalHints hints, List<BusTtdTaskSummaryT> daoPojos, SQLResultSpec result) throws SQLException {
        return DAL_TABLE_OPERATIONS.insertDuplicateUpdate(hints, daoPojos, result);
    }

    /**
     * Insert or Update multiple pojos in batch.
     * Only used for MySQL, the sql will like INSERT INTO XXXTABLE (`A`, `B`) VALUES (?, ?) ON DUPLICATE KEY UPDATE `A` = VALUES(`A`), `B` = VALUES(`B`);.
     * Insert default, when the row to be inserted cause a duplicate value in a UNIQUE index or PRIMARY KEY,
     * an UPDATE of the old row occurs.
     *
     * @param hints    Additional parameters that instruct how DAL Client perform database operation
     * @param daoPojos list of pojos to be Insert or Update
     * @return Affected rows for MySQL. SQL Server doesn't guarantee to return the actual affected rows,
     * it may return a negative value when executed successfully but cannot fetch the actual affected rows.
     * @throws SQLException When things going wrong during the execution
     */
    public int[] batchInsertDuplicateUpdate(DalHints hints, List<BusTtdTaskSummaryT> daoPojos) throws SQLException {
        return DAL_TABLE_OPERATIONS.batchInsertDuplicateUpdate(hints, daoPojos);
    }

    /**
     * Insert or Update multiple pojos in batch.
     * Only used for MySQL, the sql will like INSERT INTO XXXTABLE (`A`, `B`) VALUES (?, ?) ON DUPLICATE KEY UPDATE `A` = VALUES(`A`), `B` = VALUES(`B`);.
     * Insert default, when the row to be inserted cause a duplicate value in a UNIQUE index or PRIMARY KEY,
     * an UPDATE of the old row occurs.
     *
     * @param hints    Additional parameters that instruct how DAL Client perform database operation
     * @param daoPojos list of pojos to be Insert or Update
     * @param result   Specify the requirements for the returned result(e.g. ShardExecutionCallback).
     * @return Affected rows for MySQL. SQL Server doesn't guarantee to return the actual affected rows,
     * it may return a negative value when executed successfully but cannot fetch the actual affected rows.
     * @throws SQLException When things going wrong during the execution
     */
    public int[] batchInsertDuplicateUpdate(DalHints hints, List<BusTtdTaskSummaryT> daoPojos, SQLResultSpec result) throws SQLException {
        return DAL_TABLE_OPERATIONS.batchInsertDuplicateUpdate(hints, daoPojos, result);
    }


    /**
     * 根据code和name的映射关系更新name
     *
     * @param name
     * @param code
     * @param type
     * @param queryD
     * @return
     * @throws SQLException
     */
    public int updateNameByCode(String name,
                                String code,
                                String type,
                                String queryD) throws SQLException {
        String sql;
        switch (type) {
            case "BIZ_CATEGORY":
                sql = "update bus_ttd_task_summary_t set biz_category_name = ? where query_d = ? and  biz_category_code = ?";
                break;
            case "COLLECTION_TYPE":
                sql = "update bus_ttd_task_summary_t set task_collection_name = ? where query_d = ? and task_collection_code = ?";
                break;
            default:
                sql = "update bus_ttd_task_summary_t set task_type_name = ? where query_d = ? and task_type_code = ?";
                break;
        }
        return DAL_TABLE_OPERATIONS.update(sql, new DalHints(), name, queryD, code);
    }
}
