package com.ctrip.tour.business.dashboard.sightArchives.entity.domesticMetricEntity;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;

@Entity
@Getter
@Setter
public class Domestic11And12WeaknessBO {
    //分区
    String d;
    //	考核对象
    String examine;
    //考核指标集合
    String examineMetricList;
    //考核层级
    String examineLevel;
    //考核范围
    String examineRange;
    //携程省份ID
    String provinceName;
    //携程省份
    Long provinceId;
    //携程业务大区ID
    Long businessRegionId;
    //携程业务大区
    String businessRegionName;
    //统计维度ID
    Integer statisticsDimId;
    //劣势线路数量
    Long wLineNum;
    //线路数量
    Double lineNum;
    //劣势率
    Double wLineRate;
    //完成率
    Double wLineCoefficient;

}
