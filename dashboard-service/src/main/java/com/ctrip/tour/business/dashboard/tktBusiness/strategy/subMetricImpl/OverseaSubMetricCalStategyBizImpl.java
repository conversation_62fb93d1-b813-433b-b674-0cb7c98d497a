package com.ctrip.tour.business.dashboard.tktBusiness.strategy.subMetricImpl;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ExamineConfigBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.StrategyException;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.CompetitorSubMetricCalStrategy;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.IncomeSubMetricCalStrategy;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.QuantitySubMetricCalStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;

@Service
@Slf4j
public class OverseaSubMetricCalStategyBizImpl implements ApplicationContextAware {

    private ApplicationContext applicationContext;

    private ConcurrentHashMap<String, IncomeSubMetricCalStrategy> incomeCalStrategyMap = new ConcurrentHashMap<>();

    private ConcurrentHashMap<String, QuantitySubMetricCalStrategy> quantityCalStrategyMap = new ConcurrentHashMap<>();

    private ConcurrentHashMap<String, CompetitorSubMetricCalStrategy> competitorCalStrategyMap = new ConcurrentHashMap<>();


    public Future<MetricDetailInfo> getBus101102SubMetricCardData(TimeFilter timeFilter,
                                                                  OverseaMetricInfoBean metricInfoBean,
                                                                  String d,
                                                                  String metric,
                                                                  String subMetric) throws Exception {

        IncomeSubMetricCalStrategy metricCalStrategy = incomeCalStrategyMap.get(subMetric);
        if (metricCalStrategy == null) {
            throw new StrategyException("strategy is not found!");
        }
        return metricCalStrategy.getBus101102SubMetricCardData(timeFilter, metricInfoBean, d, metric, subMetric);
    }


    public GetOverseaTrendLineDataResponseType getBus101102SubTrendlineData(GetOverseaTrendLineDataRequestType request,
                                                                            String d,
                                                                            List<ExamineConfigBean> examineConfigBeanList) throws Exception {
        String subMetric = request.getSubMetric();
        IncomeSubMetricCalStrategy metricCalStrategy = incomeCalStrategyMap.get(subMetric);
        if (metricCalStrategy == null) {
            throw new StrategyException("strategy is not found!");
        }
        return metricCalStrategy.getBus101102SubTrendLineData(request, d, examineConfigBeanList);
    }


    public GetOverseaDrillDownBaseInfoResponseType getBus101102SubDrillDownBaseInfo(GetOverseaDrillDownBaseInfoRequestType request,
                                                                                    String d,
                                                                                    OverseaMetricInfoBean metricInfoBean) throws Exception {
        String subMetric = request.getSubMetric();
        IncomeSubMetricCalStrategy metricCalStrategy = incomeCalStrategyMap.get(subMetric);
        if (metricCalStrategy == null) {
            throw new StrategyException("strategy is not found!");
        }//根据二级指标查询指定的class
        return metricCalStrategy.getBus101102SubDrillDownBaseInfo(request, d, metricInfoBean);
    }


    public GetOverseaTableDataResponseType getBus101102SubTableData(GetOverseaTableDataRequestType request,
                                                                    String d,
                                                                    OverseaMetricInfoBean metricInfoBean) throws Exception {
        String subMetric = request.getSubMetric();
        IncomeSubMetricCalStrategy metricCalStrategy = incomeCalStrategyMap.get(subMetric);
        if (metricCalStrategy == null) {
            throw new StrategyException("strategy is not found!");
        }
        return metricCalStrategy.getBus101102SubTableData(request, d, metricInfoBean);
    }


    public Future<MetricDetailInfo> getBus103SubMetricCardData(TimeFilter timeFilter,
                                                               OverseaMetricInfoBean metricInfoBean,
                                                               String d,
                                                               String metric,
                                                               String subMetric) throws Exception {

        QuantitySubMetricCalStrategy metricCalStrategy = quantityCalStrategyMap.get(subMetric);
        if (metricCalStrategy == null) {
            throw new StrategyException("strategy is not found!");
        }
        return metricCalStrategy.getBus103SubMetricCardData(timeFilter, metricInfoBean, d, metric, subMetric);
    }


    public GetOverseaTrendLineDataResponseType getBus103SubTrendlineData(GetOverseaTrendLineDataRequestType request,
                                                                         String d,
                                                                         List<ExamineConfigBean> examineConfigBeanList) throws Exception {
        String subMetric = request.getSubMetric();
        QuantitySubMetricCalStrategy metricCalStrategy = quantityCalStrategyMap.get(subMetric);
        if (metricCalStrategy == null) {
            throw new StrategyException("strategy is not found!");
        }
        return metricCalStrategy.getBus103SubTrendLineData(request, d, examineConfigBeanList);
    }


    public GetOverseaDrillDownBaseInfoResponseType getBus103SubDrillDownBaseInfo(GetOverseaDrillDownBaseInfoRequestType request,
                                                                                 String d,
                                                                                 OverseaMetricInfoBean metricInfoBean) throws Exception {
        String subMetric = request.getSubMetric();
        QuantitySubMetricCalStrategy metricCalStrategy = quantityCalStrategyMap.get(subMetric);
        if (metricCalStrategy == null) {
            throw new StrategyException("strategy is not found!");
        }
        return metricCalStrategy.getBus103SubDrillDownBaseInfo(request, d, metricInfoBean);
    }


    public GetOverseaTableDataResponseType getBus103SubTableData(GetOverseaTableDataRequestType request,
                                                                 String d,
                                                                 OverseaMetricInfoBean metricInfoBean) throws Exception {
        String subMetric = request.getSubMetric();
        QuantitySubMetricCalStrategy metricCalStrategy = quantityCalStrategyMap.get(subMetric);
        if (metricCalStrategy == null) {
            throw new StrategyException("strategy is not found!");
        }
        return metricCalStrategy.getBus103SubTableData(request, d, metricInfoBean);
    }


    public Future<MetricDetailInfo> getBus105106107SubMetricCardData(TimeFilter timeFilter,
                                                                     OverseaMetricInfoBean metricInfoBean,
                                                                     String d,
                                                                     String metric,
                                                                     String subMetric) throws Exception {

        CompetitorSubMetricCalStrategy metricCalStrategy = competitorCalStrategyMap.get(subMetric);
        if (metricCalStrategy == null) {
            throw new StrategyException("strategy is not found!");
        }
        return metricCalStrategy.getBus105106107SubMetricCardData(timeFilter, metricInfoBean, d, metric, subMetric);
    }


    public GetOverseaTrendLineDataResponseType getBus105106107SubTrendLineData(GetOverseaTrendLineDataRequestType request,
                                                                               String d,
                                                                               List<ExamineConfigBean> examineConfigBeanList) throws Exception {

        String subMetric = request.getSubMetric();
        CompetitorSubMetricCalStrategy metricCalStrategy = competitorCalStrategyMap.get(subMetric);
        if (metricCalStrategy == null) {
            throw new StrategyException("strategy is not found!");
        }
        return metricCalStrategy.getBus105106107SubTrendLineData(request, d, examineConfigBeanList);
    }


    public GetOverseaDrillDownBaseInfoResponseType getBus105106107SubDrillDownBaseInfo(GetOverseaDrillDownBaseInfoRequestType request,
                                                                                       String d,
                                                                                       OverseaMetricInfoBean metricInfoBean) throws Exception {

        String subMetric = request.getSubMetric();
        CompetitorSubMetricCalStrategy metricCalStrategy = competitorCalStrategyMap.get(subMetric);
        if (metricCalStrategy == null) {
            throw new StrategyException("strategy is not found!");
        }
        return metricCalStrategy.getBus105106107SubDrillDownBaseInfo(request, d, metricInfoBean);
    }


    public GetOverseaTableDataResponseType getBus105106107SubTableData(GetOverseaTableDataRequestType request,
                                                                       String d,
                                                                       OverseaMetricInfoBean metricInfoBean) throws Exception {

        String subMetric = request.getSubMetric();
        CompetitorSubMetricCalStrategy metricCalStrategy = competitorCalStrategyMap.get(subMetric);
        if (metricCalStrategy == null) {
            throw new StrategyException("strategy is not found!");
        }
        return metricCalStrategy.getBus105106107SubTableData(request, d, metricInfoBean);
    }







    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
        Map<String, IncomeSubMetricCalStrategy> incomeStrategyMap = applicationContext.getBeansOfType(IncomeSubMetricCalStrategy.class);
        incomeStrategyMap.values().forEach(e -> incomeCalStrategyMap.put(e.getSubMetricName(), e));
        Map<String, QuantitySubMetricCalStrategy> quantityStrategyMap = applicationContext.getBeansOfType(QuantitySubMetricCalStrategy.class);
        quantityStrategyMap.values().forEach(e->quantityCalStrategyMap.put(e.getSubMetricName(),e));
        Map<String, CompetitorSubMetricCalStrategy> competitorStrategyMap = applicationContext.getBeansOfType(CompetitorSubMetricCalStrategy.class);
        competitorStrategyMap.values().forEach(e->competitorCalStrategyMap.put(e.getSubMetricName(),e));
    }
}
