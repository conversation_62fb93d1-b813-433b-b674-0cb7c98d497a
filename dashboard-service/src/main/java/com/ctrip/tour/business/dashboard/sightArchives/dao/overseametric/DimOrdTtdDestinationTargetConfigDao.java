package com.ctrip.tour.business.dashboard.sightArchives.dao.overseametric;

import com.ctrip.soa._27181.PreparedParameterBean;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.CdmOrdTtdOverseasPerformanceIndexBO;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.DimOrdTtdDestinationTargetBO;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.bean.DimOrdTtdDestinationTargetParamBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseasRelatedSearchParamBean;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.TktStarRocksDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.sql.Types;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.Objects;

@Repository
@Slf4j
public class DimOrdTtdDestinationTargetConfigDao {

    @Autowired
    private TktStarRocksDao tktStarRocksDao;
    @Autowired
    private DimOrdTtdPersonTargetConfigDao dimOrdTtdPersonTargetConfigDao;

    /**
     * 获取目的地目标数据
     * @return
     */
    public Double queryOverseaDestinationTargetInfo(OverseasRelatedSearchParamBean searchParamBean) {

        StringBuilder sql = new StringBuilder("SELECT");
        switch (searchParamBean.getQuarter()) {
            case "Q1":
                sql.append("  SUM(COALESCE(CAST(q1 AS DOUBLE), 0)) as examineTarget");
                break;
            case "Q2":
                sql.append("  SUM(COALESCE(CAST(q2 AS DOUBLE), 0)) as examineTarget");
                break;
            case "Q3":
                sql.append("  SUM(COALESCE(CAST(q3 AS DOUBLE), 0)) as examineTarget");
                break;
            case "Q4":
                sql.append("  SUM(COALESCE(CAST(q4 AS DOUBLE), 0)) as examineTarget");
                break;
            default:
                return 0.00;
        }
        sql.append(" FROM ods_data_upload_new_performance_target_latest WHERE 1=1");
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendBuType(parameters, sql, searchParamBean.getBuTypeNames());
        appendYear(parameters, sql, searchParamBean.getYear());
        appendBusinessRegionName(parameters, sql, searchParamBean.getBusinessRegionName());
        appendBusinessSubRegionName(parameters, sql, searchParamBean.getBusinessSubRegionNames());
        appendMetric(parameters, sql, searchParamBean.getExamineMetricType());
        appendCTName(parameters, sql, searchParamBean.getCt());
        appendD(parameters, sql, searchParamBean.getD());
        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            log.warn("queryOverseaDestinationTargetInfo error", e);
        }
        if (!CollectionUtils.isEmpty(result)) {
            return result.stream()
                    .map(bean -> bean.get("examineTarget"))
                    .filter(Objects::nonNull)
                    .map(value -> Double.parseDouble(value.toString()))
                    .findFirst()
                    .orElse(0.00);
        }
        return 0.00;
    }

    //拼业务线
    private void appendBuType(List<PreparedParameterBean> parameters, StringBuilder sql, List<String> buTypeNames) {
        if (CollectionUtils.isNotEmpty(buTypeNames)) {
            sql.append(" and bu_type in (");
            for (int i = 0; i < buTypeNames.size(); i++) {
                if (i == 0) {
                    sql.append("?");
                } else {
                    sql.append(",?");
                }
                parameters.add(new PreparedParameterBean(buTypeNames.get(i), Types.VARCHAR));
            }
            sql.append(")");
        }
    }

    //拼年份
    private void appendYear(List<PreparedParameterBean> parameters, StringBuilder sql, String year) {
        if (year != null) {
            sql.append(" and examine_year = ?");
            parameters.add(new PreparedParameterBean(year, Types.VARCHAR));
        }
    }


    //拼业务大区
    private void appendBusinessRegionName(List<PreparedParameterBean> parameters, StringBuilder sql, List<String> businessRegionName) {
        if (CollectionUtils.isNotEmpty(businessRegionName)) {
            sql.append(" and business_region_name in (");
            for (int i = 0; i < businessRegionName.size(); i++) {
                if (i == 0) {
                    sql.append("?");
                } else {
                    sql.append(",?");
                }
                parameters.add(new PreparedParameterBean(businessRegionName.get(i), Types.VARCHAR));
            }
            sql.append(")");
        }
    }

    //拼业务子区域
    private void appendBusinessSubRegionName(List<PreparedParameterBean> parameters, StringBuilder sql, List<String> businessSubRegionNames) {
        if (CollectionUtils.isNotEmpty(businessSubRegionNames)) {
            sql.append(" and business_sub_region_name in (");
            for (int i = 0; i < businessSubRegionNames.size(); i++) {
                if (i == 0) {
                    sql.append("?");
                } else {
                    sql.append(",?");
                }
                parameters.add(new PreparedParameterBean(businessSubRegionNames.get(i), Types.VARCHAR));
            }
            sql.append(")");
        }
    }

    //拼考核指标
    private void appendMetric(List<PreparedParameterBean> parameters, StringBuilder sql, String metric) {
        if (metric != null) {
            sql.append(" and examine_metric_type = ?");
            parameters.add(new PreparedParameterBean(metric, Types.VARCHAR));
        }
    }

    //拼季度
    public void appendQuarter(List<PreparedParameterBean> parameters, StringBuilder sql, String quarter) {
        if (quarter != null) {
            sql.append(" and use_quarter=?");
            parameters.add(new PreparedParameterBean(quarter, Types.VARCHAR));
        }
    }

    //拼ct站点
    private void appendCTName(List<PreparedParameterBean> parameters, StringBuilder sql, String ctName) {
        if (ctName != null) {
            if ("c".equals(ctName)) {
                ctName = "C目的地";//NOSONAR
                sql.append(" and ct = ?");
                parameters.add(new PreparedParameterBean(ctName, Types.VARCHAR));
            } else if ("t".equals(ctName)) {
                ctName = "T目的地";//NOSONAR
                sql.append(" and ct = ?");
                parameters.add(new PreparedParameterBean(ctName, Types.VARCHAR));
            }
        }
    }

    //拼时间
    private void appendD(List<PreparedParameterBean> parameters, StringBuilder sql, String d) {
        if (d != null) {
            sql.append(" and d=?");
            parameters.add(new PreparedParameterBean(d, Types.VARCHAR));
        }
    }


    private void appendSql(StringBuilder sql, String sqlAppend, List<PreparedParameterBean> parameters, List<String> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            sql.append(sqlAppend);
            for (int i = 0; i < list.size(); i++) {
                if (i == 0) {
                    sql.append("?");
                } else {
                    sql.append(",?");
                }
                parameters.add(new PreparedParameterBean(list.get(i), Types.VARCHAR));
            }
            sql.append(")");
        }
    }
    public List<DimOrdTtdDestinationTargetBO> queryDimOrdTtdDestinationTargetBOList(DimOrdTtdDestinationTargetParamBean param) {
        StringBuilder sql = new StringBuilder("select * from ods_data_upload_new_performance_target_latest where d=? and  examine_year=?  " +
                " and examine_metric_type =? " );
        List<PreparedParameterBean> parameters = new java.util.ArrayList<>();
        parameters.add(new PreparedParameterBean(param.getD(), Types.VARCHAR));
        parameters.add(new PreparedParameterBean(param.getExamineYear(), Types.VARCHAR));
        parameters.add(new PreparedParameterBean(param.getExamineMetricType(), Types.VARCHAR));
        appendSql(sql," and ct in ( ",parameters,param.getCT());
        appendSql(sql," and bu_type in ( ",parameters,param.getBuType());
        if(CollectionUtils.isNotEmpty(param.getBusinessRegionName())){
            appendSql(sql," and business_region_name in ( ",parameters,param.getBusinessRegionName());
        }
        if(CollectionUtils.isNotEmpty(param.getBusinessSubRegionName())){
            appendSql(sql," and business_sub_region_name in ( ",parameters,param.getBusinessSubRegionName());
        }
        List<Map<String, Object>> result = new ArrayList<>();
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            log.warn("queryOverseaPersonInfo error", e);
        }
        List<DimOrdTtdDestinationTargetBO> destinationTargetList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(result)) {
            destinationTargetList = result.stream()
                    .map(bean -> {
                        DimOrdTtdDestinationTargetBO destinationTarget = new DimOrdTtdDestinationTargetBO();
                        destinationTarget.setBusinessRegionName((String)bean.get("business_region_name"));
                        destinationTarget.setBusinessSubRegionName((String)bean.get("business_sub_region_name"));
                        destinationTarget.setExamineMetricType((String)bean.get("examine_metric_type"));
                        destinationTarget.setExamineYear((String)bean.get("examine_year"));
                        destinationTarget.setBuType((String)bean.get("bu_type"));
                        destinationTarget.setCT(((String)bean.get("ct")).replace(",",""));
                        destinationTarget.setQ1(((String)bean.get("q1")).replace(",",""));
                        destinationTarget.setQ2(((String)bean.get("q2")).replace(",",""));
                        destinationTarget.setQ3(((String)bean.get("q3")).replace(",",""));
                        destinationTarget.setQ4(((String)bean.get("q4")).replace(",",""));
                        return destinationTarget;
                    })
                    .collect(Collectors.toList());
        }
        return destinationTargetList;
    }


}
