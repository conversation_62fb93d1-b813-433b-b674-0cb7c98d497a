package com.ctrip.tour.business.dashboard.biz.impl

import com.ctrip.ottd.product.background.openapi.external.GetMixProductPreviewStatusRequestType
import com.ctrip.ottd.product.background.openapi.external.GetMixProductPreviewStatusResponseType
import com.ctrip.ottd.product.background.openapi.external.GetPreviewInfoResponse
import com.ctrip.soa._24922.GetPreviewInfoRequestType
import com.ctrip.tour.business.dashboard.tktBusiness.biz.impl.TicketPKBizImpl
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardEmployeeInfoDao
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardEmployeeInfo
import com.ctrip.tour.business.dashboard.utils.UserUtil
import com.ctrip.ttd.vendor.soa.BIVendorServiceClient
import com.ctrip.ttd.vendor.soa.GetTicketPKBestStatusRequestType
import com.ctrip.ttd.vendor.soa.GetTicketPKBestStatusResponseType
import com.ctrip.ttd.vendor.soa.GetTicketPKDefectOrderIdRequestType
import com.ctrip.ttd.vendor.soa.GetTicketPKDefectOrderIdResponseType
import com.ctrip.ttd.vendor.soa.GetTicketPKDefectOverallDataRequestType
import com.ctrip.ttd.vendor.soa.GetTicketPKDefectOverallDataResponseType
import com.ctrip.ttd.vendor.soa.GetTicketPKDefectTableDataRequestType
import com.ctrip.ttd.vendor.soa.GetTicketPKDefectTableDataResponseType
import com.ctrip.ttd.vendor.soa.GetTicketPKEnumDataRequestType
import com.ctrip.ttd.vendor.soa.GetTicketPKQualificationRequestType
import com.ctrip.ttd.vendor.soa.GetTicketPKQualificationResponseType
import com.ctrip.ttd.vendor.soa.GetTicketPKResourceRankingRequestType
import com.ctrip.ttd.vendor.soa.GetTicketPKResourceRankingResponseType
import com.ctrip.ttd.vendor.soa.GetTicketPKSaleunitRankingRequestType
import com.ctrip.ttd.vendor.soa.GetTicketPKSaleunitRankingResponseType
import com.ctrip.ttd.vendor.soa.GetTicketPKScheduleCalendarRequestType
import com.ctrip.ttd.vendor.soa.GetTicketPKScheduleCalendarResponseType
import com.ctrip.ttd.vendor.soa.GetTicketPKTableRequestType
import com.ctrip.ttd.vendor.soa.GetTicketPKTableResponseType
import com.ctrip.ttd.vendor.soa.TicketPkBaseQuery
import com.ctriposs.baiji.rpc.common.types.AckCodeType
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType

import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.spockframework.runtime.Sputnik
import spock.lang.Specification
import spock.lang.Unroll

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik.class)
@PowerMockIgnore(["javax.net.ssl.*", "javax.management.*", "javax.crypto.*"])
@PrepareForTest([UserUtil.class])
class TicketPKBizImplTest extends Specification {

    def remoteConfig = Mock(RemoteConfig)
    def employeeInfoDao = Mock(BusinessDashboardEmployeeInfoDao)
    def client = Mock(BIVendorServiceClient)
    TicketPKBizImpl instance = new TicketPKBizImpl()
//    def savePoint = new SavePoint()
//
//    def cleanup() {
//        savePoint.rollback()
//    }

    def setup(){
        PowerMockito.mockStatic(UserUtil.class)

        instance.client = client
        instance.remoteConfig = remoteConfig;
        instance.employeeInfoDao = employeeInfoDao
    }

    @Unroll
    def "test getTicketPKEnumData exception"() {
        given: ""
        PowerMockito.when(UserUtil.getMappingEmpCode(Mockito.anyObject())).thenReturn(emp)
        employeeInfoDao.queryByEmpCode(_) >> empPO
        client.getTicketPKEnumData(_)>> null
        when: ""
        GetTicketPKEnumDataRequestType request = new GetTicketPKEnumDataRequestType();

        instance.getTicketPKEnumData(request)
        then: ""
     //   def exception = thrown(Exception)
        where: ""
        emp |empPO
        null|null
        "aa"|null
    }
    @Unroll
    def "test getTicketPKEnumData "() {
        given: ""
        PowerMockito.when(UserUtil.getPkMappingEmpCode(Mockito.anyObject())).thenReturn("ba")
        employeeInfoDao.queryByEmpCode(_) >> new BusinessDashboardEmployeeInfo(domainName: "abc")
        and:""
        client.getTicketPKEnumData(_)>> null
        when: ""
        GetTicketPKEnumDataRequestType request = new GetTicketPKEnumDataRequestType();

        instance.getTicketPKEnumData(request)
        then: ""

        where: ""
    }

    @Unroll
    def "test getTicketPKSaleunitRanking exception"() {
        given: ""
        PowerMockito.when(UserUtil.getPkMappingEmpCode(Mockito.anyObject())).thenReturn("ba")
        employeeInfoDao.queryByEmpCode(_) >> new BusinessDashboardEmployeeInfo(domainName: "abc")
        and:""
        client.getTicketPKSaleunitRanking(_)>> null
        when: ""
        GetTicketPKSaleunitRankingRequestType request = new GetTicketPKSaleunitRankingRequestType();
        instance.getTicketPKSaleunitRanking(request)
        then: ""

//        def exception = thrown(Exception)
        where: ""
    }

    @Unroll
    def "test getTicketPKSaleunitRanking"() {
        given: ""
        PowerMockito.when(UserUtil.getPkMappingEmpCode(Mockito.anyObject())).thenReturn("ba")
        employeeInfoDao.queryByEmpCode(_) >> new BusinessDashboardEmployeeInfo(domainName: "abc")
        and:""
        client.getTicketPKSaleunitRanking(_)>> new GetTicketPKSaleunitRankingResponseType()
        when: ""
        GetTicketPKSaleunitRankingRequestType request = new GetTicketPKSaleunitRankingRequestType();
        request.setQuery(new TicketPkBaseQuery())
        instance.getTicketPKSaleunitRanking(request)
        then: ""
        where: ""
    }

    @Unroll
    def "test getTicketPKResourceRanking"() {
        given: ""
        PowerMockito.when(UserUtil.getPkMappingEmpCode(Mockito.anyObject())).thenReturn("ba")
        employeeInfoDao.queryByEmpCode(_) >> new BusinessDashboardEmployeeInfo(domainName: "abc")
        and:""
        client.getTicketPKResourceRanking(_)>> new GetTicketPKResourceRankingResponseType()
        when: ""
        GetTicketPKResourceRankingRequestType request = new GetTicketPKResourceRankingRequestType();
        request.setQuery(new TicketPkBaseQuery())
        instance.getTicketPKResourceRanking(request)
        then: ""

        where: ""
    }

    @Unroll
    def "test getTicketPKScheduleCalendar"() {
        given: ""
        PowerMockito.when(UserUtil.getPkMappingEmpCode(Mockito.anyObject())).thenReturn("ba")
        employeeInfoDao.queryByEmpCode(_) >> new BusinessDashboardEmployeeInfo(domainName: "abc")
        and:""
        client.getTicketPKResourceRanking(_)>> new GetTicketPKScheduleCalendarResponseType()
        when: ""
        GetTicketPKScheduleCalendarRequestType request = new GetTicketPKScheduleCalendarRequestType();
        request.setQuery(new TicketPkBaseQuery())
        instance.getTicketPKScheduleCalendar(request)
        then: ""

        where: ""
    }

    @Unroll
    def "test getTicketPKQualification"() {
        given: ""
        PowerMockito.when(UserUtil.getPkMappingEmpCode(Mockito.anyObject())).thenReturn("ba")
        employeeInfoDao.queryByEmpCode(_) >> new BusinessDashboardEmployeeInfo(domainName: "abc")
        and:""
        client.getTicketPKResourceRanking(_)>> new GetTicketPKQualificationResponseType()
        when: ""
        GetTicketPKQualificationRequestType request = new GetTicketPKQualificationRequestType();
        request.setQuery(new TicketPkBaseQuery())
        instance.getTicketPKQualification(request)
        then: ""

        where: ""
    }

    @Unroll
    def "test getTicketPKTable"() {
        given: ""
        PowerMockito.when(UserUtil.getPkMappingEmpCode(Mockito.anyObject())).thenReturn("ba")
        employeeInfoDao.queryByEmpCode(_) >> new BusinessDashboardEmployeeInfo(domainName: "abc")
        and:""
        client.getTicketPKResourceRanking(_)>> new GetTicketPKTableResponseType()
        when: ""
        GetTicketPKTableRequestType request = new GetTicketPKTableRequestType();
        request.setQuery(new TicketPkBaseQuery())
        instance.getTicketPKTable(request)
        then: ""

        where: ""
    }

    @Unroll
    def "test getTicketPKBestStatus"() {
        given: ""
        PowerMockito.when(UserUtil.getPkMappingEmpCode(Mockito.anyObject())).thenReturn("ba")
        employeeInfoDao.queryByEmpCode(_) >> new BusinessDashboardEmployeeInfo(domainName: "abc")
        and:""
        client.getTicketPKResourceRanking(_)>> new GetTicketPKBestStatusResponseType()
        when: ""
        GetTicketPKBestStatusRequestType request = new GetTicketPKBestStatusRequestType();
        request.setQuery(new TicketPkBaseQuery())
        instance.getTicketPKBestStatus(request)
        then: ""

        where: ""
    }

    @Unroll
    def "test getTicketPKDefectOverallData"() {
        given: ""
        PowerMockito.when(UserUtil.getPkMappingEmpCode(Mockito.anyObject())).thenReturn("ba")
        employeeInfoDao.queryByEmpCode(_) >> new BusinessDashboardEmployeeInfo(domainName: "abc")
        and:""
        client.getTicketPKResourceRanking(_)>> new GetTicketPKDefectOverallDataResponseType()
        when: ""
        GetTicketPKDefectOverallDataRequestType request = new GetTicketPKDefectOverallDataRequestType();
        request.setQuery(new TicketPkBaseQuery())
        instance.getTicketPKDefectOverallData(request)
        then: ""

        where: ""
    }

    @Unroll
    def "test getTicketPKDefectTableData"() {
        given: ""
        PowerMockito.when(UserUtil.getPkMappingEmpCode(Mockito.anyObject())).thenReturn("ba")
        employeeInfoDao.queryByEmpCode(_) >> new BusinessDashboardEmployeeInfo(domainName: "abc")
        and:""
        client.getTicketPKResourceRanking(_)>> new GetTicketPKDefectTableDataResponseType()
        when: ""
        GetTicketPKDefectTableDataRequestType request = new GetTicketPKDefectTableDataRequestType();
        request.setQuery(new TicketPkBaseQuery())
        instance.getTicketPKDefectTableData(request)
        then: ""

        where: ""
    }

    @Unroll
    def "test getTicketPKDefectOrderId"() {
        given: ""
        PowerMockito.when(UserUtil.getPkMappingEmpCode(Mockito.anyObject())).thenReturn("ba")
        employeeInfoDao.queryByEmpCode(_) >> new BusinessDashboardEmployeeInfo(domainName: "abc")
        and:""
        client.getTicketPKResourceRanking(_)>> new GetTicketPKDefectOrderIdResponseType()
        when: ""
        GetTicketPKDefectOrderIdRequestType request = new GetTicketPKDefectOrderIdRequestType();
        request.setQuery(new TicketPkBaseQuery())
        instance.getTicketPKDefectOrderId(request)
        then: ""

        where: ""
    }

    @Unroll
    def "test getMixProductPreviewStatus"() {
        given: ""
        PowerMockito.when(UserUtil.getPkMappingEmpCode(Mockito.anyObject())).thenReturn("ba")
        employeeInfoDao.queryByEmpCode(_) >> new BusinessDashboardEmployeeInfo(domainName: "abc")
        and:""
        client.getTicketPKResourceRanking(_)>> new GetMixProductPreviewStatusResponseType()
        when: ""
        GetMixProductPreviewStatusRequestType request = new GetMixProductPreviewStatusRequestType();

        instance.getMixProductPreviewStatus(request)
        then: ""

        where: ""
    }

    @Unroll
    def "test getPreviewInfo"() {
        given: ""
        PowerMockito.when(UserUtil.getPkMappingEmpCode(Mockito.anyObject())).thenReturn("ba")
        employeeInfoDao.queryByEmpCode(_) >> new BusinessDashboardEmployeeInfo(domainName: "abc")
        and:""
        client.getPreviewInfo(_)>> new GetPreviewInfoResponse(responseStatus: getResponseStatus(),
                previewInfo: new com.ctrip.ottd.product.background.openapi.external.dto.PreviewInfo())
        when: ""
        GetPreviewInfoRequestType request = new GetPreviewInfoRequestType();
        instance.getPreviewInfo(request)
        then: ""

        where: ""
    }

    def getResponseStatus(){
        ResponseStatusType responseStatusType = new ResponseStatusType();
        responseStatusType.setAck(AckCodeType.Success);
        return responseStatusType
    }
}
