package com.ctrip.tour.business.dashboard.tktBusiness.strategy.subMetricImpl;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ExamineConfigBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.CompetitorSubMetricCalStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.Future;

/**
 * 海外竞品客路子策略
 */
@Component
public class TklkMetricStrategy implements CompetitorSubMetricCalStrategy {

    @Autowired
    private CompetitorBaseStrategy competitorBaseStrategy;


    @Override
    public Future<MetricDetailInfo> getBus105106107SubMetricCardData(TimeFilter timeFilter,
                                                                     OverseaMetricInfoBean metricInfoBean,
                                                                     String d,
                                                                     String metric,
                                                                     String subMetric) throws Exception {
        return competitorBaseStrategy.getBus105106107SubMetricCardData(timeFilter, metricInfoBean, d, metric, subMetric);
    }

    @Override
    public GetOverseaTrendLineDataResponseType getBus105106107SubTrendLineData(GetOverseaTrendLineDataRequestType request,
                                                                               String d,
                                                                               List<ExamineConfigBean> examineConfigBeanList) throws Exception {
        return competitorBaseStrategy.getBus105106107SubTrendLineData(request, d, examineConfigBeanList);
    }

    @Override
    public GetOverseaDrillDownBaseInfoResponseType getBus105106107SubDrillDownBaseInfo(GetOverseaDrillDownBaseInfoRequestType request,
                                                                                       String d,
                                                                                       OverseaMetricInfoBean metricInfoBean) throws Exception {
        return competitorBaseStrategy.getBus105106107SubDrillDownBaseInfo(request, d, metricInfoBean);
    }

    @Override
    public GetOverseaTableDataResponseType getBus105106107SubTableData(GetOverseaTableDataRequestType request,
                                                                       String d,
                                                                       OverseaMetricInfoBean metricInfoBean) throws Exception {
        return competitorBaseStrategy.getBus105106107SubTableData(request, d, metricInfoBean);
    }


    @Override
    public String getSubMetricName() {
        return "tklk";
    }
}
