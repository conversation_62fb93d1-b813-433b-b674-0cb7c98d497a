package com.ctrip.tour.business.dashboard.grpBusiness.domain.dsl;

import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;

import java.io.IOException;
import java.io.StringWriter;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class SQLTemplateUtils {
    // 创建配置实例
    private static final Configuration cfg = new Configuration(Configuration.getVersion());
    private static final Map<String, Template> templateMap = new ConcurrentHashMap<>();

    public static String prepareSQLWithParam(String sqlTemplate, Map<String, Object> param) throws IOException, TemplateException {
        // 定义模板内容
        Template template;
        String hashString = "" + sqlTemplate.hashCode();
        if (!templateMap.containsKey(hashString)) {
            template = new Template(hashString, sqlTemplate, cfg);
            templateMap.put(hashString, template);
        }
        template = templateMap.get(hashString);
        // 渲染模板
        StringWriter writer = new StringWriter();
        template.process(param, writer);
        // 输出结果
        return writer.toString();
    }
}
