package com.ctrip.tour.business.dashboard.tktBusiness.bean;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class PushMessageFileBean {
    @SerializedName(value = "mimetype")
    String mimeType;
    @SerializedName(value = "filename")
    String fileName;
    @SerializedName(value = "filepath")
    String filePath;
    @SerializedName(value = "fileid")
    String fileId;
}
