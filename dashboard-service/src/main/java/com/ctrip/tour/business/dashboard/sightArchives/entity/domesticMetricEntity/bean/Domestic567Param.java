package com.ctrip.tour.business.dashboard.sightArchives.entity.domesticMetricEntity.bean;

import lombok.Data;

import java.util.List;

@Data
public class Domestic567Param {
    Integer pageIndex;
    Integer pageSize;
    String field;
    String metric;
    // 查询时间
    String d;
    // 年份
    String year;
    // 季度列表
    List<String> quarters;
    // 月份
    List<String> month;
    String examineLevel;
    String statisticsDimId;
    List<String> provinceName;
    List<String> businessRegionName;
    //	考核对象-商拓
    List<String> examineObject;

    //序号小于等于5的标识符
    String isCoefficientIdentifier;
}
