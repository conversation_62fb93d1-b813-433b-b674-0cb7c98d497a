package com.ctrip.tour.business.dashboard.sightArchives.entity.domesticMetricEntity;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;

@Entity
@Getter
@Setter
public class AdmPrdTtdCategoryCoverInfoBO {
    String 	d;
    String year;
    String month;
    String quarter;
    //时间类型(month,quarter)
    String dateType;
    //业务大区名称
    String businessRegionName;
    //	省份名称
    String provinceName;
    //品类类型：0-特色品类， 1-重点品类
    Integer categoryType;
    //	品类权重
    Double categoryWeight;
    //藏宝图特色体验数
    Long treasuremapXxpCnt;
    //营业中特色体验数
    Long openExpCnt;
    //已覆盖特色体验数
    Long coverExpCnt;
    //未覆盖特色体验数
    Long uncoverExpCnt;

}
