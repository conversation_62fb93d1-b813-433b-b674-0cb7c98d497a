package com.ctrip.tour.business.dashboard.helper

import com.ctrip.soa._24922.GetDrillDownBaseInfoRequestType
import com.ctrip.soa._24922.TimeFilter
import com.ctrip.tour.business.dashboard.tktBusiness.bean.DrillDownFieldBean
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig
import com.ctrip.tour.business.dashboard.tktBusiness.helper.Bus1Helper
import com.ctrip.tour.business.dashboard.tktBusiness.helper.Bus2Helper
import com.ctrip.tour.business.dashboard.utils.MapperUtil
import spock.lang.Specification
import spock.lang.Unroll

class Bus2HelperTest extends Specification {

    def remoteConfig = Mock(RemoteConfig)

    def setup() {
    }

    @Unroll
    def "获取可下钻维度数据接口 输入的level为 #level regionList为 #regionList"() {
        given: "设定请求参数"
        def metricInfoBean = new MetricInfoBean(level: level, regionList: regionList)

        and: "mock qconfig配置信息"
        remoteConfig.getConfigValue("domestic") >> "国内"
        remoteConfig.getConfigValue("three") >> "三方"
        remoteConfig.getConfigValue("region") >> "大区"
        remoteConfig.getConfigValue("province") >> "省份"

        when:
        def resultList = Bus1Helper.getDrillDownFieldList(metricInfoBean, remoteConfig)

        then: "验证多种返回结果"
        resultList == actualResultList

        where: "用表格方式验证多种返回结果"
        level  | regionList       | actualResultList
        "国内" | []               | ["region_name", "province_name", "examinee"]
        "大区" | ["华东大区"]     | ["province_name", "examinee"]
        "省份" | ["江苏", "上海"] | ["province_name", "examinee"]
        "省份" | ["江苏"]         | ["examinee"]
    }


    @Unroll
    def "获取下钻基础数据 输入的field为#field"() {
        given: "设定请求参数"
        def request = new GetDrillDownBaseInfoRequestType(domainName: "zheli", timeFilter:
                new TimeFilter(year: 2023, month: 11, dateType: "month"), metric: "2",
                subMetric: "notLimit", needSearch: false)
        def metricInfoBean = new MetricInfoBean(level: "国内", odtLevel: "国内", metric: "2"
                , examineType: 6)

        and: "mock qconfig配置信息"
        remoteConfig.getConfigValue("domestic") >> "国内"
        remoteConfig.getConfigValue("region") >> "大区"
        remoteConfig.getConfigValue("province") >> "省份"
        remoteConfig.getConfigValue("universalStudios") >> "环球影城"
        remoteConfig.getConfigValue("kaRegion") >> "KA大区"

        String str1 = "{\"subMetric\":\"notLimit\",\"field\":\"region_name\",\"baseInfoId\":24,\"baseInfoGroupList\":[\"region_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"other\":[\"region_name\"]},\"tableDataIdMap\":{\"current\":24,\"other\":25,\"target\":26},\"bubbleGroupListMap\":{\"other\":[\"region_name\"]}}";
        String str2 = "{\"subMetric\":\"notLimit\",\"field\":\"province_name\",\"baseInfoId\":24,\"baseInfoGroupList\":[\"province_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\"]},\"tableDataIdMap\":{\"current\":24,\"other\":25,\"target\":26},\"bubbleGroupListMap\":{\"other\":[\"province_name\"]}}"
        String str3 = "{\"subMetric\":\"notLimit\",\"field\":\"examinee\",\"baseInfoId\":29,\"baseInfoGroupList\":[\"examinee\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"current\":[\"region_name\",\"ttd_province_name\",\"odt_province_name\",\"examinee_name\",\"examinee\"],\"other\":[\"examinee\"]},\"tableDataIdMap\":{\"currentmonth\":29,\"currentquarter\":30,\"otherttd\":31,\"otherodt\":22}}"

        remoteConfig.getDrillDownFieldBean(_,_, "region_name") >> MapperUtil.str2Obj(str1, DrillDownFieldBean.class)
        remoteConfig.getDrillDownFieldBean(_,_, "province_name") >> MapperUtil.str2Obj(str2, DrillDownFieldBean.class)
        remoteConfig.getDrillDownFieldBean(_,_, "examinee") >> MapperUtil.str2Obj(str3, DrillDownFieldBean.class)


        when:
        def result = Bus2Helper.getDrillDownBaseInfoSqlBean(field,request,"2023-11-13",metricInfoBean,remoteConfig)

        then: "验证多种返回结果"
        with(result){
            id == actualId
            notInMap.size() == actualNotInMapSize
            notLikeMap.size() == actualNotLikeMapSize
        }

        where: "用表格方式验证多种返回结果"
        field           | actualId | actualNotInMapSize | actualNotLikeMapSize
        "region_name"   | 24       | 0                  | 0
        "province_name" | 24       | 0                  | 0
        "examinee"      | 29       | 0                  | 0
    }


}
