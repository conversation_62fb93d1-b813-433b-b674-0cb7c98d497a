package com.ctrip.tour.business.dashboard.sightArchives.entity.domesticMetricEntity.bean;

import lombok.Data;

import java.util.List;

@Data
public class Domestic9Param {
    Integer pageIndex;
    Integer pageSize;
    String field;
    String 	d;
    String year;
    String month;
    List<String> quarter;
    //时间类型(month,quarter)
    String dateType;
    //业务大区名称
    List<String> businessRegionName;
    //	省份名称
    List<String> provinceName;
}
