package com.ctrip.tour.business.dashboard.grpBusiness.job;

import java.sql.SQLException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ctrip.tour.business.dashboard.grpBusiness.dao.mysql.DimOrdGrpAchv2025PersonTrgtDao;
import com.ctrip.tour.business.dashboard.grpBusiness.dao.starrocks.StarRocksCommonDao;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.group.workbenchsvc.contract.StructuredTableInfoType;
import com.ctrip.tour.group.workbenchsvc.contract.StructuredTableRowInfoType;
import com.ctrip.tour.rights.client.GroupWorkbenchServiceClient;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2025/3/25
 */
@Service
@Slf4j
public class Pre30dDensityAlertNotifyService extends CommonAlertNotifyService {

    private static final String TASK_GRP_PROFIT_ALERT = "TASK_DOUBLE_BEST_OPEN_SCHEDULE_ALERT";
    private static final String EVENT_GRP_PROFIT_ALERT = "EVENT_DOUBLE_BEST_OPEN_SCHEDULE_ALERT";
    private static final String EVENT_GRP_PROFIT_ALERT_STRUCTURED_TABLE = "EVENT_DOUBLE_BEST_OPEN_SCHEDULE_ALERT_STRUCTURED_TABLE";
//private static final String TASK_GRP_PROFIT_ALERT = "TASK_NOTICE_TEST";
//    private static final String EVENT_GRP_PROFIT_ALERT = "EVENT_NOTICE_TEST";
//    private static final String EVENT_GRP_PROFIT_ALERT_STRUCTURED_TABLE = "EVENT_NOTICE_TEST_STRUCTURED_TABLE";

    @Autowired
    private StarRocksCommonDao starRocksCommonDao;

    @Autowired
    private DimOrdGrpAchv2025PersonTrgtDao achv2025PersonTrgtDao;

    private static String NOTIFY_EVENT_EMPCODES = "notify.event.empcodes";

    private GroupWorkbenchServiceClient client = GroupWorkbenchServiceClient.getInstance();

    @Autowired
    private RemoteConfig remoteConfig;

    public void handleMultipriceNotify() throws Exception {

        String whiteEmpCodesStr = remoteConfig.getExternalConfig(NOTIFY_EVENT_EMPCODES);

        List<String> whiteEmpCodes = Lists.newArrayList();
        if (StringUtils.isNotBlank(whiteEmpCodesStr)) {
            whiteEmpCodes = Splitter.on(",").splitToList(whiteEmpCodesStr);
        }

        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");


        String sql = "SELECT \n" +
                "    pm_eid,\n" +
                "    dest_country_id,\n" +
                "    productid,\n" +
                "    avg(pre30d_upg_self_tdate_density) as pre30d_tdate_density\n" +
                "FROM \n" +
                "    adm_prd_grp_product_total_work_platform_df\n" +
                "WHERE \n" +
                "    pre30d_upg_self_tdate_density IS NOT NULL and sub_bu_type='跟团游' and etl_date='" + LocalDate.now().minusDays(1).format(dtf) + "'" +//NOSONAR
                " GROUP BY \n" +
                "    pm_eid, dest_country_id,productid\n" +
                "HAVING \n" +
                "    (\n" +
                "        dest_country_id = 1 AND \n" +
                "        avg(pre30d_upg_self_tdate_density) < 0.1\n" +
                "    ) OR (\n" +
                "        dest_country_id != 1 AND \n" +
                "        avg(pre30d_upg_self_tdate_density) < 0.05\n" +
                "    );";

        List<Map<String, Object>> resultList = starRocksCommonDao.query(sql, Maps.newHashMap());
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }

        List<String> empCodes = resultList.stream().map(pw -> (String) pw.get("pm_eid"))
                .distinct().collect(Collectors.toList());

        List<String> finalWhiteEmpCodes = whiteEmpCodes;
        empCodes.stream()
                .filter(pw -> CollectionUtils.isEmpty(finalWhiteEmpCodes) || (Objects.nonNull(pw) && finalWhiteEmpCodes.contains(pw)))
                .forEach( pmEid -> {



                    String pmSql = "SELECT \n" +
                            "    vendor_id,\n" +
                            "    vendor_name,\n" +
                            "    productid,\n" +
                            "    dest_country_id,\n" +
                            "    avg(pre30d_upg_self_tdate_density) as pre30d_tdate_density\n" +
                            "FROM \n" +
                            "    adm_prd_grp_product_total_work_platform_df\n" +
                            "WHERE \n" +
                            "     pre30d_upg_self_tdate_density is not null and sub_bu_type='跟团游' and etl_date='" + LocalDate.now().minusDays(1).format(dtf) + "' " +//NOSONAR
                            "and pm_eid = '" + pmEid+ "'"+
                            " GROUP BY \n" +
                            "    vendor_id,vendor_name,productid,dest_country_id" +
                    " HAVING \n" +
                            "    (\n" +
                            "        dest_country_id = 1 AND \n" +
                            "        avg(pre30d_upg_self_tdate_density) < 0.1\n" +
                            "    ) OR (\n" +
                            "        dest_country_id != 1 AND \n" +
                            "        avg(pre30d_upg_self_tdate_density) < 0.05\n" +
                            "    );";
                    List<Map<String, Object>> pmRst = null;
                    try {
                        pmRst = starRocksCommonDao.query(pmSql, Maps.newHashMap());
                    } catch (SQLException e) {
                        log.warn("query pm data error", e);
                        return;
                    }

                    List<StructuredTableRowInfoType> structuredTableRowInfoTypes = pmRst.stream().map(pm -> {
                        StructuredTableRowInfoType rowInfoType = new StructuredTableRowInfoType();
                        Integer vendor_id = (Integer) pm.get("vendor_id");
                        Long productid = (Long) pm.get("productid");
                        String vendor_name = (String) pm.get("vendor_name");
                        Integer dest_country_id = (Integer) pm.get("dest_country_id");
                        Double ratio = (Double) pm.get("pre30d_tdate_density");
                        List<String> colList = Lists.newArrayList(LocalDate.now().minusDays(1).format(dtf),vendor_id.toString(), vendor_name, productid.toString(),
                                Objects.equals(1, dest_country_id) ? "国内" : "海外", getDataRatioStr(ratio));//NOSONAR
                        rowInfoType.setColList(colList);
                        return rowInfoType;
                    }).collect(Collectors.toList());

                    StructuredTableInfoType structuredTableInfoType = new StructuredTableInfoType();
            structuredTableInfoType.setRowList(structuredTableRowInfoTypes);
            structuredTableInfoType.setHeaderList(Lists.newArrayList("统计日期", "供应商ID", "供应商名称", "产品ID", "境内境外", "班期密度"));//NOSONAR
            String content = "未来30天，挂牌自营班期密度低于境内10%/境外5%，请及时处理";//NOSONAR
            List<String> tpInfos = Lists.newArrayList("未来30天，挂牌自营班期密度低于境内10%/境外5%，请及时处理");//NOSONAR
            notifyEmp(pmEid, structuredTableInfoType, "【双好】开班率预警",//NOSONAR
                    TASK_GRP_PROFIT_ALERT, EVENT_GRP_PROFIT_ALERT, EVENT_GRP_PROFIT_ALERT_STRUCTURED_TABLE, content, tpInfos, "1");//NOSONAR
        });


    }


}
