package com.ctrip.tour.business.dashboard.tktBusiness.biz.impl;


import com.alibaba.fastjson.JSON;
import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.BenchRegion;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.KeyProjectDashboardConfigContentBean;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.DataUpdateBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.KeyProjectDashboardBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.UserPermissionBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.ConfigFrom100032373;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.*;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.*;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.ctrip.tour.business.dashboard.utils.UserUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class KeyProjectDashboardBizImpl implements KeyProjectDashboardBiz {

    @Autowired
    private RemoteConfig remoteConfig;
    @Autowired
    private UserPermissionBiz userPermissionBiz;
    @Autowired
    private DataUpdateBiz dataUpdateBiz;
    @Autowired
    private BusinessDashboardExamineeConfigV2Dao examineConfigV2Dao;
    @Autowired
    private BusinessDashboardOverseaExamineeConfigDao overseaConfigDao;
    @Autowired
    private BusinessDashboardEmployeeInfoDao employeeInfoDao;
    @Autowired
    private DimPrdTktScenicInfoCoreMappingDao dimPrdTktScenicInfoCoreMappingDao;
    @Autowired
    private MPkgVendorQualityDbBoardEmployeeDao mPkgVendorQualityDbBoardEmployeeDao;
    @Autowired
    ConfigFrom100032373 configFrom100032373;
    @Autowired
    private BusinessDashboardOrganizationInfoDao organizationInfoDao;

    @Override
    public CheckEmpBusinessTypeResponseType checkEmpBusinessType(CheckEmpBusinessTypeRequestType requestType) throws Exception {
        CheckEmpBusinessTypeResponseType responseType = new CheckEmpBusinessTypeResponseType();

        String empCode = UserUtil.getMappingEmpCode(remoteConfig);
        if("S35097".equals(empCode)) {  //门票ceo 老季
            responseType.setEmpBusinessType("all");
            return responseType;
        }


        //是否是门票的
        CheckUserPermissionRequestType checkUserPermissionRequestType = new CheckUserPermissionRequestType(null,null,null,null);
        boolean isInDepartment41999 = userPermissionBiz.isInDepartment41999(checkUserPermissionRequestType);
        if(!isInDepartment41999){
            responseType.setEmpBusinessType("noPermission");
            return responseType;
        }
        String domainName = checkUserPermissionRequestType.getDomainName();

        //是否参与考核
        String d = dataUpdateBiz.getUpdateTime();
        TimeFilter timeFilter = new TimeFilter();
        timeFilter.setDateType("quarter");
        timeFilter.setQuarter(DateUtil.getActualQuarterOfD(d));
        timeFilter.setYear(DateUtil.getActualYearOfD(d));
        //由于目标是统一按季度配置的 将所有传入的时间统一归到季度
        String year = timeFilter.getYear();
        String quarter = DateUtil.getQuarter(timeFilter.getDateType(), timeFilter.getMonth(), timeFilter.getQuarter(), timeFilter.getHalf());
        //国内目标配置
        List<BusinessDashboardExamineeConfigV2> examineeConfigV2List = examineConfigV2Dao.querySpecificPeriodAllMetricConfig(domainName, d, year, quarter, null);
        //海外目标配置
        List<BusinessDashboardOverseaExamineeConfig> overseaConfigList = overseaConfigDao.querySpecificPeriodAllMetricConfig(domainName, d, year, quarter);

        //一个在门票活动业务部范围内的人可以是不参加考核的
        if (GeneralUtil.isEmpty(examineeConfigV2List) && GeneralUtil.isEmpty(overseaConfigList)) {
            responseType.setEmpBusinessType("noPermission");
            return responseType;
        }

        //是否是海外业务
        boolean isOverseaBoard = userPermissionBiz.isOverseaboard(domainName);

        //目前只涉及国内   后续加上海外业务后 老季的EmpBusinessType需要同时包含domestic和oversea
        if(!isOverseaBoard && CollectionUtils.isNotEmpty(examineeConfigV2List)) {
            responseType.setEmpBusinessType("domestic");
            return responseType;
        }
        if(isOverseaBoard && CollectionUtils.isNotEmpty(overseaConfigList)){
            responseType.setEmpBusinessType("oversea");
            return responseType;
        }

        //如果以上逻辑都不符合，兜底设为无权限
        responseType.setEmpBusinessType("noPermission");
        return responseType;
    }


    public CheckEmpBusinessTypeResponseType checkEmpBusinessTypeNew(CheckEmpBusinessTypeRequestType requestType) throws Exception {
        CheckEmpBusinessTypeResponseType responseType = new CheckEmpBusinessTypeResponseType();

        String empCode = UserUtil.getMappingEmpCode(remoteConfig);
        if("S35097".equals(empCode)) {  //门票ceo 老季
            responseType.setEmpBusinessType("all");
            return responseType;
        }


        //是否是门票的
        CheckUserPermissionRequestType checkUserPermissionRequestType = new CheckUserPermissionRequestType(null,null,null,null);
        boolean isInDepartment41999 = userPermissionBiz.isInDepartment41999(checkUserPermissionRequestType);
        if(!isInDepartment41999){
            responseType.setEmpBusinessType("noPermission");
            return responseType;
        }
        String domainName = checkUserPermissionRequestType.getDomainName();
        BusinessDashboardEmployeeInfo employeeInfo = employeeInfoDao.queryByDomainName(domainName);

        List<BusinessDashboardOrganizationInfo> organizationInfoList = organizationInfoDao.queryByLeaderEmpCode(employeeInfo.getEmpCode());
        List<String> nodeOrgIdList = organizationInfoList.stream().map(BusinessDashboardOrganizationInfo::getNodeOrgId).collect(Collectors.toList());

        String domesticDeptId = remoteConfig.getExternalConfig("domesticDeptId");
        String overseaDeptId = remoteConfig.getExternalConfig("overseaDeptId");
        //说明是国内leader
        if (nodeOrgIdList.contains(domesticDeptId)) {
            responseType.setEmpBusinessType("domestic");
            return responseType;
        }
        //说明是海外leader
        if (nodeOrgIdList.contains(overseaDeptId)) {
            responseType.setEmpBusinessType("oversea");
            return responseType;
        }
        //不属于以上两种情况 则一定是下属
        //不属于门票活动业务部的情况都已经被过滤了
        String orgIdPath = employeeInfo.getOrgIdPath();
        if(orgIdPath.contains(domesticDeptId)){
            responseType.setEmpBusinessType("domestic");
            return responseType;
        }
        if(orgIdPath.contains(overseaDeptId)){
            responseType.setEmpBusinessType("oversea");
            return responseType;
        }

        //如果以上逻辑都不符合，兜底设为无权限
        responseType.setEmpBusinessType("noPermission");
        return responseType;
    }

    @Override
    public GetKeyProjectDashboardConfigResponseType getKeyProjectDashboardConfig(GetKeyProjectDashboardConfigRequestType requestType) {

        //读config获取配置信息
        List<KeyProjectDashboardConfigContentBean> configContentBeanList =
                remoteConfig.getKeyProjectDashboardConfigList();

        log.info("KeyProjectDashboardBizImpl.getKeyProjectDashboardConfig 读config获取配置信息:{}", JSON.toJSONString(configContentBeanList));//NOSONAR

        //将配置信息转换为响应体中的格式
        List<KeyProjectDashboardConfig> keyProjectDashboardConfigList =
                KeyProjectDashboardConfigContentBean.convertToResponseDto(configContentBeanList);

        log.info("KeyProjectDashboardBizImpl.getKeyProjectDashboardConfig 将配置信息转换为响应体中的格式:{}", JSON.toJSONString(keyProjectDashboardConfigList));//NOSONAR

        GetKeyProjectDashboardConfigResponseType responseType = new GetKeyProjectDashboardConfigResponseType();
        responseType.setKeyProjectDashboardConfigList(keyProjectDashboardConfigList);
        return responseType;
    }

    @Override
    public GetKeyProjectDashboardFilterEnumsResponseType getKeyProjectDashboardFilterEnums(GetKeyProjectDashboardFilterEnumsRequestType requestType) throws Exception {
        GetKeyProjectDashboardFilterEnumsResponseType responseType = new GetKeyProjectDashboardFilterEnumsResponseType();
        responseType.setRegionNames(null);
        responseType.setProvinceNames(null);
        responseType.setViewspotMeids(null);
        responseType.setRoleEnums(null);

        String domainName = requestType.getDomainName();
        List<Integer> filterColumns = requestType.getFilterColumns();
        if(CollectionUtils.isEmpty(filterColumns)){
            return responseType;
        }
        String queryD = dataUpdateBiz.getUpdateTime();

        //考核指标（多个逗号分割1;2;3）指标类型 1-GMV 2-毛利 3-质量成本 4-直签 5-6-7- 8-活动覆盖 9-品类覆盖
        BusinessDashboardExamineeConfigV2 metric3Config = getMetricConfig(domainName, queryD, "3");
        BusinessDashboardExamineeConfigV2 metric1n2Config = getMetricConfig(domainName, queryD, "1;2");

        //需要返回的字段 0-大区 1-省份 2-景点经理 3-角色
        if(filterColumns.contains(0)){
            responseType.setRegionNames(getRegionNameListNew(queryD, domainName));
        }
        if(filterColumns.contains(1)){
            responseType.setProvinceNames(getProvinceNameList(queryD, metric3Config));
        }
        if(filterColumns.contains(2)){
            responseType.setViewspotMeids(getViewspotMeidList(queryD, metric1n2Config));
        }
        if(filterColumns.contains(3)){
            responseType.setRoleEnums(getRoleEnumList(queryD, domainName));
        }

        return responseType;
    }


    private BusinessDashboardExamineeConfigV2 getMetricConfig(String domainName, String queryD, String metric) throws SQLException, ParseException {

        String year = DateUtil.getActualYearOfD(queryD);
        String quarter = DateUtil.getActualQuarterOfD(queryD);

        //获取考核配置   按大区/省份/景点考核
        List<BusinessDashboardExamineeConfigV2>  metricAllConfigList
                = examineConfigV2Dao.queryMetricConfig(domainName, queryD, year, quarter, metric);

        return CollectionUtils.isEmpty(metricAllConfigList) ? null : metricAllConfigList.get(0);
    }

    private List<String> getProvinceNameList(String queryD, BusinessDashboardExamineeConfigV2 metric3Config) throws SQLException {
        if(metric3Config == null){
            return null;
        }

        //门票
        String tktExamineLevel = metric3Config.getExamineLevel();
        String tktExamineRange = metric3Config.getExamineRange();
        //活动
        String actExamineLevel = metric3Config.getActExamineLevel();
        String actExamineRange = metric3Config.getActExamineRange();
        //日游
        String odtExamineLevel = metric3Config.getOdtExamineLevel();
        String odtExamineRange = metric3Config.getOdtExamineRange();
        //出境日游
        metric3Config.getOverseaOdtExamineLevel();
        metric3Config.getOverseaOdtExamineRange();

        List<String> tktProvinceNameList = getProvinceNameListByBizLine(queryD, tktExamineLevel, tktExamineRange);
        List<String> actProvinceNameList = getProvinceNameListByBizLine(queryD, actExamineLevel, actExamineRange);
        List<String> odtProvinceNameList = getProvinceNameListByBizLine(queryD, odtExamineLevel, odtExamineRange);
        if(tktProvinceNameList!=null && CollectionUtils.isEmpty(tktProvinceNameList)
                || actProvinceNameList!=null && CollectionUtils.isEmpty(actProvinceNameList)
                || odtProvinceNameList!=null && CollectionUtils.isEmpty(odtProvinceNameList)){
            //返回空list, 代表不筛选该字段, 查询全量
            return new ArrayList<>();
        }

        Set<String> provinceNameSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(tktProvinceNameList)) provinceNameSet.addAll(tktProvinceNameList);
        if (CollectionUtils.isNotEmpty(actProvinceNameList)) provinceNameSet.addAll(actProvinceNameList);
        if (CollectionUtils.isNotEmpty(odtProvinceNameList)) provinceNameSet.addAll(odtProvinceNameList);

        return CollectionUtils.isEmpty(provinceNameSet) ? null : new ArrayList<>(provinceNameSet);
    }






    private List<String> getProvinceNameListByBizLine(String queryD, String examineLevel, String examineRange) throws SQLException {


        List<String> examineRangeList = examineRange==null ?
                Collections.singletonList("") : Arrays.asList(examineRange.split(";"));


        if("国内".equals(examineLevel)) {   //NOSONAR
            return new ArrayList<>();

        } else if("大区".equals(examineLevel)){ //NOSONAR
            List<String> provinceNameList = dimPrdTktScenicInfoCoreMappingDao.getProvinceNameListByRegionName(queryD, examineRangeList);
            return CollectionUtils.isEmpty(provinceNameList) ? null : provinceNameList;

        } else if("省份".equals(examineLevel)){  //NOSONAR
            return CollectionUtils.isEmpty(examineRangeList) ? null : examineRangeList;

        } else {
            return null;

        }
    }






    private List<String> getViewspotMeidList(String queryD, BusinessDashboardExamineeConfigV2 metric1n2Config) throws SQLException {
        if(metric1n2Config == null){
            return null;
        }

        //门票
        String tktExamineLevel = metric1n2Config.getExamineLevel();
        String tktExamineRange = metric1n2Config.getExamineRange();
        //活动
        String actExamineLevel = metric1n2Config.getActExamineLevel();
        String actExamineRange = metric1n2Config.getActExamineRange();
        //日游
        String odtExamineLevel = metric1n2Config.getOdtExamineLevel();
        String odtExamineRange = metric1n2Config.getOdtExamineRange();

        List<String> tktViewspotMeidList = getViewspotMeidListByBizLine(queryD, tktExamineLevel, tktExamineRange);
        List<String> actViewspotMeidList = getViewspotMeidListByBizLine(queryD, actExamineLevel, actExamineRange);
        List<String> odtViewspotMeidList = getViewspotMeidListByBizLine(queryD, odtExamineLevel, odtExamineRange);
        if(tktViewspotMeidList!=null && CollectionUtils.isEmpty(tktViewspotMeidList)
            || actViewspotMeidList!=null && CollectionUtils.isEmpty(actViewspotMeidList)
            || odtViewspotMeidList!=null && CollectionUtils.isEmpty(odtViewspotMeidList)){
            //返回空list, 代表不筛选该字段, 查询全量
            return new ArrayList<>();
        }


        Set<String> provinceNameSet = new HashSet<>();
        if (tktViewspotMeidList != null) provinceNameSet.addAll(tktViewspotMeidList);
        if (actViewspotMeidList != null) provinceNameSet.addAll(actViewspotMeidList);
        if (odtViewspotMeidList != null) provinceNameSet.addAll(odtViewspotMeidList);

        return CollectionUtils.isEmpty(provinceNameSet) ? null : new ArrayList<>(provinceNameSet);
    }


    private List<String> getViewspotMeidListByBizLine(String queryD, String examineLevel, String examineRange) throws SQLException {

        List<String> examineRangeList = examineRange==null ?
                Collections.singletonList("") : Arrays.asList(examineRange.split(";"));

        if("国内".equals(examineLevel)) {  //NOSONAR
            return new ArrayList<>();

        } else if("大区".equals(examineLevel)){   //NOSONAR
            List<String> viewSpotMeidList = dimPrdTktScenicInfoCoreMappingDao.getViewSpotMeidByRegionName(queryD, examineRangeList);
            return CollectionUtils.isEmpty(viewSpotMeidList) ? null : viewSpotMeidList;

        } else if("省份".equals(examineLevel)) {  //NOSONAR
            List<String> viewSpotMeidList = dimPrdTktScenicInfoCoreMappingDao.getViewSpotMeidByProvinceName(queryD, examineRangeList);
            return CollectionUtils.isEmpty(viewSpotMeidList) ? null : viewSpotMeidList;

        } else if("景点".equals(examineLevel)){  //NOSONAR
            return CollectionUtils.isEmpty(examineRangeList) ? null : examineRangeList;

        } else {
            return null;

        }
    }

    private List<String> getRegionNameListNew(String queryD, String domainName) throws SQLException, ParseException {

        if("zheli".equals(domainName)){
            return new ArrayList<>();
        }

        MPkgVendorQualityDbBoardEmployee mPkgVendorQualityDbBoardEmployee = mPkgVendorQualityDbBoardEmployeeDao.getBoardEmployeeByDomainName(domainName);
        if(mPkgVendorQualityDbBoardEmployee != null && StringUtils.isNotBlank(mPkgVendorQualityDbBoardEmployee.getRegion())){
            return Collections.singletonList(mPkgVendorQualityDbBoardEmployee.getRegion());

        }else {

            BusinessDashboardEmployeeInfo employeeInfo = employeeInfoDao.queryByDomainName(domainName);
            if(employeeInfo != null && StringUtils.isNotBlank(employeeInfo.getEmpCode())){
                List<BenchRegion> benchRegionList = configFrom100032373.getBenchRegionManagerSettings();

                List<String> regionNameList = benchRegionList.stream()
                        .filter(benchRegion -> employeeInfo.getEmpCode().equals(benchRegion.getRegionmanagers())
                                && CollectionUtils.isNotEmpty(benchRegion.getOrgnames()))
                        .flatMap(benchRegion -> benchRegion.getOrgnames().stream())
                        .collect(Collectors.toList());

                return CollectionUtils.isEmpty(regionNameList) ? null : regionNameList;
            }
        }

        return null;
    }


    private static final Map<Integer,String> roleNamaMap = new HashMap<>();
    static {
        //1一线运营/2一线BD/3省长/4省运营/5大区总/6大区运营/7中心人员
        roleNamaMap.put(1,"一线运营");   //NOSONAR
        roleNamaMap.put(2,"一线BD");    //NOSONAR
        roleNamaMap.put(3,"省长");      //NOSONAR
        roleNamaMap.put(4,"区域经理");   //NOSONAR
        roleNamaMap.put(5,"大区总");     //NOSONAR
        roleNamaMap.put(6,"大区运营");   //NOSONAR
        roleNamaMap.put(7,"中心人员");   //NOSONAR
    }
    private List<String> getRoleEnumList(String queryD, String domainName) throws ParseException, SQLException {
        String year = DateUtil.getActualYearOfD(queryD);
        String quarter = DateUtil.getActualQuarterOfD(queryD);
        List<Integer> roles = examineConfigV2Dao.getRolesByDomainName(domainName,queryD,year,quarter);

        //role字段取出来的是id，需转换成对应中文
        List<String> roleNameList = new ArrayList<>();
        for(Integer role : roles){
            if(roleNamaMap.containsKey(role)){
                roleNameList.add(roleNamaMap.get(role));
            }
        }
        if(CollectionUtils.isEmpty(roleNameList)){
            return null;   //无数据返回null
        }

        return roleNameList;
    }


}
