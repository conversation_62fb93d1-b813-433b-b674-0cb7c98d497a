package com.ctrip.tour.business.dashboard.sightArchives.dao.salesDao;


import com.ctrip.soa._24922.MarketingCampaign;
import com.ctrip.soa._27181.PreparedParameterBean;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.TktStarRocksDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.sql.Types;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class CdmLogTtdViewspotBenchActivityIndexDfDao {

    //营销活动表 数仓侧设计文档：http://conf.ctripcorp.com/pages/viewpage.action?pageId=3682998994
    //dw_ticketdb.cdm_log_ttd_viewspot_bench_activity_index_df

    @Autowired
    private TktStarRocksDao tktStarRocksDao;


    public List<MarketingCampaign> queryMarketingCampaignList(String queryD, Long sightId, Boolean needSubSight, String startDate, String endDate) {
        StringBuilder sql = new StringBuilder("select " +
                "main_activity_id" + //主活动id
                ",main_activity_name" +  //主活动名称
                ",main_activity_start_date" +  //主活动开始时间
                ",main_activity_end_date" +  //主活动结束时间
                ",cast(main_activity_expo_uv as Integer) as main_activity_expo_uv" + //主活动曝光uv
                ",sub_activity_id" +   //子活动id
                ",sub_activity_name" +   //子活动名称
                ",cast(sum(sub_activity_order_cnt) as Integer) as sub_activity_order_cnt" +  //子活动订单数
                ",cast(sum(sub_activity_gmv) as Integer) as sub_activity_gmv"  //子活动gmv
        );
        sql.append(" from cdm_log_ttd_viewspot_bench_activity_index_df ");
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendSightId(parameters, sql, sightId, needSubSight);
        appendQueryD(parameters, sql, queryD);
        appendDateRange(parameters, sql, startDate, endDate);
        sql.append(" group by main_activity_id,main_activity_name,main_activity_start_date,main_activity_end_date,main_activity_expo_uv,sub_activity_id,sub_activity_name");
        sql.append(" order by main_activity_start_date desc");

        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(),parameters);
        } catch (SQLException e) {
            log.warn("querySalesMetricCard error", e);
        }

        List<MarketingCampaign> marketingCampaignList = new ArrayList<>();

        if(CollectionUtils.isEmpty(result)){
            return marketingCampaignList;
        }
        for(Map<String, Object> map : result){
            MarketingCampaign marketingCampaign = new MarketingCampaign();
            marketingCampaign.setMarketingCampaignName((String) map.getOrDefault("main_activity_name", ""));
            marketingCampaign.setGmv((Integer) map.getOrDefault("sub_activity_gmv", 0L));
            marketingCampaign.setUv((Integer) map.getOrDefault("main_activity_expo_uv", 0));
            marketingCampaign.setOrderCount((Integer) map.getOrDefault("sub_activity_order_cnt", 0));
            marketingCampaign.setName((String) map.getOrDefault("sub_activity_name", ""));
            marketingCampaign.setDateRange(map.getOrDefault("main_activity_start_date", "") + " - " + (String) map.getOrDefault("main_activity_end_date", ""));
            marketingCampaignList.add(marketingCampaign);
        }

        return marketingCampaignList;
    }

    private void appendSightId(List<PreparedParameterBean> parameters, StringBuilder sql, Long sightId, Boolean needSubSight){
        if(needSubSight){
            sql.append(" where viewspot_id = ?");
        }else {
            sql.append(" where sub_viewspot_id = ?");
        }
        parameters.add(new PreparedParameterBean(String.valueOf(sightId), Types.BIGINT));
    }

    private void appendQueryD(List<PreparedParameterBean> parameters, StringBuilder sql, String queryD){
        sql.append(" and d = ?");
        parameters.add(new PreparedParameterBean(queryD, Types.VARCHAR));
    }

    private void appendDateRange(List<PreparedParameterBean> parameters, StringBuilder sql, String startDate, String endDate){

        sql.append(" and (main_activity_start_date <= ? and main_activity_end_date >= ?) ");
        parameters.add(new PreparedParameterBean(endDate, Types.VARCHAR));
        parameters.add(new PreparedParameterBean(startDate, Types.VARCHAR));

    }
}
