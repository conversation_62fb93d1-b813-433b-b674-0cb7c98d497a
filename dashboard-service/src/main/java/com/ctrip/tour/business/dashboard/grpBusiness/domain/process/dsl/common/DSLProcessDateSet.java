package com.ctrip.tour.business.dashboard.grpBusiness.domain.process.dsl.common;

import com.ctrip.soa._24922.DSLRequestType;
import com.ctrip.soa._24922.EnumOperators;
import com.ctrip.soa._24922.WhereCondition;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.AbstractPreDSLProcess;

import java.util.ArrayList;
import java.util.Arrays;

public class DSLProcessDateSet extends AbstractPreDSLProcess {
    private String startDate;
    private String endDate;

    public static AbstractPreDSLProcess getInstance(String startDate, String endDate) {
        DSLProcessDateSet dslProcessLimitSet = new DSLProcessDateSet();
        dslProcessLimitSet.startDate = startDate;
        dslProcessLimitSet.endDate = endDate;
        return dslProcessLimitSet;
    }

    @Override
    public DSLRequestType process(DSLRequestType dslRequestType, EarlyReturn earlyReturn) {
        // 初始化
        if (dslRequestType.getWhereCondition() == null) {
            dslRequestType.setWhereCondition(new WhereCondition());
        }
        if (dslRequestType.getWhereCondition().getSubWhereConditions() == null) {
            dslRequestType.getWhereCondition().setSubWhereConditions(new ArrayList<>());
        }

        // 日期
        WhereCondition whereCondition = new WhereCondition();
        dslRequestType.getWhereCondition().getSubWhereConditions().add(whereCondition);

        whereCondition.setFilterName("date");
        whereCondition.setOperators(EnumOperators.IN);
        // 校验时间
        whereCondition.setFilterValues(Arrays.asList(startDate, endDate));
        return dslRequestType;
    }
}
