package com.ctrip.tour.business.dashboard.tktBusiness.strategy;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ExamineConfigBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBean;
import org.springframework.scheduling.annotation.Async;

import java.util.List;
import java.util.concurrent.Future;

public interface QuantitySubMetricCalStrategy {


    //获取质量成本子指标指标卡数据
    @Async("subMetricCardExecutor")
    Future<MetricDetailInfo> getBus103SubMetricCardData(TimeFilter timeFilter,
                                                        OverseaMetricInfoBean metricInfoBean,
                                                        String d,
                                                        String metric,
                                                        String subMetric) throws Exception;


    //获取质量成本子指标趋势线数据
    GetOverseaTrendLineDataResponseType getBus103SubTrendLineData(GetOverseaTrendLineDataRequestType request,
                                                                  String d,
                                                                  List<ExamineConfigBean> examineConfigBeanList) throws Exception;



    //获取质量成本子指标下钻基础数据
    GetOverseaDrillDownBaseInfoResponseType getBus103SubDrillDownBaseInfo(GetOverseaDrillDownBaseInfoRequestType request,
                                                                          String d,
                                                                          OverseaMetricInfoBean metricInfoBean) throws Exception;


    //获取质量成本子指标下钻数据
    GetOverseaTableDataResponseType getBus103SubTableData(GetOverseaTableDataRequestType request,
                                                          String d,
                                                          OverseaMetricInfoBean metricInfoBean) throws Exception;


    //获取子指标的名称
    String getSubMetricName();


}
