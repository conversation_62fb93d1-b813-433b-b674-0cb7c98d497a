package com.ctrip.tour.business.dashboard.tktExternal;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

import com.alibaba.fastjson.TypeReference;
import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.sightArchives.service.FlowService;
import com.ctrip.tour.business.dashboard.sightArchives.service.MarketService;
import com.ctrip.tour.business.dashboard.utils.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ctrip.tour.business.dashboard.sightArchives.service.QualityService;

/**
 * <AUTHOR>
 * @Date 2025/4/24
 */
@Slf4j
@Service
public class SrvQualityDimAssembleService {

    @Autowired
    private QualityService qualityService;
    @Autowired
    private FlowService flowService;
    @Autowired
    private MarketService marketService;


    public GetServiceQualityDimDetailInfoResponseType getSrvQualityDimInfo(GetServiceQualityDimDetailInfoRequestType requestType) throws ExecutionException, InterruptedException {
        GetServiceQualityDimDetailInfoResponseType responseType = new GetServiceQualityDimDetailInfoResponseType();
        SightArchivesCommonFilter commonFilter = new SightArchivesCommonFilter();
        if (requestType.getBusinessType() != null) {
            commonFilter.setBusinessType(requestType.getBusinessType());
        } else {
            commonFilter.setBusinessType(1);
        }

        if (requestType.isNeedSubSight() != null) {
            commonFilter.setNeedSubSight(requestType.isNeedSubSight());
        } else {
            commonFilter.setNeedSubSight(false);
        }

        if (requestType.getDateType() != null) {
            commonFilter.setDateType(requestType.getDateType());
        } else {
            commonFilter.setDateType(1);
        }

        if (CollectionUtils.isNotEmpty(requestType.getVendorIdList())){
            commonFilter.setVendorIdList(requestType.getVendorIdList());
        }else {
            commonFilter.setVendorIdList(Lists.newArrayList());
        }

        commonFilter.setStartDate(requestType.getStartDate());
        commonFilter.setEndDate(requestType.getEndDate());
        commonFilter.setSightId(requestType.getSightId());

        CompletableFuture<GetFulfillmentQualityMetricResponseType> fulfillmentQualityMetricResponseTypeCompletableFuture = CompletableFuture.supplyAsync(() -> {
            GetFulfillmentQualityMetricRequestType fulfillmentQualityMetricRequestType = new GetFulfillmentQualityMetricRequestType();
            fulfillmentQualityMetricRequestType.setCommonFilter(commonFilter);
            return qualityService.getFulfillmentQualityMetric(fulfillmentQualityMetricRequestType);
        });
        CompletableFuture<GetCommentMetricResponseType> commentMetricResponseTypeCompletableFuture = CompletableFuture.supplyAsync(() -> {
            GetCommentMetricRequestType commentMetricRequestType = new GetCommentMetricRequestType();
            commentMetricRequestType.setCommonFilter(commonFilter);
            return qualityService.getCommentMetric(commentMetricRequestType);
        });
        CompletableFuture<GetServiceMetricResponseType> getServiceMetricResponseTypeCompletableFuture = CompletableFuture.supplyAsync(() -> {
            GetServiceMetricRequestType getServiceMetricRequestType = new GetServiceMetricRequestType();
            getServiceMetricRequestType.setCommonFilter(commonFilter);
            return qualityService.getServiceMetric(getServiceMetricRequestType);
        });
        CompletableFuture<GetComplaintMetricResponseType> getComplaintMetricResponseTypeCompletableFuture = CompletableFuture.supplyAsync(() -> {
            GetComplaintMetricRequestType getComplaintMetricRequestType = new GetComplaintMetricRequestType();
            getComplaintMetricRequestType.setCommonFilter(commonFilter);
            return qualityService.getComplaintMetric(getComplaintMetricRequestType);
        });
        CompletableFuture<GetFlowMetricTrendLineResponseType>  seasonalDemandTrends = CompletableFuture.supplyAsync(
                () -> flowService.getFlowMetricTrendLine(new GetFlowMetricTrendLineRequestType(commonFilter))
        );
        CompletableFuture<GetLocationHeatForecastResponseType>  marketTrends = CompletableFuture.supplyAsync(
                () -> marketService.getLocationHeatForecast(new GetLocationHeatForecastRequestType(commonFilter))
        );

        CompletableFuture.allOf(fulfillmentQualityMetricResponseTypeCompletableFuture,
                commentMetricResponseTypeCompletableFuture,
                getServiceMetricResponseTypeCompletableFuture,
                getComplaintMetricResponseTypeCompletableFuture,
                seasonalDemandTrends,
                marketTrends).join();

        GetFulfillmentQualityMetricResponseType fulfillmentQualityMetricResponseType = fulfillmentQualityMetricResponseTypeCompletableFuture.get();
        GetCommentMetricResponseType commentMetricResponseType = commentMetricResponseTypeCompletableFuture.get();
        GetServiceMetricResponseType getServiceMetricResponseType = getServiceMetricResponseTypeCompletableFuture.get();
        GetComplaintMetricResponseType getComplaintMetricResponseType = getComplaintMetricResponseTypeCompletableFuture.get();
        GetFlowMetricTrendLineResponseType getFlowMetricTrendLineResponseType = seasonalDemandTrends.get();
        GetLocationHeatForecastResponseType getLocationHeatForecastResponseType = marketTrends.get();

        if (Objects.nonNull(fulfillmentQualityMetricResponseType)) {
            KeepAgreementDetailInfo keepAgreementDetailInfo = new KeepAgreementDetailInfo();
            Double weightedDefectCount = fulfillmentQualityMetricResponseType.getWeightedDefectCount();
            Double weightedDefectRate = fulfillmentQualityMetricResponseType.getWeightedDefectRate();
            List<FulfillmentQualityPieChartSegment> pieChart = fulfillmentQualityMetricResponseType.getPieChart();

            List<AgreementDefectReasonInfo> agreementDefectReasonInfos = Optional.ofNullable(pieChart)
                    .orElse(Lists.newArrayList())
                    .stream()
                    .map(segment -> {
                        AgreementDefectReasonInfo agreementDefectReasonInfo = new AgreementDefectReasonInfo();
                        agreementDefectReasonInfo.setKeyWord(segment.getName());
                        if (segment.getValue() != null) {
                            agreementDefectReasonInfo.setValue(segment.getValue());
                        }
                        return agreementDefectReasonInfo;
                    }).collect(Collectors.toList());
            if (Objects.nonNull(weightedDefectCount)) {
                keepAgreementDetailInfo.setFailedOrderWeightedCount(weightedDefectCount);
            }
            keepAgreementDetailInfo.setFailedOrderWeightedRate(weightedDefectRate);
            keepAgreementDetailInfo.setAgreementDefectReasonList(agreementDefectReasonInfos);
            responseType.setKeepAgreementDetailInfo(keepAgreementDetailInfo);
        }

        if (Objects.nonNull(commentMetricResponseType)) {
            CurrentCommentScore currentCommentScore = commentMetricResponseType.getCurrentCommentScore();
            List<CommentKeyword> negativeCommentList = commentMetricResponseType.getNegativeCommentList();
            List<CommentKeyword> positiveCommentList = commentMetricResponseType.getPositiveCommentList();

            ReviewDetailInfo reviewDetailInfo = new ReviewDetailInfo();
            if (Objects.nonNull(currentCommentScore)) {
                reviewDetailInfo.setAvgReviewScore(currentCommentScore.getCommentScore());
                if (Objects.nonNull(currentCommentScore.getCommentCount())) {
                    reviewDetailInfo.setReviewNum(currentCommentScore.getCommentCount().longValue());
                }

                List<RelReviewKeyWordInfo> reviewKeyWordInfos = Optional.ofNullable(currentCommentScore.getDetailCommentScore())
                        .orElse(Lists.newArrayList())
                        .stream()
                        .map(detailCommentScore -> {
                            RelReviewKeyWordInfo reviewKeyWordInfo = new RelReviewKeyWordInfo();
                            reviewKeyWordInfo.setKeyWord(detailCommentScore.getName());

                            reviewKeyWordInfo.setValue(detailCommentScore.getScore());

                            return reviewKeyWordInfo;
                        }).collect(Collectors.toList());
                reviewDetailInfo.setReviewKeyWordList(reviewKeyWordInfos);

            }

            if (CollectionUtils.isNotEmpty(negativeCommentList)) {
                List<ReviewKeyWordInfo> dislikeCommentList = negativeCommentList.stream()
                        .map(commentKeyword -> {
                            ReviewKeyWordInfo reviewKeyWordInfo = new ReviewKeyWordInfo();
                            reviewKeyWordInfo.setKeyWord(commentKeyword.getKeyword());
                            if (commentKeyword.getCount() != null) {
                                reviewKeyWordInfo.setValue(commentKeyword.getCount().longValue());
                            }
                            return reviewKeyWordInfo;
                        }).collect(Collectors.toList());
                reviewDetailInfo.setReviewKeyWordWithDislikeList(dislikeCommentList);
            }

            if (CollectionUtils.isNotEmpty(positiveCommentList)) {
                List<ReviewKeyWordInfo> likeCommentList = positiveCommentList.stream()
                        .map(commentKeyword -> {
                            ReviewKeyWordInfo reviewKeyWordInfo = new ReviewKeyWordInfo();
                            reviewKeyWordInfo.setKeyWord(commentKeyword.getKeyword());
                            if (commentKeyword.getCount() != null) {
                                reviewKeyWordInfo.setValue(commentKeyword.getCount().longValue());
                            }
                            return reviewKeyWordInfo;
                        }).collect(Collectors.toList());
                reviewDetailInfo.setReviewKeyWordWithLikeList(likeCommentList);
            }
            responseType.setReviewDetailInfo(reviewDetailInfo);
        }


        if (Objects.nonNull(getServiceMetricResponseType)) {

            ServiceDetailInfo serviceDetailInfo = new ServiceDetailInfo();

            Integer preSaleConsultCount = getServiceMetricResponseType.getPreSaleConsultCount();
            Integer postSaleConsultCount = getServiceMetricResponseType.getPostSaleConsultCount();
            Integer telServiceMinutes = getServiceMetricResponseType.getTelServiceMinutes();
            Integer serviceValueAmount = getServiceMetricResponseType.getServiceValueAmount();

            if (Objects.nonNull(preSaleConsultCount)) {
                serviceDetailInfo.setBookingBeforeIMServiceNum(preSaleConsultCount.longValue());
            }
            if (Objects.nonNull(postSaleConsultCount)) {
                serviceDetailInfo.setBookingAfterIMServiceNum(postSaleConsultCount.longValue());
            }
            if (Objects.nonNull(telServiceMinutes)) {
                serviceDetailInfo.setPhoneServiceMinute(preSaleConsultCount.longValue());
            }
            if (Objects.nonNull(serviceValueAmount)) {
                serviceDetailInfo.setCostPrice(preSaleConsultCount.doubleValue());
            }
            responseType.setServiceDetail(serviceDetailInfo);
        }

        if (Objects.nonNull(getComplaintMetricResponseType)) {
            ComplainDetail complainDetail = new ComplainDetail();
            Integer complaintCount = getComplaintMetricResponseType.getComplaintCount();
            List<ComplaintQuestion> complaintQuestionList = getComplaintMetricResponseType.getComplaintQuestionList();
            if (Objects.nonNull(complaintCount)) {
                complainDetail.setComplainNumber(complaintCount.longValue());
            }
            if (CollectionUtils.isNotEmpty(complaintQuestionList)) {
                List<ComplainDetailInfo> complainDetailInfos = complaintQuestionList.stream().map(complaintQuestion -> {
                    ComplainDetailInfo complainDetailInfo = new ComplainDetailInfo();
                    complainDetailInfo.setKeyWord(complaintQuestion.getQuestion());
                    if (Objects.nonNull(complaintQuestion.getComplaintCount())) {
                        complainDetailInfo.setValue(complaintQuestion.getComplaintCount().longValue());
                    }
                    return complainDetailInfo;
                }).collect(Collectors.toList());
                complainDetail.setComplainDetailList(complainDetailInfos);
            }
            responseType.setComplainDetail(complainDetail);
        }

       if (Objects.nonNull(getFlowMetricTrendLineResponseType)) {
           SeasonalDemandTrendsForServiceQualityDimDetailInfo tempResult = ObjectUtil.deepJsonCopy(getFlowMetricTrendLineResponseType, SeasonalDemandTrendsForServiceQualityDimDetailInfo.class);
           responseType.setSeasonalDemandTrends(tempResult);
       }

       if (Objects.nonNull(getLocationHeatForecastResponseType)) {
           List<LocationHeatForecastTrendLineItemForServiceQualityDimDetailInfo> tempResult = ObjectUtil.deepJsonCopy(getLocationHeatForecastResponseType.getHeatForecastTrendLine(), new TypeReference<List<LocationHeatForecastTrendLineItemForServiceQualityDimDetailInfo>>(){});
           responseType.setMarketTrends(tempResult);
       }
        return responseType;

    }
}
