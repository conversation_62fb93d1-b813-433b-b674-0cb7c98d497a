package com.ctrip.tour.business.dashboard.sightArchives.service.Impl;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.sightArchives.dao.flowDao.CdmLogTtdViewspotBenchTrafficDiDao;
import com.ctrip.tour.business.dashboard.sightArchives.dao.flowDao.CdmLogTtdViewspotBenchTrafficFromDiDao;
import com.ctrip.tour.business.dashboard.sightArchives.service.FlowService;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.ScenicLanguageHelper;
import com.ctrip.tour.business.dashboard.utils.UserUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class FlowServiceImpl implements FlowService {
    //数仓侧设计文档: http://conf.ctripcorp.com/pages/viewpage.action?pageId=3682998994
    @Autowired
    CdmLogTtdViewspotBenchTrafficDiDao cdmLogTtdViewspotBenchTrafficDiDao;
    @Autowired
    CdmLogTtdViewspotBenchTrafficFromDiDao cdmLogTtdViewspotBenchTrafficFromDiDao;
    @Autowired
    private RemoteConfig config;

    @Override
    public GetFlowMetricResponseType getFlowMetric(GetFlowMetricRequestType requestType) {
        SightArchivesCommonFilter commonFilter = requestType.getCommonFilter();
        Long sightId = commonFilter.getSightId();
        Boolean needSubSight = commonFilter.isNeedSubSight();
        String startDate = commonFilter.getStartDate();
        String endDate = commonFilter.getEndDate();
        Integer dateType = commonFilter.getDateType();
        Integer businessType = commonFilter.getBusinessType();
        List<Long> vendorIdList = commonFilter.getVendorIdList();

        //流量转化                     漏斗
        //数据源表: dw_ticketdb.cdm_log_ttd_viewspot_bench_traffic_di
        FlowConversionFunnel flowConversionFunnel =  cdmLogTtdViewspotBenchTrafficDiDao.queryFlowConversionFunnel(sightId,startDate,endDate,needSubSight,dateType,businessType,vendorIdList);

        //流量来源分布                  饼图
        //数据源表: dw_ticketdb.cdm_log_ttd_viewspot_bench_traffic_from_di
        List<FlowSource> res = new ArrayList<>();
        List<Map<String,Object>> piechart = cdmLogTtdViewspotBenchTrafficFromDiDao.queryFlowSourceList(sightId,startDate,endDate,needSubSight,dateType,businessType,vendorIdList);//流量来源分布
        //总uv
       Integer UvSum = cdmLogTtdViewspotBenchTrafficFromDiDao.queryFlowSourceCount(sightId,startDate,endDate,needSubSight,dateType,businessType,vendorIdList);
       //去年总uv
        Integer lastYearUvSum = cdmLogTtdViewspotBenchTrafficFromDiDao.queryFlowSourceCountWithLastYear(sightId,startDate,endDate,needSubSight,dateType,businessType,vendorIdList);
        //去年uv统计
        List<Map<String,Object>> lypiechart = cdmLogTtdViewspotBenchTrafficFromDiDao.queryFlowSourceListWithLastYear(sightId,startDate,endDate,needSubSight,dateType,businessType,vendorIdList);//去年uv统计

        List<Map<String, Object>> maps = mergeLists(piechart, lypiechart);

        Double TotalRate = 0.0;
        Double TotalRateOfLastYear = 0.0;
        for(Map<String,Object> map : maps){
            FlowSource flowSource = new FlowSource();
            flowSource.setChannelName((String)map.get("channelName"));
            if(UvSum == 0){
                flowSource.setFlowSourcePercentage(0.0);
            }else{
                flowSource.setFlowSourcePercentage((Long)map.get("current_uv")*1.0/UvSum);
                TotalRate += (Long)map.get("current_uv")*1.0/UvSum;
            }
            if(map.get("last_year_uv")==null || (Long)map.get("last_year_uv")==0){
                flowSource.setYoyValue(0.0);
            }else{
                flowSource.setYoyValue(((Long)map.get("current_uv")-(Long) map.get("last_year_uv"))*1.0/(Long)map.get("last_year_uv"));//done 同比值
                TotalRateOfLastYear+=(Long)map.get("last_year_uv")*1.0/lastYearUvSum;

            }
            res.add(flowSource);
            if(TotalRate >= 0.98){
                break;
            }
        }
        if(TotalRate < 1){
            FlowSource flowSource = new FlowSource();
            flowSource.setChannelName("其它");  //NOSONAR
            flowSource.setFlowSourcePercentage(1-TotalRate);
            if(Double.compare(TotalRateOfLastYear,1.0)!=0){
                flowSource.setYoyValue(((1-TotalRate)-(1-TotalRateOfLastYear))/(1-TotalRateOfLastYear));
            }
            res.add(flowSource);
        }
        GetFlowMetricResponseType responseType = new GetFlowMetricResponseType();
        responseType.setFlowConversionFunnel(flowConversionFunnel);
        responseType.setFlowSourceList(res);
        if ("T".equals(config.getConfigValue("languageSwitch"))) {
            setMultiLanguage(responseType);
        }
        return responseType;
    }

    private void setMultiLanguage(GetFlowMetricResponseType responseType) {
        List<FlowSource> flowSourceList = responseType.getFlowSourceList();
        if (CollectionUtils.isNotEmpty(flowSourceList)) {
            for (FlowSource flowSource : flowSourceList) {
                if (StringUtils.isNotBlank(flowSource.getChannelName())) {
                    flowSource.setChannelName(ScenicLanguageHelper.getMultiLanguage(flowSource.getChannelName(), UserUtil.getVbkLocaleForScenic()));
                }
            }
        }
    }

    public static List<Map<String, Object>> mergeLists(List<Map<String, Object>> currentYearList, List<Map<String, Object>> lastYearList) {
        List<Map<String, Object>> mergedList = new ArrayList<>();

        for (Map<String, Object> currentYearMap : currentYearList) {
            String currentchannelName = (String) currentYearMap.get("channelName");
            Map<String, Object> mergedMap = new HashMap<>();
            mergedMap.put("channelName", currentchannelName);
            mergedMap.put("current_uv",currentYearMap.get("uv"));
            for (Map<String, Object> lastYearMap : lastYearList) {
                String lastYearchannelName = (String) lastYearMap.get("channelName");

                if (currentchannelName.equals(lastYearchannelName)) {
                    // 复制当前年份的指标
                    mergedMap.put("last_year_uv",lastYearMap.get("uv"));
                    break;
                }
            }
            mergedList.add(mergedMap);
        }

        return mergedList;
    }

    @Override
    public GetFlowMetricTrendLineResponseType getFlowMetricTrendLine(GetFlowMetricTrendLineRequestType requestType) {
        SightArchivesCommonFilter commonFilter = requestType.getCommonFilter();
        Long sightId = commonFilter.getSightId();
        Boolean needSubSight = commonFilter.isNeedSubSight();
        String startDate = commonFilter.getStartDate();
        String endDate = commonFilter.getEndDate();


        //趋势线
        //数据源表: dw_ticketdb.cdm_log_ttd_viewspot_bench_traffic_di
        List<Integer> peakMonthList = new ArrayList<>();
        List<Integer> offPeakMonthList = new ArrayList<>();
        List<Map<String,Object>> result = cdmLogTtdViewspotBenchTrafficDiDao.queryFlowMetricTrendLine(sightId,startDate,endDate,needSubSight,peakMonthList,offPeakMonthList);

        List<SightArchivesFlowMetricTrendLineItem> dateList = new ArrayList<>();
        // 调用方法获取最高和最低的三个日期
        List<String> highestDtlUvDates = getHighestDtlUvDates(result, 3);
        List<String> lowestDtlUvDates = getLowestDtlUvDates(result, 3);
        // 将日期字符串列表转换为月份整数列表
        List<Integer> high = convertDatesToMonths(highestDtlUvDates);//趋势线中最高的三个月份
        List<Integer> low = convertDatesToMonths(lowestDtlUvDates);//趋势线中最低的三个月份
        for (Map<String, Object> map : result) {
            SightArchivesFlowMetricTrendLineItem item = new SightArchivesFlowMetricTrendLineItem();
            item.setDate((String) map.get("date_time"));
            item.setUv((Integer) map.get("dtl_uv"));
            item.setConversionRate((Double) map.get("conversionRate"));
            dateList.add(item);
        }

        dateList.sort(Comparator.comparing(SightArchivesFlowMetricTrendLineItem::getDate));

        GetFlowMetricTrendLineResponseType responseType = new GetFlowMetricTrendLineResponseType();
        responseType.setDateList(dateList);
        responseType.setPeakMonthList(high);
        responseType.setOffPeakMonthList(low);
        return responseType;
    }

    /**
     * 获取 dtl_uv 值最高的 n 个日期
     * @param dataList 数据集
     * @param n 要获取的日期数量
     * @return 包含最高 dtl_uv 值的日期列表
     */
    public static List<String> getHighestDtlUvDates(List<Map<String, Object>> dataList, int n) {
        // 根据 dtl_uv 值进行降序排序
        dataList.sort((map1, map2) -> {
            int uv1 = (int) map1.get("dtl_uv");
            int uv2 = (int) map2.get("dtl_uv");
            return Integer.compare(uv2, uv1);
        });

        List<String> highestDates = new ArrayList<>();
        for (int i = 0; i < Math.min(n, dataList.size()); i++) {
            highestDates.add((String) dataList.get(i).get("date_time"));
        }

        Collections.sort(highestDates);

        return highestDates;
    }

    /**
     * 获取 dtl_uv 值最低的 n 个日期
     * @param dataList 数据集
     * @param n 要获取的日期数量
     * @return 包含最低 dtl_uv 值的日期列表
     */
    public static List<String> getLowestDtlUvDates(List<Map<String, Object>> dataList, int n) {
        // 根据 dtl_uv 值进行升序排序
        dataList.sort((map1, map2) -> {
            int uv1 = (int) map1.get("dtl_uv");
            int uv2 = (int) map2.get("dtl_uv");
            return Integer.compare(uv1, uv2);
        });

        List<String> lowestDates = new ArrayList<>();
        for (int i = 0; i < Math.min(n, dataList.size()); i++) {
            lowestDates.add((String) dataList.get(i).get("date_time"));
        }

        Collections.sort(lowestDates);

        return lowestDates;
    }

    /**
     * 将日期字符串列表转换为月份整数列表
     * @param dates 日期字符串列表
     * @return 月份整数列表
     */
    public static List<Integer> convertDatesToMonths(List<String> dates) {
        List<Integer> months = new ArrayList<>();
        for (String date : dates) {
            // 截取日期字符串中的月份部分并转换为整数
            String monthStr = date.substring(4, 6);
            months.add(Integer.parseInt(monthStr));
        }
        Collections.sort(months);  //按月份升序排序
        return months;
    }
}
