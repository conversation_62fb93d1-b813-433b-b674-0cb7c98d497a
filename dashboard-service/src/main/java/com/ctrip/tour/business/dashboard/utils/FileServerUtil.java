package com.ctrip.tour.business.dashboard.utils;

import com.ctrip.framework.foundation.Foundation;
import com.ctrip.framework.triplog.shaded.client.tag.TagMarker;
import com.ctrip.framework.triplog.shaded.client.tag.TagMarkerBuilder;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.UploadResult;
import com.ctrip.tour.business.dashboard.tktBusiness.enums.FileTypeEnum;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;


/**
 * 文件服务：https://docs.fx.ctripcorp.com/docs/nephele/how-to/file/api/
 */


public final class FileServerUtil {
    private static final Logger logger = LoggerFactory.getLogger(FileServerUtil.class);
    private static TagMarker marker = TagMarkerBuilder.newBuilder().add("tag", "FileServerUtil").build();


    public static UploadResult upload(byte[] data, FileTypeEnum type, String channel, String host, String fileName) throws NoSuchAlgorithmException {
        String url = String.format("http://%s/file/v1/api/upload?channel=%s&token=%s", host, channel, token(host));
        if(StringUtils.isNotBlank(fileName)){
            String encodedFileName = Base64.getUrlEncoder().encodeToString(fileName.getBytes());
            url = url + "&filename=" + encodedFileName;
        }

        HttpPost request = new HttpPost(url);
        request.addHeader("Content-Type", type.getContentType());
        request.addHeader("Crc", getCrc(data));
        request.setEntity(new ByteArrayEntity(data));

        try (CloseableHttpClient client = HttpClients.createDefault();
             CloseableHttpResponse response = client.execute(request)) {
            if (HttpStatus.SC_OK != response.getStatusLine().getStatusCode()) {
                logger.warn(marker, "[[title=FileServerUtil]]upload file failed, statuscode={}", response.getStatusLine().getStatusCode());
                return null;
            }

            return MapperUtil.str2Obj(EntityUtils.toString(response.getEntity(), "utf-8"), UploadResult.class);
        } catch (Exception ex) {
            logger.warn(marker, "[[title=FileServerUtil]]upload file failed", ex);
            return null;
        }
    }

    /**
     * 获取token
     * @return
     */
    private static String token(String host) {
        String url = String.format("http://%s/file/v1/api/gettoken?clientid=%s&ts=%s", host, Foundation.app().getAppId(), System.currentTimeMillis());
        return DownloadUtil.get(url);
    }

    public static String getCrc(byte[] fileBytes) throws NoSuchAlgorithmException {
        byte[] tmp;
        int size5M = 5 * 1024 * 1024;
        int size10M = 10 * 1024 * 1024;
        if (fileBytes.length > size10M) {
            tmp = new byte[size10M];
            System.arraycopy(fileBytes, 0, tmp, 0, size5M);
            System.arraycopy(fileBytes, fileBytes.length - size5M, tmp, size5M, size5M);
        } else {
            tmp = fileBytes;
        }
        MessageDigest m = MessageDigest.getInstance("MD5");
        byte[] digest = m.digest(tmp);
        return Hex.encodeHexString(digest);
    }


}
