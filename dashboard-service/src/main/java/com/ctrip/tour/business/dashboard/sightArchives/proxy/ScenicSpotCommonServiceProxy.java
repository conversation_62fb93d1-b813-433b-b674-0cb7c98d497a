//package com.ctrip.tour.business.dashboard.sightArchives.proxy;
//
//
//import com.ctrip.ottd.ticket.common.contract.common.QueryRequestBaseDataType;
//import com.ctrip.ottd.ticket.common.contract.service.ScenicSpotCommonServiceClient;
//import com.ctrip.ottd.ticket.common.contract.service.globalization.GlobalGetScenicspotNameRequestType;
//import com.ctrip.ottd.ticket.common.contract.service.globalization.GlobalGetScenicspotNameResponseType;
//import com.ctrip.ottd.ticket.common.contract.service.globalization.dto.GlobalScenicSpotNameType;
//import com.ctrip.tour.business.dashboard.utils.UserUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang.StringUtils;
//import org.springframework.stereotype.Component;
//
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//@Component
//@Slf4j
//public class ScenicSpotCommonServiceProxy {
//    ScenicSpotCommonServiceClient client = ScenicSpotCommonServiceClient.getInstance();
//
//    public Map<Long,String> globalGetScenicspotName(List<Long> scenicSpotIds){
//
//        GlobalGetScenicspotNameRequestType requestType = new GlobalGetScenicspotNameRequestType();
//        QueryRequestBaseDataType baseDataType = new QueryRequestBaseDataType();
//        baseDataType.setLocale(UserUtil.getVbkLocale());
//        requestType.setScenicspotIdList(scenicSpotIds);
//
//        GlobalGetScenicspotNameResponseType responseType;
//        try {
//            log.info("globalGetScenicspotName requestType: {}", requestType);
//            responseType = client.globalGetScenicspotName(requestType);
//            log.info("globalGetScenicspotName responseType: {}", responseType);
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
//        List<GlobalScenicSpotNameType> scenicSpotNameTypeList = responseType.getNameList();
//        Map<Long,String> map = new HashMap<>();
//        for(GlobalScenicSpotNameType scenicSpotNameType : scenicSpotNameTypeList) {
//            if (scenicSpotNameType.getScenicspotId() != null && StringUtils.isNotBlank(scenicSpotNameType.getGlobalName())) {
//                map.put(scenicSpotNameType.getScenicspotId(), scenicSpotNameType.getGlobalName());
//            }
//        }
//
//        return map;
//    }
//}
