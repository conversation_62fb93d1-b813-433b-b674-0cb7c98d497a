package com.ctrip.tour.business.dashboard.tktBusiness.dao;

import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.helper.DalDefaultJpaParser;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardVendorMapping;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-03-14
 * @API DOC: http://pages.release.ctripcorp.com/framework/dal-client-trip/#/3/3.2/3.2
 */

@Repository
public class BusinessDashboardVendorMappingDao {

    @Autowired
    private GeneralDao dao;

    private DalTableDao<BusinessDashboardVendorMapping> client;

    public BusinessDashboardVendorMappingDao() throws SQLException {
        this.client = new DalTableDao<>(new DalDefaultJpaParser<>(BusinessDashboardVendorMapping.class));
    }

    /**
     * 获取供应商id和业务人员的映射关系
     * @param vendorIdList
     * @return
     */
    public List<BusinessDashboardVendorMapping> getBusinessVendorMapping(List<Long> vendorIdList) throws SQLException {
        DalHints hints = DalHints.createIfAbsent(null);
        // String vendorIdStr = vendorIdList.stream().map(Object::toString).collect(Collectors.joining(","));
        String sql = "select vendorId, businessUserCode from business_dashboard_vendor_mapping where vendorId in (?) group by vendorId, businessUserCode";
        return client.query(sql, hints, vendorIdList);
    }
}
