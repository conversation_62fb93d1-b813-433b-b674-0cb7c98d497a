package com.ctrip.tour.business.dashboard.tktBusiness.select43556;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2023-07-19
 */
@Entity
@Database(name = "TtdReportDB_W")
@Table(name = "report_query_basic_config")
public class ReportQueryBasicConfig implements DalPojo {

	/**
	 * 主键
	 */
	@Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long id;

	/**
	 * 查询id
	 */
	@Column(name = "query_id")
	@Type(value = Types.BIGINT)
	private Long queryId;

	/**
	 * 引擎
	 */
	@Column(name = "engine")
	@Type(value = Types.VARCHAR)
	private String engine;

	/**
	 * 库名
	 */
	@Column(name = "dbname")
	@Type(value = Types.VARCHAR)
	private String dbname;

	/**
	 * 表名
	 */
	@Column(name = "tablename")
	@Type(value = Types.VARCHAR)
	private String tablename;

	/**
	 * 脚本
	 */
	@Column(name = "script")
	@Type(value = Types.VARCHAR)
	private String script;

	/**
	 * 时间字段
	 */
	@Column(name = "date_column")
	@Type(value = Types.VARCHAR)
	private String dateColumn;

	/**
	 * 注释
	 */
	@Column(name = "remark")
	@Type(value = Types.VARCHAR)
	private String remark;

	/**
	 * 修改时间
	 */
	@Column(name = "DataChange_LastTime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;

	/**
	 * 表别名
	 */
	@Column(name = "tablename_alias")
	@Type(value = Types.VARCHAR)
	private String tablenameAlias;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getQueryId() {
		return queryId;
	}

	public void setQueryId(Long queryId) {
		this.queryId = queryId;
	}

	public String getEngine() {
		return engine;
	}

	public void setEngine(String engine) {
		this.engine = engine;
	}

	public String getDbname() {
		return dbname;
	}

	public void setDbname(String dbname) {
		this.dbname = dbname;
	}

	public String getTablename() {
		return tablename;
	}

	public void setTablename(String tablename) {
		this.tablename = tablename;
	}

	public String getScript() {
		return script;
	}

	public void setScript(String script) {
		this.script = script;
	}

	public String getDateColumn() {
		return dateColumn;
	}

	public void setDateColumn(String dateColumn) {
		this.dateColumn = dateColumn;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Timestamp getDatachangeLasttime() {
		return datachangeLasttime;
	}

	public void setDatachangeLasttime(Timestamp datachangeLasttime) {
		this.datachangeLasttime = datachangeLasttime;
	}

	public String getTablenameAlias() {
		return tablenameAlias;
	}

	public void setTablenameAlias(String tablenameAlias) {
		this.tablenameAlias = tablenameAlias;
	}

}
