package com.ctrip.tour.business.dashboard.sightArchives.dao.qualityDao;

import com.ctrip.framework.clogging.common.exception.BusinessException;
import com.ctrip.soa._27181.PreparedParameterBean;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.TktStarRocksDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import java.sql.Types;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Repository
@Slf4j
public class AdmSevTtdViewspotDefectDetailDfDao {
    @Autowired
    private TktStarRocksDao tktStarRocksDao;



    /*

    数仓给的sql例子

    ---指标卡逻辑
SELECT  COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('确认前推翻','订单超时取消') THEN qa_index END) * 0.3
        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('凭证发送不及时') THEN qa_index END) * 0.2
        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('确认后推翻') THEN qa_index END) * 2
        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND first_exam_target IN ('到场无X') THEN qa_index END) * 4
        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND first_exam_target IN ('到场使用障碍') THEN qa_index END) * 1
        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('景点不符','导游服务不符','游览顺序不符','接送服务不符','餐食不符','服务中止','游览时间不符') THEN qa_index END) * 2
        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('增加自费项','增加购物点') THEN qa_index END) * 3
        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('对景点不满','对车辆不满','对特殊要求未满足不满','对成团人数不满','wifi机器使用不满','对体验项目不满','对礼品不满','对行程安排不满','对餐食不满','对司导服务不满','对车上推销商品不满','行车安全') THEN qa_index END) * 1.5
        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('强制好评','价格劣势') THEN qa_index END) * 0.6
        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('强制自费','强制购物') THEN qa_index END) * 3
        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('产品信息错误/缺失') THEN qa_index END) * 2
        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('人群信息错误/缺失','退改信息错误/缺失','价格倒挂','导游超时未联系') THEN qa_index END) * 0.8
        AS weighted_defect_cnt
        ,COUNT(DISTINCT CASE    WHEN is_pay_odr = 1) THEN order_id END) AS pay_odr_cnt
FROM    dw_diydb.adm_sev_ttd_viewspot_defect_detail_df
WHERE   d = CURRENT_DATE
AND     viewspotid = ''
AND     dep_date between '' and ''
AND     provider_id = ''
AND     bu_type = ''
;

--缺陷原因占比
SELECT  second_exam_target
        ,COUNT(DISTINCT qa_index)  as defect_count
FROM    dw_diydb.adm_sev_ttd_viewspot_defect_detail_df
WHERE   d = CURRENT_DATE
AND     viewspotid = ''
AND     dep_date between '' and ''
AND     provider_id = ''
AND     bu_type = ''
GROUP BY second_exam_target
;

--产品top10
SELECT  prd_id
        ,COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('确认前推翻','订单超时取消') THEN qa_index END) * 0.3
        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('凭证发送不及时') THEN qa_index END) * 0.2
        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('确认后推翻') THEN qa_index END) * 2
        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND first_exam_target IN ('到场无X') THEN qa_index END) * 4
        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND first_exam_target IN ('到场使用障碍') THEN qa_index END) * 1
        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('景点不符','导游服务不符','游览顺序不符','接送服务不符','餐食不符','服务中止','游览时间不符') THEN qa_index END) * 2
        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('增加自费项','增加购物点') THEN qa_index END) * 3
        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('对景点不满','对车辆不满','对特殊要求未满足不满','对成团人数不满','wifi机器使用不满','对体验项目不满','对礼品不满','对行程安排不满','对餐食不满','对司导服务不满','对车上推销商品不满','行车安全') THEN qa_index END) * 1.5
        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('强制好评','价格劣势') THEN qa_index END) * 0.6
        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('强制自费','强制购物') THEN qa_index END) * 3
        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('产品信息错误/缺失') THEN qa_index END) * 2
        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('人群信息错误/缺失','退改信息错误/缺失','价格倒挂','导游超时未联系') THEN qa_index END) * 0.8
        AS weighted_defect_cnt
        ,COUNT(DISTINCT CASE    WHEN is_pay_odr = 1) THEN order_id END) AS pay_odr_cnt
        ,CONCAT_WS(',',COLLECT_SET(provider_id))
FROM    dw_diydb.adm_sev_ttd_viewspot_defect_detail_df
WHERE   d = CURRENT_DATE
AND     viewspotid = ''
AND     dep_date between '' and ''
AND     provider_id = ''
AND     bu_type = ''
AND     second_exam_target = ''
GROUP BY prd_id
;

     */

    private static final Executor executor = Executors.newCachedThreadPool();


    public Map<String, Object> queryMetricCard(Long sightId, String startDate, String endDate, List<Long> vendorIdList, Integer buType, boolean needSubSight, String queryD) {

        StringBuilder sql = new StringBuilder("SELECT  COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('确认前推翻','订单超时取消') THEN qa_index END) * 0.3" +   //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('凭证发送不及时') THEN qa_index END) * 0.2" +   //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('确认后推翻') THEN qa_index END) * 2" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND first_exam_target IN ('到场无X') THEN qa_index END) * 4" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND first_exam_target IN ('到场使用障碍') THEN qa_index END) * 1" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('景点不符','导游服务不符','游览顺序不符','接送服务不符','餐食不符','服务中止','游览时间不符') THEN qa_index END) * 2" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('增加自费项','增加购物点') THEN qa_index END) * 3" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('对景点不满','对车辆不满','对特殊要求未满足不满','对成团人数不满','wifi机器使用不满','对体验项目不满','对礼品不满','对行程安排不满','对餐食不满','对司导服务不满','对车上推销商品不满','行车安全') THEN qa_index END) * 1.5" +   //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('强制好评','价格劣势') THEN qa_index END) * 0.6" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('强制自费','强制购物') THEN qa_index END) * 3" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('产品信息错误/缺失') THEN qa_index END) * 2" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('人群信息错误/缺失','退改信息错误/缺失','价格倒挂','导游超时未联系') THEN qa_index END) * 0.8" +  //NOSONAR
                "        AS weighted_defect_cnt" +
                "        ,COUNT(DISTINCT CASE    WHEN is_pay_odr = 1 THEN order_id END) AS pay_odr_cnt" +
                " FROM    adm_sev_ttd_viewspot_defect_detail_df" +
                " WHERE   d = ?"
        );
        List<PreparedParameterBean> parameters = new java.util.ArrayList<>();
        parameters.add(new PreparedParameterBean(queryD, Types.VARCHAR));

        appendSightId(parameters, sql, sightId, needSubSight);
        appendDateRange(parameters, sql, startDate, endDate);
        appendBusinessType(parameters, sql, buType);
        appendVendorIdList(parameters, sql, vendorIdList);
        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            log.warn("queryMetricCard error", e);
        }
        if (CollectionUtils.isEmpty(result)) {
            return new HashMap<>();
        }

        return result.get(0);

    }

    public Map<String, Object> queryMetricCardYoy(Long sightId, String startDate, String endDate, List<Long> vendorIdList, Integer buType, boolean needSubSight, String queryD) {
        StringBuilder sql = new StringBuilder("SELECT  COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('确认前推翻','订单超时取消') THEN qa_index END) * 0.3" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('凭证发送不及时') THEN qa_index END) * 0.2" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('确认后推翻') THEN qa_index END) * 2" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND first_exam_target IN ('到场无X') THEN qa_index END) * 4" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND first_exam_target IN ('到场使用障碍') THEN qa_index END) * 1" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('景点不符','导游服务不符','游览顺序不符','接送服务不符','餐食不符','服务中止','游览时间不符') THEN qa_index END) * 2" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('增加自费项','增加购物点') THEN qa_index END) * 3" + //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('对景点不满','对车辆不满','对特殊要求未满足不满','对成团人数不满','wifi机器使用不满','对体验项目不满','对礼品不满','对行程安排不满','对餐食不满','对司导服务不满','对车上推销商品不满','行车安全') THEN qa_index END) * 1.5" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('强制好评','价格劣势') THEN qa_index END) * 0.6" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('强制自费','强制购物') THEN qa_index END) * 3" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('产品信息错误/缺失') THEN qa_index END) * 2" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('人群信息错误/缺失','退改信息错误/缺失','价格倒挂','导游超时未联系') THEN qa_index END) * 0.8" +  //NOSONAR
                "        AS weighted_defect_cnt" +
                "        ,COUNT(DISTINCT CASE    WHEN is_pay_odr = 1 THEN order_id END) AS pay_odr_cnt" +
                " FROM    adm_sev_ttd_viewspot_defect_detail_df  t1 inner join v_dim_date t2 on t1.dep_date = t2.date_lastyear" +
                " WHERE   t1.d = ?"
        );
        List<PreparedParameterBean> parameters = new java.util.ArrayList<>();
        parameters.add(new PreparedParameterBean(queryD, Types.VARCHAR));

        appendSightId(parameters, sql, sightId, needSubSight);
        appendDateRangeOfLastYear(parameters, sql, startDate, endDate);
        appendBusinessType(parameters, sql, buType);
        appendVendorIdList(parameters, sql, vendorIdList);
        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            log.warn("queryMetricCardYoy error", e);
        }
        if (CollectionUtils.isEmpty(result)) {
            return new HashMap<>();
        }

        return result.get(0);
    }

    private void appendDateRangeOfLastYear(List<PreparedParameterBean> lyparameters, StringBuilder sql, String startDate, String endDate) {
        sql.append(" and t2.date_solar between ? and ?");
        lyparameters.add(new PreparedParameterBean(startDate, Types.VARCHAR));
        lyparameters.add(new PreparedParameterBean(endDate, Types.VARCHAR));
    }

    public List<Map<String, Object>> queryPieChart(Long sightId, String startDate, String endDate, List<Long> vendorIdList, Integer buType, boolean needSubSight, String queryD) {

        StringBuilder sql = new StringBuilder("SELECT  second_exam_target" +
                "        ,COUNT(DISTINCT qa_index) * ratio as defect_count" +
                " FROM    adm_sev_ttd_viewspot_defect_detail_df" +
                " WHERE is_examine = '是' " +  //NOSONAR
                " AND (second_exam_target IN ('确认前推翻','订单超时取消','凭证发送不及时','确认后推翻','景点不符','导游服务不符','游览顺序不符','接送服务不符','餐食不符','服务中止','游览时间不符'" +  //NOSONAR
                "                           ,'增加自费项','增加购物点','对景点不满','对车辆不满','对特殊要求未满足不满','对成团人数不满','wifi机器使用不满','对体验项目不满','对礼品不满','对行程安排不满','对餐食不满','对司导服务不满','对车上推销商品不满','行车安全'" +  //NOSONAR
                "                           ,'强制好评','价格劣势','强制自费','强制购物','产品信息错误/缺失','人群信息错误/缺失','退改信息错误/缺失','价格倒挂','导游超时未联系') " +  //NOSONAR
                "  OR first_exam_target IN ('到场无X','到场使用障碍') ) " +  //NOSONAR
                " and  d = ?" //NOSONAR
        );

        List<PreparedParameterBean> parameters = new java.util.ArrayList<>();
        parameters.add(new PreparedParameterBean(queryD, Types.VARCHAR));

        appendSightId(parameters, sql, sightId, needSubSight);
        appendDateRange(parameters, sql, startDate, endDate);
        appendBusinessType(parameters, sql, buType);
        appendVendorIdList(parameters, sql, vendorIdList);
        sql.append(" and second_exam_target != 'unkwn' ");
        sql.append(" GROUP BY second_exam_target,ratio");
        sql.append(" HAVING defect_count > 0 ");
        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            log.warn("queryPieChart error", e);
        }

        return result;
    }

    public List<Map<String, Object>> queryProductTable(String defectName, Long sightId, String startDate, String endDate, List<Long> vendorIdList, Integer buType, boolean needSubSight, String queryD) {
        //景点表现效果

        CountDownLatch countDownLatch = new CountDownLatch(2);

        AtomicReference<List<Map<String, Object>>> result1 = new AtomicReference<>(), result2 = new AtomicReference<>();

        //分母sql
        executor.execute(() -> {
            try {
                StringBuilder sql1 = new StringBuilder(
                        "SELECT prd_id, CONCAT(prd_id, '-', prd_name) AS prd_name, " +
                                "        COUNT(DISTINCT CASE WHEN is_pay_odr = 1 THEN order_id END) AS pay_odr_cnt, " +
                                "        CONCAT_WS(',', GROUP_CONCAT(DISTINCT CONCAT(provider_id, '-', provider_name))) AS provider " +
                                "    FROM adm_sev_ttd_viewspot_defect_detail_df " +
                                "    WHERE d = ? "
                );
                List<PreparedParameterBean> parameters1 = new java.util.ArrayList<>();
                parameters1.add(new PreparedParameterBean(queryD, Types.VARCHAR));
                appendSightId(parameters1, sql1, sightId, needSubSight);
                appendDateRange(parameters1, sql1, startDate, endDate);
                appendBusinessType(parameters1, sql1, buType);
                appendVendorIdList(parameters1, sql1, vendorIdList);
                //不加二级渗透
//            sql1.append(" AND second_exam_target = ?  ");
//            parameters1.add(new PreparedParameterBean(defectName, Types.VARCHAR));
                sql1.append(" GROUP BY prd_id,prd_name");
                result1.set(tktStarRocksDao.getListResultNew(sql1.toString(), parameters1));
            } catch (Exception e) {
                log.warn("queryProductTable1 error", e);
                throw new BusinessException("queryProductTable1 error", e);
            } finally {
                countDownLatch.countDown();
            }
        });


        //分子sql
        executor.execute(() -> {
            try {
                StringBuilder sql2 = new StringBuilder(
                        "SELECT prd_id, CONCAT(prd_id, '-', prd_name) AS prd_name, COUNT(DISTINCT IF(is_examine = '是', qa_index, NULL)) AS defect_cnt, " +   //NOSONAR
                                "        COUNT(DISTINCT IF(is_examine = '是', qa_index, NULL)) * ratio AS weighted_defect_cnt, " +  //NOSONAR
                                //总数不能从这里取
                                //"        COUNT(DISTINCT CASE WHEN is_pay_odr = 1 THEN order_id END) AS pay_odr_cnt, " +
                                "        CONCAT_WS(',', GROUP_CONCAT(DISTINCT CONCAT(provider_id, '-', provider_name))) AS provider " +
                                "    FROM adm_sev_ttd_viewspot_defect_detail_df " +
                                "    WHERE d = ? "
                );

                List<PreparedParameterBean> parameters2 = new java.util.ArrayList<>();
                parameters2.add(new PreparedParameterBean(queryD, Types.VARCHAR));
                appendSightId(parameters2, sql2, sightId, needSubSight);
                appendDateRange(parameters2, sql2, startDate, endDate);
                appendBusinessType(parameters2, sql2, buType);
                appendVendorIdList(parameters2, sql2, vendorIdList);
                sql2.append(" AND second_exam_target = ?  ");
                parameters2.add(new PreparedParameterBean(defectName, Types.VARCHAR));
                sql2.append(" GROUP BY prd_id,prd_name, ratio");
                result2.set(tktStarRocksDao.getListResultNew(sql2.toString(), parameters2));
            } catch (Exception e) {
                log.warn("queryProductTable2 error", e);
                throw new BusinessException("queryProductTable2 error", e);
            } finally {
                countDownLatch.countDown();
            }
        });

        try {
            Assert.isTrue(countDownLatch.await(30, TimeUnit.SECONDS), "Query Data Time Out");
        } catch (InterruptedException e) {
            log.warn("queryProductTable error", e);
            throw new BusinessException("interruptedException", e);
        }

        return mergeAndFilter(result1.get(), result2.get());
    }


    private List<Map<String, Object>> mergeAndFilter(List<Map<String, Object>> result1, List<Map<String, Object>> result2) {

        // Create a map from result2 for quick lookup
        Map<String, Map<String, Object>> result2Map = new HashMap<>();
        for (Map<String, Object> map2 : result2) {
            if (null == map2) continue;
            String key = map2.get("prd_id").toString() + "|" + map2.get("prd_name").toString();
            result2Map.put(key, map2);
        }

        // Merge result2 into result1
        List<Map<String, Object>> mergedResult = new ArrayList<>();
        for (Map<String, Object> map1 : result1) {
            String key = map1.get("prd_id").toString() + "|" + map1.get("prd_name").toString();
            Map<String, Object> map2 = result2Map.get(key);

            if (map2 != null) {
                // Create a new merged map
                Map<String, Object> mergedMap = new HashMap<>(map1);
                Object weightedDefectCnt = map2.get("weighted_defect_cnt");
                mergedMap.put("weighted_defect_cnt", Optional.ofNullable(weightedDefectCnt).orElse(0L));

                mergedResult.add(mergedMap);
            }
        }

        // Filter and sort
        return mergedResult.stream()
                .filter(map -> {
                    Object value = map.get("weighted_defect_cnt");
                    return value instanceof Number && ((Number) value).doubleValue() > 0.0;
                })
                .sorted((map1, map2) -> {
                    Number cnt1 = (Number) map1.get("weighted_defect_cnt");
                    Number cnt2 = (Number) map2.get("weighted_defect_cnt");
                    return Double.compare(cnt2.doubleValue(), cnt1.doubleValue());
                })
                .limit(10)
                .collect(Collectors.toList());
    }


    public List<Map<String, Object>> queryProductTableSpecial(List<String> defectNameList, Long sightId, String startDate, String endDate, List<Long> vendorIdList, Integer buType, boolean needSubSight, String queryD) {

        StringBuilder sql = new StringBuilder(
                "SELECT  concat(prd_id,'-',prd_name) as prd_name" +
                        "        ,COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('确认前推翻','订单超时取消') THEN qa_index END) * 0.3" +  //NOSONAR
                        "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('凭证发送不及时') THEN qa_index END) * 0.2" +  //NOSONAR
                        "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('确认后推翻') THEN qa_index END) * 2" +  //NOSONAR
                        "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND first_exam_target IN ('到场无X') THEN qa_index END) * 4" +  //NOSONAR
                        "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND first_exam_target IN ('到场使用障碍') THEN qa_index END) * 1" +  //NOSONAR
                        "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('景点不符','导游服务不符','游览顺序不符','接送服务不符','餐食不符','服务中止','游览时间不符') THEN qa_index END) * 2" +  //NOSONAR
                        "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('增加自费项','增加购物点') THEN qa_index END) * 3" +  //NOSONAR
                        "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('对景点不满','对车辆不满','对特殊要求未满足不满','对成团人数不满','wifi机器使用不满','对体验项目不满','对礼品不满','对行程安排不满','对餐食不满','对司导服务不满','对车上推销商品不满','行车安全') THEN qa_index END) * 1.5" +  //NOSONAR
                        "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('强制好评','价格劣势') THEN qa_index END) * 0.6" +  //NOSONAR
                        "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('强制自费','强制购物') THEN qa_index END) * 3" +  //NOSONAR
                        "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('产品信息错误/缺失') THEN qa_index END) * 2" +  //NOSONAR
                        "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('人群信息错误/缺失','退改信息错误/缺失','价格倒挂','导游超时未联系') THEN qa_index END) * 0.8" +  //NOSONAR
                        "        AS weighted_defect_cnt" +
                        "        ,COUNT(DISTINCT CASE    WHEN is_pay_odr = 1 THEN order_id END) AS pay_odr_cnt" +
                        "        ,CONCAT_WS(',',GROUP_CONCAT(distinct concat(provider_id,'-',provider_name))) as provider" +
                        " FROM    adm_sev_ttd_viewspot_defect_detail_df" +
                        " WHERE   d = ?"
        );
        List<PreparedParameterBean> parameters = new java.util.ArrayList<>();
        parameters.add(new PreparedParameterBean(queryD, Types.VARCHAR));
        appendSightId(parameters, sql, sightId, needSubSight);
        appendDateRange(parameters, sql, startDate, endDate);
        appendBusinessType(parameters, sql, buType);
        appendVendorIdList(parameters, sql, vendorIdList);
        if (CollectionUtils.isEmpty(defectNameList)) {
            return new ArrayList<>();
        } else {
            sql.append(" AND second_exam_target NOT IN (");
            for (String defectName : defectNameList) {
                sql.append("?");
                parameters.add(new PreparedParameterBean(defectName, Types.VARCHAR));
                if (defectNameList.indexOf(defectName) < defectNameList.size() - 1) {
                    sql.append(",");
                }
            }
            sql.append(" ) ");

        }

        sql.append(" GROUP BY prd_id,prd_name");
        sql.append(" HAVING weighted_defect_cnt > 0 ");
        sql.append(" ORDER BY weighted_defect_cnt DESC LIMIT 10");
        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            log.warn("queryProductTable error", e);
        }
        return result;
    }


    private void appendSightId(List<PreparedParameterBean> parameters, StringBuilder sql, Long sightId, boolean needSubSight) {
        if (needSubSight) {
            sql.append(" and viewspotid = ? ");
        } else {
            sql.append(" and sub_viewspotid = ? ");
        }
        parameters.add(new PreparedParameterBean(String.valueOf(sightId), Types.BIGINT));
    }

    private void appendDateRange(List<PreparedParameterBean> parameters, StringBuilder sql, String startDate, String endDate) {

        sql.append(" AND (dep_date BETWEEN ? AND ?) ");
        parameters.add(new PreparedParameterBean(startDate, Types.VARCHAR));
        parameters.add(new PreparedParameterBean(endDate, Types.VARCHAR));

    }

    private void appendBusinessType(List<PreparedParameterBean> parameters, StringBuilder sql, Integer buType) {
        if (buType != null && buType != 1) {
            if (buType == 2) {
                sql.append(" AND bu_type = ?");
                parameters.add(new PreparedParameterBean("门票", Types.VARCHAR));  //NOSONAR
            } else if (buType == 3) {
                sql.append(" AND bu_type = ?");
                parameters.add(new PreparedParameterBean("活动", Types.VARCHAR));  //NOSONAR
            }
        }
    }

    private void appendVendorIdList(List<PreparedParameterBean> parameters, StringBuilder sql, List<Long> vendorIdList) {
        if (CollectionUtils.isNotEmpty(vendorIdList)) {
            sql.append(" AND provider_id IN (");
            for (int i = 0; i < vendorIdList.size(); i++) {
                sql.append("?");
                if (i < vendorIdList.size() - 1) {
                    sql.append(",");
                }
                parameters.add(new PreparedParameterBean(String.valueOf(vendorIdList.get(i)), Types.VARCHAR));
            }
            sql.append(")");
        }
    }

    public Map<String, Object> queryRadarweightedDefectRate(String queryD, Long sightId, Integer dateType, String startDate, String endDate, List<Long> vendorIdList, Integer businessType, Boolean needSubSight) {
        StringBuilder sql = new StringBuilder("SELECT  cast((COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('确认前推翻','订单超时取消') THEN qa_index END) * 0.3" +   //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('凭证发送不及时') THEN qa_index END) * 0.2" +   //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('确认后推翻') THEN qa_index END) * 2" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND first_exam_target IN ('到场无X') THEN qa_index END) * 4" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND first_exam_target IN ('到场使用障碍') THEN qa_index END) * 1" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('景点不符','导游服务不符','游览顺序不符','接送服务不符','餐食不符','服务中止','游览时间不符') THEN qa_index END) * 2" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('增加自费项','增加购物点') THEN qa_index END) * 3" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('对景点不满','对车辆不满','对特殊要求未满足不满','对成团人数不满','wifi机器使用不满','对体验项目不满','对礼品不满','对行程安排不满','对餐食不满','对司导服务不满','对车上推销商品不满','行车安全') THEN qa_index END) * 1.5" +   //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('强制好评','价格劣势') THEN qa_index END) * 0.6" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('强制自费','强制购物') THEN qa_index END) * 3" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('产品信息错误/缺失') THEN qa_index END) * 2" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('人群信息错误/缺失','退改信息错误/缺失','价格倒挂','导游超时未联系') THEN qa_index END) * 0.8" +  //NOSONAR
                "        )" +
                "        /(COUNT(DISTINCT CASE    WHEN is_pay_odr = 1 THEN order_id END)) as Double) AS weightedDefectRate" +
                " FROM    adm_sev_ttd_viewspot_defect_detail_df" +
                " WHERE   d = ?"
        );
        List<PreparedParameterBean> parameters = new java.util.ArrayList<>();
        parameters.add(new PreparedParameterBean(queryD, Types.VARCHAR));
        appendSightId(parameters, sql, sightId, needSubSight);
        appendDateRange(parameters, sql, startDate, endDate);
        appendBusinessType(parameters, sql, businessType);
        appendVendorIdList(parameters, sql, vendorIdList);
        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            log.warn("queryRadarweightedDefectRate error", e);
        }
        if (CollectionUtils.isEmpty(result)) {
            return new HashMap<>();
        }
        return result.get(0);
    }

    public Map<String, Object> queryRadarweightedDefectRateAverage(String queryD, Long sightId, Integer dateType, String startDate, String endDate, List<Long> vendorIdList, Integer businessType) {
        StringBuilder sql = new StringBuilder("SELECT cast(avg(weightedDefectRate) as Double) as weightedDefectRate" +
                " from (SELECT  sub_viewspotid" +
                "       ,(COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('确认前推翻','订单超时取消') THEN qa_index END) * 0.3" +   //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('凭证发送不及时') THEN qa_index END) * 0.2" +   //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('确认后推翻') THEN qa_index END) * 2" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND first_exam_target IN ('到场无X') THEN qa_index END) * 4" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND first_exam_target IN ('到场使用障碍') THEN qa_index END) * 1" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('景点不符','导游服务不符','游览顺序不符','接送服务不符','餐食不符','服务中止','游览时间不符') THEN qa_index END) * 2" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('增加自费项','增加购物点') THEN qa_index END) * 3" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('对景点不满','对车辆不满','对特殊要求未满足不满','对成团人数不满','wifi机器使用不满','对体验项目不满','对礼品不满','对行程安排不满','对餐食不满','对司导服务不满','对车上推销商品不满','行车安全') THEN qa_index END) * 1.5" +   //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('强制好评','价格劣势') THEN qa_index END) * 0.6" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('强制自费','强制购物') THEN qa_index END) * 3" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('产品信息错误/缺失') THEN qa_index END) * 2" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('人群信息错误/缺失','退改信息错误/缺失','价格倒挂','导游超时未联系') THEN qa_index END) * 0.8" +  //NOSONAR
                "        )" +
                "        /(COUNT(DISTINCT CASE    WHEN is_pay_odr = 1 THEN order_id END)) AS weightedDefectRate" +
                " FROM    adm_sev_ttd_viewspot_defect_detail_df" +
                " WHERE   d = ? and dep_date between ? and ? "
        );
        List<PreparedParameterBean> parameters = new java.util.ArrayList<>();
        parameters.add(new PreparedParameterBean(queryD, Types.VARCHAR));
        parameters.add(new PreparedParameterBean(startDate, Types.VARCHAR));
        parameters.add(new PreparedParameterBean(endDate, Types.VARCHAR));
//        appendSightId(parameters, sql, sightId, false);
//        appendDateRange(parameters, sql, dateType, startDate, endDate);
//        appendBusinessType(parameters, sql, businessType);
//        appendVendorIdList(parameters, sql, vendorIdList);
        sql.append(" group by sub_viewspotid");
        sql.append(" ) a ");
        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            log.warn("queryRadarweightedDefectRateAverage error", e);
        }
        if (CollectionUtils.isEmpty(result)) {
            return new HashMap<>();
        }
        return result.get(0);
    }


    public List<Map<String, Object>> querySightRankListOfperformanceQuality(String queryD, List<Long> competitiveSightIdList, String startDate, String endDate, Integer businessType, List<Long> vendorIdList, Integer dateType) {
        if (CollectionUtils.isEmpty(competitiveSightIdList)) {
            return new ArrayList<>();
        }

        StringBuilder sql = new StringBuilder("SELECT  sub_viewspotid,sub_viewspot_name,cast((COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('确认前推翻','订单超时取消') THEN qa_index END) * 0.3" +   //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('凭证发送不及时') THEN qa_index END) * 0.2" +   //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('确认后推翻') THEN qa_index END) * 2" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND first_exam_target IN ('到场无X') THEN qa_index END) * 4" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND first_exam_target IN ('到场使用障碍') THEN qa_index END) * 1" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('景点不符','导游服务不符','游览顺序不符','接送服务不符','餐食不符','服务中止','游览时间不符') THEN qa_index END) * 2" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('增加自费项','增加购物点') THEN qa_index END) * 3" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('对景点不满','对车辆不满','对特殊要求未满足不满','对成团人数不满','wifi机器使用不满','对体验项目不满','对礼品不满','对行程安排不满','对餐食不满','对司导服务不满','对车上推销商品不满','行车安全') THEN qa_index END) * 1.5" +   //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('强制好评','价格劣势') THEN qa_index END) * 0.6" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('强制自费','强制购物') THEN qa_index END) * 3" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('产品信息错误/缺失') THEN qa_index END) * 2" +  //NOSONAR
                "        + COUNT(DISTINCT CASE    WHEN is_examine = '是' AND second_exam_target IN ('人群信息错误/缺失','退改信息错误/缺失','价格倒挂','导游超时未联系') THEN qa_index END) * 0.8" +  //NOSONAR
                "        )" +
                "        /(COUNT(DISTINCT CASE    WHEN is_pay_odr = 1 THEN order_id END)) as Double) AS weightedDefectRate" +
                " FROM    adm_sev_ttd_viewspot_defect_detail_df" +
                " WHERE   d = ?"
        );
        List<PreparedParameterBean> parameters = new java.util.ArrayList<>();
        parameters.add(new PreparedParameterBean(queryD, Types.VARCHAR));
        appendSightIdOfCompetitiveSight(parameters, sql, competitiveSightIdList);
//        appendQueryD(parameters, sql, queryD);
        appendDateRange(parameters, sql, startDate, endDate);
//        appendBusinessType(parameters, sql, businessType);
//        appendVendorIdList(parameters, sql, vendorIdList);
        sql.append(" group by sub_viewspotid,sub_viewspot_name");
        sql.append(" having weightedDefectRate is not null");
        sql.append(" order by weightedDefectRate ASC");
        sql.append(" limit 10");
        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            log.warn("querySightRankListOfperformanceQuality error", e);
        }
        return result;
    }

    private void appendSightIdOfCompetitiveSight(List<PreparedParameterBean> parameters, StringBuilder sql, List<Long> competitiveSightIdList) {
        if (CollectionUtils.isNotEmpty(competitiveSightIdList)) {
            sql.append(" and sub_viewspotid in (");
            for (int i = 0; i < competitiveSightIdList.size(); i++) {
                if (i == 0) {
                    sql.append("?");
                } else {
                    sql.append(",?");
                }
                parameters.add(new PreparedParameterBean(String.valueOf(competitiveSightIdList.get(i)), Types.BIGINT));
            }
            sql.append(")");
        } else {
            sql.append(" and sub_viewspotid =0 ");
        }
    }

    private void appendQueryD(List<PreparedParameterBean> parameters, StringBuilder sql, String queryD) {
        sql.append(" and d = ?");
        parameters.add(new PreparedParameterBean(queryD, Types.VARCHAR));
    }

}
