package com.ctrip.tour.business.dashboard.grpBusiness.dao.starrocks;

import com.ctrip.soa._27181.PreparedParameterBean;
import com.ctrip.tour.business.dashboard.grpBusiness.config.StarRocksConnPool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class StarRocksDao {


    public List<Map<String, Object>> getListResult(String sql, List<PreparedParameterBean> parameters) throws SQLException {
        List<Map<String, Object>> rsMapList = new ArrayList<>();
        //执行完毕释放资源
        try (Connection conn = StarRocksConnPool.getConnection("starrocks.xy");
             PreparedStatement ps = createPreparedStatement(conn,sql,parameters);
             ResultSet rs = ps.executeQuery()) {
            ResultSetMetaData meta = rs.getMetaData();
            int columnCount = meta.getColumnCount();
            // 处理ResultSet结果集
            while (rs.next()) {
                Map<String, Object> map = new HashMap<>();
                for (int i = 1; i <= columnCount; i++) {
                    map.put(meta.getColumnName(i),rs.getObject(i));
                }
                rsMapList.add(map);
            }
        } catch (SQLException e) {
            log.warn("StarRocks getListResult error, sql: {}, parameters: {}", sql, parameters, e);
            throw e;
        }
        return rsMapList;
    }

    private PreparedStatement createPreparedStatement(Connection conn, String sql, List<PreparedParameterBean> parameters) throws SQLException {
        PreparedStatement ps = conn.prepareStatement(sql);
        int j = 1;
        for (PreparedParameterBean parameter : parameters) {
            //目前参数类型: int long double varchar
            int type = parameter.getType();
            if (type == 0) {
                ps.setInt(j++, Integer.parseInt(parameter.getValue()));
            } else if (type == 1) {
                ps.setLong(j++, Long.parseLong(parameter.getValue()));
            } else if(type == 2){
                ps.setDouble(j++, Double.parseDouble(parameter.getValue()));
            } else {
                ps.setString(j++, parameter.getValue());
            }

        }
        return ps;
    }
}
