package com.ctrip.tour.business.dashboard.soa;

import com.ctrip.tour.business.dashboard.tktBusiness.checker.UserChecker;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.general.LogInfo;
import com.ctrip.tour.business.dashboard.utils.ClickhouseLogUtil;
import com.ctrip.tour.business.dashboard.utils.HickWallLogUtil;
import com.ctrip.tour.business.dashboard.utils.MapperUtil;
import com.ctrip.tour.business.dashboard.utils.UserUtil;
import com.ctriposs.baiji.rpc.server.HttpRequestWrapper;
import com.ctriposs.baiji.rpc.server.HttpResponseWrapper;
import com.ctriposs.baiji.rpc.server.filter.ServiceExecutionFilter;
import com.ctriposs.baiji.rpc.server.filter.ServiceExecutor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/25
 */
@Component
@Slf4j
public class SoaServiceExecutionFilter implements ServiceExecutionFilter {

    @Autowired
    private RemoteConfig remoteConfig;

    @Autowired
    private LogInfo logInfo;

    @Override
    public Object apply(HttpRequestWrapper request, HttpResponseWrapper response, ServiceExecutor serviceExecutor) throws Throwable {
        String stringRequest = MapperUtil.obj2Str(request.requestObject());
        String operationName = request.operationName();
        MDC.put("operationName", operationName);
        log.info("requestUrl:{},user:{},body:{}", request.requestPath(), UserUtil.getEmpCode(), stringRequest);
        Long start = System.currentTimeMillis();
        // 实际调用业务逻辑
        final Object result = serviceExecutor.execute(request, response);
        Long queryTime = System.currentTimeMillis() - start;
        String stringResponse = MapperUtil.obj2Str(result);
        log.info("requestUrl:{},user:{},response:{},time:{}", request.requestUrl(), UserUtil.getEmpCode(), stringResponse, queryTime);
        //过滤掉checkHealth请求
        //过滤掉只在zeus执行的请求
        if (!"checkhealth".equals(operationName) && !"updatetaskflowtabledata".equals(operationName)) {
            Map<String, String> cTagMap = logInfo.createClickhouseTagMap(stringRequest, stringResponse, "normal", queryTime);
            Map<String, String> hTagMap = logInfo.createHickWallTagMap("normal");
            String scenario = remoteConfig.getConfigValue("scenario");
            ClickhouseLogUtil.logSendWithTag(cTagMap, scenario);
            HickWallLogUtil.logSendTimeWithTag(queryTime, "tour.bi.business.dashboard.cost", hTagMap);
            HickWallLogUtil.logSendWithTag("tour.bi.business.dashboard.count", hTagMap);
        }
        return result;
    }
}
