package com.ctrip.tour.business.dashboard.tktBusiness.validator;

import com.ctrip.soa._24922.GetOverseaTableDataRequestType;
import com.ctrip.soa._24922.GetOverseaTableDataResponseType;
import com.ctrip.soa._24922.TimeFilter;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.InputArgumentException;
import com.ctrip.tour.business.dashboard.utils.MapperUtil;
import com.ctrip.tour.business.dashboard.utils.ParamterCheckUtil;
import com.ctrip.train.tieyouflight.soa.validation.validator.BusinessConstraintValidator;
import org.springframework.stereotype.Component;

@Component
public class GetOverseaTableDataValidator extends BusinessConstraintValidator<GetOverseaTableDataRequestType, GetOverseaTableDataResponseType> {
    @Override
    public GetOverseaTableDataResponseType validateBusiness(GetOverseaTableDataRequestType request) {
        TimeFilter timeFilter = request.getTimeFilter();
        if (!ParamterCheckUtil.checkTimeFilterWithoutTimeFrame(timeFilter)) {
            throw new InputArgumentException("input error timeFilter:" + MapperUtil.obj2Str(request));
        }
        return null;
    }
}
