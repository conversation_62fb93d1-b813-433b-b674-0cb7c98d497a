package com.ctrip.tour.business.dashboard.tktBusiness.bean;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
public class DomesticExamineTypeConfigDataBean {
    //业务定义中可以按某种规则进行合并查看数据的子指标(业务线)列表
    List<List<String>> mergedSubMetricDataList;

    List<ExamineTypeBean> examineTypeMetadataList;

    Map<String, List<SubMetricPermissonMappingBean>> subMetricPermissonEnumMap;
}
