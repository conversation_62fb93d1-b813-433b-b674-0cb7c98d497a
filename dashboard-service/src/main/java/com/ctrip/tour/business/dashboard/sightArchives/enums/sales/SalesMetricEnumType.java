package com.ctrip.tour.business.dashboard.sightArchives.enums.sales;

import java.util.Arrays;
import java.util.List;

public enum SalesMetricEnumType {

    //gmv gmv;
    //gmv占比 gmvPercentage;
    //毛利 profit;
    //毛利占比 profitPercentage;
    //毛利率 profitRate;
    //客单价 averageOrderValue;
    //退订订单量 fullyRefundedOrderCount;
    //订单量 orderCount;
    //退订率 fullyRefundedOrderRate;
    //票量 soldTicketCount
    //票量占比 soldTicketCountPercentage

    GMV(1,"gmv", "GMV"), //NOSONAR
    GMV_PERCENTAGE(2,"gmvPercentage", "GMV占比"), //NOSONAR
    PROFIT(3, "profit","毛利"), //NOSONAR
    PROFIT_PERCENTAGE(4, "profitPercentage","毛利占比"), //NOSONAR
    PROFIT_RATE(5, "profitRate","毛利率"), //NOSONAR
    AVERAGE_ORDER_VALUE(6, "averageOrderValue","客单价"), //NOSONAR
    FULLY_REFUNDED_ORDER_COUNT(7, "fullyRefundedOrderCount","退订订单量"), //NOSONAR
    ORDER_COUNT(8, "orderCount","订单量"), //NOSONAR
    FULLY_REFUNDED_ORDER_RATE(9, "fullyRefundedOrderRate","退订率"), //NOSONAR
    SOLD_TICKET_COUNT(10, "soldTicketCount","票量"), //NOSONAR
    SOLD_TICKET_COUNT_PERCENTAGE(11, "soldTicketCountPercentage","票量占比"); //NOSONAR

    private final int id;
    private final String englishName;
    private final String chineseName;

    SalesMetricEnumType(int id, String englishName, String chineseName) {
        this.id = id;
        this.englishName = englishName;
        this.chineseName = chineseName;
    }

    public int getId() {
        return id;
    }

    public String getEnglishName() {
        return englishName;
    }

    public String getChineseName() {
        return chineseName;
    }

    public static List<SalesMetricEnumType> getMetricCardEnumList() {
        //gmv gmv;
        //毛利 profit;
        //毛利率 profitRate;
        //客单价 averageOrderValue;
        //订单量 orderCount;
        //票量 soldTicketCount
        //退订率 fullyRefundedOrderRate;
        return Arrays.asList(GMV, PROFIT, PROFIT_RATE, AVERAGE_ORDER_VALUE, ORDER_COUNT, SOLD_TICKET_COUNT, FULLY_REFUNDED_ORDER_RATE);
    }

    public static List<String> getMetricCardEnumEnglishNameList() {
        //gmv gmv;
        //毛利 profit;
        //毛利率 profitRate;
        //客单价 averageOrderValue;
        //订单量 orderCount;
        //票量 soldTicketCount
        //退订率 fullyRefundedOrderRate;
        //返回这些指标的英文名list
        return Arrays.asList(
                GMV.getEnglishName()
                , PROFIT.getEnglishName()
                , PROFIT_RATE.getEnglishName()
                , AVERAGE_ORDER_VALUE.getEnglishName()
                , ORDER_COUNT.getEnglishName()
                , SOLD_TICKET_COUNT.getEnglishName()
                , FULLY_REFUNDED_ORDER_RATE.getEnglishName()
        );
    }

}
