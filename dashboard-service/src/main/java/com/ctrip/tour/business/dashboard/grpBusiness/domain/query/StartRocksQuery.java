package com.ctrip.tour.business.dashboard.grpBusiness.domain.query;

import com.ctrip.tour.business.dashboard.grpBusiness.dao.starrocks.StarRocksDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class StartRocksQuery extends AbstractDataQuery {
    @Autowired
    StarRocksDao starRocksDao;

    @Override
    public List<Map<String, Object>> Query(String sql) throws SQLException {
        return starRocksDao.getListResult(sql, new ArrayList<>());
    }
}
