package com.ctrip.tour.business.dashboard.grpBusiness.job;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ctrip.tour.business.dashboard.grpBusiness.dao.starrocks.StarRocksCommonDao;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.group.workbenchsvc.contract.StructuredTableInfoType;
import com.ctrip.tour.group.workbenchsvc.contract.StructuredTableRowInfoType;
import com.ctrip.tour.rights.client.GroupWorkbenchServiceClient;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2025/3/25
 */
@Service
@Slf4j
public class FittingNpsAlertNotifyService extends CommonAlertNotifyService {

    private static final String CREATOR_EID = "CREATOR_EID";
    private static final String EVENT_TYPE = "CREATOR_EID";
    private static final String EVENT_TYPE_XXX_STRUCTURED_TABLE = "CREATOR_EID";
    private static final String TASK_MULT_PRICE_NEW_ALERT = "TASK_MULT_PRICE_NEW_ALERT";
    private static final String EVENT_MULT_PRICE_NEW_ALERT = "EVENT_MULT_PRICE_NEW_ALERT";
    private static final String EVENT_MULT_PRICE_NEW_ALERT_STRUCTURED_TABLE = "EVENT_MULT_PRICE_NEW_ALERT_STRUCTURED_TABLE";

    @Autowired
    private StarRocksCommonDao starRocksCommonDao;

    private static String NOTIFY_EVENT_EMPCODES = "notify.event.empcodes";

    private GroupWorkbenchServiceClient client = GroupWorkbenchServiceClient.getInstance();

    @Autowired
    private RemoteConfig remoteConfig;

    public void handleMultipriceNotify(String condition, String exceptionType) throws Exception {

        String whiteEmpCodesStr = remoteConfig.getExternalConfig(NOTIFY_EVENT_EMPCODES);

        List<String> whiteEmpCodes = Lists.newArrayList();
        if (StringUtils.isNotBlank(whiteEmpCodesStr)) {
            whiteEmpCodes = Splitter.on(",").splitToList(whiteEmpCodesStr);
        }

        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        String sql = "SELECT *\n" +
                "FROM dw_diydb.adm_prd_grp_avg_multiple_price_work_platform_df\n" +
                "WHERE d = '" + LocalDate.now().format(dtf) + "'\n" +
                "    AND view_date = '" + LocalDate.now().minusDays(1).format(dtf) + "'\n" +
                "    AND "+condition+" > 0\n" +
                "    AND sub_bu_type = '跟团游'";//NOSONAR

        List<Map<String, Object>> resultList = starRocksCommonDao.query(sql, Maps.newHashMap());
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }

        List<String> finalWhiteEmpCodes = whiteEmpCodes;
        Map<String, List<Map<String, Object>>> emNotifyInfoMap = resultList.stream()
                .filter(pw -> CollectionUtils.isEmpty(finalWhiteEmpCodes) || (Objects.nonNull(pw) && finalWhiteEmpCodes.contains((String) pw.get("pm_eid"))))
                .collect(Collectors.groupingBy(pw -> (String) pw.get("pm_eid")));

        emNotifyInfoMap.forEach((pmEid, pwList) -> {

            List<StructuredTableRowInfoType> rowInfoTypes = pwList.stream().map(pw -> {
                StructuredTableRowInfoType rowInfoType = new StructuredTableRowInfoType();

                Long productid = (Long) pw.get("productid");
                String destProvinceName = (String) pw.get("dest_province_name");
                Long vendorId = (Long) pw.get("vendor_id");
                String vendorName = (String) pw.get("vendor_name");
                Long pre1dTotalPv = (Long) pw.get("pre1d_total_pv");

                List<String> colList = Lists.newArrayList(exceptionType, destProvinceName, productid.toString(),
                        vendorId.toString(), vendorName, pre1dTotalPv.toString());
                rowInfoType.setColList(colList);
                return rowInfoType;
            }).collect(Collectors.toList());
            StructuredTableInfoType structuredTableInfoType = new StructuredTableInfoType();
            structuredTableInfoType.setRowList(rowInfoTypes);
            structuredTableInfoType.setHeaderList(Lists.newArrayList("异常原因", "目的地省份", "产品id", "供应商id", "供应商名称", "当日日历点击PV"));//NOSONAR
            String content = "价格倍数异常指标，请关注";//NOSONAR
            List<String> tpInfos = Lists.newArrayList("价格倍数异常指标，请关注");//NOSONAR
            notifyEmp(pmEid, structuredTableInfoType, "价格倍数异常通知",//NOSONAR
                    TASK_MULT_PRICE_NEW_ALERT, EVENT_MULT_PRICE_NEW_ALERT, EVENT_MULT_PRICE_NEW_ALERT_STRUCTURED_TABLE, content, tpInfos, "9999");//NOSONAR
        });


    }

}
