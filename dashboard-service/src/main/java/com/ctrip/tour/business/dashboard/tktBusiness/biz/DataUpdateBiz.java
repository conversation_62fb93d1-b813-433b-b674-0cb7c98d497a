package com.ctrip.tour.business.dashboard.tktBusiness.biz;

import com.ctrip.soa._24922.GetUpdateTimeRequestType;
import com.ctrip.soa._24922.GetUpdateTimeResponseType;

/**
 * <AUTHOR>
 * @date 2022/8/10
 */
public interface DataUpdateBiz {

    GetUpdateTimeResponseType getUpdateTime(GetUpdateTimeRequestType getUpdateTimeRequestType) throws Exception;

    String getUpdateTime() throws Exception;

    String getTaskBoardUpdateTime() throws Exception;
}
