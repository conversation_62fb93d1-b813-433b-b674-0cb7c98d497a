include 'BaijiCommonTypes.bjsc'
include 'getMetricData.bjsc'
namespace java 'com.ctrip.soa._24922'




class GetTaskLevelDimRequestType{

}


class GetTaskLevelDimResponseType{
    BaijiCommonTypes.ResponseStatusType responseStatus;
    list<TaskDimInfo> taskDimInfoList;
}


class TaskDimInfo{
    string code;
    //任务中文
    string value;
    string type;
    //没有子节点时该字段为[]
    list<TaskDimInfo> childrenList;
}


class TaskStatisticalScope{
    string code;
    string value;
}






class CheckTaskFlowPermissionRequestType{

}

class CheckTaskFlowPermissionResponseType{
    BaijiCommonTypes.ResponseStatusType responseStatus;
    //是否有权限
    bool havePermission;
    //任务看板按人员下钻的时候需要下发枚举值 directSubordinate(直接下属)  allSubordinate(全部下属) 该list为[]则不能按人员下钻
    list<string> subordinateTypeList;
    //无权限的时候需要展示的提示话术类型   other(其他人员  展示没有权限提示话术)
    string status;
    //domestic 国内业务   oversea海外业务  operate 信息运营
    list<TaskStatisticalScope> statisticalScopeList; 

}





class GetTaskFlowMetricCardDataRequestType{
    string dateType;
    string startDate;
    string endDate;
    list<TaskDimInfo> taskDimInfoList;
    //统计范围
    string statisticalScope;
    //任务重要度分数范围
    TaskLevelScoreRange taskLevelScoreRange;
}


class GetTaskFlowMetricCardDataResponseType{
    BaijiCommonTypes.ResponseStatusType responseStatus;
    map<string,double> dimData;
    //noData 没有数据展示文案  normal 正常进行流程
    string status;
}


class GetTaskFlowTrendlineDataRequestType{
    string dateType;
    string startDate;
    string endDate;
    list<TaskDimInfo> taskDimInfoList;
    //趋势线指标  枚举值为ontime_complete_rate(按时完成率)    overtime_event_cnt(超时任务数)   average_process_time(平均处理时长)	 new_task_cnt(新增任务数)
    string trendlineMetric;
    //统计范围
    string statisticalScope;
    //任务重要度分数范围
    TaskLevelScoreRange taskLevelScoreRange;
}


class GetTaskFlowTrendlineDataResponseType{
    //趋势线数据
    list<getMetricData.TrendLineDataItem> trendLineDataItemList;
    BaijiCommonTypes.ResponseStatusType responseStatus;
}





class GetTaskFlowTableDataRequestType{
    string dateType;
    string startDate;
    string endDate;
    list<TaskDimInfo> taskDimInfoList;
    //下钻类型  subordinate(下属)  task(任务)  region(大区)
    string drillDownType;
    //按下属下钻时 具体的类型 directSubordinate(直接下属) allSubordinate(全部下属)
    string subordinateType;
    //下钻指标  枚举值为ontime_complete_rate(按时完成率)    overtime_event_cnt(超时任务数)   average_process_time(平均处理时长)	 new_task_cnt(新增任务数)
    string drilldownMetric;
    //当前页数  
    int pageNo;
    //分页条数  对于首页以及详情页的气泡图可以传999
    int pageSize;
    //统计范围
    string statisticalScope;
    //任务重要度分数范围
    TaskLevelScoreRange taskLevelScoreRange;
}


class GetTaskFlowTableDataResponseType{
    //表格数据
    list<getMetricData.TableDataItem> tableDataItemList;
    //均值(用于大区排行)
    double avearageData;
    int totalNum;
    BaijiCommonTypes.ResponseStatusType responseStatus;
}


class GetTaskLevelScoreMappingRequestType{
    
}

class GetTaskLevelScoreMappingResponseType{
    //任务重要度等级开关,true为使用等级,false为不使用等级
    bool taskLevelSwitch;
    //任务重要度等级与分数范围的映射关系
    map<string,TaskLevelScoreRange> taskLevelScoreMapping;
    BaijiCommonTypes.ResponseStatusType responseStatus;
}
class TaskLevelScoreRange{
    int minScore;
    int maxScore;
}

class DownloadTaskFlowDataResponseType{
    BaijiCommonTypes.ResponseStatusType responseStatus;
    string fileUrl;
}

@serviceName='BIBusinessDashboardService'
@serviceNamespace='http://soa.ctrip.com/_24922'
service BIBusinessDashboardService {
    GetTaskLevelDimResponseType getTaskLevelDim(GetTaskLevelDimRequestType request);
    GetTaskFlowMetricCardDataResponseType getTaskFlowMetricCardData(GetTaskFlowMetricCardDataRequestType request);
    GetTaskFlowTableDataResponseType getTaskFlowTableData(GetTaskFlowTableDataRequestType request);
    CheckTaskFlowPermissionResponseType checkTaskFlowPermission(CheckTaskFlowPermissionRequestType request);
    GetTaskFlowTrendlineDataResponseType getTaskFlowTrendlineData(GetTaskFlowTrendlineDataRequestType request);
    GetTaskLevelScoreMappingResponseType getTaskLevelScoreMapping(GetTaskLevelScoreMappingRequestType request);
    DownloadTaskFlowDataResponseType downloadTaskFlowData(GetTaskFlowMetricCardDataRequestType request);
}