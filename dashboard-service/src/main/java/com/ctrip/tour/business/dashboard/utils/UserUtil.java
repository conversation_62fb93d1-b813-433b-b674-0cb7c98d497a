package com.ctrip.tour.business.dashboard.utils;

import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.google.common.collect.Lists;
import org.slf4j.MDC;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/22
 */
public class UserUtil {

    /**
     * 获取当前登录员工工号
     *
     * @return
     */
    public static String getEmpCode() {
        return MDC.get("empCode");
    }


    /**
     * 获取当前登录员工姓名
     *
     * @return
     */
    public static String getEmpName() {
        return MDC.get("empName");
    }



    /**
     * 经营业绩看板获取实际参与计算的员工工号
     * 1.当前登录的员工工号在特殊配置表里时  返回特殊配置表映射的员工工号
     * 2.当前登录的员工工号不在特殊配置表里时  直接返回当前登录的员工工号
     *
     * @param remoteConfig
     * @return
     */
    public static String getMappingEmpCode(RemoteConfig remoteConfig) {
        String empCode = getEmpCode();
        String mappingEmpCode = remoteConfig.getEmployeeRelationValue(empCode);
        if (mappingEmpCode == null) {
            MDC.put("mappingEmpCode", empCode);
            return empCode;
        }
//        if (mappingEmpCode == null) {
//            MDC.put("mappingEmpCode", "");
//            return "";
//        }
        MDC.put("mappingEmpCode", mappingEmpCode);
        return mappingEmpCode;
    }


    /**
     * 竞争力pk看板获取实际参与计算的员工工号
     * 1.当前登录的员工工号在特殊配置表里时  返回特殊配置表映射的员工工号
     * 2.当前登录的员工工号不在特殊配置表里时  直接返回当前登录的员工工号
     *
     * @param remoteConfig
     * @return
     */
    public static String getPkMappingEmpCode(RemoteConfig remoteConfig) {
        String empCode = getEmpCode();
        String mappingEmpCode = remoteConfig.getPkEmployeeRelationValue(empCode);
        if (mappingEmpCode == null) {
            MDC.put("pkMappingEmpCode", empCode);
            return empCode;
        }
        MDC.put("pkMappingEmpCode", mappingEmpCode);
        return mappingEmpCode;
    }





    /**
     * 获取任务流看板对应的映射员工号
     * @param remoteConfig
     * @return
     */
    public static String getTaskFlowMappingEmpCode(RemoteConfig remoteConfig) {
        String mappingEmpCode = getMappingEmpCode(remoteConfig);
        String admin = remoteConfig.getExternalConfig("admin");
//        //海外任务流看板未上线之前  先把老季映射成李哲
//        if (admin.equals(mappingEmpCode)) {
//            return remoteConfig.getExternalConfig("adminInferior").split("\\|")[0];
//        }
        return mappingEmpCode;
    }

    public static String getTaskFlowMappingEmpCodeByTaskStatisticalScope(RemoteConfig remoteConfig, String taskStatisticalScope) {
        String mappingEmpCode = getMappingEmpCode(remoteConfig);
        String admin = remoteConfig.getExternalConfig("admin");
        //海外任务流看板未上线之前  先把老季映射成李哲
        if (admin.equals(mappingEmpCode)) {
            String[] adminInferiors  = remoteConfig.getExternalConfig("adminInferior").split("\\|");
            if("domestic".equals(taskStatisticalScope)){
                return adminInferiors[0];
            } else if("oversea".equals(taskStatisticalScope)){
                return adminInferiors[1];
            }else if("operate".equals(taskStatisticalScope)) {
                return adminInferiors[2];
            }
            return adminInferiors[0];
        }
        return mappingEmpCode;
    }


    public static String getVbkLocale() {
        String vbkLocale = MDC.get("vbk-locale-lang");
        List<String> validLocaleList = Lists.newArrayList("zh-CN", "en-US");
        //如果是未知的语种 那么用中文兜底
        if (!validLocaleList.contains(vbkLocale)) {
            return "zh-CN";
        }
        return vbkLocale;
    }

    public static String getVbkLocaleForScenic() {
        return MDC.get("vbk-locale-lang");
    }


    public static String getAppName() {
        return MDC.get("appname");
    }
}
