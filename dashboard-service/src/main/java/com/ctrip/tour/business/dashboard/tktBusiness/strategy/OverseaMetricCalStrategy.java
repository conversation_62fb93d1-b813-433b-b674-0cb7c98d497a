package com.ctrip.tour.business.dashboard.tktBusiness.strategy;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBean;
import org.springframework.scheduling.annotation.Async;

import java.util.List;
import java.util.concurrent.Future;

public interface OverseaMetricCalStrategy {


    //获取单个指标指标卡数据
    @Async("metricCardExecutor")
    Future<List<MetricDetailInfo>> getOverseaSingleMetricCardData(TimeFilter timeFilter,
                                                                  OverseaMetricInfoBean metricInfoBean,
                                                                  String d,
                                                                  AvailableSubMetric availableSubMetric) throws Exception;



    //获取单个指标的趋势线数据
    GetOverseaTrendLineDataResponseType getOverseaSingleTrendlineData(GetOverseaTrendLineDataRequestType request,
                                                                      String d) throws Exception;




    //获取单个指标下钻基础数据
    GetOverseaDrillDownBaseInfoResponseType getOverseaSingleDrillDownBaseInfo(GetOverseaDrillDownBaseInfoRequestType request,
                                                                              String d,
                                                                              OverseaMetricInfoBean metricInfoBean) throws Exception;


    //获取单个指标下钻数据
    GetOverseaTableDataResponseType getOverseaSingleTableData(GetOverseaTableDataRequestType request,
                                                              String d,
                                                              OverseaMetricInfoBean metricInfoBean) throws Exception;


    //获取指标枚举值
    String getMetricName();

}
