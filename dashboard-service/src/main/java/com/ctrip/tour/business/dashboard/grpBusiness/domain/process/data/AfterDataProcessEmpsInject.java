package com.ctrip.tour.business.dashboard.grpBusiness.domain.process.data;

import com.ctrip.soa._24922.DSLRequestType;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.AbstractAfterDataProcess;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.model.ResultData;
import com.ctrip.tour.business.dashboard.grpBusiness.entity.EdwHrEmpVacation;
import com.ctrip.tour.business.dashboard.grpBusiness.service.DepTreeCache;

import java.util.Map;

public class AfterDataProcessEmpsInject extends AbstractAfterDataProcess {
    private DepTreeCache depTreeCache;
    private static volatile AfterDataProcessEmpsInject instance;

    public static AbstractAfterDataProcess getInstance(DepTreeCache depTreeCache) {
        if (instance == null) { // 第一次检查
            synchronized (AfterDataProcessEmpsInject.class) { // 加锁
                if (instance == null) { // 第二次检查
                    instance = new AfterDataProcessEmpsInject();
                    instance.depTreeCache = depTreeCache;
                }
            }
        }
        return instance;
    }

    @Override
    public ResultData process(DSLRequestType dslRequestType, ResultData data) {
        boolean containsPmEid = false;
        boolean containsLocalPmEid = false;
        if (dslRequestType.getGroupBy() != null && !dslRequestType.getGroupBy().isEmpty()) {
            for (String s : dslRequestType.getGroupBy()) {
                if (s.equals("pm_eid")) {
                    containsPmEid = true;
                }
                if (s.equals("local_pm_eid")) {
                    containsLocalPmEid = true;
                }
            }
        }
        if (containsPmEid || containsLocalPmEid) {
            for (Map<String, Object> v : data.getData()) {
                String s = "";
                if (containsPmEid) {
                    s = v.get("pm_eid").toString();
                    EdwHrEmpVacation emp = depTreeCache.getEmpByEmpID(s);
                    if (emp == null) {
                        continue;
                    }
                    v.put("pm_name", emp.getDisplayName());
                }
                if (containsLocalPmEid) {
                    s = v.get("local_pm_eid").toString();
                    EdwHrEmpVacation emp = depTreeCache.getEmpByEmpID(s);
                    if (emp == null) {
                        continue;
                    }
                    v.put("local_pm_name", emp.getDisplayName());
                }
            }
        }
        return data;
    }
}
