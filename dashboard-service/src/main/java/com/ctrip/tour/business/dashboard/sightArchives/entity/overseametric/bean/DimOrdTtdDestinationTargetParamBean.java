package com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.bean;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class DimOrdTtdDestinationTargetParamBean {
    String d;
    //业务线
    private List<String> buType;
    //考核年
    private String examineYear;
    //考核季
    private List<String> examineQuaters;
    //业务大区
    private List<String> businessRegionName;
    //业务子区域
    private List<String> businessSubRegionName;
    //C/T站
    private List<String> CT;
    //考核指标类型
    private String examineMetricType;
}
