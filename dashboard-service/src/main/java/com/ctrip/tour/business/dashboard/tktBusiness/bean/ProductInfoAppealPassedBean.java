package com.ctrip.tour.business.dashboard.tktBusiness.bean;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 照妖镜 产品信息申诉通过场景的json格式
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ProductInfoAppealPassedBean {
    String vendorId;  // 供应商id
    String vendorName;  // 供应商name
    Long productId;  // 产品id
    String productName;  // 产品名字
}
