package com.ctrip.tour.business.dashboard.biz.impl;

import com.ctrip.framework.cdubbo.internal.util.JacksonUtil;
import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.soa._24922.GetTrendLineDataRequestType;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.*;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.impl.SinglePeriodTrendLineBizImpl;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.*;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.MetricHelper;
import com.ctrip.tour.business.dashboard.utils.MapperUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.boot.test.context.SpringBootTest;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

import static org.junit.Assert.assertNotNull;
import static org.mockito.Matchers.*;





@SpringBootTest
@PowerMockIgnore({"javax.net.ssl.*", "javax.management.*", "javax.crypto.*"})
@RunWith(PowerMockRunner.class)  //告诉JUnit使用PowerMockRunner进行测试
@PrepareForTest({SinglePeriodTrendLineBizImpl.class, MetricHelper.class})
//所有需要测试的类列在此处，适用于模拟final类或有final, private, static, native方法的类
public class SinglePeriodTrendLineBizImplUnitTest {

    @InjectMocks
    private SinglePeriodTrendLineBizImpl singlePeriodTrendLineBiz;

    @Mock
    private Bus1Dao bus1Dao;

    @Mock
    private Bus2Dao bus2Dao;

    @Mock
    private Bus3Dao bus3Dao;

    @Mock
    private Bus4Dao bus4Dao;

    @Mock
    private Bus567Dao bus567Dao;

    @Mock
    private Bus8Dao bus8Dao;

    @Mock
    private Bus9NewDao bus9NewDao;

    @Mock
    private RemoteConfig remoteConfig;

    @Before
    public void init() throws Exception {
        MockitoAnnotations.initMocks(this);


        PowerMockito.mockStatic(MetricHelper.class);
        PowerMockito.when(MetricHelper.getLevelColumnName(anyString())).thenReturn("");

        PowerMockito.when(MetricHelper.getDrillDownColumnName(anyString())).then((Answer<String>) invocation -> {
            String input = (String) invocation.getArguments()[0];
            switch (input) {
                case "region_name":
                    return "region_name";
                case "province_name":
                    return "province_name";
                case "viewspotid":
                    return "viewspotid";
                default:
                    return "default";
            }
        });

        PowerMockito.when(MetricHelper.canDrillDown(anyString(), anyString())).thenReturn(true);


        PowerMockito.when(remoteConfig.getConfigValue(anyString())).then((Answer<String>) invocation -> {
            String input = (String) invocation.getArguments()[0];
            switch (input) {
                case "domestic":
                    return "domestic";
                case "three":
                    return "three";
                case "region":
                    return "region";
                case "province":
                    return "province";
                case "viewspot":
                    return "viewspot";
                default:
                    return "default";
            }
        });


        PowerMockito.doNothing().when(bus1Dao).getMetricCardDataAsync(anyString(), anyMap(), anyMap(), any(DalHints.class));
        PowerMockito.doNothing().when(bus1Dao).getOveralltargetDataAsync(anyString(), anyMap(), anyMap(), anyList(), any(DalHints.class));
        PowerMockito.doNothing().when(bus1Dao).getMetricCardMomDataAsync(anyString(), anyMap(), anyMap(), anyMap(), any(DalHints.class));

        List<Object> gmvReachItem = new ArrayList<>();
        gmvReachItem.add(1.269E9);
        gmvReachItem.add(4.736E8);


        List<Object> gmvTagretItem = new ArrayList<>();
        gmvTagretItem.add(4.736E8);


        List<Object> gmvMomItem = new ArrayList<>();
        gmvMomItem.add(1.269E9);

        DalHints dalHints = PowerMockito.mock(DalHints.class);
        PowerMockito.whenNew(DalHints.class).withAnyArguments().thenReturn(dalHints);
        PowerMockito.when(dalHints.asyncExecution()).thenReturn(dalHints);


        PowerMockito.doNothing().when(bus2Dao).getMetricCardDataAsync(anyString(), anyMap(), anyMap(), any(DalHints.class));
        PowerMockito.doNothing().when(bus2Dao).getOveralltargetDataAsync(anyString(), anyMap(), anyMap(), anyList(), any(DalHints.class));
        PowerMockito.doNothing().when(bus2Dao).getMetricCardMomDataAsync(anyString(), anyMap(), anyMap(), anyMap(), any(DalHints.class));

        List<Object> profitReachItem = new ArrayList<>();
        profitReachItem.add(1.269E9);
        profitReachItem.add(4.736E8);


        List<Object> profitTagretItem = new ArrayList<>();
        profitTagretItem.add(4.736E8);


        List<Object> profitMomItem = new ArrayList<>();
        profitMomItem.add(1.269E9);

        //gmv
        PowerMockito.when(dalHints.getListResult()).thenReturn(Collections.singletonList(gmvMomItem))
                .thenReturn(Collections.singletonList(gmvMomItem))
                .thenReturn(Collections.singletonList(gmvMomItem))
                .thenReturn(Collections.singletonList(gmvReachItem))
                .thenReturn(Collections.singletonList(gmvTagretItem))
                //毛利
                .thenReturn(Collections.singletonList(profitMomItem))
                .thenReturn(Collections.singletonList(profitMomItem))
                .thenReturn(Collections.singletonList(profitMomItem))
                .thenReturn(Collections.singletonList(profitReachItem))
                .thenReturn(Collections.singletonList(profitTagretItem));

        List<Object> qaReachData = new ArrayList<>();
        qaReachData.add(6.8E7);
        qaReachData.add(2.18E9);

        List<Object> qaTargetData = new ArrayList<>();
        qaTargetData.add(0);

        List<Object> qaDrillDownReachData = new ArrayList<>();
        qaDrillDownReachData.add("region1");
        qaDrillDownReachData.add(6.8E7);
        qaDrillDownReachData.add(2.18E9);

        List<Object> qaDrillDownTargetData = new ArrayList<>();
        qaDrillDownTargetData.add("region1");
        qaDrillDownTargetData.add(0.1232);


        //质量成本
        PowerMockito.when(bus3Dao.getTrendlineReachData(anyMap(), anyList(),anyString()))
                .thenReturn(Collections.singletonList(qaReachData))
                .thenReturn(Collections.singletonList(qaDrillDownReachData));
        PowerMockito.when(bus3Dao.getSpilitTargetData(anyMap(), anyList(),anyString()))
                .thenReturn(Collections.singletonList(qaTargetData))
                .thenReturn(Collections.singletonList(qaDrillDownTargetData));



        List<Object> scineSignReachData = new ArrayList<>();
        scineSignReachData.add(0.653);

        List<Object> scineSignDrillDownReachData = new ArrayList<>();
        scineSignDrillDownReachData.add("region1");
        scineSignDrillDownReachData.add(0.653);

        List<Object> scineSignStackData = new ArrayList<>();
        scineSignStackData.add("1");
        scineSignStackData.add(170);

        //直签
//        PowerMockito.when(bus4Dao.getCompleteRateTrendlineData(anyMap(), anyList(), anyList()))
//                .thenReturn(Collections.singletonList(scineSignReachData))
//                .thenReturn(Collections.singletonList(scineSignDrillDownReachData));
        PowerMockito.when(bus4Dao.getTrendlineSignScenicCnt(anyMap(), anyList(), anyList()))
                .thenReturn(Collections.singletonList(scineSignStackData));


        List<Object> weaknessData = new ArrayList<>();
        weaknessData.add(0);
        weaknessData.add(0);
        weaknessData.add(91);
        weaknessData.add(0.07);
        weaknessData.add(4469);
        weaknessData.add(0.625);

        List<Object> weaknessDrillDownData = new ArrayList<>();
        weaknessDrillDownData.add("region1");
        weaknessDrillDownData.add(0);
        weaknessDrillDownData.add(0);
        weaknessDrillDownData.add(91);
        weaknessDrillDownData.add(0.07);
        weaknessDrillDownData.add(4469);
        weaknessDrillDownData.add(0.625);

        //劣势
        PowerMockito.when(bus567Dao.getDataWithOutDrillDown(anyMap(),anyString())).thenReturn(Collections.singletonList(weaknessData));
        PowerMockito.when(bus567Dao.getDataWithDrillDown(anyMap(),anyList(),anyString())).thenReturn(Collections.singletonList(weaknessDrillDownData));

        List<Object> activityReachData = new ArrayList<>();
        activityReachData.add("region1");
        activityReachData.add(123);
        activityReachData.add(1230);

        //活动覆盖
        PowerMockito.when(bus8Dao.getTrendlineData(anyMap(),anyList())).thenReturn(Collections.singletonList(activityReachData));


        List<Object> categoryReachData = new ArrayList<>();
        categoryReachData.add("region1");
        categoryReachData.add(0.9542);

        //品类覆盖
        PowerMockito.when(bus9NewDao.getMultiCategoryTrendlineData(anyMap(),anyList(),anyList())).thenReturn(Collections.singletonList(categoryReachData));




    }
@Test
public void jsonTest(){
        String json="{\n" +
                "    \"competitiveTargetDetailConfigBeans\": [\n" +
                "        {\n" +
                "            \"year\": \"2025\",\n" +
                "            \"brand\": \"klk\",\n" +
                "            \"metric\": \"105\",\n" +
                "            \"staticType\": \"general\",\n" +
                "            \"Q1\": 5,\n" +
                "            \"Q2\": 5,\n" +
                "            \"Q3\": 5,\n" +
                "            \"Q4\": 5,\n" +
                "            \"YEART\": 5,\n" +
                "            \"H1\": 5,\n" +
                "            \"H2\": 5\n" +
                "        },\n" +
                "        {\n" +
                "            \"year\": \"2025\",\n" +
                "            \"brand\": \"klk\",\n" +
                "            \"metric\": \"105\",\n" +
                "            \"staticType\": \"core\",\n" +
                "            \"Q1\": 1,\n" +
                "            \"Q2\": 1,\n" +
                "            \"Q3\": 1,\n" +
                "            \"Q4\": 1,\n" +
                "            \"YEART\": 1,\n" +
                "            \"H1\": 1,\n" +
                "            \"H2\": 1\n" +
                "        }     \n" +
                "    ]\n" +
                "}";
    CompetitiveTargetConfigBean bean= MapperUtil.str2Obj(json, CompetitiveTargetConfigBean.class);

    System.out.println(bean);


}

    @Test
    public void sds() {
        List<DomesticDrillBaseInfoBean> stringlist = new ArrayList<>();
        DomesticDrillBaseInfoBean bean = new DomesticDrillBaseInfoBean();
        bean.setMetric(Arrays.asList("1", "2"));
        bean.setBusinessId("1");
        bean.setSubBusinessId("2");
        bean.setFields(Arrays.asList("大区", "省份"));
        bean.setNeedTrend(true);
        bean.setNeedBubble(true);
        stringlist.add(bean);
        DomesticDrillDownBaseInfoBean bean1 = new DomesticDrillDownBaseInfoBean();
        bean1.setDomesticDrillBaseInfoBeanList(stringlist);
        String json = MapperUtil.obj2Str(bean1);
        System.out.println(json);
        DomesticDrillDownBaseInfoBean copyBean = MapperUtil.str2Obj(json, DomesticDrillDownBaseInfoBean.class);

        System.out.println(copyBean);
    }


    @Test
    public void testAll() throws Exception {
        testGetBus1SinglePeriodTrendLineData();
        testGetBus2SinglePeriodTrendLineData();
        testGetBus3SinglePeriodTrendLineData();
        testGetBus4SinglePeriodTrendLineData();
        testGetBus567SinglePeriodTrendLineData();
        testGetBus8SinglePeriodTrendLineData();
        testGetBus9SinglePeriodTrendLineData();
    }


    private void testGetBus1SinglePeriodTrendLineData() throws Exception {


        String strRequest = "{\"timeFilter\":{\"dateType\":\"quarter\",\"year\":\"2022\",\"quarter\":\"Q4\"},\"domainName\":\"zheli\"}";
        GetTrendLineDataRequestType request = MapperUtil.str2Obj(strRequest, GetTrendLineDataRequestType.class);

        String strExamineConfig = "{\"year\":\"2023\",\"month\":\"01\",\"quarter\":\"Q1\",\"isLastestPeriod\":true,\"dateType\":\"quarter\",\"timeMap\":{\"currentTime\":\"2023-Q1\",\"lastyearTime\":\"2022-Q1\",\"2019Time\":\"2020-Q1\"},\"limitTimeMap\":{},\"businessDashboardExamineeConfigV2\":{\"domainName\":\"zheli\",\"role\":\"8\",\"year\":\"2023\",\"quarter\":\"Q1\",\"examineMetric\":\"1;2\",\"examineType\":\"1\",\"examineLevel\":\"domestic\",\"queryD\":\"2023-03-03\"}}";
        ExamineConfigBean examineConfig = MapperUtil.str2Obj(strExamineConfig, ExamineConfigBean.class);

        String d = "2023-03-03";

        Future<SinglePeriodDataBean> beanFuture = singlePeriodTrendLineBiz.getBus1SinglePeriodTrendLineData(request, examineConfig, d);
        SinglePeriodDataBean bean = beanFuture.get();
        assertNotNull(bean.getPeriod2019List());

    }

    private void testGetBus2SinglePeriodTrendLineData() throws Exception {
        String strRequest = "{\"timeFilter\":{\"dateType\":\"quarter\",\"year\":\"2022\",\"quarter\":\"Q4\"},\"domainName\":\"zheli\"}";
        GetTrendLineDataRequestType request = MapperUtil.str2Obj(strRequest, GetTrendLineDataRequestType.class);

        String strExamineConfig = "{\"year\":\"2023\",\"month\":\"01\",\"quarter\":\"Q1\",\"isLastestPeriod\":true,\"dateType\":\"quarter\",\"timeMap\":{\"currentTime\":\"2023-Q1\",\"lastyearTime\":\"2022-Q1\",\"2019Time\":\"2020-Q1\"},\"limitTimeMap\":{},\"businessDashboardExamineeConfigV2\":{\"domainName\":\"zheli\",\"role\":\"8\",\"year\":\"2023\",\"quarter\":\"Q1\",\"examineMetric\":\"1;2\",\"examineType\":\"1\",\"examineLevel\":\"domestic\",\"queryD\":\"2023-03-03\"}}";
        ExamineConfigBean examineConfig = MapperUtil.str2Obj(strExamineConfig, ExamineConfigBean.class);

        String d = "2023-03-03";

        Future<SinglePeriodDataBean> beanFuture = singlePeriodTrendLineBiz.getBus2SinglePeriodTrendLineData(request, examineConfig, d);
        SinglePeriodDataBean bean = beanFuture.get();
        assertNotNull(bean.getPeriod2019List());
    }


    private void testGetBus3SinglePeriodTrendLineData() throws SQLException, ExecutionException, InterruptedException {
        String strRequest = "{\"metric\":\"3\",\"domainName\":\"zheli\",\"timeFilter\":{\"dateType\":\"month\",\"year\":\"2023\",\"month\":\"03\",\"timeFrame\":\"6\"},\"queryType\":\"trendline\"}";
        GetTrendLineDataRequestType request = MapperUtil.str2Obj(strRequest, GetTrendLineDataRequestType.class);

        String strExamineConfig = "{\"year\":\"2023\",\"month\":\"01\",\"quarter\":\"Q1\",\"isLastestPeriod\":true,\"dateType\":\"quarter\",\"timeMap\":{\"currentTime\":\"2023-Q1\",\"lastyearTime\":\"2022-Q1\",\"2019Time\":\"2020-Q1\"},\"limitTimeMap\":{},\"businessDashboardExamineeConfigV2\":{\"domainName\":\"zheli\",\"role\":\"8\",\"year\":\"2023\",\"quarter\":\"Q1\",\"examineMetric\":\"3\",\"examineType\":\"1\",\"examineLevel\":\"domestic\",\"queryD\":\"2023-03-03\"}}";
        ExamineConfigBean examineConfig = MapperUtil.str2Obj(strExamineConfig, ExamineConfigBean.class);

        String d = "2023-03-03";

        Future<SinglePeriodDataBean> beanFuture = singlePeriodTrendLineBiz.getBus3SinglePeriodTrendLineData(request, examineConfig, d);
        SinglePeriodDataBean bean = beanFuture.get();
        assertNotNull(bean.getPeriodReachList());


        String strRequest1 = "{\"metric\":\"3\",\"domainName\":\"zheli\",\"timeFilter\":{\"dateType\":\"month\",\"year\":\"2023\",\"month\":\"02\",\"timeFrame\":\"6\"},\"queryType\":\"drilldown\",\"drillDownFilter\":{\"field\":\"region_name\",\"fieldValueList\":[]}}";
        GetTrendLineDataRequestType request1 = MapperUtil.str2Obj(strRequest1, GetTrendLineDataRequestType.class);

        Future<SinglePeriodDataBean> beanFuture1 = singlePeriodTrendLineBiz.getBus3SinglePeriodTrendLineData(request1, examineConfig, d);
        SinglePeriodDataBean bean1 = beanFuture1.get();
        assertNotNull(bean1.getPeriodReachList());
    }

    private void testGetBus4SinglePeriodTrendLineData() throws SQLException, ExecutionException, InterruptedException {
        String strRequest = "{\"metric\":\"4\",\"domainName\":\"zheli\",\"timeFilter\":{\"dateType\":\"month\",\"year\":\"2023\",\"month\":\"03\",\"timeFrame\":\"6\"},\"queryType\":\"trendline\"}";
        GetTrendLineDataRequestType request = MapperUtil.str2Obj(strRequest, GetTrendLineDataRequestType.class);

        String strExamineConfig = "{\"year\":\"2023\",\"month\":\"01\",\"quarter\":\"Q1\",\"isLastestPeriod\":true,\"dateType\":\"quarter\",\"timeMap\":{\"currentTime\":\"2023-Q1\",\"lastyearTime\":\"2022-Q1\",\"2019Time\":\"2020-Q1\"},\"limitTimeMap\":{},\"businessDashboardExamineeConfigV2\":{\"domainName\":\"zheli\",\"role\":\"8\",\"year\":\"2023\",\"quarter\":\"Q1\",\"examineMetric\":\"3\",\"examineType\":\"1\",\"examineLevel\":\"domestic\",\"queryD\":\"2023-03-03\"}}";
        ExamineConfigBean examineConfig = MapperUtil.str2Obj(strExamineConfig, ExamineConfigBean.class);

        String d = "2023-03-03";

        Future<SinglePeriodDataBean> beanFuture = singlePeriodTrendLineBiz.getBus4SinglePeriodTrendLineData(request, examineConfig, d);
        SinglePeriodDataBean bean = beanFuture.get();
        assertNotNull(bean.getPeriodReachList());


        String strRequest1 = "{\"metric\":\"4\",\"domainName\":\"zheli\",\"timeFilter\":{\"dateType\":\"month\",\"year\":\"2023\",\"month\":\"02\",\"timeFrame\":\"6\"},\"queryType\":\"drilldown\",\"drillDownFilter\":{\"field\":\"region_name\",\"fieldValueList\":[]}}";
        GetTrendLineDataRequestType request1 = MapperUtil.str2Obj(strRequest1, GetTrendLineDataRequestType.class);

        Future<SinglePeriodDataBean> beanFuture1 = singlePeriodTrendLineBiz.getBus4SinglePeriodTrendLineData(request1, examineConfig, d);
        SinglePeriodDataBean bean1 = beanFuture1.get();
        assertNotNull(bean1.getPeriodReachList());
    }


    private void testGetBus567SinglePeriodTrendLineData() throws Exception {
        String strRequest = "{\"metric\":\"5\",\"domainName\":\"zheli\",\"timeFilter\":{\"dateType\":\"month\",\"year\":\"2023\",\"month\":\"03\",\"timeFrame\":\"6\"},\"queryType\":\"trendline\"}";
        GetTrendLineDataRequestType request = MapperUtil.str2Obj(strRequest, GetTrendLineDataRequestType.class);

        String strExamineConfig = "{\"year\":\"2023\",\"month\":\"01\",\"quarter\":\"Q1\",\"isLastestPeriod\":true,\"dateType\":\"quarter\",\"timeMap\":{\"currentTime\":\"2023-Q1\",\"lastyearTime\":\"2022-Q1\",\"2019Time\":\"2020-Q1\"},\"limitTimeMap\":{},\"businessDashboardExamineeConfigV2\":{\"domainName\":\"zheli\",\"role\":\"8\",\"year\":\"2023\",\"quarter\":\"Q1\",\"examineMetric\":\"3\",\"examineType\":\"1\",\"examineLevel\":\"domestic\",\"queryD\":\"2023-03-03\"}}";
        ExamineConfigBean examineConfig = MapperUtil.str2Obj(strExamineConfig, ExamineConfigBean.class);

        String d = "2023-03-03";

        Future<SinglePeriodDataBean> beanFuture = singlePeriodTrendLineBiz.getBus567SinglePeriodTrendLineData(request, examineConfig, d, "5");
        SinglePeriodDataBean bean = beanFuture.get();
        assertNotNull(bean.getPeriodWeaknessList());


        String strRequest1 = "{\"metric\":\"5\",\"domainName\":\"zheli\",\"timeFilter\":{\"dateType\":\"month\",\"year\":\"2023\",\"month\":\"02\",\"timeFrame\":\"6\"},\"queryType\":\"drilldown\",\"drillDownFilter\":{\"field\":\"region_name\",\"fieldValueList\":[]}}";
        GetTrendLineDataRequestType request1 = MapperUtil.str2Obj(strRequest1, GetTrendLineDataRequestType.class);

        Future<SinglePeriodDataBean> beanFuture1 = singlePeriodTrendLineBiz.getBus567SinglePeriodTrendLineData(request1, examineConfig, d, "5");
        SinglePeriodDataBean bean1 = beanFuture1.get();
        assertNotNull(bean1.getPeriodWeaknessList());
    }

    private void testGetBus8SinglePeriodTrendLineData() throws ExecutionException, InterruptedException, SQLException {
        String strRequest1 = "{\"metric\":\"8\",\"domainName\":\"zheli\",\"timeFilter\":{\"dateType\":\"month\",\"year\":\"2023\",\"month\":\"02\",\"timeFrame\":\"6\"},\"queryType\":\"drilldown\",\"drillDownFilter\":{\"field\":\"region_name\",\"fieldValueList\":[]}}";
        GetTrendLineDataRequestType request1 = MapperUtil.str2Obj(strRequest1, GetTrendLineDataRequestType.class);

        String d = "2023-03-03";

        String strExamineConfig = "{\"year\":\"2022\",\"month\":\"09\",\"quarter\":\"Q3\",\"isLastestPeriod\":true,\"dateType\":\"quarter\",\"timeMap\":{\"currentTime\":\"2023-Q1\",\"lastyearTime\":\"2022-Q1\",\"2019Time\":\"2020-Q1\"},\"limitTimeMap\":{},\"businessDashboardExamineeConfigV2\":{\"domainName\":\"zheli\",\"role\":\"8\",\"year\":\"2023\",\"quarter\":\"Q1\",\"examineMetric\":\"3\",\"examineType\":\"1\",\"examineLevel\":\"domestic\",\"queryD\":\"2023-03-03\"}}";
        ExamineConfigBean examineConfig = MapperUtil.str2Obj(strExamineConfig, ExamineConfigBean.class);

        Future<SinglePeriodDataBean> beanFuture1 = singlePeriodTrendLineBiz.getBus8SinglePeriodTrendLineData(request1, examineConfig, d);
        SinglePeriodDataBean bean1 = beanFuture1.get();
        assertNotNull(bean1.getPeriodReachList());
    }


    private void testGetBus9SinglePeriodTrendLineData() throws SQLException, ExecutionException, InterruptedException {
        String strRequest1 = "{\"metric\":\"9\",\"domainName\":\"zheli\",\"timeFilter\":{\"dateType\":\"month\",\"year\":\"2023\",\"month\":\"02\",\"timeFrame\":\"6\"},\"queryType\":\"drilldown\",\"drillDownFilter\":{\"field\":\"region_name\",\"fieldValueList\":[]}}";
        GetTrendLineDataRequestType request1 = MapperUtil.str2Obj(strRequest1, GetTrendLineDataRequestType.class);

        String d = "2023-03-03";

        String strExamineConfig = "{\"year\":\"2023\",\"month\":\"02\",\"quarter\":\"Q1\",\"isLastestPeriod\":true,\"dateType\":\"quarter\",\"timeMap\":{\"currentTime\":\"2023-Q1\",\"lastyearTime\":\"2022-Q1\",\"2019Time\":\"2020-Q1\"},\"limitTimeMap\":{},\"businessDashboardExamineeConfigV2\":{\"domainName\":\"zheli\",\"role\":\"8\",\"year\":\"2023\",\"quarter\":\"Q1\",\"examineMetric\":\"3\",\"examineType\":\"1\",\"examineLevel\":\"domestic\",\"queryD\":\"2023-03-03\"}}";
        ExamineConfigBean examineConfig = MapperUtil.str2Obj(strExamineConfig, ExamineConfigBean.class);

        Future<SinglePeriodDataBean> beanFuture1 = singlePeriodTrendLineBiz.getBus9SinglePeriodTrendLineData(request1, examineConfig, d);
        SinglePeriodDataBean bean1 = beanFuture1.get();
        assertNotNull(bean1.getPeriodReachList());
    }

}
