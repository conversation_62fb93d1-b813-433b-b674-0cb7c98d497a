package com.ctrip.tour.business.dashboard.tktBusiness.helper;

import com.ctrip.soa._24922.*;
import com.ctrip.soa._27181.GetRawDataResponseType;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.*;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.ctrip.tour.business.dashboard.utils.MapperUtil;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Table;
import org.apache.commons.lang.StringUtils;

import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

public class Bus1And2DayTourHelper {

    private static Table<String, String, String> extraTargetTable = HashBasedTable.create();

    private static Table<String, String, String> extraDimTable = HashBasedTable.create();

    private static Table<String, String, String> completeTable = HashBasedTable.create();

    private static Table<String, String, Long> meticCardIdTable = HashBasedTable.create();

    private final static Integer BUSINESS_LINE=1;
    static {

        extraTargetTable.put("domesticDayTour","1","odt_trgt_income");
        extraTargetTable.put("domesticDayTour","2","odt_trgt_profit");
        extraTargetTable.put("overseaDayTour","1","odt_ob_trgt_income");
        extraTargetTable.put("overseaDayTour","2","odt_ob_trgt_profit");

        extraDimTable.put("domesticDayTour","1","odt_suc_income");
        extraDimTable.put("domesticDayTour","2","odt_suc_profit");
        extraDimTable.put("overseaDayTour","1","odt_ob_suc_income");
        extraDimTable.put("overseaDayTour","2","odt_ob_suc_profit");


        completeTable.put("domesticDayTour","1","odt_suc_income|odt_trgt_income|/");
        completeTable.put("domesticDayTour","2","odt_suc_profit|odt_trgt_profit|/");
        completeTable.put("overseaDayTour","1","odt_ob_suc_income|odt_ob_trgt_income|/");
        completeTable.put("overseaDayTour","2","odt_ob_suc_profit|odt_ob_trgt_profit|/");


        meticCardIdTable.put("domesticDayTour","current",17L);
        meticCardIdTable.put("domesticDayTour","target",19L);
        meticCardIdTable.put("domesticDayTour","other",18L);
        meticCardIdTable.put("overseaDayTour","current",42L);
        meticCardIdTable.put("overseaDayTour","target",44L);
        meticCardIdTable.put("overseaDayTour","other",43L);
    }


    public static SqlParamterBean getMetricCardSqlBean(String type,
                                                       String momType,
                                                       TimeFilter timeFilter,
                                                       MetricInfoBean metricInfoBean,
                                                       String d,
                                                       RemoteConfig remoteConfig,
                                                       ExamineConfigBean examineConfigBean,
                                                       String subMetric) throws Exception {
        SqlParamterBean bean = new SqlParamterBean();
        Long id = GeneralUtil.isNotEmpty(meticCardIdTable.get(subMetric, type)) ?
                meticCardIdTable.get(subMetric, type) : meticCardIdTable.get(subMetric, "other");
        bean.setId(id);

        if ("mom".equals(type)) {
            List<Map<String, String>> baseMapList = generateMomBaseMapList(momType, timeFilter, d);
            Map<String, String> baseMap = baseMapList.get(0);
            setExamineRangeValue(baseMap, metricInfoBean, remoteConfig, subMetric);
            bean.setAndMap(baseMap);
            Map<String, String> baseMap2 = baseMapList.get(1);
            setExamineRangeValue(baseMap2, metricInfoBean, remoteConfig, subMetric);
            bean.setAndMap2(baseMap2);
        } else {
            Map<String, String> baseMap = generateBaseMap(timeFilter, type, d, examineConfigBean);
            setExamineRangeValue(baseMap, metricInfoBean, remoteConfig, subMetric);
            bean.setAndMap(baseMap);
        }


        return bean;
    }


    private static List<Map<String, String>> generateMomBaseMapList(String momType,
                                                                    TimeFilter timeFilter,
                                                                    String d) throws ParseException {

        String lastDay = DateUtil.getDayOfInterval(d, -1);
        String dateType = timeFilter.getDateType();
        String year = timeFilter.getYear();
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();
        Map<String, String> momMap = new HashMap<>();
        Map<String, String> momMap2 = new HashMap<>();
        List<Map<String, String>> resultList = new ArrayList<>();
        resultList.add(momMap);
        resultList.add(momMap2);
        momMap.put("query_d", d);
        momMap2.put("query_d", d);
        if ("7days".equals(momType) || "30days".equals(momType)) {
            int interval = "7days".equals(momType) ? -6 : -29;
            String startDate = DateUtil.getDayOfInterval(lastDay, interval);
            momMap.put("the_date", startDate + "|" + lastDay);

            String momEndDate = DateUtil.getDayOfInterval(startDate, -1);
            String momStartDate = DateUtil.getDayOfInterval(momEndDate, interval);
            momMap2.put("the_date", momStartDate + "|" + momEndDate);
        } else {
            String time = DateUtil.getInputTime(dateType, month, quarter, null);
            momMap.put("year", year);
            momMap.put(dateType, time);
            List<String> lastTimeInfo = DateUtil.getLastTimeInfoV2(timeFilter);

            momMap2.put("year", lastTimeInfo.get(0));
            momMap2.put(dateType, lastTimeInfo.get(1));
        }
        return resultList;
    }

    private static Map<String, String> generateBaseMap(TimeFilter timeFilter,
                                                       String type,
                                                       String d,
                                                       ExamineConfigBean examineConfigBean) throws Exception {
        return OverseaMetricHelper.generateBaseMap(timeFilter, type, d, examineConfigBean);
    }

    private static void setExamineRangeValue(Map<String, String> baseMap,
                                             MetricInfoBean metricInfoBean,
                                             RemoteConfig remoteConfig,
                                             String subMetric) {

        String region = remoteConfig.getConfigValue("region");
        String province = remoteConfig.getConfigValue("province");

        List<String> odtRegionList = metricInfoBean.getOdtRegionList();
        String odtLevel = metricInfoBean.getOdtLevel();
        List<String> overseaOdtRegionList = metricInfoBean.getOverseaOdtRegionList();
        String overseaOdtLevel = metricInfoBean.getOverseaOdtLevel();
        if("domesticDayTour".equals(subMetric)){
            if (GeneralUtil.isNotEmpty(odtRegionList)) {
                if (region.equals(odtLevel)) {
                    baseMap.put("region_name", StringUtils.join(odtRegionList, "|"));
                } else if (province.equals(odtLevel)) {
                    baseMap.put("province_name", StringUtils.join(odtRegionList, "|"));
                }
            }
        }else{
            if (GeneralUtil.isNotEmpty(overseaOdtRegionList)) {
                if (region.equals(overseaOdtLevel)) {
                    baseMap.put("region_name", StringUtils.join(overseaOdtRegionList, "|"));
                } else if (province.equals(overseaOdtLevel)) {
                    baseMap.put("province_name", StringUtils.join(overseaOdtRegionList, "|"));
                }
            }
        }


    }

    public static String getMomType(TimeFilter timeFilter,
                                    String d) throws ParseException {
        String lastDay = DateUtil.getDayOfInterval(d, -1);
        String dateType = timeFilter.getDateType();
        String year = timeFilter.getYear();
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();
        if ("month".equals(dateType)) {
            return DateUtil.isCurrentMonthV2(lastDay, year, month) ? "7days" : "lastmonth";
        } else {
            return DateUtil.isCurrentQuarterV2(lastDay, year, quarter) ? "30days" : "lastquarter";
        }
    }


    //填充指标卡基础数据
    public static void processMetricCardBaseData(GetRawDataResponseType currentRes,
                                                 GetRawDataResponseType targetRes,
                                                 Map<String, Double> dimMap) {
        ChartHelper.fillOverallDimMap(MapperUtil.str2ListList(currentRes.getResult(), Object.class), currentRes.getMetricList(), dimMap);
        ChartHelper.fillOverallDimMap(MapperUtil.str2ListList(targetRes.getResult(), Object.class), targetRes.getMetricList(), dimMap);

    }

    //填充指标卡完成率数据
    public static void processMetricCardCompleteData(Map<String, Double> dimMap,
                                                     String metric,
                                                     String subMetric) {
        String dim = completeTable.get(subMetric, metric);
        if (GeneralUtil.isEmpty(dim)) {
            return;
        }
        String[] dimArray = dim.split("\\|");
        Double fenzi = dimMap.get(dimArray[0]);
        Double fenmu = dimMap.get(dimArray[1]);
        dimMap.put(dim, GeneralUtil.getComplexResult(fenzi, fenmu, dimArray[2], false));
    }

    //填充同比数据
    public static void processMetricCardPopData(GetRawDataResponseType popRes,
                                                Map<String, Double> dimMap,
                                                String metric,
                                                String type,
                                                String subMetric) {
        Map<String, Double> popMap = new HashMap<>();
        ChartHelper.fillOverallDimMap(MapperUtil.str2ListList(popRes.getResult(), Object.class), popRes.getMetricList(), popMap);
        String dim = extraDimTable.get(subMetric, metric);
        String extraDim = dim + "_" + type;
        String extraValueDim = extraDim + "_value";
        dimMap.put(extraValueDim, popMap.get(dim));
        dimMap.put(extraDim, DimHelper.getSpecialDimValue(extraDim, "", dimMap, popMap));
    }

    //填充环比数据
    //填充指标卡环比数据
    public static void processMetricCardMomData(GetRawDataResponseType momRes,
                                                GetRawDataResponseType momRes2,
                                                Map<String, Double> dimMap,
                                                String metric,
                                                String momType,
                                                String subMetric) {
        Map<String, Double> momMap = new HashMap<>();
        Map<String, Double> momMap2 = new HashMap<>();
        ChartHelper.fillOverallDimMap(MapperUtil.str2ListList(momRes.getResult(), Object.class), momRes.getMetricList(), momMap);
        ChartHelper.fillOverallDimMap(MapperUtil.str2ListList(momRes2.getResult(), Object.class), momRes2.getMetricList(), momMap2);
        String dim = extraDimTable.get(subMetric, metric);
        //计算环比
        String momDim = dim + "_" + momType;
        dimMap.put(momDim + "_fenzi_value", momMap.get(dim));
        dimMap.put(momDim + "_fenmu_value", momMap2.get(dim));
        dimMap.put(momDim, DimHelper.getSpecialDimValue(momDim, "", momMap, momMap2));
    }


    //填充趋势线完成率数据
    public static void processTrendLineBaseData(List<TrendLineDetailInfo> trendLineDetailInfoList,
                                                PeriodDataBean periodDataBean,
                                                List<String> timeList,
                                                String metric,
                                                String subMetric) {
        Map<String, Double> dimMap = new HashMap<>();
        Set<String> drillDownSet = new HashSet<>();
        List<List<Object>> reachList = periodDataBean.getReachList();
        List<List<Object>> targetList = periodDataBean.getTargetList();
        List<String> reachHeaderList = periodDataBean.getReachHeaderList();
        List<String> targetHeaderList = periodDataBean.getTargetHeaderList();
        List<String> reachDimList = reachHeaderList.subList(1, reachHeaderList.size());
        List<String> targetDimList = targetHeaderList.subList(1, targetHeaderList.size());

        ChartHelper.setMonthlyMetricTrendlineActualReach(dimMap, drillDownSet, reachList, Lists.newArrayList("time"), reachDimList);
        ChartHelper.setMonthlyMetricTrendlineActualReach(dimMap, drillDownSet, targetList, Lists.newArrayList("time"), targetDimList);

        String extraDim = extraDimTable.get(subMetric, metric);
        String extraTargetDim = extraTargetTable.get(subMetric, metric);
        String completeDim = completeTable.get(subMetric, metric);
        Map<String, String> typeMap = new HashMap<>();
        typeMap.put(extraDim, "barChart");
        typeMap.put(extraTargetDim,"lineChart");
        typeMap.put(completeDim, "lineChart");

        ChartHelper.fillLineChartTrendLineData(null, timeList, dimMap,
                trendLineDetailInfoList, typeMap);

    }

    //填充趋势线同比数据
    public static void processTrendLinePopData(List<TrendLineDetailInfo> trendLineDetailInfoList,
                                               PeriodDataBean periodDataBean,
                                               List<String> timeList,
                                               String metric,
                                               String popType,
                                               String subMetric) {
        Map<String, Double> dimMap = new HashMap<>();
        Set<String> drillDownSet = new HashSet<>();
        List<List<Object>> popList;
        List<String> popDimList;
        if ("lastyear".equals(popType)) {
            popList = periodDataBean.getLastyearList();
            List<String> popHeaderList = periodDataBean.getLastyearHeaderList();
            popDimList = popHeaderList.subList(1, popHeaderList.size());
        } else {
            popList = periodDataBean.get2019List();
            List<String> popHeaderList = periodDataBean.get2019HeaderList();
            popDimList = popHeaderList.subList(1, popHeaderList.size());
        }
        popDimList = popDimList
                .stream()
                .map(i -> {
                    if (i.endsWith("_value")) {
                        return i.substring(0, i.length() - 6) + "_" + popType + "_value";
                    } else {
                        return i + "_" + popType;
                    }
                })
                .collect(Collectors.toList());
        ChartHelper.setMonthlyMetricTrendlineActualReach(dimMap, drillDownSet, popList, Lists.newArrayList("time"), popDimList);
        String dim = extraDimTable.get(subMetric, metric);
        Map<String, String> typeMap = new HashMap<>();
        typeMap.put(dim + "_" + popType, "lineChart");
        typeMap.put(dim + "_" + popType + "_value", "lineChart");
        ChartHelper.fillLineChartTrendLineData(null, timeList, dimMap, trendLineDetailInfoList, typeMap);
    }

    public static List<String> getDomesticBuTypeByBusinessId(Integer businessId,Integer subBusinessId){
        List<String> butypeList = new ArrayList<>();
        switch (businessId) {
            case 1:
                butypeList.add("tkt");
                break;
            case 2:
                switch (subBusinessId){
                    case 3:
                        butypeList.add("act");
                        break;
                    case 4:
                        butypeList.add("odt");
                        break;
                    case 5:
                        butypeList.add("act");
                        break;
                }
                break;
        }
        return butypeList;
    }
    public static List<String> getDomesticBusinessLineByBusinessId(Integer businessId,Integer subBusinessId){
        List<String> butypeList = new ArrayList<>();
        switch (businessId) {
            case 1:
                butypeList.add("101");
                break;
            case 2:
                switch (subBusinessId){
                    case 3:
                        butypeList.add("102");
                        break;
                    case 4:
                        butypeList.add("103");
                        break;
                    case 5:
                        butypeList.add("102");
                        break;
                }
                break;
        }
        return butypeList;
    }

    //获取下钻可选维度
    //国内          大区 省份 商拓
    //大区 多个省份   省份 商拓
    //单个省份       商拓
    public static List<String> getDrillDownFieldList(MetricInfoBean metricInfoBean,
                                                     RemoteConfig remoteConfig,
                                                     String subMetric) {
        String domestic = remoteConfig.getConfigValue("domestic");
        String region = remoteConfig.getConfigValue("region");
        String level = metricInfoBean.getOdtLevel();
        List<String> regionList = metricInfoBean.getOdtRegionList();
        if ("overseaDayTour".equals(subMetric)) {
            level = metricInfoBean.getOverseaOdtLevel();
            regionList = metricInfoBean.getOverseaOdtRegionList();
        }
        if (domestic.equals(level)) {
            return Lists.newArrayList("region_name", "province_name", "examinee");
        } else if (region.equals(level)) {
            return Lists.newArrayList("province_name", "examinee");
        } else {
            if (regionList.size() > 1) {
                return Lists.newArrayList("province_name", "examinee");
            } else {
                return Lists.newArrayList("examinee");
            }
        }

    }

    public static List<String> getDemosticDrillDownFieldList(MetricInfoBean metricInfoBean,
                                                             RemoteConfig remoteConfig,
                                                             Integer businessId,
                                                             String level,List<String> rangeList) {
        String domestic = remoteConfig.getConfigValue("domestic");
        String region = remoteConfig.getConfigValue("region");

        if (domestic.equals(level)) {
            if(BUSINESS_LINE.equals(businessId)){
                return Lists.newArrayList("region_name", "province_name", "examinee","viewspotid");
            }else {
                return Lists.newArrayList("region_name", "province_name", "examinee");
            }
        } else if (region.equals(level)) {
            if(BUSINESS_LINE.equals(businessId)){
                return Lists.newArrayList("province_name", "examinee","viewspotid");
            }else {
                return Lists.newArrayList("province_name", "examinee");
            }

        } else if ("省份".equals(level)){//NOSONAR
            if(BUSINESS_LINE.equals(businessId)){
                if (rangeList.size() > 1) {
                    return Lists.newArrayList("province_name", "examinee","viewspotid");
                } else {
                    return Lists.newArrayList("examinee","viewspotid");
                }
            }else {
                if (rangeList.size() > 1) {
                    return Lists.newArrayList("province_name", "examinee");
                } else {
                    return Lists.newArrayList("examinee");
                }
            }
        } else {
            if (BUSINESS_LINE.equals(businessId)) {
                return Lists.newArrayList("examinee", "viewspotid");
            } else {
                return Lists.newArrayList("examinee");
            }
        }
    }

    public static String compareLevel(String actLevel, String odtLevel) {
        String level = null;
        if ("国内".equalsIgnoreCase(actLevel)) {//NOSONAR
            level = actLevel;
            return level;
        }
        if ("国内".equalsIgnoreCase(odtLevel)) {//NOSONAR
            level = odtLevel;
            return level;
        }
        if (getIntByLevel(actLevel) > getIntByLevel(odtLevel)) {
            level = actLevel;
        } else if (getIntByLevel(actLevel) < getIntByLevel(odtLevel)) {
            level = odtLevel;
        } else if (getIntByLevel(actLevel).equals(getIntByLevel(odtLevel))) {
            level = odtLevel;
        }
        return level;
    }

    public static List<String> compareRangeList(String actLevel, List<String> actRangeList,
                                                String odtLevel, List<String> odtRangeList) {
        List<String> rangeList = null;
        if ("国内".equalsIgnoreCase(actLevel)) {//NOSONAR
            return actRangeList;
        }
        if ("国内".equalsIgnoreCase(odtLevel)) {//NOSONAR
            return odtRangeList;
        }
        if (getIntByLevel(actLevel) > getIntByLevel(odtLevel)) {
            rangeList = actRangeList;
            return rangeList;
        } else if (getIntByLevel(actLevel) < getIntByLevel(odtLevel)) {
            rangeList = odtRangeList;
            return rangeList;
        } else if (getIntByLevel(actLevel).equals(getIntByLevel(odtLevel))) {
            rangeList = getUniqueUnion(odtRangeList, actRangeList);
            return rangeList;
        }
        return rangeList;
    }

    public static List<String> getUniqueUnion(List<String> regionList, List<String> regionList2) {
        Set<String> set = new HashSet<>(regionList);
        set.addAll(regionList2);
        return new ArrayList<>(set);
    }

    private static Integer getIntByLevel(String actLevel) {
        switch (actLevel) {
            case "国内"://NOSONAR
                return 1;
            case "大区"://NOSONAR
                return 2;
            case "省份"://NOSONAR
                return 3;
            case "景点"://NOSONAR
                return 4;
        }
        return 5;
    }

    //构造子指标sql请求数据(下钻基础数据)
    public static SqlParamterBean getDrillDownBaseInfoSqlBean(String field,
                                                              GetDrillDownBaseInfoRequestType request,
                                                              String d,
                                                              MetricInfoBean metricInfoBean,
                                                              RemoteConfig remoteConfig) throws Exception {
        SqlParamterBean bean = new SqlParamterBean();

        String metric = request.getMetric();
        String subMetric = request.getSubMetric();

        DrillDownFieldBean configBean = remoteConfig.getDrillDownFieldBean(metric, subMetric, field);
        bean.setId(configBean.getBaseInfoId());

        Map<String, String> baseMap = generateBaseMap(request.getTimeFilter(), "current", d, null);
        Map<String, String> likeMap = new HashMap<>();
        bean.setAndMap(baseMap);
        bean.setLikeMap(likeMap);
        //对于考核范围
        //查询商拓数据 like拼接条件
        //查询大区省份数据  in拼接
        setDrillDownExamineRangeValue(field, likeMap, baseMap, metricInfoBean, remoteConfig, subMetric);
        List<String> groupList = configBean.getBaseInfoGroupList();
        if (request.isNeedSearch()) {
            String searchWord = request.getSearchWord();
            List<Integer> likeIndexList = configBean.getBaseInfoLikeIndexList();
            for (Integer index : likeIndexList) {
                likeMap.put(groupList.get(index), searchWord);
            }
        }
        bean.setGroupList(groupList);
        bean.setOrderList(Lists.newArrayList(groupList.get(0)));
        bean.setOrderTypeList(Lists.newArrayList("asc"));
        return bean;
    }

    //构造获取下钻基础数据请求
    //下钻数据  包括气泡图  和  下钻表格
    //包括首页和详情页
    public static SqlParamterBean getTableDataSqlBean(GetTableDataRequestType request,
                                                      String d,
                                                      MetricInfoBean metricInfoBean,
                                                      DrillDownFieldBean configBean,
                                                      RemoteConfig remoteConfig,
                                                      String type,
                                                      List<String> pagingFieldValueList,
                                                      String momType) throws Exception {
        SqlParamterBean bean = new SqlParamterBean();
        String metric = request.getMetric();
        String subMetric = request.getSubMetric();
        String field = configBean.getField();

        setDrillDownIdAndGroupListForSqlBean(request, bean, configBean, type);

        TimeFilter timeFilter = request.getTimeFilter();

        if ("mom".equals(type)) {
            //环比仅存在于  首页的下钻
            //此时下钻维度只有大区  省份
            List<Map<String, String>> baseMapList = generateMomBaseMapList(momType, timeFilter, d);
            Map<String, String> baseMap = baseMapList.get(0);
            //对于考核范围
            //查询大区省份数据  in拼接
            setDrillDownExamineRangeValue(field, new HashMap<>(), baseMap, metricInfoBean, remoteConfig, subMetric);
            bean.setAndMap(baseMap);

            Map<String, String> baseMap2 = baseMapList.get(1);
            setDrillDownExamineRangeValue(field, new HashMap<>(), baseMap2, metricInfoBean, remoteConfig, subMetric);
            bean.setAndMap2(baseMap2);
        } else {
            Map<String, String> baseMap = generateBaseMap(timeFilter, type, d, null);
            Map<String, String> likeMap = new HashMap<>();
            //对于考核范围
            //查询商拓数据 like拼接条件
            //查询大区省份数据  in拼接
            if (!"target".equals(type)) {
                setDrillDownExamineRangeValue(field, likeMap, baseMap, metricInfoBean, remoteConfig, subMetric);
            }


            DrillDownFilter drillDownFilter = request.getDrillDownFilter();

            setConditionValue(baseMap, field, drillDownFilter.getFieldValueList());
            //获取当前数据时 需要设置前端传入的分页  其他数据不需要
            //同时需要设置排序条件
            if ("current".equals(type)) {
                bean.setPageNo(request.getPageNo());
                bean.setPageSize(request.getPageSize());

                bean.setOrderList(Lists.newArrayList(extraDimTable.get(subMetric, metric), field));
                bean.setOrderTypeList(Lists.newArrayList("desc", "desc"));
            }

            //获取同比数据时  需要设置上一步中获取当前数据得到的条件
            if ("lastyear".equals(type) || "2019".equals(type)) {
                setConditionValue(baseMap, field, pagingFieldValueList);
            }
            bean.setAndMap(baseMap);
            bean.setLikeMap(likeMap);
        }


        return bean;

    }


    public static void setConditionValue(Map<String, String> baseMap,
                                         String field,
                                         List<String> fieldValueList) {
        if (GeneralUtil.isNotEmpty(fieldValueList)) {
            baseMap.put(field, StringUtils.join(fieldValueList, "|"));
        }
    }




    private static void setDrillDownIdAndGroupListForSqlBean(GetTableDataRequestType request,
                                                             SqlParamterBean bean,
                                                             DrillDownFieldBean configBean,
                                                             String type) {
        Long otherId = configBean.getTableDataIdMap().get("other");
        Long id = configBean.getTableDataIdMap().get(type);
        bean.setId(GeneralUtil.isEmpty(id) ? otherId : id);

        String source = request.getSource();
        String queryType = request.getQueryType();

        if ("firstpage".equals(source) || ("detailpage".equals(source) && "bubble".equals(queryType))) {
            //首页或者详情页气泡图
            List<String> otherGroupList = configBean.getBubbleGroupListMap().get("other");
            List<String> groupList = configBean.getBubbleGroupListMap().get(type);

            bean.setGroupList(GeneralUtil.isEmpty(groupList) ? otherGroupList : groupList);

        } else {

            //详情页表格
            List<String> otherGroupList = configBean.getTableGroupListMap().get("other");
            List<String> groupList = configBean.getTableGroupListMap().get(type);

            bean.setGroupList(GeneralUtil.isEmpty(groupList) ? otherGroupList : groupList);

        }

    }


    private static void setDrillDownExamineRangeValue(String columnName,
                                                      Map<String, String> likeMap,
                                                      Map<String, String> baseMap,
                                                      MetricInfoBean metricInfoBean,
                                                      RemoteConfig remoteConfig,
                                                      String subMetric) {
        if ("examinee".equals(columnName)) {
            setExamineLikeValue(likeMap, metricInfoBean, remoteConfig, subMetric);
        } else {
            setExamineRangeValue(baseMap, metricInfoBean, remoteConfig, subMetric);
        }
    }


    private static void setExamineLikeValue(Map<String, String> likeMap,
                                            MetricInfoBean metricInfoBean,
                                            RemoteConfig remoteConfig,
                                            String subMetric) {

        String region = remoteConfig.getConfigValue("region");
        String province = remoteConfig.getConfigValue("province");

        List<String> odtRegionList = metricInfoBean.getOdtRegionList();
        String odtLevel = metricInfoBean.getOdtLevel();
        List<String> overseaOdtRegionList = metricInfoBean.getOverseaOdtRegionList();
        String overseaOdtLevel = metricInfoBean.getOverseaOdtLevel();

        if ("domesticDayTour".equals(subMetric)) {
            if (GeneralUtil.isNotEmpty(odtRegionList)) {
                if (region.equals(odtLevel)) {
                    likeMap.put("region_name", StringUtils.join(odtRegionList, "|"));
                } else if (province.equals(odtLevel)) {
                    likeMap.put("province_name", StringUtils.join(odtRegionList, "|"));
                }
            }
        } else {
            if (GeneralUtil.isNotEmpty(overseaOdtRegionList)) {
                if (region.equals(overseaOdtLevel)) {
                    likeMap.put("region_name", StringUtils.join(overseaOdtRegionList, "|"));
                } else if (province.equals(overseaOdtLevel)) {
                    likeMap.put("province_name", StringUtils.join(overseaOdtRegionList, "|"));
                }
            }
        }

    }


    //填充下钻基础数据
    public static void processDrillDownBaseInfo(String field,
                                                GetRawDataResponseType response,
                                                List<FieldDataItem> fieldDataItemList){
        ChartHelper.fillFieldDataItemList(field, MapperUtil.str2ListList(response.getResult(), Object.class), fieldDataItemList);
    }


    //填充表格基础数据
    public static void processTableBaseData(GetRawDataResponseType currentRes,
                                            GetRawDataResponseType targetRes,
                                            List<TableDataItem> tableDataItemList,
                                            String metric,
                                            String subMetric) {
        List<List<Object>> currentList = MapperUtil.str2ListList(currentRes.getResult(), Object.class);
        List<String> currentGroupList = currentRes.getGroupList();
        List<String> currentMetricList = currentRes.getMetricList();

        List<List<Object>> targetList = MapperUtil.str2ListList(targetRes.getResult(), Object.class);
        List<String> targetGroupList = targetRes.getGroupList();
        List<String> targetMetricList = targetRes.getMetricList();

        ChartHelper.fillCommmonTableDataV2(tableDataItemList,
                currentGroupList,
                targetGroupList,
                currentMetricList,
                targetMetricList,
                currentList,
                targetList);

        for (TableDataItem item : tableDataItemList) {
            Map<String, Double> dimMap = item.getDimMap();
            processMetricCardCompleteData(dimMap, metric, subMetric);
        }
    }


    //填充表格同比当期数据
    public static void processTableCurrentPopData(GetRawDataResponseType currentpopRes,
                                                  List<TableDataItem> tableDataItemList) {
        List<String> metricList = currentpopRes.getMetricList();
        List<String> virtualMetricList = metricList.stream()
                .map(i -> i + "_virtual")
                .collect(Collectors.toList());
        List<String> groupList = currentpopRes.getGroupList();
        List<List<Object>> objectList = MapperUtil.str2ListList(currentpopRes.getResult(), Object.class);
        ChartHelper.fillCommmonTableDataV2(tableDataItemList,
                new ArrayList<>(),
                groupList,
                new ArrayList<>(),
                virtualMetricList,
                new ArrayList<>(),
                objectList);
    }

    //填充表格同比数据
    public static void processTablePopData(GetRawDataResponseType popRes,
                                           List<TableDataItem> tableDataItemList,
                                           String type) {
        List<List<Object>> popList = MapperUtil.str2ListList(popRes.getResult(), Object.class);
        List<String> popGroupList = popRes.getGroupList();
        List<String> popMetricList = popRes.getMetricList();
        ChartHelper.fillTableSingleDimPopData(tableDataItemList,
                popList,
                popGroupList,
                "_" + type,
                popMetricList,
                "_virtual");
    }


    //填充表格环比数据
    public static void processTableMomData(GetRawDataResponseType momRes,
                                           GetRawDataResponseType momRes2,
                                           List<TableDataItem> tableDataItemList,
                                           String momType) {
        List<List<Object>> momList = MapperUtil.str2ListList(momRes.getResult(), Object.class);
        List<List<Object>> mom2List = MapperUtil.str2ListList(momRes2.getResult(), Object.class);
        List<String> groupList = momRes.getGroupList();
        List<String> metricList = momRes.getMetricList();
        ChartHelper.makeUpTableMomData(tableDataItemList,
                momList,
                mom2List,
                metricList,
                groupList,
                "_" + momType);
    }

}
