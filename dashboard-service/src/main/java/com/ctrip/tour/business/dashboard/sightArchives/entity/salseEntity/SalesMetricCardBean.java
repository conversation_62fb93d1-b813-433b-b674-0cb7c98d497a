package com.ctrip.tour.business.dashboard.sightArchives.entity.salseEntity;

import com.ctrip.tour.business.dashboard.sightArchives.enums.sales.SalesMetricEnumType;

import java.util.List;
import java.util.Map;

//表名: cdm_ord_ttd_vst_archive_df
public class SalesMetricCardBean {
    //订单量
    private Integer orderCount;
    //毛利
    private Integer profit;
    //毛利率
    private Integer profitRate;
    //客单价
    private Integer averageOrderValue;
    //gmv
    private Integer gmv;
    //票量
    private Integer soldTicketCount;
    //退订率
    private Integer fullyRefundedOrderRate;

    public Integer getOrderCount() {
        return orderCount;
    }

    public void setOrderCount(Integer orderCount) {
        this.orderCount = orderCount;
    }

    public Integer getProfit() {
        return profit;
    }

    public void setProfit(Integer profit) {
        this.profit = profit;
    }

    public Integer getProfitRate() {
        return profitRate;
    }

    public void setProfitRate(Integer profitRate) {
        this.profitRate = profitRate;
    }

    public Integer getAverageOrderValue() {
        return averageOrderValue;
    }

    public void setAverageOrderValue(Integer averageOrderValue) {
        this.averageOrderValue = averageOrderValue;
    }

    public Integer getGmv() {
        return gmv;
    }

    public void setGmv(Integer gmv) {
        this.gmv = gmv;
    }

    public Integer getSoldTicketCount() {
        return soldTicketCount;
    }

    public void setSoldTicketCount(Integer soldTicketCount) {
        this.soldTicketCount = soldTicketCount;
    }

    public Integer getFullyRefundedOrderRate() {
        return fullyRefundedOrderRate;
    }

    public void setFullyRefundedOrderRate(Integer fullyRefundedOrderRate) {
        this.fullyRefundedOrderRate = fullyRefundedOrderRate;
    }

    public static SalesMetricCardBean transferToSalesMetricCardBean(List<Map<String, Object>> rawData) {

        SalesMetricCardBean salesMetricCardBean = new SalesMetricCardBean();
        if (rawData != null && rawData.size() > 0) {
            Map<String, Object> data = rawData.get(0);
            salesMetricCardBean.setGmv((Integer) data.get(SalesMetricEnumType.GMV.getEnglishName()));
            salesMetricCardBean.setProfit((Integer) data.get(SalesMetricEnumType.PROFIT.getEnglishName()));
            salesMetricCardBean.setProfitRate((Integer) data.get(SalesMetricEnumType.PROFIT_RATE.getEnglishName()));
            salesMetricCardBean.setOrderCount((Integer) data.get(SalesMetricEnumType.ORDER_COUNT.getEnglishName()));
            salesMetricCardBean.setSoldTicketCount((Integer) data.get(SalesMetricEnumType.SOLD_TICKET_COUNT.getEnglishName()));
            salesMetricCardBean.setAverageOrderValue((Integer) data.get(SalesMetricEnumType.AVERAGE_ORDER_VALUE.getEnglishName()));
        }
        return salesMetricCardBean;
    }

}
