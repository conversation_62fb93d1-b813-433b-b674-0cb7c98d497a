package com.ctrip.tour.business.dashboard.tktBusiness.dao;

import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.StatementParameters;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.ctrip.tour.business.dashboard.utils.SqlUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class Bus9NewDao {

    @Autowired
    private GeneralDao generalDao;


    //获取选定周期进展 目标
    public List<List<Object>> getMetricCardData(Map<String, List<String>> inMap) throws SQLException {
        StringBuilder sb = new StringBuilder();
        sb.append(" select sum(weight_single_category_complete_rate)/sum(viewspot_type_weight) as weight_single_category_complete_rate from ( ");
        sb.append("select sum(single_category_complete_rate) * sum(viewspot_type_weight) as weight_single_category_complete_rate,sum(viewspot_type_weight) as viewspot_type_weight, viewspot_type_name ");
        sb.append(" from (");
        sb.append("      select COALESCE(sum(cover_scenic_cnt)/sum(open_scenic_cnt),0) AS single_category_complete_rate" +
                ",viewspot_type_name, viewspot_type_weight from bus_9_multi_category_cover_info_t_new ");
        StatementParameters parameters = new StatementParameters();
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        //添加限制 过滤未配置目标的品类
        sb.append(" and open_scenic_cnt>0 ");
        sb.append(" group by viewspot_type_name,viewspot_type_weight");
        sb.append("      ) aa group by viewspot_type_name ) bb ");
        return generalDao.getListResult(sb.toString(), parameters);
    }


    //按月或者季度拆分 返回各个品类的完成情况
    public List<List<Object>> getSingleCategoryTrendlineData(Map<String, List<String>> inMap,
                                                             List<String> groupTagList) throws SQLException {
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        SqlUtil.jointGroupCondition(sb, false, groupTagList);
        sb.append(",sum(cover_scenic_cnt) AS cover_scenic_cnt" +
                " from bus_9_multi_category_cover_info_t_new ");
        StatementParameters parameters = new StatementParameters();
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        SqlUtil.jointGroupCondition(sb, true, groupTagList);
        return generalDao.getListResult(sb.toString(), parameters);
    }

    //按月或者季度拆分  返回品类的综合完成情况(也可以表示下钻趋势线的情况)
    /**
     * @param inMap             所有的查询条件
     * @param innerGroupTagList 内侧sql用的拆分条件
     * @param outerGroupTagList 最外侧sql用的拆分条件
     * @return
     */
    public List<List<Object>> getMultiCategoryTrendlineData(Map<String, List<String>> inMap,
                                                            List<String> innerGroupTagList,
                                                            List<String> outerGroupTagList) throws SQLException {
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        if(GeneralUtil.isNotEmpty(outerGroupTagList)){
            SqlUtil.jointGroupCondition(sb, false, outerGroupTagList);
            sb.append(",");
        }
        sb.append("sum(weight_single_category_complete_rate)/sum(viewspot_type_weight) as multi_category_complete_rate from (");
        sb.append("select ");
        SqlUtil.jointGroupCondition(sb, false, innerGroupTagList);
        sb.append(",sum(single_category_complete_rate) * sum(viewspot_type_weight) as weight_single_category_complete_rate,sum(viewspot_type_weight) as viewspot_type_weight ");
        sb.append(" from (");
        sb.append("        select ");
        SqlUtil.jointGroupCondition(sb, false, innerGroupTagList);
        sb.append(",COALESCE(sum(cover_scenic_cnt)/sum(open_scenic_cnt),0) AS single_category_complete_rate" +
                ",viewspot_type_weight from bus_9_multi_category_cover_info_t_new ");
        StatementParameters parameters = new StatementParameters();
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        //添加限制 过滤未配置目标的品类
        sb.append(" and open_scenic_cnt>0 ");
        SqlUtil.jointGroupCondition(sb, true, innerGroupTagList);
        sb.append(",viewspot_type_weight");
        sb.append("      ) aa ");
        SqlUtil.jointGroupCondition(sb, true, innerGroupTagList);
        sb.append("  ) bb ");
        if(GeneralUtil.isNotEmpty(outerGroupTagList)){
            SqlUtil.jointGroupCondition(sb, true, outerGroupTagList);
        }
        return generalDao.getListResult(sb.toString(), parameters);
    }


    //获取选定周期的下钻数据
    public List<List<Object>> getSingleCategoryTableData(Map<String, List<String>> inMap,
                                                         List<String> groupTagList,
                                                         List<String> orderTagList) throws SQLException {
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        SqlUtil.jointGroupCondition(sb, false, groupTagList);
        sb.append(",sum(cover_scenic_cnt) as cover_scenic_cnt" +
                ", sum(open_scenic_cnt) as open_scenic_cnt,sum(cover_scenic_cnt) / sum(open_scenic_cnt) AS single_category_complete_rate " +
                ", sum(treasuremap_scenic_cnt) as treasuremap_scenic_cnt ");
        sb.append(" from bus_9_multi_category_cover_info_t_new ");
        StatementParameters parameters = new StatementParameters();
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        SqlUtil.jointGroupCondition(sb, true, groupTagList);
        SqlUtil.jointOrderCondition(sb, orderTagList, "desc");
        return generalDao.getListResult(sb.toString(), parameters);
    }


    public List<List<Object>> getFieldList(Map<String, List<String>> inMap,
                                           Map<String, String> likeMap,
                                           List<String> tagList) throws SQLException {
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        SqlUtil.jointGroupCondition(sb, false, tagList);
        sb.append(" from bus_9_multi_category_cover_info_t_new ");
        StatementParameters parameters = new StatementParameters();
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        if (!GeneralUtil.isEmpty(likeMap)) {
            SqlUtil.jointLikeCondition(sb, parameters, likeMap, true);
        }
        SqlUtil.jointGroupCondition(sb, true, tagList);
        SqlUtil.jointOrderCondition(sb, tagList, "desc");
        return generalDao.getListResult(sb.toString(), parameters);
    }

    //获取某个考核周期的品类个数
    public Integer getCategoryNum(Map<String, List<String>> inMap) throws SQLException {
        StringBuilder sb = new StringBuilder();
        sb.append("select count(distinct viewspot_type_name) from bus_9_multi_category_cover_info_t_new ");
        StatementParameters parameters = new StatementParameters();
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        List<List<Object>> resultList = generalDao.getListResult(sb.toString(), parameters);
        return Integer.valueOf(String.valueOf(resultList.get(0).get(0)));
    }

    //获取某个考核周期具体的品类列表
    public List<String> getCategoryEnum(Map<String, List<String>> inMap,
                                        List<String> orderTagList) throws SQLException {
        StringBuilder sb = new StringBuilder();
        //不要考虑过滤条件 不然取出来的数据可能少
        Map<String, List<String>> innerInMap = new HashMap<>(inMap);
        innerInMap.remove("region_name");
        innerInMap.remove("province_name");
        sb.append("select distinct viewspot_type_name from bus_9_multi_category_cover_info_t_new ");
        StatementParameters parameters = new StatementParameters();
        SqlUtil.jointWhereCondition(sb, parameters, innerInMap);
        SqlUtil.jointOrderCondition(sb, orderTagList, "desc");
        List<List<Object>> resultList = generalDao.getListResult(sb.toString(), parameters);
        List<String> categoryEnumList = new ArrayList<>();
        for (List<Object> rowResult : resultList) {
            categoryEnumList.add(String.valueOf(rowResult.get(0)));
        }
        return categoryEnumList;
    }

    public void getRankAsync(Map<String, List<String>> inMap,
                             DalHints dalHints) throws SQLException {
        StringBuilder sb = new StringBuilder();
        sb.append("select ranking  from bus_4_8_9_10_rank_t ");
        StatementParameters parameters = new StatementParameters();
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        generalDao.getListResultAsync(sb.toString(), parameters, dalHints);
    }





}
