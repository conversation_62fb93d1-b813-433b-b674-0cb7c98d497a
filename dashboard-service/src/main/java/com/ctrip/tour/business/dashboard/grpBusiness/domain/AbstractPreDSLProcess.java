package com.ctrip.tour.business.dashboard.grpBusiness.domain;

import com.ctrip.soa._24922.DSLRequestType;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.model.ResultData;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

public abstract class AbstractPreDSLProcess {
    @Getter
    @Setter
    public static class EarlyReturn {
        Integer status;
        String msg;
        Map<String, Object> data;
        ResultData defaultResultData;
    }

    public abstract DSLRequestType process(DSLRequestType dslRequestType, EarlyReturn earlyReturn);
}
