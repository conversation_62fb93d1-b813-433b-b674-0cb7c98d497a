package com.ctrip.tour.business.dashboard.tktBusiness.biz;

import com.ctrip.soa._24922.*;

public interface TaskFlowBiz {


    GetTaskLevelDimResponseType getTaskLevelDim(GetTaskLevelDimRequestType getTaskLevelDimRequestType, String empCode) throws Exception;


    GetTaskFlowTableDataResponseType getTaskFlowTableData(GetTaskFlowTableDataRequestType getTaskFlowTableDataRequestType, String empCode) throws Exception;


    GetTaskFlowTrendlineDataResponseType getTaskFlowTrendlineData(GetTaskFlowTrendlineDataRequestType getTaskFlowTrendlineDataRequestType, String empCode) throws Exception;

    GetTaskLevelScoreMappingResponseType getTaskLevelScoreMapping(GetTaskLevelScoreMappingRequestType getTaskLevelScoreMappingRequestType) throws Exception;
}
