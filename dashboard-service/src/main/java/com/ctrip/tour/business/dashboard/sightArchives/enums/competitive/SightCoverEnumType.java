package com.ctrip.tour.business.dashboard.sightArchives.enums.competitive;

import java.util.Arrays;
import java.util.List;

public enum SightCoverEnumType {

//                    " sum(adult_ticket_score) as adult_ticket_score, " +   //成人票覆盖
//                            " sum(children_ticket_score) as children_ticket_score, " +   //儿童票覆盖
//                            " sum(elder_ticket_score) as elder_ticket_score, " +   //老人票覆盖
//                            " sum(student_ticket_score)as student_ticket_score," +   //学生票覆盖
//                            " sum(family_ticket_score) as family_ticket_score, " +   //亲子家庭票覆盖
//
//                            " sum(today_book_score) as today_book_score," +   //当天可订覆盖
//                            " sum(immediately_confirm_score) as immediately_confirm_score, " +   //下单立即确认覆盖
//                            " sum(immediately_confirm_score) as immediately_confirm_score," +   //出票立即可用覆盖
//                            " sum(enter_directly_score) as enter_directly_score," +   //直接入园覆盖
//                            " sum(anytime_refund_score) as anytime_refund_score," +   //随时退覆盖
//                            " sum(price_schedule_score) as price_schedule_score" +   //班期覆盖

    ADULT_TICKET_SCORE(1, "is_adult_cover", "成人票"), //NOSONAR
    CHILDREN_TICKET_SCORE(2, "is_children_cover", "儿童票"), //NOSONAR
    ELDER_TICKET_SCORE(3, "is_elder_cover", "老人票"), //NOSONAR
    STUDENT_TICKET_SCORE(4, "is_student_cover", "学生票"), //NOSONAR
    FAMILY_TICKET_SCORE(5, "is_family_cover", "亲子家庭票"), //NOSONAR

    TODAY_BOOK_SCORE(6, "is_today_book", "当天可订"), //NOSONAR
    IMMEDIATELY_CONFIRM_SCORE(7, "is_immediately_confirm", "下单立即确认"), //NOSONAR
    IMMEDIATELY_USE_SCORE(7, "is_immediately_available", "出票立即可用"), //NOSONAR
    ENTER_DIRECTLY_SCORE(8, "is_enter_directly", "扫码/刷证直接入园"), //NOSONAR
    ANYTIME_REFUND_SCORE(9, "is_anytime_refund", "随时退"), //NOSONAR
    PRICE_SCHEDULE_SCORE(10, "is_price_schedule", "未来15天可订天数12天以上"); //NOSONAR

    private final int id;
    private final String englishName;
    private final String chineseName;

    SightCoverEnumType(int id, String englishName, String chineseName) {
        this.id = id;
        this.englishName = englishName;
        this.chineseName = chineseName;
    }

    public int getId() {
        return id;
    }

    public String getEnglishName() {
        return englishName;
    }

    public String getChineseName() {
        return chineseName;
    }

    public static final List<SightCoverEnumType> userGroupCoverEnumList = Arrays.asList(ADULT_TICKET_SCORE, CHILDREN_TICKET_SCORE, ELDER_TICKET_SCORE, STUDENT_TICKET_SCORE, FAMILY_TICKET_SCORE);


    public static final List<SightCoverEnumType> competiveCoverEnumList = Arrays.asList(TODAY_BOOK_SCORE, IMMEDIATELY_CONFIRM_SCORE, IMMEDIATELY_USE_SCORE, ENTER_DIRECTLY_SCORE, ANYTIME_REFUND_SCORE, PRICE_SCHEDULE_SCORE);

}
