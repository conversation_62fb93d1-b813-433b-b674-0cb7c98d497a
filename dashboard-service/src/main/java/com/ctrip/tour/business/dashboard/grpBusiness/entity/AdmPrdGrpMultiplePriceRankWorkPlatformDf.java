package com.ctrip.tour.business.dashboard.grpBusiness.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Sensitive;
import com.ctrip.platform.dal.dao.annotation.Type;
import java.sql.Types;
import java.sql.Timestamp;

import com.ctrip.platform.dal.dao.DalPojo;

/**
 * <AUTHOR>
 * @date 2024-12-10
 */
@Entity
@Database(name = "TtdReportDB_W")
@Table(name = "adm_prd_grp_multiple_price_rank_work_platform_df")
public class AdmPrdGrpMultiplePriceRankWorkPlatformDf implements DalPojo {

    /**
     * 主键
     */
    @Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long id;

    /**
     * 产线
     */
	@Column(name = "sub_bu_type")
	@Type(value = Types.VARCHAR)
	private String subBuType;

    /**
     * 产品大区/产品经理
     */
	@Column(name = "business_team")
	@Type(value = Types.VARCHAR)
	private String businessTeam;

    /**
     * 当月价格倍数
     */
	@Column(name = "tm_multiple_price")
	@Type(value = Types.DOUBLE)
	private Double tmMultiplePrice;

    /**
     * 当月浏览pv
     */
	@Column(name = "tm_click_pv")
	@Type(value = Types.BIGINT)
	private Long tmClickPv;

    /**
     * 当月加权价格倍数
     */
	@Column(name = "tm_weight_multiple_price")
	@Type(value = Types.DOUBLE)
	private Double tmWeightMultiplePrice;

    /**
     * 浏览月（年+月）
     */
	@Column(name = "view_month")
	@Type(value = Types.VARCHAR)
	private String viewMonth;

    /**
     * 价格倍数排名
     */
	@Column(name = "price_rank")
	@Type(value = Types.INTEGER)
	private Integer priceRank;

    /**
     * 价格倍数排名占比
     */
	@Column(name = "price_rank_ratio")
	@Type(value = Types.VARCHAR)
	private String priceRankRatio;

	/**
	 * 价格倍数排名
	 */
	@Column(name = "total_price_cnt")
	@Type(value = Types.INTEGER)
	private Integer totalPriceCnt;

	public Integer getTotalPriceCnt() {
		return totalPriceCnt;
	}

	public void setTotalPriceCnt(Integer totalPriceCnt) {
		this.totalPriceCnt = totalPriceCnt;
	}

	/**
     * 更新时间
     */
	@Column(name = "datachange_lasttime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getSubBuType() {
		return subBuType;
	}

	public void setSubBuType(String subBuType) {
		this.subBuType = subBuType;
	}

	public String getBusinessTeam() {
		return businessTeam;
	}

	public void setBusinessTeam(String businessTeam) {
		this.businessTeam = businessTeam;
	}

	public Double getTmMultiplePrice() {
		return tmMultiplePrice;
	}

	public void setTmMultiplePrice(Double tmMultiplePrice) {
		this.tmMultiplePrice = tmMultiplePrice;
	}

	public Long getTmClickPv() {
		return tmClickPv;
	}

	public void setTmClickPv(Long tmClickPv) {
		this.tmClickPv = tmClickPv;
	}

	public Double getTmWeightMultiplePrice() {
		return tmWeightMultiplePrice;
	}

	public void setTmWeightMultiplePrice(Double tmWeightMultiplePrice) {
		this.tmWeightMultiplePrice = tmWeightMultiplePrice;
	}

	public String getViewMonth() {
		return viewMonth;
	}

	public void setViewMonth(String viewMonth) {
		this.viewMonth = viewMonth;
	}

	public Integer getPriceRank() {
		return priceRank;
	}

	public void setPriceRank(Integer priceRank) {
		this.priceRank = priceRank;
	}

	public String getPriceRankRatio() {
		return priceRankRatio;
	}

	public void setPriceRankRatio(String priceRankRatio) {
		this.priceRankRatio = priceRankRatio;
	}

	public Timestamp getDatachangeLasttime() {
		return datachangeLasttime;
	}

	public void setDatachangeLasttime(Timestamp datachangeLasttime) {
		this.datachangeLasttime = datachangeLasttime;
	}

}