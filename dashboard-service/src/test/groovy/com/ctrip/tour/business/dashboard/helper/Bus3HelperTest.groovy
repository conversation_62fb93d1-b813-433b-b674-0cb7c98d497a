package com.ctrip.tour.business.dashboard.helper

import com.ctrip.soa._24922.TableDataItem
import com.ctrip.tour.business.dashboard.tktBusiness.helper.Bus3Helper
import com.ctrip.tour.business.dashboard.utils.MapperUtil
import com.google.common.collect.Lists
import spock.lang.Specification

class Bus3HelperTest extends Specification {
    def helper = new Bus3Helper()


    def "test getReachDimList"() {
        expect: "call"
        resp == helper.getReachDimList(year)

        where: "多条件验证"
        resp    |   year
        Lists.newArrayList("ttd_qa_cost", "ttd_suc_income", "ttd_orders", "ttd_qa_cost_rate")   |   "2022"
        Lists.newArrayList("ttd_weighted_defect_cnt", "ttd_pay_odr_cnt", "ttd_weighted_defect_rate")   |   "2024"


    }

    def "test getTargetDimList"() {
        expect: "call"
        resp == helper.getTargetDimList(year)

        where: "多条件验证"
        resp    |   year
        Lists.newArrayList("ttd_trgt_qa_cost_rate")   |   "2022"
        Lists.newArrayList("ttd_weighted_defect_target")   |   "2024"
    }

    def "test getLineChartTrendlineType"() {
        expect: "call"
        resp == helper.getLineChartTrendlineType(year)

        where: "多条件验证"
        resp    |   year
        MapperUtil.str2Obj("{\"ttd_weighted_defect_rate\":\"lineChart\",\"ttd_weighted_defect_target\":\"lineChart\"}", Map.class)       |   "2024"
        MapperUtil.str2Obj("{\"ttd_qa_cost_rate\":\"lineChart\",\"ttd_trgt_qa_cost_rate\":\"lineChart\"}", Map.class)                    |   "2023"
    }

    def "getLineChartTrendlineTypeWithDrillDown"() {
        expect: "call"
        resp == helper.getLineChartTrendlineTypeWithDrillDown(year)

        where: "多条件验证"
        resp    |   year
        MapperUtil.str2Obj("{\"ttd_weighted_defect_rate\":\"lineChart\"}", Map.class)            |   "2024"
        MapperUtil.str2Obj("{\"ttd_qa_cost_rate\":\"lineChart\"}", Map.class)                    |   "2023"
    }

    def "makeUpMetricCardData"(){

        expect: "call"
        helper.makeUpMetricCardData(dimMap, year)

        where: "多条件验证"
        dimMap      |       year
        MapperUtil.str2Obj("{}", Map.class)     |   "2024"
        MapperUtil.str2Obj("{}", Map.class)     |   "2023"

    }

    def "makeUpMetricCardPopData"(){
        given: "mock param"
        def dimMap = new HashMap()
        dimMap.put("ttd_weighted_defect_achieved_rate", -2.9515355086372357D)
        dimMap.put("ttd_weighted_defect_cnt", 123.8D)
        dimMap.put("ttd_pay_odr_cnt", 11997.0D)
        dimMap.put("ttd_weighted_defect_target", 0.002084D)
        dimMap.put("ttd_weighted_defect_rate", 0.010319D)

        def popDimMap = new HashMap()
        popDimMap.put("ttd_weighted_defect_cnt", 16.4D)
        popDimMap.put("ttd_pay_odr_cnt", 1007.0D)
        popDimMap.put("ttd_weighted_defect_rate", 0.016286D)


        expect: "call"
        helper.makeUpMetricCardPopData(year, dimMap, popDimMap, preffix)

        where: "多条件验证"
        year    |   preffix
        "2024"  |   ""

    }


    def "makeUpTableData"(){
        expect: "call"
        helper.makeUpTableData(tableDataItemList, year)

        where: "多条件验证"
        tableDataItemList   |   year
        MapperUtil.str2List("[{\"fieldMap\":{\"region_name\":\"云南大区\"},\"dimMap\":{\"ttd_weighted_defect_cnt\":1038.4,\"ttd_pay_odr_cnt\":343517.0,\"ttd_weighted_defect_target\":0.00471,\"ttd_weighted_defect_rate\":0.003023}},{\"fieldMap\":{\"region_name\":\"华东大区\"},\"dimMap\":{\"ttd_weighted_defect_cnt\":15474.6,\"ttd_pay_odr_cnt\":1.1544628E7,\"ttd_weighted_defect_target\":0.014465,\"ttd_weighted_defect_rate\":0.00134}},{\"fieldMap\":{\"region_name\":\"华北大区\"},\"dimMap\":{\"ttd_weighted_defect_cnt\":16658.3,\"ttd_pay_odr_cnt\":4726529.0,\"ttd_weighted_defect_target\":0.009482,\"ttd_weighted_defect_rate\":0.003524}},{\"fieldMap\":{\"region_name\":\"华南大区\"},\"dimMap\":{\"ttd_weighted_defect_cnt\":8117.2,\"ttd_pay_odr_cnt\":6350616.0,\"ttd_weighted_defect_target\":0.008325,\"ttd_weighted_defect_rate\":0.001278}},{\"fieldMap\":{\"region_name\":\"华西大区\"},\"dimMap\":{\"ttd_weighted_defect_cnt\":11590.4,\"ttd_pay_odr_cnt\":6384615.0,\"ttd_weighted_defect_target\":0.007237,\"ttd_weighted_defect_rate\":0.001815}}]", TableDataItem.class)   |   "2024"


    }
}
