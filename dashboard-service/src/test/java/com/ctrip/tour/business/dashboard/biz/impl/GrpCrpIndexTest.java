//package com.ctrip.tour.business.dashboard.biz.impl;
//
//import java.sql.SQLException;
//import java.text.SimpleDateFormat;
//import java.util.ArrayList;
//import java.util.Calendar;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.Random;
//
//import org.apache.commons.lang.RandomStringUtils;
//import org.apache.commons.lang3.RandomUtils;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import com.ctrip.platform.dal.dao.DalHints;
//import com.ctrip.tour.business.dashboard.grpBusiness.dao.CdmSevGrpCprPlatformSelfSrvCrDfDao;
//import com.ctrip.tour.business.dashboard.grpBusiness.entity.CdmSevGrpCprPlatformSelfSrvCrDf;
//import com.ctrip.tour.business.dashboard.grpBusiness.index.GrpCprSelfSrvCardService;
//import com.ctrip.tour.business.dashboard.grpBusiness.index.GrpCprSelfSrvDillDownService;
//import com.ctrip.tour.business.dashboard.grpBusiness.index.GrpCprSelfSrvTrendService;
//import com.google.common.collect.Lists;
//import com.google.common.collect.Maps;
//
///**
// * <AUTHOR>
// * @Date 2024/12/11
// */
//@SpringBootTest
//@RunWith(SpringRunner.class)
//public class GrpCrpIndexTest {
//
//    @Autowired
//    private CdmSevGrpCprPlatformSelfSrvCrDfDao selfSrvCrDfDao;
//    @Autowired
//    private GrpCprSelfSrvCardService cprSelfSrvService;
//    @Autowired
//    private GrpCprSelfSrvTrendService trendService;
//    @Autowired
//    private GrpCprSelfSrvDillDownService dillDownService;
//
//
//    @Test
//    public void test4() {
//
//        HashMap<String, Object> map = Maps.newHashMap();
//        map.put("chat_create_date", new String[]{"2024-09-30","2024-11-30"});
//
//        List<Map<String, Object>> slf_cover_ratio = dillDownService.queryByCondition("slf_cover_ratio_dilldown", map, Lists.newArrayList("bu_type"), "", false);
//        System.out.println(slf_cover_ratio);
//    }
//    @Test
//    public void test2() {
//
//        HashMap<String, Object> map = Maps.newHashMap();
//        map.put("chat_create_date", new String[]{"2024-09-30","2024-11-30"});
//
//        List<Map<String, Object>> slf_cover_ratio = cprSelfSrvService.queryByCondition("slf_cover_ratio", map, Lists.newArrayList(), "week", true);
//        System.out.println(slf_cover_ratio);
//    }
//
//    @Test
//    public void test3() {
//
//        HashMap<String, Object> map = Maps.newHashMap();
//        map.put("chat_create_date", new String[]{"2024-09-30","2024-11-30"});
//
//        List<Map<String, Object>> slf_cover_ratio = trendService.queryByCondition("slf_cover_ratio_trend", map, com.google.common.collect.Lists.newArrayList("chat_create_date"), "month", true);
//        System.out.println(slf_cover_ratio);
//    }
//
//    @Test
//    public void test1() throws SQLException {
//        // 创建批量插入的数据列表
//        List<CdmSevGrpCprPlatformSelfSrvCrDf> entities = new ArrayList<>();
//
//        // 示例数据
//        for (int i = 0; i < 500; i++) {
//            CdmSevGrpCprPlatformSelfSrvCrDf entity = new CdmSevGrpCprPlatformSelfSrvCrDf();
//            entity.setBuType("业务线" + RandomStringUtils.randomNumeric(1));
//            entity.setSubBuType("产线" + RandomStringUtils.randomNumeric(1));
//            entity.setPrdCategoryId(1000L + i);
//            entity.setPrdCategoryName("产品类型" + i);
//            entity.setPrdPatternId(2000L + i);
//            entity.setPrdPatternName("产品形态" + i);
//            entity.setDestDomain("目的地区域" + i);
//            entity.setSaleChannelName("销售渠道" + i);
//            entity.setSaleModeName("销售模式" + i);
//            entity.setTourRegionType("客源地目的地" + i);
//            entity.setPrdRegionId(3000L + i);
//            entity.setPrdRegionName("产品大区" + i);
//            entity.setPmEid("产品经理ID" + i);
//            entity.setLocalPmEid("驻地业务经理" + i);
//            entity.setDestFirstRegion("运营一级大区" + i);
//            entity.setDestCityId(4000L + i);
//            entity.setDestCityName("目的地城市" + i);
//            entity.setDestProvinceId(5000L + i);
//            entity.setDestProvinceName("目的地省份" + i);
//            entity.setDestCountryId(6000L + i);
//            entity.setDestCountryName("目的地国家" + i);
//            entity.setVendorId(7000L + i);
//            entity.setVendorName("产品供应商" + i);
//            entity.setIsSecKill(i % 2 == 0 ? 1 : 0);
//            entity.setOrderId(8000L + i);
//            entity.setUid(RandomStringUtils.randomNumeric(3));
//            entity.setSecondSessionType(RandomUtils.nextInt(1,10) > 7?"商户自服务会话":"携程服务会话");
//            entity.setChatCreateDate(generateRandomDateWithinSixMonths());
//            entity.setDatachangeLasttime(new java.sql.Timestamp(System.currentTimeMillis()));
//
//            entities.add(entity);
//        }
//        selfSrvCrDfDao.batchInsert(new DalHints(), entities);
//    }
//
//    public static String generateRandomDateWithinSixMonths() {
//        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
//        // 获取当前日期
//        Calendar calendar = Calendar.getInstance();
//
//        // 设置范围为近6个月
//        int maxDays = 90; // 6个月大约有180天
//        Random random = new Random();
//        int randomDays = random.nextInt(maxDays + 1); // 生成0到180之间的随机数
//
//        // 减去随机天数
//        calendar.add(Calendar.DAY_OF_MONTH, -randomDays);
//
//        // 格式化日期
//        return dateFormat.format(calendar.getTime());
//    }
//
//}
