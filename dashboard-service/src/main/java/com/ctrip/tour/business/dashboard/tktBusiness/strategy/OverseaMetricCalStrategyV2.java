package com.ctrip.tour.business.dashboard.tktBusiness.strategy;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBeanV2;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBeanV3;
import org.springframework.scheduling.annotation.Async;

import java.util.List;
import java.util.concurrent.Future;

public interface OverseaMetricCalStrategyV2 {


    //获取单个指标指标卡数据
    @Async("metricCardExecutor")
    Future<OveaseaMetric> getOverseaSingleMetricCardData(TimeFilter timeFilter,
                                                         OverseaMetricInfoBeanV2 metricInfoBean,
                                                         String d,
                                                         AvailableSubMetric availableSubMetric,
                                                         GetOverseaMetricCardDataV2RequestType requestType) throws Exception;


    //获取单个指标的趋势线数据
    GetOverseaTrendLineDataV2ResponseType getOverseaSingleTrendlineData(GetOverseaTrendLineDataV2RequestType request,
                                                                        String d) throws Exception;


    //获取单个指标下钻基础数据
    GetOverseaDrillDownBaseInfoV2ResponseType getOverseaSingleDrillDownBaseInfo(GetOverseaDrillDownBaseInfoV2RequestType request,
                                                                              String d,
                                                                                OverseaMetricInfoBean metricInfoBean) throws Exception;


    //获取单个指标下钻数据
    GetOverseaTableDataV2ResponseType getOverseaSingleTableData(GetOverseaTableDataV2RequestType request,
                                                              String d,
                                                                OverseaMetricInfoBean metricInfoBean) throws Exception;


    //获取指标枚举值
    String getMetricName();

}
