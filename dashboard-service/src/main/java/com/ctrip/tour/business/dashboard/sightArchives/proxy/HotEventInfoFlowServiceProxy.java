package com.ctrip.tour.business.dashboard.sightArchives.proxy;

import com.ctrip.gs.hotevent.infoflow.contract.*;
import com.ctrip.gs.hotevent.infoflow.contract.common.RequestHeadType;
import com.ctrip.soa._24922.PopularSight;
import com.ctrip.tour.business.dashboard.utils.UserUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class HotEventInfoFlowServiceProxy {

    public List<PopularSight> getInfoFlowListForExternal(Long districtId, Integer rankType) {

        HotEventInfoflowServiceClient hotEventInfoflowServiceClient = HotEventInfoflowServiceClient.getInstance();

        GetInfoFlowListForExternalRequestType requestType = new GetInfoFlowListForExternalRequestType();
        requestType.setModuleList(Collections.singletonList("card"));
        requestType.setDistrictId(districtId);  //攻略系的国家/省份/城市id  攻略系互相不会重复   需将酒店系id转为攻略系id

        // 1-新开业景点-标签筛选theme_four、2-热门景点-不传filterList
        if(rankType==1){
            List<FilterType> filterList = new ArrayList<>();
            FilterType filterType = new FilterType();
            filterType.setKey("label");
            filterType.setValue("theme_four");
            filterList.add(filterType);
            requestType.setFilterList(filterList);
        }
        RequestHeadType headType = new RequestHeadType();
        headType.setPlatform("09");
        headType.setLocale(UserUtil.getVbkLocale());
        requestType.setHead(headType);

        GetInfoFlowListForExternalResponseType responseType = null;
        try {
            log.info("getInfoFlowListForExternal requestType: {}", requestType);
            responseType = hotEventInfoflowServiceClient.getInfoFlowListForExternal(requestType);
            log.info("getInfoFlowListForExternal responseType: {}", responseType);
        } catch (Exception e) {
            log.warn("getInfoFlowListForExternal failed", e);
            return new ArrayList<>();
        }
        List<CardType> cardList = responseType.getCardList();
        //筛选出事件类型为poi的卡片
        List<CardType> poiCardList = cardList.stream().filter(cardType -> "poi".equals(cardType.getEventType())).collect(Collectors.toList());
        //按热度分倒序排序
        poiCardList.sort((o1, o2) -> {
            if (StringUtils.isBlank(o1.getPoiHotScore())|| StringUtils.isBlank(o2.getPoiHotScore())) {
                return 0;
            }
            return Double.compare(Double.parseDouble(o2.getPoiHotScore()), Double.parseDouble(o1.getPoiHotScore()));
        });

        //筛选出top5
        List<CardType> top5PoiCardList = poiCardList.subList(0, Math.min(poiCardList.size(), 5));
        List<PopularSight> popularSightList = new ArrayList<>();
        for(CardType cardType : top5PoiCardList){
            PopularSight popularSight = new PopularSight();
            popularSight.setHotScore(Double.valueOf(cardType.getPoiHotScore()));
            popularSight.setSightName(cardType.getBusinessTitle());
            popularSight.setHotScoreChangeDesc(cardType.getRecommendDesc());
            popularSightList.add(popularSight);
        }

        return popularSightList;
    }
}
