package com.ctrip.tour.business.dashboard.tktBusiness.biz;

import com.ctrip.soa._24922.GetEmployeeByFilterRequestType;
import com.ctrip.soa._24922.GetEmployeeByFilterResponseType;
import com.ctrip.soa._24922.GetOrganizationByFilterRequestType;
import com.ctrip.soa._24922.GetOrganizationByFilterResponseType;

/**
 * <AUTHOR>
 * @date 2022/7/22
 */
public interface FilterBoxBiz {

    GetEmployeeByFilterResponseType getEmployeeByFilter(GetEmployeeByFilterRequestType getEmployeeByFilterRequestType) throws Exception;

    GetOrganizationByFilterResponseType getOrganizationByFilter(GetOrganizationByFilterRequestType getOrganizationByFilterRequestType) throws Exception;
}
