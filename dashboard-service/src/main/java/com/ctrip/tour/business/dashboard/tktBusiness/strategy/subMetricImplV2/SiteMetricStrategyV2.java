package com.ctrip.tour.business.dashboard.tktBusiness.strategy.subMetricImplV2;

import com.ctrip.soa._24922.*;
import com.ctrip.soa._27181.BIBaseReportQueryServiceClient;
import com.ctrip.soa._27181.GetRawDataRequestType;
import com.ctrip.soa._27181.GetRawDataResponseType;
import com.ctrip.tour.business.dashboard.sightArchives.dao.overseametric.*;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.CdmOrdTtdOverseasPerformanceIndexBO;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.DimOrdTtdSiteChannelTargetBO;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.bean.DimOrdTtdSiteChannelTargetParamBean;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.bean.OverseasPerformanceInfoParamBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.*;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.OverseaSinglePeriodTrendLineBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.bo.OverSeaExamineConfigBO;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.InputArgumentException;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.Bus101102Helper;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.Bus103Helper;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.OverseaMetricHelper;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.SwitchNewTableHelper;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.*;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.google.common.util.concurrent.ListenableFuture;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

@Component
public class SiteMetricStrategyV2 implements IncomeSubMetricCalStrategyV2, TicketQuantityCalStrategyV2 ,QuantitySubMetricCalStrategyV2{

    @Autowired
    private BIBaseReportQueryServiceClient baseReportQueryServiceClient;

    @Autowired
    private RemoteConfig remoteConfig;

    @Autowired
    private OverseaSinglePeriodTrendLineBiz overseaSinglePeriodTrendLineBiz;

    @Autowired
    private SwitchNewTableHelper switchNewTableHelper;

    @Autowired
    private ChannelSiteMetricStrategyV2 siteMetricStrategyV2;
    @Autowired
    private CdmOrdTtdOverseasPerformanceIndexDao cdmOrdTtdOverseasPerformanceIndexDao;
    @Autowired
    private CdmOrdTtdOverseasPerformanceIndexAddrDao cdmOrdTtdOverseasPerformanceIndexAddrDao;
    @Autowired
    private DimOrdTtdSiteChannelTargetConfigDao dimOrdTtdSiteChannelTargetConfigDao;

    @Override
    public Future<OveaseaSubMetric> getBus101102SubMetricCardData(TimeFilter timeFilter,
                                                                  OverseaMetricInfoBeanV2 metricInfoBean,
                                                                  String d,
                                                                  String subMetric,
                                                                  GetOverseaMetricCardDataV2RequestType request) throws Exception {
        return siteMetricStrategyV2.getBus101102110SubMetricCardData(timeFilter, metricInfoBean, d, subMetric, request, "站点");//NOSONAR
    }

    @Override
    public GetOverseaTrendLineDataV2ResponseType getBus101102SubTrendLineData(GetOverseaTrendLineDataV2RequestType request,
                                                                              String d,
                                                                              List<String> timeList) throws Exception {
        return siteMetricStrategyV2.getBus101102110SubTrendlineData(request, d, timeList, "站点");//NOSONAR
    }

    @Override
    public GetOverseaDrillDownBaseInfoV2ResponseType getBus101102SubDrillDownBaseInfo(GetOverseaDrillDownBaseInfoV2RequestType request, String d, OverseaMetricInfoBean metricInfoBean) throws Exception {
        GetOverseaDrillDownBaseInfoV2ResponseType response = new GetOverseaDrillDownBaseInfoV2ResponseType();
        Boolean needSearch = StringUtils.isNotEmpty(request.getSearchInDim());
        String subMetric = request.getSubMetric();
        List<String> fieldList = new ArrayList<>();
        if (needSearch) {
            fieldList.add(request.getSearchInDim());
        } else {
            fieldList.addAll(Bus101102Helper.getDrillDownFieldListV2(subMetric, metricInfoBean, remoteConfig));
            response.setDefaultChosenDim(fieldList.get(0));
        }
        Map<String, Future<GetRawDataResponseType>> fieldMap = new HashMap<>();
        for (String field : fieldList) {
            SqlParamterBean sqlParamterBean = Bus101102Helper.getSiteDrillDownBaseInfoSqlBeanV2(field, request, d, metricInfoBean, remoteConfig);
            GetRawDataRequestType fieldRequest = sqlParamterBean.convertBeanToRequest(false);
            fieldMap.put(field,switchNewTableHelper.switchRemoteDatabaseAsync(fieldRequest));
        }
        List<DilldownDim> fieldDataItemList = new ArrayList<>();
        response.setDimList(fieldDataItemList);
        for (String field : fieldList) {
            DilldownDim item = new DilldownDim();
            fieldDataItemList.add(item);
            Bus101102Helper.processDrillDownBaseInfoV2(request, field, fieldMap.get(field).get(), item, remoteConfig);
        }
        return response;
    }

    @Override
    public GetOverseaTableDataV2ResponseType getBus101102SubTableData(GetOverseaTableDataV2RequestType request, String d, OverseaMetricInfoBean metricInfoBean) throws Exception {
        GetOverseaTableDataV2ResponseType response = new GetOverseaTableDataV2ResponseType();
        String metric = request.getMetric();
        String subMetric = request.getSubMetric();
        String field = request.getDimName();
        SubMetricFiledBean configBean = remoteConfig.getSubMetricFiledBeanV2(metric, subMetric, field);
        Boolean needBubble = configBean.getNeedBubble();
        String type = request.getQueryType();
        if ("bubble".equals(type) && !needBubble) {
            throw new InputArgumentException("this filed can't get bubble,please check it!");
        }
        Boolean needTarget = configBean.getNeedTarget();
        response.setTableHeaderList(Bus101102Helper.getTableHeaderListV2(configBean,needTarget));
        //获取当前数据
        OverseasPerformanceInfoParamBean overseasPerformanceInfoParamBean =
                Bus101102Helper.getChannelTableDataSqlBeanV2(null,request, d,metricInfoBean,"current",remoteConfig,null);
        Integer totalCount = cdmOrdTtdOverseasPerformanceIndexAddrDao.queryOverseasPerformanceInfoCount(overseasPerformanceInfoParamBean);
        if (totalCount == 0) {
            return response;
        }
        response.setTotalNum(totalCount);
        List<CdmOrdTtdOverseasPerformanceIndexBO> currentResult
                = cdmOrdTtdOverseasPerformanceIndexAddrDao.getOverseasProductionResponseInfo(overseasPerformanceInfoParamBean);
        List<DimOrdTtdSiteChannelTargetBO> targetResult=null;
        if (needTarget) {
            //获取目标数据
            //查站点渠道目标表
            DimOrdTtdSiteChannelTargetParamBean param =
                    Bus101102Helper.getDimOrdTtdSiteChannelTargetParamBean(request, metricInfoBean, "站点", d);//NOSONAR
            targetResult = dimOrdTtdSiteChannelTargetConfigDao.getDimOrdTtdSiteChannelTargetBO(param);
        }
        //获取去年数据
        List<CdmOrdTtdOverseasPerformanceIndexBO> lastYearResult =null;
        List<String> siteList=currentResult.stream().map(x->x.getSites()).collect(Collectors.toList());
            OverseasPerformanceInfoParamBean overseasPerformanceInfoParamBeanLastYear = Bus101102Helper.getChannelTableDataSqlBeanV2(null,request, d, metricInfoBean, "lastYear",remoteConfig,siteList);
            lastYearResult = cdmOrdTtdOverseasPerformanceIndexAddrDao.getOverseasProductionResponseInfo(overseasPerformanceInfoParamBeanLastYear);

        List<CdmOrdTtdOverseasPerformanceIndexBO> momResult = null;
        List<CdmOrdTtdOverseasPerformanceIndexBO> last30daysResult = null;
        if ("firstPage".equalsIgnoreCase(request.getQuerySource())) {
            //求30日环比
            OverseasPerformanceInfoParamBean momParam = Bus101102Helper.getChannelTableDataSqlBeanV2("30",request, d, metricInfoBean, null, remoteConfig, siteList);
            momResult = cdmOrdTtdOverseasPerformanceIndexAddrDao.getMomOverseasProductionResponseInfo(momParam);
            OverseasPerformanceInfoParamBean last30daysParam = Bus101102Helper.getChannelTableDataSqlBeanV2("last30",request, d, metricInfoBean, null, remoteConfig, siteList);
            last30daysResult = cdmOrdTtdOverseasPerformanceIndexAddrDao.getMomOverseasProductionResponseInfo(last30daysParam);
        }

        List<OverseaTableDataRow> tableDataItemList = new ArrayList<>();
        Bus101102Helper.processTableBaseDataByChannel(request,
                metricInfoBean,
                response.getTableHeaderList(),
                currentResult,
                targetResult,
                lastYearResult,
                momResult,last30daysResult,
                tableDataItemList);
        response.setRows(tableDataItemList);
        response.setTotalNum(totalCount);
        response.setMomType(OverseaMetricHelper.getMomType(request.getTimeFilter(), d));
        return response;
    }

    @Override
    public GetOverseaTableDataV2ResponseType getBus110SubTableData(GetOverseaTableDataV2RequestType request, String d, OverseaMetricInfoBean metricInfoBean) throws Exception {
        return getBus101102SubTableData(request, d, metricInfoBean);
    }

    @Override
    public GetOverseaTrendLineDataV2ResponseType getBus110SubTrendLineData(GetOverseaTrendLineDataV2RequestType request, String d, List<String> timeList) throws Exception {
        return siteMetricStrategyV2.getBus101102110SubTrendlineData(request, d, timeList, "站点");//NOSONAR
    }

    @Override
    public GetOverseaDrillDownBaseInfoV2ResponseType getBus110SubDrillDownBaseInfo(GetOverseaDrillDownBaseInfoV2RequestType request, String d, OverseaMetricInfoBean metricInfoBean) throws Exception {
        return getBus101102SubDrillDownBaseInfo(request,d,metricInfoBean);
    }

    @Override
    public Future<OveaseaSubMetric> getBus110SubMetricCardData(TimeFilter timeFilter, OverseaMetricInfoBeanV2 metricInfoBean, String d, String subMetric, GetOverseaMetricCardDataV2RequestType request) throws Exception {
        return siteMetricStrategyV2.getBus101102110SubMetricCardData(timeFilter, metricInfoBean, d, subMetric, request, "站点");//NOSONAR
    }

    @Override
    public GetOverseaDrillDownBaseInfoV2ResponseType getBus103SubDrillDownBaseInfo(GetOverseaDrillDownBaseInfoV2RequestType request, String d, OverseaMetricInfoBean metricInfoBean) throws Exception {
        GetOverseaDrillDownBaseInfoV2ResponseType response = new GetOverseaDrillDownBaseInfoV2ResponseType();
        Boolean needSearch = StringUtils.isNotEmpty(request.getSearchInDim());

        String subMetric = request.getSubMetric();
        List<String> fieldList = new ArrayList<>();
        if (needSearch) {
            fieldList.add(request.getSearchInDim());
        } else {
            fieldList.addAll(Bus103Helper.getDrillDownFieldList(subMetric, metricInfoBean, remoteConfig));
            response.setDefaultChosenDim(fieldList.get(0));
        }
        Map<String, Future<GetRawDataResponseType>> fieldMap = new HashMap<>();
        for (String field : fieldList) {
            SqlParamterBean sqlParamterBean = Bus103Helper.getSiteDrillDownBaseInfoSqlBeanV2(field, request, d, metricInfoBean, remoteConfig);
            GetRawDataRequestType fieldRequest = sqlParamterBean.convertBeanToRequest(false);
            fieldMap.put(field, switchNewTableHelper.switchRemoteDatabaseAsync(fieldRequest));
        }
        List<DilldownDim> fieldDataItemList = new ArrayList<>();
        response.setDimList(fieldDataItemList);
        for (String field : fieldList) {
            DilldownDim item = new DilldownDim();
            fieldDataItemList.add(item);
            Bus101102Helper.processDrillDownBaseInfoV2(request, field, fieldMap.get(field).get(), item, remoteConfig);
        }
        return response;
    }

    @Override
    public GetOverseaTableDataV2ResponseType getBus103SubTableData(GetOverseaTableDataV2RequestType request, String d, OverseaMetricInfoBean metricInfoBean) throws Exception {
        GetOverseaTableDataV2ResponseType response = new GetOverseaTableDataV2ResponseType();
        List<OverseaTableDataRow> tableDataItemList = new ArrayList<>();
        response.setRows(tableDataItemList);
        String metric = request.getMetric();
        String subMetric = request.getSubMetric();
        String field = request.getDimName();
        String year = request.getTimeFilter().getYear();
        int y = year != null ? Integer.parseInt(year) : 0;
        SubMetricFiledBean configBean;
        if(y >= 2024){
            configBean = remoteConfig.getSubMetricFiledBeanV2(year, metric, subMetric, field);
        }else {
            configBean = remoteConfig.getSubMetricFiledBeanV2(metric, subMetric, field);
        }
        Boolean needTarget = configBean.getNeedTarget();
        //获取当前数据
        SqlParamterBean currentBean = Bus103Helper.getSiteTableDataSqlBeanV2(request, d, metricInfoBean, configBean, remoteConfig, "current");
        GetRawDataRequestType currentReq = currentBean.convertBeanToRequest(true);
        ListenableFuture<GetRawDataResponseType> currentResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(currentReq);

        if (needTarget) {
            //获取目标数据
            SqlParamterBean targetBean = Bus103Helper.getSiteTableDataSqlBeanV2(request, d, metricInfoBean, configBean, remoteConfig, "target");
            GetRawDataRequestType targetReq = targetBean.convertBeanToRequest(true);
            ListenableFuture<GetRawDataResponseType> targetResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(targetReq);
            Bus103Helper.processTableBaseDataV2(currentResFuture.get(), targetResFuture.get(), tableDataItemList, needTarget, year);
        } else {
            Bus103Helper.processTableBaseDataV2(currentResFuture.get(), null, tableDataItemList, needTarget, year);
        }
        response.setTotalNum(currentResFuture.get().getTotalNum());
        response.setTableHeaderList(Bus103Helper.getTableHeaderList(configBean, year));
        response.setMomType(OverseaMetricHelper.getMomType(request.getTimeFilter(), d));
        return response;
    }

    @Override
    public String getSubMetricName() {
        return "site";
    }
}
