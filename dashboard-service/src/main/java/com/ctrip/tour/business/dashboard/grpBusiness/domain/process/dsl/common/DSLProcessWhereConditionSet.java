package com.ctrip.tour.business.dashboard.grpBusiness.domain.process.dsl.common;

import com.ctrip.soa._24922.DSLRequestType;
import com.ctrip.soa._24922.EnumOperators;
import com.ctrip.soa._24922.WhereCondition;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.AbstractPreDSLProcess;
import qunar.agile.Strings;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class DSLProcessWhereConditionSet extends AbstractPreDSLProcess {
    private List<WhereCondition> whereConditions;

    public static AbstractPreDSLProcess getInstance() {
        return new DSLProcessWhereConditionSet();
    }

    public static AbstractPreDSLProcess getInstance(String filterName, List<String> filterValues) {
        DSLProcessWhereConditionSet dslProcessLimitSet = new DSLProcessWhereConditionSet();
        if (filterName == null || filterName.isEmpty()) {
            return dslProcessLimitSet;
        }
        if (filterValues == null || filterValues.isEmpty()) {
            return dslProcessLimitSet;
        }
        // 初始化
        WhereCondition whereCondition = new WhereCondition();
        whereCondition.setFilterName("cur_filter_str");
        whereCondition.setFilterValues(Collections.singletonList(filterName + " in ('" + String.join("','", filterValues) + "')"));
        whereCondition.setOperators(EnumOperators.IN);
        dslProcessLimitSet.whereConditions = new ArrayList<>();
        dslProcessLimitSet.whereConditions.add(whereCondition);
        return dslProcessLimitSet;
    }

    @Override
    public DSLRequestType process(DSLRequestType dslRequestType, EarlyReturn earlyReturn) {
        if (whereConditions == null || whereConditions.isEmpty()) {
            return dslRequestType;
        }
        // 初始化
        if (dslRequestType.getWhereCondition() == null) {
            dslRequestType.setWhereCondition(new WhereCondition());
        }
        if (dslRequestType.getWhereCondition().getSubWhereConditions() == null) {
            dslRequestType.getWhereCondition().setSubWhereConditions(new ArrayList<>());
        }
        // where condition
        dslRequestType.getWhereCondition().getSubWhereConditions().addAll(whereConditions);
        return dslRequestType;
    }
}
