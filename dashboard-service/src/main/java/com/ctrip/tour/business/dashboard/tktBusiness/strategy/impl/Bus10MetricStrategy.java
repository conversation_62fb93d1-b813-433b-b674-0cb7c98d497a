package com.ctrip.tour.business.dashboard.tktBusiness.strategy.impl;

import com.ctrip.soa._24922.*;
import com.ctrip.soa._27181.BIBaseReportQueryServiceClient;
import com.ctrip.soa._27181.GetRawDataRequestType;
import com.ctrip.soa._27181.GetRawDataResponseType;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.*;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.SinglePeriodTrendLineBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.bo.ExamineConfigBo;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardExamineeConfigV2Dao;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.Bus10Helper;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.MetricHelper;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.MultiPeriodMappingHelper;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.SwitchNewTableHelper;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.MetricCalStrategy;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

@Component
public class Bus10MetricStrategy implements MetricCalStrategy {


    @Autowired
    private BIBaseReportQueryServiceClient baseReportQueryServiceClient;

    @Autowired
    private BusinessDashboardExamineeConfigV2Dao examineeConfigV2Dao;

    @Autowired
    private RemoteConfig remoteConfig;

    @Autowired
    private SinglePeriodTrendLineBiz singlePeriodTrendLineBiz;

    @Autowired
    private SwitchNewTableHelper switchNewTableHelper;

    @Override
    public Future<MetricDetailInfo> getSingleMetricCardData(String domainName,
                                                            TimeFilter timeFilter,
                                                            MetricInfoBean metricInfoBean,
                                                            String d,
                                                            Boolean needRank) throws Exception {
        MetricDetailInfo metricDetailInfo = new MetricDetailInfo();
        String metric = metricInfoBean.getMetric();
        Map<String, Double> dimMap = new HashMap<>();
        metricDetailInfo.setDimData(dimMap);
        metricDetailInfo.setMetric(metric);
        MetricHelper.setMetricCardDrillDownInfo(metricInfoBean, metricDetailInfo);

        SqlParamterBean bean = Bus10Helper.getMetricCardSqlBean(timeFilter, metricInfoBean, d);
        GetRawDataRequestType metricCardReq = bean.convertBeanToRequest(true);
        GetRawDataResponseType metricCardRes = switchNewTableHelper.switchRemoteDatabase(metricCardReq);
        Bus10Helper.processMetricCardData(metricCardRes, dimMap);
        if (needRank) {
            SqlParamterBean rankingBean = Bus10Helper.getRankingSqlBean(domainName, timeFilter, d, "10");
            GetRawDataRequestType rankingReq = rankingBean.convertBeanToRequest(true);
            GetRawDataResponseType rankingRes = switchNewTableHelper.switchRemoteDatabase(rankingReq);
            Bus10Helper.processRankData(rankingRes, metricDetailInfo);
        }
        return new AsyncResult<>(metricDetailInfo);
    }

    @Override
    public GetTrendLineDataResponseType getSingleTrendlineData(GetTrendLineDataRequestType request,
                                                               MetricInfoBean metricInfoBean,
                                                               String d) throws Exception {

        GetTrendLineDataResponseType response = new GetTrendLineDataResponseType();
        List<TrendLineDetailInfo> trendLineDetailInfoList = new ArrayList<>();
        response.setTrendLineDetailInfoList(trendLineDetailInfoList);

        TimeFilter timeFilter = request.getTimeFilter();
        String domainName = request.getDomainName();

        List<ExamineConfigBean> examineConfigBeanList = MultiPeriodMappingHelper.getMultiPeriodConfigList(timeFilter, d, examineeConfigV2Dao.queryMetricAllConfig(domainName, d, getMetricName()), null);
        List<Future<SinglePeriodDataBean>> futureList = new ArrayList<>();

        for (ExamineConfigBean bean : examineConfigBeanList) {
            ExamineConfigBo bo = new ExamineConfigBo();
            MetricInfoBean innerMetricInfoBean = bo.getMetricInfoBean(bean.getBusinessDashboardExamineeConfigV2(), remoteConfig);
            SqlParamterBean sqlParamterBean = Bus10Helper.getTrendlineSqlBean(request, bean, innerMetricInfoBean, d);
            GetRawDataRequestType singlePeriodReq = sqlParamterBean.convertBeanToRequest(true);
            futureList.add(singlePeriodTrendLineBiz.getBus10SinglePeriodTrendLineData(request, singlePeriodReq, bean, innerMetricInfoBean));
        }

        //转化汇总返回数据
        PeriodDataBean periodDataBean = new PeriodDataBean();
        periodDataBean.setBeanData(futureList);

        //由于该指标要求23年开始生效  可以等价为海外指标
        List<String> timeList = DateUtil.getAllSelectedTime(request.getTimeFilter(), "oversea");

        String queryType = request.getQueryType();
        if ("trendline".equals(queryType)) {
            Bus10Helper.processTrendLineData(trendLineDetailInfoList, periodDataBean, timeList);
        } else {
            Bus10Helper.processDrilldownTrendLineData(trendLineDetailInfoList, periodDataBean, timeList);
        }

        return response;
    }

    @Override
    public GetTableDataResponseType getSingleTableData(GetTableDataRequestType request,
                                                       MetricInfoBean metricInfoBean,
                                                       String d) throws Exception {
        GetTableDataResponseType response = new GetTableDataResponseType();
        List<TableDataItem> tableDataItemList = new ArrayList<>();
        response.setTableDataItemList(tableDataItemList);

        SqlParamterBean bean = Bus10Helper.getTableSqlBean(request, d, metricInfoBean, remoteConfig);
        GetRawDataRequestType tableReq = bean.convertBeanToRequest(true);
        GetRawDataResponseType tableRes = switchNewTableHelper.switchRemoteDatabase(tableReq);

        Bus10Helper.processTableData(tableRes, tableDataItemList);
        response.setTotalNum(tableRes.getTotalNum());
        return response;
    }

    @Override
    public GetDrillDownBaseInfoResponseType getSingleDrillDownBaseInfo(GetDrillDownBaseInfoRequestType request,
                                                                       MetricInfoBean metricInfoBean,
                                                                       String d) throws Exception {
        GetDrillDownBaseInfoResponseType response = new GetDrillDownBaseInfoResponseType();
        List<FieldDataItem> fieldDataItemList = new ArrayList<>();
        response.setFieldDataItemList(fieldDataItemList);

        Boolean needSearch = request.isNeedSearch();
        if (GeneralUtil.isEmpty(needSearch)) {
            request.setNeedSearch(false);
            needSearch = false;
        }
        List<String> fieldList = new ArrayList<>();
        if (needSearch) {
            fieldList.add(MetricHelper.getDrillDownColumnName(request.getSearchField()));
        } else {
            fieldList.addAll(Bus10Helper.getDrillDownFieldList(metricInfoBean));
            response.setDefaultChosenField(MetricHelper.getDataBaseColumnName(fieldList.get(0)));
        }
        Map<String, Future<GetRawDataResponseType>> fieldMap = new HashMap<>();
        for (String field : fieldList) {
            SqlParamterBean sqlParamterBean = Bus10Helper.getDrillDownBaseInfoSqlBean(field, request, d, metricInfoBean, remoteConfig);
            GetRawDataRequestType fieldRequest = sqlParamterBean.convertBeanToRequest(false);
            fieldMap.put(field, switchNewTableHelper.switchRemoteDatabaseAsync(fieldRequest));
        }

        for (String field : fieldList) {
            Bus10Helper.processDrillDownBaseInfo(field, fieldMap.get(field).get(), fieldDataItemList);
        }

        return response;
    }

    @Override
    public String getMetricName() {
        return "10";
    }
}
