package com.ctrip.tour.business.dashboard.grpBusiness.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Sensitive;
import com.ctrip.platform.dal.dao.annotation.Type;
import java.sql.Types;
import java.math.BigDecimal;
import java.sql.Timestamp;

import com.ctrip.platform.dal.dao.DalPojo;

/**
 * <AUTHOR>
 * @date 2025-03-31
 */
@Entity
@Database(name = "ttdreportdb_dalcluster")
@Table(name = "dim_ord_grp_achv2025_person_trgt")
public class DimOrdGrpAchv2025PersonTrgt implements DalPojo {

    /**
     * 主键
     */
    @Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long id;

    /**
     * 出发月份
     */
	@Column(name = "dep_month")
	@Type(value = Types.VARCHAR)
	private String depMonth;

    /**
     * 目的地区域
     */
	@Column(name = "dest_domain")
	@Type(value = Types.VARCHAR)
	private String destDomain;

    /**
     * 销售渠道
     */
	@Column(name = "sale_channel")
	@Type(value = Types.VARCHAR)
	private String saleChannel;

    /**
     * 销售模式
     */
	@Column(name = "sale_mode")
	@Type(value = Types.VARCHAR)
	private String saleMode;

    /**
     * 运营中心
     */
	@Column(name = "region_center")
	@Type(value = Types.VARCHAR)
	private String regionCenter;

    /**
     * 产品大区
     */
	@Column(name = "prd_region")
	@Type(value = Types.VARCHAR)
	private String prdRegion;

    /**
     * 产品类型
     */
	@Column(name = "prd_catg")
	@Type(value = Types.VARCHAR)
	private String prdCatg;

    /**
     * 目的地大洲
     */
	@Column(name = "dest_cont")
	@Type(value = Types.VARCHAR)
	private String destCont;

    /**
     * 目的地国家
     */
	@Column(name = "dest_ctry")
	@Type(value = Types.VARCHAR)
	private String destCtry;

    /**
     * 目的地省份
     */
	@Column(name = "dest_province")
	@Type(value = Types.VARCHAR)
	private String destProvince;

    /**
     * 一级业务经理
     */
	@Column(name = "pm_lvl1")
	@Type(value = Types.VARCHAR)
	private String pmLvl1;

    /**
     * 一级业务经理工号
     */
	@Column(name = "pm_lvl1_no")
	@Type(value = Types.VARCHAR)
	private String pmLvl1No;

    /**
     * 二级业务经理
     */
	@Column(name = "pm_lvl2")
	@Type(value = Types.VARCHAR)
	private String pmLvl2;

    /**
     * 二级业务经理工号
     */
	@Column(name = "pm_lvl2_no")
	@Type(value = Types.VARCHAR)
	private String pmLvl2No;

    /**
     * 三级业务经理
     */
	@Column(name = "pm_lvl3")
	@Type(value = Types.VARCHAR)
	private String pmLvl3;

    /**
     * 三级业务经理工号
     */
	@Column(name = "pm_lvl3_no")
	@Type(value = Types.VARCHAR)
	private String pmLvl3No;

    /**
     * 人头指标
     */
	@Column(name = "target_np")
	@Type(value = Types.DECIMAL)
	private BigDecimal targetNp;

    /**
     * 销售额指标
     */
	@Column(name = "target_income")
	@Type(value = Types.DECIMAL)
	private BigDecimal targetIncome;

    /**
     * 毛利指标
     */
	@Column(name = "target_profit")
	@Type(value = Types.DECIMAL)
	private BigDecimal targetProfit;

    /**
     * 更新时间
     */
	@Column(name = "datachange_lasttime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;

	private String groupColName;

	public String getGroupColName() {
		return groupColName;
	}

	public void setGroupColName(String groupColName) {
		this.groupColName = groupColName;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getDepMonth() {
		return depMonth;
	}

	public void setDepMonth(String depMonth) {
		this.depMonth = depMonth;
	}

	public String getDestDomain() {
		return destDomain;
	}

	public void setDestDomain(String destDomain) {
		this.destDomain = destDomain;
	}

	public String getSaleChannel() {
		return saleChannel;
	}

	public void setSaleChannel(String saleChannel) {
		this.saleChannel = saleChannel;
	}

	public String getSaleMode() {
		return saleMode;
	}

	public void setSaleMode(String saleMode) {
		this.saleMode = saleMode;
	}

	public String getRegionCenter() {
		return regionCenter;
	}

	public void setRegionCenter(String regionCenter) {
		this.regionCenter = regionCenter;
	}

	public String getPrdRegion() {
		return prdRegion;
	}

	public void setPrdRegion(String prdRegion) {
		this.prdRegion = prdRegion;
	}

	public String getPrdCatg() {
		return prdCatg;
	}

	public void setPrdCatg(String prdCatg) {
		this.prdCatg = prdCatg;
	}

	public String getDestCont() {
		return destCont;
	}

	public void setDestCont(String destCont) {
		this.destCont = destCont;
	}

	public String getDestCtry() {
		return destCtry;
	}

	public void setDestCtry(String destCtry) {
		this.destCtry = destCtry;
	}

	public String getDestProvince() {
		return destProvince;
	}

	public void setDestProvince(String destProvince) {
		this.destProvince = destProvince;
	}

	public String getPmLvl1() {
		return pmLvl1;
	}

	public void setPmLvl1(String pmLvl1) {
		this.pmLvl1 = pmLvl1;
	}

	public String getPmLvl1No() {
		return pmLvl1No;
	}

	public void setPmLvl1No(String pmLvl1No) {
		this.pmLvl1No = pmLvl1No;
	}

	public String getPmLvl2() {
		return pmLvl2;
	}

	public void setPmLvl2(String pmLvl2) {
		this.pmLvl2 = pmLvl2;
	}

	public String getPmLvl2No() {
		return pmLvl2No;
	}

	public void setPmLvl2No(String pmLvl2No) {
		this.pmLvl2No = pmLvl2No;
	}

	public String getPmLvl3() {
		return pmLvl3;
	}

	public void setPmLvl3(String pmLvl3) {
		this.pmLvl3 = pmLvl3;
	}

	public String getPmLvl3No() {
		return pmLvl3No;
	}

	public void setPmLvl3No(String pmLvl3No) {
		this.pmLvl3No = pmLvl3No;
	}

	public BigDecimal getTargetNp() {
		return targetNp;
	}

	public void setTargetNp(BigDecimal targetNp) {
		this.targetNp = targetNp;
	}

	public BigDecimal getTargetIncome() {
		return targetIncome;
	}

	public void setTargetIncome(BigDecimal targetIncome) {
		this.targetIncome = targetIncome;
	}

	public BigDecimal getTargetProfit() {
		return targetProfit;
	}

	public void setTargetProfit(BigDecimal targetProfit) {
		this.targetProfit = targetProfit;
	}

	public Timestamp getDatachangeLasttime() {
		return datachangeLasttime;
	}

	public void setDatachangeLasttime(Timestamp datachangeLasttime) {
		this.datachangeLasttime = datachangeLasttime;
	}

}