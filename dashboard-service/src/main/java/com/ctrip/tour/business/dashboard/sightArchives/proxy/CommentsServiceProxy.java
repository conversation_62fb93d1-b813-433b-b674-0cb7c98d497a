package com.ctrip.tour.business.dashboard.sightArchives.proxy;

import com.ctrip.gs.soa.commentsservice.*;
import com.ctrip.ibu.platform.shark.sdk.api.Shark;
import com.ctrip.soa._24922.CurrentCommentScore;
import com.ctrip.soa._24922.DetailCommentScore;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.ScenicLanguageHelper;
import com.ctrip.tour.business.dashboard.utils.MapperUtil;
import com.ctrip.tour.business.dashboard.utils.UserUtil;
import com.ctriposs.baiji.rpc.mobile.common.types.MobileRequestHead;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class CommentsServiceProxy {

    CommentsServiceClient commentsServiceClient = CommentsServiceClient.getInstance();

    @Autowired
    private RemoteConfig remoteConfig;

    public CurrentCommentScore getPoiCommentInfoWithHotTag(Long poiId) {

        //测试环境mock数据
        if("mock".equals(remoteConfig.getConfigValue("getPoiCommentInfoWithHotTag"))){
            CurrentCommentScore currentCommentScore = new CurrentCommentScore();
            currentCommentScore.setCommentScore(4.6);
            currentCommentScore.setCommentCount(13452);
            String scoreText = "很好"; //NOSONAR
            currentCommentScore.setCommentScoreDesc(scoreText);
            List<DetailCommentScore> detailCommentScoreList = new ArrayList<>();
            DetailCommentScore detailCommentScore = new DetailCommentScore();
            detailCommentScore.setName("景色"); //NOSONAR
            detailCommentScore.setScore(4.8);
            detailCommentScoreList.add(detailCommentScore);
            DetailCommentScore detailCommentScore1 = new DetailCommentScore();
            detailCommentScore1.setName("趣味"); //NOSONAR
            detailCommentScore1.setScore(4.6);
            detailCommentScoreList.add(detailCommentScore1);
            DetailCommentScore detailCommentScore2 = new DetailCommentScore();
            detailCommentScore2.setName("性价比"); //NOSONAR
            detailCommentScore2.setScore(4.7);
            detailCommentScoreList.add(detailCommentScore2);
            currentCommentScore.setDetailCommentScore(detailCommentScoreList);
            return currentCommentScore;
        }

        CurrentCommentScore currentCommentScore = new CurrentCommentScore();
        GetPoiCommentInfoWithHotTagRequestType requestType = new GetPoiCommentInfoWithHotTagRequestType();

        GetPoiCommentInfoWithHotTagArg arg = new GetPoiCommentInfoWithHotTagArg();
        arg.setPoiId(poiId);
        arg.setPageType(1);
        arg.setSourceType(1);
        arg.setShowAllTag(true);
        requestType.setArg(arg);
        MobileRequestHead head = new MobileRequestHead();
        head.setAppid("100038120");
//        head.setLang(UserUtil.getVbkLocale());
        requestType.setHead(head);

        GetPoiCommentInfoWithHotTagResponseType responseType = null;
        try {
            log.info("getPoiCommentInfoWithHotTag requestType: {}", MapperUtil.obj2Str(requestType));
            responseType = commentsServiceClient.getPoiCommentInfoWithHotTag(requestType);
            log.info("getPoiCommentInfoWithHotTag responseType: {}", MapperUtil.obj2Str(responseType));
            GetPoiCommentInfoWithHotTagResult poiCommentInfoWithHotTagResult = responseType.getResult();
            BasePoiCommentInfoType basePoiCommentInfoType = poiCommentInfoWithHotTagResult.getPoiInfo();
            Integer commentCount = basePoiCommentInfoType.getCommentCount();  //点评数
            currentCommentScore.setCommentCount(commentCount);

            BigDecimal commentScore = basePoiCommentInfoType.getCommentScore();  //点评分
            if(commentScore ==null){
                return currentCommentScore;
            }

            double commentScoreDouble = commentScore.doubleValue();
            currentCommentScore.setCommentScore(commentScoreDouble);
            String scoreText = "";
            String sharkKey = "";
            if(commentScoreDouble >= 5) {
                scoreText = "超棒";  //NOSONAR
                sharkKey = "key.review.poi.write.level5";
            }else if(commentScoreDouble >= 4) {
                scoreText = "很好";  //NOSONAR
                sharkKey = "key.review.poi.write.level4";
            }else if(commentScoreDouble >= 3) {
                scoreText = "不错"; //NOSONAR
                sharkKey = "key.review.poi.write.level3";
            }else if(commentScoreDouble >= 2) {
                scoreText = "一般";  //NOSONAR
                sharkKey = "key.review.poi.write.level2";
            }else if(commentScoreDouble >= 1) {
                scoreText = "不佳";  //NOSONAR
                sharkKey = "key.review.poi.write.level1";
            }
            currentCommentScore.setCommentScoreDesc(scoreText);

            //多语言处理
            if ("T".equals(remoteConfig.getConfigValue("languageSwitch")) && "en-US".equals(UserUtil.getVbkLocale())){
                String enSharkValue = Shark.getByLocale(100035353, sharkKey, UserUtil.getVbkLocale());
                currentCommentScore.setCommentScoreDesc(enSharkValue);
            }

            List<BaseScoreInfoType> baseScoreInfoTypes = basePoiCommentInfoType.getScores();
            List<DetailCommentScore> detailCommentScoreList = new ArrayList<>();
            for(BaseScoreInfoType baseScoreInfoType : baseScoreInfoTypes){
                String name = baseScoreInfoType.getName();  //点评小分名称
                BigDecimal score = baseScoreInfoType.getScore();  //点评小分分数
                DetailCommentScore detailCommentScore = new DetailCommentScore();
                detailCommentScore.setName(ScenicLanguageHelper.getMultiLanguage(name, UserUtil.getVbkLocaleForScenic()));    //todo 修改位置需要检验
                detailCommentScore.setScore(score.doubleValue());
                detailCommentScoreList.add(detailCommentScore);
            }
            currentCommentScore.setDetailCommentScore(detailCommentScoreList);
        } catch (Exception e) {
            log.warn("getPoiCommentInfoWithHotTag failed", e);
        }
        return currentCommentScore;

    }
}
