package com.ctrip.tour.business.dashboard.sightArchives.dao.qualityDao;

import com.ctrip.soa._24922.ComplaintQuestion;
import com.ctrip.soa._27181.PreparedParameterBean;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.TktStarRocksDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.sql.Types;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
@Repository
@Slf4j
public class AdmSevTtdViewspotQuestionRankDfDao {
    @Autowired
    private TktStarRocksDao tktStarRocksDao;

    public List<Map<String, Object>> queryComplaintMetric(String queryD,Long sightId, Boolean needSubSight, String startDate, String endDate, Integer dateType, Integer businessType, List<Long> vendorIdList) {
        StringBuilder sql = new StringBuilder("select complain_question_type," +
                "cast(sum(complain_question_cnt) as Integer) as complain_question_cnt," +
                "cast(sum(sev_question_cnt) as Integer) as sev_question_cnt" +
                " from adm_sev_ttd_viewspot_question_rank_df");
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendSightId(parameters, sql, sightId, needSubSight);
        appendQueryD(parameters, sql, queryD);
        appendDateRange(parameters, sql, dateType, startDate, endDate);
        appendBusinessType(parameters, sql, businessType);
        appendVendorIdList(parameters, sql, vendorIdList);
        sql.append(" group by complain_question_type");
        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        if (result.size() == 0) {
            return new ArrayList<>();
        }
        return result;
    }

    //拼景点id
    private void appendSightId(List<PreparedParameterBean> parameters, StringBuilder sql, Long sightId, Boolean needSubSight){
        if(needSubSight){
            sql.append(" where viewspotid = ?");
        }else {
            sql.append(" where sub_viewspotid = ?");
        }
        parameters.add(new PreparedParameterBean(String.valueOf(sightId), Types.BIGINT));
    }
    //拼日期范围
    private void appendDateRange(List<PreparedParameterBean> parameters, StringBuilder sql, Integer dateType, String startDate, String endDate){


        sql.append(" and dep_date between ? and ?");

        parameters.add(new PreparedParameterBean(startDate, Types.VARCHAR));
        parameters.add(new PreparedParameterBean(endDate, Types.VARCHAR));

    }

    //拼业务类型
    private void appendBusinessType(List<PreparedParameterBean> parameters, StringBuilder sql, Integer businessType){
        if(businessType != 1){
            sql.append(" and bu_type = ?");
            parameters.add(new PreparedParameterBean(businessType==2?"门票":"活动", Types.VARCHAR)); //NOSONAR
        }

    }
    //拼供应商id列表
    private void appendVendorIdList(List<PreparedParameterBean> parameters, StringBuilder sql, List<Long> vendorIdList){
        if(CollectionUtils.isNotEmpty(vendorIdList)){
            sql.append(" and provider_id in (");
            for(int i = 0; i < vendorIdList.size(); i++){
                if(i == 0){
                    sql.append("?");
                }else {
                    sql.append(",?");
                }
                parameters.add(new PreparedParameterBean(String.valueOf(vendorIdList.get(i)), Types.BIGINT));
            }
            sql.append(")");
        }
    }
    private void appendDateRangeOfLastYear(List<PreparedParameterBean> lyparameters, StringBuilder sql, String startDate, String endDate) {
        sql.append(" and t2.date_solar between ? and ?");
        lyparameters.add(new PreparedParameterBean(startDate, Types.VARCHAR));
        lyparameters.add(new PreparedParameterBean(endDate, Types.VARCHAR));
    }


    public List<Map<String, Object>> queryComplaintMetricYoy(String queryD, Long sightId, Boolean needSubSight, String startDate, String endDate, Integer dateType, Integer businessType, List<Long> vendorIdList) {
        StringBuilder sql = new StringBuilder("select " +
                "cast(sum(complain_cnt) as Integer) as complain_cnt," +
                "cast(sum(odr_cnt) as Integer) as odr_cnt" +
                " from adm_sev_ttd_viewspot_file_index_df t1 inner join v_dim_date t2 on t1.dep_date = t2.date_lastyear ");
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendSightId(parameters, sql, sightId, needSubSight);
        sql.append(" and t1.d = ? ");
        parameters.add(new PreparedParameterBean(queryD, Types.VARCHAR));
        appendDateRangeOfLastYear(parameters, sql, startDate, endDate);
        appendBusinessType(parameters, sql, businessType);
        appendVendorIdList(parameters, sql, vendorIdList);
        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        if (result.size() == 0) {
            return new ArrayList<>();
        }
        return result;
    }

    public List<Map<String, Object>> queryComplaintMetricPop(String queryD,Long sightId, Boolean needSubSight, String startDate, String endDate, Integer dateType, Integer businessType, List<Long> vendorIdList) {
        StringBuilder sql = new StringBuilder("select " +
                "cast(sum(complain_cnt) as Integer) as complain_cnt," +
                "cast(sum(odr_cnt) as Integer) as odr_cnt" +
                " from adm_sev_ttd_viewspot_question_rank_df");
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendSightId(parameters, sql, sightId, needSubSight);
        appendQueryD(parameters, sql, queryD);
        try {
            appendDateRange(parameters, sql, dateType, DateUtil.getPopTime(startDate, 1), DateUtil.getPopTime(endDate, 1));
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        appendBusinessType(parameters, sql, businessType);
        appendVendorIdList(parameters, sql, vendorIdList);
        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        if (result.size() == 0) {
            return new ArrayList<>();
        }
        return result;
    }

    public List<Map<String, Object>> querySevMetric(String queryD, Long sightId, Boolean needSubSight, String startDate, String endDate, Integer dateType, Integer businessType, List<Long> vendorIdList) {
        StringBuilder sql = new StringBuilder("select sev_question_type," +
                "cast(sum(sev_question_cnt) as Integer) as sev_question_cnt" +
                " from adm_sev_ttd_viewspot_question_rank_df");
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendSightId(parameters, sql, sightId, needSubSight);
        appendQueryD(parameters, sql, queryD);
        appendDateRange(parameters, sql, dateType, startDate, endDate);
        appendBusinessType(parameters, sql, businessType);
        appendVendorIdList(parameters, sql, vendorIdList);
        sql.append(" group by sev_question_type");
        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        if (result.size() == 0) {
            return new ArrayList<>();
        }
        return result;
    }


    public List<Map<String, Object>> queryPositiveComment(String queryD,Long sightId, Boolean needSubSight, String startDate, String endDate, Integer dateType, Integer businessType, List<Long> vendorIdList) {
        StringBuilder sql = new StringBuilder("select good_keywords," +
                "cast(sum(good_keywords_cnt) as Integer) as good_keywords_cnt" +
                " from adm_sev_ttd_viewspot_question_rank_df");
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendSightId(parameters, sql, sightId, needSubSight);
        appendQueryD(parameters, sql, queryD);
        appendDateRange(parameters, sql, dateType, startDate, endDate);
        appendBusinessType(parameters, sql, businessType);
        appendVendorIdList(parameters, sql, vendorIdList);
        sql.append(" and good_keywords_cnt > 0");
        sql.append(" group by good_keywords");
        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        if (result.size() == 0) {
            return new ArrayList<>();
        }
        return result;
    }

    private void appendQueryD(List<PreparedParameterBean> parameters, StringBuilder sql, String queryD){
        sql.append(" and d = ?");
        parameters.add(new PreparedParameterBean(queryD, Types.VARCHAR));
    }

    public List<Map<String, Object>> queryNegativeComment(String queryD,Long sightId, Boolean needSubSight, String startDate, String endDate, Integer dateType, Integer businessType, List<Long> vendorIdList) {
        StringBuilder sql = new StringBuilder("select bad_keywords," +
                "cast(sum(bad_keywords_cnt) as Integer) as bad_keywords_cnt" +
                " from adm_sev_ttd_viewspot_question_rank_df");
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendSightId(parameters, sql, sightId, needSubSight);
        appendQueryD(parameters, sql, queryD);
        appendDateRange(parameters, sql, dateType, startDate, endDate);
        appendBusinessType(parameters, sql, businessType);
        appendVendorIdList(parameters, sql, vendorIdList);
        sql.append(" and bad_keywords_cnt > 0");
        sql.append(" group by bad_keywords");
        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        if (result.size() == 0) {
            return new ArrayList<>();
        }
        return result;
    }
}
