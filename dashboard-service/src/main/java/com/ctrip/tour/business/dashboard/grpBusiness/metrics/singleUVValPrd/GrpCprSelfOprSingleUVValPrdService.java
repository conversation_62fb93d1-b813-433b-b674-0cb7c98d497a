package com.ctrip.tour.business.dashboard.grpBusiness.metrics.singleUVValPrd;


import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import com.ctrip.platform.dal.dao.helper.DalColumnMapRowMapper;
import com.ctrip.soa._24922.GetGrpMetricDataRequestType;
import com.ctrip.soa._24922.GrpMetric;
import com.ctrip.soa._24922.GrpTrendLinePoint;
import com.ctrip.tour.business.dashboard.grpBusiness.annotation.IndexAssemblyHandler;
import com.ctrip.tour.business.dashboard.grpBusiness.dao.starrocks.CdmSevGrpCprPlatformSelfSrvCrDfDao;
import com.ctrip.tour.business.dashboard.grpBusiness.enums.MetricCategoryEnum;
import com.ctrip.tour.business.dashboard.grpBusiness.enums.MetricEnum;
import com.ctrip.tour.business.dashboard.grpBusiness.handler.dim.DimconvertHandler;
import com.ctrip.tour.business.dashboard.grpBusiness.handler.metric.uv.GrpCrpSelfOprUvQueryService;
import com.ctrip.tour.business.dashboard.grpBusiness.metrics.IndexCommonQueryAbstractSerice;
import com.ctrip.tour.business.dashboard.utils.MapperUtil;
import com.ctrip.tour.business.dashboard.utils.SqlBuilder;
import com.google.common.base.Joiner;

import lombok.extern.slf4j.Slf4j;

@Service
@IndexAssemblyHandler(calcDateName = "dep_date",
        calcFieldName = "self_opr_num,self_opr_pv", calcExpression = "self_opr_num / self_opr_pv",
        tableName = "adm_ord_grp_work_platform_prdt_df")
@Slf4j
public class GrpCprSelfOprSingleUVValPrdService extends IndexCommonQueryAbstractSerice {

    @Autowired
    private GrpCrpSelfOprUvQueryService selfOprUvQueryService;
    @Autowired
    CdmSevGrpCprPlatformSelfSrvCrDfDao selfSrvCrDfDao;

    public GrpCprSelfOprSingleUVValPrdService(ApplicationContext ac) {
        super(ac);
    }

    @Override
    protected String otherCon(Map<String, ?> param, List<String> groupByCols, String timeAggType) {
        return doOtherCon(param, MetricCategoryEnum.CONVERSION_RATE_CATEGORY.getEnglishName());
    }

    @Override
    protected SqlBuilder selectColsAssembly(SqlBuilder sqlBuilder, Map<String, ?> param, List<String> groupByCols, String timeAggType) {

        String sql = " sum(suc_income) as self_opr_num ";

        if (CollectionUtils.isNotEmpty(groupByCols)) {
            sql = String.join(",", sql, Joiner.on(",").skipNulls().join(groupByCols));
        }

        sqlBuilder.select(sql);

        return sqlBuilder;
    }

    @Override
    protected List<Map<String, Object>> queryData(String sql, Map<String, ?> param, List<String> groupByCols, String timeAggType) {
        List<Map<String, Object>> queryData = null;
        try {
            DalColumnMapRowMapper dalColumnMapRowMapper = new DalColumnMapRowMapper();
            queryData = selfSrvCrDfDao.query(sql, param);
            if (StringUtils.equals(timeAggType, "day")) {
                queryData =  handleResults(this, param, timeAggType, true, queryData, groupByCols);
            }
            return queryData;
        } catch (SQLException e) {
            log.warn("query cdm_sev_grp_cpr_platform_self_srv_cr_df error", e);

        }
        return null;
    }

    @Override
    protected List<Map<String, Object>> handleResults(IndexCommonQueryAbstractSerice serice, Map<String, ?> param, String timeAggType, boolean needTimeAgg, List<Map<String, Object>> rowData, List<String> groupCols) {

        String extInfo = (String)param.get("ext_info");

        GetGrpMetricDataRequestType requestType = MapperUtil.str2Obj(extInfo, GetGrpMetricDataRequestType.class);

        try {

            if (CollectionUtils.isNotEmpty(groupCols) && StringUtils.isBlank(timeAggType)) {
                String groupCol = groupCols.get(0);
                String incomeTblName = DimconvertHandler.convertDimEnumName(groupCol, MetricCategoryEnum.SINGLE_UV_VAL_PRD_CATEGORY.getEnglishName());
                Map<String, GrpMetric> dillDownResult = selfOprUvQueryService.queryMetricDillDownResult(requestType, MetricEnum.SELF_OPR_UV, 0, selfOprUvQueryService);

                for (Map<String, Object> rowDatum : rowData) {
                    dillDownResult.forEach((k, v) -> {

                        if (StringUtils.equals((String) rowDatum.get(incomeTblName),k)) {
                            rowDatum.put("self_opr_pv", v.getMetricValue());
                        }
                    });

                }

            } else if (StringUtils.isNotBlank(timeAggType)) {

                List<GrpTrendLinePoint> trendLinePoints = selfOprUvQueryService.queryMetricTrendLineP(requestType, MetricEnum.SELF_OPR_UV,
                        selfOprUvQueryService, requestType.getBusinessLine());
                Map<String, GrpMetric> dateValMap = trendLinePoints.stream().collect(Collectors.toMap(GrpTrendLinePoint::getDate,
                        g -> g.getTrendLinePoints().get(0), (v1, v2) -> v1));
                for (Map<String, Object> rowDatum : rowData) {
                    String depDate = (String)rowDatum.get("dep_date");
                    GrpMetric metric = dateValMap.get(depDate);
                    if (Objects.nonNull(metric) && Objects.nonNull(metric.getMetricValue())) {
                        rowDatum.put("self_opr_pv", metric.getMetricValue());
                    }
                }

            } else {
                GrpMetric metric = selfOprUvQueryService.queryMetricValue(requestType, MetricEnum.SELF_OPR_UV, selfOprUvQueryService, requestType.getBusinessLine());
                rowData.forEach(row -> {
                    row.put("self_opr_pv", metric.getMetricValue());
                });
            }
        } catch (Exception e) {
            log.warn("query trgt error", e);
        }

        return doHandleResults(serice, param, timeAggType, needTimeAgg, rowData);
    }
}
