package com.ctrip.tour.business.dashboard.sightArchives.entity.domesticMetricEntity;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;
import java.util.List;

@Entity
@Getter
@Setter
public class BiGmvProfitTargetBO {
    //业务大区名称
    String businessRegionName;
    //业务子区域名称
    String businessSubRegionName;
    //景点名称
    String vstId;
    String vstName;
    // bd名称
    String examinee;
    //目标收入GMV
    Double ttdTrgtIncome;
    //目标收入毛利
    Double ttdTrgtProfit;
}
