package com.ctrip.tour.business.dashboard.sightArchives.enums.common;

public enum OverseaExamineLevelEnumType {

    OVERSEA(1, "oversea", "海外"), //NOSONAR
    REGION(2, "region", "大区"), //NOSONAR
    SUB_REGION(4, "subRegion", "子区域"); //NOSONAR

    private final int id;
    private final String englishName;
    private final String chineseName;

    OverseaExamineLevelEnumType(int id, String englishName, String chineseName) {
        this.id = id;
        this.englishName = englishName;
        this.chineseName = chineseName;
    }

    public int getId() {
        return id;
    }

    public String getEnglishName() {
        return englishName;
    }

    public String getChineseName() {
        return chineseName;
    }
}
