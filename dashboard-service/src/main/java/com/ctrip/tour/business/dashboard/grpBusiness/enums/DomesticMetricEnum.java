package com.ctrip.tour.business.dashboard.grpBusiness.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/7/8
 */
@Getter
public enum DomesticMetricEnum {
    GMV(1, "gmv"),
    PROFIT(2, "profit"),
    QUALITY(3, "quality"),
    DIRECT_SIGN(4, "directSign"),
    SIGHT_COVER(5, "sightCover"),
    TYPE_COVER(6, "typeCover"),
    TICKET_BOOKING(7, "ticketBooking"),
    CATEGORY_COVER(9, "categoryCover"),
    DAY_TOUR_COVER(11, "dayTourCover"),
    DAY_TOUR_BOOKING(12, "dayTourBooking"),
    TIC_PRODUCT_POWER(13, "ticGoods"),
    DAY_TOUR_PRODUCT_POWER(14, "actGoods"),
    ;

    int id;
    String code;

    DomesticMetricEnum(int id, String code) {
        this.id = id;
        this.code = code;
    }

    // 静态映射表用于快速查找
    private static final Map<Integer, DomesticMetricEnum> idToEnum = new HashMap<>();
    private static final Map<String, DomesticMetricEnum> codeToEnum = new HashMap<>();

    static {
        // 初始化时遍历所有枚举值，填充映射表
        for (DomesticMetricEnum metric : values()) {
            idToEnum.put(metric.id, metric);
            codeToEnum.put(metric.code, metric);
        }
    }
    // 根据id获取code
    public static String getCodeById(int id) {
        DomesticMetricEnum metric = idToEnum.get(id);
        return metric != null ? metric.code : null;
    }

    // 根据code获取id
    public static Integer getIdByCode(String code) {
        DomesticMetricEnum metric = codeToEnum.get(code);
        return metric != null ? metric.id : null;
    }
    // 根据code获取id
    public static String getMetricByCode(String code) {
        DomesticMetricEnum metric = codeToEnum.get(code);
        return metric != null ? String.valueOf(metric.id) : "";
    }
}
