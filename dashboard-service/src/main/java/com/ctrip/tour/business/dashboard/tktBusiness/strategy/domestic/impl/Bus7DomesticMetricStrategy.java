package com.ctrip.tour.business.dashboard.tktBusiness.strategy.domestic.impl;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.domestic.DomesticMetricCalStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.Future;

@Component
public class Bus7DomesticMetricStrategy implements DomesticMetricCalStrategy {

    @Autowired
    private Bus567DomesticMetricStrategy bus567DomesticMetricStrategy;

    @Override
    public Future<DomesticMetricDetailInfo> getSingleMetricCardData(String domainName, TimeFilter timeFilter, List<MetricInfoBean> metricInfoBeanList, String d, <PERSON><PERSON><PERSON> isFirst, Integer businessId) throws Exception {
        return new AsyncResult<>(bus567DomesticMetricStrategy.getSingleMetricCardData(domainName, timeFilter, metricInfoBeanList, d, isFirst, getMetricName()));
    }

    @Override
    public GetDomesticMetricTrendDataResponseType getSingleTrendlineData(GetDomesticMetricTrendDataRequestType request, String d) throws Exception {
        return bus567DomesticMetricStrategy.getDomesticMetricTrendLineData(request, d);
    }
    @Override
    public GetFirstPageDomesticMetricCardDrillDataResponseType getFirstPageDomesticMetricCardDrillData(
            GetFirstPageDomesticMetricCardDrillDataRequestType request,MetricInfoBean metricInfoBean, String d) throws Exception{
        return bus567DomesticMetricStrategy.getFirstPageDomesticMetricCardDrillData(request, metricInfoBean, d);
    }
    @Override
    public GetDomesticTableDataResponseType getSingleTableData(GetDomesticTableDataRequestType request, MetricInfoBean metricInfoBean, String d) throws Exception {
        return bus567DomesticMetricStrategy.getSingleTableData(request, metricInfoBean, d);
    }

    @Override
    public GetDomesticDrillDownBaseInfoResponseType getSingleDrillDownBaseInfo(GetDomesticDrillDownBaseInfoRequestType request, MetricInfoBean metricInfoBean, String d) throws Exception {
        return bus567DomesticMetricStrategy.getSingleDrillDownBaseInfo(request, metricInfoBean, d);
    }

    @Override
    public Integer getMetricName() {
        return 7;
    }
}
