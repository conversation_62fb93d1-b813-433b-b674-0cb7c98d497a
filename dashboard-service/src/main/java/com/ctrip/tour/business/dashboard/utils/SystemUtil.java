package com.ctrip.tour.business.dashboard.utils;

/**
 * <AUTHOR>
 */
public class SystemUtil {
    private static int originStackIndex = 2;

    public static String getFileName() {
        return Thread.currentThread().getStackTrace()[originStackIndex].getFileName();
    }

    public static String getClassName() {
        return Thread.currentThread().getStackTrace()[originStackIndex].getClassName();
    }

    public static String getMethodName() {
        return Thread.currentThread().getStackTrace()[originStackIndex].getMethodName();
    }

    public static String getClassAndMethodName() {
        String format = String.format("%s.%s",
                Thread.currentThread().getStackTrace()[originStackIndex].getClassName(),
                Thread.currentThread().getStackTrace()[originStackIndex].getMethodName());
        return format;
    }

    public static int getLineNumber() {
        return Thread.currentThread().getStackTrace()[originStackIndex].getLineNumber();
    }

}
