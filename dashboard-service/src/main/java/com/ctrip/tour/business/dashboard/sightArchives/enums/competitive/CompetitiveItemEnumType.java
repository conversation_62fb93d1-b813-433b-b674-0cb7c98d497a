package com.ctrip.tour.business.dashboard.sightArchives.enums.competitive;

import java.util.Arrays;
import java.util.List;

public enum CompetitiveItemEnumType {

    //提前预订时间
    //确认方式
    //预订生效时间
    //入园方式
    //退改规则
    //未来15天班期
    //均价

    ADVANCE_BOOKING_TIME(1, "advanceBookingTime", "提前预订时间","abt_inferior_days"), //NOSONAR
    CONFIRMATION_METHOD(2, "confirmationMethod", "确认方式","ct_inferior_days"), //NOSONAR
    BOOKING_EFFECTIVE_TIME(3, "bookingEffectiveTime", "预订生效时间","bet_inferior_days"), //NOSONAR
    ENTRY_METHOD(4, "entryMethod", "入园方式","et_inferior_days"), //NOSONAR
    REFUND_RULE(5, "refundRule", "退改规则","rc_inferior_days"), //NOSONAR
    FUTURE_15_DAY_SCHEDULE(6, "future15DaySchedule", "未来15天班期","pr_inferior_days"); //NOSONAR
//    AVERAGE_PRICE(7, "averagePrice", "均价","rival_sale_price"); //NOSONAR

    private final int id;
    private final String englishName;
    private final String chineseName;
    private final String queryResultName;

    CompetitiveItemEnumType(int id, String englishName, String chineseName, String queryResultName) {
        this.id = id;
        this.englishName = englishName;
        this.chineseName = chineseName;
        this.queryResultName = queryResultName;
    }

    public int getId() {
        return id;
    }

    public String getEnglishName() {
        return englishName;
    }

    public String getChineseName() {
        return chineseName;
    }
    public String getQueryResultName() {
        return queryResultName;
    }

    //美团
    public static List<CompetitiveItemEnumType> MeiTuanCompetitiveItemList =
            Arrays.asList(
                    ADVANCE_BOOKING_TIME
                    , BOOKING_EFFECTIVE_TIME
                    , ENTRY_METHOD
                    , REFUND_RULE
                    , FUTURE_15_DAY_SCHEDULE
//                    , AVERAGE_PRICE
            );


    //飞猪
    public static List<CompetitiveItemEnumType> FeiZhuCompetitiveItemList =
            Arrays.asList(
                    ADVANCE_BOOKING_TIME
                    , CONFIRMATION_METHOD
                    , ENTRY_METHOD
                    , REFUND_RULE
                    , FUTURE_15_DAY_SCHEDULE
//                    , AVERAGE_PRICE
            );
    //客路
    public static List<CompetitiveItemEnumType> KeLuCompetitiveItemList =
            Arrays.asList(
                    ADVANCE_BOOKING_TIME
                    , CONFIRMATION_METHOD
                    , ENTRY_METHOD
                    , REFUND_RULE
                    , FUTURE_15_DAY_SCHEDULE
//                    , AVERAGE_PRICE
            );

}
