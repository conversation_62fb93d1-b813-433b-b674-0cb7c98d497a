include 'BaijiCommonTypes.bjsc'
include '../dsl.bjsc'
namespace java 'com.ctrip.soa._24922'

class GetGrpUpdateTimeRequestType{

}
class GetGrpUpdateTimeResponseType{
    BaijiCommonTypes.ResponseStatusType responseStatus; 
    string updateTime;
}

class GetCustTourDateTypeRequestType{

}
class GetCustTourDateTypeResponseType{
    BaijiCommonTypes.ResponseStatusType responseStatus; 
    list<string> dateTypeList;
}

class GetCustTourRegionListRequestType{
    string empCode;
    string domainName;
}
class GetCustTourRegionListResponseType{
    BaijiCommonTypes.ResponseStatusType responseStatus; 
    list<CustTourRegionInfo> regionList;
}

class CustTourRegionInfo {
    string regionName;
    list<string> areas;
}

class GetGrpOrganizationRequestType{
    string searchKey;
    int selectTab;
}
class GetGrpOrganizationResponseType{
    BaijiCommonTypes.ResponseStatusType responseStatus; 
    bool havePermission;
    list<GrpOrganizationNode> organizationItemList;
}
class GrpOrganizationNode{
    string orgId;
    string orgName;    
    list<GrpOrganizationNode> children;
}

class GetGrpEmployeeRequestType{
    string orgId;
    string orgName;
    string searchKey;
}
class GetGrpEmployeeResponseType{
    BaijiCommonTypes.ResponseStatusType responseStatus; 
    list<GrpEmployeeItem> employeeItemList;
}
class GrpEmployeeItem{
    string empCode;
    string displayName;
    string domainName;
    string orgId;
    string orgName;
    list<int> businessLine;
    int defaultBusinessLine;
}

class GetGrpProductPatternRequestType{
    int businessLine;
}
class GetGrpProductPatternResponseType{
    BaijiCommonTypes.ResponseStatusType responseStatus; 
    list<string> productPatternList;
}



class getGrpMetricCardResponseType{
    BaijiCommonTypes.ResponseStatusType responseStatus; 
    list<GrpMetricCategorie> metricCardList;

}
class GrpMetricCategorie{
    string metricCategorieName;
    string metricCategorieNameSharkkey;
    string metricCategorieTipsSharkkey;
    list<GrpMetric> metricList;
    double rank;
    double rankRatio;
    double overSeaRank;
    double overSeaRankRatio;
}
class GrpMetric{
    string metricName;
    string metricNameSharkKey;
    string metricTipsSharkKey;
    double metricValue;
    bool isRatioValue;
    ExtraValues extraValues;
}
class ExtraValues{
    double yearOverYear;
    double weekOverWeek;
    double monthOverMonth;
}


class GetGrpMetricDataRequestType{
    string empCode;
    string domainName;
    int businessLine;
    string productPattern;
    string startDate;
    string endDate;
    string metricCategorieName;
    string aggregationGranularity;
    string drillDownDim;
    list<string> selectDimEnum;
    int selectTab;
    string dateType;
    string region
    list<string> areas;
    list<string> provNameList;
}

class GetGrpTrendLineResponseType{
    BaijiCommonTypes.ResponseStatusType responseStatus;
    list<GrpTrendLinePoint> trendLine;
}
class GrpTrendLinePoint{
    string date;
    list<GrpMetric> trendLinePoints;
}

class GetGrpDillDownDimRequestType{
    string empCode;
    string domainName;
    int businessLine;
    string productPattern;
    string startDate;
    string endDate;
    string metricCategorieName;
    int selectTab;
    string dateType;
    string region
    list<string> areas;
    list<string> provNameList;
}
class GetGrpDillDownDimResponseType{
    BaijiCommonTypes.ResponseStatusType responseStatus;
    list<string> drillDownDimList;
}

// 下钻纬度
class GetGrpDillDownDimRequestTypeV2{
    // 100: 跟团游
    // 210: 独立出游-私家团
    // 220: 独立出游-拼小团
    // 230: 独立出游-定制游
    int businessType;
    // 指标名称
    string indicatorCategory;
    // 纬度名称
    string dimName;
}

class GetGrpDillDownDimResponseTypeV2{
    BaijiCommonTypes.ResponseStatusType responseStatus;
    dsl.DSLResponseType data;
}


class GetGrpDillDownDimEnumRequestType{
    string empCode;
    string domainName;
    int businessLine;
    string productPattern;
    string startDate;
    string endDate;
    string metricCategorieName;
    string drillDownDim;
    int selectTab;
    string dateType;
    string region
    list<string> areas;
    list<string> provNameList;
}
class GetGrpDillDownDimEnumResponseType{
    BaijiCommonTypes.ResponseStatusType responseStatus;
    list<string> dimEnumList;
}


class GetGrpDillDownDetailResponseType{
    BaijiCommonTypes.ResponseStatusType responseStatus;
    list<GrpTableRow> tableRows;
}
class GrpTableRow{
    string dimEnum;
    list<GrpMetric> metricList;
    list<GrpMetric> busLine1MetricList;
    list<GrpMetric> busLine2MetricList;
}

class GetGrpFirstPageMetricCardRequestType{
    string startDate;
    string endDate;
}

class GetGrpFirstPageMetricCardResponseType{
    BaijiCommonTypes.ResponseStatusType responseStatus;
    list<GrpFirstPageMetricCategorie> firstPageMetricCardList;
}

class GrpFirstPageMetricCategorie{
    string metricCategorieName;
    string metricCategorieNameSharkkey;
    string metricCategorieTipsSharkkey;
    list<GrpMetric> metricList;
    double rank;
    double rankRatio;
    list<GrpTrendLinePoint> trendLine;
}

class DownloadGrpDillDownDetailRequestType{
    string empCode;
    string domainName;
    int businessLine;
    string productPattern;
    string startDate;
    string endDate;
    string metricCategorieName;
    string drillDownDim;
    list<string> selectDimEnum;
    int selectTab;
}
class DownloadGrpDillDownDetailResponseType{
    BaijiCommonTypes.ResponseStatusType responseStatus;
    string fileUrl;
}

class GetGrpOrgTabListRequestType{

}

class GetGrpOrgTabListResponseType{
    BaijiCommonTypes.ResponseStatusType responseStatus;
    list<int> tabList;
    int defaultTabList;
}

class QueryByDSLRequestType {
    dsl.DSLRequestType dsl;
}



class QueryByDSLResponseType {
    BaijiCommonTypes.ResponseStatusType responseStatus;
    dsl.DSLResponseType data;
}

class GraphResponseType{
    BaijiCommonTypes.ResponseStatusType responseStatus;
    Graph graph;
}

class Graph{
    string name;
    string type;
    dsl.DSLResponseType data;
}

class GraphRequestType{
}

 // dsl query
 class QueryByDSLRequestType {
     dsl.DSLRequestType dsl;
 }

 class QueryByDSLResponseType {
     BaijiCommonTypes.ResponseStatusType responseStatus;
     dsl.DSLResponseType data;
 }

 // 指标卡通用接口
 class PerformanceCardRequestType {
     list<string> indicators;
     list<string> compareConfig;
     string startDate;
     string endDate;
     // 100: 跟团游
     // 210: 独立出游-私家团
     // 220: 独立出游-拼小团
     // 230: 独立出游-定制游
     int businessType;
     list<string> empCodes;
     list<string> provNames;
     list<string> productPattern;

     // 拼小团
     list<string> area;
     // PREORDER_DATE: 预定
     // TRIP_DATE: 出行
     // RETURN_DATE: 返程
     string dateType;
 }

 class PerformanceCardResponseType {
     BaijiCommonTypes.ResponseStatusType responseStatus;
     dsl.DSLResponseType data;
 }

 // 趋势图通用接口
 class ChangeTrendRequestType {
     list<string> indicators;
     list<string> compareConfig;
     list<string> groupBy;
     string startDate;
     string endDate;
     // 100: 跟团游
     // 210: 独立出游-私家团
     // 220: 独立出游-拼小团
     // 230: 独立出游-定制游
     int businessType;
     list<string> empCodes;
     list<string> provNames;
     list<string> productPattern;

     // 拼小团
     list<string> area;
     // PREORDER_DATE: 预定
     // TRIP_DATE: 出行
     // RETURN_DATE: 返程
     string dateType;
 }

 class ChangeTrendResponseType {
     BaijiCommonTypes.ResponseStatusType responseStatus;
     dsl.DSLResponseType data;
 }

 // 下钻通用接口
 class TopRankingRequestType {
     list<string> indicators;
     list<string> compareConfig;
     list<string> groupBy;
     string startDate;
     string endDate;
     // 100: 跟团游
     // 210: 独立出游-私家团
     // 220: 独立出游-拼小团
     // 230: 独立出游-定制游
     int businessType;
     list<string> empCodes;
     list<string> provNames;
     list<string> productPattern;

     // 拼小团
     list<string> area;
     // PREORDER_DATE: 预定
     // TRIP_DATE: 出行
     // RETURN_DATE: 返程
     string dateType;

     // 下钻过滤
     string dimFilterName;
     list<string> dimFilterValues;

     // 分页排序
     int page;
     int size;
     string orderField;
     string orderType;
 }

 class TopRankingResponseType {
     BaijiCommonTypes.ResponseStatusType responseStatus;
     dsl.DSLResponseType data;
 }

@serviceName='BIBusinessDashboardService'
@serviceNamespace='http://soa.ctrip.com/_24922'
service BIBusinessDashboardService {

    //勿删 为了引用新增的
    GraphResponseType getGraph(GraphRequestType request);

    //获取定制游评级区域
    GetCustTourRegionListResponseType getCustTourRegionList(GetCustTourRegionListRequestType request);

    //获取定制游日期类型
    GetCustTourDateTypeResponseType getCustTourDateType(GetCustTourDateTypeRequestType request);

    //数据最新更新日期
    GetGrpUpdateTimeResponseType getGrpUpdateTime(GetGrpUpdateTimeRequestType request);

    //获取组织架构
    GetGrpOrganizationResponseType getGrpOrganization(GetGrpOrganizationRequestType request);
    //获取组织架构
    GetGrpOrganizationResponseType getGrpOrganizationV2(GetGrpOrganizationRequestType request);

    //获取人员列表
    GetGrpEmployeeResponseType getGrpEmployee(GetGrpEmployeeRequestType request);

    //产品形态筛选框
    GetGrpProductPatternResponseType getGrpProductPattern(GetGrpProductPatternRequestType request);
    //产品形态筛选框
    GetGrpProductPatternResponseType getGrpProductPatternV2(GetGrpProductPatternRequestType request);

    //指标卡
    getGrpMetricCardResponseType getGrpMetricCard(GetGrpMetricDataRequestType request);

    //趋势线
    GetGrpTrendLineResponseType getGrpTrendLine(GetGrpMetricDataRequestType request);

    //下钻维度列表
    GetGrpDillDownDimResponseType getGrpDillDownDim(GetGrpDillDownDimRequestType request);
    //下钻维度列表
    GetGrpDillDownDimResponseTypeV2 getGrpDillDownDimV2(GetGrpDillDownDimRequestTypeV2 request);
    
    //该维度下的枚举值list
    GetGrpDillDownDimEnumResponseType getGrpDillDownDimEnum(GetGrpDillDownDimEnumRequestType request);

    //下钻分析 详情表格
    GetGrpDillDownDetailResponseType getGrpDillDownDetail(GetGrpMetricDataRequestType request);

    //下钻分析 表格下载
    DownloadGrpDillDownDetailResponseType downloadGrpDillDownDetail(DownloadGrpDillDownDetailRequestType request);
    
    //首页指标卡
    GetGrpFirstPageMetricCardResponseType getGrpFirstPageMetricCard(GetGrpFirstPageMetricCardRequestType request);

    //获取tab列表及默认tab
    GetGrpOrgTabListResponseType getGrpOrgTabList(GetGrpOrgTabListRequestType request);
    //获取tab列表及默认tab
    GetGrpOrgTabListResponseType getGrpOrgTabListV2(GetGrpOrgTabListRequestType request);

    // dsl通用接口
    QueryByDSLResponseType QueryDataByDSL(QueryByDSLRequestType request);

    // dsl通用接口
    PerformanceCardResponseType QueryPerformanceCard(PerformanceCardRequestType request);
    // dsl通用接口
    ChangeTrendResponseType QueryChangeTrend(ChangeTrendRequestType request);
    // dsl通用接口
    TopRankingResponseType QueryTopRanking(TopRankingRequestType request);

}