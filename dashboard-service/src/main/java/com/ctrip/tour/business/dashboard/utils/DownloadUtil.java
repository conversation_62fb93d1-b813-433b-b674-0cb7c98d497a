package com.ctrip.tour.business.dashboard.utils;

import com.ctrip.framework.triplog.shaded.client.tag.TagMarker;
import com.ctrip.framework.triplog.shaded.client.tag.TagMarkerBuilder;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class DownloadUtil {
    private static final Logger logger = LoggerFactory.getLogger(DownloadUtil.class);
    private static TagMarker marker = TagMarkerBuilder.newBuilder().add("tag", "DownloadUtil").build();

    /**
     * post json请求
     * @param url
     * @param data：请求json
     * @return
     */
    public static String post(String url, String data) {
        HttpPost request = new HttpPost(url);
        // 设置请求参数
        StringEntity entity = new StringEntity(data, "UTF-8");
        request.addHeader("Content-Type", "application/json");
        request.setEntity(entity);

        try (CloseableHttpClient client = HttpClients.createDefault();
             CloseableHttpResponse response = client.execute(request)) {
            if (HttpStatus.SC_OK != response.getStatusLine().getStatusCode()) {
                logger.warn(marker, String.format("post %s failed, statuscode=%s", url, response.getStatusLine().getStatusCode()));
                return StringUtils.EMPTY;
            }
            return EntityUtils.toString(response.getEntity(), "utf-8");
        } catch (Exception ex) {
            logger.warn(marker, String.format("post %s failed", url), ex);
            return StringUtils.EMPTY;
        }
    }

    /**
     * get请求
     * @param url
     * @return
     */
    public static String get(String url) {
        HttpGet request = new HttpGet(url);
        try (CloseableHttpClient client = HttpClients.createDefault();
             CloseableHttpResponse response = client.execute(request)) {
            if (HttpStatus.SC_OK != response.getStatusLine().getStatusCode()) {
                logger.warn(marker, String.format("get %s failed, status: %s", url, response.getStatusLine().getStatusCode()));
                return StringUtils.EMPTY;
            }
            return EntityUtils.toString(response.getEntity(), "utf-8");
        } catch (Exception ex) {
            logger.warn(marker, String.format("get %s failed", url), ex);
            return StringUtils.EMPTY;
        }
    }
}
