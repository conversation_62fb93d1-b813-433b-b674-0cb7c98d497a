package com.ctrip.tour.business.dashboard.grpBusiness.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Sensitive;
import com.ctrip.platform.dal.dao.annotation.Type;
import java.sql.Types;
import java.sql.Timestamp;

import com.ctrip.platform.dal.dao.DalPojo;

/**
 * <AUTHOR>
 * @date 2025-03-19
 */
@Entity
@Database(name = "ttdreportdb_dalcluster")
@Table(name = "dim_sev_tor_bd_personnel_df")
public class DimSevTorBdPersonnelDf implements DalPojo {

    /**
     * 主键
     */
    @Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long id;

    /**
     * 中心
     */
	@Column(name = "center")
	@Type(value = Types.VARCHAR)
	private String center;

    /**
     * 省(门店省份、供应商注册省份)
     */
	@Column(name = "province")
	@Type(value = Types.VARCHAR)
	private String province;

    /**
     * 区域
     */
	@Column(name = "region")
	@Type(value = Types.VARCHAR)
	private String region;

    /**
     * 省区经理
     */
	@Column(name = "governor")
	@Type(value = Types.VARCHAR)
	private String governor;

    /**
     * 省区经理工号
     */
	@Column(name = "governor_code")
	@Type(value = Types.VARCHAR)
	private String governorCode;

    /**
     * 业务经理
     */
	@Column(name = "bd")
	@Type(value = Types.VARCHAR)
	private String bd;

    /**
     * 业务经理工号
     */
	@Column(name = "bd_code")
	@Type(value = Types.VARCHAR)
	private String bdCode;

    /**
     * 更新时间
     */
	@Column(name = "datachange_lasttime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getCenter() {
		return center;
	}

	public void setCenter(String center) {
		this.center = center;
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getRegion() {
		return region;
	}

	public void setRegion(String region) {
		this.region = region;
	}

	public String getGovernor() {
		return governor;
	}

	public void setGovernor(String governor) {
		this.governor = governor;
	}

	public String getGovernorCode() {
		return governorCode;
	}

	public void setGovernorCode(String governorCode) {
		this.governorCode = governorCode;
	}

	public String getBd() {
		return bd;
	}

	public void setBd(String bd) {
		this.bd = bd;
	}

	public String getBdCode() {
		return bdCode;
	}

	public void setBdCode(String bdCode) {
		this.bdCode = bdCode;
	}

	public Timestamp getDatachangeLasttime() {
		return datachangeLasttime;
	}

	public void setDatachangeLasttime(Timestamp datachangeLasttime) {
		this.datachangeLasttime = datachangeLasttime;
	}

}