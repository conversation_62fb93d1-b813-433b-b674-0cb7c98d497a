package com.ctrip.tour.business.dashboard.tktBusiness.strategy.domestic.impl;

import com.alibaba.fastjson.JSONObject;
import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.grpBusiness.enums.DomesticMetricEnum;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.StrategyException;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.MetricCalStrategy;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.domestic.DomesticMetricCalStrategy;
import com.ctrip.tour.business.dashboard.utils.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;
@Service
@Slf4j
public class DomesticMetricCalStrategyBizImpl  implements ApplicationContextAware {

    private ApplicationContext applicationContext;

    private ConcurrentHashMap<Integer, DomesticMetricCalStrategy> metricCalStrategyMap = new ConcurrentHashMap<>();


    private ConcurrentHashMap<Integer, DomesticMetricCalStrategy> metricCalStrategyMetricMap = new ConcurrentHashMap<>();


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
        Map<String, DomesticMetricCalStrategy> strategyMap = applicationContext.getBeansOfType(DomesticMetricCalStrategy.class);
        strategyMap.values().forEach(e -> metricCalStrategyMap.put(e.getMetricName(), e));
    }

    public GetDomesticDrillDownBaseInfoResponseType getSingleDrillDownBaseInfo(GetDomesticDrillDownBaseInfoRequestType request,
                                                                               MetricInfoBean metricInfoBean,
                                                                               String d) throws Exception {
        Integer metric = DomesticMetricEnum.getIdByCode(request.getMetricCode());
        DomesticMetricCalStrategy metricCalStrategy = metricCalStrategyMap.get(metric);
        if (metricCalStrategy == null) {
            throw new StrategyException("未找到对应策略!");//NOSONAR
        }
        return metricCalStrategy.getSingleDrillDownBaseInfo(request, metricInfoBean, d);
    }

    public GetDomesticTableDataResponseType getSingleTableData(GetDomesticTableDataRequestType request,
                                                               MetricInfoBean metricInfoBean,
                                                               String d) throws Exception {
        Integer metric = DomesticMetricEnum.getIdByCode(request.getMetricCode());
        DomesticMetricCalStrategy metricCalStrategy = metricCalStrategyMap.get(metric);
        if (metricCalStrategy == null) {
            throw new StrategyException("未找到对应策略!");//NOSONAR
        }
        return metricCalStrategy.getSingleTableData(request, metricInfoBean, d);
    }

    public Future<DomesticMetricDetailInfo> getSingleMetricCardData(String domainName,
                                                                    TimeFilter timeFilter,
                                                                    List<MetricInfoBean> metricInfoBeanList,
                                                                    String d,
                                                                    Boolean isFirst,
                                                                    Integer type,
                                                                    Integer businessId,
                                                                    Integer metric) throws Exception {
        if (metricInfoBeanList.isEmpty()){
            throw new StrategyException("指标值为空!");//NOSONAR
        }
        setMetricCalStrategyMap(type, businessId);
        DomesticMetricCalStrategy metricCalStrategy = metricCalStrategyMetricMap.get(metric);
        if (metricCalStrategy == null) {
            return new AsyncResult<>(new DomesticMetricDetailInfo());
        }
        return metricCalStrategy.getSingleMetricCardData(domainName, timeFilter, metricInfoBeanList, d, isFirst,businessId);
    }


    /**
     * 分成三份
     * @param type 0首页、1大盘、2详情
     */
    public void setMetricCalStrategyMap(Integer type, Integer businessId) {
        Set<Integer> first = new HashSet<>(Arrays.asList(1,2,3,9,13,14));
        Set<Integer> summary = new HashSet<>(Arrays.asList(1,2,3));
        Set<Integer> detail = new HashSet<>(Arrays.asList(1,2,3,4,5,6,7,9,11,12));
        Map<String, DomesticMetricCalStrategy> strategyMap = applicationContext.getBeansOfType(DomesticMetricCalStrategy.class);
        metricCalStrategyMetricMap = new ConcurrentHashMap<>();
        switch (type) {
            case 0:
                strategyMap.values().forEach(e -> {
                    if (first.contains(e.getMetricName()) && checkExistWithBusiness(e.getMetricName(), businessId)) {
                        metricCalStrategyMetricMap.put(e.getMetricName(), e);
                    }
                });
                break;
            case 1:
                strategyMap.values().forEach(e -> {
                    if (summary.contains(e.getMetricName()) && checkExistWithBusiness(e.getMetricName(), businessId)) {
                        metricCalStrategyMetricMap.put(e.getMetricName(), e);
                    }
                });
                break;
            case 2:
                strategyMap.values().forEach(e -> {
                    if (detail.contains(e.getMetricName()) && checkExistWithBusiness(e.getMetricName(), businessId)) {
                        metricCalStrategyMetricMap.put(e.getMetricName(), e);
                    }
                });
                break;
        }
    }

    public boolean checkExistWithBusiness(Integer metricName, Integer businessId) {
        if (businessId == 1) {
            return 11 != metricName && 12 != metricName;
        }else if (businessId == 2) {
            return 5 != metricName && 6 != metricName && 7 != metricName;
        }
        return true;
    }

    public GetDomesticMetricTrendDataResponseType getSingleTrendlineData(GetDomesticMetricTrendDataRequestType request, String d) throws Exception {
        Integer metric = DomesticMetricEnum.getIdByCode(request.getMetricCode());
        DomesticMetricCalStrategy metricCalStrategy = metricCalStrategyMap.get(metric);
        if (metricCalStrategy == null) {
            throw new StrategyException("未找到对应策略!");//NOSONAR
        }
        return metricCalStrategy.getSingleTrendlineData(request, d);
    }

    public GetFirstPageDomesticMetricCardDrillDataResponseType getFirstPageDomesticMetricCardDrillData(
            GetFirstPageDomesticMetricCardDrillDataRequestType request, MetricInfoBean metricInfoBean, String d) throws Exception {
        Integer metric = DomesticMetricEnum.getIdByCode(request.getMetricCode());
        DomesticMetricCalStrategy metricCalStrategy = metricCalStrategyMap.get(metric);
        log.info("getFirstPageDomesticMetricCardDrillData,metric=" + JSONObject.toJSONString(metricCalStrategyMap));
        if (metricCalStrategy == null) {
            throw new StrategyException("未找到对应策略!");//NOSONAR
        }
        return metricCalStrategy.getFirstPageDomesticMetricCardDrillData(request, metricInfoBean, d);
    }


}
