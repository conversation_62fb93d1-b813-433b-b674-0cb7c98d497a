package com.ctrip.tour.business.dashboard.grpBusiness.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Sensitive;
import com.ctrip.platform.dal.dao.annotation.Type;
import java.sql.Types;
import java.sql.Timestamp;

import com.ctrip.platform.dal.dao.DalPojo;

/**
 * <AUTHOR>
 * @date 2024-12-17
 */
@Entity
@Database(name = "TtdReportDB_W")
@Table(name = "adm_prd_grp_prdid_depcty_prcedate_online_pr_df")
public class AdmPrdGrpPrdidDepctyPrcedateOnlinePrDf implements DalPojo {

    /**
     * 主键
     */
    @Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long id;

    /**
     * 产品id
     */
	@Column(name = "prd_id")
	@Type(value = Types.BIGINT)
	private Long prdId;

    /**
     * 商户id
     */
	@Column(name = "vendor_id")
	@Type(value = Types.BIGINT)
	private Long vendorId;

    /**
     * 班期日期
     */
	@Column(name = "departure_date")
	@Type(value = Types.VARCHAR)
	private String departureDate;

    /**
     * 供应商标签
     */
	@Column(name = "vendor_flag")
	@Type(value = Types.VARCHAR)
	private String vendorFlag;

    /**
     * 是否核心产品
     */
	@Column(name = "is_focus_prd")
	@Type(value = Types.VARCHAR)
	private String isFocusPrd;

    /**
     * 更新时间
     */
	@Column(name = "DataChange_LastTime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getPrdId() {
		return prdId;
	}

	public void setPrdId(Long prdId) {
		this.prdId = prdId;
	}

	public Long getVendorId() {
		return vendorId;
	}

	public void setVendorId(Long vendorId) {
		this.vendorId = vendorId;
	}

	public String getDepartureDate() {
		return departureDate;
	}

	public void setDepartureDate(String departureDate) {
		this.departureDate = departureDate;
	}

	public String getVendorFlag() {
		return vendorFlag;
	}

	public void setVendorFlag(String vendorFlag) {
		this.vendorFlag = vendorFlag;
	}

	public String getIsFocusPrd() {
		return isFocusPrd;
	}

	public void setIsFocusPrd(String isFocusPrd) {
		this.isFocusPrd = isFocusPrd;
	}

	public Timestamp getDatachangeLasttime() {
		return datachangeLasttime;
	}

	public void setDatachangeLasttime(Timestamp datachangeLasttime) {
		this.datachangeLasttime = datachangeLasttime;
	}

}