package com.ctrip.tour.business.dashboard.tktBusiness.strategy.subMetricImplV2;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBeanV2;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.IncomeSubMetricCalStrategyV2;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.QuantitySubMetricCalStrategyV2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.Future;

@Component
public class DestinationCMetricStrategyV2 implements IncomeSubMetricCalStrategyV2, QuantitySubMetricCalStrategyV2 {

    @Autowired
    private DestinationBaseStrategyV2 destinationBaseStrategy;


    @Override
    public Future<OveaseaSubMetric> getBus101102SubMetricCardData(TimeFilter timeFilter,
                                                                  OverseaMetricInfoBeanV2 metricInfoBean,
                                                                  String d,
                                                                  String subMetric,
                                                                  GetOverseaMetricCardDataV2RequestType request) throws Exception {
        boolean isH = "half".equals(timeFilter.getDateType());
        return isH ? destinationBaseStrategy.getBus101102110SubMetricCardDataHalf(timeFilter, metricInfoBean, d, subMetric, request) :
                destinationBaseStrategy.getBus101102110SubMetricCardDataQuarter(timeFilter, metricInfoBean, d, subMetric, request);
    }

    @Override
    public GetOverseaTrendLineDataV2ResponseType getBus101102SubTrendLineData(GetOverseaTrendLineDataV2RequestType request,
                                                                              String d,
                                                                              List<String> timeList) throws Exception {
        return destinationBaseStrategy.getBus101102110SubTrendlineData(request, d, timeList);
    }

    @Override
    public GetOverseaDrillDownBaseInfoV2ResponseType getBus101102SubDrillDownBaseInfo(GetOverseaDrillDownBaseInfoV2RequestType request,
                                                                                    String d,
                                                                                    OverseaMetricInfoBean metricInfoBean) throws Exception {
        return destinationBaseStrategy.getBus101102SubDrillDownBaseInfo(request, d, metricInfoBean);
    }

    @Override
    public GetOverseaTableDataV2ResponseType getBus101102SubTableData(GetOverseaTableDataV2RequestType request,
                                                                    String d,
                                                                    OverseaMetricInfoBean metricInfoBean) throws Exception {
        return destinationBaseStrategy.getBus101102SubTableData(request, d, metricInfoBean);
    }

    @Override
    public GetOverseaDrillDownBaseInfoV2ResponseType getBus103SubDrillDownBaseInfo(GetOverseaDrillDownBaseInfoV2RequestType request, String d, OverseaMetricInfoBean metricInfoBean) throws Exception {
        return destinationBaseStrategy.getBus103SubDrillDownBaseInfo(request, d, metricInfoBean);
    }


    @Override
    public GetOverseaTableDataV2ResponseType getBus103SubTableData(GetOverseaTableDataV2RequestType request, String d, OverseaMetricInfoBean metricInfoBean) throws Exception {
        return destinationBaseStrategy.getBus103SubTableData(request, d, metricInfoBean);
    }

    @Override
    public String getSubMetricName() {
        return "destinationC";
    }
}
