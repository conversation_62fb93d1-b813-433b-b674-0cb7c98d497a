package com.ctrip.tour.business.dashboard.tktBusiness.strategy.subMetricImpl;

import com.ctrip.soa._24922.*;
import com.ctrip.soa._27181.BIBaseReportQueryServiceClient;
import com.ctrip.soa._27181.GetRawDataRequestType;
import com.ctrip.soa._27181.GetRawDataResponseType;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.*;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.OverseaSinglePeriodTrendLineBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.bo.OverSeaExamineConfigBO;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.Bus101102Helper;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.Bus103Helper;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.OverseaMetricHelper;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.SwitchNewTableHelper;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.IncomeSubMetricCalStrategy;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.QuantitySubMetricCalStrategy;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.google.common.util.concurrent.ListenableFuture;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

@Component
public class SiteMetricStrategy implements IncomeSubMetricCalStrategy, QuantitySubMetricCalStrategy {

    @Autowired
    private BIBaseReportQueryServiceClient baseReportQueryServiceClient;

    @Autowired
    private RemoteConfig remoteConfig;

    @Autowired
    private OverseaSinglePeriodTrendLineBiz overseaSinglePeriodTrendLineBiz;

    @Autowired
    private SwitchNewTableHelper switchNewTableHelper;

    @Override
    public Future<MetricDetailInfo> getBus101102SubMetricCardData(TimeFilter timeFilter,
                                                                  OverseaMetricInfoBean metricInfoBean,
                                                                  String d,
                                                                  String metric,
                                                                  String subMetric) throws Exception {

        MetricDetailInfo metricDetailInfo = new MetricDetailInfo();
        metricDetailInfo.setMetric(metric);
        metricDetailInfo.setSubMetric(subMetric);
        metricDetailInfo.setActualSubMetric(subMetric);
        //填充一些前端需要的额外信息
        metricDetailInfo.setMomType(OverseaMetricHelper.getMomType(timeFilter, d));
        Bus101102Helper.setMetricCardDrillDownParmater(metricDetailInfo, subMetric, metricInfoBean, remoteConfig);
        Map<String, Double> dimMap = new HashMap<>();
        metricDetailInfo.setDimData(dimMap);
        //获取当期数据
        SqlParamterBean currentBean = Bus101102Helper.getSiteMetricCardSqlBean("current", timeFilter, metricInfoBean, d, remoteConfig,null);
        GetRawDataRequestType currentReq = currentBean.convertBeanToRequest(true);
        ListenableFuture<GetRawDataResponseType> currentResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(currentReq);
        //获取目标
        SqlParamterBean targetBean = Bus101102Helper.getSiteMetricCardSqlBean("target", timeFilter, metricInfoBean, d, remoteConfig,null);
        GetRawDataRequestType targetReq = targetBean.convertBeanToRequest(true);
        ListenableFuture<GetRawDataResponseType> targetResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(targetReq);
        //获取去年数据
        SqlParamterBean lastyearBean = Bus101102Helper.getSiteMetricCardSqlBean("lastyear", timeFilter, metricInfoBean, d, remoteConfig,null);
        GetRawDataRequestType lastyearReq = lastyearBean.convertBeanToRequest(true);
        ListenableFuture<GetRawDataResponseType> lastyearResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(lastyearReq);
        //获取2019年数据
        SqlParamterBean _2019Bean = Bus101102Helper.getSiteMetricCardSqlBean("2019", timeFilter, metricInfoBean, d, remoteConfig,null);
        GetRawDataRequestType _2019Req = _2019Bean.convertBeanToRequest(true);
        ListenableFuture<GetRawDataResponseType> _2019ResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(_2019Req);
        //获取环比数据
        SqlParamterBean momBean = Bus101102Helper.getSiteMetricCardSqlBean("mom", timeFilter, metricInfoBean, d, remoteConfig,null);
        //环比的当前数据
        GetRawDataRequestType momReq = momBean.convertBeanToRequest(true);
        ListenableFuture<GetRawDataResponseType> momRes = switchNewTableHelper.switchRemoteDatabaseAsync(momReq);
        //环比的上一期数据
        GetRawDataRequestType momReq2 = momBean.convertBeanToRequest2(true);
        ListenableFuture<GetRawDataResponseType> momRes2 = switchNewTableHelper.switchRemoteDatabaseAsync(momReq2);

        //指标卡基础数据
        Bus101102Helper.processMetricCardBaseData(currentResFuture.get(), targetResFuture.get(), dimMap, metric);

        //指标卡同比数据
        Bus101102Helper.processMetricCardPopData(lastyearResFuture.get(), dimMap, metric, "lastyear");
        Bus101102Helper.processMetricCardPopData(_2019ResFuture.get(), dimMap, metric, "2019");

        //指标卡环比数据
        Bus101102Helper.processMetricCardMomData(momRes.get(), momRes2.get(), dimMap, metric, metricDetailInfo.getMomType());
        return new AsyncResult<>(metricDetailInfo);
    }

    @Override
    public GetOverseaTrendLineDataResponseType getBus101102SubTrendLineData(GetOverseaTrendLineDataRequestType request,
                                                                            String d,
                                                                            List<ExamineConfigBean> examineConfigBeanList) throws Exception {


        GetOverseaTrendLineDataResponseType response = new GetOverseaTrendLineDataResponseType();
        List<TrendLineDetailInfo> trendLineDetailInfoList = new ArrayList<>();
        response.setTrendLineDetailInfoList(trendLineDetailInfoList);

        String metric = request.getMetric();
        List<Future<SinglePeriodDataBean>> futureList = new ArrayList<>();
        for (ExamineConfigBean bean : examineConfigBeanList) {
            OverSeaExamineConfigBO bo = new OverSeaExamineConfigBO();
            OverseaMetricInfoBean metricInfoBean = bo.getMetricInfoBean(bean.getBusinessDashboardOverseaExamineeConfig(), remoteConfig);
            //获取当期数据
            SqlParamterBean currentBean = Bus101102Helper.getSiteMetricCardSqlBean("current", null, metricInfoBean, d, remoteConfig, bean);
            GetRawDataRequestType currentReq = currentBean.convertBeanToRequest(true);
            //获取目标
            SqlParamterBean targetBean = Bus101102Helper.getSiteMetricCardSqlBean("target", null, metricInfoBean, d, remoteConfig, bean);
            GetRawDataRequestType targetReq = targetBean.convertBeanToRequest(true);
            //获取去年数据
            SqlParamterBean lastyearBean = Bus101102Helper.getSiteMetricCardSqlBean("lastyear", null, metricInfoBean, d, remoteConfig, bean);
            GetRawDataRequestType lastyearReq = lastyearBean.convertBeanToRequest(true);
            //获取2019年数据
            SqlParamterBean _2019Bean = Bus101102Helper.getSiteMetricCardSqlBean("2019", null, metricInfoBean, d, remoteConfig, bean);
            GetRawDataRequestType _2019Req = _2019Bean.convertBeanToRequest(true);

            futureList.add(overseaSinglePeriodTrendLineBiz.getBus101102SubSinglePeriodTrendLineData(currentReq, targetReq, lastyearReq, _2019Req, bean));
        }

        //转化汇总返回数据
        PeriodDataBean periodDataBean = new PeriodDataBean();
        periodDataBean.setBeanData(futureList);

        //有效时间范围
        List<String> timeList = DateUtil.getAllSelectedTime(request.getTimeFilter(), "oversea");

        //趋势线基础数据
        Bus101102Helper.processTrendLineBaseData(trendLineDetailInfoList, periodDataBean, timeList, metric);
        //趋势线同比数据
        Bus101102Helper.processTrendLinePopData(trendLineDetailInfoList, periodDataBean, timeList, metric, "lastyear");
        Bus101102Helper.processTrendLinePopData(trendLineDetailInfoList, periodDataBean, timeList, metric, "2019");
        return response;
    }

    @Override
    public GetOverseaDrillDownBaseInfoResponseType getBus101102SubDrillDownBaseInfo(GetOverseaDrillDownBaseInfoRequestType request,
                                                                                    String d,
                                                                                    OverseaMetricInfoBean metricInfoBean) throws Exception {
        GetOverseaDrillDownBaseInfoResponseType response = new GetOverseaDrillDownBaseInfoResponseType();
        Boolean needSearch = request.isNeedSearch();
        if (GeneralUtil.isEmpty(needSearch)) {
            request.setNeedSearch(false);
            needSearch = false;
        }
        String subMetric = request.getSubMetric();
        List<String> fieldList = new ArrayList<>();
        if (needSearch) {
            fieldList.add(request.getSearchField());
        } else {
            fieldList.addAll(Bus101102Helper.getDrillDownFieldList(subMetric, metricInfoBean, remoteConfig));
            response.setDefaultChosenField(fieldList.get(0));
        }
        Map<String, Future<GetRawDataResponseType>> fieldMap = new HashMap<>();
        for (String field : fieldList) {
            SqlParamterBean sqlParamterBean = Bus101102Helper.getSiteDrillDownBaseInfoSqlBean(field, request, d, metricInfoBean, remoteConfig);
            GetRawDataRequestType fieldRequest = sqlParamterBean.convertBeanToRequest(false);
            fieldMap.put(field,switchNewTableHelper.switchRemoteDatabaseAsync(fieldRequest));
        }
        List<FieldDataItem> fieldDataItemList = new ArrayList<>();
        response.setFieldDataItemList(fieldDataItemList);
        for (String field : fieldList) {
            FieldDataItem item = new FieldDataItem();
            fieldDataItemList.add(item);
            Bus101102Helper.processDrillDownBaseInfo(request, field, fieldMap.get(field).get(), item, remoteConfig);
        }
        return response;
    }

    @Override
    public GetOverseaTableDataResponseType getBus101102SubTableData(GetOverseaTableDataRequestType request,
                                                                    String d,
                                                                    OverseaMetricInfoBean metricInfoBean) throws Exception {
        GetOverseaTableDataResponseType response = new GetOverseaTableDataResponseType();
        List<TableDataItem> tableDataItemList = new ArrayList<>();
        response.setTableDataItemList(tableDataItemList);

        String metric = request.getMetric();
        String subMetric = request.getSubMetric();
        DrillDownFilter drillDownFilter = request.getDrillDownFilter();
        String field = drillDownFilter.getField();
        SubMetricFiledBean configBean = remoteConfig.getSubMetricFiledBean(metric, subMetric, field);
        OverseaMetricHelper.checkBubble(configBean, request);

        Boolean needTarget = configBean.getNeedTarget();

        //获取当前数据
        SqlParamterBean currentBean = Bus101102Helper.getSiteTableDataSqlBean(request, d, metricInfoBean, configBean, remoteConfig, "current", null);
        GetRawDataRequestType currentReq = currentBean.convertBeanToRequest(true);
        ListenableFuture<GetRawDataResponseType> currentResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(currentReq);

        if (needTarget) {
            //获取目标数据
            SqlParamterBean targetBean = Bus101102Helper.getSiteTableDataSqlBean(request, d, metricInfoBean, configBean, remoteConfig, "target", null);
            GetRawDataRequestType targetReq = targetBean.convertBeanToRequest(true);
            ListenableFuture<GetRawDataResponseType> targetResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(targetReq);
            Bus101102Helper.processTableBaseData(currentResFuture.get(), targetResFuture.get(), tableDataItemList, metric, needTarget);
        } else {
            Bus101102Helper.processTableBaseData(currentResFuture.get(), null, tableDataItemList, metric, needTarget);
        }

        //收集下钻字段的数据
        List<String> pagingFieldValueList = tableDataItemList.stream()
                .map(i -> i.getFieldMap().get(configBean.getConditionColumn()))
                .collect(Collectors.toList());

        //如果当前有数据 则去获取同环比
        if (GeneralUtil.isNotEmpty(pagingFieldValueList)) {


            //获取去年数据
            SqlParamterBean lastyearBean = Bus101102Helper.getSiteTableDataSqlBean(request, d, metricInfoBean, configBean, remoteConfig, "lastyear", pagingFieldValueList);
            GetRawDataRequestType lastyearReq = lastyearBean.convertBeanToRequest(true);
            ListenableFuture<GetRawDataResponseType> lastyearResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(lastyearReq);
            //获取2019年数据
            SqlParamterBean _2019Bean = Bus101102Helper.getSiteTableDataSqlBean(request, d, metricInfoBean, configBean, remoteConfig, "2019", pagingFieldValueList);
            GetRawDataRequestType _2019Req = _2019Bean.convertBeanToRequest(true);
            ListenableFuture<GetRawDataResponseType> _2019ResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(_2019Req);


            Bus101102Helper.processTablePopData(lastyearResFuture.get(), tableDataItemList, "lastyear");
            Bus101102Helper.processTablePopData(_2019ResFuture.get(), tableDataItemList, "2019");
            //只有首页需要获取环比数据
            if ("firstpage".equals(request.getSource())) {
                //获取环比数据
                SqlParamterBean momBean = Bus101102Helper.getSiteTableDataSqlBean(request, d, metricInfoBean, configBean, remoteConfig, "mom", null);
                //环比的当前数据
                GetRawDataRequestType momReq = momBean.convertBeanToRequest(true);
                ListenableFuture<GetRawDataResponseType> momResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(momReq);
                //环比的上一期数据
                GetRawDataRequestType momReq2 = momBean.convertBeanToRequest2(true);
                ListenableFuture<GetRawDataResponseType> momRes2Future = switchNewTableHelper.switchRemoteDatabaseAsync(momReq2);
                String momType = OverseaMetricHelper.getMomType(request.getTimeFilter(), d);
                response.setMomType(momType);
                Bus101102Helper.processTableMomData(momResFuture.get(), momRes2Future.get(), tableDataItemList, momType);
            }
        }


        response.setTotalNum(currentResFuture.get().getTotalNum());
        response.setTableHeaderList(Bus101102Helper.getTableHeaderList(configBean,metric,needTarget));
        response.setShowField(configBean.getShowField());
        response.setShowFieldId(configBean.getShowFieldId());
        return response;
    }


    @Override
    public Future<MetricDetailInfo> getBus103SubMetricCardData(TimeFilter timeFilter,
                                                               OverseaMetricInfoBean metricInfoBean,
                                                               String d,
                                                               String metric,
                                                               String subMetric) throws Exception {

        MetricDetailInfo metricDetailInfo = new MetricDetailInfo();
        metricDetailInfo.setMetric(metric);
        metricDetailInfo.setSubMetric(subMetric);
        metricDetailInfo.setActualSubMetric(subMetric);
        //填充一些前端需要的额外信息
        Bus101102Helper.setMetricCardDrillDownParmater(metricDetailInfo, subMetric, metricInfoBean, remoteConfig);
        Map<String, Double> dimMap = new HashMap<>();
        metricDetailInfo.setDimData(dimMap);

        //获取当期数据
        SqlParamterBean currentBean = Bus103Helper.getSiteMetricCardSqlBean("current", timeFilter, metricInfoBean, d, remoteConfig);
        GetRawDataRequestType currentReq = currentBean.convertBeanToRequest(true);
        ListenableFuture<GetRawDataResponseType> currentResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(currentReq);

        //获取去年数据
        SqlParamterBean lastyearBean = Bus103Helper.getSiteMetricCardSqlBean("lastyear", timeFilter, metricInfoBean, d, remoteConfig);
        GetRawDataRequestType lastyearReq = lastyearBean.convertBeanToRequest(true);
        ListenableFuture<GetRawDataResponseType> lastyearResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(lastyearReq);

        //获取目标
        SqlParamterBean targetBean = Bus103Helper.getSiteMetricCardSqlBean("target", timeFilter, metricInfoBean, d, remoteConfig);
        GetRawDataRequestType targetReq = targetBean.convertBeanToRequest(true);
        ListenableFuture<GetRawDataResponseType> targetResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(targetReq);

        Bus103Helper.processMetricCardBaseData(currentResFuture.get(), targetResFuture.get(), dimMap, timeFilter.getYear());
        Bus103Helper.processMetricCardPopData(lastyearResFuture.get(), dimMap, timeFilter.getYear());

        return new AsyncResult<>(metricDetailInfo);
    }

    @Override
    public GetOverseaTrendLineDataResponseType getBus103SubTrendLineData(GetOverseaTrendLineDataRequestType request,
                                                                         String d,
                                                                         List<ExamineConfigBean> examineConfigBeanList) throws Exception {

        GetOverseaTrendLineDataResponseType response = new GetOverseaTrendLineDataResponseType();
        List<TrendLineDetailInfo> trendLineDetailInfoList = new ArrayList<>();
        response.setTrendLineDetailInfoList(trendLineDetailInfoList);

        String queryType = request.getQueryType();
        List<Future<SinglePeriodDataBean>> futureList = new ArrayList<>();

        for (ExamineConfigBean bean : examineConfigBeanList){

            OverSeaExamineConfigBO bo = new OverSeaExamineConfigBO();
            OverseaMetricInfoBean metricInfoBean = bo.getMetricInfoBean(bean.getBusinessDashboardOverseaExamineeConfig(), remoteConfig);

            //获取当期数据
            SqlParamterBean currentBean = Bus103Helper.getSiteTrendlineSqlBean(request, "current", metricInfoBean, d, remoteConfig, bean, bean.getYear());
            GetRawDataRequestType currentReq = currentBean.convertBeanToRequest(true);

            if("trendline".equals(queryType)){
                //获取目标
                SqlParamterBean targetBean = Bus103Helper.getSiteTrendlineSqlBean(request, "target", metricInfoBean, d, remoteConfig, bean, bean.getYear());
                GetRawDataRequestType targetReq = targetBean.convertBeanToRequest(true);

                futureList.add(overseaSinglePeriodTrendLineBiz.getBus103SubSinglePeriodTrendLineData(request, metricInfoBean, currentReq, targetReq, bean));
            }else{
                futureList.add(overseaSinglePeriodTrendLineBiz.getBus103SubSinglePeriodTrendLineData(request, metricInfoBean, currentReq, null, bean));
            }


        }

        //转化汇总返回数据
        PeriodDataBean periodDataBean = new PeriodDataBean();
        periodDataBean.setBeanData(futureList);

        //有效时间范围
        List<String> timeList = DateUtil.getAllSelectedTime(request.getTimeFilter(), "oversea");

        if ("trendline".equals(queryType)){
            //趋势线基础数据
            Bus103Helper.processTrendLineData(trendLineDetailInfoList, periodDataBean, timeList, request.getTimeFilter().getYear());
        }else{
            Bus103Helper.processDrilldownTrendLineData(trendLineDetailInfoList, periodDataBean, timeList, request.getTimeFilter().getYear());
        }

        return response;
    }

    @Override
    public GetOverseaDrillDownBaseInfoResponseType getBus103SubDrillDownBaseInfo(GetOverseaDrillDownBaseInfoRequestType request,
                                                                                 String d,
                                                                                 OverseaMetricInfoBean metricInfoBean) throws Exception {

        GetOverseaDrillDownBaseInfoResponseType response = new GetOverseaDrillDownBaseInfoResponseType();
        Boolean needSearch = request.isNeedSearch();
        if (GeneralUtil.isEmpty(needSearch)) {
            request.setNeedSearch(false);
            needSearch = false;
        }
        String subMetric = request.getSubMetric();
        List<String> fieldList = new ArrayList<>();

        if (needSearch) {
            fieldList.add(request.getSearchField());
        } else {
            fieldList.addAll(Bus103Helper.getDrillDownFieldList(subMetric, metricInfoBean, remoteConfig));
            response.setDefaultChosenField(fieldList.get(0));
        }
        Map<String, Future<GetRawDataResponseType>> fieldMap = new HashMap<>();
        for (String field : fieldList) {
            SqlParamterBean sqlParamterBean = Bus103Helper.getSiteDrillDownBaseInfoSqlBean(field, request, d, metricInfoBean, remoteConfig);
            GetRawDataRequestType fieldRequest = sqlParamterBean.convertBeanToRequest(false);
            fieldMap.put(field, switchNewTableHelper.switchRemoteDatabaseAsync(fieldRequest));
        }
        List<FieldDataItem> fieldDataItemList = new ArrayList<>();
        response.setFieldDataItemList(fieldDataItemList);
        for (String field : fieldList) {
            FieldDataItem item = new FieldDataItem();
            fieldDataItemList.add(item);
            Bus103Helper.processDrillDownBaseInfo(request, field, fieldMap.get(field).get(), item, remoteConfig);
        }


        return response;
    }

    @Override
    public GetOverseaTableDataResponseType getBus103SubTableData(GetOverseaTableDataRequestType request,
                                                                 String d,
                                                                 OverseaMetricInfoBean metricInfoBean) throws Exception {

        GetOverseaTableDataResponseType response = new GetOverseaTableDataResponseType();
        List<TableDataItem> tableDataItemList = new ArrayList<>();
        response.setTableDataItemList(tableDataItemList);

        String metric = request.getMetric();
        String subMetric = request.getSubMetric();
        DrillDownFilter drillDownFilter = request.getDrillDownFilter();
        String field = drillDownFilter.getField();
        String year = request.getTimeFilter().getYear();
        int y = year != null ? Integer.parseInt(year) : 0;
        SubMetricFiledBean configBean;
        if(y >= 2024){
            configBean = remoteConfig.getSubMetricFiledBean(year, metric, subMetric, field);
        }else {
            configBean = remoteConfig.getSubMetricFiledBean(metric, subMetric, field);
        }


        Boolean needTarget = configBean.getNeedTarget();

        //获取当前数据
        SqlParamterBean currentBean = Bus103Helper.getSiteTableDataSqlBean(request, d, metricInfoBean, configBean, remoteConfig, "current");
        GetRawDataRequestType currentReq = currentBean.convertBeanToRequest(true);
        ListenableFuture<GetRawDataResponseType> currentResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(currentReq);

        if (needTarget) {
            //获取目标数据
            SqlParamterBean targetBean = Bus103Helper.getSiteTableDataSqlBean(request, d, metricInfoBean, configBean, remoteConfig, "target");
            GetRawDataRequestType targetReq = targetBean.convertBeanToRequest(true);
            ListenableFuture<GetRawDataResponseType> targetResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(targetReq);
            Bus103Helper.processTableBaseData(currentResFuture.get(), targetResFuture.get(), tableDataItemList, needTarget, year);
        } else {
            Bus103Helper.processTableBaseData(currentResFuture.get(), null, tableDataItemList, needTarget, year);
        }


        response.setTotalNum(currentResFuture.get().getTotalNum());
        response.setTableHeaderList(Bus103Helper.getTableHeaderList(configBean, year));
        response.setShowField(configBean.getShowField());
        response.setShowFieldId(configBean.getShowFieldId());
        return response;
    }




    @Override
    public String getSubMetricName() {
        return "site";
    }
}
