package com.ctrip.tour.business.dashboard.tktBusiness.biz.impl;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.FilterBoxBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.bo.OrganizationInfoBo;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardEmployeeInfoDao;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardOrganizationInfoDao;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardEmployeeInfo;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardOrganizationInfo;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.ctrip.tour.business.dashboard.utils.UserUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/7/22
 *
 * 1.2023/03/17 增加对超级管理员老季的支持  其所有数据为李哲的数据+孙磊的数据之和
 */
@Service
@Slf4j
public class FilterBoxBizImpl implements FilterBoxBiz {


    @Autowired
    private RemoteConfig remoteConfig;

    @Autowired
    private BusinessDashboardEmployeeInfoDao employeeInfoDao;

    @Autowired
    private BusinessDashboardOrganizationInfoDao organizationInfoDao;


    @Override
    public GetEmployeeByFilterResponseType getEmployeeByFilter(GetEmployeeByFilterRequestType request) throws Exception {
        Boolean needSearch = request.isNeedSearch();
        //默认为正常查询模式
        if (needSearch == null) {
            needSearch = false;
        }
        //搜索模式
        if (needSearch) {
            Boolean needRecursion = request.isNeedRecursion();
            if (needRecursion == null) {
                needRecursion = false;
            }
            if (needRecursion) {
                //递归搜索模式 会模糊搜索获取该员工有权限查看的所有人
                return getEmployeeWithRecursionSearch(request, null);
            } else {
                //正常搜索模式
                return getEmployeeWithSearch(request, null);
            }
        }
        GetEmployeeByFilterResponseType response =  getEmployeeWithoutSearch(request,null);

        return response;
    }

    private GetEmployeeByFilterResponseType getAdminEmployeeWithRecursionSearch(GetEmployeeByFilterRequestType request) throws SQLException {
        String adminInferior = remoteConfig.getExternalConfig("adminInferior");
        String[] inferiorArray = adminInferior.split("\\|");
        GetEmployeeByFilterResponseType response0 = getEmployeeWithRecursionSearch(request, inferiorArray[0]);
        GetEmployeeByFilterResponseType response1 = getEmployeeWithRecursionSearch(request, inferiorArray[1]);
        List<EmployeeItem> employeeItemList0 = response0.getEmployeeItemList();
        List<EmployeeItem> employeeItemList1 = response1.getEmployeeItemList();
        employeeItemList0.addAll(employeeItemList1);
        //只是单层结构 去重展示
        response0.setEmployeeItemList(employeeItemList0.stream().distinct().collect(Collectors.toList()));
        return response0;
    }

    private GetEmployeeByFilterResponseType getEmployeeWithRecursionSearch(GetEmployeeByFilterRequestType request,
                                                                           String forceEmpCode) throws SQLException {

        GetEmployeeByFilterResponseType response = new GetEmployeeByFilterResponseType();

        List<EmployeeItem> employeeItemList = new ArrayList<>();
        response.setEmployeeItemList(employeeItemList);

        String searchWord = request.getSearchWord();
        if (searchWord == null || "".equals(searchWord)) {
            return response;
        }

        String empCode = UserUtil.getMappingEmpCode(remoteConfig);
        if(GeneralUtil.isNotEmpty(forceEmpCode)){
            empCode = forceEmpCode;
        }

        //管理员的数据等于李哲+孙磊的数据
        if(remoteConfig.getExternalConfig("admin").equals(empCode)){
            return getAdminEmployeeWithRecursionSearch(request);
        }
        BusinessDashboardEmployeeInfo employeeInfo = employeeInfoDao.queryByEmpCode(empCode);

        //默认数据 员工一定可以搜索本人 此时不用附加任何条件
        String displayName = employeeInfo.getDisplayName();
        String domainName = employeeInfo.getDomainName();
        if (displayName.contains(searchWord) || domainName.contains(searchWord)) {
            EmployeeItem defaultItem = new EmployeeItem();
            defaultItem.setEmpCode(employeeInfo.getEmpCode());
            defaultItem.setDisplayName(employeeInfo.getDisplayName());
            defaultItem.setDomainName(employeeInfo.getDomainName());
            defaultItem.setPostion(employeeInfo.getPosition());
            defaultItem.setOrgId(employeeInfo.getTeamId());
            defaultItem.setOrgName(employeeInfo.getTeamCname());
            employeeItemList.add(defaultItem);
        }

        //获取所有以该员工为leader的组织
        List<BusinessDashboardOrganizationInfo> organizationInfoList = organizationInfoDao.queryByLeaderEmpCode(empCode);
        //该员工不是leader 直接就返回了
        if (GeneralUtil.isEmpty(organizationInfoList)) {
            return response;
        }
        OrganizationInfoBo bo = new OrganizationInfoBo();
        List<String> childOrgIdList = bo.getChildOrgIdList(organizationInfoList);

        List<BusinessDashboardEmployeeInfo> possibleEmployeeInfoList = employeeInfoDao.likeSearchEmpInfo(searchWord);
        for (BusinessDashboardEmployeeInfo possibleEmployeeInfo : possibleEmployeeInfoList) {
            String orgIdPath = possibleEmployeeInfo.getOrgIdPath();
            for (String childOrgId : childOrgIdList) {
                if (orgIdPath.contains(childOrgId)) {
                    EmployeeItem employeeItem = new EmployeeItem();
                    employeeItem.setEmpCode(possibleEmployeeInfo.getEmpCode());
                    employeeItem.setDisplayName(possibleEmployeeInfo.getDisplayName());
                    employeeItem.setDomainName(possibleEmployeeInfo.getDomainName());
                    employeeItem.setPostion(possibleEmployeeInfo.getPosition());
                    employeeItem.setOrgId(possibleEmployeeInfo.getTeamId());
                    employeeItem.setOrgName(possibleEmployeeInfo.getTeamCname());
                    employeeItemList.add(employeeItem);
                    break;
                }
            }
        }
        return response;
    }


    private GetEmployeeByFilterResponseType getAdminEmployeeWithoutSearch(GetEmployeeByFilterRequestType request) throws SQLException {
        String adminInferior = remoteConfig.getExternalConfig("adminInferior");
        String[] inferiorArray = adminInferior.split("\\|");
        GetEmployeeByFilterResponseType response0 = getEmployeeWithoutSearch(request, inferiorArray[0]);
        GetEmployeeByFilterResponseType response1 = getEmployeeWithoutSearch(request, inferiorArray[1]);
        List<EmployeeItem> employeeItemList0 = response0.getEmployeeItemList();
        List<EmployeeItem> employeeItemList1 = response1.getEmployeeItemList();
        employeeItemList0.addAll(employeeItemList1);
        //只是单层结构 去重展示
        List<EmployeeItem> employeeItemList = employeeItemList0.stream().distinct().collect(Collectors.toList());

        String boardType = request.getBoardType();
        if ("oversea".equalsIgnoreCase(boardType)){
            Collections.reverse(employeeItemList);
        }
        response0.setEmployeeItemList(employeeItemList);
        return response0;
    }


    private GetEmployeeByFilterResponseType getEmployeeWithoutSearch(GetEmployeeByFilterRequestType request,
                                                                     String forceEmpCode) throws SQLException {
        GetEmployeeByFilterResponseType response = new GetEmployeeByFilterResponseType();
        List<EmployeeItem> employeeItemList = new ArrayList<>();
        response.setEmployeeItemList(employeeItemList);

        String empCode = UserUtil.getMappingEmpCode(remoteConfig);
        if(GeneralUtil.isNotEmpty(forceEmpCode)){
            empCode = forceEmpCode;
        }

        //管理员的数据等于李哲+孙磊的数据
        if(remoteConfig.getExternalConfig("admin").equals(empCode)){
            return getAdminEmployeeWithoutSearch(request);
        }

        BusinessDashboardEmployeeInfo employeeInfo = employeeInfoDao.queryByEmpCode(empCode);

        String orgId = request.getOrgId();
        //没有传入orgId时 为默认请求
        //传入的orgId为员工的直接上级组织id时  特殊处理
        if (orgId == null || employeeInfo.getTeamId().equals(orgId)) {
            EmployeeItem employeeItem = new EmployeeItem();
            employeeItem.setEmpCode(employeeInfo.getEmpCode());
            employeeItem.setDisplayName(employeeInfo.getDisplayName());
            employeeItem.setDomainName(employeeInfo.getDomainName());
            employeeItem.setPostion(employeeInfo.getPosition());
            employeeItem.setOrgId(employeeInfo.getTeamId());
            employeeItem.setOrgName(employeeInfo.getTeamCname());
            employeeItemList.add(employeeItem);
            return response;
        }

        List<BusinessDashboardEmployeeInfo> employeeInfoList = employeeInfoDao.queryByTeamId(orgId);

        for (BusinessDashboardEmployeeInfo bean : employeeInfoList) {
            EmployeeItem employeeItem = new EmployeeItem();
            employeeItem.setEmpCode(bean.getEmpCode());
            employeeItem.setDisplayName(bean.getDisplayName());
            employeeItem.setDomainName(bean.getDomainName());
            employeeItem.setPostion(bean.getPosition());
            employeeItem.setOrgId(bean.getTeamId());
            employeeItem.setOrgName(bean.getTeamCname());
            employeeItemList.add(employeeItem);
        }
        return response;
    }

    private GetEmployeeByFilterResponseType getAdminEmployeeWithSearch(GetEmployeeByFilterRequestType request) throws SQLException {
        String adminInferior = remoteConfig.getExternalConfig("adminInferior");
        String[] inferiorArray = adminInferior.split("\\|");
        GetEmployeeByFilterResponseType response0 = getEmployeeWithSearch(request, inferiorArray[0]);
        GetEmployeeByFilterResponseType response1 = getEmployeeWithSearch(request, inferiorArray[1]);
        List<EmployeeItem> employeeItemList0 = response0.getEmployeeItemList();
        List<EmployeeItem> employeeItemList1 = response1.getEmployeeItemList();
        employeeItemList0.addAll(employeeItemList1);
        //只是单层结构 去重展示
        response0.setEmployeeItemList(employeeItemList0.stream().distinct().collect(Collectors.toList()));
        return response0;
    }

    private GetEmployeeByFilterResponseType getEmployeeWithSearch(GetEmployeeByFilterRequestType request,
                                                                  String forceEmpCode) throws SQLException {
        GetEmployeeByFilterResponseType response = new GetEmployeeByFilterResponseType();

        List<EmployeeItem> employeeItemList = new ArrayList<>();
        response.setEmployeeItemList(employeeItemList);

        String searchWord = request.getSearchWord();
        if (searchWord == null || "".equals(searchWord)) {
            return response;
        }

        String orgId = request.getOrgId();
        //当搜索的是当前登录人所在组织时  特殊处理
        String empCode = UserUtil.getMappingEmpCode(remoteConfig);

        if(GeneralUtil.isNotEmpty(forceEmpCode)){
            empCode = forceEmpCode;
        }

        //管理员的数据等于李哲+孙磊的数据
        if(remoteConfig.getExternalConfig("admin").equals(empCode)){
            return getAdminEmployeeWithSearch(request);
        }

        BusinessDashboardEmployeeInfo employeeInfo = employeeInfoDao.queryByEmpCode(empCode);

        if (employeeInfo.getTeamId().equals(orgId)) {
            if(employeeInfo.getDisplayName().contains(searchWord)){
                EmployeeItem employeeItem = new EmployeeItem();
                employeeItem.setEmpCode(employeeInfo.getEmpCode());
                employeeItem.setDisplayName(employeeInfo.getDisplayName());
                employeeItem.setDomainName(employeeInfo.getDomainName());
                employeeItem.setPostion(employeeInfo.getPosition());
                employeeItem.setOrgId(employeeInfo.getTeamId());
                employeeItem.setOrgName(employeeInfo.getTeamCname());
                employeeItemList.add(employeeItem);
            }
            return response;
        }



        List<BusinessDashboardEmployeeInfo> employeeInfoList = employeeInfoDao.likeSearchEmpInfo(searchWord, orgId);

        for (BusinessDashboardEmployeeInfo bean : employeeInfoList) {
            EmployeeItem employeeItem = new EmployeeItem();
            employeeItem.setEmpCode(bean.getEmpCode());
            employeeItem.setDisplayName(bean.getDisplayName());
            employeeItem.setDomainName(bean.getDomainName());
            employeeItem.setPostion(bean.getPosition());
            employeeItem.setOrgId(bean.getTeamId());
            employeeItem.setOrgName(bean.getTeamCname());
            employeeItemList.add(employeeItem);
        }
        return response;

    }

    @Override
    public GetOrganizationByFilterResponseType getOrganizationByFilter(GetOrganizationByFilterRequestType request) throws Exception {

        //是否需要全量组织架构
        Boolean needFullData = request.isNeedFullData();
        if (needFullData == null) {
            needFullData = false;
        }
        if(needFullData){
            return getFullOrganization(null);
        }

        Boolean needSearch = request.isNeedSearch();
        //默认为正常查询模式
        if (needSearch == null) {
            needSearch = false;
        }
        GetOrganizationByFilterResponseType response;
        //搜索模式
        if (needSearch) {
            response = getOrganizationWithSearch(request,null);
        } else {
            response = getOrganizationWithoutSearch(request,null);
        }
        //判断结果中的组织列表是否有下级组织
        List<String> orgIdList = response.getOrganizationItemList().stream()
                .map(OrganizationItem::getOrgId)
                .collect(Collectors.toList());
        if(!orgIdList.isEmpty()){
            List<BusinessDashboardOrganizationInfo> organizationInfoList = organizationInfoDao.queryByParentOrgId(orgIdList);
            OrganizationInfoBo bo = new OrganizationInfoBo();
            Set<String> orgIdSet = bo.getOrgIdSet(organizationInfoList);
            for (OrganizationItem item : response.getOrganizationItemList()) {
                String orgId = item.getOrgId();
                //如果没有下级 则填充是叶子节点
                item.setIsLeaf(!orgIdSet.contains(orgId));
            }
        }
        return response;
    }

    private GetOrganizationByFilterResponseType getAdminFullOrganization() throws SQLException {

        String adminInferior = remoteConfig.getExternalConfig("adminInferior");
        String[] inferiorArray = adminInferior.split("\\|");
        GetOrganizationByFilterResponseType response0 = getFullOrganization(inferiorArray[0]);
        GetOrganizationByFilterResponseType response1 = getFullOrganization(inferiorArray[1]);
        List<OrganizationItem> organizationItemList0 = response0.getOrganizationItemList();
        List<OrganizationItem> organizationItemList1 = response1.getOrganizationItemList();
        organizationItemList0.get(0).getChildren().addAll(organizationItemList1.get(0).getChildren());
        return response0;
    }

    private GetOrganizationByFilterResponseType getFullOrganization(String forceEmpCode) throws SQLException {
        GetOrganizationByFilterResponseType response = new GetOrganizationByFilterResponseType();
        List<OrganizationItem> organizationItemList = new ArrayList<>();
        response.setOrganizationItemList(organizationItemList);

        //先查直接上级组织
        String empCode = UserUtil.getMappingEmpCode(remoteConfig);
        if(GeneralUtil.isNotEmpty(forceEmpCode)){
            empCode = forceEmpCode;
        }
        //如果是管理员 老季这种 那么拉取全量的组织
        //全量的组织等于李哲的全部组织+孙磊的全部组织
        if(remoteConfig.getExternalConfig("admin").equals(empCode)){
            return getAdminFullOrganization();
        }

        BusinessDashboardEmployeeInfo employeeInfo = employeeInfoDao.queryByEmpCode(empCode);
        if (employeeInfo == null) {
            return response;
        }
        OrganizationItem parentItem = new OrganizationItem();
        parentItem.setOrgId(employeeInfo.getTeamId());
        parentItem.setOrgName(employeeInfo.getTeamCname());
        organizationItemList.add(parentItem);

        //查找所有以当前员工为领导的组织
        List<BusinessDashboardOrganizationInfo> childOrganizationInfoList = organizationInfoDao.queryByLeaderEmpCode(empCode);
        OrganizationInfoBo bo = new OrganizationInfoBo();
        List<String> childOrgIdList = bo.getChildOrgIdList(childOrganizationInfoList);

        //如果不存在以某员工为领导的组织  则为基层员工
        if (childOrgIdList.isEmpty()) {
            return response;
        }

        //查找所有父级组织id为当前传入id的组织
        List<BusinessDashboardOrganizationInfo> organizationInfoList = organizationInfoDao.queryWithChildreOrgId(childOrgIdList);
        Map<String, List<OrganizationItem>> orgItemListMap = new HashMap<>();
        //待排除的orgId
        String excludeOrgId = remoteConfig.getConfigValue("excludeOrgId");
        List<OrganizationItem> specialOrganizationList = new ArrayList<>();
        for (BusinessDashboardOrganizationInfo bean : organizationInfoList) {
            String nodeOrgId = bean.getNodeOrgId();
            if (excludeOrgId.equals(nodeOrgId)) {
                continue;
            }
            String parentOrgId = bean.getParentOrgId();
            List<OrganizationItem> orgItemList = orgItemListMap.getOrDefault(parentOrgId, new ArrayList<>());
            OrganizationItem item = new OrganizationItem();
            item.setOrgId(nodeOrgId);
            item.setOrgName(bean.getNodeOrgName());
            orgItemList.add(item);
            orgItemListMap.put(parentOrgId, orgItemList);
        }
        //补偿逻辑 如果当前父节点无法从map中取到数据 就把中间的节点补齐
        makeUpIntermediateNode(orgItemListMap, organizationInfoList,parentItem);
        //创建队列
        Deque<OrganizationItem> deque = new LinkedList();
        deque.addLast(parentItem);
        while (!deque.isEmpty()) {
            OrganizationItem firstItem = deque.pollFirst();
            String firstOrgId = firstItem.getOrgId();
            if (!GeneralUtil.isEmpty(remoteConfig.getOrganizationOrderValue(firstOrgId))) {
                specialOrganizationList.add(firstItem);
            }
            List<OrganizationItem> orgItemList = orgItemListMap.get(firstOrgId);
            if (!GeneralUtil.isEmpty(orgItemList)) {
                firstItem.setChildren(orgItemList);
                for (OrganizationItem item : orgItemList) {
                    deque.addLast(item);
                }
            } else {
                firstItem.setIsLeaf(true);
            }
        }

        for (OrganizationItem item : specialOrganizationList) {
            String orgId = item.getOrgId();
            List<String> childrenOrgIdList = Lists.newArrayList(remoteConfig.getOrganizationOrderValue(orgId).split("\\|"));
            Map<String, OrganizationItem> childrenItemMap = new HashMap<>();
            for (OrganizationItem childrenItem : item.getChildren()) {
                String childrenOrgId = childrenItem.getOrgId();
                childrenItemMap.put(childrenOrgId, childrenItem);
            }
            List<OrganizationItem> orderOrganizationItemList = new ArrayList<>();
            for (String childrenOrgId : childrenOrgIdList) {
                OrganizationItem organizationItem = childrenItemMap.get(childrenOrgId);
                if (organizationItem != null) {
                    orderOrganizationItemList.add(organizationItem);
                    childrenItemMap.remove(childrenOrgId);
                }
            }
            for (Map.Entry<String, OrganizationItem> entry : childrenItemMap.entrySet()) {
                orderOrganizationItemList.add(entry.getValue());
            }
            item.setChildren(orderOrganizationItemList);

        }
        return response;
    }


    private void makeUpIntermediateNode(Map<String, List<OrganizationItem>> orgItemListMap,
                                        List<BusinessDashboardOrganizationInfo> organizationInfoList,
                                        OrganizationItem parentItem) throws SQLException {
        String orgId = parentItem.getOrgId();
        //如果能取到下级节点 直接返回
        if (orgItemListMap.containsKey(orgId)) {
            return;
        }
        Set<String> orgIdSet = new HashSet<>();
        for (BusinessDashboardOrganizationInfo info : organizationInfoList) {
            String orgIdPath = info.getOrgIdPath();
            String[] orgIdArray = orgIdPath.split("\\.");
            //最后一个元素map里面肯定有了
            for (int i = orgIdArray.length - 2; i >= 0; i--) {
                if (orgId.equals(orgIdArray[i])) {
                    break;
                }
                orgIdSet.add(orgIdArray[i]);
            }
        }
        if (GeneralUtil.isNotEmpty(orgIdSet)) {
            List<BusinessDashboardOrganizationInfo> makeUpList = organizationInfoDao.queryByOrgId(new ArrayList<>(orgIdSet));
            for (BusinessDashboardOrganizationInfo info : makeUpList) {
                OrganizationItem item = new OrganizationItem();
                item.setOrgId(info.getNodeOrgId());
                item.setOrgName(info.getNodeOrgName());
                String parentOrgId = info.getParentOrgId();
                List<OrganizationItem> orgItemList = orgItemListMap.getOrDefault(parentOrgId, new ArrayList<>());
                orgItemList.add(item);
                orgItemListMap.put(parentOrgId, orgItemList);
            }
            //可能会重复添加 对元素去重
            for (Map.Entry<String, List<OrganizationItem>> entry : orgItemListMap.entrySet()) {
                String key = entry.getKey();
                List<OrganizationItem> newValueList = entry.getValue()
                        .stream()
                        .distinct()
                        .collect(Collectors.toList());
                orgItemListMap.put(key, newValueList);
            }
        }

    }


    private GetOrganizationByFilterResponseType getAdminOrganizationWithSearch(GetOrganizationByFilterRequestType request) throws SQLException {
        String adminInferior = remoteConfig.getExternalConfig("adminInferior");
        String[] inferiorArray = adminInferior.split("\\|");
        GetOrganizationByFilterResponseType response0 = getOrganizationWithSearch(request, inferiorArray[0]);
        GetOrganizationByFilterResponseType response1 = getOrganizationWithSearch(request, inferiorArray[1]);
        List<OrganizationItem> organizationItemList0 = response0.getOrganizationItemList();
        List<OrganizationItem> organizationItemList1 = response1.getOrganizationItemList();
        organizationItemList0.addAll(organizationItemList1);
        //只是单层结构 去重展示
        response0.setOrganizationItemList(organizationItemList0.stream().distinct().collect(Collectors.toList()));
        return response0;
    }


    private GetOrganizationByFilterResponseType getOrganizationWithSearch(GetOrganizationByFilterRequestType request,
                                                                          String forceEmpCode) throws SQLException {
        GetOrganizationByFilterResponseType response = new GetOrganizationByFilterResponseType();
        List<OrganizationItem> organizationItemList = new ArrayList<>();
        response.setOrganizationItemList(organizationItemList);
        String searchWord = request.getSearchWord();
        if (searchWord == null || "".equals(searchWord)) {
            return response;
        }

        String empCode = UserUtil.getMappingEmpCode(remoteConfig);

        if(GeneralUtil.isNotEmpty(forceEmpCode)){
            empCode = forceEmpCode;
        }
        //如果是管理员 老季这种
        //他的数据是李哲和孙磊的数据之和
        if(remoteConfig.getExternalConfig("admin").equals(empCode)){
            return getAdminOrganizationWithSearch(request);
        }

        //特殊处理该员工的直接上级组织
        //如果符合搜索条件 则额外追加到结果集中
        BusinessDashboardEmployeeInfo employeeInfo = employeeInfoDao.queryByEmpCode(empCode);
        if (employeeInfo.getTeamCname().contains(searchWord)) {
            OrganizationItem organizationItem = new OrganizationItem();
            organizationItem.setOrgId(employeeInfo.getTeamId());
            organizationItem.setOrgName(employeeInfo.getTeamCname());
            organizationItemList.add(organizationItem);
        }
        //查找以当前员工为领导的组织 组织路径中包含这些组织id的组织都是当前员工可以查看的组织
        List<BusinessDashboardOrganizationInfo> childOrganizationInfoList = organizationInfoDao.queryByLeaderEmpCode(empCode);
        OrganizationInfoBo bo = new OrganizationInfoBo();
        List<String> childOrgIdList = bo.getChildOrgIdList(childOrganizationInfoList);

        //如果不存在以某员工为领导的组织  则为基层员工
        if (childOrgIdList.isEmpty()) {
            return response;
        }
        //待排除的orgId
        String excludeOrgId = remoteConfig.getConfigValue("excludeOrgId");
        List<BusinessDashboardOrganizationInfo> organizationInfoList = organizationInfoDao.likeSearchOrgInfo(searchWord, childOrgIdList);
        for (BusinessDashboardOrganizationInfo bean : organizationInfoList) {
            String nodeOrgId = bean.getNodeOrgId();
            if (excludeOrgId.equals(nodeOrgId)) {
                continue;
            }
            OrganizationItem organizationItem = new OrganizationItem();
            organizationItem.setOrgId(nodeOrgId);
            organizationItem.setOrgName(bean.getNodeOrgName());
            organizationItemList.add(organizationItem);
        }
        return response;

    }

    private GetOrganizationByFilterResponseType getAdminOrganizationWithoutSearch(GetOrganizationByFilterRequestType request) throws SQLException {
        String adminInferior = remoteConfig.getExternalConfig("adminInferior");
        String[] inferiorArray = adminInferior.split("\\|");
        GetOrganizationByFilterResponseType response0 = getOrganizationWithoutSearch(request, inferiorArray[0]);
        GetOrganizationByFilterResponseType response1 = getOrganizationWithoutSearch(request, inferiorArray[1]);
        List<OrganizationItem> organizationItemList0 = response0.getOrganizationItemList();
        List<OrganizationItem> organizationItemList1 = response1.getOrganizationItemList();
        organizationItemList0.addAll(organizationItemList1);
        //只是单层结构 去重展示
        response0.setOrganizationItemList(organizationItemList0.stream().distinct().collect(Collectors.toList()));
        return response0;
    }


    private GetOrganizationByFilterResponseType getOrganizationWithoutSearch(GetOrganizationByFilterRequestType request,
                                                                             String forceEmpCode) throws SQLException {
        GetOrganizationByFilterResponseType response = new GetOrganizationByFilterResponseType();
        List<OrganizationItem> organizationItemList = new ArrayList<>();
        response.setOrganizationItemList(organizationItemList);

        String empCode = UserUtil.getMappingEmpCode(remoteConfig);

        if(GeneralUtil.isNotEmpty(forceEmpCode)){
            empCode = forceEmpCode;
        }
        //如果是管理员 老季这种
        //他的数据是李哲和孙磊的数据之和
        if(remoteConfig.getExternalConfig("admin").equals(empCode)){
            return getAdminOrganizationWithoutSearch(request);
        }

        String orgId = request.getOrgId();
        //此时为默认请求  返回当前员工对应的直接上级组织信息
        if (orgId == null) {
            BusinessDashboardEmployeeInfo employeeInfo = employeeInfoDao.queryByEmpCode(empCode);
            //查不到意味着这个人没权限  直接返回
            if(GeneralUtil.isEmpty(employeeInfo)){
                return response;
            }
            OrganizationItem organizationItem = new OrganizationItem();
            organizationItem.setOrgId(employeeInfo.getTeamId());
            organizationItem.setOrgName(employeeInfo.getTeamCname());
            organizationItemList.add(organizationItem);
            return response;
        }

        //若传入父级组织id
        //查找以当前员工为领导的组织 组织路径中包含这些组织id的组织都是当前员工可以查看的组织
        List<BusinessDashboardOrganizationInfo> childOrganizationInfoList = organizationInfoDao.queryByLeaderEmpCode(empCode);
        OrganizationInfoBo bo = new OrganizationInfoBo();
        List<String> childOrgIdList = bo.getChildOrgIdList(childOrganizationInfoList);

        //如果不存在以某员工为领导的组织  则为基层员工
        if (childOrgIdList.isEmpty()) {
            return response;
        }

        //查找所有父级组织id为当前传入id的组织
        List<BusinessDashboardOrganizationInfo> organizationInfoList = organizationInfoDao.queryWithOrgId(orgId, childOrgIdList);
        Map<String, OrganizationItem> itemMap = new HashMap<>();
        for (BusinessDashboardOrganizationInfo bean : organizationInfoList) {
            String nodeOrgId = bean.getNodeOrgId();
            OrganizationItem organizationItem = new OrganizationItem();
            organizationItem.setOrgId(nodeOrgId);
            organizationItem.setOrgName(bean.getNodeOrgName());
            organizationItemList.add(organizationItem);
            itemMap.put(nodeOrgId, organizationItem);
        }
        String parentOrgId = remoteConfig.getConfigValue("parentOrgId");
        if (parentOrgId.equals(orgId)) {
            List<OrganizationItem> orderOrganizationItemList = new ArrayList<>();
            List<String> childrenOrgIdList =
                    Lists.newArrayList(remoteConfig.getConfigValue("childrenOrgIdList").split("\\|"));
            for (String childrenOrgId : childrenOrgIdList) {
                OrganizationItem organizationItem = itemMap.get(childrenOrgId);
                if (organizationItem != null) {
                    orderOrganizationItemList.add(organizationItem);
                    itemMap.remove(childrenOrgId);
                }
            }
            //待排除的orgId
            String excludeOrgId = remoteConfig.getConfigValue("excludeOrgId");
            itemMap.remove(excludeOrgId);
            for(Map.Entry<String,OrganizationItem> entry:itemMap.entrySet()){
                orderOrganizationItemList.add(entry.getValue());
            }
            response.setOrganizationItemList(orderOrganizationItemList);
        }
        return response;
    }
}
