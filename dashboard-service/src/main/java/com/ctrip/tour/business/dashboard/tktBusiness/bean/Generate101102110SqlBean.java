package com.ctrip.tour.business.dashboard.tktBusiness.bean;

import lombok.Data;

import java.util.List;

@Data
public class Generate101102110SqlBean {
    String searchType;
    String quarter;
    String year;
    List<String> buTypeNames;
    List<String> destinationLevels;
    String domainName;
    String ct;
    String d;
    String dateType;
    String siteType;
    String metric;


    public Generate101102110SqlBean(String searchType,
                                    String quarter,
                                    String year,
                                    List<String> buTypeNames,
                                    List<String> destinationLevels,
                                    String domainName,
                                    String ct,
                                    String d,
                                    String dateType,
                                    String metric) {
        this.d = d;
        this.ct = ct;
        this.domainName = domainName;
        this.destinationLevels = destinationLevels;
        this.buTypeNames = buTypeNames;
        this.year = year;
        this.quarter = quarter;
        this.searchType = searchType;
        this.dateType = dateType;
        this.metric = metric;
    }
}
