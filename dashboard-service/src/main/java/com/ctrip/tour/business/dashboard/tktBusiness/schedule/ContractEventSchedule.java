package com.ctrip.tour.business.dashboard.tktBusiness.schedule;

import com.ctrip.tour.business.dashboard.tktBusiness.bean.ContractEventAggTimeBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ContractEventSceneBean;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.PushMessageToTaskBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardNoticeInfoDao;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardNoticeInfo;
import com.ctrip.tour.business.dashboard.utils.MapperUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import qunar.tc.qschedule.config.QSchedule;

import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service
public class ContractEventSchedule {
    private Logger log = LoggerFactory.getLogger(ContractEventSchedule.class);

    @Autowired
    private BusinessDashboardNoticeInfoDao noticeInfoDao;

    @Autowired
    private RemoteConfig remoteConfig;

    @Autowired
    private PushMessageToTaskBiz messageToTaskBiz;

    public DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 对多个场景进行统一调度 同步  删除
     *      *  数据：0点前数据
     *      *  方案1：第二天 凌晨1点开始调度当天0点前的数据；4点开始同步0点前的数据；同步完毕后删除0点前的数据；
     *      *  方案2：第二天 凌晨1点同步；凌晨2点开始调度当天0点前的数据，跑完代码删除；
     * @throws Exception
     */
    @QSchedule("ctrip.tour.bi.businessDashboard.contractEvent")
    public void contractEventJob() throws Exception{
        log.info("start contractEvent schedule....");
        // 获取每个场景的配置信息
        Map<String, ContractEventSceneBean> configMap = configGroupBy();
        log.info("get bigScene size: " + configMap.size());

        for (String sceneType : configMap.keySet()) {
            ContractEventSceneBean sceneMap = configMap.get(sceneType);
            boolean isSplitScene = sceneMap.isSplitScene();
            // 需要切分场景，从子场景中取值
            if (isSplitScene) {
                Map<String, ContractEventSceneBean> splitSceneMap = sceneMap.getSplitSceneMap();
                for (String splitType : splitSceneMap.keySet()) {
                    ContractEventSceneBean sceneBean = splitSceneMap.get(splitType);
                    doScene(sceneBean);
                }
            }
            // 不需要切分场景，直接取值
            else {
                doScene(sceneMap);
            }
        }

        // 删除当天已操作过数据
        // noticeInfoDao.deleteByDate(doTime);
        log.info("stop contractEvent schedule....");
    }

    /**
     * 根据配置信息，对单个场景进行操作
     *
     * @param sceneBean
     */
    public void doScene(ContractEventSceneBean sceneBean) throws Exception {
        try{
            log.info("start scene: " + sceneBean.getSceneType());
            int messageType = sceneBean.getMessageType();
            String sceneType = sceneBean.getSceneType();
            // 1: 通知，需要聚合后发送
            if (messageType == 1) {
                // 查表
                Map<String, List<BusinessDashboardNoticeInfo>> sceneDataMap = getSceneDataMap(messageType, sceneType, sceneBean.getContractEventAggTimeBean());
                // 聚合
                for (String receiver : sceneDataMap.keySet()) {
                    try{
                        List<BusinessDashboardNoticeInfo> sceneDataList = sceneDataMap.get(receiver);
                        String className = sceneBean.getClassName();
                        Class<?> cls = Class.forName(className);
                        List<Object> messageList = new ArrayList<>();
                        for (BusinessDashboardNoticeInfo noticeInfo : sceneDataList) {
                            List<?> messages = MapperUtil.str2List(noticeInfo.getMessage(), cls);
                            messageList.addAll(messages);
                        }
                        // 推送
                        messageToTaskBiz.pushMessage(receiver, messageList, sceneBean);
                    }catch (Exception e){
                        log.warn("doscene receiver fail, reason: " + e.getMessage());
                    }


                }
            }
            log.info("stop scene: " + sceneBean.getSceneType());
        }catch (Exception e){
            log.warn("doscene fail, reason: " + e.getMessage());
        }

    }



    /**
     * 根据场景查询数据，并按照发送人进行数据分组
     *
     * @param messageType
     * @param SceneType
     * @return
     * @throws SQLException
     */
    public Map<String, List<BusinessDashboardNoticeInfo>> getSceneDataMap(int messageType, String SceneType, ContractEventAggTimeBean contractEventAggTimeBean) throws SQLException {
        // String doTime = LocalDate.now().minusDays(1).toString();
        Map<String, String> timeRange = getAggTimeRange(contractEventAggTimeBean);
        List<BusinessDashboardNoticeInfo> noticeInfoList = noticeInfoDao.getSceneDataList(String.valueOf(messageType), SceneType, timeRange);
        return noticeInfoList.stream().collect(Collectors.groupingBy(BusinessDashboardNoticeInfo::getReceiver));
    }

    public Map<String, String> getAggTimeRange(ContractEventAggTimeBean contractEventAggTimeBean){
        Map<String, String> timeRange = new HashMap<>();
        LocalDateTime now = LocalDateTime.now();
        int year = now.getYear();
        int month = now.getMonth().getValue();
        int day = now.getDayOfMonth();
        int hour = now.getHour();

        int endTime = contractEventAggTimeBean.getEndType() == 1 ? hour : contractEventAggTimeBean.getEndTime();  // endType默认等于0；默认走配置endTime
        LocalDateTime endDate = LocalDateTime.of(year, month, day, endTime, 0, 0);
        String start = endDate.minusHours(contractEventAggTimeBean.getInterval()).format(formatter);
        String end = endDate.format(formatter);
        timeRange.put("start", start);
        timeRange.put("end", end);
        return timeRange;
    }


    /**
     * 对配置的场景进行分组方便解析
     *
     * @return
     */
    public Map<String, ContractEventSceneBean> configGroupBy() {
        String scheduleConfig = remoteConfig.getConfigValue("scheduleConfig");
        List<ContractEventSceneBean> scheduleConfigList = MapperUtil.str2List(scheduleConfig, ContractEventSceneBean.class);
        return scheduleConfigList.stream()
                .collect(Collectors.toMap(ContractEventSceneBean::getSceneType, // 分组依据
                        contractEventSceneBean -> contractEventSceneBean, // 分组后的元素处理
                        (v1, v2) -> v1)); // 分组后的Value冲突处理, 选择第一个
    }
}
