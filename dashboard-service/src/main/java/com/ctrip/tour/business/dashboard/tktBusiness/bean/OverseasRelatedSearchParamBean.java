package com.ctrip.tour.business.dashboard.tktBusiness.bean;

import lombok.Data;

import java.util.List;

@Data
public class OverseasRelatedSearchParamBean {

    String d;

    // 邮箱前缀
    String domainName;
    // 特殊情况
    List<String> domainNameList;
    /** 产量 **/
    //使用日期
    String useDate;
    //季度
    String quarter;
    //年
    String year;
    //业务线
    List<String> buTypeNames;
    //CT站
    String ct;
    //站点
    List<String> sites;
    //国家/地区
    List<String> ctryNames;
    //省份
    List<String> provNames;
    //城市
    List<String> cityNames;
    //产品经理
    List<String> prdMeids;
    //产品助理
    List<String> prdAeids;
    //景点经理
    List<String> vstMeids;
    //景点助理
    List<String> vstAeids;
    //开始时间
    String startDate;
    //结束时间
    String endDate;
    //考核层级
    List<String> examineLevel;

    /** 目标值 **/
    // 考核类型
    String examineType;

    // 考核类型值
    List<String> examineTypeValue;

    // 考核指标类型
    String examineMetricType;

    // 业务大区
    List<String> businessRegionName;

    // 业务子区域
    List<String> businessSubRegionNames;

    // C/T站
    String ctType;

    /** 商品力竞争 **/
    // 统计维度ID
    // 1-邮箱前缀
    //2-邮箱前缀,大区,子区域
    //3-大区
    //4-大区,子区域
    //5-大区,子区域,国家/地区
    String statisticsDimId;

    // 国家
    List<String> countryNames;

    // 季度列表
    List<String> quarters;

    // 表示维度
    String dimValue;
}
