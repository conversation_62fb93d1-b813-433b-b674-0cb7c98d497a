package com.ctrip.tour.business.dashboard.tktBusiness.qmq;

import com.ctrip.framework.triplog.shaded.client.tag.TagMarker;
import com.ctrip.framework.triplog.shaded.client.tag.TagMarkerBuilder;
import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ContractEventSceneBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.EmailGroupBean;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.PushMessageToTaskBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.KmsConfiguration;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardEmployeeInfoDao;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardNoticeInfoDao;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardEmployeeInfo;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardNoticeInfo;
import com.ctrip.tour.business.dashboard.utils.*;
import com.ctrip.tour.ttd.product.soa.*;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;
import java.lang.reflect.Field;
import java.sql.SQLException;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 任务管理系统（八期）-签约事件接入工作台 相关32个场景接入
 */
@Service
public class QmqContractEventConsumer {
    private static final Logger log = LoggerFactory.getLogger(QmqContractEventConsumer.class);
    private static TagMarker marker = TagMarkerBuilder.newBuilder().add("tag", "QmqContractEventConsumer").build();

    @Autowired
    private BusinessDashboardEmployeeInfoDao employeeInfoDao;

    @Autowired
    private BusinessDashboardNoticeInfoDao noticeInfoDao;

    @Autowired
    private RemoteConfig remoteConfig;

    @Autowired
    private KmsConfiguration kmsConfiguration;

    @Autowired
    private TtdProductBasicServiceClient productClient;

    @Autowired
    private PushMessageToTaskBiz messageToTaskBiz;

    @QmqConsumer(prefix = "tour.bi.businessdashboard.notice", consumerGroup = "100038120", idempotentChecker="redisIdempotent")
    public void onMessage(Message msg) throws Exception {
        String messageId = msg.getMessageId();
        log.info(marker, "receive message: " + messageId);
        // 业务处理：接受消息，如果在处理消息 落库之前 处理失败，默认该消息不符合要求，过滤掉
        // String messageType = msg.getStringProperty("messageType");
        String sceneType = msg.getStringProperty("sceneType");
        String messageInfo = msg.getStringProperty("messageInfo");
        String receivers = msg.getStringProperty("receivers");
        monitor(sceneType, messageInfo, "tour.bi.business.dashboard.contract.event.qmq.length");
        handleMessage(messageId, sceneType, messageInfo, receivers);
    }


    /**
     * 监控每个场景的长度
     * @param sceneType
     * @param messageInfo
     */
    public void monitor(String sceneType, String messageInfo, String name){
        Map<String, String> tagMap = new HashMap<>();
        tagMap.put("sceneType", sceneType);
        HickWallLogUtil.logSendLengthWithTag(messageInfo.length(), name, tagMap);
    }


    /**
     * 对qmq接受到的消息的处理
     * @param messageId
     * @param sceneType
     * @param messageInfo
     * @param receivers
     */
    public void handleMessage(String messageId, String sceneType, String messageInfo, String receivers){
        log.info(marker, "start handle message: " + messageId);
        try{
            // 获取每个场景的配置信息
            Map<String, ContractEventSceneBean> configMap = configGroupBy();
            ContractEventSceneBean sceneMap = configMap.get(sceneType);
            log.info(marker, "get configMap success: " + messageId);
            // 对数据进行预处理

            // 1. java反射方式通用解析消息
            String className = sceneMap.getClassName();
            Class<?> cls = Class.forName(className);
            List<?> messageList = MapperUtil.str2List(messageInfo, cls);
            log.info(marker, "transform data success: " + messageId);

            // 2. 接收人转换工号：如果不在门票活动的人员，获取不到员工号，直接舍弃
            List<String> receiverList = preprocessingOfGetEmployee(receivers, messageId);
            log.info(marker, "transform receiver success: " + messageId);

            // 3. 场景拆分 & 区分消息类型[任务直接触发] & 入库
            // 是否需要切分场景
            boolean isSplitScene = sceneMap.isSplitScene();
            // 需要切分场景
            if (isSplitScene) {
                int splitType = sceneMap.getSplitType(); // 获取切分场景的方式
                Map<String, List<?>> splitMessageList = getSplitSceneMessageMap(messageList, cls, splitType);  // 按照场景切分数据
                Map<String, ContractEventSceneBean> splitSceneMap = sceneMap.getSplitSceneMap();  // 获取切分场景的配置项

                for (String scene : splitSceneMap.keySet()) {
                    ContractEventSceneBean sceneConfig = splitSceneMap.get(scene);
                    List<?> sceneMessageList = splitMessageList.get(scene);
                    monitor(sceneType, MapperUtil.obj2Str(sceneMessageList), "tour.bi.business.dashboard.contract.event.qmq.split.length");
                    preprocessingOfSplit(sceneMessageList, receiverList, messageId, sceneConfig);
                }
            }
            // 不需要切分场景, 根据messageType选择入库还是发送
            else {
                preprocessingOfSplit(messageList, receiverList, messageId, sceneMap);  // TODO：暂时无该场景，后续再测试
            }
            log.info(marker, "handle message success: " + messageId);
        } catch (Exception e){
            HickWallLogUtil.logSend("tour.bi.business.dashboard.tkt.notice.push.error.count");
            log.error(marker, "handle message fail: " + messageId + "; reason: " + e.getMessage());
        }
        log.info(marker, "stop handle message: " + messageId);
    }

    /**
     * 对配置的场景进行分组方便解析
     * @return
     */
    public Map<String, ContractEventSceneBean> configGroupBy(){
        String scheduleConfig = remoteConfig.getConfigValue("scheduleConfig");
        List<ContractEventSceneBean> scheduleConfigList = MapperUtil.str2List(scheduleConfig, ContractEventSceneBean.class);
        return scheduleConfigList.stream()
                .collect(Collectors.toMap(ContractEventSceneBean::getSceneType, // 分组依据
                        contractEventSceneBean -> contractEventSceneBean, // 分组后的元素处理
                        (v1, v2) -> v1)); // 分组后的Value冲突处理, 选择第一个
    }

    /**
     * 数据预处理的最后一步，根据配置判断是通知还是任务
     * 1. 通知：入库[数据按照人员维度拆分]
     * 2. 任务：入库后, 直接发送
     * @param sceneMessageList
     * @param sceneConfig
     */
    public void preprocessingOfSplit(List<?> sceneMessageList, List<String> receiverList, String messageId, ContractEventSceneBean sceneConfig) throws Exception {
        log.info(marker, "start preprocessingOfSplit messageId: " + messageId);
        // 1: 通知，需要聚合,所以按照人拆分落库 | 2:任务：入库后, 直接发送
        List<BusinessDashboardNoticeInfo> noticeInfos = new ArrayList<>();
        if(sceneMessageList != null && sceneMessageList.size() > 0){
            for (String receiver : receiverList) {
                BusinessDashboardNoticeInfo noticeInfo = new BusinessDashboardNoticeInfo();
                noticeInfo.setMessageId(messageId);
                noticeInfo.setMessageType(String.valueOf(sceneConfig.getMessageType()));
                noticeInfo.setSceneType(sceneConfig.getSceneType());
                noticeInfo.setMessage(MapperUtil.obj2Str(sceneMessageList));
                noticeInfo.setReceiver(receiver);
                noticeInfo.setDate(LocalDate.now().toString());
                noticeInfos.add(noticeInfo);
            }
            log.info(marker, "batchInsert size: " + noticeInfos.size());
            int[] r = noticeInfoDao.batchInsert(new DalHints(), noticeInfos);
            log.info(marker, "batchInsert success size: " + r.length);
            // 2:任务，直接发送  3:不需聚合的直接发送
            if (sceneConfig.getMessageType() == 2 || sceneConfig.getMessageType() == 3) {
                try{
                    List<Object> messageList = new ArrayList<>(sceneMessageList);
                    for (String receiver : receiverList) {
                        log.info(marker, "start push message, receiver: " + receiver + "; messageId: " + messageId);
                        messageToTaskBiz.pushMessage(receiver, messageList, sceneConfig);
                    }
                }catch (Exception e){
                    log.warn("qmqConsumer send message fail, reason: " + e.getMessage());
                    throw new RuntimeException(e);
                }

            }
        }
        log.info(marker, "stop preprocessingOfSplit messageId: " + messageId);
    }




    /**
     * 数据预处理，获取员工号
     * @param receivers
     * @return
     */
    public List<String> preprocessingOfGetEmployee(String receivers, String messageId) throws SQLException {
        log.info(marker, "before receiver: " + receivers + "; messageId: " + messageId);
        List<String> employees = new ArrayList<>();
        List<String> receiverList = MapperUtil.str2List(receivers, String.class);
        List<String> domainList = new ArrayList<>();
        for (String receiver : receiverList) {
            if (receiver.contains("@trip.com")) {
                employees.addAll(Objects.requireNonNull(getEmployeeNumberByEmailGroup(receiver)));
            } else {
                domainList.add(receiver);
            }
        }
        List<BusinessDashboardEmployeeInfo> employeeInfos = employeeInfoDao.getEmployeeCode(domainList);
        if(employeeInfos != null && employeeInfos.size() > 0){
            employees.addAll(employeeInfos.stream().map(BusinessDashboardEmployeeInfo::getEmpCode).collect(Collectors.toList()));
        }
        log.info(marker, "after receiver: " + MapperUtil.obj2Str(employees) + "; messageId: " + messageId);
        return employees;
    }


    /**
     * 根据邮箱组获取邮箱组内成员工号
     */
    public List<String> getEmployeeNumberByEmailGroup(String emailGroup) {
        List<String> employeeList = new ArrayList<>();
        String url = kmsConfiguration.getKmsValue("getMemberInfoUrl");
        log.info(marker, "kms value: " + url);
        String data = String.format("{\"access_token\":\"%s\",\"request_body\":{\"emailaddress\":\"%s\"}}", kmsConfiguration.getKmsValue("getMemberInfoToken"), emailGroup);
        String resp = DownloadUtil.post(url, data);
        List<EmailGroupBean> result = MapperUtil.str2List(resp, EmailGroupBean.class);
        for (EmailGroupBean emailGroupBean : result) {
            employeeList.add(emailGroupBean.getEmpCode());
        }
        return employeeList;
    }




    /**
     * 根据配置项选择不同的获取切割场景的方式进行场景数据切割
     *
     * @param messageList：消息列表
     * @param cls：消息对应的类
     * @param splitType：切割方式
     * @throws Exception
     */
    public Map<String, List<?>> getSplitSceneMessageMap(List<?> messageList, Class<?> cls, int splitType) throws Exception {
        if (splitType == 1) {
            return splitSceneByProductId(messageList, cls);
        }
        return null;
    }


    /**
     * 一期拆分场景的功能：根据产品id进行拆分成business|supplier
     * @param messageList
     * @param cls
     * @return
     * @throws Exception
     */
    public Map<String, List<?>> splitSceneByProductId(List<?> messageList, Class<?> cls) throws Exception {
        Map<String, List<?>> resultMap = new HashMap<>();
        // 获取供应商id的集合
        List<Long> productIdList = messageList.stream().map(obj -> {
            try {
                Field name = cls.getDeclaredField("productId");
                name.setAccessible(true);
                return (Long) name.get(obj);
            } catch (IllegalAccessException | NoSuchFieldException e) {
                throw new RuntimeException(e);
            }
        }).collect(Collectors.toList());

        // 获取id对应场景，需要切分
        Map<Long, Integer> map = new HashMap<>();
        List<Long> pidList = new ArrayList<>();
        for (int i = 1; i <= productIdList.size(); i++) {
            pidList.add(productIdList.get(i - 1));
            if (i % 100 == 0 || i == productIdList.size()) {
                GetProductInfoRequestType request = new GetProductInfoRequestType();
                request.setProductIds(pidList);
                ProductReturnOptionType returnOption = new ProductReturnOptionType();
                returnOption.setNeedBasicInfo(true);
                request.setReturnOption(returnOption);
                GetProductInfoResponseType response = productClient.getProductInfo(request);
                if(response.getProductInfoList() != null && response.getProductInfoList().size() > 0) {
                    for (ProductInfoType p : response.getProductInfoList()) {
                        int source = p.getBasicInfo().getSource();
                        map.put(p.getProductId(), source);
                    }
                }else{
                    log.warn(marker, "split scene fail: productClient.getProductInfo == null | size <=0, productIds: " + MapperUtil.obj2Str(pidList));
                    throw new RuntimeException("split scene fail: productClient.getProductInfo == null | size <=0");
                }
                pidList = new ArrayList<>();
            }
        }
        Field name = cls.getDeclaredField("productId");
        name.setAccessible(true);

        List<Object> supplierList = new ArrayList<>();  // 供应商
        List<Object> businessList = new ArrayList<>();  // 业务

        for (Object m : messageList) {
            Long productId = (Long) name.get(m);
            int source = map.get(productId);
            if (source == 4 || source == 11) {
                supplierList.add(m);
            } else {
                businessList.add(m);
            }
        }
        // 这里与配置项保持一致
        resultMap.put("business", businessList);
        resultMap.put("supplier", supplierList);

        return resultMap;
    }
}
