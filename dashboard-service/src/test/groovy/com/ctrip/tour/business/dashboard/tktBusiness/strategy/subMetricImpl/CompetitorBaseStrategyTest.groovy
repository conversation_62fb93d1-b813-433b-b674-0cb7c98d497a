package com.ctrip.tour.business.dashboard.tktBusiness.strategy.subMetricImpl

import com.ctrip.soa._24922.DrillDownFilter
import com.ctrip.soa._24922.GetOverseaDrillDownBaseInfoRequestType
import com.ctrip.soa._24922.GetOverseaTableDataRequestType
import com.ctrip.soa._24922.GetOverseaTrendLineDataRequestType
import com.ctrip.soa._24922.TimeFilter
import com.ctrip.soa._27181.BIBaseReportQueryServiceClient
import com.ctrip.soa._27181.GetRawDataResponseType
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ExamineConfigBean
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBean
import com.ctrip.tour.business.dashboard.tktBusiness.bean.SinglePeriodDataBean
import com.ctrip.tour.business.dashboard.tktBusiness.bean.SubMetricFiledBean
import com.ctrip.tour.business.dashboard.tktBusiness.biz.OverseaSinglePeriodTrendLineBiz
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig
import com.ctrip.tour.business.dashboard.utils.MapperUtil
import com.google.common.util.concurrent.JdkFutureAdapters
import org.springframework.scheduling.annotation.AsyncResult
import spock.lang.Specification
import spock.lang.Unroll

class CompetitorBaseStrategyTest extends Specification {

    def baseReportQueryServiceClient = Mock(BIBaseReportQueryServiceClient)
    def remoteConfig = Mock(RemoteConfig)
    def overseaSinglePeriodTrendLineBiz = Mock(OverseaSinglePeriodTrendLineBiz)

    def competitorBaseStrategy = new CompetitorBaseStrategy(baseReportQueryServiceClient: baseReportQueryServiceClient,
            remoteConfig: remoteConfig, overseaSinglePeriodTrendLineBiz: overseaSinglePeriodTrendLineBiz)



    def setup() {
    }


//    @Unroll
//    def "测试指标卡接口"() {
//
//        given: "设置请求参数"
//        def timeFilter = new TimeFilter(year: "2023", dateType: "half", half: "H2")
//        def metricInfoBean = new OverseaMetricInfoBean(metric: inputMetric, destinationLevel: destinationLevel, destinationRangeList: destinationRangeList)
//
//        and: "mock接口请求返回数据"
//        def currentStr = "{\"result\":\"[[20,0.0]]\",\"metricList\":[\"cw_Inferiority_num\",\"cw_Inferiority_rate\"]}"
//        baseReportQueryServiceClient.getRawData(_) >> getResponse(currentStr)
//
//        and: "mock qconfig配置信息"
//        remoteConfig.getConfigValue("oversea") >> "海外"
//        remoteConfig.getConfigValue("region") >> "大区"
//        remoteConfig.getConfigValue("subRegion") >> "子区域"
//
//        when: "调用指标卡接口"
//        def response = competitorBaseStrategy.getBus105106107SubMetricCardData(timeFilter, metricInfoBean, '2023-09-21', inputMetric, inputSubMetric).get()
//
//        then: "验证返回结果"
//
//        with(response) {
//            metric == inputMetric
//            subMetric == inputSubMetric
//            defaultField == outputDefaultField
//            dimData.get("cw_Inferiority_num") < 0.244
//
//        }
//
//        where: "用表格方式验证多种返回结果"
//        inputMetric | inputSubMetric | destinationLevel | destinationRangeList | outputDefaultField
//        "105"       | "tklk"         | "海外"           | []                   | "region"
//        "105"       | "tfly"         | "海外"           | []                   | "region"
//        "105"       | "tklk"         | "大区"           | ["海长大区"]         | "province"
//        "105"       | "tklk"         | "子区域"         | ["日本", "韩国"]     | "province"
//
//
//    }


//    @Unroll
//    def "测试趋势线接口"() {
//        given: "设置请求参数"
//        def timeFilter = new TimeFilter(year: "2023", dateType: "month", month: "09", timeFrame: 3)
//        def examineConfigBeanListStr = "[{\"year\":\"2023\",\"month\":\"07\",\"timeMap\":{\"currentTime\":\"2023-07\",\"lastyearTime\":\"2022-07\",\"2019Time\":\"2019-07\"},\"isLastestPeriod\":false,\"limitTimeMap\":{},\"businessDashboardOverseaExamineeConfig\":{\"id\":40240,\"domainName\":\"leisun\",\"year\":\"2023\",\"quarter\":\"Q3\",\"examineMetric\":\"105;106;107\",\"destinationExamineLevel\":\"海外\",\"destinationExamineRange\":\"\",\"siteExamineRange\":\"\",\"channelExamineRange\":\"\",\"queryD\":\"2023-09-22\",\"datachangeLasttime\":\"Sep 22, 2023 12:34:45 AM\"},\"dateType\":\"month\"},{\"year\":\"2023\",\"month\":\"08\",\"timeMap\":{\"currentTime\":\"2023-08\",\"lastyearTime\":\"2022-08\",\"2019Time\":\"2019-08\"},\"isLastestPeriod\":false,\"limitTimeMap\":{},\"businessDashboardOverseaExamineeConfig\":{\"id\":40240,\"domainName\":\"leisun\",\"year\":\"2023\",\"quarter\":\"Q3\",\"examineMetric\":\"105;106;107\",\"destinationExamineLevel\":\"海外\",\"destinationExamineRange\":\"\",\"siteExamineRange\":\"\",\"channelExamineRange\":\"\",\"queryD\":\"2023-09-22\",\"datachangeLasttime\":\"Sep 22, 2023 12:34:45 AM\"},\"dateType\":\"month\"},{\"year\":\"2023\",\"month\":\"09\",\"timeMap\":{\"currentTime\":\"2023-09\",\"lastyearTime\":\"2022-09\",\"2019Time\":\"2019-09\"},\"isLastestPeriod\":true,\"limitTimeMap\":{\"popYearTime\":\"2019-09-21\",\"time\":\"2023-09-21\",\"lastYearTime\":\"2022-09-21\"},\"businessDashboardOverseaExamineeConfig\":{\"id\":40240,\"domainName\":\"leisun\",\"year\":\"2023\",\"quarter\":\"Q3\",\"examineMetric\":\"105;106;107\",\"destinationExamineLevel\":\"海外\",\"destinationExamineRange\":\"\",\"siteExamineRange\":\"\",\"channelExamineRange\":\"\",\"queryD\":\"2023-09-22\",\"datachangeLasttime\":\"Sep 22, 2023 12:34:45 AM\"},\"dateType\":\"month\"}]"
//        def examineConfigBeanList = MapperUtil.str2List(examineConfigBeanListStr, ExamineConfigBean.class)
//        def request = new GetOverseaTrendLineDataRequestType(timeFilter: timeFilter, metric: "105", subMetric: "tklk", domainName: "leisun", queryType: queryType, drillDownFilter: drillDownFilter)
//
//        and: "mock汇总接口请求返回数据"
//        def period1Str = "{\"periodWeaknessList\":[[\"2023-09\",0.27710843373493976,3.4476085671521445E-4]],\"weaknessHeaderList\":[\"time\",\"cw_Inferiority_num\",\"cw_Inferiority_rate\"]}"
//        def period2Str = "{\"periodWeaknessList\":[[\"2023-08\",0.27710843373493976,3.4476085671521445E-4]],\"weaknessHeaderList\":[\"time\",\"cw_Inferiority_num\",\"cw_Inferiority_rate\"]}"
//        def period3Str = "{\"periodWeaknessList\":[[\"2023-07\",0.27710843373493976,3.4476085671521445E-4]],\"weaknessHeaderList\":[\"time\",\"cw_Inferiority_num\",\"cw_Inferiority_rate\"]}"
//
//        overseaSinglePeriodTrendLineBiz.getBus105106107SubSinglePeriodTrendLineData({ i -> i.queryType == "trendline" }, _, _, _, _) >> getTrendLineFutureResponse(period1Str) >> getTrendLineFutureResponse(period2Str) >> getTrendLineFutureResponse(period3Str)
//
//
//        and: "mock下钻趋势线请求返回数据"
//        def period4Str = "{\"periodWeaknessList\":[[\"2023-09\",\"东南亚\",0.0,0.0],[\"2023-09\",\"东北亚\",0.21686746987951808,0.001903523059241064],[\"2023-09\",\"港澳新\",0.3855421686746988,0.0029357852297331355],[\"2023-09\",\"海长大区\",0.0,0.0],[\"2023-09\",\"欧洲\",0.012048192771084338,1.075731497418241E-4]],\"weaknessHeaderList\":[\"time\",\"region_name\",\"cw_Inferiority_num\",\"cw_Inferiority_rate\"]}"
//        def period5Str = "{\"periodWeaknessList\":[[\"2023-08\",\"东南亚\",0.0,0.0],[\"2023-08\",\"东北亚\",0.21686746987951808,0.001903523059241064],[\"2023-08\",\"港澳新\",0.3855421686746988,0.0029357852297331355],[\"2023-08\",\"海长大区\",0.0,0.0],[\"2023-08\",\"欧洲\",0.012048192771084338,1.075731497418241E-4]],\"weaknessHeaderList\":[\"time\",\"region_name\",\"cw_Inferiority_num\",\"cw_Inferiority_rate\"]}"
//        def period6Str = "{\"periodWeaknessList\":[[\"2023-07\",\"东南亚\",0.0,0.0],[\"2023-07\",\"东北亚\",0.21686746987951808,0.001903523059241064],[\"2023-07\",\"港澳新\",0.3855421686746988,0.0029357852297331355],[\"2023-07\",\"海长大区\",0.0,0.0],[\"2023-07\",\"欧洲\",0.012048192771084338,1.075731497418241E-4]],\"weaknessHeaderList\":[\"time\",\"region_name\",\"cw_Inferiority_num\",\"cw_Inferiority_rate\"]}"
//        def subMetricFieldBeanStr = "{\"subMetricList\":[\"tklk\",\"tfly\"],\"field\":\"region\",\"baseInfoGroupList\":[\"region_id\",\"region_name\"],\"baseInfoLikeIndexList\":[1],\"needLine\":true,\"lineGroupListMap\":{\"current\":[\"region_name\"]},\"conditionColumn\":\"region_id\",\"headerFieldList\":[\"region_name\"],\"tableGroupListMap\":{\"other\":[\"region_name\"]},\"showField\":\"region_name\",\"showFieldId\":\"region_id\"}"
//
//        overseaSinglePeriodTrendLineBiz.getBus105106107SubSinglePeriodTrendLineData({ i -> i.queryType == "drilldown" }, _, _, _, _) >> getTrendLineFutureResponse(period4Str) >> getTrendLineFutureResponse(period5Str) >> getTrendLineFutureResponse(period6Str)
//
//
//        and: "mock qconfig配置信息"
//        remoteConfig.getConfigValue("oversea") >> "海外"
//        remoteConfig.getConfigValue("region") >> "大区"
//        remoteConfig.getConfigValue("subRegion") >> "子区域"
//        remoteConfig.getSubMetricFiledBean(_, _, _) >> MapperUtil.str2Obj(subMetricFieldBeanStr, SubMetricFiledBean.class)
//
//        when: "调用趋势线接口"
//        def response = competitorBaseStrategy.getBus105106107SubTrendLineData(request, "2023-09-22", examineConfigBeanList)
//
//        then: "验证返回结果"
//        with(response) {
//            if (trendLineDetailInfoList) {
//                trendLineDetailInfoList.size() == actualSize
//                def innerList = trendLineDetailInfoList.get(0).getTrendLineDataItemList()
//                if (innerList) {
//                    with(innerList) {
//                        size() == 3
//                    }
//                }
//            }
//        }
//
//
//        where: "用表格方式验证多种返回结果"
//        queryType   | drillDownFilter                                          | actualSize
//        "trendline" | null                                                     | 3
//        "drilldown" | new DrillDownFilter(field: "region", fieldValueList: []) | 5
//    }


//    @Unroll
//    def "测试下钻基础数据接口"() {
//
//        given: "设置请求参数"
//        def request = new GetOverseaDrillDownBaseInfoRequestType(metric: "105", subMetric: "tklk",
//                domainName: "leisun", timeFilter: new TimeFilter(year: "2023", dateType: "half", half: "H2"),
//                needSearch: needSearch, searchWord: searchWord, searchField: searchField)
//        def metricInfoBean = new OverseaMetricInfoBean(metric: "105", destinationLevel: "海外")
//
//        and: "mock接口请求返回数据"
//        def regionStr = "{\"result\":\"[[100,\\\"North East Asia\\\"],[200,\\\"South East Asia\\\"],[300,\\\"Hong Kong&Macau&Singapore\\\"],[400,\\\"Long Haul\\\"],[500,\\\"Europe\\\"],[600,\\\"Travel Essencials\\\"]]\",\"groupList\":[\"region_id\",\"region_name_en\"],\"metricList\":[]}";
//        def provinceStr = "{\"result\":\"[[1001,\\\"Japan\\\"],[1002,\\\"Korea\\\"],[1003,\\\"Taiwan\\\"],[2001,\\\"Thailand\\\"],[2002,\\\"Malaysia&Indonesia&Brunei Darussalam\\\"],[2003,\\\"Vietnam&Cambodia&Philippines\\\"],[2004,\\\"Other region\\\"],[3001,\\\"Hong Kong&Macau\\\"],[3002,\\\"Singapore\\\"],[4001,\\\"America\\\"],[4002,\\\"Australia&New Zealand\\\"],[4003,\\\"Middle East&Africa\\\"],[4004,\\\"North Asia\\\"],[5001,\\\"Europe\\\"],[6001,\\\"Travel Essencials\\\"]]\",\"groupList\":[\"province_id\",\"province_name_en\"],\"metricList\":[]}"
//        def countryStr = "{\"result\":\"[[-9999,\\\"unkwn\\\"],[330,\\\"Maldives\\\"],[444,\\\"Mauritius\\\"],[1615,\\\"Barbados\\\"],[20340,\\\"Estonia\\\"],[20354,\\\"United Kingdom\\\"],[20372,\\\"Armenia\\\"],[20391,\\\"Mongolia\\\"],[20424,\\\"New Caledonia\\\"],[20435,\\\"French Polynesia\\\"],[21396,\\\"Singapore\\\"],[21600,\\\"Saint Lucia\\\"],[21608,\\\"Jamaica\\\"],[21615,\\\"U.S. Virgin Islands\\\"],[21617,\\\"Puerto Rico\\\"],[100021,\\\"Thailand\\\"],[100022,\\\"Malaysia\\\"],[100023,\\\"Belgium\\\"],[100024,\\\"France\\\"],[100025,\\\"Germany\\\"],[100026,\\\"Italy\\\"],[100027,\\\"Austria\\\"],[100028,\\\"Netherlands\\\"],[100029,\\\"Canada\\\"],[100030,\\\"Egypt\\\"],[100035,\\\"Spain\\\"],[100036,\\\"Greece\\\"],[100041,\\\"Japan\\\"],[100042,\\\"South Korea\\\"],[100044,\\\"Philippines\\\"],[100045,\\\"Indonesia\\\"],[100046,\\\"Vietnam\\\"],[100047,\\\"United States\\\"],[100048,\\\"Australia\\\"],[100049,\\\"South Africa\\\"],[100050,\\\"Switzerland\\\"],[100069,\\\"Denmark\\\"],[100073,\\\"Türkiye\\\"],[100074,\\\"Israel\\\"],[100075,\\\"Malta\\\"],[100078,\\\"New Zealand\\\"],[100079,\\\"Nepal\\\"],[100080,\\\"India\\\"],[100081,\\\"Cambodia\\\"],[100083,\\\"Russia\\\"],[100084,\\\"Sri Lanka\\\"],[100085,\\\"Jordan\\\"],[100087,\\\"Kenya\\\"],[100089,\\\"Brunei Darussalam\\\"],[100090,\\\"Ireland\\\"],[100092,\\\"Finland\\\"],[100094,\\\"Czech Republic\\\"],[100096,\\\"Iceland\\\"],[100097,\\\"Sweden\\\"],[100098,\\\"Norway\\\"],[100099,\\\"United Arab Emirates\\\"],[100101,\\\"Hungary\\\"],[100102,\\\"Fiji\\\"],[100105,\\\"Laos\\\"],[100106,\\\"Portugal\\\"],[100108,\\\"Mexico\\\"],[100109,\\\"Brazil\\\"],[100111,\\\"Argentina\\\"],[100115,\\\"Tunisia\\\"],[100116,\\\"Cyprus\\\"],[100118,\\\"Poland\\\"],[100120,\\\"Romania\\\"],[100121,\\\"Saudi Arabia\\\"],[100126,\\\"Ecuador\\\"],[100129,\\\"Croatia\\\"],[100131,\\\"Monaco\\\"],[100132,\\\"Morocco\\\"],[100134,\\\"Colombia\\\"],[100135,\\\"Panama\\\"],[100137,\\\"Qatar\\\"],[100140,\\\"Costa Rica\\\"],[100145,\\\"Dominican Republic\\\"],[100151,\\\"Namibia\\\"],[100200,\\\"Côte d'Ivoire\\\"],[100215,\\\"Somalia\\\"],[100220,\\\"El Salvador\\\"],[100223,\\\"Albania\\\"],[100229,\\\"Georgia\\\"],[100259,\\\"Bolivia\\\"],[100260,\\\"Uruguay\\\"],[100261,\\\"Luxembourg\\\"],[110000,\\\"China\\\"]]\",\"groupList\":[\"country_id\",\"country_name_en\"],\"metricList\":[]}"
//        def examineeStr = "{\"result\":\"[[\\\"danyangwu\\\",\\\"danyang wu （吴丹阳）\\\"],[\\\"eric.huang\\\",\\\"Eric Huang （黃孟樺）\\\"],[\\\"hongjin\\\",\\\"hong jin （金鸿）\\\"],[\\\"ivy.tan\\\",\\\"Ivy Tan\\\"],[\\\"jack.yee\\\",\\\"Jack Yee （余聪杰）\\\"],[\\\"jaemin.park\\\",\\\"Jaemin Park\\\"],[\\\"jcdong\\\",\\\"Kathy Dong （董纪春）\\\"],[\\\"jcn\\\",\\\"CN CHAN （詹馨恩）\\\"],[\\\"jimmy.aman\\\",\\\"Jimmy Aman\\\"],[\\\"junyoung.kim\\\",\\\"Junyoung Kim\\\"],[\\\"karina.chong\\\",\\\"Karina Chong\\\"],[\\\"kelvin.ong\\\",\\\"Kelvin Ong\\\"],[\\\"kim_sojung\\\",\\\"Sojung Kim\\\"],[\\\"lalita.sincharoenkun\\\",\\\"Lalita Sincharoenkun\\\"],[\\\"masun\\\",\\\"Michelle SUN （孙敏安）\\\"],[\\\"mh_jiang\\\",\\\"muhwa Chiang （江慕华）\\\"],[\\\"ming.li\\\",\\\"Ming Li （李明）\\\"],[\\\"mtxu\\\",\\\"Nicole Xu （许孟婷）\\\"],[\\\"mydinh.ho\\\",\\\"Ho My Dinh (Ho My Dinh)\\\"],[\\\"myra.ho\\\",\\\"Myra Ho\\\"],[\\\"nelson.lee\\\",\\\"Nelson Lee\\\"],[\\\"rennay.lam\\\",\\\"Rennay Lam\\\"],[\\\"seungjoo.baek\\\",\\\"SEUNG JOO BAEK\\\"],[\\\"siqicheng\\\",\\\"Stella Cheng （程思琦）\\\"],[\\\"siriwimon.siemsorn\\\",\\\"Siriwimon Siemsorn\\\"],[\\\"siyili\\\",\\\"Siyi Li （李思怡）\\\"],[\\\"sun_yi\\\",\\\"Yi Sun （孙昳）\\\"],[\\\"swluo\\\",\\\"Tony Luo （罗圣文）\\\"],[\\\"taijung.chung\\\",\\\"PATRICK CHUNG\\\"],[\\\"tianyangma\\\",\\\"Ma Tianyang （馬天揚）\\\"],[\\\"w.ren\\\",\\\"wan ren （任婉）\\\"],[\\\"wangxiye\\\",\\\"Sheila Wang （王玺烨）\\\"],[\\\"wang_jjie\\\",\\\"junjie wang （王骏婕）\\\"],[\\\"wen_luo\\\",\\\"nova luo （罗文）\\\"],[\\\"wjyu\\\",\\\"Serene YU （俞文娟）\\\"],[\\\"xie_c\\\",\\\"Ashley Xie （谢晨）\\\"],[\\\"xinmoli\\\",\\\"Kerina Li （李昕默）\\\"],[\\\"xm.he\\\",\\\"Hesmond He （何旭明）\\\"],[\\\"xsli\\\",\\\"Isabella Li （李晓霜）\\\"],[\\\"yalilin\\\",\\\"Pearl Lin （林雅莉）\\\"],[\\\"yiminli\\\",\\\"yimin li （李一敏）\\\"],[\\\"yuri.misono\\\",\\\"Yuri Misono （御園 有里）\\\"],[\\\"yuying_huang\\\",\\\"Elixir Huang （黄玉莹）\\\"],[\\\"yuyushu\\\",\\\"yuyu shu （舒昱昱）\\\"],[\\\"zhcui\\\",\\\"zhenghua cui （崔正花）\\\"]]\",\"groupList\":[\"examinee\",\"examinee_display\"],\"metricList\":[]}"
//        def viewspotStr = "{\"result\":\"[[\\\"0\\\",\\\"unkwn\\\"],[\\\"100985972\\\",\\\"Kiztopia (Marina Square)\\\"],[\\\"104146685\\\",\\\"Big Fun Museum\\\"],[\\\"104148371\\\",\\\"Sands Expo & Convention Centre\\\"],[\\\"104153893\\\",\\\"Museum of Illusions\\\"],[\\\"107330861\\\",\\\"The Londoner Macao\\\"],[\\\"107420\\\",\\\"Desert Safari Camp\\\"],[\\\"107511\\\",\\\"卡帕多奇亚热气球\\\"],[\\\"107549\\\",\\\"Roppongi Hills\\\"],[\\\"107563\\\",\\\"Acropolis Museum\\\"],[\\\"107564\\\",\\\"River Thames\\\"],[\\\"107593\\\",\\\"Bali Safari and Marine Park\\\"],[\\\"107597\\\",\\\"Nusa Lembongan\\\"],[\\\"107614\\\",\\\"Calypso Cabaret\\\"],[\\\"107616\\\",\\\"Sea Life Bangkok Ocean World\\\"],[\\\"107617\\\",\\\"Sky100 Observation Deck\\\"],[\\\"107852\\\",\\\"Hueree Nature Life Park\\\"],[\\\"108080\\\",\\\"Marina Square\\\"],[\\\"108118\\\",\\\"Orchard Road\\\"],[\\\"108733450\\\",\\\"LEGOLAND® Discovery Centre Hong Kong\\\"],[\\\"109788\\\",\\\"Underwater World Langkawi\\\"],[\\\"109804\\\",\\\"Eco Green Campus [Daegwallyeong Samyang Ranch]\\\"],[\\\"109837\\\",\\\"Mabul Island\\\"],[\\\"109854\\\",\\\"Kapalai\\\"],[\\\"109872\\\",\\\"Similan Islands\\\"],[\\\"109876\\\",\\\"Tokyo Skytree\\\"],[\\\"109964\\\",\\\"The Queen's Gallery, Buckingham Palace\\\"],[\\\"109976\\\",\\\"London Zoo\\\"],[\\\"110100\\\",\\\"Pulau Dayang Bunting\\\"],[\\\"110180\\\",\\\"Chiangmai zoo\\\"],[\\\"110230\\\",\\\"Antelope Canyon\\\"],[\\\"110237\\\",\\\"Flam Train\\\"],[\\\"110284\\\",\\\"National Art Museum of Catalonia\\\"],[\\\"110314\\\",\\\"Asakusa\\\"],[\\\"110385\\\",\\\"Aquarium Barcelona\\\"],[\\\"110448\\\",\\\"Simon Cabaret Phuket\\\"],[\\\"110499\\\",\\\"Yasaka Shrine\\\"],[\\\"110540\\\",\\\"Amanohashidate\\\"],[\\\"113164\\\",\\\"The Peak Tram\\\"],[\\\"113165\\\",\\\"Star Ferry\\\"],[\\\"113372\\\",\\\"Peak Tower\\\"],[\\\"118481279\\\",\\\"Alangka Cruise\\\"],[\\\"11976\\\",\\\"Borghese Gallery and Museum\\\"],[\\\"12018\\\",\\\"Cathedral of Santa Maria del Fiore\\\"],[\\\"12023\\\",\\\"Uffizi Gallery\\\"],[\\\"12070\\\",\\\"Milan Cathedral\\\"],[\\\"12079\\\",\\\"Santa Maria delle Grazie\\\"],[\\\"121152026\\\",\\\"愤怒鸟游乐中心(澳门十六浦店)\\\"],[\\\"122816\\\",\\\"Gold Coast beach\\\"],[\\\"122861\\\",\\\"Fukuoka Tower\\\"]]\",\"groupList\":[\"viewspot_id\",\"viewspot_name_en\"],\"metricList\":[]}"
//        baseReportQueryServiceClient.getRawDataAsync(_) >>> [getFutureResponse(regionStr), getFutureResponse(provinceStr), getFutureResponse(countryStr), getFutureResponse(examineeStr), getFutureResponse(viewspotStr)]
//
//        and: "mock qconfig配置信息"
//        remoteConfig.getConfigValue("oversea") >> "海外"
//        remoteConfig.getConfigValue("region") >> "大区"
//        remoteConfig.getConfigValue("subRegion") >> "子区域"
//        def regionConfigStr = "{\"subMetricList\":[\"tklk\",\"tfly\"],\"field\":\"region\",\"baseInfoGroupList\":[\"region_id\",\"region_name\"],\"baseInfoLikeIndexList\":[1],\"lineGroupListMap\":{\"current\":[\"region_name\"]},\"tableGroupListMap\":{\"other\":[\"region_name\"]},\"needLine\":true,\"headerFieldList\":[\"region_name\"],\"conditionColumn\":\"region_id\",\"showField\":\"region_name\",\"showFieldId\":\"region_id\"}"
//        def provinceConfigStr = "{\"subMetricList\":[\"tklk\",\"tfly\"],\"field\":\"province\",\"baseInfoGroupList\":[\"province_id\",\"province_name\"],\"baseInfoLikeIndexList\":[1],\"lineGroupListMap\":{\"current\":[\"province_name\"]},\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\"]},\"needLine\":true,\"headerFieldList\":[\"region_name\",\"province_name\"],\"conditionColumn\":\"province_id\",\"showField\":\"province_name\",\"showFieldId\":\"province_id\"}"
//        def countryConfigStr = "{\"subMetricList\":[\"tklk\",\"tfly\"],\"field\":\"country\",\"baseInfoGroupList\":[\"country_id\",\"country_name\"],\"baseInfoLikeIndexList\":[1],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\",\"country_name\"]},\"needLine\":false,\"headerFieldList\":[\"region_name\",\"province_name\",\"country_name\"],\"conditionColumn\":\"country_id\"}"
//        def examineeConfigStr = "{\"subMetricList\":[\"tklk\",\"tfly\"],\"field\":\"examinee\",\"baseInfoGroupList\":[\"examinee_display\",\"examinee\"],\"baseInfoLikeIndexList\":[1],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\",\"examinee_display\"]},\"needLine\":false,\"headerFieldList\":[\"region_name\",\"province_name\",\"examinee_display\"],\"conditionColumn\":\"examinee\"}"
//
//
//        remoteConfig.getSubMetricFiledBean(_, _, "region") >> MapperUtil.str2Obj(regionConfigStr, SubMetricFiledBean.class)
//        remoteConfig.getSubMetricFiledBean(_, _, "province") >> MapperUtil.str2Obj(provinceConfigStr, SubMetricFiledBean.class)
//        remoteConfig.getSubMetricFiledBean(_, _, "country") >> MapperUtil.str2Obj(countryConfigStr, SubMetricFiledBean.class)
//        remoteConfig.getSubMetricFiledBean(_, _, "examinee") >> MapperUtil.str2Obj(examineeConfigStr, SubMetricFiledBean.class)
//
//
//        when: "调用获取下钻维度接口"
//        def response = competitorBaseStrategy.getBus105106107SubDrillDownBaseInfo(request, "2023-09-25", metricInfoBean)
//
//
//        then: "验证返回结果"
//        with(response) {
//            defaultChosenField == actualChosenField
//            if (fieldDataItemList) {
//                fieldDataItemList.size() == actualSize
//                fieldDataItemList.get(0).isNeedLine()
//            }
//        }
//
//        where: "用表格方式验证多种返回结果"
//        needSearch | searchWord | searchField | actualChosenField | actualSize | needLine
//        null       | null       | null        | "region"          | 4          | true
//        true       | "weddf"    | "region"    | null              | 1          | true
//
//
//    }


//    def "测试获取下钻数据接口"(){
//
//        given: "设置请求参数"
//        def request = new GetOverseaTableDataRequestType(metric: "105", subMetric: "tklk",
//                domainName: "leisun", timeFilter: new TimeFilter(year: "2023", dateType: "half", half: "H2"),
//                drillDownFilter: new DrillDownFilter(field: "region", fieldValueList: ["400"]), source: "detailpage",
//                pageNo: 1, pageSize: 10)
//        def metricInfoBean = new OverseaMetricInfoBean(metric: "105", destinationLevel: "海外")
//
//        and: "mock接口请求返回数据"
//        def responseStr = "{\"result\":\"[[\\\"海长大区\\\",44,0.5569620253164556],[\\\"东北亚\\\",30,0.4477611940298508],[\\\"东南亚\\\",28,0.3373493975903616],[\\\"港澳新\\\",14,0.2058823529411764],[\\\"欧洲\\\",10,0.1204819277108434]]\",\"groupList\":[\"region_name\"],\"metricList\":[\"cw_Inferiority_num\",\"cw_Inferiority_rate\"],\"totalNum\":5}"
//        baseReportQueryServiceClient.getRawData(_) >>> [getResponse(responseStr)]
//
//        and: "mock qconfig配置信息"
//        remoteConfig.getConfigValue("oversea") >> "海外"
//        remoteConfig.getConfigValue("region") >> "大区"
//        remoteConfig.getConfigValue("subRegion") >> "子区域"
//        def subMetricFieldBeanStr = "{\"subMetricList\":[\"tklk\",\"tfly\"],\"field\":\"region\",\"baseInfoGroupList\":[\"region_id\",\"region_name\"],\"baseInfoLikeIndexList\":[1],\"needLine\":true,\"lineGroupListMap\":{\"current\":[\"region_name\"]},\"conditionColumn\":\"region_id\",\"headerFieldList\":[\"region_name\"],\"tableGroupListMap\":{\"other\":[\"region_name\"]},\"showField\":\"region_name\",\"showFieldId\":\"region_id\"}"
//        remoteConfig.getSubMetricFiledBean(_, _, _) >> MapperUtil.str2Obj(subMetricFieldBeanStr, SubMetricFiledBean.class)
//
//        when: "调用获取下钻数据接口"
//        def response = competitorBaseStrategy.getBus105106107SubTableData(request, "2023-09-25", metricInfoBean)
//
//        then: "验证返回结果"
//        with(response){
//            totalNum == 5
//            tableHeaderList.size() == 5
//            showField == "region_name"
//            showFieldId == "region_id"
//        }
//
//
//
//
//    }


//    def getResponse(def str) {
//        return MapperUtil.str2Obj(str, GetRawDataResponseType.class)
//    }


//    def getFutureResponse(def str) {
//        def response = MapperUtil.str2Obj(str, GetRawDataResponseType.class)
//        return JdkFutureAdapters.listenInPoolThread(new AsyncResult<>(response))
//    }

//    def getTrendLineFutureResponse(def str) {
//        def response = MapperUtil.str2Obj(str, SinglePeriodDataBean.class)
//        return JdkFutureAdapters.listenInPoolThread(new AsyncResult<>(response))
//    }



}
