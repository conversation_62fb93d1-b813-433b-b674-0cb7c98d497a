package com.ctrip.tour.business.dashboard.tktBusiness.strategy.impl;

import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ExamineConfigBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.SinglePeriodDataBean;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.SinglePeriodTrendLineBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.Bus567Dao;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardExamineeConfigV2Dao;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.Bus567Helper;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.ChartHelper;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.MetricHelper;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.MultiPeriodMappingHelper;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


import java.util.*;
import java.util.concurrent.Future;


/**
 * 指标5 6 7的复合策略
 *
 * <AUTHOR>
 * @date 2022/8/2
 */
@Component
public class Bus567MetricStrategy {


    @Autowired
    private Bus567Dao dao;

    @Autowired
    private BusinessDashboardExamineeConfigV2Dao examineeConfigV2Dao;

    @Autowired
    private SinglePeriodTrendLineBiz singlePeriodTrendLineBiz;

    @Autowired
    private RemoteConfig remoteConfig;


    public MetricDetailInfo getSingleMetricCardData(String domainName,
                                                    TimeFilter timeFilter,
                                                    MetricInfoBean metricInfoBean,
                                                    String d,
                                                    Boolean needRank) throws Exception {

        MetricDetailInfo metricDetailInfo = new MetricDetailInfo();
        String metric = metricInfoBean.getMetric();
        metricDetailInfo.setMetric(metric);
        Map<String, Double> dimMap = new HashMap<>();
        metricDetailInfo.setDimData(dimMap);


        String originLevel = metricInfoBean.getLevel();
        String year = timeFilter.getYear();
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();
        String dateType = MetricHelper.getDateTypeColumnName(timeFilter.getDateType());
        List<String> timeList = DateUtil.getTimeList(dateType, month, quarter);

        DalHints rankDalHints = new DalHints().asyncExecution();
        if (needRank) {
            getRankDataAsync(dateType, year, quarter, month, d, domainName, metric, rankDalHints);
        }


        Map<String, List<String>> inMap = new HashMap<>();
        inMap.put("query_d", Lists.newArrayList(d));
        inMap.put("year", Lists.newArrayList(year));
        inMap.put(dateType, timeList);
        inMap.put("statistics_dim_id", Bus567Helper.getStatisticsDimIdList("withoutDrillDown", null));
        inMap.put("examine_level", Lists.newArrayList(originLevel));
        inMap.put("examinee", Lists.newArrayList(domainName));

        Integer additionalGapDay = Bus567Helper.getAddtionalGapDay(inMap, null, year, dateType, month, quarter, "", d);

        List<List<Object>> rawResultList = dao.getDataWithOutDrillDown(inMap, metric);
//        Integer gapDays = DateUtil.getOverallDaysOfMonthOrQuarter(year, dateType, month, quarter, d);
        Integer gapDays = dao.getGapDays("5,6,7", year, dateType, d, timeList);
        List<String> dimList = Bus567Helper.getDimList();
        ChartHelper.fillOverallDimMap(rawResultList, dimList, dimMap);
        //修正指标数据
        Bus567Helper.calMetricCardAverageData(dimMap, gapDays, additionalGapDay);
        Bus567Helper.makeUpMetricData(dimMap);

        MetricHelper.setMetricCardDrillDownInfo(metricInfoBean, metricDetailInfo);

        //获取排名数据
        if (needRank) {
            ChartHelper.fillRankData(metricDetailInfo, rankDalHints.getListResult());
        }

        return metricDetailInfo;
    }


    public GetTrendLineDataResponseType getSingleTrendlineData(GetTrendLineDataRequestType request,
                                                               MetricInfoBean metricInfoBean,
                                                               String d,
                                                               String metric) throws Exception {
        String queryType = request.getQueryType();
        if ("trendline".equals(queryType)) {
            return getSingleTrendlineDataWithoutDrillDown(request, metricInfoBean, d, metric);
        } else {
            return getSingleTrendlineDataWithDrillDown(request, metricInfoBean, d, metric);
        }
    }


    public GetTableDataResponseType getSingleTableData(GetTableDataRequestType request,
                                                       MetricInfoBean metricInfoBean,
                                                       String d) throws Exception {
        GetTableDataResponseType response = new GetTableDataResponseType();
        List<TableDataItem> tableDataItemList = new ArrayList<>();
        String metric = request.getMetric();
        String originLevel = metricInfoBean.getLevel();
        String mappingLevel = MetricHelper.getLevelColumnName(originLevel);
        TimeFilter timeFilter = request.getTimeFilter();
        String year = timeFilter.getYear();
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();
        String dateType = MetricHelper.getDateTypeColumnName(timeFilter.getDateType());
        List<String> timeList = DateUtil.getTimeList(dateType, month, quarter);
        List<String> regionList = metricInfoBean.getRegionList();
        List<String> bdList = metricInfoBean.getBdList();

        DrillDownFilter drillDownFilter = request.getDrillDownFilter();
        String field = MetricHelper.getDrillDownColumnName(drillDownFilter.getField());
        List<String> fieldValueList = drillDownFilter.getFieldValueList();

        //由于该指标的计算方式
        //所以需要单独拉取排序字段的值 做分页  每次根据分页参数取对应的6个
        //作为条件放入事实表的查询
        List<String> pagingGroupTagList = Lists.newArrayList(field);
        Map<String, List<String>> pagingMap = new LinkedHashMap<>();
        Map<String, List<String>> notInMap = new HashMap<>();
        pagingMap.put("query_d", Lists.newArrayList(d));
        pagingMap.put("year", Lists.newArrayList(year));
        pagingMap.put(dateType, timeList);
        if (!"".equals(mappingLevel)) {
            if ("examinee".equals(mappingLevel)) {
                pagingMap.put(mappingLevel, bdList);
            } else {
                pagingMap.put(mappingLevel, regionList);
            }
        }
        if (!GeneralUtil.isEmpty(fieldValueList)) {
            pagingMap.put(field, fieldValueList);
        }
        pagingMap.put("statistics_dim_id", Bus567Helper.getStatisticsDimIdList("drillDown", field));
        pagingMap.put("examine_level", Lists.newArrayList(MetricHelper.getFieldChineseName(field)));
        List<List<Object>> paingResult = dao.getFieldListNew(pagingMap, notInMap, new HashMap<>(), pagingGroupTagList, 1, 9999);
        Integer pageNo = request.getPageNo();
        Integer pageSize = request.getPageSize();
        if (GeneralUtil.isEmpty(pageNo)) {
            pageNo = 1;
        }
        if (GeneralUtil.isEmpty(pageSize)) {
            pageSize = 18;
        }
        List<String> pagingCondition = Bus567Helper.getPagingCondition(paingResult, pageNo, pageSize);
        Integer totalNum = Bus567Helper.getTotalNum(paingResult);
        response.setTotalNum(totalNum);

        //在第一页的时候 如果没有数据 直接返回
        //不进入下面的分页查询
        if (totalNum > 0) {
            Map<String, List<String>> inMap = new LinkedHashMap<>();
            inMap.put("query_d", Lists.newArrayList(d));
            inMap.put("year", Lists.newArrayList(year));
            inMap.put(dateType, timeList);
            if (!"".equals(mappingLevel)) {
                if ("examinee".equals(mappingLevel)) {
                    inMap.put(mappingLevel, bdList);
                } else {
                    inMap.put(mappingLevel, regionList);
                }
            }
            if (!GeneralUtil.isEmpty(fieldValueList)) {
                inMap.put(field, fieldValueList);
            }
            inMap.put(field, pagingCondition);
            inMap.put("statistics_dim_id", Bus567Helper.getStatisticsDimIdList("drillDown", field));
            inMap.put("examine_level", Lists.newArrayList(MetricHelper.getFieldChineseName(field)));
            List<String> groupTagList = MetricHelper.getTableDrillDownGroupList(field);
            Integer additionalGapDay = Bus567Helper.getAddtionalGapDay(inMap, null, year, dateType, month, quarter, "", d);
            List<List<Object>> rawResultList = dao.getDataWithDrillDown(inMap, groupTagList,metric);
            List<String> dimList = Bus567Helper.getDimList();
            ChartHelper.fillCommmonTableData(tableDataItemList, groupTagList, dimList,
                    new ArrayList<>(), rawResultList, new ArrayList<>());
//            Integer gapDays = DateUtil.getOverallDaysOfMonthOrQuarter(year, dateType, month, quarter, d);

            Integer gapDays = dao.getGapDays("5,6,7", year, dateType, d, timeList);
            Bus567Helper.calTableAverageData(tableDataItemList, gapDays, additionalGapDay);
            Bus567Helper.makeUpTableData(tableDataItemList);
            String source = request.getSource();
            if ("firstpage".equals(source)) {
                //首页数据不变形
                response.setTableDataItemList(tableDataItemList);
                response.setTotalNum(response.getTotalNum() / 3);
            } else {
                response.setTableDataItemList(Bus567Helper.convertTableDataItemList(tableDataItemList));
            }
        }
        return response;
    }


    public GetDrillDownBaseInfoResponseType getSingleDrillDownBaseInfo(GetDrillDownBaseInfoRequestType request,
                                                                       MetricInfoBean metricInfoBean,
                                                                       String d) throws Exception {
        GetDrillDownBaseInfoResponseType response = new GetDrillDownBaseInfoResponseType();
        List<FieldDataItem> fieldDataItemList = new ArrayList<>();
        response.setFieldDataItemList(fieldDataItemList);

        TimeFilter timeFilter = request.getTimeFilter();
        String year = timeFilter.getYear();
        String dateType = timeFilter.getDateType();
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();

        String metric = metricInfoBean.getMetric();
        List<String> regionList = metricInfoBean.getRegionList();
        List<String> bdList = metricInfoBean.getBdList();
        String originLevel = metricInfoBean.getLevel();//国内  三方  大区  省份  景点
        String mappingLevel = MetricHelper.getLevelColumnName(originLevel);

        boolean needSearch = request.isNeedSearch();
        String searchWord = request.getSearchWord();
        List<String> fieldList = Bus567Helper.getFieldList(originLevel);

        if (needSearch) {
            String searchField = request.getSearchField();//大区 省份 商拓

            if (searchField != null) {
                String searchFieldColumnName = MetricHelper.getDrillDownColumnName(searchField);
                fieldList = Lists.newArrayList(searchFieldColumnName);
            }
        } else {
            response.setDefaultChosenField(MetricHelper.getDefaultChosenField(originLevel, metric));
        }
        Map<String, List<String>> inMap = new LinkedHashMap<>();
        Map<String, List<String>> notInMap = new HashMap<>();
        inMap.put("query_d", Lists.newArrayList(d));
        inMap.put("year", Lists.newArrayList(year));
        inMap.put(dateType, DateUtil.getTimeList(dateType, month, quarter));
        if (!"".equals(mappingLevel)) {
            if ("examinee".equals(mappingLevel)) {
                inMap.put(mappingLevel, bdList);
            } else {
                inMap.put(mappingLevel, regionList);
            }
        }
        for (String field : fieldList) {
            Map<String, List<String>> fieldInMap = new HashMap<>(inMap);
            fieldInMap.put("statistics_dim_id", Bus567Helper.getStatisticsDimIdList("drillDown", field));
            fieldInMap.put("examine_level", Lists.newArrayList(MetricHelper.getFieldChineseName(field)));

            Integer pageNo = null;
            Integer pageSize = null;
            if ("examinee".equals(field)) {
                //默认拉取field  限制下POI和商拓获取的数量
                if (!needSearch) {
                    pageNo = 1;
                    pageSize = 50;
                }
            }
            List<String> tagList = Lists.newArrayList(field);
            Map<String, String> likeMap = new HashMap<>();
            if (needSearch) {
                likeMap.put(field, searchWord);
            }
            List<List<Object>> rawObjectList = dao.getFieldListNew(fieldInMap, notInMap, likeMap, tagList, pageNo, pageSize);
            ChartHelper.fillFieldDataItemList(field, rawObjectList, fieldDataItemList);
        }
        return response;
    }

    private void getRankDataAsync(String dateType,
                                  String year,
                                  String quarter,
                                  String month,
                                  String d,
                                  String domainName,
                                  String metric,
                                  DalHints dalHints) throws Exception {
        Map<String, List<String>> inMap = new LinkedHashMap<>();
        inMap.put("query_d", Lists.newArrayList(d));
        inMap.put("examinee", Lists.newArrayList(domainName));
        inMap.put("year", Lists.newArrayList(year));
        if ("month".equals(dateType)) {
            inMap.put("month", Lists.newArrayList(month));
        } else {
            String mappingMonth = DateUtil.getMappingMonthByQuarter(d, year, quarter);
            inMap.put("month", Lists.newArrayList(mappingMonth));
        }
        inMap.put("metric", Lists.newArrayList(metric));
        dao.getRankAsync(inMap, dalHints);
    }

    private GetTrendLineDataResponseType getSingleTrendlineDataWithoutDrillDown(GetTrendLineDataRequestType request,
                                                                                MetricInfoBean metricInfoBean,
                                                                                String d,
                                                                                String metric) throws Exception {
        GetTrendLineDataResponseType response = new GetTrendLineDataResponseType();
        List<TrendLineDetailInfo> trendLineDetailInfoList = new ArrayList<>();
        response.setTrendLineDetailInfoList(trendLineDetailInfoList);

        TimeFilter timeFilter = request.getTimeFilter();
        String domainName = request.getDomainName();

        List<ExamineConfigBean> examineConfigBeanList = MultiPeriodMappingHelper.getMultiPeriodConfigList(timeFilter, d, examineeConfigV2Dao.queryMetricAllConfig(domainName, d, metric),null);
        List<Future<SinglePeriodDataBean>> futureList = new ArrayList<>();
        for (ExamineConfigBean examineConfigBean : examineConfigBeanList) {
            futureList.add(singlePeriodTrendLineBiz.getBus567SinglePeriodTrendLineData(request, examineConfigBean, d, metric));
        }
        List<List<Object>> weaknessList = new ArrayList<>();
        for (Future<SinglePeriodDataBean> futureResult : futureList) {
            SinglePeriodDataBean singlePeriodDataBean = futureResult.get();
            List<List<Object>> periodWeaknessList = singlePeriodDataBean.getPeriodWeaknessList();
            if (!GeneralUtil.isEmpty(periodWeaknessList)) {
                weaknessList.addAll(periodWeaknessList);
            }
        }

        Map<String, Double> dimMap = new HashMap<>();
        Set<String> drillDownSet = new HashSet<>();
        List<String> dimList = Bus567Helper.getDimList();
        ChartHelper.setMonthlyMetricTrendlineActualReach(dimMap, drillDownSet, weaknessList, Lists.newArrayList("time"), dimList);
        //有效时间范围
        List<String> timeList = DateUtil.getAllSelectedTime(timeFilter, "domestic");
        ChartHelper.fillLineChartTrendLineData(null, timeList, dimMap, trendLineDetailInfoList, Bus567Helper.getRatioTrendlineType());
        Bus567Helper.makeUpTrendlineData(trendLineDetailInfoList);
        return response;
    }

    private GetTrendLineDataResponseType getSingleTrendlineDataWithDrillDown(GetTrendLineDataRequestType request,
                                                                             MetricInfoBean metricInfoBean,
                                                                             String d,
                                                                             String metric) throws Exception {
        GetTrendLineDataResponseType response = new GetTrendLineDataResponseType();
        List<TrendLineDetailInfo> trendLineDetailInfoList = new ArrayList<>();
        response.setTrendLineDetailInfoList(trendLineDetailInfoList);

        TimeFilter timeFilter = request.getTimeFilter();

        DrillDownFilter drillDownFilter = request.getDrillDownFilter();
        String field = MetricHelper.getDrillDownColumnName(drillDownFilter.getField());
        String domainName = request.getDomainName();

        List<ExamineConfigBean> examineConfigBeanList = MultiPeriodMappingHelper.getMultiPeriodConfigList(timeFilter, d, examineeConfigV2Dao.queryMetricAllConfig(domainName, d, metric),null);
        List<Future<SinglePeriodDataBean>> futureList = new ArrayList<>();
        for (ExamineConfigBean examineConfigBean : examineConfigBeanList) {
            futureList.add(singlePeriodTrendLineBiz.getBus567SinglePeriodTrendLineData(request, examineConfigBean, d, metric));
        }
        List<List<Object>> weaknessList = new ArrayList<>();
        for (Future<SinglePeriodDataBean> futureResult : futureList) {
            SinglePeriodDataBean singlePeriodDataBean = futureResult.get();
            List<List<Object>> periodWeaknessList = singlePeriodDataBean.getPeriodWeaknessList();
            if (!GeneralUtil.isEmpty(periodWeaknessList)) {
                weaknessList.addAll(periodWeaknessList);
            }

        }
        Map<String, Double> dimMap = new HashMap<>();
        Set<String> drillDownSet = new HashSet<>();
        List<String> dimList = Bus567Helper.getDimList();
        ChartHelper.setMonthlyMetricTrendlineActualReach(dimMap, drillDownSet, weaknessList, Lists.newArrayList("time", field), dimList);

        //有效时间范围
        List<String> timeList = DateUtil.getAllSelectedTime(timeFilter, "domestic");
        ChartHelper.fillLineChartTrendLineDataWithDrillDown(null, timeList, dimMap,
                trendLineDetailInfoList, Bus567Helper.getRatioTrendlineType(), drillDownSet, false);
        return response;
    }
}
