package com.ctrip.tour.business.dashboard.grpBusiness.enums;

import com.ctrip.tour.business.dashboard.grpBusiness.common.GrpConstant;

/**
 * <AUTHOR>
 * @Date 2024/12/18
 */
public enum MetricDimEnum {

    INCOME(GrpConstant.INCOME, "dep_date","adm_ord_grp_work_platform_prdt_df"),
    SELF_OPR_INCOME(GrpConstant.SELF_OPR_INCOME, "dep_date","adm_ord_grp_work_platform_prdt_df"),
    SELF_OPR_INCOME_RATIO(GrpConstant.SELF_OPR_INCOME_RATIO, "dep_date","adm_ord_grp_work_platform_prdt_df"),
    PROFIT_PRICE(GrpConstant.PROFIT_PRICE, "dep_date","adm_ord_grp_work_platform_prdt_df"),
    SELF_OPR_PROFIT(GrpConstant.SELF_OPR_PROFIT, "dep_date","adm_ord_grp_work_platform_prdt_df"),
    SELF_OPR_PROFIT_RATE(GrpConstant.SELF_OPR_PROFIT_RATE, "dep_date","adm_ord_grp_work_platform_prdt_df"),
    SELF_OPR_PROFIT_RATIO(GrpConstant.SELF_OPR_PROFIT_RATIO, "dep_date","adm_ord_grp_work_platform_prdt_df"),

    UV(GrpConstant.UV, "partition_d","edw_log_grp_cpr_platform_flow_cr_di"),
    CONVERSION_RATE(GrpConstant.CONVERSION_RATE, "partition_d","edw_log_grp_cpr_platform_flow_cr_di"),
    SELF_OPR_CONVERSION_RATE(GrpConstant.SELF_OPR_CONVERSION_RATE, "partition_d","edw_log_grp_cpr_platform_flow_cr_di"),
    AGENT_CONVERSION_RATE(GrpConstant.AGENT_CONVERSION_RATE, "partition_d","edw_log_grp_cpr_platform_flow_cr_di"),

    MULTIPLE_PRICE(GrpConstant.MULTIPLE_PRICE, "view_date","adm_prd_grp_multiple_price_work_platform_df"),
    MULTIPLE_PRICE_MORE_RATE(GrpConstant.MULTIPLE_PRICE_MORE_RATE, "view_date","adm_prd_grp_multiple_price_work_platform_df"),
    MULTIPLE_PRICE_LESS_RATE(GrpConstant.MULTIPLE_PRICE_LESS_RATE, "view_date","adm_prd_grp_multiple_price_work_platform_df"),

    SELF_SERVICE_COVERAGE_RATE(GrpConstant.SELF_SERVICE_COVERAGE_RATE, "chat_create_date","cdm_sev_grp_cpr_platform_self_srv_cr_df"),
    SELF_SERVICE_CONVERSION_RATE(GrpConstant.SELF_SERVICE_CONVERSION_RATE, "chat_create_date","cdm_sev_grp_cpr_platform_self_srv_cr_df"),

    FITTING_NPS(GrpConstant.FITTING_NPS, "return_date","cdm_sev_grp_cpr_platform_fitting_nps_df"),
    RECOMMENDATION_RATE(GrpConstant.RECOMMENDATION_RATE, "return_date","cdm_sev_grp_cpr_platform_fitting_nps_df"),
    SLANDER_RATE(GrpConstant.SLANDER_RATE, "return_date","cdm_sev_grp_cpr_platform_fitting_nps_df"),
    ORDER_COVERAGE_RATE(GrpConstant.ORDER_COVERAGE_RATE, "return_date","cdm_sev_grp_cpr_platform_fitting_nps_df"),

    WEIGHTED_PLATFORM_INFO_SCORE(GrpConstant.WEIGHTED_PLATFORM_INFO_SCORE, "etl_date","adm_prd_grp_product_score_df"),
    WEIGHTED_PRD_INFO_SCORE(GrpConstant.WEIGHTED_PRD_INFO_SCORE, "etl_date","adm_prd_grp_product_score_df"),

    SELF_OPR_SINGLE_UV_VAL_PRD(GrpConstant.SELF_OPR_SINGLE_UV_VAL_PRD, "dep_date","adm_ord_grp_work_platform_prdt_df"),
    AGENT_SINGLE_UV_VAL_PRD(GrpConstant.AGENT_SINGLE_UV_VAL_PRD, "dep_date","adm_ord_grp_work_platform_prdt_df"),

    HIGH_QUA_VENDOR_COUNT_WITH_SELFOPRPRD(GrpConstant.HIGH_QUA_VENDOR_COUNT_WITH_SELFOPRPRD, "etl_date","adm_prd_grp_product_total_work_platform_df"),
    HIGH_QUA_VENDOR_COUNT_WITH_SELFOPR_PRD_FAILTOMEET(GrpConstant.HIGH_QUA_VENDOR_COUNT_WITH_SELFOPR_PRD_FAILTOMEET, "etl_date","adm_prd_grp_product_total_work_platform_df"),

    SELF_OPR_PARENT_PRD_COUNT(GrpConstant.SELF_OPR_PARENT_PRD_COUNT, "etl_date","adm_prd_grp_product_total_work_platform_df"),
    ACTIVE_SELF_OPR_PARENT_PRD_COUNT(GrpConstant.ACTIVE_SELF_OPR_PARENT_PRD_COUNT, "etl_date","adm_prd_grp_product_total_work_platform_df"),
    ACTIVE_SELF_OPR_PARENT_PRD_RATE(GrpConstant.ACTIVE_SELF_OPR_PARENT_PRD_RATE, "etl_date","adm_prd_grp_product_total_work_platform_df"),
    SRV_FREQUENCY_INLST30D(GrpConstant.SRV_FREQUENCY_INLST30D, "etl_date","adm_prd_grp_product_total_work_platform_df"),

    GRP_GUIDER_CHECKIN_RATE(GrpConstant.GRP_GUIDER_CHECKIN_RATE, "dep_date","adm_ord_grp_work_platform_prdt_df"),
    GRP_GUIDER_ACTUAL_DISPACTH_RATE(GrpConstant.GRP_GUIDER_ACTUAL_DISPACTH_RATE, "dep_date","adm_ord_grp_work_platform_prdt_df"),
    GRP_GUIDER_DISPATCH_RATE(GrpConstant.GRP_GUIDER_DISPATCH_RATE, "dep_date","adm_ord_grp_work_platform_prdt_df"),
    GRP_DRIVER_CHECKIN_RATE(GrpConstant.GRP_DRIVER_CHECKIN_RATE, "dep_date","adm_ord_grp_work_platform_prdt_df"),
    GRP_DRIVER_DISPATCH_RATE(GrpConstant.GRP_DRIVER_DISPATCH_RATE, "dep_date","adm_ord_grp_work_platform_prdt_df"),
    GRP_DRIVER_EXECUTION_RATE(GrpConstant.GRP_DRIVER_EXECUTION_RATE, "dep_date","adm_ord_grp_work_platform_prdt_df");

    private String metricName;
    private String calcDateName;
    private String tableName;

    public static MetricDimEnum getMetricDimEnumByMetricName(String metricName) {
        for (MetricDimEnum metricDimEnum : MetricDimEnum.values()) {
            if (metricDimEnum.getMetricName().equals(metricName)) {
                return metricDimEnum;
            }
        }
        return null;
    }


    MetricDimEnum(String metricName, String calcDateName, String tableName) {
        this.metricName = metricName;
        this.calcDateName = calcDateName;
        this.tableName = tableName;
    }

    public String getMetricName() {
        return metricName;
    }

    public void setMetricName(String metricName) {
        this.metricName = metricName;
    }

    public String getCalcDateName() {
        return calcDateName;
    }

    public void setCalcDateName(String calcDateName) {
        this.calcDateName = calcDateName;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }
}
