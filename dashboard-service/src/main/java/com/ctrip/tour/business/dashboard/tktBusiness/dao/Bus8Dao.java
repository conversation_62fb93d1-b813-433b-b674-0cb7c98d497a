package com.ctrip.tour.business.dashboard.tktBusiness.dao;

import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.StatementParameters;


import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.ctrip.tour.business.dashboard.utils.SqlUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/29
 */
@Repository
public class Bus8Dao {


    @Autowired
    private GeneralDao generalDao;


    //获取选定周期进展 目标
    public List<List<Object>> getMetricCardData(Map<String, List<String>> inMap) throws SQLException {
        StringBuilder sb = new StringBuilder();
        sb.append("select sum(ttd_sale1_stk_res_cnt) as ttd_sale1_stk_res_cnt,sum(ttd_trgt_activity) as ttd_trgt_activity ");
        sb.append(" from bus_8_activity_cover_info_t_new ");
        StatementParameters parameters = new StatementParameters();
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        return generalDao.getListResult(sb.toString(), parameters);
    }


    //按月或者季度拆分 返回当年进展 目标
    public List<List<Object>> getTrendlineData(Map<String, List<String>> inMap,
                                               List<String> groupTagList) throws SQLException {
        StringBuilder sb = new StringBuilder();
        StatementParameters parameters = new StatementParameters();
        sb.append("select ");
        if(!GeneralUtil.isEmpty(groupTagList)){
            SqlUtil.jointGroupCondition(sb, false, groupTagList);
            sb.append(",");
        }
        sb.append("sum(ttd_sale1_stk_res_cnt) as ttd_sale1_stk_res_cnt,sum(ttd_trgt_activity) as ttd_trgt_activity ");
        sb.append(" from bus_8_activity_cover_info_t_new ");
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        if(!GeneralUtil.isEmpty(groupTagList)){
            SqlUtil.jointGroupCondition(sb, true, groupTagList);
        }
        return generalDao.getListResult(sb.toString(), parameters);
    }




    //获取某月或者某季数据进展 目标
    //按照下钻维度排序
    public List<List<Object>> getTableData(Map<String, List<String>> inMap,
                                           List<String> tagList,
                                           Integer pageNo,
                                           Integer pageSize) throws SQLException {
        StringBuilder sb = new StringBuilder();
        StatementParameters parameters = new StatementParameters();
        sb.append("select ");
        SqlUtil.jointGroupCondition(sb, false, tagList);
        sb.append(",sum(ttd_sale1_stk_res_cnt) as ttd_sale1_stk_res_cnt,sum(ttd_trgt_activity) as ttd_trgt_activity ");
        sb.append(" from bus_8_activity_cover_info_t_new ");
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        SqlUtil.jointGroupCondition(sb, true, tagList);
        SqlUtil.jointOrderCondition(sb, tagList, "asc");
        SqlUtil.jointPagingCondition(sb, parameters, pageNo, pageSize);
        return generalDao.getListResult(sb.toString(), parameters);
    }

    //获取表格数据总条数
    public Integer getTableDataCount(Map<String, List<String>> inMap,
                                     List<String> tagList) throws SQLException {
        StringBuilder sb = new StringBuilder();
        StatementParameters parameters = new StatementParameters();
        sb.append("select count(*) from ( select ");
        SqlUtil.jointGroupCondition(sb, false, tagList);
        sb.append(",sum(ttd_sale1_stk_res_cnt) as ttd_sale1_stk_res_cnt,sum(ttd_trgt_activity) as ttd_trgt_activity ");
        sb.append(" from bus_8_activity_cover_info_t_new ");
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        SqlUtil.jointGroupCondition(sb, true, tagList);
        SqlUtil.jointOrderCondition(sb, tagList, "asc");
        sb.append(" ) aa");
        List<List<Object>> resultList = generalDao.getListResult(sb.toString(), parameters);
        return Integer.valueOf(String.valueOf(resultList.get(0).get(0)));
    }


    public List<List<Object>> getFieldList(Map<String, List<String>> inMap,
                                           Map<String, String> likeMap,
                                           List<String> tagList) throws SQLException {
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        SqlUtil.jointGroupCondition(sb, false, tagList);
        sb.append(" from bus_8_activity_cover_info_t_new ");
        StatementParameters parameters = new StatementParameters();
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        if (!GeneralUtil.isEmpty(likeMap)) {
            SqlUtil.jointLikeCondition(sb, parameters, likeMap, true);
        }
        SqlUtil.jointGroupCondition(sb, true, tagList);
        return generalDao.getListResult(sb.toString(), parameters);
    }


    public void getRankAsync(Map<String, List<String>> inMap,
                             DalHints dalHints) throws SQLException {
        StringBuilder sb = new StringBuilder();
        sb.append("select ranking from bus_4_8_9_10_rank_t ");
        StatementParameters parameters = new StatementParameters();
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        generalDao.getListResultAsync(sb.toString(), parameters, dalHints);
    }




}
