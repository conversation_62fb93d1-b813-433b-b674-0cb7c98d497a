package com.ctrip.tour.business.dashboard.tktBusiness.enums;

public enum ChannelSiteEnum {
    CHANNEL("渠道"),//NOSONAR
    SITE("站点");//NOSONAR

    private String name;

    ChannelSiteEnum(String name) {
        this.name = name;
    }


    public String getName() {
        return name;
    }

    public static ChannelSiteEnum getByCode(String code) {
        for (ChannelSiteEnum buType : ChannelSiteEnum.values()) {
            if (buType.getName().equals(code)) {
                return buType;
            }
        }
        return null;
    }
}
