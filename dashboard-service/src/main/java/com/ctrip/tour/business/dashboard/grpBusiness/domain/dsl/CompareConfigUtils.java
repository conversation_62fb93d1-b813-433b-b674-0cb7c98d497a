package com.ctrip.tour.business.dashboard.grpBusiness.domain.dsl;

import com.ctrip.soa._24922.CompareConfig;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import org.joda.time.DateTime;
import org.joda.time.Days;

import java.util.ArrayList;
import java.util.Arrays;

@Getter
@Setter
public class CompareConfigUtils {
    @JsonIgnore
    public static CompareConfig getYearTB(String start, String end, String indicatorName) {
        DateTime startTime = new DateTime(start);
        DateTime endTime = new DateTime(end);
        DateTime compareStartTime = startTime.plusYears(-1);
        DateTime compareEndTime = endTime.plusYears(-1);

        ArrayList<String> days = new ArrayList<>(Arrays.asList(compareStartTime.toString("yyyy-MM-dd"), compareEndTime.toString("yyyy-MM-dd")));
        CompareConfig compareConfig = new CompareConfig();
        compareConfig.setCompareDate(days);
        compareConfig.setPrefix("year_tb");
        compareConfig.setCompareIndicatorName(indicatorName);
        compareConfig.setCurDate(Arrays.asList(start, end));
        return compareConfig;
    }

    @JsonIgnore
    public static CompareConfig getMonthTB(String start, String end, String indicatorName) {
        DateTime startTime = new DateTime(start);
        DateTime endTime = new DateTime(end);
        DateTime compareStartTime = startTime.plusMonths(-1);
        DateTime compareEndTime = endTime.plusMonths(-1);

        ArrayList<String> days = new ArrayList<>(Arrays.asList(compareStartTime.toString("yyyy-MM-dd"), compareEndTime.toString("yyyy-MM-dd")));
        CompareConfig compareConfig = new CompareConfig();
        compareConfig.setCompareDate(days);
        compareConfig.setPrefix("month_tb");
        compareConfig.setCompareIndicatorName(indicatorName);
        compareConfig.setCurDate(Arrays.asList(start, end));
        return compareConfig;
    }

    @JsonIgnore
    public static CompareConfig getHBWithPrefix(String start, String end, String indicatorName, String prefix) {
        DateTime startTime = new DateTime(start);
        DateTime endTime = new DateTime(end);
        DateTime compareEndTime = startTime.plusDays(-1);
        DateTime compareStartTime = compareEndTime.plusDays(-Days.daysBetween(startTime, endTime).getDays());

        ArrayList<String> days = new ArrayList<>(Arrays.asList(compareStartTime.toString("yyyy-MM-dd"), compareEndTime.toString("yyyy-MM-dd")));
        CompareConfig compareConfig = new CompareConfig();
        compareConfig.setCompareDate(days);
        compareConfig.setPrefix(prefix);
        compareConfig.setCompareIndicatorName(indicatorName);
        compareConfig.setCurDate(Arrays.asList(start, end));
        return compareConfig;
    }

    @JsonIgnore
    public static CompareConfig getHB(String start, String end, String indicatorName) {
        return getHBWithPrefix(start, end, indicatorName, "hb");
    }

}
