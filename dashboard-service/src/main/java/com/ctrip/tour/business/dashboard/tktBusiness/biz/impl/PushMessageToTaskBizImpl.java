package com.ctrip.tour.business.dashboard.tktBusiness.biz.impl;


import com.ctrip.tour.business.dashboard.tktBusiness.bean.*;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.PushMessageToTaskBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.enums.FileTypeEnum;
import com.ctrip.tour.business.dashboard.utils.*;
import com.ctrip.tour.rights.client.TaskPropertyType;
import com.ctrip.tour.rights.client.TourRightsServiceClient;
import com.ctrip.tour.rights.client.WbCreateTaskRequestType;
import com.ctrip.tour.rights.client.WbCreateTaskResponseType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class PushMessageToTaskBizImpl implements PushMessageToTaskBiz {
    @Autowired
    private TourRightsServiceClient pushClient;

    @Autowired
    private RemoteConfig remoteConfig;

    private Logger log = LoggerFactory.getLogger(PushMessageToTaskBizImpl.class);

    /**
     * 数据处理，根据header里的映射关系，通过反射 解析对应的结果
     * @param headers
     * @return
     */
    public List<List<String>> object2List(Map<String, List<String>> headers, List<Object> messageList) throws Exception {
        List<List<String>> messageResult = new ArrayList<>();
        for (Object message : messageList) {
            List<String> oneList = new ArrayList<>();
            for (String header : headers.keySet()) {
                List<String> vList = headers.get(header);
                List<String> valueList = new ArrayList<>();
                for (String v : vList) {
                    Field field = message.getClass().getDeclaredField(v);
                    field.setAccessible(true); // 需要设置访问权限为可访问
                    valueList.add(String.valueOf(field.get(message)));
                }
                oneList.add(String.join("_", valueList));
            }
            messageResult.add(oneList);
        }
        return messageResult;
    }

    /**
     * 根据配置中需要替换占位符的枚举值，进行分类计算
     *
     * @param templateList
     * @param messageList
     * @return
     */
    public Map<String, String> getTemplateMap(List<String> templateList, List<Object> messageList) throws NoSuchFieldException, IllegalAccessException {
        Map<String, String> templateMap = new HashMap<>();
        if(templateList != null && templateList.size() > 0){
            for (String t : templateList) {
                if (t.equalsIgnoreCase("count")) {
                    templateMap.put(t, String.valueOf(messageList.size()));
                }
                if(t.equalsIgnoreCase("product[ID+Name]")){
                    // 此场景 都是messageList 只会是一条数据
                    Field idField = messageList.get(0).getClass().getDeclaredField("productId");
                    idField.setAccessible(true);
                    Field nameField = messageList.get(0).getClass().getDeclaredField("productName");
                    nameField.setAccessible(true);
                    String productId = String.valueOf(idField.get(messageList.get(0)));
                    String productName = String.valueOf(nameField.get(messageList.get(0)));
                    templateMap.put(t, productId + "_" + productName);
                }
                if(t.equalsIgnoreCase("changeTimeLimit")){
                    // 此场景 都是messageList 只会是一条数据
                    Field field = messageList.get(0).getClass().getDeclaredField("rectificationDeadline");
                    field.setAccessible(true);
                    templateMap.put(t, String.valueOf(field.get(messageList.get(0))));
                }
                if(t.equalsIgnoreCase("rejectReason")){
                    // 此场景 都是messageList 只会是一条数据
                    Field field = messageList.get(0).getClass().getDeclaredField("rejectReason");
                    field.setAccessible(true);
                    templateMap.put(t, String.valueOf(field.get(messageList.get(0))));
                }
                if(t.equalsIgnoreCase("vendor[ID+Name]")){
                    // 此场景 都是messageList 只会是一条数据
                    Field idField = messageList.get(0).getClass().getDeclaredField("vendorId");
                    idField.setAccessible(true);
                    Field nameField = messageList.get(0).getClass().getDeclaredField("vendorName");
                    nameField.setAccessible(true);
                    String vendorId = String.valueOf(idField.get(messageList.get(0)));
                    String vendorName = String.valueOf(nameField.get(messageList.get(0)));
                    templateMap.put(t, vendorId + "_" + vendorName);
                }
            }
        }
        return templateMap;
    }

    /**
     * 获取模板字符串，如果有占位符，则替换为内容
     *
     * @param serviceInfo
     * @param templateMap
     * @return
     */
    public String getTemplate(ContractEventContentSceneBean serviceInfo, Map<String, String> templateMap) {
        String templateStr = serviceInfo.getTemplateStr();
        boolean isPlace = serviceInfo.isIsplace();
        if (isPlace) {
            List<String> templateList = serviceInfo.getTemplateList();
            List<String> templateResultList = new ArrayList<>();
            for (String t : templateList) {
                templateResultList.add(templateMap.get(t));
            }
            return String.format(templateStr, templateResultList.toArray());
        }
        return templateStr;
    }


    /**
     * 构造消息展示内容，适用于所有场景
     *
     * @param serviceInfo
     * @param messageList
     * @return
     * @throws Exception
     */
    public String getContent(ContractEventContentSceneBean serviceInfo, List<Object> messageList) throws Exception {
        if (serviceInfo != null) {
            Map<String, String> templateMap = getTemplateMap(serviceInfo.getTemplateList(), messageList);
            if (serviceInfo.getContentType() == 1) {
                // 文本
                return getTemplate(serviceInfo, templateMap);
            } else if (serviceInfo.getContentType() == 2) {
                // 文本和表格
                String templateStr = getTemplate(serviceInfo, templateMap);
                List<List<String>> mList = object2List(serviceInfo.getHeaders(), messageList);
                List<String> headers = serviceInfo.getHeaders().keySet().stream().collect(Collectors.toList());
                return PushMessageUtil.getPushMessageHtml(templateStr, mList, headers, serviceInfo.getLength(), serviceInfo.getHyperLinks());
            }
        }
        return null;

    }

    /**
     * 构造附件：
     * 1. 判断是否需要附件
     * 2. 构造附件
     * 3. 上传附件生成附件链接
     *
     * @param sceneBean
     * @param messageList
     * @return: 返回附件地址
     */
    public List<PushMessageFileBean> getAndUploadAttachment(ContractEventSceneBean sceneBean, List<Object> messageList) throws Exception {
        List<PushMessageFileBean> fileList = new ArrayList<>();
        ContractEventContentSceneBean serviceInfo = sceneBean.getDetailInfo();
        if (serviceInfo.getContentType() == 2) {
            Map<String, String> templateMap = getTemplateMap(serviceInfo.getTemplateList(), messageList);
            String templateStr = getTemplate(serviceInfo, templateMap);

            List<List<String>> mList = object2List(serviceInfo.getHeaders(), messageList);
            List<String> headers = serviceInfo.getHeaders().keySet().stream().collect(Collectors.toList());
            // 判断是否超长，超长则构造附件
            boolean isOver = PushMessageUtil.isOverLength(templateStr, mList, headers, serviceInfo.getLength(), serviceInfo.getHyperLinks());
            if (!isOver) {
                // 生成文件再上传的方式
                // String path = ExcelUtil.writeExcel(remoteConfig.getConfigValue("basePath"), sceneBean.getTitle(), headers, mList);
                // byte[] byteArr = ExcelUtil.readExcel(path);
                // 直接生成文件的byte数组
                byte[] byteArr = ExcelUtil.getExcelStream(sceneBean.getTitle(), headers, mList);

                UploadResult uploadResult = FileServerUtil.upload(byteArr, FileTypeEnum.XLSX, remoteConfig.getConfigValue("fileChannel"), remoteConfig.getConfigValue("hostName"),null);

                PushMessageFileBean fileBean = new PushMessageFileBean();
                fileBean.setMimeType(FileTypeEnum.XLSX.getPushType());
                fileBean.setFileName(sceneBean.getTitle() + ".xlsx");
                fileBean.setFilePath(uploadResult.getFileUrl());
                // fileBean.setFileId();
                // 删除文件
                fileList.add(fileBean);
                // FileUtil.deleteFile(path);
            }
        }
        return fileList;
    }

    /**
     * 获取服务号展示内容; 参考逻辑：
     * http://conf.ctripcorp.com/pages/viewpage.action?pageId=1110538132
     * http://conf.ctripcorp.com/pages/viewpage.action?pageId=1041712480
     *
     * @param sceneBean
     * @param messageList
     * @return
     */
    public PushMessageServiceNumberTrippalInfoBean getServiceNumShowInfo(ContractEventSceneBean sceneBean, List<Object> messageList) throws Exception {
        PushMessageServiceNumberTrippalInfoBean trippalInfoBean = new PushMessageServiceNumberTrippalInfoBean();

        ContractEventContentSceneBean serviceInfo = sceneBean.getServiceNumInfo();

        trippalInfoBean.setTitle(sceneBean.getTitle());
        List<PushMessageServiceNumberTrippalInfoContentBean> trippalInfoContentBeans = new ArrayList<>();
        String content = getContent(serviceInfo, messageList);
        List<String> cList = new ArrayList<>();
        if(content.contains("<br/><br/>")){
            cList.addAll(Arrays.asList(content.split("<br/><br/>")));
        }else{
            cList.add(content);
        }
        for (String c : cList) {
            PushMessageServiceNumberTrippalInfoContentBean trippalInfoContentBean = new PushMessageServiceNumberTrippalInfoContentBean();
            trippalInfoContentBean.setIdx(0);
            trippalInfoContentBean.setType(1);
            trippalInfoContentBean.setTag("p");
            PushMessageServiceNumberTrippalInfoContentBean trippalInfoContentChildBean = new PushMessageServiceNumberTrippalInfoContentBean();
            trippalInfoContentChildBean.setIdx(0);
            trippalInfoContentChildBean.setType(0);
            trippalInfoContentChildBean.setText(c);
            trippalInfoContentBean.setChildren(Collections.singletonList(trippalInfoContentChildBean));
            trippalInfoContentBeans.add(trippalInfoContentBean);
        }

        trippalInfoBean.setContent(trippalInfoContentBeans);
        return trippalInfoBean;
    }


    /**
     * 同步情况下对push任务结果处理
     *
     * @param request
     * @param response
     */
    public void handleResponse(WbCreateTaskRequestType request, WbCreateTaskResponseType response) {
        Long taskId = response.getTaskId();
        if (taskId == null) {
            log.warn("scene taskId is null, response: " + MapperUtil.obj2Str(response) + "; request: " + MapperUtil.obj2Str(request));
            System.out.println("scene send fail...");
        } else {
            log.info("scene taskId=" + taskId);
            String sceneType = request.getTaskPropertyList().get(0).getKey();
            List<PushMessageBean> r = MapperUtil.str2List(request.getTaskPropertyList().get(0).getValue(), PushMessageBean.class);
            System.out.println("scene send success, taskId: " + taskId + "; receiver: " + r.get(0).getProcessoreIdList() + "; sceneType: " + sceneType);
        }
    }

    /**
     * 获取消息包含的所有产品id和供应商id，作为属性传值
     * @param messageList
     * @return
     * @throws NoSuchFieldException
     * @throws IllegalAccessException
     */
    public Map<String, String> getAllPropertyId(List<Object> messageList) throws NoSuchFieldException, IllegalAccessException {
        Map<String, String> propertyMap = new HashMap<>();
        Field pidField = messageList.get(0).getClass().getDeclaredField("productId");
        pidField.setAccessible(true);

        Field vidField = messageList.get(0).getClass().getDeclaredField("vendorId");
        vidField.setAccessible(true);
        List<String> pList = new ArrayList<>();
        List<String> vList = new ArrayList<>();
        for (Object o : messageList) {
            pList.add(String.valueOf(pidField.get(o)));
            vList.add(String.valueOf(vidField.get(o)));
        }
        propertyMap.put("productId", String.join(",", pList));
        propertyMap.put("vendorId", String.join(",", vList));
        return propertyMap;

    }

    /**
     * 推送消息到消息系统
     *
     * @param receiver：接收人
     * @param messageList：消息列表
     * @param sceneBean：场景对象
     */
    @Override
    public void pushMessage(String receiver, List<Object> messageList, ContractEventSceneBean sceneBean) throws Exception {
        if(Boolean.parseBoolean(remoteConfig.getConfigValue("isPushSceneMessage"))){
            log.info("start scene push message, receiver: " + receiver);
            // 服务号展示内容
            PushMessageServiceNumberTrippalInfoBean trippalInfoBean = getServiceNumShowInfo(sceneBean, messageList);
            // 详情页展示内容
            String detailContent = getContent(sceneBean.getDetailInfo(), messageList);
            List<PushMessageFileBean> fileList = getAndUploadAttachment(sceneBean, messageList);

            // 构造发送内容
            PushMessageBean pushMessageBean = new PushMessageBean();
            pushMessageBean.setProcessoreIdList(receiver);
            pushMessageBean.setContent(detailContent);
            pushMessageBean.setListFiles(fileList);
            pushMessageBean.setTrippalInfo(trippalInfoBean);

            List<PushMessageBean> messageBeanList = new ArrayList<>();
            messageBeanList.add(pushMessageBean);

            // push 请求体构造
            WbCreateTaskRequestType request = new WbCreateTaskRequestType();
            request.setTaskType(sceneBean.getTaskType());
            request.setTaskName(sceneBean.getTaskName());
            List<TaskPropertyType> taskPropertyList = new ArrayList<>();
            TaskPropertyType taskProperty = new TaskPropertyType();
            taskProperty.setKey(sceneBean.getEventType());
            taskProperty.setValue(MapperUtil.obj2Str(messageBeanList));
            taskPropertyList.add(taskProperty);
            if(sceneBean.getMessageType() == 1 || sceneBean.getMessageType() == 3){
                TaskPropertyType resourceProperty = new TaskPropertyType();
                resourceProperty.setKey("RESOURCE_ID");
                resourceProperty.setValue("99999999");
                taskPropertyList.add(resourceProperty);
            }
            Map<String, String> propertyList = getAllPropertyId(messageList);
            taskPropertyList.add( new TaskPropertyType("PRODUCT_ID", propertyList.get("productId")));
            taskPropertyList.add( new TaskPropertyType("PROVIDER_ID", propertyList.get("vendorId")));
            request.setTaskPropertyList(taskPropertyList);
            try {
                WbCreateTaskResponseType response = pushClient.wbCreateTask(request);
                handleResponse(request, response);
            } catch (Exception e) {
                log.error("business Dashboard push message is error: " + e.getMessage());
            }
            log.info("stop scene push message, receiver: " + receiver);
        }else{
            log.info("don't start PushSceneMessage");
        }

    }
}
