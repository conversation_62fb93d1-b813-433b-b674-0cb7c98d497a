package com.ctrip.tour.business.dashboard.tktBusiness.biz;

import com.ctrip.soa._24922.*;

public interface OverseaMetricDataBiz {


    GetOverseaMetricCardDataResponseType getOverseaMetricCardData(GetOverseaMetricCardDataRequestType request) throws Exception;

    GetOverseaMetricCardDataV2ResponseType getOverseaMetricCardDataV2(GetOverseaMetricCardDataV2RequestType request) throws Exception;

    GetOverseaTrendLineDataResponseType getOverseaTrendLineData(GetOverseaTrendLineDataRequestType request) throws Exception;

    GetOverseaTrendLineDataV2ResponseType getOverseaTrendLineDataV2(GetOverseaTrendLineDataV2RequestType request) throws Exception;

    GetOverseaDrillDownBaseInfoResponseType getOverseaDrillDownBaseInfo(GetOverseaDrillDownBaseInfoRequestType request) throws Exception;

    GetOverseaDrillDownBaseInfoV2ResponseType getOverseaDrillDownBaseInfoV2(GetOverseaDrillDownBaseInfoV2RequestType request) throws Exception;

    GetOverseaTableDataResponseType getOverseaTableData(GetOverseaTableDataRequestType request) throws Exception;

    GetOverseaTableDataV2ResponseType getOverseaTableDataV2(GetOverseaTableDataV2RequestType request) throws Exception;
}
