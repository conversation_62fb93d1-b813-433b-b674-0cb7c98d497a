package com.ctrip.tour.business.dashboard.tktBusiness.enums;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public enum BuTypeEnum {
    ALL(0, "全部"),//NOSONAR
    TICKET(1, "门票"),//NOSONAR
    ACTIVITY(2, "玩乐"),//NOSONAR
    ACTIVITY_ACT(3, "活动"),//NOSONAR
    ACTIVITY_DAY(4, "日游");//NOSONAR

    private int code;
    private String name;

    BuTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static BuTypeEnum getByCode(int code) {
        for (BuTypeEnum buType : BuTypeEnum.values()) {
            if (buType.getCode() == code) {
                return buType;
            }
        }
        return ALL;
    }

    public static List<String> getCTByByType(int code) {
        if (code == 0) {
            return Arrays.asList(BuTypeEnum.TICKET.getName(), BuTypeEnum.ACTIVITY.getName());
        }
        List<String> result = new ArrayList<>();
        for (BuTypeEnum buType : BuTypeEnum.values()) {
            if (buType.getCode() == code) {
                result.add(buType.getName());
            }
        }
        return result;
    }

    public static List<String> getBuTypeList(Integer code,String tkt,String act,String odt) {
        List<String> result = new ArrayList<>();
        if (BuTypeEnum.TICKET.getCode() == code) {
            result.add(tkt);
        }
        if (BuTypeEnum.ACTIVITY.getCode() == code) {
            result.add(act);
            result.add(odt);
        }
        if (BuTypeEnum.ALL.getCode() == code) {
            result.add(act);
            result.add(odt);
            result.add(tkt);
        }
        return result;
    }


}
