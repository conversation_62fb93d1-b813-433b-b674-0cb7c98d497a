package com.ctrip.tour.business.dashboard.utils.time;

import lombok.AllArgsConstructor;
import lombok.Data;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.joda.time.format.DateTimeFormat;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 半年计算器类，用于处理半年相关的日期计算、转换及比较
 * 格式示例：2025H1(2025年1-6月)、2025H2(2025年7-12月)
 */
@Data
@AllArgsConstructor
public class HalfYearCalculator implements Cloneable, Comparable<HalfYearCalculator> {
    private Integer year;
    private Integer halfYear; // 1表示上半年(1-6月)，2表示下半年(7-12月)

    /**
     * 默认构造方法，初始化当前半年
     */
    public HalfYearCalculator() {
        DateTime now = DateTime.now();
        year = now.getYear();
        halfYear = now.getMonthOfYear() <= 6 ? 1 : 2;
    }

    /**
     * 根据半年字符串创建HalfYearCalculator实例
     *
     * @param halfYearString 半年字符串，格式：yyyyH1或yyyyH2
     * @return HalfYearCalculator实例
     */
    public static HalfYearCalculator getByHalfYearString(String halfYearString) {
        HalfYearCalculator halfYearCalculator = new HalfYearCalculator();
        halfYearCalculator.setHalfYearString(halfYearString);
        return halfYearCalculator;
    }

    /**
     * 获取两个半年之间的所有半年列表
     *
     * @param start        起始半年
     * @param end          结束半年
     * @param includeStart 是否包含起始半年
     * @param includeEnd   是否包含结束半年
     * @return 半年列表（按时间升序排列）
     */
    public static List<HalfYearCalculator> getBetween(HalfYearCalculator start, HalfYearCalculator end, boolean includeStart, boolean includeEnd) {
        List<HalfYearCalculator> result = new ArrayList<>();

        while (end.compareTo(start) >= 0) {
            result.add(end);
            end = end.getLastHalfYear();
        }

        Collections.reverse(result);

        if (!includeStart) {
            result.remove(0);
        }
        if (!includeEnd && result.size() > 0) {
            result.remove(result.size() - 1);
        }
        return result;
    }

    /**
     * 根据日期字符串创建HalfYearCalculator实例（截取年月部分）
     *
     * @param date 日期字符串，格式：yyyy-MM-dd
     * @return HalfYearCalculator实例
     */
    public static HalfYearCalculator getByDate(String date) {
        DateTime dateTime = DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(date);
        int halfYear = dateTime.getMonthOfYear() <= 6 ? 1 : 2;
        return new HalfYearCalculator(dateTime.getYear(), halfYear);
    }

    /**
     * 获取当前半年的HalfYearCalculator实例
     *
     * @return 当前半年实例
     */
    public static HalfYearCalculator getCurrentHalfYear() {
        return new HalfYearCalculator();
    }

    /**
     * 获取半年字符串（格式：yyyyH1或yyyyH2）
     *
     * @return 半年字符串，若年或半年为null则返回null
     */
    public String getHalfYearString() {
        if (year != null && halfYear != null) {
            return year + "H" + halfYear;
        } else {
            return null;
        }
    }

    /**
     * 设置半年字符串（支持格式：yyyyH1、yyyyh1、yyyyH2或yyyyh2）
     *
     * @param halfYearString 半年字符串
     */
    public void setHalfYearString(String halfYearString) {
        try {
            String[] hs = null;
            // 同时支持大写H和小写h作为分隔符
            if (halfYearString.contains("H")) {
                hs = halfYearString.split("H");
            } else if (halfYearString.contains("h")) {
                hs = halfYearString.split("h");
            } else {
                throw new IllegalArgumentException("Invalid half-year expression"); // 不合法的半年表达 → Invalid half-year expression
            }

            this.year = Integer.parseInt(hs[0]);
            this.halfYear = Integer.parseInt(hs[1]);
            Assert.isTrue(halfYear > 0 && halfYear < 3, "Invalid half-year parameter!"); // 不合法的半年参数！ → Invalid half-year parameter!
        } catch (Exception e) {
            throw new IllegalArgumentException("Incorrect parameter:" + halfYearString, e); // 不正确的参数: → Incorrect parameter:
        }
    }

    /**
     * 获取半年的第一天（格式：yyyy-MM-dd）
     *
     * @return 半年第一天字符串
     */
    public String getDateStart() {
        int startMonth = halfYear == 1 ? 1 : 7;
        return new DateTime(year, startMonth, 1, 0, 0).toString("yyyy-MM-dd");
    }

    /**
     * 获取半年的最后一天（格式：yyyy-MM-dd）
     *
     * @return 半年最后一天字符串
     */
    public String getDateEnd() {
        if (halfYear == 1) {
            return new DateTime(year, 6, 1, 0, 0).dayOfMonth().withMaximumValue().toString("yyyy-MM-dd");
        } else {
            return new DateTime(year, 12, 1, 0, 0).dayOfMonth().withMaximumValue().toString("yyyy-MM-dd");
        }
    }

    /**
     * 获取半年的天数
     *
     * @return 半年总天数
     */
    public Integer getDayCount() {
        DateTime startDate = DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(getDateStart());
        DateTime endDate = DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(getDateEnd());
        return Days.daysBetween(startDate, endDate).getDays() + 1;
    }

    /**
     * 获取半年最后一天（若半年未结束则返回今天）
     *
     * @return 调整后的半年最后一天字符串
     */
    public String getDateEndWithoutFuture() {
        String dateEnd = getDateEnd();
        DateTime now = DateTime.now();
        if (DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(dateEnd).isAfter(now)) {
            return now.toString("yyyy-MM-dd");
        } else {
            return dateEnd;
        }
    }

    /**
     * 根据对比半年的周期长度获取当前半年的对应结束日期
     *
     * @param needCompareHalfYear 对比半年
     * @return 计算后的结束日期字符串
     */
    public String getDateEndWithSamePeriod(HalfYearCalculator needCompareHalfYear) {
        DateTime now = DateTime.now();
        String dateEnd = needCompareHalfYear.getDateEnd();
        if (DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(dateEnd).isBefore(now)) {
            String dateStart = needCompareHalfYear.getDateStart();
            int days = Days.daysBetween(
                    DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(dateStart),
                    DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(dateEnd)
            ).getDays();
            return DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(getDateStart()).plusDays(days).toString("yyyy-MM-dd");
        } else {
            return getDateEnd();
        }
    }

    /**
     * 获取上一个半年的HalfYearCalculator实例
     *
     * @return 上一个半年实例
     */
    public HalfYearCalculator getLastHalfYear() {
        HalfYearCalculator clone = this.clone();
        if (clone.halfYear == 1) {
            clone.year -= 1;
            clone.halfYear = 2;
        } else {
            clone.halfYear = 1;
        }

        return clone;
    }

    /**
     * 获取下一个半年的HalfYearCalculator实例
     *
     * @return 下一个半年实例
     */
    public HalfYearCalculator getNextHalfYear() {
        HalfYearCalculator clone = this.clone();
        if (clone.halfYear == 2) {
            clone.year += 1;
            clone.halfYear = 1;
        } else {
            clone.halfYear = 2;
        }

        return clone;
    }

    /**
     * 获取去年同期半年的HalfYearCalculator实例
     *
     * @return 去年同期半年实例
     */
    public HalfYearCalculator getLastYearSameHalfYear() {
        HalfYearCalculator clone = this.clone();
        clone.year -= 1;
        return clone;
    }

    /**
     * 获取同比日期范围（不考虑时间窗口概念）
     *
     * @return 同比日期范围
     */
    public DateRange getYoYDateRange() {
        HalfYearCalculator lastYearSameHalfYear = getLastYearSameHalfYear();
        return new DateRange(lastYearSameHalfYear.getDateStart(), lastYearSameHalfYear.getDateEnd());
    }

    /**
     * 获取同比日期范围（考虑时间窗口概念）
     *
     * @param windowDate 窗口日期，格式：yyyy-MM-dd
     * @return 对齐时间窗口的同比日期范围
     */
    public DateRange getYoYDateRangeWithWindow(String windowDate) {
        HalfYearCalculator lastYearSameHalfYear = getLastYearSameHalfYear();
        DateTime windowDateTime = DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(windowDate);
        DateTime currentHalfYearEnd = DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(getDateEnd());

        // 如果窗口日期在当前半年结束后，返回完整半年
        if (windowDateTime.isAfter(currentHalfYearEnd)) {
            return new DateRange(lastYearSameHalfYear.getDateStart(), lastYearSameHalfYear.getDateEnd());
        }

        // 计算当前半年已过去的天数
        DateTime currentHalfYearStart = DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(getDateStart());
        int daysPassed = Days.daysBetween(currentHalfYearStart, windowDateTime).getDays() + 1;

        // 计算同比半年对应的结束日期
        DateTime yoyStartDate = DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(lastYearSameHalfYear.getDateStart());
        DateTime yoyEndDate = yoyStartDate.plusDays(daysPassed - 1);

        // 处理天数溢出情况：确保不超过目标半年的最后一天
        DateTime targetHalfYearEnd = DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(lastYearSameHalfYear.getDateEnd());
        if (yoyEndDate.isAfter(targetHalfYearEnd)) {
            yoyEndDate = targetHalfYearEnd;
        }

        return new DateRange(lastYearSameHalfYear.getDateStart(), yoyEndDate.toString("yyyy-MM-dd"));
    }


    /**
     * 获取环比日期范围（不考虑时间窗口概念）
     *
     * @return 环比日期范围
     */
    public DateRange getHoHDateRange() {
        HalfYearCalculator lastHalfYear = getLastHalfYear();
        return new DateRange(lastHalfYear.getDateStart(), lastHalfYear.getDateEnd());
    }


    /**
     * 获取环比日期范围（考虑时间窗口概念）
     *
     * @param windowDate 窗口日期，格式：yyyy-MM-dd
     * @return 对齐时间窗口的环比日期范围
     */
    public DateRange getHoHDateRangeWithWindow(String windowDate) {
        HalfYearCalculator lastHalfYear = getLastHalfYear();
        DateTime windowDateTime = DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(windowDate);
        DateTime currentHalfYearEnd = DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(getDateEnd());

        // 如果窗口日期在当前半年结束后，返回完整半年
        if (windowDateTime.isAfter(currentHalfYearEnd)) {
            return new DateRange(lastHalfYear.getDateStart(), lastHalfYear.getDateEnd());
        }

        // 计算当前半年已过去的天数
        DateTime currentHalfYearStart = DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(getDateStart());
        int daysPassed = Days.daysBetween(currentHalfYearStart, windowDateTime).getDays() + 1;

        // 计算环比半年对应的结束日期
        DateTime hoStartDate = DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(lastHalfYear.getDateStart());
        DateTime hoEndDate = hoStartDate.plusDays(daysPassed - 1);

        // 处理天数溢出情况：确保不超过目标半年的最后一天
        DateTime targetHalfYearEnd = DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(lastHalfYear.getDateEnd());
        if (hoEndDate.isAfter(targetHalfYearEnd)) {
            hoEndDate = targetHalfYearEnd;
        }

        return new DateRange(lastHalfYear.getDateStart(), hoEndDate.toString("yyyy-MM-dd"));
    }



    /**
     * 克隆当前实例
     *
     * @return 克隆后的HalfYearCalculator实例
     */
    @Override
    protected HalfYearCalculator clone() {
        return new HalfYearCalculator(year, halfYear);
    }

    /**
     * 比较两个HalfYearCalculator实例（按年升序，年相同则按半年升序）
     *
     * @param o 待比较的实例
     * @return 比较结果（-1：当前实例小，0：相等，1：当前实例大）
     */
    @Override
    public int compareTo(HalfYearCalculator o) {
        int yearCompare = this.year - o.getYear();
        if (yearCompare == 0) {
            return this.halfYear - o.getHalfYear();
        } else {
            return yearCompare;
        }
    }

    /**
     * 判断两个HalfYearCalculator实例是否相等（基于年和半年）
     *
     * @param o 待比较对象
     * @return 是否相等
     */
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof HalfYearCalculator)) return false;

        HalfYearCalculator that = (HalfYearCalculator) o;

        if (getYear() != null ? !getYear().equals(that.getYear()) : that.getYear() != null) return false;
        return getHalfYear() != null ? getHalfYear().equals(that.getHalfYear()) : that.getHalfYear() == null;
    }

    /**
     * 计算哈希码（基于年和半年）
     *
     * @return 哈希码
     */
    @Override
    public int hashCode() {
        int result = getYear() != null ? getYear().hashCode() : 0;
        result = 31 * result + (getHalfYear() != null ? getHalfYear().hashCode() : 0);
        return result;
    }

    /**
     * 转换为字符串（返回半年字符串）
     *
     * @return 半年字符串（格式：yyyyH1或yyyyH2）
     */
    @Override
    public String toString() {
        return getHalfYearString();
    }
}

