package com.ctrip.tour.business.dashboard.sightArchives.dao.overseametric;

import com.ctrip.soa._27181.PreparedParameterBean;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.CdmOrdTtdOverseasPerformanceIndexBO;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.bean.OverseasPerformanceInfoParamBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseasRelatedSearchParamBean;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.TktStarRocksDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.sql.Types;
import java.util.*;
import java.util.ArrayList;
import java.util.stream.Collectors;

@Repository
@Slf4j
public class CdmOrdTtdOverseasPerformanceIndexDao {

    @Autowired
    private TktStarRocksDao tktStarRocksDao;

    /**
     * 获取海外业绩考核业绩统计信息
     *
     * @return
     */
    public Double queryOverseasPerformanceInfo(OverseasRelatedSearchParamBean searchParamBean) {
        StringBuilder sql = new StringBuilder("select");
        if (searchParamBean.getExamineMetricType() == null) {
            return 0.0;
        }
        switch (searchParamBean.getExamineMetricType()) {
            case "101":
                sql.append(" sum(ifnull(ttd_ord_suc_gmv,0)) as metricData");
                break;
            case "102":
                sql.append(" sum(ifnull(ttd_ord_suc_profit,0)) as metricData");
                break;
            case "110":
                sql.append(" sum(ifnull(ttd_ord_suc_qty,0)) as metricData");
                break;
            default:
                return 0.0;
        }
        sql.append(" from cdm_ord_ttd_overseas_performance_index_df where 1 = 1");
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendQuarter(parameters, sql, searchParamBean.getQuarter());
        appendYear(parameters, sql, searchParamBean.getYear());
        Set<String> levelSet = new HashSet<>(searchParamBean.getExamineLevel());
        if (levelSet.contains("渠道") || levelSet.contains("站点")) {//NOSONAR
            searchParamBean.setBuTypeNames(null);
        }
        appendBuType(parameters, sql, searchParamBean.getBuTypeNames());
        appendExamineLevels(parameters, sql, searchParamBean.getExamineLevel());
        appendDomainName(parameters, sql, searchParamBean.getDomainName());
        appendCT(parameters, sql, searchParamBean.getCt());
        appendTimeRange(parameters, sql, searchParamBean.getStartDate(), searchParamBean.getEndDate());
        appendD(parameters, sql, searchParamBean.getD());

        List<Map<String, Object>> result = new ArrayList<>();
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            log.warn("queryOverseasPerformanceInfo error", e);
        }
        if (!CollectionUtils.isEmpty(result)) {
            return result.stream()
                    .map(bean -> bean.get("metricData"))
                    .filter(Objects::nonNull)
                    .map(value -> Double.parseDouble(value.toString()))
                    .findFirst()
                    .orElse(0.0);
        }
        return 0.00;
    }

    //拼季度
    public void appendQuarter(List<PreparedParameterBean> parameters, StringBuilder sql, String quarter) {
        if (quarter != null) {
            sql.append(" and use_quarter=?");
            parameters.add(new PreparedParameterBean(quarter, Types.VARCHAR));
        }
    }

    //拼季度
    public void appendYear(List<PreparedParameterBean> parameters, StringBuilder sql, String year) {
        if (year != null) {
            sql.append(" and use_year=?");
            parameters.add(new PreparedParameterBean(year, Types.VARCHAR));
        }
    }

    //拼业务线
    private void appendBuType(List<PreparedParameterBean> parameters, StringBuilder sql, List<String> buTypeNames) {
        if (CollectionUtils.isNotEmpty(buTypeNames)) {
            sql.append(" and bu_type_name in (");
            for (int i = 0; i < buTypeNames.size(); i++) {
                if (i == 0) {
                    sql.append("?");
                } else {
                    sql.append(",?");
                }
                parameters.add(new PreparedParameterBean(buTypeNames.get(i), Types.VARCHAR));
            }
            sql.append(")");
        }
    }

    //拼考核层级
    private void appendExamineLevels(List<PreparedParameterBean> parameters, StringBuilder sql, List<String> destinationLevels) {
        if (CollectionUtils.isNotEmpty(destinationLevels)) {
            sql.append(" and examine_level in (");
            for (int i = 0; i < destinationLevels.size(); i++) {
                if (i == 0) {
                    sql.append("?");
                } else {
                    sql.append(",?");
                }
                parameters.add(new PreparedParameterBean(destinationLevels.get(i), Types.VARCHAR));
            }
            sql.append(")");
        }
    }

    //拼邮箱前缀
    public void appendDomainName(List<PreparedParameterBean> parameters, StringBuilder sql, String domainName) {
        if (domainName != null) {
            sql.append(" and domain_name=?");
            parameters.add(new PreparedParameterBean(domainName, Types.VARCHAR));
        }
    }

    //拼季度
    public void appendCT(List<PreparedParameterBean> parameters, StringBuilder sql, String ct) {
        if (ct != null) {
            if ("c".equals(ct)){
                ct = "C站";//NOSONAR
            }else{
                ct = "T站";//NOSONAR
            }
            sql.append(" and ct=?");
            parameters.add(new PreparedParameterBean(ct, Types.VARCHAR));
        }
    }

    //拼时间
    public void appendTimeRange(List<PreparedParameterBean> parameters, StringBuilder sql, String beginTime, String endTime) {
        if (beginTime != null) {
            sql.append(" and use_date >= ?");
            parameters.add(new PreparedParameterBean(beginTime, Types.VARCHAR));
        }
        if (endTime != null) {
            sql.append(" and use_date <= ?");
            parameters.add(new PreparedParameterBean(endTime, Types.VARCHAR));
        }
    }

    // 拼时间
    public void appendD(List<PreparedParameterBean> parameters, StringBuilder sql, String d) {
        if (d != null) {
            sql.append(" and d=?");
            parameters.add(new PreparedParameterBean(d, Types.VARCHAR));
        }
    }


    public List<CdmOrdTtdOverseasPerformanceIndexBO> getOverseasProductionResponseInfo(OverseasPerformanceInfoParamBean param) {
        StringBuilder sql = new StringBuilder("select sum(ifnull(ttd_ord_suc_gmv,0)) as ttd_ord_suc_gmv, sum(ifnull(ttd_ord_suc_profit,0)) as ttd_ord_suc_profit,sum(ifnull(ttd_ord_suc_qty,0)) as ttd_ord_suc_qty, " +
                " business_region_name,business_sub_region_name,business_region_name_en,business_sub_region_name_en,domain_name from cdm_ord_ttd_overseas_performance_index_df where domain_name!='unkwn' and  d=?  " +
                "and use_year=? " );

        List<PreparedParameterBean> parameters = new java.util.ArrayList<>();
        buildParameters(sql,parameters,param);
        sql.append(" group by business_region_name,business_sub_region_name,domain_name,business_region_name_en,business_sub_region_name_en  ");
        buildOrderParameters(sql,parameters,param);

        List<Map<String, Object>> result = new ArrayList<>();
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            log.warn("queryOverseaPersonInfo error", e);
        }
        List<CdmOrdTtdOverseasPerformanceIndexBO> CdmOrdTtdOverseasPerformanceIndexBOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(result)) {
            CdmOrdTtdOverseasPerformanceIndexBOList = result.stream()
                    .map(bean -> {
                        CdmOrdTtdOverseasPerformanceIndexBO performanceIndexBO = new CdmOrdTtdOverseasPerformanceIndexBO();
                        performanceIndexBO.setGmv((Double) bean.get("ttd_ord_suc_gmv"));
                        performanceIndexBO.setProfit((Double) bean.get("ttd_ord_suc_profit"));
                        performanceIndexBO.setQty(((Long) bean.get("ttd_ord_suc_qty")).doubleValue());

                        performanceIndexBO.setBuRegionNames((String) bean.get("business_region_name"));
                        performanceIndexBO.setBuSubRegionNames((String) bean.get("business_sub_region_name"));
                        performanceIndexBO.setCtryName((String) bean.get("dep_ctry_name"));
                        performanceIndexBO.setProvName((String) bean.get("dep_prov_name"));
                        performanceIndexBO.setCityName((String) bean.get("dep_city_name"));
                        performanceIndexBO.setDomainName((String) bean.get("domain_name"));
                        performanceIndexBO.setBuRegionNameEn((String) bean.get("business_region_name_en"));
                        performanceIndexBO.setBuSubRegionNameEn((String) bean.get("business_sub_region_name_en"));
                        performanceIndexBO.setCtryNameEn((String) bean.get("dep_ctry_name_en"));
                        performanceIndexBO.setProvNameEn((String) bean.get("dep_prov_name_en"));
                        performanceIndexBO.setCityNameEn((String) bean.get("dep_city_name_en"));
                        performanceIndexBO.setDomainName((String) bean.get("domain_name"));
                        performanceIndexBO.setVstId((Long) bean.get("vst_id"));
                        performanceIndexBO.setVstName((String) bean.get("vst_name"));
                        performanceIndexBO.setVstNameEn((String) bean.get("vst_name_en"));
                        performanceIndexBO.setSites((String) bean.get("site"));
                        performanceIndexBO.setLocale((String) bean.get("locale"));
                        performanceIndexBO.setDisChannelName((String) bean.get("dis_channel_name"));
                        return performanceIndexBO;
                    })
                    .collect(Collectors.toList());
        }
        return CdmOrdTtdOverseasPerformanceIndexBOList;
    }

    private void buildOrderParameters(StringBuilder sql, List<PreparedParameterBean> parameters, OverseasPerformanceInfoParamBean param) {
        if (param.getPageIndex() == null || param.getPageSize() == null) {
            return;
        }
        if (CollectionUtils.isNotEmpty(param.getBuRegionNames())) {
            sql.append(" order by business_region_name ");
        } else if (CollectionUtils.isNotEmpty(param.getBuSubRegionNames())) {
            sql.append(" order by business_sub_region_name ");
        }else {
            sql.append(" order by domain_name ");
        }
        if (param.getPageIndex() != null && param.getPageSize() != null) {
            sql.append(" limit ?,? ");
            parameters.add(new PreparedParameterBean(String.valueOf((param.getPageIndex() - 1) * param.getPageSize()), Types.INTEGER));
            parameters.add(new PreparedParameterBean(String.valueOf(param.getPageSize()), Types.INTEGER));
        }
    }

    public void buildParameters(StringBuilder sql,List<PreparedParameterBean> parameters,OverseasPerformanceInfoParamBean param){
        parameters.add(new PreparedParameterBean(param.getD(), Types.VARCHAR));
        parameters.add(new PreparedParameterBean(param.getYear(), Types.VARCHAR));
        if (StringUtils.isNotEmpty(param.getExamineLevel())) {
            sql.append(" and examine_level =? ");
            parameters.add(new PreparedParameterBean(param.getExamineLevel(), Types.VARCHAR));
        }

        if (StringUtils.isNotEmpty(param.getStartDate())) {
            sql.append(" and use_date >= ? ");
            parameters.add(new PreparedParameterBean(param.getStartDate(), Types.VARCHAR));
        }

        if (StringUtils.isNotEmpty(param.getEndDate())) {
            sql.append(" and use_date <= ? ");
            parameters.add(new PreparedParameterBean(param.getEndDate(), Types.VARCHAR));
        }

        if (StringUtils.isNotEmpty(param.getQuarter())) {
            sql.append(" and use_quarter =? ");
            parameters.add(new PreparedParameterBean(param.getQuarter(), Types.VARCHAR));
        }
        if (StringUtils.isNotEmpty(param.getHalfYear())) {
            sql.append(" and use_half_year =? ");
            parameters.add(new PreparedParameterBean(param.getHalfYear(), Types.VARCHAR));
        }
        appendSql(sql," and bu_type_name in ( ",parameters,param.getBuTypeName());
        appendSql(sql," and ct in ( ",parameters,param.getCt());
        if (CollectionUtils.isNotEmpty(param.getDomainNames())) {
            appendSql(sql," and domain_name in ( ",parameters,param.getDomainNames());
        }

        if (CollectionUtils.isNotEmpty(param.getBuRegionNames())) {
            appendSql(sql, " and business_region_name in ( ", parameters, param.getBuRegionNames());
        }
        if (CollectionUtils.isNotEmpty(param.getBuSubRegionNames())) {
            appendSql(sql, " and business_sub_region_name in ( ", parameters, param.getBuSubRegionNames());
        }
    }

    private void appendSql(StringBuilder sql, String sqlAppend, List<PreparedParameterBean> parameters, List<String> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            sql.append(sqlAppend);
            for (int i = 0; i < list.size(); i++) {
                if (i == 0) {
                    sql.append("?");
                } else {
                    sql.append(",?");
                }
                parameters.add(new PreparedParameterBean(list.get(i), Types.VARCHAR));
            }
            sql.append(")");
        }
    }

    public Integer getOverseasProductionResponseInfoCount(OverseasPerformanceInfoParamBean param) {
        StringBuilder sql = new StringBuilder("select count(distinct business_region_name,business_sub_region_name,domain_name,business_region_name_en,business_sub_region_name_en) as totalNum " +
                "from cdm_ord_ttd_overseas_performance_index_df where domain_name!='unkwn' and  d=?  " +
                "and use_year=? ");

        List<PreparedParameterBean> parameters = new java.util.ArrayList<>();
        buildParameters(sql, parameters, param);
        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(),parameters);
        } catch (SQLException e) {
            log.warn("queryUncoveredTicketTypeTotalNum error", e);
        }
        return CollectionUtils.isNotEmpty(result)? Math.toIntExact((Long) result.get(0).getOrDefault("totalNum","0")) :0;
    }


}
