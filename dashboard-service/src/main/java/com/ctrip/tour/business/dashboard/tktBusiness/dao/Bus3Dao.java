package com.ctrip.tour.business.dashboard.tktBusiness.dao;

import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.StatementParameters;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.ctrip.tour.business.dashboard.utils.SqlUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/8/5
 */
@Repository
public class Bus3Dao {

    @Autowired
    private GeneralDao dao;

    public List<List<Object>> getMetricCardReachData(Map<String, List<String>> inMap,
                                                     String year) throws SQLException {
        StringBuilder sb = new StringBuilder();
        // 2024年口径修改
        int y = year != null ? Integer.parseInt(year) : 0;
        if(y >= 2024){
            sb.append("select sum(weighted_defect_cnt) as ttd_weighted_defect_cnt, sum(pay_odr_cnt) as ttd_pay_odr_cnt, sum(weighted_defect_cnt)/sum(pay_odr_cnt) as ttd_weighted_defect_rate ");
            sb.append(" from bus_3_weighted_defect_province_finish_t ");
        }else{
            sb.append("select sum(ttd_qa_cost) as ttd_qa_cost,sum(ttd_suc_income) as ttd_suc_income,sum(ttd_orders) as ttd_orders");
            if ("2022".equals(year)) {
                sb.append(",sum(ttd_qa_cost)/sum(ttd_suc_income) as ttd_qa_cost_rate ");
            } else {
                sb.append(",sum(ttd_qa_cost)/sum(ttd_orders) as ttd_qa_cost_rate ");
            }
            sb.append(" from bus_3_qacost_gmv_orders_finish_t ");
        }
        StatementParameters parameters = new StatementParameters();
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        return dao.getListResult(sb.toString(), parameters);
    }

    //获取选定周期目标
    //2022年目标为质量成本/GMV
    //2023年以及以后目标为质量成本/提交单量
    public List<List<Object>> getMetricCardTargetData(Map<String, List<String>> inMap,
                                                      String year) throws SQLException {
        StringBuilder sb = new StringBuilder();
        StatementParameters parameters = new StatementParameters();
        int y = year != null ? Integer.parseInt(year) : 0;
        if(y >= 2024){
            sb.append("select sum(weight_defect_count)/sum(order_paid_count) as ttd_weighted_defect_target ");
        }else if ("2022".equals(year)) {
            sb.append("select sum(ttd_trgt_qa_cost)/sum(ttd_trgt_income) as ttd_trgt_qa_cost_rate ");
        } else {
            sb.append("select sum(ttd_trgt_qa_cost)/sum(order_count) as ttd_trgt_qa_cost_rate ");
        }
        sb.append(" from bus_3_region_qa_config_new ");
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        return dao.getListResult(sb.toString(), parameters);
    }

    //按月或者季拆分 返回当年进展
    public List<List<Object>> getTrendlineReachData(Map<String, List<String>> inMap,
                                                    List<String> groupTagList,
                                                    String year) throws SQLException {
        StringBuilder sb = new StringBuilder();
        StatementParameters parameters = new StatementParameters();
        sb.append("select ");
        if (!GeneralUtil.isEmpty(groupTagList)) {
            SqlUtil.jointGroupCondition(sb, false, groupTagList);
            sb.append(",");
        }
        int y = year != null ? Integer.parseInt(year) : 0;
        if(y >= 2024){
            sb.append("sum(weighted_defect_cnt) as ttd_weighted_defect_cnt, sum(pay_odr_cnt) as ttd_pay_odr_cnt, sum(weighted_defect_cnt)/sum(pay_odr_cnt) as ttd_weighted_defect_rate ");
            sb.append(" from bus_3_weighted_defect_province_finish_t ");
        }else{
            sb.append("sum(ttd_qa_cost) as ttd_qa_cost,sum(ttd_suc_income) as ttd_suc_income,sum(ttd_orders) as ttd_orders");
            if ("2022".equals(year)) {
                sb.append(",sum(ttd_qa_cost)/sum(ttd_suc_income) as ttd_qa_cost_rate ");
            } else {
                sb.append(",sum(ttd_qa_cost)/sum(ttd_orders) as ttd_qa_cost_rate ");
            }
            sb.append(" from bus_3_qacost_gmv_orders_finish_t ");
        }
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        if (!GeneralUtil.isEmpty(groupTagList)) {
            SqlUtil.jointGroupCondition(sb, true, groupTagList);
        }
        return dao.getListResult(sb.toString(), parameters);
    }


    //按季拆分 返回当年目标
    //2022年目标为质量成本/GMV
    //2023年以及以后目标为质量成本/提交单量
    public List<List<Object>> getSpilitTargetData(Map<String, List<String>> inMap,
                                                  List<String> groupTagList,
                                                  String year) throws SQLException{
        StringBuilder sb = new StringBuilder();
        StatementParameters parameters = new StatementParameters();
        sb.append("select ");
        if (!GeneralUtil.isEmpty(groupTagList)){
            SqlUtil.jointGroupCondition(sb, false, groupTagList);
            sb.append(",");
        }
        int y = year != null ? Integer.parseInt(year) : 0;
        if(y >= 2024){
            sb.append("sum(weight_defect_count)/sum(order_paid_count) as ttd_weighted_defect_target ");
        }else{
            if ("2022".equals(year)) {
                sb.append("sum(ttd_trgt_qa_cost)/sum(ttd_trgt_income) as ttd_trgt_qa_cost_rate ");
            } else {
                sb.append("sum(ttd_trgt_qa_cost)/sum(order_count) as ttd_trgt_qa_cost_rate ");
            }
        }
        sb.append(" from bus_3_region_qa_config_new ");
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        if (!GeneralUtil.isEmpty(groupTagList)){
            SqlUtil.jointGroupCondition(sb, true, groupTagList);
        }
        return dao.getListResult(sb.toString(), parameters);
    }


    public List<List<Object>> getFieldList(String tableName, Map<String, List<String>> inMap,
                                           Map<String, String> likeMap,
                                           List<String> tagList) throws SQLException {
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        SqlUtil.jointGroupCondition(sb, false, tagList);
        sb.append(String.format(" from %s ", tableName));
        StatementParameters parameters = new StatementParameters();
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        if (!GeneralUtil.isEmpty(likeMap)) {
            SqlUtil.jointLikeCondition(sb, parameters, likeMap, true);
        }
        SqlUtil.jointGroupCondition(sb, true, tagList);
        return dao.getListResult(sb.toString(), parameters);
    }

    //获取某月或者某季数据明细
    //按照下钻维度排序
    public List<List<Object>> getTableReachData(Map<String, List<String>> inMap,
                                                List<String> tagList,
                                                Integer pageNo,
                                                Integer pageSize,
                                                String year) throws SQLException {
        StringBuilder sb = new StringBuilder();
        StatementParameters parameters = new StatementParameters();
        sb.append("select ");
        SqlUtil.jointGroupCondition(sb, false, tagList);

        int y = year != null ? Integer.parseInt(year) : 0;
        if(y >= 2024){
            sb.append(" ,sum(weighted_defect_cnt) as ttd_weighted_defect_cnt, sum(pay_odr_cnt) as ttd_pay_odr_cnt, sum(weighted_defect_cnt)/sum(pay_odr_cnt) as ttd_weighted_defect_rate ");
            sb.append(" from bus_3_weighted_defect_province_finish_t ");
        }else{
            sb.append(",sum(ttd_qa_cost) as ttd_qa_cost,sum(ttd_suc_income) as ttd_suc_income,sum(ttd_orders) as ttd_orders");
            if ("2022".equals(year)) {
                sb.append(",sum(ttd_qa_cost)/sum(ttd_suc_income) as ttd_qa_cost_rate ");
            } else {
                sb.append(",sum(ttd_qa_cost)/sum(ttd_orders) as ttd_qa_cost_rate ");
            }
            sb.append(" from bus_3_qacost_gmv_orders_finish_t ");
        }
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        SqlUtil.jointGroupCondition(sb, true, tagList);
        SqlUtil.jointOrderCondition(sb, tagList, "asc");
        if (pageNo != null && pageSize != null) {
            SqlUtil.jointPagingCondition(sb, parameters, pageNo, pageSize);
        }
        return dao.getListResult(sb.toString(), parameters);
    }

    //获取表格数据总条数
    public Integer getTableReachDataCount(Map<String, List<String>> inMap,
                                          List<String> tagList,
                                          String year
                                          ) throws SQLException {
        StringBuilder sb = new StringBuilder();
        StatementParameters parameters = new StatementParameters();
        sb.append("select count(*) from ( select ");
        SqlUtil.jointGroupCondition(sb, false, tagList);
        int y = year != null ? Integer.parseInt(year) : 0;
        if(y >= 2024){
            sb.append(" ,sum(weighted_defect_cnt) as ttd_weighted_defect_cnt, sum(pay_odr_cnt) as ttd_pay_odr_cnt");
            sb.append(" from bus_3_weighted_defect_province_finish_t ");
        }else{
            sb.append(",sum(ttd_qa_cost) as ttd_qa_cost,sum(ttd_suc_income) as ttd_suc_income ");
            sb.append(" from bus_3_qacost_gmv_orders_finish_t ");
        }
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        SqlUtil.jointGroupCondition(sb, true, tagList);
        SqlUtil.jointOrderCondition(sb, tagList, "asc");
        sb.append(" ) aa");
        List<List<Object>> resultList = dao.getListResult(sb.toString(), parameters);
        return Integer.valueOf(String.valueOf(resultList.get(0).get(0)));
    }


    public void getRankAsync(Map<String, List<String>> inMap,
                             DalHints dalHints) throws SQLException {
        StringBuilder sb = new StringBuilder();
        sb.append("select ranking from bus_3_rank_t ");
        StatementParameters parameters = new StatementParameters();
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        dao.getListResultAsync(sb.toString(), parameters, dalHints);
    }


}
