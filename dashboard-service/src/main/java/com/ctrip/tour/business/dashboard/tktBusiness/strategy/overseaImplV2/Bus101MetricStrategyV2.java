package com.ctrip.tour.business.dashboard.tktBusiness.strategy.overseaImplV2;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.sightArchives.dao.overseametric.DimOrdTtdTargetConfigDao;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ExamineConfigBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBeanV2;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoWithMetricBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bo.OverSeaExamineConfigBO;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBeanV3;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardOverseaExamineeConfigDao;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardOverseaExamineeConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.enums.BuTypeEnum;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.Bus101102Helper;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.MultiPeriodMappingHelper;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.OverseaMetricCalStrategyV2;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.subMetricImpl.OverseaSubMetricCalStategyBizImpl;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.subMetricImplV2.OverseaSubMetricCalStategyBizImplV2;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Future;

@Component
public class Bus101MetricStrategyV2 implements OverseaMetricCalStrategyV2 {


    @Autowired
    private OverseaSubMetricCalStategyBizImplV2 overseaSubMetricCalStategyBiz;

    @Autowired
    private BusinessDashboardOverseaExamineeConfigDao examineeConfigDao;

    @Autowired
    private DimOrdTtdTargetConfigDao dimOrdTtdTargetConfigDao;

    @Autowired
    private RemoteConfig remoteConfig;


    @Override
    public Future<OveaseaMetric> getOverseaSingleMetricCardData(TimeFilter timeFilter,
                                                                OverseaMetricInfoBeanV2 metricInfoBean,
                                                                String d,
                                                                AvailableSubMetric availableSubMetric,
                                                                GetOverseaMetricCardDataV2RequestType request) throws Exception {
        List<String> subMetricList = availableSubMetric.getSubMetricList();
        List<Future<OveaseaSubMetric>> futureList = new ArrayList<>();
        for (String subMetric : subMetricList) {
            futureList.add(overseaSubMetricCalStategyBiz.getBus101102SubMetricCardData(timeFilter, metricInfoBean, d, subMetric, request));
        }
        OveaseaMetric overSeaMetrics = new OveaseaMetric();
        overSeaMetrics.setMetric("101");
        List<OveaseaSubMetric> overSeaMetricsList = new ArrayList<>();
        for (Future<OveaseaSubMetric> futureResult : futureList) {
            overSeaMetricsList.add(futureResult.get());
        }
        overseaSubMetricCalStategyBiz.setNewSubMetricWithCT(overSeaMetricsList);
        overSeaMetrics.setSubMetricList(overSeaMetricsList);
        overSeaMetrics.getSubMetricList().removeIf(subMetric -> subMetric.getTargetValue() == null || subMetric.getTargetValue() == 0.00);
        return new AsyncResult<>(overSeaMetrics);
    }

    @Override
    public GetOverseaTrendLineDataV2ResponseType getOverseaSingleTrendlineData(GetOverseaTrendLineDataV2RequestType request,
                                                                             String d) throws Exception {
        //最新的数据时间
        List<String> timeList = DateUtil.getAllSelectedTime(request.getTimeFilter(), "oversea");
        return overseaSubMetricCalStategyBiz.getBus101102SubTrendlineData(request, d, timeList);
    }

    @Override
    public GetOverseaDrillDownBaseInfoV2ResponseType getOverseaSingleDrillDownBaseInfo(GetOverseaDrillDownBaseInfoV2RequestType request,
                                                                                     String d,
                                                                                       OverseaMetricInfoBean metricInfoBean) throws Exception {
        return overseaSubMetricCalStategyBiz.getBus101102SubDrillDownBaseInfo(request, d, metricInfoBean);
    }

    @Override
    public GetOverseaTableDataV2ResponseType getOverseaSingleTableData(GetOverseaTableDataV2RequestType request,
                                                                     String d,
                                                                       OverseaMetricInfoBean metricInfoBean) throws Exception {
        return overseaSubMetricCalStategyBiz.getBus101102SubTableData(request, d, metricInfoBean);
    }


    @Override
    public String getMetricName() {
        return "101";
    }
}
