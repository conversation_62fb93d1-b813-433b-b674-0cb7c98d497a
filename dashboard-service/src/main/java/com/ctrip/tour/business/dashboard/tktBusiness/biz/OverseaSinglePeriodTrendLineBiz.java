package com.ctrip.tour.business.dashboard.tktBusiness.biz;

import com.ctrip.soa._24922.GetOverseaTrendLineDataRequestType;
import com.ctrip.soa._27181.GetRawDataRequestType;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ExamineConfigBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.SinglePeriodDataBean;
import org.springframework.scheduling.annotation.Async;

import java.util.concurrent.Future;

public interface OverseaSinglePeriodTrendLineBiz {

    //gmv
    @Async("trendLineExecutor")
    Future<SinglePeriodDataBean> getBus101102SubSinglePeriodTrendLineData(GetRawDataRequestType currentReq,
                                                                          GetRawDataRequestType targetReq,
                                                                          GetRawDataRequestType lastyearReq,
                                                                          GetRawDataRequestType _2019Req,
                                                                          ExamineConfigBean examineConfig) throws Exception;





    @Async("trendLineExecutor")
    Future<SinglePeriodDataBean> getBus103SubSinglePeriodTrendLineData(GetOverseaTrendLineDataRequestType request,
                                                                       OverseaMetricInfoBean metricInfoBean,
                                                                       GetRawDataRequestType currentReq,
                                                                       GetRawDataRequestType targetReq,
                                                                       ExamineConfigBean examineConfig) throws Exception;



    @Async("trendLineExecutor")
    Future<SinglePeriodDataBean> getBus105106107SubSinglePeriodTrendLineData(GetOverseaTrendLineDataRequestType request,
                                                                             OverseaMetricInfoBean metricInfoBean,
                                                                             GetRawDataRequestType currentReq,
                                                                             ExamineConfigBean examineConfig,
                                                                             Integer actualGapDays) throws Exception;


}
