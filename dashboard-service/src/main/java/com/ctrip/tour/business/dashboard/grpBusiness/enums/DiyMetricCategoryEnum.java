package com.ctrip.tour.business.dashboard.grpBusiness.enums;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.apache.commons.lang.StringUtils;

import com.google.common.collect.Lists;

public enum DiyMetricCategoryEnum {

    INCOME_CATEGORY("incomeCategory", "销售额(GMV)类","v.page.workbench.diySalesVolume.category.tips"),//NOSONAR

    PROFIT_PRICE_CATEGORY("profitPriceCategory", "毛利类","v.page.workbench.diyProfitPrice.category.tips"),//NOSONAR

    AVA_ORDER_CAP_CATEGORY("avaOrderCapCategory", "可接单量","v.page.workbench.diyAvaOrderCap.category.tips"),//NOSONAR

    VENDOR_ORD_ACCEPT_RATE_CATEGORY("vendorOrdAcceptRateCategory", "cpr供应商接单率","v.page.workbench.diyVendorOrdAcceptRate.category.tips"),//NOSONAR

    TRADING_VOL_CATEGORY("tradingVolCategory", "成交","v.page.workbench.diyTradingVol.category.tips"),//NOSONAR

    TOP_RATE_GUIDER_CATEGORY("topRateGuiderCategory", "金牌导游","v.page.workbench.diyTopRateGuider.category.tips"),//NOSONAR

    TOP_RATE_DRIVER_CATEGORY("topRateDriverCategory", "金牌司导","v.page.workbench.diyTopRateDriver.category.tips"),//NOSONAR

    FITTING_NPS_CATEGORY("fittingNpsCategory", "拟合NPS","v.page.workbench.diyFittingNps.category.tips");//NOSONAR


    private String englishName;
    private String chineseName;
    private String categoryTipsSharkKey;

    DiyMetricCategoryEnum(String englishName, String chineseName, String categoryTipsSharkKey) {
        this.englishName = englishName;
        this.chineseName = chineseName;
        this.categoryTipsSharkKey = categoryTipsSharkKey;
    }

    public String getEnglishName() {
        return englishName;
    }

    public void setEnglishName(String englishName) {
        this.englishName = englishName;
    }

    public String getChineseName() {
        return chineseName;
    }

    public void setChineseName(String chineseName) {
        this.chineseName = chineseName;
    }

    public String getCategoryTipsSharkKey() {
        return categoryTipsSharkKey;
    }

    public void setCategoryTipsSharkKey(String categoryTipsSharkKey) {
        this.categoryTipsSharkKey = categoryTipsSharkKey;
    }

    private static final List<DiyMetricCategoryEnum> allMetricCategoryEnums = Arrays.asList(DiyMetricCategoryEnum.values());



    private static final List<MetricEnum> incomeCategoryEnumList = Arrays.asList(
            MetricEnum.DIY_INCOME,
            MetricEnum.DIY_INCOME_ACHIEVE_RATE
    );

    private static final List<MetricEnum> profitPriceCategoryEnumList = Arrays.asList(
            MetricEnum.DIY_PROFIT,
            MetricEnum.DIY_PROFIT_ACHIEVE_RATE
    );

    private static final List<MetricEnum> avaOrderCapCategoryEnumList = Arrays.asList(
            MetricEnum.AVA_ORDER_CAP
    );

    private static final List<MetricEnum> vendorOrdAcceptRateRateCategoryEnumList = Arrays.asList(
            MetricEnum.VENDOR_ORD_ACCEPT_RATE
    );


    private static final List<MetricEnum> tradingVolCategoryEnumListWithPreOderDate = Arrays.asList(
            MetricEnum.COMPLETED_ORDERS,
            MetricEnum.COMPLETED_ORDERS_RATE,
            MetricEnum.ORD_AVG_PRICE,
            MetricEnum.CPR_COMPLETED_ORDERS_RATE
    );

    private static final List<MetricEnum> tradingVolCategoryEnumListWithTripDate = Arrays.asList(
            MetricEnum.ORD_AVG_PRICE
            );

    private static final List<MetricEnum> fittingNpsCategoryEnumList = Arrays.asList(
            MetricEnum.DIY_FITTINGNPS,
            MetricEnum.DIY_RECOMMENDATION_RATE,
            MetricEnum.DIY_SLANDER_RATE
    );

    private static final List<MetricEnum> topRateGuiderCategoryEnumList = Arrays.asList(
            MetricEnum.DIY_GUIDER_DISPATCH_RATE
    );

    private static final List<MetricEnum> topRateDriverCategoryEnumList = Arrays.asList(
            MetricEnum.DIY_DRIVER_ACTUAL_DISPACTH_RATE,
            MetricEnum.DIY_DRIVER_EXECUTION_RATE,
            MetricEnum.DIY_DRIVER_DISPATCH_RATE
    );


    public static List<DiyMetricCategoryEnum> getAllMetricCategoryEnums() {
        return allMetricCategoryEnums;
    }

    public static DiyMetricCategoryEnum getMetricCategoryEnumByEnglishName(String metricCategoryEnglishName) {
        for(DiyMetricCategoryEnum metricCategoryEnum : allMetricCategoryEnums) {
            if(metricCategoryEnum.getEnglishName().equals(metricCategoryEnglishName)) {
                return metricCategoryEnum;
            }
        }

        return null;
    }

    public static List<MetricEnum> getMetricEnumsByCategoryEnglishName(String metricCategoryEnglishName, String dateType) {

        if(INCOME_CATEGORY.getEnglishName().equals(metricCategoryEnglishName)
                && (StringUtils.equals(dateType, CustTourDateTypeEnum.TRIP_DATE.name()) ||
                StringUtils.equals(dateType, CustTourDateTypeEnum.PREORDER_DATE.name()))) {
            return incomeCategoryEnumList;
        } else if(PROFIT_PRICE_CATEGORY.getEnglishName().equals(metricCategoryEnglishName) &&
                (StringUtils.equals(dateType, CustTourDateTypeEnum.TRIP_DATE.name()) ||
                        StringUtils.equals(dateType, CustTourDateTypeEnum.PREORDER_DATE.name()))) {
            return profitPriceCategoryEnumList;
        } else if(AVA_ORDER_CAP_CATEGORY.getEnglishName().equals(metricCategoryEnglishName)) {
            return avaOrderCapCategoryEnumList;
        } else if(VENDOR_ORD_ACCEPT_RATE_CATEGORY.getEnglishName().equals(metricCategoryEnglishName)) {
            return vendorOrdAcceptRateRateCategoryEnumList;
        } else if(TRADING_VOL_CATEGORY.getEnglishName().equals(metricCategoryEnglishName) &&
                (StringUtils.equals(dateType, CustTourDateTypeEnum.PREORDER_DATE.name()))) {
            return tradingVolCategoryEnumListWithPreOderDate;
        } else if(TRADING_VOL_CATEGORY.getEnglishName().equals(metricCategoryEnglishName) &&
                (StringUtils.equals(dateType, CustTourDateTypeEnum.TRIP_DATE.name()))) {
            return tradingVolCategoryEnumListWithTripDate;
        } else if(TOP_RATE_GUIDER_CATEGORY.getEnglishName().equals(metricCategoryEnglishName)) {
            return topRateGuiderCategoryEnumList;
        }else if(TOP_RATE_DRIVER_CATEGORY.getEnglishName().equals(metricCategoryEnglishName)) {
            return topRateDriverCategoryEnumList;
        } else if(FITTING_NPS_CATEGORY.getEnglishName().equals(metricCategoryEnglishName)) {
            return fittingNpsCategoryEnumList;
        }

        return new ArrayList<>();
    }

    public static List<MetricEnum> getMetricEnumsByCategoryEnum(DiyMetricCategoryEnum metricCategoryEnum, String dateType) {

        if(INCOME_CATEGORY.equals(metricCategoryEnum) &&
                (StringUtils.equals(dateType, CustTourDateTypeEnum.TRIP_DATE.name()) ||
                        StringUtils.equals(dateType, CustTourDateTypeEnum.PREORDER_DATE.name()))) {
            return incomeCategoryEnumList;
        } else if(PROFIT_PRICE_CATEGORY.equals(metricCategoryEnum) && (StringUtils.equals(dateType, CustTourDateTypeEnum.TRIP_DATE.name()) ||
                StringUtils.equals(dateType, CustTourDateTypeEnum.PREORDER_DATE.name()))) {
            return profitPriceCategoryEnumList;
        } else if(AVA_ORDER_CAP_CATEGORY.equals(metricCategoryEnum)) {
            return avaOrderCapCategoryEnumList;
        } else if(VENDOR_ORD_ACCEPT_RATE_CATEGORY.equals(metricCategoryEnum)) {
            return vendorOrdAcceptRateRateCategoryEnumList;
        } else if(TRADING_VOL_CATEGORY.equals(metricCategoryEnum) &&
                (StringUtils.equals(dateType, CustTourDateTypeEnum.PREORDER_DATE.name()))) {
            return tradingVolCategoryEnumListWithPreOderDate;
        } else if(TRADING_VOL_CATEGORY.equals(metricCategoryEnum) &&
                (StringUtils.equals(dateType, CustTourDateTypeEnum.TRIP_DATE.name()))) {
            return tradingVolCategoryEnumListWithTripDate;
        } else if(TOP_RATE_GUIDER_CATEGORY.equals(metricCategoryEnum)) {
            return topRateGuiderCategoryEnumList;
        } else if(FITTING_NPS_CATEGORY.equals(metricCategoryEnum)) {
            return fittingNpsCategoryEnumList;
        } else if(TOP_RATE_DRIVER_CATEGORY.equals(metricCategoryEnum)) {
            return topRateDriverCategoryEnumList;
        }

        return new ArrayList<>();
    }

    public static List<String> getCategoryNameByDateType(String dateType) {

        if(StringUtils.isBlank(dateType) || StringUtils.equals(dateType, CustTourDateTypeEnum.PREORDER_DATE.name())) {
            return Lists.newArrayList(INCOME_CATEGORY.englishName, PROFIT_PRICE_CATEGORY.englishName, AVA_ORDER_CAP_CATEGORY.englishName,
                    VENDOR_ORD_ACCEPT_RATE_CATEGORY.englishName, TRADING_VOL_CATEGORY.englishName);
        } else if(StringUtils.equals(dateType, CustTourDateTypeEnum.TRIP_DATE.name())) {
            return Lists.newArrayList(INCOME_CATEGORY.englishName, PROFIT_PRICE_CATEGORY.englishName, TRADING_VOL_CATEGORY.englishName);
        } else if(StringUtils.equals(dateType, CustTourDateTypeEnum.RETURN_DATE.name())) {
            return Lists.newArrayList(FITTING_NPS_CATEGORY.englishName, TOP_RATE_DRIVER_CATEGORY.englishName
            , TOP_RATE_GUIDER_CATEGORY.englishName);
        }

        return new ArrayList<>();
    }

}
