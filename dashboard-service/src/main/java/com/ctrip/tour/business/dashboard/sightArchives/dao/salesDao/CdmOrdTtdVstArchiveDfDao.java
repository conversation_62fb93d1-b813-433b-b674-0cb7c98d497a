package com.ctrip.tour.business.dashboard.sightArchives.dao.salesDao;

import com.ctrip.soa._24922.*;
import com.ctrip.soa._27181.PreparedParameterBean;
import com.ctrip.tour.business.dashboard.sightArchives.bean.SaleUnitInfo;
import com.ctrip.tour.business.dashboard.sightArchives.enums.sales.SalesMetricEnumType;
import com.ctrip.tour.business.dashboard.sightArchives.enums.sales.SalesPieChartEnumType;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.TktStarRocksDao;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.ScenicLanguageHelper;
import com.ctrip.tour.business.dashboard.utils.UserUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.sql.Types;
import java.util.*;

@Repository
@Slf4j
public class CdmOrdTtdVstArchiveDfDao {

    //产量汇总表 数仓侧设计文档:http://conf.ctripcorp.com/pages/viewpage.action?pageId=3672460730
    //数据源表: dw_ticketdb.cdm_ord_ttd_vst_archive_df  没有subsight

    @Autowired
    private TktStarRocksDao tktStarRocksDao;

    //指标卡
    public Map<String, Object> querySalesMetricCard(Long sightId, Integer dateType, String startDate, String endDate, Boolean needSubSight, Integer businessType, List<Long> vendorIdList, String queryD){

        StringBuilder sql = new StringBuilder(
                "select "
                        + " cast(sum(ord_ttd_suc_income) as Double) as gmv "  //gmv
                        + ",cast( sum(ord_ttd_suc_profit) as Double) as profit"   //毛利
                        + ",cast( sum(ord_ttd_suc_profit)/sum(ord_ttd_suc_income) as Double) as profitRate"  //毛利率
                        + ",cast( sum(ord_ttd_suc_ord_cnt) as Double) as orderCount"   //订单量
                        + ",cast( sum(ord_ttd_suc_qty_amt) as Double) as soldTicketCount "  //票量
                        + ",cast( sum(ord_ttd_suc_income)/sum(ord_ttd_suc_ord_cnt) as Double) as averageOrderValue"  //客单价
                        + ",cast( sum(ord_ttd_total_refund_ord_cnt)/sum(ord_ttd_sbt_ord_cnt) as Double) as fullyRefundedOrderRate"  //退订率
                        + " from cdm_ord_ttd_vst_archive_df "
        );

        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendSightId(parameters, sql, sightId);
        appendQueryD(parameters,sql,queryD);
        appendDateRange(parameters, sql, dateType, startDate, endDate);
        appendBusinessType(parameters, sql, businessType);
        appendVendorIdList(parameters, sql, vendorIdList);
        appendGroupingSetsFilter(sql, vendorIdList, needSubSight);


        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(),parameters);
        } catch (SQLException e) {
            log.warn("querySalesMetricCard error", e);
        }
//        SalesMetricCardBean salesMetricCardBean = SalesMetricCardBean.transferToSalesMetricCardBean(result);

        return CollectionUtils.isEmpty(result)? new HashMap<>():result.get(0);
    }

    private void appendQueryD(List<PreparedParameterBean> parameters, StringBuilder sql, String queryD){
        sql.append(" and d = ?");
        parameters.add(new PreparedParameterBean(queryD, Types.VARCHAR));
    }

    private void appendGroupingSetsFilter(StringBuilder sql, List<Long> vendorIdList, Boolean needSubSight) {
        appendGroupingSetsFilter(sql, vendorIdList, needSubSight, null);
    }

    private void appendGroupingSetsFilter(StringBuilder sql, List<Long> vendorIdList, Boolean needSubSight, Integer drillDownDim) {
        if(needSubSight){  //含子景点维度
            if((drillDownDim!=null && drillDownDim==2) || CollectionUtils.isNotEmpty(vendorIdList)) { //含供应商维度
                sql.append(" and dim_type_id = 3");
            }else {     //不含供应商维度
                sql.append(" and dim_type_id = 4");
            }
        }else {   //不含子景点维度
            if((drillDownDim!=null && drillDownDim==2) || CollectionUtils.isNotEmpty(vendorIdList)) { //含供应商维度
                sql.append(" and dim_type_id = 1");
            }else {     //不含供应商维度
                sql.append(" and dim_type_id = 2");
            }
        }
    }

    public List<SalesPieChartSegment> querySalesPieChart(
            String queryD
            , Long sightId
            , Integer dateType
            , String startDate
            , String endDate
            , Boolean needSubSight
            , Integer businessType
            , List<Long> vendorIdList
            , SalesPieChartEnumType salesPieChartEnumType
            , String metricName
    ){

        StringBuilder sql = new StringBuilder(
                "select "
                        + salesPieChartEnumType.getDrillDownColumn()
                        + ", cast(sum(ord_ttd_suc_income) as Double) as gmv "  //gmv
                        + ",cast( sum(ord_ttd_suc_profit) as Double) as profit"   //毛利
                        + ",cast( sum(ord_ttd_suc_profit)/sum(ord_ttd_suc_income) as Double) as profitRate"  //毛利率
                        + ",cast( sum(ord_ttd_suc_ord_cnt) as Double) as orderCount"   //订单量
                        + ",cast( sum(ord_ttd_suc_qty_amt) as Double) as soldTicketCount "  //票量
                        + ",cast( sum(ord_ttd_suc_income)/sum(ord_ttd_suc_ord_cnt) as Double) as averageOrderValue"  //客单价
                        + ",cast( sum(ord_ttd_total_refund_ord_cnt)/sum(ord_ttd_sbt_ord_cnt) as Double) as fullyRefundedOrderRate"  //退订率
                        + ",cast(sum(ord_ttd_sbt_ord_cnt) as Integer) as submitOrderCount"  //提交订单量
                        + " from cdm_ord_ttd_vst_archive_df "
        );

        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendSightId(parameters, sql, sightId);
        appendQueryD(parameters,sql,queryD);
        appendDateRange(parameters, sql, dateType, startDate, endDate);
        appendBusinessType(parameters, sql, businessType);
        appendVendorIdList(parameters, sql, vendorIdList);
        if(salesPieChartEnumType.isRestrictTktBuType()){
            sql.append(" and bu_type = ? ");
            parameters.add(new PreparedParameterBean("tkt", Types.VARCHAR));
        }
        if(salesPieChartEnumType.isExcludeUnkwnData()){
            sql.append(" and ").append(salesPieChartEnumType.getDrillDownColumn()).append(" != 'unkwn' ");
        }
        appendGroupingSetsFilter(sql, vendorIdList, needSubSight);
        appendGroupBy(sql, salesPieChartEnumType.getDrillDownColumn());

        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(),parameters);
        } catch (SQLException e) {
            log.warn("querySalesPieChart error", e);
        }

        result = CollectionUtils.isEmpty(result)? new ArrayList<>():result;

        List<SalesPieChartSegment> pieChartSegmentList = new ArrayList<>();
        //计算各行的值占总数的占比
        Double totalValue = 0.0;
        for(Map<String, Object> resultMap : result){
            if(resultMap.get(metricName) == null){
                continue;
            }
            totalValue += (Double) resultMap.getOrDefault(metricName,0.0);
        }
        if(totalValue>0.0){
            if(SalesMetricEnumType.FULLY_REFUNDED_ORDER_RATE.getEnglishName().equals(metricName)){
                for(Map<String, Object> resultMap : result){
                    SalesPieChartSegment pieChartSegment = new SalesPieChartSegment();
                    pieChartSegment.setName(String.valueOf(resultMap.get(salesPieChartEnumType.getDrillDownColumn())));
                    if("1".equals(pieChartSegment.getName())){
                        pieChartSegment.setName("机酒交叉");  //NOSONAR
                    }else if("0".equals(pieChartSegment.getName())){
                        pieChartSegment.setName("非机酒交叉"); //NOSONAR
                    }
                    pieChartSegment.setMetricValue((Double) resultMap.getOrDefault(metricName,0.0));
                    pieChartSegment.setSubmitOrderCount((Integer)resultMap.getOrDefault("submitOrderCount",0));
                    pieChartSegmentList.add(pieChartSegment);
                }
            }else if (SalesMetricEnumType.AVERAGE_ORDER_VALUE.getEnglishName().equals(metricName)){
                for(Map<String, Object> resultMap : result){
                    SalesPieChartSegment pieChartSegment = new SalesPieChartSegment();
                    pieChartSegment.setName(String.valueOf(resultMap.get(salesPieChartEnumType.getDrillDownColumn())));
                    if("1".equals(pieChartSegment.getName())){
                        pieChartSegment.setName("机酒交叉"); //NOSONAR
                    }else if("0".equals(pieChartSegment.getName())){
                        pieChartSegment.setName("非机酒交叉"); //NOSONAR
                    }
                    pieChartSegment.setMetricValue((Double) resultMap.getOrDefault(metricName,0.0));
                    pieChartSegmentList.add(pieChartSegment);
                }
            } else {
                for(Map<String, Object> resultMap : result){
                    SalesPieChartSegment pieChartSegment = new SalesPieChartSegment();
                    pieChartSegment.setName(String.valueOf(resultMap.get(salesPieChartEnumType.getDrillDownColumn())));
                    if("1".equals(pieChartSegment.getName())){
                        pieChartSegment.setName("机酒交叉"); //NOSONAR
                    }else if("0".equals(pieChartSegment.getName())){
                        pieChartSegment.setName("非机酒交叉"); //NOSONAR
                    }
                    pieChartSegment.setMetricValue((Double) resultMap.getOrDefault(metricName,0.0)/totalValue);
                    if(SalesMetricEnumType.PROFIT.getEnglishName().equals(metricName)) {
                        pieChartSegment.setProfitRate((Double) resultMap.getOrDefault("profitRate", 0.0));
                    }
                    pieChartSegmentList.add(pieChartSegment);
                }
            }

        }

        return pieChartSegmentList;
    }

    public SaleUnitInfo queryFirstSaleUnitId(String queryD, String propertyValueName){
        StringBuilder sql = new StringBuilder(
                "select "
                        + "saleunit_lv1_id as firstSaleUnitId,saleunit_lv2_id as secondSaleUnitId "
                        + " from cdm_ord_ttd_vst_archive_df "
                        + " where property_value_name = '" + propertyValueName + "'"
                        + " and d = '" + queryD + "'"
                        + " limit 1"
        );

        List<PreparedParameterBean> parameters = new ArrayList<>();

        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(),parameters);
        } catch (SQLException e) {
            log.warn("querySalesPieChart error", e);
        }

        result = CollectionUtils.isEmpty(result)? new ArrayList<>():result;
        SaleUnitInfo saleUnitInfo = new SaleUnitInfo();

        for(Map<String, Object> resultMap : result){
            saleUnitInfo.setFirstSaleUnitId((Long) resultMap.getOrDefault("firstSaleUnitId",0L));
            saleUnitInfo.setSecondSaleUnitId((Long) resultMap.getOrDefault("secondSaleUnitId",0L));
            break;
        }

        return saleUnitInfo;
    }


    private void appendGroupBy(StringBuilder sql, String drillDownColumn){
        sql.append(" group by ").append(drillDownColumn);
    }

//    //拼景点id
    private void appendSightId(List<PreparedParameterBean> parameters, StringBuilder sql, Long sightId){

        sql.append(" where vst_id = ?");
        parameters.add(new PreparedParameterBean(String.valueOf(sightId), Types.BIGINT));

    }
    //拼日期范围
    private void appendDateRange(List<PreparedParameterBean> parameters, StringBuilder sql, Integer dateType, String startDate, String endDate){

        if(dateType == 2){
            sql.append(" and ord_date between ? and ?");
        }else {
            sql.append(" and use_date between ? and ?");
        }
        parameters.add(new PreparedParameterBean(startDate, Types.VARCHAR));
        parameters.add(new PreparedParameterBean(endDate, Types.VARCHAR));

    }

    //拼业务类型
    private void appendBusinessType(List<PreparedParameterBean> parameters, StringBuilder sql, Integer businessType){
        if(businessType != 1){
            sql.append(" and bu_type = ? ");
            parameters.add(new PreparedParameterBean(businessType==2?"tkt":"act", Types.VARCHAR));
        }

    }
    //拼供应商id列表
    private void appendVendorIdList(List<PreparedParameterBean> parameters, StringBuilder sql, List<Long> vendorIdList){
        if(CollectionUtils.isNotEmpty(vendorIdList)){
            sql.append(" and vend_id in (");
            for(int i = 0; i < vendorIdList.size(); i++){
                if(i == 0){
                    sql.append("?");
                }else {
                    sql.append(",?");
                }
                parameters.add(new PreparedParameterBean(String.valueOf(vendorIdList.get(i)), Types.BIGINT));
            }
            sql.append(")");
        }
    }

    private void appendIsDomestic(List<PreparedParameterBean> parameters, StringBuilder sql, Long isDomestic) {
        sql.append(" and t3.is_domestic = ? ");
        parameters.add(new PreparedParameterBean(String.valueOf(isDomestic), Types.INTEGER));
    }

    private List<String> appendDrillDownDim(Integer drillDownDim){
        if(drillDownDim==1){
            return Collections.singletonList(" saleunit_lv1_id , saleunit_lv1 ");
        }else if(drillDownDim==2){
            return Arrays.asList(" concat(vend_id,'-',vend_name) "," is_zq ");
        }else if(drillDownDim==3){
            return Collections.singletonList(" concat(alliance_id,'-',alliance_name) ");
        }
        return new ArrayList<>();
    }


    private void appendPage(List<PreparedParameterBean> parameters, StringBuilder sql,Integer pageNo,Integer pageSize){
        String pageNumber=(pageNo-1)*pageSize + "";
        String size = pageSize.toString();
        sql.append(" limit ?,?");
        parameters.add(new PreparedParameterBean(pageNumber, Types.INTEGER));
        parameters.add(new PreparedParameterBean(size, Types.INTEGER));
    }


    public List<CooperativeProject> queryCooperativeProjectOutput(String queryD, Long sightId, Integer dateType, String startDate, String endDate, Boolean needSubSight, Integer businessType, List<Long> vendorIdList) {

        StringBuilder sql = new StringBuilder("select cast(sum(case when is_zd = 1 then ord_ttd_suc_income else 0 end) as Double) as zdgmv,\n" +
                "cast(sum(case when is_bp = 1 then ord_ttd_suc_income else 0 end) as Double) as bpgmv,\n" +
                "cast(sum(case when is_dt = 1 then ord_ttd_suc_income else 0 end) as Double) as dtgmv,\n" +
                "cast(sum(case when is_pj = 1 then ord_ttd_suc_income else 0 end) as Double) as pjgmv,\n" +
                "cast(sum(case when is_yx = 1 then ord_ttd_suc_income else 0 end) as Double) as yxgmv,\n" +
                "cast(sum(case when is_zd = 1 then ord_ttd_suc_profit else 0 end) as Integer) as zdprofit,\n" +
                "cast(sum(case when is_bp = 1 then ord_ttd_suc_profit else 0 end) as Integer) as bpprofit,\n" +
                "cast(sum(case when is_dt = 1 then ord_ttd_suc_profit else 0 end) as Integer) as dtprofit,\n" +
                "cast(sum(case when is_pj = 1 then ord_ttd_suc_profit else 0 end) as Integer) as pjprofit,\n" +
                "cast(sum(case when is_yx = 1 then ord_ttd_suc_profit else 0 end) as Integer) as yxprofit,\n" +
                "cast(sum(case when is_zd = 1 then ord_ttd_suc_qty_amt else 0 end) as Integer) as zdordercnt,\n" +
                "cast(sum(case when is_bp = 1 then ord_ttd_suc_qty_amt else 0 end) as Integer) as bpordercnt,\n" +
                "cast(sum(case when is_dt = 1 then ord_ttd_suc_qty_amt else 0 end) as Integer) as dtordercnt,\n" +
                "cast(sum(case when is_pj = 1 then ord_ttd_suc_qty_amt else 0 end) as Integer) as pjordercnt,\n" +
                "cast(sum(case when is_yx = 1 then ord_ttd_suc_qty_amt else 0 end) as Integer) as yxordercnt,\n" +
                "cast(sum(case when is_zd = 1 or is_bp = 1 or is_dt = 1 or is_pj = 1 or is_yx = 1 then ord_ttd_suc_income else 0 end) as Double) as totalgmv,\n" +
                "cast(sum(case when is_zd = 1 or is_bp = 1 or is_dt = 1 or is_pj = 1 or is_yx = 1 then ord_ttd_suc_profit else 0 end) as Integer) as totalprofit,\n" +
                "cast(sum(case when is_zd = 1 or is_bp = 1 or is_dt = 1 or is_pj = 1 or is_yx = 1 then ord_ttd_suc_qty_amt else 0 end) as Integer) as totalordercnt\n" +
                "from cdm_ord_ttd_vst_archive_df ");
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendSightId(parameters, sql, sightId);
        appendQueryD(parameters,sql,queryD);
        appendDateRange(parameters, sql, dateType, startDate, endDate);
        appendBusinessType(parameters, sql, businessType);
        appendVendorIdList(parameters, sql, vendorIdList);
        appendGroupingSetsFilter(sql, vendorIdList, needSubSight);

        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(),parameters);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }

        if(CollectionUtils.isEmpty(result)){
            return new ArrayList<>();
        }
        Map<String, Object> qresultMap = result.get(0);

        String language = UserUtil.getVbkLocaleForScenic();

        CooperativeProject zd = new CooperativeProject();
        zd.setName(ScenicLanguageHelper.getMultiLanguage("总代",language));  //NOSONAR
        Double zdgmv =(Double) qresultMap.get("zdgmv");
        if(zdgmv != null) {
            zd.setGmv(zdgmv.intValue());
        }
        if(qresultMap.get("totalgmv") != null && ((Double) qresultMap.get("totalgmv")) != 0.0) {
            zd.setGmvPercentage(zdgmv / ((Double) qresultMap.get("totalgmv")));
        }
        zd.setProfit((Integer) qresultMap.get("zdprofit"));
        if (qresultMap.get("totalprofit") != null && ((Integer) qresultMap.get("totalprofit")) != 0) {
            zd.setProfitPercentage((Integer) qresultMap.get("zdprofit") * 1.0 / ((Integer) qresultMap.get("totalprofit")));
        }
        zd.setSoldTicketCount((Integer) qresultMap.get("zdordercnt"));
        if (qresultMap.get("totalordercnt") != null && ((Integer) qresultMap.get("totalordercnt")) != 0) {
            zd.setSoldTicketCountPercentage((Integer) qresultMap.get("zdordercnt") * 1.0 / ((Integer) qresultMap.get("totalordercnt")));
        }

        CooperativeProject bp = new CooperativeProject();
        bp.setName(ScenicLanguageHelper.getMultiLanguage("包票",language));  //NOSONAR
        Double bpgmv =(Double) qresultMap.get("bpgmv");
        if (bpgmv != null) {
            bp.setGmv(bpgmv.intValue());
        }
        if(qresultMap.get("totalgmv") != null && ((Double) qresultMap.get("totalgmv")) != 0.0) {
            bp.setGmvPercentage(bpgmv / ((Double) qresultMap.get("totalgmv")));
        }
        bp.setProfit((Integer) qresultMap.get("bpprofit"));
        if (qresultMap.get("totalprofit") != null && ((Integer) qresultMap.get("totalprofit")) != 0) {
            bp.setProfitPercentage((Integer) qresultMap.get("bpprofit") * 1.0 / ((Integer) qresultMap.get("totalprofit")));
        }
        bp.setSoldTicketCount((Integer) qresultMap.get("bpordercnt"));
        if (qresultMap.get("totalordercnt") != null && ((Integer) qresultMap.get("totalordercnt")) != 0) {
            bp.setSoldTicketCountPercentage((Integer) qresultMap.get("bpordercnt") * 1.0 / ((Integer) qresultMap.get("totalordercnt")));
        }

        CooperativeProject dt = new CooperativeProject();
        dt.setName(ScenicLanguageHelper.getMultiLanguage("地推",language));  //NOSONAR
        Double dtgmv =(Double) qresultMap.get("dtgmv");
        if (dtgmv != null) {
            dt.setGmv(dtgmv.intValue());
        }
        if (qresultMap.get("totalgmv") != null && ((Double) qresultMap.get("totalgmv")) != 0.0) {
            dt.setGmvPercentage(dtgmv / ((Double) qresultMap.get("totalgmv")));
        }
        dt.setProfit((Integer) qresultMap.get("dtprofit"));
        if (qresultMap.get("totalprofit") != null && ((Integer) qresultMap.get("totalprofit")) != 0) {
            dt.setProfitPercentage((Integer) qresultMap.get("dtprofit") * 1.0 / ((Integer) qresultMap.get("totalprofit")));
        }
        dt.setSoldTicketCount((Integer) qresultMap.get("dtordercnt"));
        if (qresultMap.get("totalordercnt") != null && ((Integer) qresultMap.get("totalordercnt")) != 0) {
            dt.setSoldTicketCountPercentage((Integer) qresultMap.get("dtordercnt") * 1.0 / ((Integer) qresultMap.get("totalordercnt")));
        }


        CooperativeProject pj = new CooperativeProject();
        pj.setName(ScenicLanguageHelper.getMultiLanguage("票机",language));  //NOSONAR
        Double pjgmv =(Double) qresultMap.get("pjgmv");
        if (pjgmv != null) {
            pj.setGmv(pjgmv.intValue());
        }
        if (qresultMap.get("totalgmv") != null && ((Double) qresultMap.get("totalgmv")) != 0.0) {
            pj.setGmvPercentage(pjgmv / ((Double) qresultMap.get("totalgmv")));
        }
        pj.setProfit((Integer) qresultMap.get("pjprofit"));
        if (qresultMap.get("totalprofit") != null && ((Integer) qresultMap.get("totalprofit")) != 0) {
            pj.setProfitPercentage((Integer) qresultMap.get("pjprofit") * 1.0 / ((Integer) qresultMap.get("totalprofit")));
        }
        pj.setSoldTicketCount((Integer) qresultMap.get("pjordercnt"));
        if (qresultMap.get("totalordercnt") != null && ((Integer) qresultMap.get("totalordercnt")) != 0) {
            pj.setSoldTicketCountPercentage((Integer) qresultMap.get("pjordercnt") * 1.0 / ((Integer) qresultMap.get("totalordercnt")));
        }

        CooperativeProject yx = new CooperativeProject();
        yx.setName(ScenicLanguageHelper.getMultiLanguage("优选",language));  //NOSONAR
        Double yxgmv =(Double) qresultMap.get("yxgmv");
        if (yxgmv != null) {
            yx.setGmv(yxgmv.intValue());
        }
        if (qresultMap.get("totalgmv") != null && ((Double) qresultMap.get("totalgmv")) != 0.0) {
            yx.setGmvPercentage(yxgmv / ((Double) qresultMap.get("totalgmv")));
        }
        yx.setProfit((Integer) qresultMap.get("yxprofit"));
        if (qresultMap.get("totalprofit") != null && ((Integer) qresultMap.get("totalprofit")) != 0) {
            yx.setProfitPercentage((Integer) qresultMap.get("yxprofit") * 1.0 / ((Integer) qresultMap.get("totalprofit")));
        }
        yx.setSoldTicketCount((Integer) qresultMap.get("yxordercnt"));
        if (qresultMap.get("totalordercnt") != null && ((Integer) qresultMap.get("totalordercnt")) != 0) {
            yx.setSoldTicketCountPercentage((Integer) qresultMap.get("yxordercnt") * 1.0 / ((Integer) qresultMap.get("totalordercnt")));
        }

        List<CooperativeProject> res = new ArrayList<>();
        res.add(zd);
        res.add(bp);
        res.add(dt);
        res.add(pj);
        res.add(yx);

        return res;

    }

    public List<SalesMetricTableRow> querySalesMetricRankTable(String queryD, Long sightId, Integer dateType, String startDate, String endDate, Boolean needSubSight, Integer businessType, List<Long> vendorIdList, String metricName, Integer drillDownDim) {

        StringBuilder sql = new StringBuilder("select ");
        List<String> drillDownDimList =appendDrillDownDim(drillDownDim);
        for(int i=0; i< drillDownDimList.size(); i++){
            if(i==0){
                sql.append(drillDownDimList.get(i)).append(" as name,");
            }else {
                sql.append(drillDownDimList.get(i)).append(",");
            }
        }

        sql.append("cast(sum(ord_ttd_suc_income) as Integer) as gmv," +//gmv
                "cast(sum(ord_ttd_suc_ord_cnt) as Integer) as orderCount," +//订单量
                "cast(sum(ord_ttd_suc_qty_amt) as Integer) as soldTicketCount," + //票量
                "cast(sum(ord_ttd_suc_income)/sum(ord_ttd_suc_ord_cnt) as Double) as averageOrderValue," +//客单价
                "cast(sum(ord_ttd_total_refund_ord_cnt)/sum(ord_ttd_sbt_ord_cnt) as Double) as fullyRefundedOrderRate," +//退订率
                "cast(sum(ord_ttd_total_refund_ord_cnt) as Integer) as fullyRefundedOrderCount," +//退订订单量
//                "sum(ord_ttd_sbt_ord_cnt) as subordcount," +//提交订单量
                "cast(sum(ord_ttd_suc_profit) as Integer) as profit," +//毛利
                "cast(sum(ord_ttd_suc_profit)/sum(ord_ttd_suc_income) as Double) as profitRate " +//毛利率
                "from cdm_ord_ttd_vst_archive_df ");
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendSightId(parameters, sql, sightId);
        appendQueryD(parameters,sql,queryD);
        if (drillDownDim==1){ //票种维度
            sql.append(" and bu_type = ? ");//指定门票业务线
            parameters.add(new PreparedParameterBean("tkt", Types.VARCHAR));
            sql.append(" and saleunit_lv1 != 'unkwn' ");//排除unkwn数据
        }
        appendDateRange(parameters, sql, dateType, startDate, endDate);
        appendBusinessType(parameters, sql, businessType);
        appendVendorIdList(parameters, sql, vendorIdList);
        appendGroupingSetsFilter(sql, vendorIdList, needSubSight,drillDownDim);
        //group by条件
        sql.append(" group by ");
        for (int i=0; i< drillDownDimList.size(); i++){
            sql.append(drillDownDimList.get(i));
            if (i!=drillDownDimList.size()-1){
                sql.append(",");
            }
        }
        //order by条件
        sql.append(" order by ").append(metricName).append(" DESC");


        //统计具体值
        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(),parameters);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        Double gmvtotal = 0.0;
        Double profittotal = 0.0;
        Double orderCountTotal = 0.0;
        Double soldTicketCountTotal = 0.0;
        for(Map<String, Object> map:result){
            gmvtotal=gmvtotal+(Integer) map.getOrDefault(SalesMetricEnumType.GMV.getEnglishName(),"0.0");
            profittotal=profittotal+(Integer) map.getOrDefault(SalesMetricEnumType.PROFIT.getEnglishName(),"0.0");
            orderCountTotal=orderCountTotal+(Integer) map.getOrDefault(SalesMetricEnumType.ORDER_COUNT.getEnglishName(),"0.0");
            soldTicketCountTotal=soldTicketCountTotal+(Integer) map.getOrDefault(SalesMetricEnumType.SOLD_TICKET_COUNT.getEnglishName(),"0.0");
        }
        List<SalesMetricTableRow> res = new ArrayList<>();
        for(Map<String, Object> map:result){
            SalesMetricTableRow salesMetricTableRow = new SalesMetricTableRow();
            salesMetricTableRow.setId((Long) map.get("saleunit_lv1_id"));
            salesMetricTableRow.setName((String) map.get("name"));
            if(drillDownDim==2){
                salesMetricTableRow.setDirectContract(map.get("is_zq")==null?null:((Integer) map.get("is_zq") == 1));
            }
            salesMetricTableRow.setGmv((Integer) map.get(SalesMetricEnumType.GMV.getEnglishName()));
            if(gmvtotal != 0.0) {
                salesMetricTableRow.setGmvPercentage((Integer) map.get(SalesMetricEnumType.GMV.getEnglishName()) / gmvtotal);
            }
            salesMetricTableRow.setProfit((Integer) map.get(SalesMetricEnumType.PROFIT.getEnglishName()));
            if(profittotal != 0.0) {
                salesMetricTableRow.setProfitPercentage((Integer) map.get(SalesMetricEnumType.PROFIT.getEnglishName()) / profittotal);
            }
            salesMetricTableRow.setProfitRate((Double) map.get(SalesMetricEnumType.PROFIT_RATE.getEnglishName()));
            salesMetricTableRow.setAverageOrderValue((Double) map.get(SalesMetricEnumType.AVERAGE_ORDER_VALUE.getEnglishName()));
            salesMetricTableRow.setFullyRefundedOrderCount((Integer) map.get(SalesMetricEnumType.FULLY_REFUNDED_ORDER_COUNT.getEnglishName()));
            salesMetricTableRow.setFullyRefundedOrderRate((Double) map.get(SalesMetricEnumType.FULLY_REFUNDED_ORDER_RATE.getEnglishName()));
            salesMetricTableRow.setOrderCount((Integer) map.get(SalesMetricEnumType.ORDER_COUNT.getEnglishName()));
            salesMetricTableRow.setSoldTicketCount((Integer) map.get(SalesMetricEnumType.SOLD_TICKET_COUNT.getEnglishName()));
            if (orderCountTotal != 0.0) {
                salesMetricTableRow.setOrderCountPercentage((Integer) map.get(SalesMetricEnumType.ORDER_COUNT.getEnglishName()) / orderCountTotal);
            }
            if (soldTicketCountTotal != 0.0) {
                salesMetricTableRow.setSoldTicketCountPercentage((Integer) map.get(SalesMetricEnumType.SOLD_TICKET_COUNT.getEnglishName()) / soldTicketCountTotal);
            }
            res.add(salesMetricTableRow);
        }

        return res;
    }

    public List<Vendor> queryVendorList(Long sightId, String searchKey, String queryD){

        String sql = "select distinct vend_id,vend_name from cdm_ord_ttd_vst_archive_df where d=? and vst_id = ? and (vend_id = ? or vend_name like ?) limit 20";

        List<PreparedParameterBean> parameters = new ArrayList<>();
        parameters.add(new PreparedParameterBean(queryD, Types.VARCHAR));
        parameters.add(new PreparedParameterBean(String.valueOf(sightId), Types.BIGINT));
        parameters.add(new PreparedParameterBean(searchKey, Types.VARCHAR));
        parameters.add(new PreparedParameterBean("%"+searchKey+"%", Types.VARCHAR));

        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql,parameters);
        } catch (SQLException e) {
            log.warn("querySalesMetricCard error", e);
        }
        if(CollectionUtils.isEmpty(result)){
            return new ArrayList<>();
        }
        List<Vendor> vendorList = new ArrayList<>();
        for(Map<String,Object> map : result){
            Vendor vendor = new Vendor();
            vendor.setVendorId((Long) map.get("vend_id"));
            vendor.setVendorName((String) map.get("vend_name"));
            vendorList.add(vendor);
        }

        return vendorList;
    }


    public List<SightArchivesSalesMetricTrendLineItem> querySalesMetricTrendLine(String queryD, Long sightId, Integer dateType, String startDate, String endDate, Boolean needSubSight, Integer businessType, List<Long> vendorIdList, String metricName) {

        //指标值
        StringBuilder sql = new StringBuilder("select ");
        if(dateType == 2){
            sql.append("ord_date as date_time,");
        }else {
            sql.append("use_date as date_time,");
        }
        sql.append("cast(sum(ord_ttd_suc_income) as Double) as gmv," +//gmv
                "cast(sum(ord_ttd_suc_profit) as Double) as profit," +//毛利
                "cast(sum(ord_ttd_suc_ord_cnt) as Double) as orderCount," + //订单量
                "cast(sum(ord_ttd_suc_qty_amt) as Double) as soldTicketCount," +//票量
                "cast(sum(ord_ttd_suc_income)/sum(ord_ttd_suc_ord_cnt) as Double) as averageOrderValue," +//客单价
                "cast(sum(ord_ttd_total_refund_ord_cnt)/sum(ord_ttd_sbt_ord_cnt) as Double) as fullyRefundedOrderRate" +//退订率
                " from cdm_ord_ttd_vst_archive_df ");
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendSightId(parameters, sql, sightId);
        appendQueryD(parameters, sql, queryD);
        appendDateRange(parameters, sql, dateType, startDate, endDate);
        appendBusinessType(parameters, sql, businessType);
        appendVendorIdList(parameters, sql, vendorIdList);
        appendGroupingSetsFilter(sql, vendorIdList, needSubSight);
        sql.append(" group by date_time");
        sql.append(" order by date_time asc");

        List<Map<String, Object>> list;
        try {
            list = tktStarRocksDao.getListResultNew(sql.toString(), parameters);//查询条数
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
//        if(list.size()==0){
//            return new ArrayList<>();
//        }

        //去年同期指标值
        StringBuilder lysql = new StringBuilder("select ");
        lysql.append("t2.date_solar as date_time_lastYear,");
        lysql.append("cast(sum(t1.ord_ttd_suc_income) as Double) as gmv_lastYear," +//gmv
                "cast(sum(t1.ord_ttd_suc_profit) as Double) as profit_lastYear," +//毛利
                "cast(sum(t1.ord_ttd_suc_ord_cnt) as Double) as orderCount_lastYear," + //订单量
                "cast(sum(t1.ord_ttd_suc_qty_amt) as Double) as soldTicketCount_lastYear," +//票量
                "cast(sum(t1.ord_ttd_suc_income)/sum(t1.ord_ttd_suc_ord_cnt) as Double) as averageOrderValue_lastYear," +//客单价
                "cast(sum(t1.ord_ttd_total_refund_ord_cnt)/sum(t1.ord_ttd_sbt_ord_cnt) as Double) as fullyRefundedOrderRate_lastYear" +//退订率
                " from cdm_ord_ttd_vst_archive_df t1 inner join v_dim_date t2");
        List<PreparedParameterBean> lyparameters = new ArrayList<>();
        appendJoinON(lyparameters, lysql, dateType);
        appendSightId(lyparameters, lysql, sightId);
        sql.append(" and t1.d = ?");
        parameters.add(new PreparedParameterBean(queryD, Types.VARCHAR));
        appendDateRangeOfLastYear(lyparameters, lysql, dateType, startDate, endDate);
        appendBusinessType(lyparameters, lysql, businessType);
        appendVendorIdList(lyparameters, lysql, vendorIdList);
        appendGroupingSetsFilter(lysql, vendorIdList, needSubSight);
        lysql.append(" group by date_time_lastYear");

        List<Map<String, Object>> lylist;
        try {
            lylist = tktStarRocksDao.getListResultNew(lysql.toString(), lyparameters);//查询条数
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }

        List<Map<String, Object>> maps = mergeLists(list, lylist);

        //同比去年
        List<SightArchivesSalesMetricTrendLineItem> res = new ArrayList<>();
        for(Map<String, Object> map:maps){
            SightArchivesSalesMetricTrendLineItem sightArchivesSalesMetricTrendLineItem = new SightArchivesSalesMetricTrendLineItem();
            sightArchivesSalesMetricTrendLineItem.setDate((String) map.get("date_time"));
            sightArchivesSalesMetricTrendLineItem.setMetricValue((Double) map.get(metricName));
            sightArchivesSalesMetricTrendLineItem.setLastYearMetricValue((Double) map.get(metricName+"_lastYear"));//done
            sightArchivesSalesMetricTrendLineItem.setYoyValue((Double) map.get(metricName+"_yoy"));//done
            res.add(sightArchivesSalesMetricTrendLineItem);
        }
        return res;
    }

    public static List<Map<String, Object>> mergeLists(List<Map<String, Object>> currentYearList, List<Map<String, Object>> lastYearList) {
        List<Map<String, Object>> mergedList = new ArrayList<>();

        for (Map<String, Object> currentYearMap : currentYearList) {
            String currentYearDate = (String) currentYearMap.get("date_time");
            Map<String, Object> mergedMap = new HashMap<>();
            mergedMap.put("date_time", currentYearDate);

            // 复制当前年份的指标
            mergedMap.put(SalesMetricEnumType.GMV.getEnglishName(), currentYearMap.get(SalesMetricEnumType.GMV.getEnglishName()));
            mergedMap.put(SalesMetricEnumType.PROFIT.getEnglishName(), currentYearMap.get(SalesMetricEnumType.PROFIT.getEnglishName()));
            mergedMap.put(SalesMetricEnumType.ORDER_COUNT.getEnglishName(), currentYearMap.get(SalesMetricEnumType.ORDER_COUNT.getEnglishName()));
            mergedMap.put(SalesMetricEnumType.SOLD_TICKET_COUNT.getEnglishName(), currentYearMap.get(SalesMetricEnumType.SOLD_TICKET_COUNT.getEnglishName()));
            mergedMap.put(SalesMetricEnumType.AVERAGE_ORDER_VALUE.getEnglishName(), currentYearMap.get(SalesMetricEnumType.AVERAGE_ORDER_VALUE.getEnglishName()));
            mergedMap.put(SalesMetricEnumType.FULLY_REFUNDED_ORDER_RATE.getEnglishName(), currentYearMap.get(SalesMetricEnumType.FULLY_REFUNDED_ORDER_RATE.getEnglishName()));

            for (Map<String, Object> lastYearMap : lastYearList) {
                String lastYearDate = (String) lastYearMap.get("date_time_lastYear");
                if (currentYearDate.equals(lastYearDate)) {
                    // 复制上一年份的指标
                    mergedMap.put(SalesMetricEnumType.GMV.getEnglishName()+"_lastYear", lastYearMap.get(SalesMetricEnumType.GMV.getEnglishName()+"_lastYear"));
                    mergedMap.put(SalesMetricEnumType.PROFIT.getEnglishName()+"_lastYear", lastYearMap.get(SalesMetricEnumType.PROFIT.getEnglishName()+"_lastYear"));
                    mergedMap.put(SalesMetricEnumType.ORDER_COUNT.getEnglishName()+"_lastYear", lastYearMap.get(SalesMetricEnumType.ORDER_COUNT.getEnglishName()+"_lastYear"));
                    mergedMap.put(SalesMetricEnumType.SOLD_TICKET_COUNT.getEnglishName()+"_lastYear", lastYearMap.get(SalesMetricEnumType.SOLD_TICKET_COUNT.getEnglishName()+"_lastYear"));
                    mergedMap.put(SalesMetricEnumType.AVERAGE_ORDER_VALUE.getEnglishName()+"_lastYear", lastYearMap.get(SalesMetricEnumType.AVERAGE_ORDER_VALUE.getEnglishName()+"_lastYear"));
                    mergedMap.put(SalesMetricEnumType.FULLY_REFUNDED_ORDER_RATE.getEnglishName()+"_lastYear", lastYearMap.get(SalesMetricEnumType.FULLY_REFUNDED_ORDER_RATE.getEnglishName()+"_lastYear"));

                    // 计算同比值
                    calculateYoy(mergedMap, SalesMetricEnumType.GMV.getEnglishName());
                    calculateYoy(mergedMap, SalesMetricEnumType.PROFIT.getEnglishName());
                    calculateYoy(mergedMap, SalesMetricEnumType.ORDER_COUNT.getEnglishName());
                    calculateYoy(mergedMap, SalesMetricEnumType.SOLD_TICKET_COUNT.getEnglishName());
                    calculateYoy(mergedMap, SalesMetricEnumType.AVERAGE_ORDER_VALUE.getEnglishName());
                    calculateYoy(mergedMap, SalesMetricEnumType.FULLY_REFUNDED_ORDER_RATE.getEnglishName());

                    break;
                }
            }

            mergedList.add(mergedMap);
        }

        return mergedList;
    }

    private static void calculateYoy(Map<String, Object> mergedMap, String key) {
        // 检查当前值是否为null
        Object currentValueObj = mergedMap.get(key);
        Object lastYearValueObj = mergedMap.get(key + "_lastYear");

        // 如果其中一个值为null，则无法计算同比增长率，直接返回或设置默认值
        if (currentValueObj == null || lastYearValueObj == null) {
            mergedMap.put(key + "_yoy", 0.0); // 或者根据业务需求设置为其他默认值
            return;
        }

        // 将值转换为Double
        Double currentValue = Double.parseDouble(currentValueObj.toString());
        Double lastYearValue = Double.parseDouble(lastYearValueObj.toString());

        // 计算同比增长率
        if (lastYearValue != 0) {
            double yoy = (currentValue - lastYearValue) / lastYearValue;
            mergedMap.put(key + "_yoy", yoy);
        } else {
            // 如果去年值为0，通常同比增长率无法计算，这里也可以根据业务需求处理
            mergedMap.put(key + "_yoy", 0.0);
        }
    }


    private void appendDateRangeOfLastYear(List<PreparedParameterBean> lyparameters, StringBuilder sql, Integer dateType, String startDate, String endDate) {
        sql.append(" and t2.date_solar between ? and ?");
        lyparameters.add(new PreparedParameterBean(startDate, Types.VARCHAR));
        lyparameters.add(new PreparedParameterBean(endDate, Types.VARCHAR));
    }

    private void appendJoinON(List<PreparedParameterBean> lyparameters, StringBuilder lysql, Integer dateType) {
        if(dateType == 2){
            lysql.append(" on t1.ord_date = t2.date_lastyear");
        }else {
            lysql.append(" on t1.use_date = t2.date_lastyear");
        }
    }

    private void appendJoinONScenicInfo(StringBuilder lysql) {
        lysql.append(" inner join dim_prd_tkt_scenic_info_core t3 on t1.vst_id = t3.viewspot_id ");
    }

    public List<Map<String, Object>> querySightRankListOfrefundRate(String queryD, List<Long> competitiveSightIdList, String startDate, String endDate, Integer businessType, List<Long> vendorIdList, Integer dateType) {
        if (CollectionUtils.isEmpty(competitiveSightIdList)) {
            return new ArrayList<>();
        }
        StringBuilder sql = new StringBuilder(
                "select vst_id,vst_name,"
                        + "cast(sum(ord_ttd_total_refund_ord_cnt)/sum(ord_ttd_sbt_ord_cnt) as Double) as fullyRefundedOrderRate"  //退订率
                        + " from cdm_ord_ttd_vst_archive_df "
        );
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendSightIdOfCompetitiveSight(parameters, sql, competitiveSightIdList);
        appendQueryD(parameters, sql, queryD);
        appendDateRange(parameters, sql, dateType, startDate, endDate);
//        appendBusinessType(parameters, sql, businessType);
//        appendVendorIdList(parameters, sql, vendorIdList);
        appendGroupingSetsFilter(sql, new ArrayList<>(), false);
        sql.append(" and ord_ttd_sbt_ord_cnt > 0" );//提交订单量大于0
        appendGroupBy(sql,"vst_id,vst_name");
        sql.append(" order by fullyRefundedOrderRate ASC");
        sql.append(" limit 10");

        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(),parameters);
        } catch (SQLException e) {
            log.warn("querySalesMetricCard error", e);
        }

        return result;
    }
    private void appendSightIdOfCompetitiveSight(List<PreparedParameterBean> parameters, StringBuilder sql, List<Long> competitiveSightIdList) {
        if(CollectionUtils.isNotEmpty(competitiveSightIdList)){
            sql.append(" where vst_id in (");
            for(int i = 0; i < competitiveSightIdList.size(); i++){
                if(i == 0){
                    sql.append("?");
                }else {
                    sql.append(",?");
                }
                parameters.add(new PreparedParameterBean(String.valueOf(competitiveSightIdList.get(i)), Types.BIGINT));
            }
            sql.append(")");
        }else {
            sql.append(" where vst_id=0 ");
        }
    }

    public List<Map<String, Object>> querySightRankListOfaverageOrderValue(String queryD, List<Long> competitiveSightIdList, String startDate, String endDate, Integer businessType, List<Long> vendorIdList,Integer dateType) {
        if (CollectionUtils.isEmpty(competitiveSightIdList)) {
            return new ArrayList<>();
        }
        StringBuilder sql = new StringBuilder(
                "select vst_id,vst_name,"
                        + "cast(sum(ord_ttd_suc_income)/sum(ord_ttd_suc_ord_cnt) as Double) as averageOrderValue"  //客单价
                        + " from cdm_ord_ttd_vst_archive_df "
        );
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendSightIdOfCompetitiveSight(parameters, sql, competitiveSightIdList);
        appendQueryD(parameters, sql, queryD);
        appendDateRange(parameters, sql, dateType, startDate, endDate);
//        appendBusinessType(parameters, sql, businessType);
//        appendVendorIdList(parameters, sql, vendorIdList);
        appendGroupingSetsFilter(sql, new ArrayList<>(),false);
        appendGroupBy(sql,"vst_id,vst_name");
        sql.append(" order by averageOrderValue desc");
        sql.append(" limit 10");

        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(),parameters);
        } catch (SQLException e) {
            log.warn("querySalesMetricCard error", e);
        }

        return result;
    }

    public List<Map<String, Object>> querySightRankListOfscenicSpotFlow(String queryD,List<Long> competitiveSightIdList, String startDate, String endDate, Integer businessType, List<Long> vendorIdList,Integer dateType) {
        if (CollectionUtils.isEmpty(competitiveSightIdList)) {
            return new ArrayList<>();
        }
        StringBuilder sql = new StringBuilder(
                "select vst_id,vst_name,"
                        + "cast(sum(ord_ttd_suc_qty_amt) as Double) as ord_ttd_suc_qty_amt"  //订单量
                        + " from cdm_ord_ttd_vst_archive_df "
        );
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendSightIdOfCompetitiveSight(parameters, sql, competitiveSightIdList);
        appendQueryD(parameters, sql, queryD);
        appendDateRange(parameters, sql, dateType, startDate, endDate);
//        appendBusinessType(parameters, sql, businessType);
//        appendVendorIdList(parameters, sql, vendorIdList);
        appendGroupingSetsFilter(sql, new ArrayList<>(),false);
        appendGroupBy(sql,"vst_id,vst_name");
        sql.append(" order by ord_ttd_suc_qty_amt desc");
        sql.append(" limit 10");

        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(),parameters);
        } catch (SQLException e) {
            log.warn("querySalesMetricCard error", e);
        }

        return result;
    }

    public Map<String, Object> queryRadargmv(String queryD, Long sightId, Integer dateType, String startDate, String endDate, List<Long> vendorIdList, Integer businessType,Boolean needSubSight) {
        StringBuilder sql = new StringBuilder(
                "select "
                        + "cast(sum(ord_ttd_suc_income) as Integer) as gmv"  //gmv
                        + " from cdm_ord_ttd_vst_archive_df "
        );
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendSightId(parameters, sql, sightId);
        appendQueryD(parameters, sql, queryD);
        appendDateRange(parameters, sql, dateType, startDate, endDate);
        appendBusinessType(parameters, sql, businessType);
        appendVendorIdList(parameters, sql, vendorIdList);
        appendGroupingSetsFilter(sql, vendorIdList, needSubSight);



        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(),parameters);
        } catch (SQLException e) {
            log.warn("querySalesMetricCard error", e);
        }
        if(CollectionUtils.isEmpty(result)){
            return new HashMap<>();
        }
        return  result.get(0);
    }

    public Map<String, Object> querySalesMetricCardYoy(Long sightId, Integer dateType, String startDate, String endDate, Boolean needSubSight, Integer businessType, List<Long> vendorIdList, String queryD) {
        StringBuilder sql = new StringBuilder(
                "select "
                        + " cast(sum(ord_ttd_suc_income) as Double) as gmv "  //gmv
                        + ",cast( sum(ord_ttd_suc_profit) as Double) as profit"   //毛利
                        + ",cast( sum(ord_ttd_suc_profit)/sum(ord_ttd_suc_income) as Double) as profitRate"  //毛利率
                        + ",cast( sum(ord_ttd_suc_ord_cnt) as Double) as orderCount"   //订单量
                        + ",cast( sum(ord_ttd_suc_qty_amt) as Double) as soldTicketCount "  //票量
                        + ",cast( sum(ord_ttd_suc_income)/sum(ord_ttd_suc_ord_cnt) as Double) as averageOrderValue"  //客单价
                        + ",cast( sum(ord_ttd_total_refund_ord_cnt)/sum(ord_ttd_sbt_ord_cnt) as Double) as fullyRefundedOrderRate"  //退订率
                        + " from cdm_ord_ttd_vst_archive_df t1 inner join v_dim_date t2"
        );
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendJoinON(parameters, sql, dateType);
        appendSightId(parameters, sql, sightId);
        sql.append(" and t1.d = ?");
        parameters.add(new PreparedParameterBean(queryD, Types.VARCHAR));
        appendDateRangeOfLastYear(parameters, sql, dateType, startDate, endDate);
        appendBusinessType(parameters, sql, businessType);
        appendVendorIdList(parameters, sql, vendorIdList);
        appendGroupingSetsFilter(sql, vendorIdList, needSubSight);
        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(),parameters);
        } catch (SQLException e) {
            log.warn("querySalesMetricCard error", e);
        }
        if(CollectionUtils.isEmpty(result)){
            return new HashMap<>();
        }
        return  result.get(0);
    }

    public Map<String, Object> querySalesMetricCardPop(Long sightId, Integer dateType, String startDate, String endDate, Boolean needSubSight, Integer businessType, List<Long> vendorIdList, String queryD) {
        StringBuilder sql = new StringBuilder(
                "select "
                        + " cast(sum(ord_ttd_suc_income) as Double) as gmv "  //gmv
                        + ",cast( sum(ord_ttd_suc_profit) as Double) as profit"   //毛利
                        + ",cast( sum(ord_ttd_suc_profit)/sum(ord_ttd_suc_income) as Double) as profitRate"  //毛利率
                        + ",cast( sum(ord_ttd_suc_ord_cnt) as Double) as orderCount"   //订单量
                        + ",cast( sum(ord_ttd_suc_qty_amt) as Double) as soldTicketCount "  //票量
                        + ",cast( sum(ord_ttd_suc_income)/sum(ord_ttd_suc_ord_cnt) as Double) as averageOrderValue"  //客单价
                        + ",cast( sum(ord_ttd_total_refund_ord_cnt)/sum(ord_ttd_sbt_ord_cnt) as Double) as fullyRefundedOrderRate"  //退订率
                        + " from cdm_ord_ttd_vst_archive_df "
        );

        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendSightId(parameters, sql, sightId);
        appendQueryD(parameters,sql,queryD);
        appendDateRange(parameters, sql, dateType,startDate,endDate);

        appendBusinessType(parameters, sql, businessType);
        appendVendorIdList(parameters, sql, vendorIdList);
        appendGroupingSetsFilter(sql, vendorIdList, needSubSight);

        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(),parameters);
        } catch (SQLException e) {
            log.warn("querySalesMetricCard error", e);
        }
//        SalesMetricCardBean salesMetricCardBean = SalesMetricCardBean.transferToSalesMetricCardBean(result);

        return CollectionUtils.isEmpty(result)? new HashMap<>():result.get(0);
    }




    public List<SalesPieChartSegment>  querySalesPieChartYoy(
            String queryD
            , Long sightId
            , Integer dateType
            , String startDate
            , String endDate
            , Boolean needSubSight
            , Integer businessType
            , List<Long> vendorIdList
            , SalesPieChartEnumType salesPieChartEnumType
            , String metricName
    ){

        StringBuilder sql = new StringBuilder(
                "select "
                        + salesPieChartEnumType.getDrillDownColumn()
                        + ", cast(sum(ord_ttd_suc_income) as Double) as gmv "  //gmv
                        + ",cast( sum(ord_ttd_suc_profit) as Double) as profit"   //毛利
                        + ",cast( sum(ord_ttd_suc_profit)/sum(ord_ttd_suc_income) as Double) as profitRate"  //毛利率
                        + ",cast( sum(ord_ttd_suc_ord_cnt) as Double) as orderCount"   //订单量
                        + ",cast( sum(ord_ttd_suc_qty_amt) as Double) as soldTicketCount "  //票量
                        + ",cast( sum(ord_ttd_suc_income)/sum(ord_ttd_suc_ord_cnt) as Double) as averageOrderValue"  //客单价
                        + ",cast( sum(ord_ttd_total_refund_ord_cnt)/sum(ord_ttd_sbt_ord_cnt) as Double) as fullyRefundedOrderRate"  //退订率
                        + ",cast(sum(ord_ttd_sbt_ord_cnt) as Integer) as submitOrderCount"  //提交订单量
                        + " from cdm_ord_ttd_vst_archive_df t1 inner join v_dim_date t2"

        );

        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendJoinON(parameters, sql, dateType);
        appendSightId(parameters, sql, sightId);
        sql.append(" and t1.d = ?");
        parameters.add(new PreparedParameterBean(queryD, Types.VARCHAR));
        appendDateRangeOfLastYear(parameters, sql, dateType, startDate, endDate);
        appendBusinessType(parameters, sql, businessType);
        appendVendorIdList(parameters, sql, vendorIdList);
        if(salesPieChartEnumType.isExcludeUnkwnData()){
            sql.append(" and ").append(salesPieChartEnumType.getDrillDownColumn()).append(" != 'unkwn' ");
        }
        appendGroupingSetsFilter(sql, vendorIdList, needSubSight);
        appendGroupBy(sql, salesPieChartEnumType.getDrillDownColumn());

        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(),parameters);
        } catch (SQLException e) {
            log.warn("querySalesPieChart error", e);
        }

        result = CollectionUtils.isEmpty(result)? new ArrayList<>():result;

        List<SalesPieChartSegment> pieChartSegmentList = new ArrayList<>();
        //计算各行的值占总数的占比
        Double totalValue = 0.0;
        for(Map<String, Object> resultMap : result){
            totalValue += resultMap.getOrDefault(metricName,0.0)==null?0.0:(Double) resultMap.getOrDefault(metricName,0.0);
        }
        if(totalValue>0.0){
                for(Map<String, Object> resultMap : result){
                    SalesPieChartSegment pieChartSegment = new SalesPieChartSegment();
                    pieChartSegment.setName(String.valueOf(resultMap.get(salesPieChartEnumType.getDrillDownColumn())));
                    if("1".equals(pieChartSegment.getName())){
                        pieChartSegment.setName("机酒交叉");  //NOSONAR
                    }else if("0".equals(pieChartSegment.getName())){
                        pieChartSegment.setName("非机酒交叉"); //NOSONAR
                    }
                    pieChartSegment.setYoyValue((Double) Optional.ofNullable(resultMap.getOrDefault(metricName,0.0)).orElse(0.0)/totalValue);
                    pieChartSegmentList.add(pieChartSegment);
                }
            }

        return pieChartSegmentList;
    }


//    public Map<String, Object> querySalesPieChartYoy(Long sightId, Integer dateType, String startDate, String endDate, Boolean needSubSight, Integer businessType, List<Long> vendorIdList) {
//        StringBuilder sql = new StringBuilder(
//                "select "
//                        + " cast(sum(ord_ttd_suc_income) as Double) as gmv "  //gmv
//                        + ",cast( sum(ord_ttd_suc_profit) as Double) as profit"   //毛利
//                        + ",cast( sum(ord_ttd_suc_profit)/sum(ord_ttd_suc_income) as Double) as profitRate"  //毛利率
//                        + ",cast( sum(ord_ttd_suc_ord_cnt) as Double) as orderCount"   //订单量
//                        + ",cast( sum(ord_ttd_suc_qty_amt) as Double) as soldTicketCount "  //票量
//                        + ",cast( sum(ord_ttd_suc_income)/sum(ord_ttd_suc_ord_cnt) as Double) as averageOrderValue"  //客单价
//                        + ",cast( sum(ord_ttd_total_refund_ord_cnt)/sum(ord_ttd_sbt_ord_cnt) as Double) as fullyRefundedOrderRate"  //退订率
//                        + " from cdm_ord_ttd_vst_archive_df t1 inner join v_dim_date t2"
//        );
//        List<PreparedParameterBean> parameters = new ArrayList<>();
//        appendJoinON(parameters, sql, dateType);
//        appendSightId(parameters, sql, sightId);
//        appendDateRangeOfLastYear(parameters, sql, dateType, startDate, endDate);
//        appendBusinessType(parameters, sql, businessType);
//        appendVendorIdList(parameters, sql, vendorIdList);
//        appendGroupingSetsFilter(sql, vendorIdList, needSubSight);
//        List<Map<String, Object>> result = null;
//        try {
//            result = tktStarRocksDao.getListResultNew(sql.toString(),parameters);
//        } catch (SQLException e) {
//            log.warn("querySalesMetricCard error", e);
//        }
//        if(CollectionUtils.isEmpty(result)){
//            return new HashMap<>();
//        }
//        return  result.get(0);
//    }

    public Map<String, Object> queryRadargmvPop(String queryD,Long sightId, Integer dateType, String startDate, String endDate, List<Long> vendorIdList, Integer businessType,Boolean needSubSight) {
        StringBuilder sql = new StringBuilder(
                "select "
                        + "cast(sum(ord_ttd_suc_income) as Double) as gmv"  //gmv
                        + " from cdm_ord_ttd_vst_archive_df t1 inner join v_dim_date t2"  //gmv
        );
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendJoinON(parameters, sql, dateType);
        appendSightId(parameters, sql, sightId);
        sql.append(" and t1.d = ?");
        parameters.add(new PreparedParameterBean(queryD, Types.VARCHAR));
        appendDateRangeOfLastYear(parameters, sql, dateType, startDate, endDate);
        appendBusinessType(parameters, sql, businessType);
        appendVendorIdList(parameters, sql, vendorIdList);
        appendGroupingSetsFilter(sql, vendorIdList, needSubSight);

        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(),parameters);
        } catch (SQLException e) {
            log.warn("querySalesMetricCard error", e);
        }
        if(CollectionUtils.isEmpty(result)){
            return new HashMap<>();
        }
        return  result.get(0);
    }

    public Map<String, Object> queryRadargmvAverage(String queryD, Long sightId, Integer dateType, String startDate, String endDate, List<Long> vendorIdList, Integer businessType, Long isDomestic) {
        StringBuilder sql = new StringBuilder(
                "select "
                        + "cast(sum(ord_ttd_suc_income) as Double)/COUNT(DISTINCT vst_id) as gmv_average"  //gmv
                        + " from cdm_ord_ttd_vst_archive_df t1  "
        );
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendJoinONScenicInfo(sql);//关联景点信息表过滤国内\海外景点
        sql.append(" where 1=1 ");
        sql.append(" and t1.d = ?");
        parameters.add(new PreparedParameterBean(queryD, Types.VARCHAR));
        appendDateRange(parameters, sql, dateType, startDate, endDate);
        appendBusinessType(parameters, sql, businessType);
        appendVendorIdList(parameters, sql, vendorIdList);
        appendIsDomestic(parameters, sql, isDomestic);
        appendGroupingSetsFilter(sql, vendorIdList, false);

        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(),parameters);
        } catch (SQLException e) {
            log.warn("querySalesMetricCard error", e);
        }
        if(CollectionUtils.isEmpty(result)){
            return new HashMap<>();
        }
        return  result.get(0);
    }

    public Map<String, Object> queryRadargmvAveragePop(String queryD, Long sightId, Integer dateType, String startDate, String endDate, List<Long> vendorIdList, Integer businessType, Long isDomestic) {
        StringBuilder sql = new StringBuilder(
                "select "
                        + "cast(sum(ord_ttd_suc_income) as Double)/COUNT(DISTINCT vst_id) as gmv_average"  //gmv
                        + " from cdm_ord_ttd_vst_archive_df t1 inner join v_dim_date t2 "  //gmv
        );

        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendJoinON(parameters, sql, dateType);
        appendJoinONScenicInfo(sql);//关联景点信息表过滤国内\海外景点
        sql.append(" where 1=1 ");
        sql.append(" and t1.d = ?");
        parameters.add(new PreparedParameterBean(queryD, Types.VARCHAR));
        appendDateRangeOfLastYear(parameters, sql, dateType, startDate, endDate);
        appendBusinessType(parameters, sql, businessType);
        appendVendorIdList(parameters, sql, vendorIdList);
        appendIsDomestic(parameters, sql, isDomestic);
        appendGroupingSetsFilter(sql, vendorIdList, false);

        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(),parameters);
        } catch (SQLException e) {
            log.warn("querySalesMetricCard error", e);
        }
        if(CollectionUtils.isEmpty(result)){
            return new HashMap<>();
        }
        return  result.get(0);
    }

    public List<Map<String, Object>> querySightRankListOfscenicSpotFlowOfTenIds(String queryD,List<Long> sightidlist, String startDate, String endDate, Integer businessType, List<Long> vendorIdList,Integer dateType) {
        if (CollectionUtils.isEmpty(sightidlist)) {
            return new ArrayList<>();
        }
        //同比
        StringBuilder sql = new StringBuilder(
                "select vst_id,vst_name,"
                        + "cast(sum(ord_ttd_suc_qty_amt) as Double) as ord_ttd_suc_qty_amt"  //订单量
                        + " from cdm_ord_ttd_vst_archive_df t1 inner join v_dim_date t2"
        );
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendJoinON(parameters, sql, dateType);
        appendSightIdOfCompetitiveSight(parameters, sql, sightidlist);
        sql.append(" and t1.d = ?");
        parameters.add(new PreparedParameterBean(queryD, Types.VARCHAR));
        appendDateRangeOfLastYear(parameters, sql, dateType, startDate, endDate);
        appendBusinessType(parameters, sql, businessType);
        appendVendorIdList(parameters, sql, vendorIdList);
        appendGroupingSetsFilter(sql, new ArrayList<>(),false);
        appendGroupBy(sql,"vst_id,vst_name");
        sql.append(" order by ord_ttd_suc_qty_amt desc");

        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(),parameters);
        } catch (SQLException e) {
            log.warn("querySalesMetricCard error", e);
        }

        return result;
    }
}
