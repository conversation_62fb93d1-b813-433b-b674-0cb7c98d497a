package com.ctrip.tour.business.dashboard.grpBusiness.metrics.multiplePrice;

import java.sql.SQLException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.tour.business.dashboard.grpBusiness.dao.mysql.DimOrdGrpAchv2025PersonTrgtDao;
import com.ctrip.tour.business.dashboard.grpBusiness.dao.mysql.EdwHrEmpVacationDao;
import com.ctrip.tour.business.dashboard.grpBusiness.dao.starrocks.StarRocksCommonDao;
import com.ctrip.tour.business.dashboard.grpBusiness.entity.DimOrdGrpAchv2025PersonTrgt;
import com.ctrip.tour.business.dashboard.grpBusiness.entity.EdwHrEmpVacation;
import com.google.common.base.Joiner;
import com.google.common.collect.Maps;

/**
 * <AUTHOR>
 * @Date 2025/4/9
 */
@Service
public class GrpMpWeiAnomalyRateRankService {

    @Autowired
    private StarRocksCommonDao starRocksCommonDao;
    @Autowired
    private DimOrdGrpAchv2025PersonTrgtDao trgtDao;


    public Integer[] getPmRank(String startDate,  String endDate, String empCode) throws SQLException {

        List<DimOrdGrpAchv2025PersonTrgt> tgts = trgtDao.query("1=1", new DalHints());
        List<String> collect = tgts.stream()
                .map(DimOrdGrpAchv2025PersonTrgt::getPmLvl3No)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(collect) || !collect.contains(empCode) || StringUtils.equals(empCode, "S44002")) {
            return null;
        }

        String empCodes = Joiner.on(",")
                .join(collect.stream().map(code -> "'" + code + "'").collect(Collectors.toList()));

        String sql = "select \n" +
                "((sum(pre1d_more1_pv)/sum(pre1d_total_pv))*0.8 + \n" +
                "(sum(pre1d_uncomp_pv)/sum(pre1d_total_pv)*0.2)) as abnomalRate,\n" +
                "pm_eid\n" +
                "from adm_prd_grp_avg_multiple_price_work_platform_df\n" +
                "where view_date>='"+startDate+"' and view_date<='"+endDate+"' and pm_eid in ("+empCodes+")\n" +
                "and partition_d='"+ LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) +"'\n" +
                "group by pm_eid\n" +
                "order by abnomalRate";

        List<Map<String, Object>> query = starRocksCommonDao.query(sql, Maps.newHashMap());



        if (CollectionUtils.isEmpty(query)) {
            return null;
        }

        int rank = IntStream.range(0, query.size())
                .filter(i -> empCode.equals(query.get(i).get("pm_eid")))
                .findFirst()
                .orElse(0);
        return new Integer[]{rank, query.size()};
    }
}
