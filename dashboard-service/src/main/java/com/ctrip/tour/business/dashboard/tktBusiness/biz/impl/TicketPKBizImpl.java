package com.ctrip.tour.business.dashboard.tktBusiness.biz.impl;

import com.ctrip.ottd.product.background.openapi.external.GetMixProductPreviewStatusRequestType;
import com.ctrip.ottd.product.background.openapi.external.GetMixProductPreviewStatusResponseType;
import com.ctrip.ottd.product.background.openapi.external.GetPreviewInfoResponse;
import com.ctrip.soa._24922.GetPreviewInfoRequestType;
import com.ctrip.soa._24922.GetPreviewInfoResponseType;
import com.ctrip.soa._24922.GetResponseBaseDataType;
import com.ctrip.soa._24922.PreviewInfo;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.TicketPKBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardEmployeeInfoDao;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardEmployeeInfo;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.NestedExceptionUtils;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.ServiceException;
import com.ctrip.tour.business.dashboard.utils.UserUtil;
import com.ctrip.ttd.vendor.soa.BIVendorServiceClient;
import com.ctrip.ttd.vendor.soa.GetTicketPKBestStatusRequestType;
import com.ctrip.ttd.vendor.soa.GetTicketPKBestStatusResponseType;
import com.ctrip.ttd.vendor.soa.GetTicketPKDefectOrderIdRequestType;
import com.ctrip.ttd.vendor.soa.GetTicketPKDefectOrderIdResponseType;
import com.ctrip.ttd.vendor.soa.GetTicketPKDefectOverallDataRequestType;
import com.ctrip.ttd.vendor.soa.GetTicketPKDefectOverallDataResponseType;
import com.ctrip.ttd.vendor.soa.GetTicketPKDefectTableDataRequestType;
import com.ctrip.ttd.vendor.soa.GetTicketPKDefectTableDataResponseType;
import com.ctrip.ttd.vendor.soa.GetTicketPKEnumDataRequestType;
import com.ctrip.ttd.vendor.soa.GetTicketPKEnumDataResponseType;
import com.ctrip.ttd.vendor.soa.GetTicketPKQualificationRequestType;
import com.ctrip.ttd.vendor.soa.GetTicketPKQualificationResponseType;
import com.ctrip.ttd.vendor.soa.GetTicketPKResourceRankingRequestType;
import com.ctrip.ttd.vendor.soa.GetTicketPKResourceRankingResponseType;
import com.ctrip.ttd.vendor.soa.GetTicketPKSaleunitRankingRequestType;
import com.ctrip.ttd.vendor.soa.GetTicketPKSaleunitRankingResponseType;
import com.ctrip.ttd.vendor.soa.GetTicketPKScheduleCalendarRequestType;
import com.ctrip.ttd.vendor.soa.GetTicketPKScheduleCalendarResponseType;
import com.ctrip.ttd.vendor.soa.GetTicketPKTableRequestType;
import com.ctrip.ttd.vendor.soa.GetTicketPKTableResponseType;
import com.ctrip.ttd.vendor.soa.TicketPkBaseQuery;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.util.Objects;

@Service
public class TicketPKBizImpl implements TicketPKBiz {
    @Autowired
    RemoteConfig remoteConfig;
    @Autowired
    BusinessDashboardEmployeeInfoDao employeeInfoDao;

   // @Autowired
    BIVendorServiceClient client = BIVendorServiceClient.getInstance();

    {
        client.headers().put("appname", "clientforwarding");
    }

    private String getPme() throws SQLException {
        String empCode = getLoginEmp();
        BusinessDashboardEmployeeInfo employeeInfo = employeeInfoDao.queryByEmpCode(empCode);
        if (Objects.isNull(employeeInfo)) {
            throw new ServiceException("E102",String.format("empCode:%s is not ticket bu user",
                empCode));
        }
        return employeeInfo.getDomainName();
    }

    private void baseValidate(TicketPkBaseQuery query) throws SQLException {
        if (Objects.isNull(query)) {
            throw new ServiceException("E103","query parameter is null");
        }
        query.setDomainName(getPme());
    }

    private String getLoginEmp(){
        String empCode = UserUtil.getPkMappingEmpCode(remoteConfig);
        if (StringUtils.isBlank(empCode)) {
            throw new ServiceException("E101","login fail");
        }
        return empCode;
    }

    @Override
    public GetTicketPKEnumDataResponseType getTicketPKEnumData(GetTicketPKEnumDataRequestType request) throws Exception {
        GetTicketPKEnumDataResponseType response = new GetTicketPKEnumDataResponseType();
        try {
            request.setDomainName(getPme());
            response = client.getTicketPKEnumData(request);
        } catch (ServiceException e) {
            response.setResponseStatus(NestedExceptionUtils.responseFail(e));
        }
        return response;
    }

    @Override
    public GetTicketPKSaleunitRankingResponseType getTicketPKSaleunitRanking(GetTicketPKSaleunitRankingRequestType request) throws Exception {
        GetTicketPKSaleunitRankingResponseType response = new GetTicketPKSaleunitRankingResponseType();
        try {
            baseValidate(request.getQuery());
            response = client.getTicketPKSaleunitRanking(request);
        } catch (ServiceException e) {
            response.setResponseStatus(NestedExceptionUtils.responseFail(e));
        }
        return response;
    }

    @Override
    public GetTicketPKResourceRankingResponseType getTicketPKResourceRanking(GetTicketPKResourceRankingRequestType request) throws Exception {
        GetTicketPKResourceRankingResponseType response = new GetTicketPKResourceRankingResponseType();
        try {
            baseValidate(request.getQuery());
            response = client.getTicketPKResourceRanking(request);
        } catch (ServiceException e) {
            response.setResponseStatus(NestedExceptionUtils.responseFail(e));
        }
        return response;
    }

    @Override
    public GetTicketPKScheduleCalendarResponseType getTicketPKScheduleCalendar(GetTicketPKScheduleCalendarRequestType request) throws Exception {
        GetTicketPKScheduleCalendarResponseType response = new GetTicketPKScheduleCalendarResponseType();
        try {
            baseValidate(request.getQuery());
            response = client.getTicketPKScheduleCalendar(request);
        } catch (ServiceException e) {
            response.setResponseStatus(NestedExceptionUtils.responseFail(e));
        }
        return response;
    }

    @Override
    public GetTicketPKQualificationResponseType getTicketPKQualification(GetTicketPKQualificationRequestType request) throws Exception {
        GetTicketPKQualificationResponseType response = new GetTicketPKQualificationResponseType();
        try {
            baseValidate(request.getQuery());
            response = client.getTicketPKQualification(request);
        } catch (ServiceException e) {
            response.setResponseStatus(NestedExceptionUtils.responseFail(e));
        }
        return response;
    }

    @Override
    public GetTicketPKTableResponseType getTicketPKTable(GetTicketPKTableRequestType request) throws Exception {
        GetTicketPKTableResponseType response = new GetTicketPKTableResponseType();
        try {
            baseValidate(request.getQuery());
            response = client.getTicketPKTable(request);
        } catch (ServiceException e) {
            response.setResponseStatus(NestedExceptionUtils.responseFail(e));
        }
        return response;
    }

    @Override
    public GetTicketPKBestStatusResponseType getTicketPKBestStatus(GetTicketPKBestStatusRequestType request) throws Exception {
        GetTicketPKBestStatusResponseType response = new GetTicketPKBestStatusResponseType();
        try {
            baseValidate(request.getQuery());
            response = client.getTicketPKBestStatus(request);
        } catch (ServiceException e) {
            response.setResponseStatus(NestedExceptionUtils.responseFail(e));
        }
        return response;
    }

    @Override
    public GetTicketPKDefectOverallDataResponseType getTicketPKDefectOverallData(GetTicketPKDefectOverallDataRequestType request) throws Exception {
        GetTicketPKDefectOverallDataResponseType response = new GetTicketPKDefectOverallDataResponseType();
        try {
            baseValidate(request.getQuery());
            response = client.getTicketPKDefectOverallData(request);
        } catch (ServiceException e) {
            response.setResponseStatus(NestedExceptionUtils.responseFail(e));
        }
        return response;
    }

    @Override
    public GetTicketPKDefectTableDataResponseType getTicketPKDefectTableData(GetTicketPKDefectTableDataRequestType request) throws Exception {
        GetTicketPKDefectTableDataResponseType response = new GetTicketPKDefectTableDataResponseType();
        try {
            baseValidate(request.getQuery());
            response = client.getTicketPKDefectTableData(request);
        } catch (ServiceException e) {
            response.setResponseStatus(NestedExceptionUtils.responseFail(e));
        }
        return response;
    }

    @Override
    public GetTicketPKDefectOrderIdResponseType getTicketPKDefectOrderId(GetTicketPKDefectOrderIdRequestType request) throws Exception {
        GetTicketPKDefectOrderIdResponseType response = new GetTicketPKDefectOrderIdResponseType();
        try {
            baseValidate(request.getQuery());
            response = client.getTicketPKDefectOrderId(request);
        } catch (ServiceException e) {
            response.setResponseStatus(NestedExceptionUtils.responseFail(e));
        }
        return response;
    }
    @Override
    public GetMixProductPreviewStatusResponseType getMixProductPreviewStatus(GetMixProductPreviewStatusRequestType request) throws Exception {
        GetMixProductPreviewStatusResponseType response = new GetMixProductPreviewStatusResponseType();
        try {
            getLoginEmp();
            response = client.getMixProductPreviewStatus(request);
        } catch (ServiceException e) {
            response.setResponseStatus(NestedExceptionUtils.responseFail(e));
        }
        return response;
    }
    @Override
    public GetPreviewInfoResponseType getPreviewInfo(GetPreviewInfoRequestType request) throws Exception {
        getLoginEmp();
        GetPreviewInfoResponseType response = new GetPreviewInfoResponseType();
        try {
            com.ctrip.ttd.vendor.soa.GetPreviewInfoRequestType requestType =
                new com.ctrip.ttd.vendor.soa.GetPreviewInfoRequestType();
            BeanUtils.copyProperties(request, requestType);
            GetPreviewInfoResponse responseType = client.getPreviewInfo(requestType);


            if (Objects.nonNull(responseType.getResponseBaseData())) {
                GetResponseBaseDataType responseBaseData = new GetResponseBaseDataType();
                BeanUtils.copyProperties(responseType.getResponseBaseData(), responseBaseData);
                response.setResponseBaseData(responseBaseData);
            }
            if (Objects.nonNull(responseType.getPreviewInfo())) {
                PreviewInfo previewInfo = new PreviewInfo();
                BeanUtils.copyProperties(responseType.getPreviewInfo(), previewInfo);
                response.setPreviewInfo(previewInfo);
            }
        } catch (ServiceException e) {
            response.setResponseStatus(NestedExceptionUtils.responseFail(e));
        }
        return response;

    }


}
