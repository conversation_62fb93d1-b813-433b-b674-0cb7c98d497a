package com.ctrip.tour.business.dashboard.sightArchives.proxy;


import com.ctrip.basebiz.geolocation.service.*;
import com.ctrip.tour.business.dashboard.utils.UserUtil;
import com.ctriposs.baiji.rpc.mobile.common.types.MobileRequestHead;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 地理信息接口
 */
@Component
@Slf4j
public class GeoLocationServiceProxy {

    GeoLocationServiceClient client = GeoLocationServiceClient.getInstance();

    //酒店系id转攻略系id
    public Long transferAreaId(Long globalid, Integer geocategoryid) {

        MapRequestType requestType = new MapRequestType();
        requestType.setGeocategoryid(geocategoryid);  //1-国家  2-省份  3-城市
        requestType.setGlobalid(globalid);
        requestType.setType("base");  //酒店系
        requestType.setLocale("zh_CN");
        MapResponseType responseType = null;
        try {
            log.info("transferHtlAreaIdTo requestType: {}", requestType);
            responseType = client.map(requestType);
            log.info("transferHtlAreaIdTo responseType: {}", responseType);
        } catch (Exception e) {
            log.warn("transferHtlAreaIdTo failed", e);
            return null;
        }
//        if(responseType.getResultCode()==0){
//            log.warn("transferHtlAreaIdTo failed, resultCode:{}, resultMsg:{}",responseType.getResultCode(),responseType.getResultMsg());
//            return null;
//        }

        List<Location> locationList = responseType.getLocations();
        for(Location location : locationList){
            if("gs_district".equals(location.getType())){  //攻略系
                return location.getGlobalid();
            }
        }
        //按省份查没查到，改成用城市查
        if(geocategoryid==2){
            return transferAreaId(globalid,3);
        }

        log.warn("transferHtlAreaIdTo failed, locationList:{}",locationList);
        return null;
    }

    /**
     * 传入地理信息id来获取翻译地理信息翻译结果
     * @param locationIdList id列表
     * @param geocategoryid 1-国家  2-省份  3-城市
     * @return
     */
    public Map<Long,String> getLocationTranslateResult(List<Long> locationIdList,Integer geocategoryid) {
        if(CollectionUtils.isEmpty(locationIdList) || geocategoryid==null ){
            return new HashMap<>();
        }
        locationIdList = new ArrayList<>(new HashSet<>(locationIdList)); //去重

        GetLocationInfosRequestType requestType = new GetLocationInfosRequestType();
        requestType.setLocale(UserUtil.getVbkLocale());

        MobileRequestHead head = new MobileRequestHead();
        head.setLang(UserUtil.getVbkLocale());
        requestType.setHead(head);

        List<LocationKey> locationKeyList = new ArrayList<>();
        requestType.setKeys(locationKeyList);
        for (Long locationId : locationIdList) {
            LocationKey locationKey = new LocationKey();
            locationKey.setGlobalid(locationId);
            locationKey.setGeocategoryid(geocategoryid);  //1-国家  2-省份  3-城市
            locationKey.setType("base");
            locationKeyList.add(locationKey);
        }
        //50个LocationKey一组调用getLocationInfos
        List<List<LocationKey>> partitionedKeys = new ArrayList<>();
        for (int i = 0; i < locationKeyList.size(); i += 50) {
            int end = Math.min(i + 50, locationKeyList.size());
            partitionedKeys.add(locationKeyList.subList(i, end));
        }
        Map<Long,String> resultMap = new HashMap<>();
        for(List<LocationKey> keys : partitionedKeys) {
            requestType.setKeys(keys);
            Map<Long,String> map = getLocationInfosPartitioned(requestType);
            if(!map.isEmpty()){
                resultMap.putAll(map);
            }
        }

        return resultMap;
    }

    private Map<Long,String> getLocationInfosPartitioned(GetLocationInfosRequestType requestType) {
        GetLocationInfosResponseType responseType = null;
        try {
            log.info("getLocationInfos requestType: {}", requestType);
            responseType = client.getLocationInfos(requestType);
            log.info("getLocationInfos responseType: {}", responseType);
        } catch (Exception e) {
            log.warn("getLocationInfos failed, requestType: {}", requestType, e);
            return Collections.emptyMap();
        }
        if(responseType==null || CollectionUtils.isEmpty(responseType.getLocationInfos())) {
            log.warn("getLocationInfos responseType is null or empty, requestType: {}", requestType);
            return Collections.emptyMap();
        }
        List<LocationInfo> locationInfoList = responseType.getLocationInfos();

        Map<Long,String> map = new HashMap<>();
        for(LocationInfo locationInfo : locationInfoList){
            if(StringUtils.isNotBlank(locationInfo.getEname())){
                map.put(locationInfo.getGlobalid(), locationInfo.getEname());
            }
        }
        return map;
    }


}
