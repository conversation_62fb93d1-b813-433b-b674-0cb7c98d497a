package com.ctrip.tour.business.dashboard.sightArchives.entity.domesticMetricEntity;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;

@Entity
@Getter
@Setter
public class GmvProfitTargetRegionBO {
    //业务大区名称
    String businessRegionName;
    //业务子区域名称
    String businessSubRegionName;
    //景点名称
    String vstId;
    String vstName;
    // bd名称
    String examinee;
    //目标收入
    Long ttdTrgt1;
    Long ttdTrgt2;
    Long ttdTrgt3;
    Long ttdTrgt4;
    Long ttdTrgt5;
    Long ttdTrgt6;
    Long ttdTrgt7;
    Long ttdTrgt8;
    Long ttdTrgt9;
    Long ttdTrgt10;
    Long ttdTrgt11;
    Long ttdTrgt12;
}
