package com.ctrip.tour.business.dashboard.tktBusiness.bean;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ContractHyperLinkBean {
    String head; // 表头字段
    int index;   // 该表头字段所在的索引位置
    int type;    // 选择哪种 超链接外显内容 的方式；1：传参内容  2：自定义字符串
    int apparentIndex;  // 通过传的字段索引[headers对应列表的索引位置，可以固定]来设置外显内容
    String custom;      // 自定义内容
}
