package com.ctrip.tour.business.dashboard.sightArchives.service.Impl;

import com.alibaba.fastjson.JSONObject;
import com.ctrip.basebiz.geolocation.service.GeoLocationService;
import com.ctrip.basebiz.geolocation.service.GeoLocationServiceClient;
import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.sightArchives.dao.commonDao.AdmPrdTtdCpdTripDimInfoUnifiedOutputDao;
import com.ctrip.tour.business.dashboard.sightArchives.dao.commonDao.AdmPrdTtdCpdTripDimInfoUnifiedOutputSRDao;
import com.ctrip.tour.business.dashboard.sightArchives.dao.marketDao.AdmOrdTtdViewspotArchivesFhttSaleSummaryDfDao;
import com.ctrip.tour.business.dashboard.sightArchives.entity.commonEntity.AdmPrdTtdCpdTripDimInfoUnifiedOutput;
import com.ctrip.tour.business.dashboard.sightArchives.proxy.GeoLocationServiceProxy;
import com.ctrip.tour.business.dashboard.sightArchives.proxy.HotEventInfoFlowServiceProxy;
import com.ctrip.tour.business.dashboard.sightArchives.service.CommonService;
import com.ctrip.tour.business.dashboard.sightArchives.service.MarketService;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardUpdatetimeDao;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.text.ParseException;
import java.util.*;

@Service
@Slf4j
public class MarketServiceImpl implements MarketService {

    @Autowired
    private HotEventInfoFlowServiceProxy hotEventInfoFlowServiceProxy;
    @Autowired
    AdmPrdTtdCpdTripDimInfoUnifiedOutputSRDao admPrdTtdCpdTripDimInfoUnifiedOutputSRDao;
    @Autowired
    AdmOrdTtdViewspotArchivesFhttSaleSummaryDfDao admOrdTtdViewspotArchivesFhttSaleSummaryDfDao;
    @Autowired
    private GeoLocationServiceProxy geoLocationServiceProxy;
    @Autowired
    private BusinessDashboardUpdatetimeDao businessDashboardUpdatetimeDao;
    @Autowired
    private CommonService commonService;

    @Override
    public GetLocationHeatForecastResponseType getLocationHeatForecast(GetLocationHeatForecastRequestType requestType) {
        SightArchivesCommonFilter commonFilter = requestType.getCommonFilter();
        Long sightId = commonFilter.getSightId();

        String queryD = commonService.getQueryD();
        log.info("queryD={}", queryD);

        //查询景点信息
        AdmPrdTtdCpdTripDimInfoUnifiedOutput sightInfo = admPrdTtdCpdTripDimInfoUnifiedOutputSRDao.querySightInfo(sightId,queryD);
        log.info("sightInfo={}", JSONObject.toJSONString(sightInfo));

        if(sightInfo==null||sightInfo.getIsDomestic()==null){
            return new GetLocationHeatForecastResponseType();
        }
        String areaName = "";
        // 1-城市  2-国家
        int areaType = 1;
        if(sightInfo.getIsDomestic()==1){
            areaName = sightInfo.getCityName();
        }else {
            areaName = sightInfo.getCountryName();
            areaType = 2;
        }
        String startDate = "";
        String endDate = "";
        String yesterday = "";
        try {
            startDate = DateUtil.getDayOfInterval(queryD, -30);
            yesterday = DateUtil.getDayOfInterval(queryD,-1);
            endDate = DateUtil.getDayOfInterval(queryD, 30);
        } catch (ParseException e) {
            log.warn("getDayOfInterval error", e);
        }

        //查询历史热度
        List<Map<String,Object>> historyHeatList = admOrdTtdViewspotArchivesFhttSaleSummaryDfDao.queryHistoryHeatList(queryD, areaName,areaType,startDate,yesterday);
        //查询历史热度同比
        List<Map<String,Object>> historyHeatListPop = admOrdTtdViewspotArchivesFhttSaleSummaryDfDao.queryHistoryHeatListPop(queryD, areaName,areaType,startDate,yesterday);
        log.info("historyHeatList={}, historyHeatListPop={} ", JSONObject.toJSONString(historyHeatList), JSONObject.toJSONString(historyHeatListPop));
        List<Map<String, Object>> maps = mergeLists(historyHeatList, historyHeatListPop);
        List<LocationHeatForecastTrendLineItem> locationHeatForecastTrendLineItemList = new ArrayList<>();
        for(Map<String,Object> map : maps){
            LocationHeatForecastTrendLineItem item = new LocationHeatForecastTrendLineItem();
            item.setDate(String.valueOf(map.get("use_date")));
            item.setAirplaneTicketHeat((map.get("flt_suc_qty") == null ? 0 : Double.parseDouble(String.valueOf(map.get("flt_suc_qty")))));
            item.setHotelTicketHeat((map.get("htl_suc_qty") == null ? 0 : Double.parseDouble(String.valueOf(map.get("htl_suc_qty")))));
            item.setTrainTicketHeat((map.get("trn_suc_qty") == null ? 0 : Double.parseDouble(String.valueOf(map.get("trn_suc_qty")))));
            item.setCityHeat(item.getAirplaneTicketHeat()+item.getHotelTicketHeat()+item.getTrainTicketHeat()); //城市热度 = 机票热度 + 酒店热度 + 火车票热度
            double CityHeatYoy = (map.get("flt_suc_qty_pop") == null ? 0 : Double.parseDouble(String.valueOf(map.get("flt_suc_qty_pop"))))+
                    (map.get("htl_suc_qty_pop") == null ? 0 : Double.parseDouble(String.valueOf(map.get("htl_suc_qty_pop"))));
            //同比口径下，不要火车票热度
            double cityHeatCompareValue = item.getAirplaneTicketHeat() + item.getHotelTicketHeat();
//                    (map.get("trn_suc_qty_pop") == null ? 0 : Double.parseDouble(String.valueOf(map.get("trn_suc_qty_pop"))));
            item.setCityHeatYoy(CityHeatYoy == 0? 0:(cityHeatCompareValue-CityHeatYoy)/CityHeatYoy); //同比
            locationHeatForecastTrendLineItemList.add(item);
        }

        List<LocationHeatForecastTrendLineItem> forecastHeatList = admOrdTtdViewspotArchivesFhttSaleSummaryDfDao.queryForecastHeatList(queryD, areaName,areaType,queryD,endDate);
        locationHeatForecastTrendLineItemList.addAll(forecastHeatList);

        //对locationHeatForecastTrendLineItemList进行排序
        locationHeatForecastTrendLineItemList.sort(Comparator.comparing(LocationHeatForecastTrendLineItem::getDate));

        GetLocationHeatForecastResponseType responseType = new GetLocationHeatForecastResponseType();
        responseType.setHeatForecastTrendLine(locationHeatForecastTrendLineItemList);
        return responseType;
    }

    @Override
    public GetPopularSightsResponseType getPopularSights(GetPopularSightsRequestType requestType) {
        SightArchivesCommonFilter commonFilter = requestType.getCommonFilter();
        Long sightId = commonFilter.getSightId();

        String queryD = commonService.getQueryD();

        //查询景点信息
        AdmPrdTtdCpdTripDimInfoUnifiedOutput sightInfo = admPrdTtdCpdTripDimInfoUnifiedOutputSRDao.querySightInfo(sightId,queryD);
        if(sightInfo==null||sightInfo.getIsDomestic()==null){
            return new GetPopularSightsResponseType();
        }
        // 1-同城、2-同省、3-全国
        Integer rankScope = requestType.getRankScope();
        //需调接口将酒店系id转为攻略系id
        Long districtId = null;
        if(rankScope==1){
            //同城
            districtId = geoLocationServiceProxy.transferAreaId(sightInfo.getCityId(),3);
        }else if(rankScope==2){
            //同省
            districtId = geoLocationServiceProxy.transferAreaId(sightInfo.getProvinceId(),2);

        }else {
            //全国
            districtId = geoLocationServiceProxy.transferAreaId(sightInfo.getCountryId(),1);
        }
        if(districtId==null){
            return new GetPopularSightsResponseType();
        }
        List<PopularSight> popularSightList = new ArrayList<>();
        try {
            popularSightList = hotEventInfoFlowServiceProxy.getInfoFlowListForExternal(districtId,requestType.getRankType());
        }catch (Exception e){
            log.warn("getPopularSights error", e);
        }


        GetPopularSightsResponseType responseType = new GetPopularSightsResponseType();
        responseType.setPopularSightList(popularSightList);
        return responseType;
    }

    public static List<Map<String, Object>> mergeLists(List<Map<String, Object>> list1, List<Map<String, Object>> list2) {
        Map<String, Map<String, Object>> dateToMap = new HashMap<>();

        for (Map<String, Object> map : list1) {
            String useDate = (String) map.get("use_date");
            dateToMap.put(useDate, new HashMap<>(map));
        }

        for (Map<String, Object> map : list2) {
            String useDate = (String) map.get("use_date");
            if (dateToMap.containsKey(useDate)) {
                dateToMap.get(useDate).putAll(map);
            } else {
                dateToMap.put(useDate, new HashMap<>(map));
            }
        }

        return new ArrayList<>(dateToMap.values());
    }
}
