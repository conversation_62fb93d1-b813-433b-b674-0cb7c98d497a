package com.ctrip.tour.business.dashboard.tktBusiness.strategy.impl

import com.ctrip.soa._24922.CheckUserPermissionResponseType
import com.ctrip.soa._24922.GetTrendLineDataRequestType
import com.ctrip.soa._24922.GetTrendLineDataResponseType
import com.ctrip.soa._24922.MetricDetailInfo
import com.ctrip.soa._24922.TimeFilter
import com.ctrip.soa._27181.BIBaseReportQueryServiceClient
import com.ctrip.soa._27181.GetRawDataResponseType
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean
import com.ctrip.tour.business.dashboard.tktBusiness.biz.UserPermissionBiz
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig
import com.ctrip.tour.business.dashboard.utils.MapperUtil
import spock.lang.Specification
import spock.lang.Unroll


class Bus1MetricStrategyTest extends Specification {

    def bus1TicketActivityStrategy = Mock(Bus1TicketActivityStrategy)

    def bus1And2DayTourStrategy = Mock(Bus1And2DayTourStrategy)

    def userPermissionBiz = Mock(UserPermissionBiz)

    def remoteConfig = Mock(RemoteConfig)

    def baseReportQueryServiceClient = Mock(BIBaseReportQueryServiceClient)

    def bus1MetricStrategy = new Bus1MetricStrategy(bus1TicketActivityStrategy:bus1TicketActivityStrategy, bus1And2DayTourStrategy:bus1And2DayTourStrategy, userPermissionBiz:userPermissionBiz,
            remoteConfig:remoteConfig, baseReportQueryServiceClient:baseReportQueryServiceClient)

//    def "测试指标卡接口"() {
//        given: "设置mock数据结果"
//        String permissionStr1 = "{\"domesticBasicConfig\":{\"metricCardConfigMap\":{\"1\":{\"subMetricList\":[\"ticketActivity+domesticDayTour+overseaDayTour\",\"ticketActivity\",\"domesticDayTour\",\"overseaDayTour\"]},\"2\":{\"subMetricList\":[\"ticketActivity+domesticDayTour+overseaDayTour\",\"ticketActivity\",\"domesticDayTour\",\"overseaDayTour\"]}},\"trendLineConfigMap\":{\"1\":{\"subMetricList\":[\"ticketActivity+domesticDayTour+overseaDayTour\",\"ticketActivity+domesticDayTour\",\"ticketActivity\",\"domesticDayTour\",\"overseaDayTour\"]},\"2\":{\"subMetricList\":[\"ticketActivity+domesticDayTour+overseaDayTour\",\"ticketActivity+domesticDayTour\",\"ticketActivity\",\"domesticDayTour\",\"overseaDayTour\"]}}}}"
//        String permissionStr2 = "{\"domesticBasicConfig\":{\"metricCardConfigMap\":{\"1\":{\"subMetricList\":[\"ticketActivity\"]},\"2\":{\"subMetricList\":[\"ticketActivity\"]}},\"trendLineConfigMap\":{\"1\":{\"subMetricList\":[\"ticketActivity\"]},\"2\":{\"subMetricList\":[\"ticketActivity\"]}}}}"
//        String permissionStr3 = "{\"domesticBasicConfig\":{\"metricCardConfigMap\":{\"1\":{\"subMetricList\":[\"domesticDayTour\"]},\"2\":{\"subMetricList\":[\"domesticDayTour\"]}},\"trendLineConfigMap\":{\"1\":{\"subMetricList\":[\"domesticDayTour\"]},\"2\":{\"subMetricList\":[\"domesticDayTour\"]}}}}"
//
//        userPermissionBiz.checkUserPermission(_) >> MapperUtil.str2Obj(permissionStr1, CheckUserPermissionResponseType.class) >> MapperUtil.str2Obj(permissionStr2, CheckUserPermissionResponseType.class) >> MapperUtil.str2Obj(permissionStr3, CheckUserPermissionResponseType.class)
//
//
//        String ticketActivityStr = "{\"subMetric\":\"ticketActivity\",\"dimData\":{\"ttd_suc_income\":2.3763997211E8,\"ttd_suc_income|ttd_trgt_income|/\":0.****************,\"ttd_trgt_income\":4.80214511E8,\"ttd_suc_income_lastyear_value\":4.416363988E7,\"ttd_suc_income_lastyear\":4.380896428729778,\"ttd_suc_income_2019\":0.8662397875002441,\"ttd_suc_income_7days_fenmu_value\":1.5254442555E8,\"ttd_suc_income_7days_fenzi_value\":1.2041633642E8,\"ttd_suc_income_2019_value\":1.2733624784E8,\"ttd_suc_income_7days\":-0.2106146390742366},\"momType\":\"7days\",\"needDrillDown\":true,\"defaultField\":\"大区\"}"
//        bus1TicketActivityStrategy.getSingleMetricCardData(_, _, _, _) >> MapperUtil.str2Obj(ticketActivityStr, MetricDetailInfo.class)
//
//        String domesticDayTourStr = "{\"subMetric\":\"domesticDayTour\",\"dimData\":{\"odt_suc_income_7days_fenmu_value\":1.1720927E7,\"odt_sys_inner_profit\":2286214.32,\"odt_suc_income|odt_trgt_income|/\":0.33553310743520076,\"odt_suc_income_lastyear_value\":1255720.15,\"odt_sys_outer_profit\":0.0,\"odt_trgt_income\":5.6946317E7,\"odt_suc_income_7days_fenzi_value\":1.042000529E7,\"odt_suc_income\":1.91073747E7,\"odt_trgt_profit\":6176941.0,\"odt_suc_income_2019_value\":2.903573042E7,\"odt_suc_profit\":2286214.32,\"odt_suc_income_2019\":-0.34193580035311544,\"odt_suc_income_7days\":-0.11099136697976197,\"odt_suc_income_lastyear\":14.21626829035116},\"momType\":\"7days\"}"
//        bus1And2DayTourStrategy.getSingleMetricCardData(_, _, _, _, "domesticDayTour") >> MapperUtil.str2Obj(domesticDayTourStr, MetricDetailInfo.class)
//
//        String overseaDayTourStr = "{\"subMetric\":\"overseaDayTour\",\"dimData\":{\"odt_ob_suc_profit\":2286214.32,\"odt_ob_trgt_profit\":6176941.0,\"odt_ob_suc_income_lastyear_value\":1255720.15,\"odt_ob_suc_income\":1.91073747E7,\"odt_ob_trgt_income\":5.6946317E7,\"odt_ob_suc_income_7days_fenmu_value\":1.1720927E7,\"odt_ob_suc_income_7days\":-0.11099136697976197,\"odt_ob_suc_income|odt_ob_trgt_income|/\":0.33553310743520076,\"odt_ob_suc_income_2019\":-0.34193580035311544,\"odt_ob_sys_inner_profit\":2286214.32,\"odt_ob_suc_income_7days_fenzi_value\":1.042000529E7,\"odt_ob_sys_outer_profit\":0.0,\"odt_ob_suc_income_lastyear\":14.21626829035116,\"odt_ob_suc_income_2019_value\":2.903573042E7},\"momType\":\"7days\"}"
//        bus1And2DayTourStrategy.getSingleMetricCardData(_, _, _, _, "overseaDayTour") >> MapperUtil.str2Obj(overseaDayTourStr, MetricDetailInfo.class)
//
//        String rankingStr = "{\"result\":\"[[\\\"80/134\\\",\\\"63/134\\\"]]\",\"metricList\":[\"ranking_gmv\",\"ranking_profit\"]}"
//        baseReportQueryServiceClient.getRawData(_) >> MapperUtil.str2Obj(rankingStr, GetRawDataResponseType.class)
//
//
//
//        and: "设置请求参数"
//        TimeFilter timeFilter = new TimeFilter(dateType: "month", month: "11", year: "2023")
//        MetricInfoBean metricInfoBean = new MetricInfoBean(level: "国内", odtLevel: "国内", overseaOdtLevel: "国内");
//
//        and: "设置qconfig配置"
//        remoteConfig.getConfigValue("domestic") >> "国内"
//        remoteConfig.getConfigValue("region") >> "大区"
//        remoteConfig.getConfigValue("province") >> "省份"
//
//
//        when: "调用指标卡接口"
//        def response1 = bus1MetricStrategy.getSingleMetricCardData("zheli", timeFilter, metricInfoBean, "2023-11-16", false).get()
//        def response2 = bus1MetricStrategy.getSingleMetricCardData("zheli", timeFilter, metricInfoBean, "2023-11-16", true).get()
//        def response3 = bus1MetricStrategy.getSingleMetricCardData("zheli", timeFilter, metricInfoBean, "2023-11-16", true).get()
//
//        then: "验证返回结果"
//        with(response1) {
//            subMetric == "ticketActivity+domesticDayTour+overseaDayTour"
//            defaultField == "大区"
//            dimData.size() == 6
//            subMetricDetailInfoList.size() == 3
//        }
//
//        with(response2) {
//            subMetric == "ticketActivity"
//            defaultField == "大区"
//            dimData.size() == 10
//            subMetricDetailInfoList.size() == 1
//            rank == "80/134"
//        }
//
//        with(response3) {
//            subMetric == "domesticDayTour"
//            defaultField == "大区"
//            dimData.size() == 10
//            subMetricDetailInfoList.size() == 1
//            rank == "80/134"
//        }
//
//
//    }

//    @Unroll
//    def "测试趋势线接口"() {
//        given: "设置mock数据结果"
//        def ticketActivityStr = "{\"level\":\"国内\",\"trendLineDetailInfoList\":[{\"type\":\"barChart\",\"dim\":\"ttd_suc_income\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":8.5912346509E8},{\"time\":\"2023-10\",\"value\":1.52729176812E9},{\"time\":\"2023-11\",\"value\":2.3763997211E8}]},{\"type\":\"lineChart\",\"dim\":\"ttd_suc_income|ttd_trgt_income|/\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":1.1100655491760725},{\"time\":\"2023-10\",\"value\":1.0320506744437166},{\"time\":\"2023-11\",\"value\":0.****************}]},{\"type\":\"barChart\",\"dim\":\"ttd_trgt_income\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":7.739394E8},{\"time\":\"2023-10\",\"value\":1.479861218E9},{\"time\":\"2023-11\",\"value\":4.80214511E8}]},{\"type\":\"lineChart\",\"dim\":\"ttd_suc_income_lastyear\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":1.8522108323366164},{\"time\":\"2023-10\",\"value\":2.1110146120720885},{\"time\":\"2023-11\",\"value\":4.380896428729778}]},{\"type\":\"lineChart\",\"dim\":\"ttd_suc_income_lastyear_value\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":3.0121316957E8},{\"time\":\"2023-10\",\"value\":4.9093043864E8},{\"time\":\"2023-11\",\"value\":4.416363988E7}]},{\"type\":\"lineChart\",\"dim\":\"ttd_suc_income_2019\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":1.616883811835848},{\"time\":\"2023-10\",\"value\":0.9565035030236297},{\"time\":\"2023-11\",\"value\":0.8662397875002441}]},{\"type\":\"lineChart\",\"dim\":\"ttd_suc_income_2019_value\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":3.2830019476E8},{\"time\":\"2023-10\",\"value\":7.8062306853E8},{\"time\":\"2023-11\",\"value\":1.2733624784E8}]}]}"
//        bus1TicketActivityStrategy.getSingleTrendlineData(_, _, _) >> MapperUtil.str2Obj(ticketActivityStr, GetTrendLineDataResponseType.class)
//
//
//        String domesticDayTourStr = "{\"trendLineDetailInfoList\":[{\"type\":\"lineChart\",\"dim\":\"odt_suc_income|odt_trgt_income|/\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":0.9377420023167349},{\"time\":\"2023-10\",\"value\":1.043053367482866},{\"time\":\"2023-11\",\"value\":0.33553310743520076}]},{\"type\":\"lineChart\",\"dim\":\"odt_trgt_income\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":8.2869217E7},{\"time\":\"2023-10\",\"value\":1.26100701E8},{\"time\":\"2023-11\",\"value\":5.6946317E7}]},{\"type\":\"barChart\",\"dim\":\"odt_suc_income\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":7.770994548E7},{\"time\":\"2023-10\",\"value\":1.3152976082E8},{\"time\":\"2023-11\",\"value\":1.91073747E7}]},{\"type\":\"lineChart\",\"dim\":\"odt_suc_income_lastyear_value\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":1.198897647E7},{\"time\":\"2023-10\",\"value\":1.300893568E7},{\"time\":\"2023-11\",\"value\":1255720.15}]},{\"type\":\"lineChart\",\"dim\":\"odt_suc_income_lastyear\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":5.481783134236146},{\"time\":\"2023-10\",\"value\":9.11072420184339},{\"time\":\"2023-11\",\"value\":14.21626829035116}]},{\"type\":\"lineChart\",\"dim\":\"odt_suc_income_2019_value\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":8.255666642E7},{\"time\":\"2023-10\",\"value\":1.2834274975E8},{\"time\":\"2023-11\",\"value\":2.903573042E7}]},{\"type\":\"lineChart\",\"dim\":\"odt_suc_income_2019\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":-0.0587078082264455},{\"time\":\"2023-10\",\"value\":0.024832030451334308},{\"time\":\"2023-11\",\"value\":-0.34193580035311544}]}]}"
//        bus1And2DayTourStrategy.getSingleTrendlineData({ i -> i.subMetric == "domesticDayTour" }, _) >> MapperUtil.str2Obj(domesticDayTourStr, GetTrendLineDataResponseType.class)
//
//        String overseaDayTourStr = "{\"trendLineDetailInfoList\":[{\"type\":\"lineChart\",\"dim\":\"odt_ob_suc_income|odt_ob_trgt_income|/\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":0.9377420023167349},{\"time\":\"2023-10\",\"value\":1.043053367482866},{\"time\":\"2023-11\",\"value\":0.33553310743520076}]},{\"type\":\"barChart\",\"dim\":\"odt_ob_suc_income\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":7.770994548E7},{\"time\":\"2023-10\",\"value\":1.3152976082E8},{\"time\":\"2023-11\",\"value\":1.91073747E7}]},{\"type\":\"lineChart\",\"dim\":\"odt_ob_trgt_income\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":8.2869217E7},{\"time\":\"2023-10\",\"value\":1.26100701E8},{\"time\":\"2023-11\",\"value\":5.6946317E7}]},{\"type\":\"lineChart\",\"dim\":\"odt_ob_suc_income_lastyear_value\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":1.198897647E7},{\"time\":\"2023-10\",\"value\":1.300893568E7},{\"time\":\"2023-11\",\"value\":1255720.15}]},{\"type\":\"lineChart\",\"dim\":\"odt_ob_suc_income_lastyear\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":5.481783134236146},{\"time\":\"2023-10\",\"value\":9.11072420184339},{\"time\":\"2023-11\",\"value\":14.21626829035116}]},{\"type\":\"lineChart\",\"dim\":\"odt_ob_suc_income_2019\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":-0.0587078082264455},{\"time\":\"2023-10\",\"value\":0.024832030451334308},{\"time\":\"2023-11\",\"value\":-0.34193580035311544}]},{\"type\":\"lineChart\",\"dim\":\"odt_ob_suc_income_2019_value\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":8.255666642E7},{\"time\":\"2023-10\",\"value\":1.2834274975E8},{\"time\":\"2023-11\",\"value\":2.903573042E7}]}]}"
//        bus1And2DayTourStrategy.getSingleTrendlineData({ i -> i.subMetric == "overseaDayTour" }, _) >> MapperUtil.str2Obj(overseaDayTourStr, GetTrendLineDataResponseType.class)
//
//
//        and: "设置请求参数"
//        def request = new GetTrendLineDataRequestType(domainName: "zheli", metric: "1", subMetric: subMetric, timeFilter: new TimeFilter(month: 11, year: 2023, dateType: "month", timeFrame: 3))
//        def metricInfoBean = new MetricInfoBean(level: "国内", odtLevel: "国内", overseaOdtLevel: "国内");
//
//        when: "调用指标卡接口"
//        def response = bus1MetricStrategy.getSingleTrendlineData(request, metricInfoBean, "2023-11-16")
//
//        then: "验证结果"
//        with(response){
//            trendLineDetailInfoList.size() == actualSize
//            level == actualLevel
////            trendLineDetailInfoList.get(0).getTrendLineDataItemList().size() == 3
//        }
//
//
//        where: "用表格方式验证多种返回结果"
//        subMetric                                       | actualSize | actualLevel
//        "ticketActivity+domesticDayTour+overseaDayTour" | 4          | null
//        "ticketActivity+domesticDayTour"                | 4          | null
//        "ticketActivity"                                | 7          | "国内"
//        "domesticDayTour"                               | 7          | null
//        "overseaDayTour"                                | 7          | null
//
//    }


}
