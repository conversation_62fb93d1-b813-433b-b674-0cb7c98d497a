package com.ctrip.tour.business.dashboard.tktBusiness.select43556;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2023-03-28
 */
@Entity
@Database(name = "TtdReportDB_W")
@Table(name = "report_query_whitelist")
public class ReportQueryWhitelist implements DalPojo {

	/**
	 * 主键
	 */
	@Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long id;

	/**
	 * 主配置id
	 */
	@Column(name = "query_id")
	@Type(value = Types.BIGINT)
	private Long queryId;

	/**
	 * 库名
	 */
	@Column(name = "dbname")
	@Type(value = Types.VARCHAR)
	private String dbname;

	/**
	 * 表名
	 */
	@Column(name = "tablename")
	@Type(value = Types.VARCHAR)
	private String tablename;

	/**
	 * 列名 用于做白名单匹配
	 */
	@Column(name = "column_name")
	@Type(value = Types.VARCHAR)
	private String columnName;

	/**
	 * 字段类型(0:INTEGER;1:BIGINT;2:VARCHAR)
	 */
	@Column(name = "column_type")
	@Type(value = Types.INTEGER)
	private Integer columnType;

	/**
	 * 指标的计算方式
	 */
	@Column(name = "calculate_type")
	@Type(value = Types.VARCHAR)
	private String calculateType;


	/**
	 * 修改时间
	 */
	@Column(name = "DataChange_LastTime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;

	/**
	 * 字段对应的拼接条件 1  in 2  between  and 3  not in
	 */
	@Column(name = "column_condition")
	@Type(value = Types.INTEGER)
	private Integer columnCondition;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getQueryId() {
		return queryId;
	}

	public void setQueryId(Long queryId) {
		this.queryId = queryId;
	}

	public String getDbname() {
		return dbname;
	}

	public void setDbname(String dbname) {
		this.dbname = dbname;
	}

	public String getTablename() {
		return tablename;
	}

	public void setTablename(String tablename) {
		this.tablename = tablename;
	}

	public String getColumnName() {
		return columnName;
	}

	public void setColumnName(String columnName) {
		this.columnName = columnName;
	}

	public Integer getColumnType() {
		return columnType;
	}

	public void setColumnType(Integer columnType) {
		this.columnType = columnType;
	}

	public String getCalculateType() {
		return calculateType;
	}

	public void setCalculateType(String calculateType) {
		this.calculateType = calculateType;
	}


	public Timestamp getDatachangeLasttime() {
		return datachangeLasttime;
	}

	public void setDatachangeLasttime(Timestamp datachangeLasttime) {
		this.datachangeLasttime = datachangeLasttime;
	}

	public Integer getColumnCondition() {
		return columnCondition;
	}

	public void setColumnCondition(Integer columnCondition) {
		this.columnCondition = columnCondition;
	}

}
