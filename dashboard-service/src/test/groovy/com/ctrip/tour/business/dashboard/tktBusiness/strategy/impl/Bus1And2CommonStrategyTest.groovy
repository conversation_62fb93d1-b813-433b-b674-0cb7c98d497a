package com.ctrip.tour.business.dashboard.tktBusiness.strategy.impl

import com.ctrip.soa._24922.TimeFilter
import com.ctrip.soa._27181.BIBaseReportQueryServiceClient
import com.ctrip.soa._27181.GetRawDataResponseType
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean
import com.ctrip.tour.business.dashboard.tktBusiness.biz.SinglePeriodTrendLineBiz
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardExamineeConfigV2Dao
import com.ctrip.tour.business.dashboard.utils.MapperUtil
import com.google.common.util.concurrent.JdkFutureAdapters
import org.springframework.scheduling.annotation.AsyncResult
import spock.lang.Specification

class Bus1And2CommonStrategyTest extends Specification {

    def remoteConfig = Mock(RemoteConfig)

    def baseReportQueryServiceClient = Mock(BIBaseReportQueryServiceClient)

    def examineeConfigV2Dao = Mock(BusinessDashboardExamineeConfigV2Dao)

    def singlePeriodTrendLineBiz = Mock(SinglePeriodTrendLineBiz)

    def bus1And2DayTourStrategy = new Bus1And2DayTourStrategy(remoteConfig: remoteConfig, baseReportQueryServiceClient: baseReportQueryServiceClient, examineeConfigV2Dao: examineeConfigV2Dao, singlePeriodTrendLineBiz: singlePeriodTrendLineBiz)



//    def "测试指标卡接口"() {
//
//        given: "设置请求参数"
//        def timeFilter = new TimeFilter(year: "2023", dateType: "month", month: "11")
//        def metricInfoBean = new MetricInfoBean(odtLevel: "国内", overseaOdtLevel: "国内")
//
//        and: "设置qconfig请求参数"
//        remoteConfig.getConfigValue("domestic") >> "国内"
//        remoteConfig.getConfigValue("region") >> "大区"
//        remoteConfig.getConfigValue("province") >> "省份"
//
//
//        and: "mock第三方服务调用"
//        def domesticCurrentStr = "{\"result\":\"[[1.91073747E7,2286214.32,2286214.32,0.0]]\",\"metricList\":[\"odt_suc_income\",\"odt_suc_profit\",\"odt_sys_inner_profit\",\"odt_sys_outer_profit\"]}"
//        def domesticTargetStr = "{\"result\":\"[[5.6946317E7,6176941.0]]\",\"metricList\":[\"odt_trgt_income\",\"odt_trgt_profit\"]}"
//        def domesticLastyearStr = "{\"result\":\"[[1255720.15,146438.08,146438.08,0.0]]\",\"metricList\":[\"odt_suc_income\",\"odt_suc_profit\",\"odt_sys_inner_profit\",\"odt_sys_outer_profit\"]}"
//        def domestic2019Str = "{\"result\":\"[[2.903573042E7,2621489.14,2621489.14,0.0]]\",\"metricList\":[\"odt_suc_income\",\"odt_suc_profit\",\"odt_sys_inner_profit\",\"odt_sys_outer_profit\"]}"
//        def domesticMom1Str = "{\"result\":\"[[1.042000529E7,1246734.93,1246734.93,0.0]]\",\"metricList\":[\"odt_suc_income\",\"odt_suc_profit\",\"odt_sys_inner_profit\",\"odt_sys_outer_profit\"]}"
//        def domesticMom2Str = "{\"result\":\"[[1.1720927E7,1397853.83,1397853.83,0.0]]\",\"metricList\":[\"odt_suc_income\",\"odt_suc_profit\",\"odt_sys_inner_profit\",\"odt_sys_outer_profit\"]}"
//
//
//        def overseaCurrentStr = "{\"result\":\"[[1.91073747E7,2286214.32,2286214.32,0.0]]\",\"metricList\":[\"odt_ob_suc_income\",\"odt_ob_suc_profit\",\"odt_ob_sys_inner_profit\",\"odt_ob_sys_outer_profit\"]}"
//        def overseaTargetStr = "{\"result\":\"[[5.6946317E7,6176941.0]]\",\"metricList\":[\"odt_ob_trgt_income\",\"odt_ob_trgt_profit\"]}"
//        def overseaLastyearStr = "{\"result\":\"[[1255720.15,146438.08,146438.08,0.0]]\",\"metricList\":[\"odt_ob_suc_income\",\"odt_ob_suc_profit\",\"odt_ob_sys_inner_profit\",\"odt_ob_sys_outer_profit\"]}"
//        def oversea2019Str = "{\"result\":\"[[2.903573042E7,2621489.14,2621489.14,0.0]]\",\"metricList\":[\"odt_ob_suc_income\",\"odt_ob_suc_profit\",\"odt_ob_sys_inner_profit\",\"odt_ob_sys_outer_profit\"]}"
//        def overseaMom1Str = "{\"result\":\"[[1.042000529E7,1246734.93,1246734.93,0.0]]\",\"metricList\":[\"odt_ob_suc_income\",\"odt_ob_suc_profit\",\"odt_ob_sys_inner_profit\",\"odt_ob_sys_outer_profit\"]}"
//        def overseaMom2Str = "{\"result\":\"[[1.1720927E7,1397853.83,1397853.83,0.0]]\",\"metricList\":[\"odt_ob_suc_income\",\"odt_ob_suc_profit\",\"odt_ob_sys_inner_profit\",\"odt_ob_sys_outer_profit\"]}"
//
//
//        baseReportQueryServiceClient.getRawDataAsync(_) >> getFutureResponse(domesticCurrentStr) >> getFutureResponse(domesticTargetStr) >> getFutureResponse(domesticLastyearStr) >>
//                getFutureResponse(domestic2019Str) >> getFutureResponse(domesticMom1Str) >> getFutureResponse(domesticMom2Str) >> getFutureResponse(overseaCurrentStr) >>
//                getFutureResponse(overseaTargetStr) >> getFutureResponse(overseaLastyearStr) >> getFutureResponse(oversea2019Str) >> getFutureResponse(overseaMom1Str) >>
//                getFutureResponse(overseaMom2Str)
//
//        when: "调用指标卡接口"
//        def response1 = bus1And2DayTourStrategy.getSingleMetricCardData(timeFilter, metricInfoBean, "2023-11-16", "1", "domesticDayTour")
//        def response2 = bus1And2DayTourStrategy.getSingleMetricCardData(timeFilter, metricInfoBean, "2023-11-16", "1", "overseaDayTour")
//
//        then: "验证返回结果"
//        with(response1) {
//            subMetric == "domesticDayTour"
//            dimData.get("odt_suc_income").intValue() == 19107374
//            dimData.get("odt_suc_income_lastyear").intValue() ==  14
//        }
//
//        with(response2) {
//            subMetric == "overseaDayTour"
//            dimData.get("odt_ob_suc_income").intValue() == 19107374
//            dimData.get("odt_ob_suc_income_lastyear").intValue() ==  14
//        }
//
//    }






    def getFutureResponse(def str) {
        def response = MapperUtil.str2Obj(str, GetRawDataResponseType.class)
        return JdkFutureAdapters.listenInPoolThread(new AsyncResult<>(response))
    }
}
