package com.ctrip.tour.business.dashboard.tktBusiness.entity;


import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

@Entity
@Database(name = "TtdReportDB_W")
@Table(name = "m_pkgvendorqualitydb_board_employee ")
public class MPkgVendorQualityDbBoardEmployee {

    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 邮箱前缀
     */
    @Column(name = "domain_name")
    @Type(value = Types.VARCHAR)
    private String domainName;

    /**
     * 是否在职
     */
    @Column(name = "active")
    @Type(value = Types.INTEGER)
    private String active;

    /**
     * 所在大区
     */
    @Column(name = "region")
    @Type(value = Types.VARCHAR)
    private String region;

    /**
     * 工号
     */
    @Column(name = "eid")
    @Type(value = Types.VARCHAR)
    private String eid;

    /**
     * 所属组织id
     */
    @Column(name = "org_id")
    @Type(value = Types.VARCHAR)
    private String orgId;

    /**
     * 直属领导工号
     */
    @Column(name = "leader_eid")
    @Type(value = Types.VARCHAR)
    private String leaderEid;

    /**
     * 员工前缀+员工姓名
     */
    @Column(name = "name")
    @Type(value = Types.VARCHAR)
    private String name;

    /**
     * 员工所属大区id
     */
    @Column(name = "region_id")
    @Type(value = Types.VARCHAR)
    private String regionId;

    /**
     * 分区
     */
    @Column(name = "partition_d")
    @Type(value = Types.VARCHAR)
    private String partitionD;

    /**
     * 最后更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDomainName() {
        return domainName;
    }

    public void setDomainName(String domainName) {
        this.domainName = domainName;
    }

    public String getActive() {
        return active;
    }

    public void setActive(String active) {
        this.active = active;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getEid() {
        return eid;
    }

    public void setEid(String eid) {
        this.eid = eid;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getLeaderEid() {
        return leaderEid;
    }

    public void setLeaderEid(String leaderEid) {
        this.leaderEid = leaderEid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    public String getPartitionD() {
        return partitionD;
    }

    public void setPartitionD(String partitionD) {
        this.partitionD = partitionD;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }
}
