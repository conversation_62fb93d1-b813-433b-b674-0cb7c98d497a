package com.ctrip.tour.business.dashboard.sightArchives.dao.userProfileDao;

import com.ctrip.soa._24922.UserResidenceItem;
import com.ctrip.soa._24922.UserSearchPreferenceSearchKey;
import com.ctrip.soa._27181.PreparedParameterBean;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.TktStarRocksDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.Types;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Repository
public class CdmLogTtdViewspotBenchSearchTrafficDiDao {

    //用户搜索偏好 词云   数仓侧设计文档：http://conf.ctripcorp.com/pages/viewpage.action?pageId=3682998994
    //dw_ticketdb.cdm_log_ttd_viewspot_bench_search_traffic_di


    @Autowired
    private TktStarRocksDao tktStarRocksDao;

    public List<UserSearchPreferenceSearchKey> querySearchKeyList(String startDate, String endDate, Long sightId, Boolean needSubSight) {
        StringBuilder sql = new StringBuilder("select cast(sum(search_uv) as Integer) as search_uv,keyword " +
                "from cdm_log_ttd_viewspot_bench_search_traffic_di");
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendSightId(parameters, sql, sightId, needSubSight);
        appendDateRange(parameters, sql, startDate, endDate);
        sql.append(" group by keyword");
        sql.append(" order by search_uv desc limit 20");
        List<Map<String,Object>> groupbyres = new ArrayList<>();
        try {
            groupbyres = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        if(groupbyres.size() == 0){
            return new ArrayList<>();
        }
        List<UserSearchPreferenceSearchKey> res = new ArrayList<>();
        for(Map<String,Object> map : groupbyres){
            UserSearchPreferenceSearchKey userSearchPreferenceSearchKey = new UserSearchPreferenceSearchKey();
            userSearchPreferenceSearchKey.setSearchKey((String) map.get("keyword"));
            userSearchPreferenceSearchKey.setCount((Integer) map.get("search_uv"));
            res.add(userSearchPreferenceSearchKey);
        }
        return res;
    }

    private void appendSightId(List<PreparedParameterBean> parameters, StringBuilder sql, Long sightId, Boolean needSubSight){
        if(needSubSight){
            sql.append(" where viewspot_id = ?");
        }else {
            sql.append(" where sub_viewspot_id = ?");
        }
        parameters.add(new PreparedParameterBean(String.valueOf(sightId), Types.BIGINT));
    }

    private void appendDateRange(List<PreparedParameterBean> parameters, StringBuilder sql, String startDate, String endDate){

        sql.append(" and d between ? and ?");//done

        parameters.add(new PreparedParameterBean(startDate, Types.VARCHAR));
        parameters.add(new PreparedParameterBean(endDate, Types.VARCHAR));
    }


}
