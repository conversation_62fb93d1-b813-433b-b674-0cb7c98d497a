package com.ctrip.tour.business.dashboard.tktBusiness.strategy.overseaImplV2;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.sightArchives.dao.overseametric.DimOrdTtdTargetConfigDao;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBeanV2;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoWithMetricBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bo.OverSeaExamineConfigBO;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardOverseaExamineeConfigDao;
import com.ctrip.tour.business.dashboard.tktBusiness.enums.BuTypeEnum;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.MultiPeriodMappingHelper;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.subMetricImplV2.OverseaSubMetricCalStategyBizImplV2;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import groovy.util.logging.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Future;

@Component
@Slf4j
public class Bus105And106And107MetricStrategyV2 {


    @Autowired
    private OverseaSubMetricCalStategyBizImplV2 overseaSubMetricCalStategyBiz;

    @Autowired
    private BusinessDashboardOverseaExamineeConfigDao examineeConfigDao;

    @Autowired
    private DimOrdTtdTargetConfigDao dimOrdTtdTargetConfigDao;

    @Autowired
    private RemoteConfig remoteConfig;


    public Future<OveaseaMetric> getOverseaSingleMetricCardData(TimeFilter timeFilter,
                                                                         OverseaMetricInfoBeanV2 metricInfoBean,
                                                                         String d,
                                                                         AvailableSubMetric availableSubMetric,
                                                                         String metric) throws Exception {
        OveaseaMetric oveaseaMetric = new OveaseaMetric();
        oveaseaMetric.setMetric(metric);
        List<String> subMetricList = availableSubMetric.getSubMetricList();
        List<Future<OveaseaSubMetric>> futureList = new ArrayList<>();
        List<OveaseaSubMetric> overSeaMetricsList = new ArrayList<>();
        for (String subMetric : subMetricList) {
            futureList.add(overseaSubMetricCalStategyBiz.getBus105106107SubMetricCardData(timeFilter, metricInfoBean, d, metric, subMetric));
        }
        for (Future<OveaseaSubMetric> futureResult : futureList) {
            overSeaMetricsList.add(futureResult.get());
        }
        oveaseaMetric.setSubMetricList(overSeaMetricsList);
        return new AsyncResult<>(oveaseaMetric);
    }


    public GetOverseaTrendLineDataV2ResponseType getOverseaSingleTrendlineData(GetOverseaTrendLineDataV2RequestType request,
                                                                               String d) throws Exception {
        //最新的数据时间
        List<String> timeList = DateUtil.getAllSelectedTime(request.getTimeFilter(), "oversea");
        return overseaSubMetricCalStategyBiz.getBus105106107SubTrendLineData(request, d, timeList);
    }


    public GetOverseaDrillDownBaseInfoV2ResponseType getOverseaSingleDrillDownBaseInfo(GetOverseaDrillDownBaseInfoV2RequestType request,
                                                                                     String d,
                                                                                       OverseaMetricInfoBean metricInfoBean) throws Exception {
        return overseaSubMetricCalStategyBiz.getBus105106107SubDrillDownBaseInfo(request, d, metricInfoBean);
    }


    public GetOverseaTableDataV2ResponseType getOverseaSingleTableData(GetOverseaTableDataV2RequestType request,
                                                                     String d,
                                                                       OverseaMetricInfoBean metricInfoBean) throws Exception {
        return overseaSubMetricCalStategyBiz.getBus105106107SubTableData(request, d, metricInfoBean);
    }

}
