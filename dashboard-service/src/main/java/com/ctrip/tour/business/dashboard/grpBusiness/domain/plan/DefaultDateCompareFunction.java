package com.ctrip.tour.business.dashboard.grpBusiness.domain.plan;

import com.ctrip.soa._24922.CompareConfig;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.AbstractOrchestrationFunction;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.model.ResultData;
import lombok.Getter;
import lombok.Setter;
import org.joda.time.DateTime;
import org.joda.time.Days;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.HashSet;
import java.util.Arrays;

@Getter
@Setter
public class DefaultDateCompareFunction extends AbstractOrchestrationFunction {
    // 动态环比指标
    static Set<String> defaultDynamicDateCompareSet = new HashSet<>(Arrays.asList("date", "date_week", "date_month"));
    CompareConfig compareConfig; // 同环比配置
    List<String> dims; // 纬度
    boolean needFixDate; // 是否需要时间修复

    // 时间
    DateTime curStartTime;
    DateTime curEndTime;
    // 对比时间
    DateTime compareStartTime;
    DateTime compareEndTime;
    // mapping序列
    Map<String, String> mappingMap = new HashMap<>();


    public DefaultDateCompareFunction() {
        super();
    }

    public DefaultDateCompareFunction(CompareConfig compareConfig, List<String> dims) {
        super();
        this.compareConfig = compareConfig;
        if (dims != null) {
            this.dims = new ArrayList<>(dims).stream()
                    .filter(v -> !defaultDynamicDateCompareSet.contains(v))
                    .collect(Collectors.toList());
        }
        // 初始化时间
        compareStartTime = new DateTime(compareConfig.getCompareDate().get(0));
        compareEndTime = new DateTime(compareConfig.getCompareDate().get(1));
        // 时间
        curStartTime = new DateTime(compareConfig.getCurDate().get(0));
        curEndTime = new DateTime(compareConfig.getCurDate().get(1));
        // 同环比序列
        if (compareConfig.getCompareIndicatorName().equals("date")) {
            DateTime curStartTimeCp = new DateTime(curStartTime);
            while (!curStartTimeCp.isAfter(curEndTime)) {
                switch (compareConfig.getPrefix()) {
                    case "hb":
                    case "month_hb":
                    case "week_hb":
                    case "year_hb":
                        mappingMap.put(curStartTimeCp.toString("yyyy-MM-dd"), curStartTimeCp.toString("yyyy-MM-dd"));
                        break;
                    case "year_tb":
                        // 年同比
                        mappingMap.put(curStartTimeCp.toString("yyyy-MM-dd"), curStartTimeCp.plusYears(-1).toString("yyyy-MM-dd"));
                        break;
                    // 季度同比
                    // 月同比
                    // 周同比
                }
                curStartTimeCp = curStartTimeCp.plusDays(1);
            }
        }
        if (compareConfig.getCompareIndicatorName().equals("date_week")) {
            DateTime curStartTimeCp = curStartTime.plusDays(-curStartTime.getDayOfWeek() + 1);
            DateTime compareStartTimeCp = compareStartTime.plusDays(-compareStartTime.getDayOfWeek() + 1);
            while (!curStartTimeCp.isAfter(curEndTime)) {
                switch (compareConfig.getPrefix()) {
                    case "hb":
                    case "month_hb":
                    case "week_hb":
                    case "year_hb":
                        mappingMap.put(curStartTimeCp.toString("yyyy-MM-dd"), compareStartTimeCp.toString("yyyy-MM-dd"));
                        break;
                    case "year_tb":
                        // 年同比
                        mappingMap.put(curStartTimeCp.toString("yyyy-MM-dd"), compareStartTimeCp.plusYears(-1).toString("yyyy-MM-dd"));
                        break;
                    // 季度同比
                    // 月同比
                    // 周同比
                }
                curStartTimeCp = curStartTimeCp.plusWeeks(1);
                compareStartTimeCp = compareStartTimeCp.plusWeeks(1);
            }
        }
        if (compareConfig.getCompareIndicatorName().equals("date_month")) {
            DateTime curStartTimeCp = curStartTime.plusDays(-curStartTime.getDayOfMonth() + 1);
            DateTime compareStartTimeCp = compareStartTime.plusDays(-compareStartTime.getDayOfMonth() + 1);
            while (!curStartTimeCp.isAfter(curEndTime)) {
                switch (compareConfig.getPrefix()) {
                    case "hb":
                    case "month_hb":
                    case "week_hb":
                    case "year_hb":
                        mappingMap.put(curStartTimeCp.toString("yyyy-MM-dd"), compareStartTimeCp.toString("yyyy-MM-dd"));
                        break;
                    case "year_tb":
                        // 年同比
                        mappingMap.put(curStartTimeCp.toString("yyyy-MM-dd"), compareStartTimeCp.plusYears(-1).toString("yyyy-MM-dd"));
                        break;
                    // 季度同比
                    // 月同比
                    // 周同比
                }
                curStartTimeCp = curStartTimeCp.plusMonths(1);
                compareStartTimeCp = compareStartTimeCp.plusMonths(1);
            }
        }
    }

    public String getCompareDate(String date) {
        DateTime curTime = new DateTime(date);
        return mappingMap.getOrDefault(curTime.toString("yyyy-MM-dd"), "");
    }

    // 时间修复
    public void fixDate(ResultData mainQueryDataResult) {
        // 开始时间
        DateTime compareStartTime = new DateTime(compareConfig.getCompareDate().get(0));
        DateTime compareEndTime = new DateTime(compareConfig.getCompareDate().get(1));
        Days diff = Days.daysBetween(compareStartTime, compareEndTime);
        DateTime startTime = compareEndTime.plusDays(1);
        DateTime endTime = startTime.plusDays(diff.getDays());

        Map<String, Boolean> containsMap = new HashMap<>();
        mainQueryDataResult.getData().forEach(
                v -> containsMap.put(v.get(compareConfig.getCompareIndicatorName()).toString(), true)
        );
        while (!startTime.isAfter(endTime)) {
            String dateItem = startTime.toString("yyyy-MM-dd");
            if (containsMap.get(dateItem) == null) {
                Map<String, Object> item = new HashMap<>();
                item.put(compareConfig.getCompareIndicatorName(), dateItem);
                mainQueryDataResult.getData().add(item);
            }
            startTime = startTime.plusDays(1);
        }
    }

    @Override
    public void compare(LogicQueryPlan mainLogicPlain, LogicQueryPlan otherLogicPlain) {
        ResultData mainQueryDataResult = mainLogicPlain.getReduceQueryDataResult(); // 主查询
        ResultData otherQueryDataResult = otherLogicPlain.getReduceQueryDataResult(); // 子查询

        // 时间修复
        if (needFixDate) {
            fixDate(mainQueryDataResult);
        }
        // 数据合并
        Map<String, Map<String, Object>> compareMap = otherQueryDataResult.getData().stream()
                .collect(Collectors.toMap(
                        v -> {
                            String v1 = v.getOrDefault(compareConfig.getCompareIndicatorName(), "").toString();
                            StringBuilder v2 = new StringBuilder(v1);
                            if (dims != null) {
                                dims.forEach(dim -> {
                                    v2.append(v.getOrDefault(dim, "").toString());
                                });
                            }
                            return v2.toString();
                        },
                        v -> v
                ));

        // 数据合并
        mainQueryDataResult.getData().forEach(
                v -> {
                    String d = v.getOrDefault(compareConfig.getCompareIndicatorName(), "").toString();
                    StringBuilder compareDate = new StringBuilder();
                    if (d != null && !d.isEmpty()) {
                        compareDate.append(getCompareDate(d));
                    }
                    if (dims != null) {
                        dims.forEach(dim -> compareDate.append(v.getOrDefault(dim, "").toString()));
                    }
                    Map<String, Object> compareData = compareMap.get(compareDate.toString());
                    // 数据合并
                    mainQueryDataResult.getMeta().forEach(meta -> {
                        // 是否纬度？
                        if (meta.isIndicatorIsDimension()) {
                            return;
                        }
                        String indicatorName = meta.getIndicatorName();
                        Map<String, Object> deriveData;
                        Object o = v.get(indicatorName + "DeriveData");
                        if (o instanceof Map) {
                            deriveData = (Map<String, Object>) o;
                        } else {
                            deriveData = new HashMap<>();
                            deriveData.put(compareConfig.getPrefix() + "_value", null);
                            deriveData.put(compareConfig.getPrefix() + "_rate", null);
                            v.put(indicatorName + "DeriveData", deriveData);
                        }
                        // 同环比计算
                        if (compareData != null) {
                            Object m = v.get(indicatorName);
                            Object t = compareData.get(indicatorName);
                            Double mValue = null; // 主
                            Double tValue = null; // 从
                            if (m != null) {
                                mValue = Double.parseDouble(m.toString());
                            }
                            if (t != null) {
                                tValue = Double.parseDouble(t.toString());
                                deriveData.put(compareConfig.getPrefix() + "_value", tValue);
                            }
                            if (mValue != null && tValue != null) {
                                double r = mValue / tValue;
                                if (!Double.isNaN(r) && !Double.isInfinite(r)) {
                                    deriveData.put(compareConfig.getPrefix() + "_rate", mValue / tValue - 1);
                                }
                            }
                        }
                    });
                }
        );
    }
}
