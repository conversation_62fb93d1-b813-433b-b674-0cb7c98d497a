package com.ctrip.tour.business.dashboard.grpBusiness.config;

import com.ctrip.tour.business.dashboard.grpBusiness.entity.GrpUserMappingConfDto;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.*;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.InputArgumentException;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.ctrip.tour.business.dashboard.utils.MapperUtil;
import com.google.common.collect.Maps;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;
import qunar.tc.qconfig.client.spring.QMapConfig;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class GrpUserMappingConfig {

    @QMapConfig("grpUserMappingConfig.properties")
    private Map<String,String> grpUserMappingConfigMap;

    @QConfig("grp_user_mapping_config.json")
    private GrpUserMappingConfDto grpUserMappingConfigMap2;

    @QMapConfig("config.properties")
    private Map<String,String> configMap;

    public Map<String, String> getGrpUserMappingConfigMap() {
        return grpUserMappingConfigMap;
    }

    public String getGrpUserMappingResult(String empCode) {

        String switchStr = configMap.getOrDefault("mapping.conf.switch", "off");
        if (StringUtils.equals(switchStr, "on")) {
            Map<String, String> grpUserMappingConfigMap2 = getGrpUserMappingConfigMap2();
            grpUserMappingConfigMap.forEach((k, v) -> {
                String s = grpUserMappingConfigMap2.get(k);
                if (StringUtils.isBlank(s)) {
                    grpUserMappingConfigMap2.put(k, v);
                }
            });
            return grpUserMappingConfigMap2.get(empCode);
        }
        return grpUserMappingConfigMap.get(empCode);

    }

    public Map<String, String> getGrpUserMappingConfigMap2() {
      return   grpUserMappingConfigMap2.getMappingConfs()
                .stream()
                .collect(Collectors.toMap(GrpUserMappingConfDto.MappingConf::getOrginEmpCode, GrpUserMappingConfDto.MappingConf::getMappingEmpCode, (a, b) -> a));
    }
}
