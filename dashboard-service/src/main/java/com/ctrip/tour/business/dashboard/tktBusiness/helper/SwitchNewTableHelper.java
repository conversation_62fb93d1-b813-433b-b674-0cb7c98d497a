package com.ctrip.tour.business.dashboard.tktBusiness.helper;

import com.alibaba.fastjson.JSONObject;
import com.ctrip.framework.ucs.common.util.StringUtils;
import com.ctrip.soa._27181.BIBaseReportQueryServiceClient;
import com.ctrip.soa._27181.GetRawDataRequestType;
import com.ctrip.soa._27181.GetRawDataResponseType;
import com.ctrip.tour.business.dashboard.utils.MapperUtil;
import com.google.common.util.concurrent.ListenableFuture;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QMapConfig;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
@Slf4j
public class SwitchNewTableHelper implements InitializingBean, DisposableBean {

    @Autowired
    private BIBaseReportQueryServiceClient baseReportQueryServiceClient;

    //todo
    @QMapConfig("SgpNewTableSwitch.properties")
    private Map<String,String> switchMap;

    private static ExecutorService executor;


    @Override
    public void afterPropertiesSet() throws Exception {
        executor = Executors.newCachedThreadPool();
    }

    @Override
    public void destroy() throws Exception {
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
    }

    //远程查询数据库切换
    public GetRawDataResponseType switchRemoteDatabase(GetRawDataRequestType req){
        String tableSwitch = switchMap.get("switch");
        if("on".equals(tableSwitch)){
            Long queryId = req.getQueryId();
            String newQueryId = switchMap.get("queryId_" + queryId);
            if(StringUtils.isNotBlank(newQueryId)){
                req.setQueryId(Long.valueOf(newQueryId));
                log.info("SwitchNewTableHelper.switchRemoteDatabaseAsync queryId:{},newQueryId:{}",queryId,newQueryId);
            }
        }
        GetRawDataResponseType metricCardRes = null;
        try {
            String uuid = UUID.randomUUID().toString().replace("-", "");
            log.info("SwitchNewTableHelper-switchRemoteDatabase-request,uuid:{},req:{}",uuid, JSONObject.toJSONString(req));
            metricCardRes = baseReportQueryServiceClient.getRawData(req);
            log.info("SwitchNewTableHelper-switchRemoteDatabase-response,uuid:{},res:{}",uuid, JSONObject.toJSONString(metricCardRes));
        } catch (Exception e) {
            log.error("baseReportQueryServiceClient.getRawData() error,req:{}", MapperUtil.obj2Str(req),e);
        }
        return metricCardRes;
    }

    public ListenableFuture<GetRawDataResponseType> switchRemoteDatabaseAsync(GetRawDataRequestType req){
        String tableSwitch = switchMap.get("switch");
        if("on".equals(tableSwitch)){
            Long queryId = req.getQueryId();
            String newQueryId = switchMap.get("queryId_" + queryId);
            if(StringUtils.isNotBlank(newQueryId)){
                req.setQueryId(Long.valueOf(newQueryId));
                log.info("SwitchNewTableHelper.switchRemoteDatabaseAsync queryId:{},newQueryId:{}",queryId,newQueryId);
            }
        }

        ListenableFuture<GetRawDataResponseType> metricCardRes = null;
        try {
            String uuid = UUID.randomUUID().toString().replace("-", "");
            log.info("SwitchNewTableHelper-switchRemoteDatabaseAsync-request,uuid:{},req:{}", uuid, JSONObject.toJSONString(req));


            metricCardRes = baseReportQueryServiceClient.getRawDataAsync(req);


            //守护线程输出异步结果
            ListenableFuture<GetRawDataResponseType> finalMetricCardRes = metricCardRes;
            executor.execute(() -> {
                try {
                    GetRawDataResponseType getRawDataResponseType = finalMetricCardRes.get(90, TimeUnit.SECONDS);
                    log.info("SwitchNewTableHelper-switchRemoteDatabaseAsync-response,uuid:{},res:{}", uuid, JSONObject.toJSONString(getRawDataResponseType));
                } catch (Exception e) {
                    log.error("SwitchNewTableHelper-switchRemoteDatabaseAsync-response-error!,uuid:{}", uuid, e);
                }
            });
        } catch (Exception e) {
            log.error("baseReportQueryServiceClient.getRawDataAsync() error,req:{}", MapperUtil.obj2Str(req),e);
        }
        return metricCardRes;
    }

    public String switchLocalDatabase(String sql){
        String tableSwitch = switchMap.get("switch");
        if("on".equals(tableSwitch)){
            List<String> tables = extractTables(sql);
            log.info("switchLocalDatabase oldSql:{},tableNames:{}",sql,tables);
            //判断是否需要替换表名
            for (String table : tables) {
                String newTable = switchMap.get("tableName_" + table);
                if (StringUtils.isNotBlank(newTable)) {
                    sql = sql.replace(" " + table + " ", " " + newTable + " ");
                    log.info("SwitchNewTableHelper.switchLocalDatabase tableName:{},newTableName:{}",table,newTable);
                }
            }
            log.info("switchLocalDatabase newSql:{}",sql);
        }
        return sql;
    }

    //提取SQL中的表名
    public List<String> extractTables(String sql) {
        List<String> tables = new ArrayList<>();
        extractTablesRecursive(sql, tables);
        return tables;
    }

    private void extractTablesRecursive(String sql, List<String> tables) {
        String regex = "(?i)\\bFROM\\s+([\\w\\.]+)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(sql);
        while (matcher.find()) {
            if (matcher.group(1) != null) {
                tables.add(matcher.group(1));
            }
        }
        String subqueryRegex = "(?i)\\bFROM\\s+\\(([^\\)]+)\\)";
        Pattern subqueryPattern = Pattern.compile(subqueryRegex);
        Matcher subqueryMatcher = subqueryPattern.matcher(sql);
        while (subqueryMatcher.find()) {
            String subquery = subqueryMatcher.group(1);
            extractTablesRecursive(subquery, tables);
        }
    }
}
