package com.ctrip.tour.business.dashboard.tktBusiness.strategy.impl;

import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ExamineConfigBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.SinglePeriodDataBean;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.SinglePeriodTrendLineBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.Bus2Dao;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardExamineeConfigV2Dao;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.Bus2TicketActivityHelper;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.ChartHelper;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.MetricHelper;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.MultiPeriodMappingHelper;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/8/8
 */
@Component
public class Bus2TicketActivityStrategy {

    @Autowired
    private Bus2Dao dao;

    @Autowired
    private BusinessDashboardExamineeConfigV2Dao examineeConfigV2Dao;

    @Autowired
    private SinglePeriodTrendLineBiz singlePeriodTrendLineBiz;

    @Autowired
    private RemoteConfig remoteConfig;


    public MetricDetailInfo getSingleMetricCardData(String domainName,
                                                    TimeFilter timeFilter,
                                                    MetricInfoBean metricInfoBean,
                                                    String d) throws Exception {
        MetricDetailInfo metricDetailInfo = new MetricDetailInfo();
        Map<String, Double> dimMap = new HashMap<>();
        metricDetailInfo.setDimData(dimMap);


        String year = timeFilter.getYear();
        String dateType = MetricHelper.getDateTypeColumnName(timeFilter.getDateType());
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();
        String originLevel = metricInfoBean.getLevel();
        String mappingLevel = MetricHelper.getLevelColumnName(originLevel);
        List<String> regionList = metricInfoBean.getRegionList();
        String needUniversalStudios = metricInfoBean.getNeedUniversalStudios();


        DalHints rankDalHints = new DalHints().asyncExecution();

        //由于mysql需要设置索引 因此需要保持顺序
        Map<String, List<String>> inMap = new HashMap<>();
        Map<String, List<String>> notInMap = new HashMap<>();
        Map<String, List<String>> targetInMap = new HashMap<>();
        inMap.put("query_d", Lists.newArrayList(d));
        inMap.put("year", Lists.newArrayList(year));
        targetInMap.put("year", Lists.newArrayList(year));
        if ("month".equals(dateType)) {
            inMap.put("month", Lists.newArrayList(month));
            targetInMap.put("month", Lists.newArrayList(month));
        } else {
            inMap.put("quarter", Lists.newArrayList(quarter));
            targetInMap.put("quarter", Lists.newArrayList(quarter));
        }
        if (!"".equals(mappingLevel)) {
            if ("examinee".equals(mappingLevel)) {
                inMap.put(mappingLevel, Lists.newArrayList(domainName));
                targetInMap.put(mappingLevel, Lists.newArrayList(domainName));
            } else {
                inMap.put(mappingLevel, regionList);
                targetInMap.put(mappingLevel, regionList);
                //如果考核层级是省份并且不需要看环球影城的业绩
                //则把环球影城的业绩直接过滤掉
                if ("province_name".equals(mappingLevel) && "0".equals(needUniversalStudios)) {
                    notInMap.put("region_name", Lists.newArrayList("环球影城"));
                }
            }
        } else {
            //KA大区 业绩双记  对于考核国内的人员添加额外的过滤条件
            String domestic = remoteConfig.getConfigValue("domestic");
            if (domestic.equals(metricInfoBean.getLevel())) {
                String kaRegion = remoteConfig.getConfigValue("kaRegion");
                notInMap.put("region_name", Lists.newArrayList(kaRegion));
            }
        }
        DalHints currentDataHints = new DalHints().asyncExecution();
        dao.getMetricCardDataAsync(originLevel, inMap, notInMap, currentDataHints);

        DalHints targetHints = new DalHints().asyncExecution();
        dao.getOveralltargetDataAsync(originLevel, targetInMap, notInMap, new ArrayList<>(), targetHints);
        List<String> targetDimList = Bus2TicketActivityHelper.getTargetDimList();

        //单独获取系统内外毛利
        //考核层级为三方 不需要系统内毛利 和 系统外毛利
        metricDetailInfo.setLevel(originLevel);
        DalHints profitDetailDataHints = new DalHints().asyncExecution();
        if (!"三方".equals(originLevel)) {
            dao.getMetricCardDetailProfitAsync(originLevel, inMap, notInMap, profitDetailDataHints);
        }


        Map<String, List<String>> compareMap = new HashMap<>(inMap);



        List<String> dimList = Bus2TicketActivityHelper.getDimList();
        //拼接数据
        ChartHelper.fillOverallDimMap(currentDataHints.getListResult(), dimList, dimMap);
        Bus2TicketActivityHelper.makeUpMetricCardData(dimMap);
        //重新写入目标数据(在最新季度如果从事实表取 无法关联到完整的目标 因此目标统一从目标表获取)
        ChartHelper.fillOverallDimMap(targetHints.getListResult(), targetDimList, dimMap);
        Bus2TicketActivityHelper.makeUpMetricCardData(dimMap);

        //获取同比数据
        setMetricCardPopData(dateType, year, quarter, month, d,
                originLevel, metricDetailInfo, compareMap, notInMap, Bus2TicketActivityHelper.getSingleDimList());

        //获取环比数据
        setMetricCardMomData(dateType, year, quarter, month, d,
                originLevel, metricDetailInfo, compareMap, notInMap);
        if (!"三方".equals(originLevel)) {
            List<String> profitDetailDimList = Bus2TicketActivityHelper.getProfitDeatilList();
            ChartHelper.fillOverallDimMap(profitDetailDataHints.getListResult(), profitDetailDimList, dimMap);
        }
        MetricHelper.setMetricCardDrillDownInfo(metricInfoBean, metricDetailInfo);
        //获取排名数据
//        if (needRank) {
//            ChartHelper.fillRankData(metricDetailInfo, rankDalHints.getListResult());
//        }
        metricDetailInfo.setSubMetric("ticketActivity");
        return metricDetailInfo;
    }


    //同比数据计算口径
    //如果当前为9月5日(数据已更新)  当前数据计算2022 09-01~-09-04
    //去年数据计算 2021 09-01~09-04
    //2019数据计算 2019 09-01~09-04、
    //非当月数据按普通逻辑取(都是整月)
    private void setMetricCardPopData(String dateType,
                                      String year,
                                      String quarter,
                                      String month,
                                      String d,
                                      String originLevel,
                                      MetricDetailInfo metricDetailInfo,
                                      Map<String, List<String>> compareMap,
                                      Map<String, List<String>> notInMap,
                                      List<String> singleDimList) throws Exception {
        String lastDay = DateUtil.getDayOfInterval(d, -1);
        //特殊逻辑 对于考核层级为景点的人
        //如果不是最新季 不需要看同环比
        if ("景点".equals(originLevel) && !DateUtil.isLastestQuarter(year, month, quarter, dateType, lastDay)) {
            return;
        }
        List<String> lineList = Lists.newArrayList("current", "lastyear", "2019");
        Map<String, DalHints> dalHintsMap = new HashMap<>();
        String lastYear = DateUtil.getLastYear(year);
        for (String line : lineList) {
            DalHints dalHints = new DalHints().asyncExecution();
            Map<String, List<String>> inMap = new LinkedHashMap<>(compareMap);
            Map<String, List<String>> betweenMap = new LinkedHashMap<>();
            String lineYear = year;
            if ("lastyear".equals(line)) {
                inMap.put("year", Lists.newArrayList(lastYear));
                lineYear = lastYear;
            } else if ("2019".equals(line)) {
                inMap.put("year", Lists.newArrayList("2019"));
                lineYear = "2019";
            }
            if (("month".equals(dateType) && DateUtil.isCurrentMonth(lastDay, month))
                    || ("quarter".equals(dateType) && DateUtil.isCurrentQuarter(lastDay, quarter))) {
                betweenMap.put("the_date", DateUtil.getPopTimeRange(dateType, lastDay, month, quarter, lineYear, false));
            }
            dao.getMetricCardMomDataAsync(originLevel, inMap, notInMap, betweenMap, dalHints);
            dalHintsMap.put(line, dalHints);
        }
        Map<String, Double> dimMap = new HashMap<>();
        ChartHelper.fillOverallDimMap(dalHintsMap.get("current").getListResult(), singleDimList, dimMap);
        for (String line : lineList) {
            //不是当前数据
            if (!"current".equals(line)) {
                Map<String, Double> popDimMap = new HashMap<>();
                ChartHelper.fillOverallDimMap(dalHintsMap.get(line).getListResult(), singleDimList, popDimMap);
                Bus2TicketActivityHelper.makeUpMetricCardPopData(dimMap, popDimMap, "_" + line);
            }
        }
        Map<String, Double> orginDimMap = metricDetailInfo.getDimData();
        for (Map.Entry<String, Double> entry : dimMap.entrySet()) {
            String key = entry.getKey();
            Double value = entry.getValue();
            if (!orginDimMap.containsKey(key)) {
                orginDimMap.put(key, value);
            }
        }
    }

    public GetTrendLineDataResponseType getSingleTrendlineData(GetTrendLineDataRequestType request,
                                                               MetricInfoBean metricInfoBean,
                                                               String d) throws Exception {
        GetTrendLineDataResponseType response = new GetTrendLineDataResponseType();
        List<TrendLineDetailInfo> trendLineDetailInfoList = new ArrayList<>();
        response.setTrendLineDetailInfoList(trendLineDetailInfoList);

        TimeFilter timeFilter = request.getTimeFilter();

        String domainName = request.getDomainName();
        String originLevel = metricInfoBean.getLevel();
        List<ExamineConfigBean> examineConfigBeanList = MultiPeriodMappingHelper.getMultiPeriodConfigList(timeFilter, d, examineeConfigV2Dao.queryMetricAllConfig(domainName, d, "2"), null);
        List<Future<SinglePeriodDataBean>> futureList = new ArrayList<>();
        for (ExamineConfigBean examineConfigBean : examineConfigBeanList) {
            futureList.add(singlePeriodTrendLineBiz.getBus2SinglePeriodTrendLineData(request, examineConfigBean, d));
        }
        List<List<Object>> reachList = new ArrayList<>();
        List<List<Object>> targetList = new ArrayList<>();
        List<List<Object>> lastyearList = new ArrayList<>();
        List<List<Object>> _2019List = new ArrayList<>();
        for (Future<SinglePeriodDataBean> futureResult : futureList) {
            SinglePeriodDataBean singlePeriodDataBean = futureResult.get();
            List<List<Object>> periodReachList = singlePeriodDataBean.getPeriodReachList();
            List<List<Object>> periodTargetList = singlePeriodDataBean.getPeriodTargetList();
            List<List<Object>> periodLastyearList = singlePeriodDataBean.getPeriodLastyearList();
            List<List<Object>> period2019List = singlePeriodDataBean.getPeriod2019List();
            if (!GeneralUtil.isEmpty(periodReachList)) {
                reachList.addAll(periodReachList);
            }
            if (!GeneralUtil.isEmpty(periodTargetList)) {
                targetList.addAll(periodTargetList);
            }
            if (!GeneralUtil.isEmpty(periodLastyearList)) {
                lastyearList.addAll(periodLastyearList);
            }
            if (!GeneralUtil.isEmpty(period2019List)) {
                _2019List.addAll(period2019List);
            }
        }

        //有效时间范围
        List<String> timeList = DateUtil.getAllSelectedTime(timeFilter, "domestic");

        Map<String, Double> dimMap = new HashMap<>();
        Set<String> drillDownSet = new HashSet<>();
        List<String> dimList = Bus2TicketActivityHelper.getDimList();
        ChartHelper.setMonthlyMetricTrendlineActualReach(dimMap, drillDownSet, reachList, Lists.newArrayList("time"), dimList);
        //覆盖目标数据
        List<String> targetDimList = Bus2TicketActivityHelper.getTargetDimList();
        ChartHelper.setMonthlyMetricTrendlineActualReach(dimMap, drillDownSet, targetList, Lists.newArrayList("time"), targetDimList);

        ChartHelper.fillLineChartTrendLineData(null, timeList, dimMap,
                trendLineDetailInfoList, Bus2TicketActivityHelper.getLineChartTrendlineType());


        ChartHelper.setMonthlyMetricTrendlineActualReach(dimMap, drillDownSet, lastyearList, Lists.newArrayList("time"), Lists.newArrayList("ttd_cps_tot_profit_lastyear","ttd_cps_tot_profit_lastyear_value"));
        ChartHelper.setMonthlyMetricTrendlineActualReach(dimMap, drillDownSet, _2019List, Lists.newArrayList("time"), Lists.newArrayList("ttd_cps_tot_profit_2019","ttd_cps_tot_profit_2019_value"));

        ChartHelper.fillLineChartTrendLineData(null, timeList, dimMap,
                trendLineDetailInfoList, Bus2TicketActivityHelper.getLineChartLastYearTrendlineType());
        ChartHelper.fillLineChartTrendLineData(null, timeList, dimMap,
                trendLineDetailInfoList, Bus2TicketActivityHelper.getLineChart2019TrendlineType());


        response.setLevel(originLevel);

        return response;
    }




    public GetTableDataResponseType getSingleTableData(GetTableDataRequestType request,
                                                       MetricInfoBean metricInfoBean,
                                                       String d) throws Exception {
        GetTableDataResponseType response = new GetTableDataResponseType();
        List<TableDataItem> tableDataItemList = new ArrayList<>();
        response.setTableDataItemList(tableDataItemList);

        TimeFilter timeFilter = request.getTimeFilter();
        String year = timeFilter.getYear();
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();
        String dateType = MetricHelper.getDateTypeColumnName(timeFilter.getDateType());
        String originLevel = metricInfoBean.getLevel();
        String mappingLevel = MetricHelper.getLevelColumnName(originLevel);
        List<String> regionList = metricInfoBean.getRegionList();
        List<String> bdList = metricInfoBean.getBdList();
        String needUniversalStudios = metricInfoBean.getNeedUniversalStudios();

        Integer pageNo = request.getPageNo();
        //获取气泡图时需要该字段 不传默认为20
        Integer pageSize = request.getPageSize();
        if (pageSize == null) {
            pageSize = 20;
        }

        DrillDownFilter drillDownFilter = request.getDrillDownFilter();
        String originField = drillDownFilter.getField();
        String mappingField = MetricHelper.getDrillDownColumnName(originField);
        List<String> fieldValueList = drillDownFilter.getFieldValueList();

        List<String> subjectTagList = Lists.newArrayList(mappingField);
        List<String> relationTagList = Lists.newArrayList(mappingField);
        String source = request.getSource();
        String queryType = request.getQueryType();
        if ("detailpage".equals(source) && "table".equals(queryType)) {
            subjectTagList = MetricHelper.getTableDrillDownGroupList(mappingField);
            if ("province_name".equals(mappingField)) {
                relationTagList = MetricHelper.getTableDrillDownGroupList(mappingField);
            }
        }
        if ("firstpage".equals(source) && "examinee".equals(mappingField)) {
            subjectTagList = Lists.newArrayList("examinee_name", "examinee");
        }
        //由于mysql需要设置索引 因此需要保持顺序
        Map<String, List<String>> inMap = new LinkedHashMap<>();
        Map<String, List<String>> targetInMap = new LinkedHashMap<>();
        Map<String, List<String>> profitDetailInMap = new LinkedHashMap<>();
        Map<String, List<String>> notInMap = new HashMap<>();
        //对于按商拓下钻 添加like条件
        Map<String, List<String>> likeMap = new HashMap<>();
        Map<String, List<String>> notLikeMap = new HashMap<>();
        inMap.put("query_d", Lists.newArrayList(d));
        if("examinee".equals(mappingField)){
            targetInMap.put("query_d", Lists.newArrayList(d));
        }
        inMap.put("year", Lists.newArrayList(year));
        targetInMap.put("year", Lists.newArrayList(year));
        profitDetailInMap.put("query_d", Lists.newArrayList(d));
        profitDetailInMap.put("year", Lists.newArrayList(year));
        if ("month".equals(dateType)) {
            inMap.put("month", Lists.newArrayList(month));
            targetInMap.put("month", Lists.newArrayList(month));
            profitDetailInMap.put("month", Lists.newArrayList(month));
        } else {
            inMap.put("quarter", Lists.newArrayList(quarter));
            targetInMap.put("quarter", Lists.newArrayList(quarter));
            profitDetailInMap.put("quarter", Lists.newArrayList(quarter));
        }
        if (!"".equals(mappingLevel)) {
            if ("examinee".equals(mappingLevel)) {
                inMap.put(mappingLevel, bdList);
                profitDetailInMap.put(mappingLevel, bdList);
            } else {
                if ("examinee".equals(mappingField)) {
                    likeMap.put(mappingLevel, regionList);
                } else {
                    inMap.put(mappingLevel, regionList);
                    profitDetailInMap.put(mappingLevel, regionList);
                }
                //如果考核层级是省份并且不需要看环球影城的业绩
                //则把环球影城的业绩直接过滤掉
                if ("province_name".equals(mappingLevel) && "0".equals(needUniversalStudios)) {
                    if ("examinee".equals(mappingField)){
                        notLikeMap.put("region_name", Lists.newArrayList("环球影城"));
                    }else{
                        notInMap.put("region_name", Lists.newArrayList("环球影城"));
                    }
                }
            }
        } else {
            //POI下钻时 双记的数据对于考核国内和三方的人员不展示
            if("POI".equals(originField)){
                String kaRegion = remoteConfig.getConfigValue("kaRegion");
                notInMap.put("region_name", Lists.newArrayList(kaRegion));
            }
        }
        if (!GeneralUtil.isEmpty(fieldValueList)) {
            inMap.put(mappingField, fieldValueList);
        }
        DalHints currentDataHints = new DalHints().asyncExecution();
        dao.getTableReachDataAsync(originField, inMap, notInMap, subjectTagList,
                pageNo, pageSize, new HashMap<>(), currentDataHints, "masterdata", dateType, likeMap, notLikeMap);

        //获取数据总条数
        DalHints countDalHints = new DalHints().asyncExecution();
        dao.getTableDataCountAsync(originField, inMap, notInMap, subjectTagList, countDalHints, dateType, likeMap, notLikeMap);

        //当下钻层级是POI时 不能获取目标以及完成率
        //当下钻层级不是POI时  需要获取系统内外毛利
        if (!"POI".equals(originField)) {
            //目标 完成率
            //目标由于数量少 因此只限制时间范围
            DalHints currentTargetHints = new DalHints().asyncExecution();
            if ("省份".equals(originField)) {
                dao.getSpilitTargetDataAsync(originField, targetInMap, notInMap, relationTagList, currentTargetHints, dateType);
            } else {
                dao.getSpilitTargetDataAsync(originField, targetInMap, new HashMap<>(), relationTagList, currentTargetHints, dateType);
            }
            //系统内外毛利
            DalHints profitDeatilHints = new DalHints().asyncExecution();
            dao.getTableProfitDetailDataAsync(originField, profitDetailInMap, notInMap, relationTagList, profitDeatilHints, dateType, likeMap, notLikeMap);


            ChartHelper.fillCommmonTableDataV2(tableDataItemList, subjectTagList, relationTagList, Bus2TicketActivityHelper.getSingleDimList(),
                    Bus2TicketActivityHelper.getTargetDimList(), currentDataHints.getListResult(), currentTargetHints.getListResult());
            ChartHelper.fillOnlyMatchTableData(tableDataItemList, relationTagList, Bus2TicketActivityHelper.getProfitDeatilList(),
                    profitDeatilHints.getListResult());


            Bus2TicketActivityHelper.makeUpTableData(tableDataItemList);
        } else {

            ChartHelper.fillCommmonTableDataV2(tableDataItemList, subjectTagList, relationTagList, Bus2TicketActivityHelper.getSingleDimList(),
                    Bus2TicketActivityHelper.getTargetDimList(), currentDataHints.getListResult(), new ArrayList<>());
        }
        //收集下钻字段的值
        List<String> actualFieldList = tableDataItemList.stream()
                .map(i -> i.getFieldMap().get(mappingField))
                .collect(Collectors.toList());
        if (!actualFieldList.isEmpty()) {

            //如果下钻维度为商拓或者POI 基础的条件map里面需要去除所有与provicne_name和region_name的筛选项
            Map<String, List<String>> compareInMap = new HashMap<>(inMap);
            removeSpecialItem(compareInMap, mappingField);

            Map<String, List<String>> compareNotInMap = new HashMap<>(notInMap);
            removeSpecialItem(compareNotInMap, mappingField);


            //给首页添加近7日环比 或者 近30日环比
            if ("firstpage".equals(source)) {
                setTableMomData(dateType, quarter, month, d, originField, mappingField,
                        relationTagList, compareInMap, compareNotInMap, actualFieldList, response,"drilldown");
            }


            setTablePopData(dateType, year, quarter, month, d, originField, relationTagList, tableDataItemList,
                    compareInMap, compareNotInMap, Bus2TicketActivityHelper.getSingleDimList(), Bus2TicketActivityHelper.getVirtualDimList(),
                    mappingField, actualFieldList,"drilldown");

        }
        List<List<Object>> countList = countDalHints.getListResult();
        Integer totalNum = Integer.valueOf(String.valueOf(countList.get(0).get(0)));
        response.setTotalNum(totalNum);

        String lastDay = DateUtil.getDayOfInterval(d,-1);
        response.setNeedShowExamineePopData(DateUtil.isLastestQuarter(year, month, quarter, dateType, lastDay));

        return response;
    }

    private void removeSpecialItem(Map<String, List<String>> inMap,
                                   String mappingField) {

        if ("examinee".equals(mappingField)) {
            inMap.keySet().removeIf(key -> "region_name".equals(key) || "province_name".equals(key));
        }
        if ("viewspotid".equals(mappingField)) {
            inMap.keySet().removeIf(key -> "region_name".equals(key) || "province_name".equals(key) || "examinee".equals(key));
        }
    }




    private void setTablePopData(String dateType,
                                 String year,
                                 String quarter,
                                 String month,
                                 String d,
                                 String originField,
                                 List<String> relationTagList,
                                 List<TableDataItem> tableDataItemList,
                                 Map<String, List<String>> compareMap,
                                 Map<String, List<String>> notInMap,
                                 List<String> singleDimList,
                                 List<String> virtualDimList,
                                 String mappingField,
                                 List<String> actualFieldList,
                                 String type) throws Exception {

        List<String> lineList = Lists.newArrayList("current", "lastyear", "2019");
        Map<String, DalHints> dalHintsMap = new HashMap<>();
        String lastYear = DateUtil.getLastYear(year);
        String lastDay = DateUtil.getDayOfInterval(d, -1);
        for (String line : lineList) {
            DalHints dalHints = new DalHints().asyncExecution();
            Map<String, List<String>> inMap = new LinkedHashMap<>(compareMap);
            Map<String, List<String>> betweenMap = new LinkedHashMap<>();
            String lineYear = year;
            if ("lastyear".equals(line)) {
                inMap.put("year", Lists.newArrayList(lastYear));
                lineYear = lastYear;
            } else if ("2019".equals(line)) {
                inMap.put("year", Lists.newArrayList("2019"));
                lineYear = "2019";
            }
            if (("month".equals(dateType) && DateUtil.isCurrentMonthV2(lastDay, year, month))
                    || ("quarter".equals(dateType) && DateUtil.isCurrentQuarterV2(lastDay, year, quarter))) {
                betweenMap.put("the_date", DateUtil.getPopTimeRange(dateType, lastDay, month, quarter, lineYear, false));
            }
            inMap.put(mappingField, actualFieldList);
            dao.getTableReachDataAsync(originField, inMap, notInMap, relationTagList,
                    null, null, betweenMap, dalHints, type,
                    null, new HashMap<>(), new HashMap<>());
            dalHintsMap.put(line, dalHints);
        }
        ChartHelper.fillOnlyMatchTableData(tableDataItemList, relationTagList, virtualDimList, dalHintsMap.get("current").getListResult());

        for (String line : lineList) {
            //不是当前数据
            if (!"current".equals(line)) {
                ChartHelper.fillTableSingleDimPopData(tableDataItemList, dalHintsMap.get(line).getListResult(), relationTagList,
                        "_" + line, singleDimList, "_virtual");
            }
        }

    }

    public GetDrillDownBaseInfoResponseType getSingleDrillDownBaseInfo(GetDrillDownBaseInfoRequestType request,
                                                                       MetricInfoBean metricInfoBean,
                                                                       String d) throws Exception {
        GetDrillDownBaseInfoResponseType response = new GetDrillDownBaseInfoResponseType();
        List<FieldDataItem> fieldDataItemList = new ArrayList<>();
        response.setFieldDataItemList(fieldDataItemList);

        TimeFilter timeFilter = request.getTimeFilter();
        String year = timeFilter.getYear();
        String dateType = timeFilter.getDateType();
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();

        List<String> regionList = metricInfoBean.getRegionList();
        List<String> bdList = metricInfoBean.getBdList();
        String level = metricInfoBean.getLevel();//国内  三方  大区  省份 景点
        String mappingLevel = MetricHelper.getLevelColumnName(level);
        String needUniversalStudios = metricInfoBean.getNeedUniversalStudios();

        boolean needSearch = request.isNeedSearch();
        String searchWord = request.getSearchWord();
        List<String> fieldList = Bus2TicketActivityHelper.getFieldList(level, metricInfoBean);


        if (needSearch) {
            String searchField = request.getSearchField();//大区 省份 商拓 POI

            if (searchField != null) {
                String searchFieldColumnName = MetricHelper.getDrillDownColumnName(searchField);
                fieldList = Lists.newArrayList(searchFieldColumnName);
            }

        } else {
            response.setDefaultChosenField(MetricHelper.getDataBaseColumnName(fieldList.get(0)));
        }

        Map<String, DalHints> dalHintsMap = new HashMap<>();
        for (String field : fieldList) {

            Map<String, List<String>> inMap = new LinkedHashMap<>();
            Map<String, List<String>> notInMap = new HashMap<>();
            Map<String, List<String>> likeMap = new HashMap<>();
            inMap.put("query_d", Lists.newArrayList(d));
            inMap.put("year", Lists.newArrayList(year));
            if ("month".equals(dateType)) {
                inMap.put("month", Lists.newArrayList(month));
            } else {
                inMap.put("quarter", Lists.newArrayList(quarter));
            }
            if (!"".equals(mappingLevel)) {
                if ("examinee".equals(mappingLevel)) {
                    inMap.put(mappingLevel, bdList);
                } else {
                    //如果查看商拓下钻的维度  此时大区省份需要按like条件过滤
                    if ("examinee".equals(field)) {
                        likeMap.put(mappingLevel, regionList);
                    } else {
                        inMap.put(mappingLevel, regionList);
                    }
                    //如果考核层级是省份并且不需要看环球影城的业绩
                    //则把环球影城的业绩直接过滤掉
                    if ("province_name".equals(mappingLevel) && "0".equals(needUniversalStudios)) {
                        notInMap.put("region_name", Lists.newArrayList("环球影城"));
                    }
                }
            }
            //产品要求按POI下钻的时候按国内的考核层级需要过滤掉KA的数据  但这里可以不加条件 因为这只是获取维度而已

            Integer pageNo = null;
            Integer pageSize = null;
            List<String> tagList = Lists.newArrayList(field);
            if ("viewspotid".equals(field)) {
                tagList.add("viewspot_name");
            }
            if ("viewspotid".equals(field) || "examinee".equals(field)) {
                //默认拉取field  限制下POI和商拓获取的数量
                if (!needSearch) {
                    pageNo = 1;
                    pageSize = 50;
                }
            }
            if (needSearch) {
                if ("viewspotid".equals(field)) {
                    likeMap.put("viewspotid|viewspot_name", Lists.newArrayList(searchWord));
                }else{
                    likeMap.put(field, Lists.newArrayList(searchWord));
                }
            }
            DalHints dalHints = new DalHints().asyncExecution();
            dao.getFieldListAsync(MetricHelper.getDataBaseColumnName(field), inMap, likeMap, tagList, pageNo, pageSize, dalHints, dateType);
            dalHintsMap.put(field, dalHints);
        }
        for (Map.Entry<String, DalHints> entry : dalHintsMap.entrySet()) {
            String field = entry.getKey();
            DalHints dalHints = entry.getValue();
            ChartHelper.fillFieldDataItemList(field, dalHints.getListResult(), fieldDataItemList);
        }
        MetricHelper.adjustFieldItemOrder(response);
        return response;
    }



    //由于对于景点层级的人员  其任意一种环比都要从新表获取
    //因此环比上月 和  环比上季时  当期数据需要从重新获取
    //因此对于所有考核层级的计算  当期数据全部重新获取
    private void setMetricCardMomData(String dateType,
                                      String year,
                                      String quarter,
                                      String month,
                                      String d,
                                      String level,
                                      MetricDetailInfo metricDetailInfo,
                                      Map<String, List<String>> inMap,
                                      Map<String, List<String>> notInMap) throws Exception {

        //数据整体是T+1的
        //获取最新数据的时间
        //例如 d=2022-08-15  则数仓给出的最新数据是2022-08-14
        String lastDay = DateUtil.getDayOfInterval(d, -1);

        Map<String, Double> dimMap = metricDetailInfo.getDimData();
        List<String> dimList = Bus2TicketActivityHelper.getSingleDimList();
        Map<String, List<String>> momInMap = new LinkedHashMap<>();
        Map<String, List<String>> lastInMap = new LinkedHashMap<>();
        DalHints dalHints = new DalHints().asyncExecution();
        DalHints momDalHints = new DalHints().asyncExecution();
        //近7日或者近30日需要的条件
        for (Map.Entry<String, List<String>> entry : inMap.entrySet()) {
            String key = entry.getKey();
            List<String> valueList = entry.getValue();
            lastInMap.put(key, valueList);
            if ("month".equals(key) || "quarter".equals(key) || "year".equals(key)) {
                continue;
            }
            momInMap.put(key, valueList);
        }

        if ("month".equals(dateType)) {
            if (DateUtil.isCurrentMonthV2(lastDay, year, month)) {
                //当月
                metricDetailInfo.setMomType("7days");
                String startDate = DateUtil.getDayOfInterval(lastDay, -6);
                Map<String, List<String>> betweenMap = new HashMap<>();
                betweenMap.put("the_date", Lists.newArrayList(startDate, lastDay));
                dao.getMetricCardMomDataAsync(level, momInMap, notInMap, betweenMap, dalHints);

                String momEndDate = DateUtil.getDayOfInterval(startDate, -1);
                String momStartDate = DateUtil.getDayOfInterval(momEndDate, -6);
                Map<String, List<String>> momBetweenMap = new HashMap<>();
                momBetweenMap.put("the_date", Lists.newArrayList(momStartDate, momEndDate));
                dao.getMetricCardMomDataAsync(level, momInMap, notInMap, momBetweenMap, momDalHints);
            } else {
                //非当月
                metricDetailInfo.setMomType("lastmonth");

                dao.getMetricCardMomDataAsync(level, inMap, notInMap, new HashMap<>(), dalHints);

                List<String> lastTimeInfo = DateUtil.getLastTimeInfo(dateType, year, quarter, month);
                lastInMap.put("year", Lists.newArrayList(lastTimeInfo.get(0)));
                lastInMap.put("month", Lists.newArrayList(lastTimeInfo.get(1)));
                dao.getMetricCardDataAsync(level, lastInMap, notInMap, momDalHints);
            }
        } else {
            if (DateUtil.isCurrentQuarterV2(lastDay, year, quarter)) {
                //当季
                metricDetailInfo.setMomType("30days");
                String startDate = DateUtil.getDayOfInterval(lastDay, -29);
                Map<String, List<String>> betweenMap = new HashMap<>();
                betweenMap.put("the_date", Lists.newArrayList(startDate, lastDay));
                dao.getMetricCardMomDataAsync(level, momInMap, notInMap, betweenMap, dalHints);

                String momEndDate = DateUtil.getDayOfInterval(startDate, -1);
                String momStartDate = DateUtil.getDayOfInterval(momEndDate, -29);
                Map<String, List<String>> momBetweenMap = new HashMap<>();
                momBetweenMap.put("the_date", Lists.newArrayList(momStartDate, momEndDate));
                dao.getMetricCardMomDataAsync(level, momInMap, notInMap, momBetweenMap, momDalHints);
            } else {
                //非当季
                metricDetailInfo.setMomType("lastquarter");

                dao.getMetricCardMomDataAsync(level, inMap, notInMap, new HashMap<>(), dalHints);

                List<String> lastTimeInfo = DateUtil.getLastTimeInfo(dateType, year, quarter, month);
                lastInMap.put("year", Lists.newArrayList(lastTimeInfo.get(0)));
                lastInMap.put("quarter", Lists.newArrayList(lastTimeInfo.get(1)));
                dao.getMetricCardDataAsync(level, lastInMap, notInMap, momDalHints);
            }
        }
        //特殊逻辑 对于考核层级为景点的人
        //如果不是最新季 不需要看同环比
        if ("景点".equals(level) && !DateUtil.isLastestQuarter(year, month, quarter, dateType, lastDay)) {
            return;
        }
        ChartHelper.makeUpMetricCardMomDataV2(dimMap, dalHints, momDalHints,
                dimList, metricDetailInfo.getMomType());
    }


    //只存在当月和当季的情况
    //其他直接返回
    private void setTableMomData(String dateType,
                                 String quarter,
                                 String month,
                                 String d,
                                 String field,
                                 String mappingFiled,
                                 List<String> groupTagList,
                                 Map<String, List<String>> inMap,
                                 Map<String, List<String>> notInMap,
                                 List<String> actualFieldList,
                                 GetTableDataResponseType response,
                                 String type) throws Exception {

        //数据整体是T+1的
        //获取最新数据的时间
        //例如 d=2022-08-15  则数仓给出的最新数据是2022-08-14
        String lastDay = DateUtil.getDayOfInterval(d, -1);
        List<String> dimList = Bus2TicketActivityHelper.getSingleDimList();
        Map<String, List<String>> momInMap = new LinkedHashMap<>();
        DalHints dalHints = new DalHints().asyncExecution();
        DalHints momDalHints = new DalHints().asyncExecution();
        //近7日或者近30日需要的条件
        for (Map.Entry<String, List<String>> entry : inMap.entrySet()) {
            String key = entry.getKey();
            List<String> valueList = entry.getValue();
            if ("month".equals(key) || "quarter".equals(key) || "year".equals(key)) {
                continue;
            }
            momInMap.put(key, valueList);
        }
        momInMap.put(mappingFiled, actualFieldList);

        if ("month".equals(dateType)) {
            if (DateUtil.isCurrentMonth(lastDay, month)) {
                //当月
                response.setMomType("7days");
                String startDate = DateUtil.getDayOfInterval(lastDay, -6);
                Map<String, List<String>> betweenMap = new HashMap<>();
                betweenMap.put("the_date", Lists.newArrayList(startDate, lastDay));
                dao.getTableReachDataAsync(field, momInMap, notInMap, groupTagList,
                        null, null, betweenMap, dalHints, type,
                        null, new HashMap<>(), new HashMap<>());


                String momEndDate = DateUtil.getDayOfInterval(startDate, -1);
                String momStartDate = DateUtil.getDayOfInterval(momEndDate, -6);
                Map<String, List<String>> momBetweenMap = new HashMap<>();
                momBetweenMap.put("the_date", Lists.newArrayList(momStartDate, momEndDate));
                dao.getTableReachDataAsync(field, momInMap, notInMap, groupTagList,
                        null, null, momBetweenMap, momDalHints, type,
                        null, new HashMap<>(), new HashMap<>());
                ChartHelper.makeUpTableMomData(response.getTableDataItemList(), dalHints, momDalHints, dimList, groupTagList, "_7days");

            }
        } else {
            if (DateUtil.isCurrentQuarter(lastDay, quarter)) {
                //当季
                response.setMomType("30days");
                String startDate = DateUtil.getDayOfInterval(lastDay, -29);
                Map<String, List<String>> betweenMap = new HashMap<>();
                betweenMap.put("the_date", Lists.newArrayList(startDate, lastDay));
                dao.getTableReachDataAsync(field, momInMap, notInMap, groupTagList,
                        null, null, betweenMap, dalHints, type,
                        null, new HashMap<>(), new HashMap<>());

                String momEndDate = DateUtil.getDayOfInterval(startDate, -1);
                String momStartDate = DateUtil.getDayOfInterval(momEndDate, -29);
                Map<String, List<String>> momBetweenMap = new HashMap<>();
                momBetweenMap.put("the_date", Lists.newArrayList(momStartDate, momEndDate));
                dao.getTableReachDataAsync(field, momInMap, notInMap, groupTagList,
                        null, null, momBetweenMap, momDalHints, type,
                        null, new HashMap<>(), new HashMap<>());
                ChartHelper.makeUpTableMomData(response.getTableDataItemList(), dalHints, momDalHints, dimList, groupTagList, "_30days");

            }
        }
    }

}
