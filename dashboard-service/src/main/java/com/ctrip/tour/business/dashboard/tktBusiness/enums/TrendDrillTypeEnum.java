package com.ctrip.tour.business.dashboard.tktBusiness.enums;

public enum TrendDrillTypeEnum {
    TREND_LINE("trendline"),
    DRILL_DOWN("drilldown");

    private String name;

    TrendDrillTypeEnum(String name) {
        this.name = name;
    }


    public String getName() {
        return name;
    }

    public static TrendDrillTypeEnum getByCode(String code) {
        for (TrendDrillTypeEnum buType : TrendDrillTypeEnum.values()) {
            if (buType.getName().equals(code)) {
                return buType;
            }
        }
        return null;
    }
}
