package com.ctrip.tour.business.dashboard.tktBusiness.biz.impl;

import com.ctrip.soa._24922.GetUpdateTimeRequestType;
import com.ctrip.soa._24922.GetUpdateTimeResponseType;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.DataUpdateBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardUpdatetimeDao;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2022/8/10
 */
@Service
public class DataUpdateBizImpl implements DataUpdateBiz {


    @Autowired
    private BusinessDashboardUpdatetimeDao dao;

    @Override
    public GetUpdateTimeResponseType getUpdateTime(GetUpdateTimeRequestType request) throws Exception {
        GetUpdateTimeResponseType response = new GetUpdateTimeResponseType();
        //业绩经营看板数据更新时间
        String updateTime = dao.getUpdateTime();
        //任务看板数据更新时间
        String taskBoardUpdateTime = dao.getTaskBoardUpdateTime();
        if(StringUtils.isNotBlank(taskBoardUpdateTime)) {
            response.setTaskBoardUpdateTime(taskBoardUpdateTime);
        }
        response.setUpdateTime(updateTime);
        return response;
    }

    @Override
    public String getUpdateTime() throws Exception {
        GetUpdateTimeRequestType request = new GetUpdateTimeRequestType();
        GetUpdateTimeResponseType response = getUpdateTime(request);
        return response.getUpdateTime();
    }

    @Override
    public String getTaskBoardUpdateTime() throws Exception {
        GetUpdateTimeRequestType request = new GetUpdateTimeRequestType();
        GetUpdateTimeResponseType response = getUpdateTime(request);
        return response.getTaskBoardUpdateTime();
    }
}
