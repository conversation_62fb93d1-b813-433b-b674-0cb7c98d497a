package com.ctrip.tour.business.dashboard.tktBusiness.biz.impl;

import com.ctrip.soa._24922.PushSelfQualityOfServiceToBusinessRequestType;
import com.ctrip.soa._24922.PushSelfQualityOfServiceToBusinessResponseType;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.PushMessageBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.PushMessageServiceNumberTrippalInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.PushMessageServiceNumberTrippalInfoContentBean;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.SelfServiceQualityPushBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardEmployeeInfoDao;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardVendorMappingDao;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardVendorSalesDao;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardEmployeeInfo;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardVendorMapping;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.MapperUtil;
import com.ctrip.tour.business.dashboard.utils.PushMessageUtil;
import com.ctrip.tour.rights.client.TaskPropertyType;
import com.ctrip.tour.rights.client.TourRightsServiceClient;
import com.ctrip.tour.rights.client.WbCreateTaskRequestType;
import com.ctrip.tour.rights.client.WbCreateTaskResponseType;
import com.ctrip.tour.vbkProviderSvc.service.QuerySummaryProviderListRequestType;
import com.ctrip.tour.vbkProviderSvc.service.QuerySummaryProviderListResponseType;
import com.ctrip.tour.vbkProviderSvc.service.VbkProviderServiceClient;
import com.ctrip.tour.vendor.providersvc.soa.v1.service.VendorProviderServiceClient;
import com.ctrip.tour.vendor.providersvc.soa.v1.service.type.QuerySelfServiceKpiDataRequestType;
import com.ctrip.tour.vendor.providersvc.soa.v1.service.type.QuerySelfServiceKpiDataResponseType;
import com.ctrip.tour.vendor.providersvc.soa.v1.service.type.SelfServiceKpiDataType;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Service
public class SelfServiceQualityPushBizImpl implements SelfServiceQualityPushBiz {
    @Autowired
    private TourRightsServiceClient pushClient;

    @Autowired
    private VendorProviderServiceClient providerServiceClient;

    @Autowired
    private VbkProviderServiceClient vbkClient;

    @Autowired
    private BusinessDashboardVendorSalesDao salesDao;

    @Autowired
    private BusinessDashboardVendorMappingDao mappingDao;

    @Autowired
    private BusinessDashboardEmployeeInfoDao employeeInfoDao;

    @Autowired
    private RemoteConfig config;

    private Logger log = LoggerFactory.getLogger(SelfServiceQualityPushBizImpl.class);
    private ExecutorService exe = Executors.newFixedThreadPool(8, new ThreadFactoryBuilder().setNameFormat("ProductMetaServiceProxy-%s").build());

//    /**
//     * 对回调结果成功的进行后续处理
//     * @param request
//     * @param response
//     */
//    private void onSuccessByCallBack(WbCreateTaskRequestType request, WbCreateTaskResponseType response){
//        Long taskId = response.getTaskId();
//        if(taskId == null){
//            log.warn("taskId is null, response: " + MapperUtil.obj2Str(response) + "; request: " + MapperUtil.obj2Str(request));
//        }else{
//            log.info("taskId=" + taskId);
//        }
//    }
//    /**
//     * 对回调结果错误的进行后续处理
//     * @param t
//     *//*
//    private void onFailureByCallBack(WbCreateTaskRequestType request, Throwable t){
//        log.warn("push failure, message: " + t.getMessage() + "; request: " + MapperUtil.obj2Str(request));
//    }
//
//    *//**
//     * 对异步结果增加回调
//     * @param request
//     * @param responseFuture
//     *//*
//    private void handleFutureResponse(WbCreateTaskRequestType request, ListenableFuture<WbCreateTaskResponseType> responseFuture){
//        Futures.addCallback(responseFuture, new FutureCallback<WbCreateTaskResponseType>() {
//            @Override
//            public void onSuccess(WbCreateTaskResponseType response) {
//                onSuccessByCallBack(request, response);
//            }
//            @Override
//            public void onFailure(Throwable t) {
//                onFailureByCallBack(request, t);
//            }
//        }, exe);
//    }*/


    /**
     * 同步情况下对push任务结果处理
     * @param request
     * @param response
     */
    public void handleResponse(WbCreateTaskRequestType request, WbCreateTaskResponseType response){
        // response.getResponseStatus().getAck().getValue() == 0: success
        Long taskId = response.getTaskId();
        if(taskId == null){
            log.warn("taskId is null, response: " + MapperUtil.obj2Str(response) + "; request: " + MapperUtil.obj2Str(request));
        }else{
            log.info("taskId=" + taskId);
        }
    }


    /**
     * 获取供应商name
     * @param vendorId
     * @return
     * @throws Exception
     */
    public String getVendorName(Long vendorId) throws Exception {
        QuerySummaryProviderListRequestType request = new QuerySummaryProviderListRequestType();
        request.setProviderId(vendorId);
        QuerySummaryProviderListResponseType resp = vbkClient.querySummaryProviderList(request);
        if (resp.getTotalCount() > 0) {
            return resp.getProviderInfoList().get(0).getProviderName();
        }
        return "";
    }

    /**
     * 获取供应商的奖惩信息
     *
     * @param vendorId
     * @param startDate
     * @return
     * @throws Exception
     */
    public SelfServiceKpiDataType getVendorAssessInfo(Long vendorId, String startDate) throws Exception {
        QuerySelfServiceKpiDataRequestType request = new QuerySelfServiceKpiDataRequestType();
        request.setKpiType("month");
        request.setProviderId(vendorId);
        request.setCooperateBusinessLineIds("7");
        request.setStartDate(DateUtil.StringToCalendar(startDate, "yyyy-MM-dd"));
        QuerySelfServiceKpiDataResponseType response = providerServiceClient.querySelfServiceKpiData(request);
        if (response.getTotalCount() > 0) {
            return response.getContractServiceKpiList().get(0);
        }
        return null;
    }

    /**
     * 获取供应商销量
     *
     * @param vendorId
     * @return
     */
    public String getVendorSales(long vendorId) throws SQLException {
        Object sales = salesDao.getSalesByVendor(vendorId);
        if (sales != null) {
            return sales.toString();
        }
        return null;
    }

    /**
     * 获取供应商详细信息
     *
     * @param vendorList
     * @param startDate
     * @return
     * @throws Exception
     */
    public Map<Long, List<String>> getVendorInfoList(List<Long> vendorList, String startDate) throws Exception {
        Map<Long, List<String>> vendorInfoMap = new HashMap<>();
        for (Long vendorId : vendorList) {
            SelfServiceKpiDataType kpiData = getVendorAssessInfo(vendorId, startDate);
            List<String> vendorInfo = new ArrayList<>();
            vendorInfo.add(vendorId.toString());
            vendorInfo.add(getVendorName(vendorId));
            if (kpiData != null) {
                vendorInfo.add(getPercentFormat(kpiData.getTtdTimelyResponseRate()));
                vendorInfo.add(getPercentFormat(kpiData.getTtdNegativeReviewsRate()));
                vendorInfo.add(kpiData.getTtdTotalVendorSessionCnt().toString());
            } else {
                vendorInfo.add("");
                vendorInfo.add("");
                vendorInfo.add("");
            }
            String sales = getVendorSales(vendorId);
            vendorInfo.add(sales == null ? "0" : sales);
            vendorInfoMap.put(vendorId, vendorInfo);
        }
        return vendorInfoMap;
    }

    /**
     * 获取业务人员邮箱前缀和工号的对应关系
     *
     * @param emailNameList
     * @return
     * @throws SQLException
     */
    public Map<String, String> getEmployeeUserCode(List<String> emailNameList) throws SQLException {
        List<BusinessDashboardEmployeeInfo> employeeInfos = employeeInfoDao.getEmployeeCode(emailNameList);
        Map<String, String> nameMap = new HashMap<>();
        for (BusinessDashboardEmployeeInfo employeeInfo : employeeInfos) {
            nameMap.put(employeeInfo.getDomainName(), employeeInfo.getEmpCode());
        }
        return nameMap;
    }


    /**
     * 获取业务人员和供应商和业务线的对应关系，以业务人员为中心开始做业务人员和供应商考核信息的匹配
     *
     * @param vendorIdList
     * @return
     */
    public Map<String, List<Long>> getBusinessAndVendorMapping(List<Long> vendorIdList) throws SQLException {
        Map<String, List<Long>> bvMap = new HashMap<>();
        List<BusinessDashboardVendorMapping> mappingList = mappingDao.getBusinessVendorMapping(vendorIdList);
        if (mappingList.size() > 0) {
            for (BusinessDashboardVendorMapping m : mappingList) {
                String emailName = m.getBusinessUserCode();
                List<Long> vendorList = bvMap.computeIfAbsent(emailName, k -> new ArrayList<>());
                vendorList.add(m.getVendorId());
            }
        }
        return bvMap;
    }


    public PushMessageServiceNumberTrippalInfoBean getServiceNumShowInfo(String content, String title) throws Exception {
        PushMessageServiceNumberTrippalInfoBean trippalInfoBean = new PushMessageServiceNumberTrippalInfoBean();
        trippalInfoBean.setTitle(title);
        List<PushMessageServiceNumberTrippalInfoContentBean> trippalInfoContentBeans = new ArrayList<>();
        List<String> cList = new ArrayList<>();
        if(content.contains("<br/><br/>")){
            cList.addAll(Arrays.asList(content.split("<br/><br/>")));
        }else{
            cList.add(content);
        }
        for (String c : cList) {
            PushMessageServiceNumberTrippalInfoContentBean trippalInfoContentBean = new PushMessageServiceNumberTrippalInfoContentBean();
            trippalInfoContentBean.setIdx(0);
            trippalInfoContentBean.setType(1);
            trippalInfoContentBean.setTag("p");
            PushMessageServiceNumberTrippalInfoContentBean trippalInfoContentChildBean = new PushMessageServiceNumberTrippalInfoContentBean();
            trippalInfoContentChildBean.setIdx(0);
            trippalInfoContentChildBean.setType(0);
            trippalInfoContentChildBean.setText(c);
            trippalInfoContentBean.setChildren(Collections.singletonList(trippalInfoContentChildBean));
            trippalInfoContentBeans.add(trippalInfoContentBean);
        }

        trippalInfoBean.setContent(trippalInfoContentBeans);
        return trippalInfoBean;
    }

    /**
     * 推送消息到工作台
     *
     * @param taskName
     * @param employeeCode
     * @param taskType
     * @param eventType
     * @param html
     * @throws Exception
     */
    public void pushMessage(String taskName, String employeeCode, String taskType, String eventType, String html, String serviceNumInfo) throws Exception {
        WbCreateTaskRequestType request = new WbCreateTaskRequestType();
        request.setTaskType(taskType);
        request.setTaskName(taskName);

        PushMessageBean pushMessageBean = new PushMessageBean();
        pushMessageBean.setProcessoreIdList(employeeCode);
        pushMessageBean.setContent(html);

        List<TaskPropertyType> taskPropertyList = new ArrayList<>();
        if(Boolean.parseBoolean(config.getConfigValue("isSendServiceNum"))){
            TaskPropertyType resourceProperty = new TaskPropertyType();
            resourceProperty.setKey("RESOURCE_ID");
            resourceProperty.setValue("99999999");
            taskPropertyList.add(resourceProperty);
            pushMessageBean.setTrippalInfo(getServiceNumShowInfo(serviceNumInfo, taskName));
        }
        List<PushMessageBean> messageBeanList = new ArrayList<>();
        messageBeanList.add(pushMessageBean);
        TaskPropertyType taskProperty = new TaskPropertyType();
        taskProperty.setKey(eventType);
        taskProperty.setValue(MapperUtil.obj2Str(messageBeanList));
        taskPropertyList.add(taskProperty);
        request.setTaskPropertyList(taskPropertyList);
        try {
            WbCreateTaskResponseType response = pushClient.wbCreateTask(request);
            handleResponse(request, response);
        } catch (Exception e) {
            log.error("business Dashboard push message is error: " + e.getMessage());
        }
    }

    public String getPercentFormat(double data){
        DecimalFormat df = new DecimalFormat("0.00%");
        return df.format(data);
    }


    /**
     * 自服务质量一期：向业务方推送预警和关停的供应商信息
     * @param request
     * @return
     * @throws Exception
     */
    @Override
    public PushSelfQualityOfServiceToBusinessResponseType pushSelfQualityOfServiceToBusiness(PushSelfQualityOfServiceToBusinessRequestType request) throws Exception {
        log.info("pushSelfQualityOfServiceToBusiness request: " + MapperUtil.obj2Str(request));
        if(Boolean.parseBoolean(config.getConfigValue("isPushSelfServiceQuality"))){
            log.info("start PushSelfServiceQuality");
            int month = LocalDate.parse(request.getStartDate()).getMonth().getValue();
            String ttdTimelyLine = getPercentFormat(request.getTtdTimelyResponseRateLine());
            String ttdNegativeLine = getPercentFormat(request.getTtdNegativeReviewsRateLine());
            List<Long> vendorList = request.getVendorList();
            if (vendorList != null && vendorList.size() > 0) {
                log.info("vendorList Size: " + vendorList.size());
                List<String> headList = Arrays.asList(config.getConfigValue("vendorId"), config.getConfigValue("vendorName"), config.getConfigValue("ttdTimelyRate"), config.getConfigValue("ttdNegativeRate"), config.getConfigValue("totalSessionCount"), String.format(config.getConfigValue("monthName"), DateUtil.getSubMonth(month, 1)));
                Map<String, List<Long>> bvMap = getBusinessAndVendorMapping(vendorList);
                Map<String, String> nameMap = getEmployeeUserCode(new ArrayList<>(bvMap.keySet()));
                log.info("businessList Size: " + bvMap.keySet().size());

                log.info("businessList RealSize: " + nameMap.keySet().size());


                if (bvMap.size() > 0) {
                    Map<Long, List<String>> vendorInfoMap = getVendorInfoList(vendorList, request.getStartDate());
                    for (String domainName : bvMap.keySet()) {
                        List<List<String>> vendorInfoList = new ArrayList<>();
                        List<Long> vendorIdList = bvMap.get(domainName);
                        for (Long vendorId : vendorIdList) {
                            vendorInfoList.add(vendorInfoMap.get(vendorId));
                        }
                        String employeeCode = nameMap.get(domainName);
                        if (employeeCode != null) {
                            // 预警推送，一天一次
                            if (request.getPushType() == 1) {
                                String title = String.format(config.getConfigValue("warnTitle"), month, ttdTimelyLine, ttdNegativeLine, DateUtil.getLastDate(request.getStartDate()));
                                String html = PushMessageUtil.getPushMessage(title, vendorInfoList, headList, 4000);
                                String taskName = String.format(config.getConfigValue("warnTaskName"), month);
                                String warnServiceNumTitle = String.format(config.getConfigValue("warnServiceNumTitle"), vendorInfoList.size());
                                // 推送消息到业务方
                                pushMessage(taskName, employeeCode, config.getConfigValue("warnTaskType"), config.getConfigValue("warnEventType"), html, warnServiceNumTitle);
                            }
                            // 关停推送，一月一次
                            else if (request.getPushType() == 2) {
                                String title = String.format(config.getConfigValue("closeTitle"), month, request.getCurrentDate(), request.getCloseDays(), request.getCloseDays(), ttdTimelyLine, ttdNegativeLine);
                                String html = PushMessageUtil.getPushMessage(title, vendorInfoList, headList, 4000);
                                String taskName = String.format(config.getConfigValue("closeTaskName"), month);
                                String closeServiceNumTitle = String.format(config.getConfigValue("closeServiceNumTitle"), vendorInfoList.size());
                                // 推送消息到业务方
                                pushMessage(taskName, employeeCode, config.getConfigValue("closeTaskType"), config.getConfigValue("closeEventType"), html, closeServiceNumTitle);
                            }
                        }
                    }
                }
            }
        }else{
            log.info("not start PushSelfServiceQuality");
        }
        return new PushSelfQualityOfServiceToBusinessResponseType();
    }
}
