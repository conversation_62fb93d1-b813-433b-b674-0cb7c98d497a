package com.ctrip.tour.business.dashboard.grpBusiness.enums;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

public enum MetricCategoryEnum {

    INCOME_CATEGORY("incomeCategory", "销售额(GMV)类","v.page.workbench.salesVolume.category.tips"),//NOSONAR

    PROFIT_PRICE_CATEGORY("profitPriceCategory", "毛利类","v.page.workbench.grossProfit.category.tips"),//NOSONAR

    UV_CATEGORY("uvCategory", "访问人数类","v.page.workbench.detailPage.UV.category.tips"),//NOSONAR

    CONVERSION_RATE_CATEGORY("conversionRateCategory", "详情页转化类","v.page.workbench.detailPage.conversion.category.tips"),//NOSONAR


    FITTING_NPS_CATEGORY("fittingNpsCategory", "拟合nps类","v.page.workbench.fitting.nps.category.tips"),//NOSONAR

    MULTIPLE_PRICE_CATEGORY("multiplePriceCategory", "价格倍数类","v.page.workbench.price.multiple.category.tips"),//NOSONAR

    INFO_SCORE_CATEGORY("infoScoreCategory", "信息分数类","v.page.workbench.information.division.category.tips"),//NOSONAR

    SELF_SERVICE_CATEGORY("selfServiceCategory", "自服务类","v.page.workbench.self.service.category.tips"),//NOSONAR

    TOP_RATE_GUIDER_CATEGORY("topRateGuiderCategory", "金牌导游","v.page.workbench.grpTopRateGuider.category.tips"),//NOSONAR

    TOP_RATE_DRIVER_CATEGORY("topRateDriverCategory", "金牌司导","v.page.workbench.grpTopRateDriver.category.tips"),//NOSONAR
    SINGLE_UV_VAL_PRD_CATEGORY("singleUVValPrdCategory", "商品单uv价值","v.page.workbench.singleUVValPrdCategory.category.tips"),//NOSONAR
    SELF_OPR_PARENT_PRD_COUNT_CATEGORY("selfOprParentPrdCountCategory", "自营母商品数量","v.page.workbench.selfOprParentPrdCount.category.tips"),//NOSONAR
    HIGH_QUA_VENDOR_COUNT_WITH_SELFOPR_PRD_CATEGORY("highQuaVendorCountWithSelfOprPrdCategory", "有自营商品的好商家数量","v.page.workbench.highQuaVendorCountWithSelfOprPrd.category.tips"),;//NOSONAR
    private String englishName;
    private String chineseName;
    private String categoryTipsSharkKey;

    MetricCategoryEnum(String englishName, String chineseName, String categoryTipsSharkKey) {
        this.englishName = englishName;
        this.chineseName = chineseName;
        this.categoryTipsSharkKey = categoryTipsSharkKey;
    }

    public String getEnglishName() {
        return englishName;
    }

    public void setEnglishName(String englishName) {
        this.englishName = englishName;
    }

    public String getChineseName() {
        return chineseName;
    }

    public void setChineseName(String chineseName) {
        this.chineseName = chineseName;
    }

    public String getCategoryTipsSharkKey() {
        return categoryTipsSharkKey;
    }

    public void setCategoryTipsSharkKey(String categoryTipsSharkKey) {
        this.categoryTipsSharkKey = categoryTipsSharkKey;
    }

    private static final List<MetricCategoryEnum> allMetricCategoryEnums = Arrays.asList(MetricCategoryEnum.values());



    private static final List<MetricEnum> incomeCategoryEnumList = Arrays.asList(
            MetricEnum.INCOME,
            MetricEnum.GRP_INCOME_ACHIEVE_RATE,
            MetricEnum.SELF_OPR_INCOME,
            MetricEnum.SELF_OPR_INCOME_RATIO
    );

    private static final List<MetricEnum> profitPriceCategoryEnumList = Arrays.asList(
            MetricEnum.PROFIT_PRICE,
            MetricEnum.GRP_PROFIT_ACHIEVE_RATE,
            MetricEnum.SELF_OPR_PROFIT,
            MetricEnum.SELF_OPR_PROFIT_RATE,
            MetricEnum.SELF_OPR_PROFIT_RATIO
    );

    private static final List<MetricEnum> uvCategoryEnumList = Arrays.asList(
            MetricEnum.UV
    );

    private static final List<MetricEnum> conversionRateCategoryEnumList = Arrays.asList(
            MetricEnum.CONVERSION_RATE,
            MetricEnum.SELF_OPR_CONVERSION_RATE,
            MetricEnum.AGENT_CONVERSION_RATE
    );

    private static final List<MetricEnum> multiplePriceCategoryEnumList = Arrays.asList(
            MetricEnum.MULTIPLE_PRICE,
            MetricEnum.MULTIPLE_PRICE_MORE_RATE,
            MetricEnum.MULTIPLE_PRICE_LESS_RATE,
            MetricEnum.UNCOMPARE_RATE,
            MetricEnum.WEIGHTED_ANOMALY_RATE
    );

    private static final List<MetricEnum> selfServiceCategoryEnumList = Arrays.asList(
            MetricEnum.SELF_SERVICE_COVERAGE_RATE,
            MetricEnum.SELF_SERVICE_CONVERSION_RATE,
            MetricEnum.TIMELY_RESPONSE_RATE,
            MetricEnum.DISP_FAIL_RATE,
            MetricEnum.AVG_TRANSLATION_REVIEW_SCORE
    );

    private static final List<MetricEnum> fittingNpsCategoryEnumList = Arrays.asList(
            MetricEnum.FITTING_NPS,
            MetricEnum.RECOMMENDATION_RATE,
            MetricEnum.SLANDER_RATE,
            MetricEnum.ORDER_COVERAGE_RATE
    );

    private static final List<MetricEnum> infoScoreCategoryEnumList = Arrays.asList(
//            MetricEnum.PRD_INFO_SCORE,
//            MetricEnum.PLATFORM_INFO_SCORE,
            MetricEnum.WEIGHTED_PLATFORM_INFO_SCORE,
            MetricEnum.WEIGHTED_PRD_INFO_SCORE
    );

    private static final List<MetricEnum> topRateGuiderCategoryEnumList = Arrays.asList(
            MetricEnum.GRP_GUIDER_ACTUAL_DISPACTH_RATE,
            MetricEnum.GRP_GUIDER_DISPATCH_RATE,
            MetricEnum.GRP_GUIDER_CHECKIN_RATE
    );

    private static final List<MetricEnum> topRateDriverCategoryEnumList = Arrays.asList(
            MetricEnum.GRP_DRIVER_CHECKIN_RATE,
            MetricEnum.GRP_DRIVER_EXECUTION_RATE,
            MetricEnum.GRP_DRIVER_DISPATCH_RATE
    );

    private static final List<MetricEnum> singleUVValPrdCategoryEnumList = Arrays.asList(
            MetricEnum.SELF_OPR_SINGLE_UV_VAL_PRD,
            MetricEnum.AGENT_SINGLE_UV_VAL_PRD
    );

    private static final List<MetricEnum> selfOprParentPrdCountCategoryEnumList = Arrays.asList(
            MetricEnum.SELF_OPR_PARENT_PRD_COUNT,
            MetricEnum.ACTIVE_SELF_OPR_PARENT_PRD_COUNT,
            MetricEnum.ACTIVE_SELF_OPR_PARENT_PRD_RATE,
            MetricEnum.SRV_FREQUENCY_INLST30D
    );

    private static final List<MetricEnum> highQuaVendorCountWithSelfOprPrdCategoryEnumList = Arrays.asList(
            MetricEnum.HIGH_QUA_VENDOR_COUNT_WITH_SELFOPRPRD,
            MetricEnum.HIGH_QUA_VENDOR_COUNT_WITH_SELFOPR_PRD_FAILTOMEET
    );

    public static List<MetricCategoryEnum> getAllMetricCategoryEnums() {
        return allMetricCategoryEnums;
    }


    public static MetricCategoryEnum getMetricCategoryEnumByEnglishName(String metricCategoryEnglishName) {
        for(MetricCategoryEnum metricCategoryEnum : allMetricCategoryEnums) {
            if(metricCategoryEnum.getEnglishName().equals(metricCategoryEnglishName)) {
                return metricCategoryEnum;
            }
        }

        return null;
    }

    public static List<MetricEnum> getMetricEnumsByCategoryEnglishName(String metricCategoryEnglishName) {

        if(INCOME_CATEGORY.getEnglishName().equals(metricCategoryEnglishName)) {
            return incomeCategoryEnumList;
        } else if(PROFIT_PRICE_CATEGORY.getEnglishName().equals(metricCategoryEnglishName)) {
            return profitPriceCategoryEnumList;
        } else if(UV_CATEGORY.getEnglishName().equals(metricCategoryEnglishName)) {
            return uvCategoryEnumList;
        } else if(CONVERSION_RATE_CATEGORY.getEnglishName().equals(metricCategoryEnglishName)) {
            return conversionRateCategoryEnumList;
        } else if(MULTIPLE_PRICE_CATEGORY.getEnglishName().equals(metricCategoryEnglishName)) {
            return multiplePriceCategoryEnumList;
        } else if(SELF_SERVICE_CATEGORY.getEnglishName().equals(metricCategoryEnglishName)) {
            return selfServiceCategoryEnumList;
        } else if(FITTING_NPS_CATEGORY.getEnglishName().equals(metricCategoryEnglishName)) {
            return fittingNpsCategoryEnumList;
        } else if(INFO_SCORE_CATEGORY.getEnglishName().equals(metricCategoryEnglishName)) {
            return infoScoreCategoryEnumList;
        } else if(TOP_RATE_DRIVER_CATEGORY.getEnglishName().equals(metricCategoryEnglishName)) {
            return topRateDriverCategoryEnumList;
        } else if(SINGLE_UV_VAL_PRD_CATEGORY.getEnglishName().equals(metricCategoryEnglishName)) {
            return singleUVValPrdCategoryEnumList;
        } else if(SELF_OPR_PARENT_PRD_COUNT_CATEGORY.getEnglishName().equals(metricCategoryEnglishName)) {
            return selfOprParentPrdCountCategoryEnumList;
        } else if(HIGH_QUA_VENDOR_COUNT_WITH_SELFOPR_PRD_CATEGORY.getEnglishName().equals(metricCategoryEnglishName)) {
            return highQuaVendorCountWithSelfOprPrdCategoryEnumList;
        }

        return new ArrayList<>();
    }

    public static List<MetricEnum> getMetricEnumsByCategoryEnum(MetricCategoryEnum metricCategoryEnum) {

        if(INCOME_CATEGORY.equals(metricCategoryEnum)) {
            return incomeCategoryEnumList;
        } else if(PROFIT_PRICE_CATEGORY.equals(metricCategoryEnum)) {
            return profitPriceCategoryEnumList;
        } else if(UV_CATEGORY.equals(metricCategoryEnum)) {
            return uvCategoryEnumList;
        } else if(CONVERSION_RATE_CATEGORY.equals(metricCategoryEnum)) {
            return conversionRateCategoryEnumList;
        } else if(MULTIPLE_PRICE_CATEGORY.equals(metricCategoryEnum)) {
            return multiplePriceCategoryEnumList;
        } else if(SELF_SERVICE_CATEGORY.equals(metricCategoryEnum)) {
            return selfServiceCategoryEnumList;
        } else if(FITTING_NPS_CATEGORY.equals(metricCategoryEnum)) {
            return fittingNpsCategoryEnumList;
        } else if(INFO_SCORE_CATEGORY.equals(metricCategoryEnum)) {
            return infoScoreCategoryEnumList;
        } else if (TOP_RATE_GUIDER_CATEGORY.equals(metricCategoryEnum)) {
            return topRateGuiderCategoryEnumList;
        } else if (TOP_RATE_DRIVER_CATEGORY.equals(metricCategoryEnum)) {
            return topRateDriverCategoryEnumList;
        } else if (SELF_OPR_PARENT_PRD_COUNT_CATEGORY.equals(metricCategoryEnum)) {
            return selfOprParentPrdCountCategoryEnumList;
        } else if (HIGH_QUA_VENDOR_COUNT_WITH_SELFOPR_PRD_CATEGORY.equals(metricCategoryEnum)) {
            return highQuaVendorCountWithSelfOprPrdCategoryEnumList;
        } else if (SINGLE_UV_VAL_PRD_CATEGORY.equals(metricCategoryEnum)) {
            return singleUVValPrdCategoryEnumList;
        }


        return new ArrayList<>();
    }

}
