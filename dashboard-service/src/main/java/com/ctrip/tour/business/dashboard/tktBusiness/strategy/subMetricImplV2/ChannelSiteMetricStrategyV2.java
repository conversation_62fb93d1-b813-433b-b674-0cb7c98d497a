package com.ctrip.tour.business.dashboard.tktBusiness.strategy.subMetricImplV2;


import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.sightArchives.dao.overseametric.CdmOrdTtdOverseasPerformanceIndexDao;
import com.ctrip.tour.business.dashboard.sightArchives.dao.overseametric.DimOrdTtdSiteChannelTargetConfigDao;
import com.ctrip.tour.business.dashboard.sightArchives.dao.overseametric.DimOrdTtdTargetConfigDao;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.*;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.enums.GraphTypeEnum;
import com.ctrip.tour.business.dashboard.tktBusiness.enums.TrendLineNameEnum;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.Bus101102Helper;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.OverseaMetricHelper;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.util.*;
import java.util.concurrent.Future;

@Component
public class ChannelSiteMetricStrategyV2 {

    @Autowired
    private RemoteConfig remoteConfig;

    @Autowired
    private DimOrdTtdSiteChannelTargetConfigDao dimOrdTtdSiteChannelTargetConfigDao;

    @Autowired
    private CdmOrdTtdOverseasPerformanceIndexDao cdmOrdTtdOverseasPerformanceIndexDao;

    @Autowired
    private DimOrdTtdTargetConfigDao dimOrdTtdTargetConfigDao;

    public Future<OveaseaSubMetric> getBus101102110SubMetricCardData(TimeFilter timeFilter,
                                                                     OverseaMetricInfoBeanV2 metricInfoBean,
                                                                     String d,
                                                                     String subMetric,
                                                                     GetOverseaMetricCardDataV2RequestType request,
                                                                     String siteType) throws Exception {

        OveaseaSubMetric oveaseaSubMetric = new OveaseaSubMetric();
        oveaseaSubMetric.setSubMetric(subMetric);
        List<String> buType = OverseaMetricHelper.getBuType(request.getBusinessLine());
        //填充一些前端需要的额外信息
        oveaseaSubMetric.setMomType(OverseaMetricHelper.getMomType(timeFilter, d));
        Bus101102Helper.setMetricCardDrillDownParmaterV2(oveaseaSubMetric, subMetric, metricInfoBean, remoteConfig, timeFilter);

        // 获取目标来源，站点渠道不再区分
        double targetValue = 0.00;
        String dateType = timeFilter.getDateType();
        String year = timeFilter.getYear();
        List<String> quarterList = "quarter".equals(dateType) ? Collections.singletonList(timeFilter.getQuarter()) : DateUtil.getQuarterOfHalf(timeFilter.getHalf());
        for (String quarter : quarterList) {
            OverseaPersonConfigResponse personConfigResponse = getExamineLevel(quarter, year, buType, metricInfoBean.getMetric(), request.getDomainName(), siteType, d);
            OverseasRelatedSearchParamBean targetValueParam = Bus101102Helper.generate101102110SiteChannelTargetOverseaInfoSearch(quarter, year, personConfigResponse.getDestinationRangeList(), siteType, metricInfoBean.getMetric(),d);
            targetValue += dimOrdTtdSiteChannelTargetConfigDao.queryOverseaSiteTargetInfo(targetValueParam);
        }

        // 获取当期达成值
        double completeCurrentValue = getCompleteValueWithHOrQ("default", timeFilter, d, request.getDomainName(), buType, siteType, metricInfoBean.getMetric());

        // 获取去年达成值
        double completeLastYearValue = getCompleteValueWithHOrQ("lastYear", timeFilter, d, request.getDomainName(), buType, siteType, metricInfoBean.getMetric());

        // 获取环比相关数据
        double momData;
        if ("30days".equals(oveaseaSubMetric.getMomType())) {
            // 最近30天数据
            double complete30DaysYearValue = getCompleteValueWithHOrQ("30days", timeFilter, d, request.getDomainName(), buType, siteType, metricInfoBean.getMetric());

            // 上个30天数据
            double completeLast30DaysYearValue = getCompleteValueWithHOrQ("last30days", timeFilter, d, request.getDomainName(), buType, siteType, metricInfoBean.getMetric());

            momData = completeLast30DaysYearValue == 0 ? 0 : (complete30DaysYearValue - completeLast30DaysYearValue) / completeLast30DaysYearValue;
        } else {
            // 上个周期数据(半年或季)
            double completeLastQorHYearValue = getCompleteValueWithHOrQ("lastCycle", timeFilter, d, request.getDomainName(), buType, siteType, metricInfoBean.getMetric());

            momData = completeLastQorHYearValue == 0 ? 0 : (completeCurrentValue - completeLastQorHYearValue) / completeLastQorHYearValue;
        }
        oveaseaSubMetric.setCompleteValue(completeCurrentValue);
        oveaseaSubMetric.setTargetValue(targetValue);
        oveaseaSubMetric.setCompleteRate(targetValue == 0 ? 0 : completeCurrentValue / targetValue);
        oveaseaSubMetric.setYoyValue(completeLastYearValue == 0 ? 0 : (completeCurrentValue - completeLastYearValue) / completeLastYearValue);
        oveaseaSubMetric.setPopValue(momData);

        return new AsyncResult<>(oveaseaSubMetric);
    }

    public GetOverseaTrendLineDataV2ResponseType getBus101102110SubTrendlineData(GetOverseaTrendLineDataV2RequestType request,
                                                                                 String d,
                                                                                 List<String> timeList,
                                                                                 String siteType) throws Exception {
        GetOverseaTrendLineDataV2ResponseType response = new GetOverseaTrendLineDataV2ResponseType();
        List<OverseaTrendLine> trendLineDetailInfoList = new ArrayList<>();
        response.setTrendlines(trendLineDetailInfoList);

        List<OverseaTrendLineDataItem> completeItems = new ArrayList<>();
        List<OverseaTrendLineDataItem> completeRateItems = new ArrayList<>();
        List<OverseaTrendLineDataItem> yoyItems = new ArrayList<>();

        List<String> buType = OverseaMetricHelper.getBuType(request.getBusinessLine());

        for (String bean : timeList) {
            String year = bean.split("-")[0];
            String quarter = bean.split("-")[1];
            request.getTimeFilter().setDateType("quarter");
            request.getTimeFilter().setQuarter(quarter);
            request.getTimeFilter().setYear(year);
            if (Integer.parseInt(year) <= 2024) {
                continue;
            }

            OverseaPersonConfigResponse personConfigResponse = getExamineLevel(quarter, year, buType, request.getMetric(), request.getDomainName(), siteType, d);

            // 获取目标来源，站点渠道不再区分
            OverseasRelatedSearchParamBean targetValueParam = Bus101102Helper.generate101102110SiteChannelTargetOverseaInfoSearch(quarter, year, personConfigResponse.getDestinationRangeList(), siteType, request.getMetric(), d);
            double targetValue = dimOrdTtdSiteChannelTargetConfigDao.queryOverseaSiteTargetInfo(targetValueParam);

            // 获取当期达成值
            double completeCurrentValue = getCompleteValueWithHOrQ("default", request.getTimeFilter(), d, request.getDomainName(), buType, siteType, request.getMetric());

            // 获取去年达成值
            double completeLastYearValue = getCompleteValueWithHOrQ("lastYear", request.getTimeFilter(), d, request.getDomainName(), buType, siteType, request.getMetric());

            String dateInfo = year + "-" + quarter;
            completeItems.add(new OverseaTrendLineDataItem(null,dateInfo, completeCurrentValue));
            completeRateItems.add(new OverseaTrendLineDataItem(null,dateInfo,targetValue == 0 ? 0 : completeCurrentValue / targetValue));
            yoyItems.add(new OverseaTrendLineDataItem(null,dateInfo, completeLastYearValue == 0 ? 0 : (completeCurrentValue - completeLastYearValue) / completeLastYearValue));
        }

        trendLineDetailInfoList.add(new OverseaTrendLine(TrendLineNameEnum.COMPLETE_VALUE.getName(), GraphTypeEnum.BAR_CHART.getName(), completeItems));
        trendLineDetailInfoList.add(new OverseaTrendLine(TrendLineNameEnum.COMPLETE_RATE.getName(), GraphTypeEnum.LINE_CHART.getName(), completeRateItems));
        trendLineDetailInfoList.add(new OverseaTrendLine(TrendLineNameEnum.YOY_VALUE.getName(), GraphTypeEnum.LINE_CHART.getName(), yoyItems));

        return response;
    }

    /**
     * 抽象完成值查询提取（区分半年和季度）
     * @param searchType
     * @param timeFilter
     * @param d
     * @param buTypeList
     * @param siteType
     * @return
     * @throws ParseException
     */
    public Double getCompleteValueWithHOrQ(String searchType,
                                           TimeFilter timeFilter,
                                           String d,
                                           String domainName,
                                           List<String> buTypeList,
                                           String siteType,
                                           String metric) throws ParseException {
        boolean isH = "half".equals(timeFilter.getDateType());
        if (isH) {
            //半年
            String half = timeFilter.getHalf();
            String quarter1 = OverseaMetricHelper.getQWithHalf(half).get(0);
            Generate101102110SqlBean sqlBeanQ1 = new Generate101102110SqlBean(searchType, quarter1, timeFilter.getYear(), buTypeList, Collections.singletonList(siteType), domainName, null, d, timeFilter.getDateType(),metric);
            OverseasRelatedSearchParamBean personalCurrentCompleteParamQ1 = Bus101102Helper.generate101102110CompleteChannelSiteOverseaInfoSearch(sqlBeanQ1);
            personalCurrentCompleteParamQ1.setD(d);
            double completeCurrentValueQ1 = cdmOrdTtdOverseasPerformanceIndexDao.queryOverseasPerformanceInfo(personalCurrentCompleteParamQ1);

            String quarter2 = OverseaMetricHelper.getQWithHalf(half).get(1);
            Generate101102110SqlBean sqlBeanQ2 = new Generate101102110SqlBean(searchType, quarter2, timeFilter.getYear(), buTypeList, Collections.singletonList(siteType), domainName, null, d, timeFilter.getDateType(), metric);
            OverseasRelatedSearchParamBean personalCurrentCompleteParamQ2 = Bus101102Helper.generate101102110CompleteChannelSiteOverseaInfoSearch(sqlBeanQ2);
            personalCurrentCompleteParamQ2.setD(d);
            double completeCurrentValueQ2 = cdmOrdTtdOverseasPerformanceIndexDao.queryOverseasPerformanceInfo(personalCurrentCompleteParamQ2);

            return completeCurrentValueQ1 + completeCurrentValueQ2;
        }else{
            //季度
            String quarter = timeFilter.quarter;
            Generate101102110SqlBean sqlBean = new Generate101102110SqlBean(searchType, quarter, timeFilter.getYear(), buTypeList, Collections.singletonList(siteType), domainName, null, d, timeFilter.getDateType(), metric);
            OverseasRelatedSearchParamBean personalCurrentCompleteParam = Bus101102Helper.generate101102110CompleteChannelSiteOverseaInfoSearch(sqlBean);
            personalCurrentCompleteParam.setD(d);
            return cdmOrdTtdOverseasPerformanceIndexDao.queryOverseasPerformanceInfo(personalCurrentCompleteParam);
        }
    }

    /**
     * 获取海外考核人员配置信息
     * @param quarter 季度
     * @param year 年份
     * @param buType 业务类型
     * @param metric 考核指标
     * @param domainName 域名
     * @return OverseaPersonConfigResponse
     */
    public OverseaPersonConfigResponse getExamineLevel(String quarter, String year, List<String> buType, String metric, String domainName, String siteType, String d) {
        OverseaPersonConfigResponse overseaPersonConfigResponse = new OverseaPersonConfigResponse();
        //目的地考核层级
        List<String> destinationLevelList = new ArrayList<>();
        //站点考核范围
        List<String> siteChannelRangeList = new ArrayList<>();
        List<OverseaMetricInfoWithMetricBean> overseaMetricInfoWithMetricBeanList = dimOrdTtdTargetConfigDao.queryOverseaPersonInfo(Collections.singletonList(quarter), year, buType, metric, domainName,siteType,d);
        overseaMetricInfoWithMetricBeanList.forEach(OverseaMetricInfoWithMetricBean -> {
            destinationLevelList.add(OverseaMetricInfoWithMetricBean.getDestinationLevel());
            //全部就不加条件
            if (!"ALL".equals(OverseaMetricInfoWithMetricBean.getDestinationRange())){
                siteChannelRangeList.add(OverseaMetricInfoWithMetricBean.getDestinationRange());
            }
        });

        //去重
        Set<String> destinationLevelSet = new HashSet<>(destinationLevelList);
        overseaPersonConfigResponse.setDestinationLevelList(new ArrayList<>(destinationLevelSet));
        Set<String> destinationRangeSet = new HashSet<>(siteChannelRangeList);
        overseaPersonConfigResponse.setDestinationRangeList(new ArrayList<>(destinationRangeSet));
        return overseaPersonConfigResponse;
    }

}
