package com.ctrip.tour.business.dashboard.tktBusiness.strategy.domestic;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean;
import org.springframework.scheduling.annotation.Async;

import java.util.List;
import java.util.concurrent.Future;


public interface DomesticMetricCalStrategy {

    //获取单个指标指标卡数据
    @Async("metricCardExecutor")
    Future<DomesticMetricDetailInfo> getSingleMetricCardData(String domainName,
                                                             TimeFilter timeFilter,
                                                             List<MetricInfoBean> metricInfoBeanList,
                                                             String d,
                                                             Boolean isFirst,
                                                             Integer businessId) throws Exception;

    GetFirstPageDomesticMetricCardDrillDataResponseType getFirstPageDomesticMetricCardDrillData(
            GetFirstPageDomesticMetricCardDrillDataRequestType request,MetricInfoBean metricInfoBean,String d) throws Exception;


        //获取单个指标的趋势线数据
    GetDomesticMetricTrendDataResponseType getSingleTrendlineData(GetDomesticMetricTrendDataRequestType request,
                                                        String d) throws Exception;

    //获取单个指标表格数据
    GetDomesticTableDataResponseType getSingleTableData(GetDomesticTableDataRequestType request,
                                                MetricInfoBean metricInfoBean,
                                                String d) throws Exception;


    //获取下钻相关基础信息
    GetDomesticDrillDownBaseInfoResponseType getSingleDrillDownBaseInfo(GetDomesticDrillDownBaseInfoRequestType request,
                                                                        MetricInfoBean metricInfoBean,
                                                                        String d) throws Exception;



    //获取指标枚举值
    Integer getMetricName();


}
