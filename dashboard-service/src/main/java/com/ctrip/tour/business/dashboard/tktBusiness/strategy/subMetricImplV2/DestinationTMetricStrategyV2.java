package com.ctrip.tour.business.dashboard.tktBusiness.strategy.subMetricImplV2;

import com.ctrip.soa._24922.*;
import com.ctrip.soa._27181.GetRawDataRequestType;
import com.ctrip.soa._27181.GetRawDataResponseType;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ExamineConfigBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBeanV2;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.SqlParamterBean;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.Bus101102Helper;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

@Component
public class DestinationTMetricStrategyV2 implements IncomeSubMetricCalStrategyV2, TicketQuantityCalStrategyV2,QuantitySubMetricCalStrategyV2 {

    @Autowired
    private DestinationBaseStrategyV2 destinationBaseStrategy;


    @Override
    public Future<OveaseaSubMetric> getBus101102SubMetricCardData(TimeFilter timeFilter,
                                                                  OverseaMetricInfoBeanV2 metricInfoBean,
                                                                  String d,
                                                                  String subMetric,
                                                                  GetOverseaMetricCardDataV2RequestType request) throws Exception {
        boolean isH = "half".equals(timeFilter.getDateType());
        return isH ? destinationBaseStrategy.getBus101102110SubMetricCardDataHalf(timeFilter, metricInfoBean, d, subMetric, request) :
                destinationBaseStrategy.getBus101102110SubMetricCardDataQuarter(timeFilter, metricInfoBean, d, subMetric, request);
    }

    @Override
    public GetOverseaTrendLineDataV2ResponseType getBus101102SubTrendLineData(GetOverseaTrendLineDataV2RequestType request,
                                                                              String d,
                                                                              List<String> timeList) throws Exception {
        return destinationBaseStrategy.getBus101102110SubTrendlineData(request, d, timeList);
    }

    @Override
    public GetOverseaDrillDownBaseInfoV2ResponseType getBus101102SubDrillDownBaseInfo(GetOverseaDrillDownBaseInfoV2RequestType request,
                                                                                    String d,
                                                                                    OverseaMetricInfoBean metricInfoBean) throws Exception {
        return destinationBaseStrategy.getBus101102SubDrillDownBaseInfo(request, d, metricInfoBean);
    }

    @Override
    public GetOverseaTableDataV2ResponseType getBus101102SubTableData(GetOverseaTableDataV2RequestType request,
                                                                    String d,
                                                                    OverseaMetricInfoBean metricInfoBean) throws Exception {
        return destinationBaseStrategy.getBus101102SubTableData(request, d, metricInfoBean);
    }

    @Override
    public GetOverseaTableDataV2ResponseType getBus110SubTableData(GetOverseaTableDataV2RequestType request, String d, OverseaMetricInfoBean metricInfoBean) throws Exception {
        return getBus101102SubTableData(request, d, metricInfoBean);
    }

    @Override
    public GetOverseaDrillDownBaseInfoV2ResponseType getBus110SubDrillDownBaseInfo(GetOverseaDrillDownBaseInfoV2RequestType request, String d, OverseaMetricInfoBean metricInfoBean) throws Exception {
        return destinationBaseStrategy.getBus110SubDrillDownBaseInfo(request, d, metricInfoBean);
    }

    @Override
    public GetOverseaTrendLineDataV2ResponseType getBus110SubTrendLineData(GetOverseaTrendLineDataV2RequestType request, String d, List<String> timeList) throws Exception {
        return destinationBaseStrategy.getBus101102110SubTrendlineData(request, d, timeList);
    }

    @Override
    public Future<OveaseaSubMetric> getBus110SubMetricCardData(TimeFilter timeFilter, OverseaMetricInfoBeanV2 metricInfoBean, String d, String subMetric, GetOverseaMetricCardDataV2RequestType request) throws Exception {
        boolean isH = "half".equals(timeFilter.getDateType());
        return isH ? destinationBaseStrategy.getBus101102110SubMetricCardDataHalf(timeFilter, metricInfoBean, d, subMetric, request) :
                destinationBaseStrategy.getBus101102110SubMetricCardDataQuarter(timeFilter, metricInfoBean, d, subMetric, request);
    }

    @Override
    public GetOverseaDrillDownBaseInfoV2ResponseType getBus103SubDrillDownBaseInfo(GetOverseaDrillDownBaseInfoV2RequestType request, String d, OverseaMetricInfoBean metricInfoBean) throws Exception {
        return destinationBaseStrategy.getBus103SubDrillDownBaseInfo(request, d, metricInfoBean);
    }


    @Override
    public GetOverseaTableDataV2ResponseType getBus103SubTableData(GetOverseaTableDataV2RequestType request, String d, OverseaMetricInfoBean metricInfoBean) throws Exception {
        return destinationBaseStrategy.getBus103SubTableData(request, d, metricInfoBean);
    }

    @Override
    public String getSubMetricName() {
        return "destinationT";
    }
}
