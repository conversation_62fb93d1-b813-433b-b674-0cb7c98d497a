package com.ctrip.tour.business.dashboard.tktBusiness.validator;

import com.ctrip.soa._24922.GetMetricCardDataRequestType;
import com.ctrip.soa._24922.GetMetricCardDataResponseType;
import com.ctrip.soa._24922.TimeFilter;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.InputArgumentException;
import com.ctrip.tour.business.dashboard.utils.MapperUtil;
import com.ctrip.tour.business.dashboard.utils.ParamterCheckUtil;
import com.ctrip.train.tieyouflight.soa.validation.validator.BusinessConstraintValidator;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/9/8
 */
@Component
public class GetMetricCardDataValidator extends BusinessConstraintValidator<GetMetricCardDataRequestType, GetMetricCardDataResponseType> {


    @Override
    public GetMetricCardDataResponseType validateBusiness(GetMetricCardDataRequestType request) {
        String domainName = request.getDomainName();
        if (!ParamterCheckUtil.checkDomainName(domainName)) {
            throw new InputArgumentException("input error domainName:" + MapperUtil.obj2Str(request));
        }
        TimeFilter timeFilter = request.getTimeFilter();
        if (!ParamterCheckUtil.checkTimeFilterWithoutTimeFrame(timeFilter)) {
            throw new InputArgumentException("input error timeFilter:" + MapperUtil.obj2Str(request));
        }
        return null;
    }
}
