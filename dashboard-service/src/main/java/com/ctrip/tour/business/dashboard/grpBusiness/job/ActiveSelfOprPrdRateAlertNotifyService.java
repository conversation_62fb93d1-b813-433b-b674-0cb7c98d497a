package com.ctrip.tour.business.dashboard.grpBusiness.job;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.tour.business.dashboard.grpBusiness.dao.mysql.DimOrdGrpAchv2025PersonTrgtDao;
import com.ctrip.tour.business.dashboard.grpBusiness.dao.starrocks.StarRocksCommonDao;
import com.ctrip.tour.business.dashboard.grpBusiness.entity.DimOrdGrpAchv2025PersonTrgt;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.group.workbenchsvc.contract.StructuredTableInfoType;
import com.ctrip.tour.group.workbenchsvc.contract.StructuredTableRowInfoType;
import com.ctrip.tour.rights.client.GroupWorkbenchServiceClient;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2025/3/25
 */
@Service
@Slf4j
public class ActiveSelfOprPrdRateAlertNotifyService extends CommonAlertNotifyService {

    private static final String TASK_GRP_PROFIT_ALERT = "TASK_BENEFIT_ACHIEVEMENT_ALERT";
    private static final String EVENT_GRP_PROFIT_ALERT = "EVENT_BENEFIT_ACHIEVEMENT_ALERT";
    private static final String EVENT_GRP_PROFIT_ALERT_STRUCTURED_TABLE = "EVENT_BENEFIT_ACHIEVEMENT_ALERT_STRUCTURED_TABLE";
//private static final String TASK_GRP_PROFIT_ALERT = "TASK_NOTICE_TEST";
//    private static final String EVENT_GRP_PROFIT_ALERT = "EVENT_NOTICE_TEST";
//    private static final String EVENT_GRP_PROFIT_ALERT_STRUCTURED_TABLE = "EVENT_NOTICE_TEST_STRUCTURED_TABLE";

    @Autowired
    private StarRocksCommonDao starRocksCommonDao;

    @Autowired
    private DimOrdGrpAchv2025PersonTrgtDao achv2025PersonTrgtDao;

    private static String NOTIFY_EVENT_EMPCODES = "notify.event.empcodes";

    private GroupWorkbenchServiceClient client = GroupWorkbenchServiceClient.getInstance();

    @Autowired
    private RemoteConfig remoteConfig;

    public void handleMultipriceNotify() throws Exception {

        String whiteEmpCodesStr = remoteConfig.getExternalConfig(NOTIFY_EVENT_EMPCODES);

        List<String> whiteEmpCodes = Lists.newArrayList();
        if (StringUtils.isNotBlank(whiteEmpCodesStr)) {
            whiteEmpCodes = Splitter.on(",").splitToList(whiteEmpCodesStr);
        }

        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");


        String sql = "SELECT \n" +
                "    pm_eid,\n" +
                "    dest_country_id,\n" +
                "    vendor_id,\n" +
                "    COUNT(DISTINCT upg_self_parent_prd_id) AS total_self_parent_prd,\n" +
                "    COUNT(DISTINCT oln_upg_self_parent_prd_id) AS online_oln_self_parent_prd,\n" +
                "    (COUNT(DISTINCT oln_self_parent_prd_id) / COUNT(DISTINCT self_parent_prd_id)) AS ratio\n" +
                "FROM \n" +
                "    adm_prd_grp_product_total_work_platform_df\n" +
                "WHERE \n" +
                "    upg_self_parent_prd_id IS NOT NULL and partition_d='" + LocalDate.now().minusDays(1).format(dtf) + "'" +
                "GROUP BY \n" +
                "    pm_eid, dest_country_id,vendor_id\n" +
                "HAVING \n" +
                "    (\n" +
                "        dest_country_id = 1 AND \n" +
                "        (COUNT(DISTINCT oln_self_parent_prd_id) / COUNT(DISTINCT upg_self_parent_prd_id)) < 0.9\n" +
                "    ) OR (\n" +
                "        dest_country_id != 1 AND \n" +
                "        (COUNT(DISTINCT oln_self_parent_prd_id) / COUNT(DISTINCT upg_self_parent_prd_id)) < 0.85\n" +
                "    );";

        List<Map<String, Object>> resultList = starRocksCommonDao.query(sql, Maps.newHashMap());
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }

        List<String> empCodes = resultList.stream().map(pw -> (String) pw.get("pm_eid"))
                .distinct().collect(Collectors.toList());

        List<String> finalWhiteEmpCodes = whiteEmpCodes;
        empCodes.stream()
                .filter(pw -> CollectionUtils.isEmpty(finalWhiteEmpCodes) || (Objects.nonNull(pw) && finalWhiteEmpCodes.contains(pw)))
                .forEach( pmEid -> {




                    String pmSql = "SELECT \n" +
                            "    vendor_id,\n" +
                            "    vendor_name,\n" +
                            "    dest_country_id,\n" +
                            "    (COUNT(DISTINCT oln_self_parent_prd_id) / COUNT(DISTINCT upg_self_parent_prd_id)) AS ratio\n" +
                            "FROM \n" +
                            "    adm_prd_grp_product_total_work_platform_df\n" +
                            "WHERE \n" +
                            "    upg_self_parent_prd_id IS NOT NULL and partition_d='" + LocalDate.now().minusDays(1).format(dtf) + "' " +
                            "and pm_eid = '" + pmEid+ "'"+
                            "GROUP BY \n" +
                            "    vendor_id,vendor_name,dest_country_id" +
                            " HAVING \n" +
                            "    (\n" +
                            "        dest_country_id = 1 AND \n" +
                            "        (COUNT(DISTINCT oln_self_parent_prd_id) / COUNT(DISTINCT upg_self_parent_prd_id)) < 0.9\n" +
                            "    ) OR (\n" +
                            "        dest_country_id != 1 AND \n" +
                            "        (COUNT(DISTINCT oln_self_parent_prd_id) / COUNT(DISTINCT upg_self_parent_prd_id)) < 0.85\n" +
                            "    );";
                    List<Map<String, Object>> pmRst = null;
                    try {
                        pmRst = starRocksCommonDao.query(pmSql, Maps.newHashMap());
                    } catch (SQLException e) {
                        log.warn("query pm data error", e);
                        return;
                    }

                    List<StructuredTableRowInfoType> structuredTableRowInfoTypes = pmRst.stream().map(pm -> {
                        StructuredTableRowInfoType rowInfoType = new StructuredTableRowInfoType();
                        Integer vendor_id = (Integer) pm.get("vendor_id");
                        String vendor_name = (String) pm.get("vendor_name");
                        Integer dest_country_id = (Integer) pm.get("dest_country_id");
                        Double ratio = (Double) pm.get("ratio");
                        List<String> colList = Lists.newArrayList(LocalDate.now().minusDays(1).format(dtf),vendor_id.toString(), vendor_name,
                                Objects.equals(1, dest_country_id) ? "国内" : "海外", getDataRatioStr(ratio));//NOSONAR
                        rowInfoType.setColList(colList);
                        return rowInfoType;
                    }).collect(Collectors.toList());

                    StructuredTableInfoType structuredTableInfoType = new StructuredTableInfoType();
            structuredTableInfoType.setRowList(structuredTableRowInfoTypes);
            structuredTableInfoType.setHeaderList(Lists.newArrayList("统计日期", "供应商ID", "供应商名称", "境内境外", "有效商品比例"));//NOSONAR
            String content = "挂牌自营有效商品比例不达标（国内低于90%，境外低于85%），请及时处理";//NOSONAR
            List<String> tpInfos = Lists.newArrayList("挂牌自营有效商品比例不达标（国内低于90%，境外低于85%），请及时处理");//NOSONAR
            notifyEmp(pmEid, structuredTableInfoType, "【双好】挂牌自营有效商品比例",//NOSONAR
                    TASK_GRP_PROFIT_ALERT, EVENT_GRP_PROFIT_ALERT, EVENT_GRP_PROFIT_ALERT_STRUCTURED_TABLE, content, tpInfos, "1");//NOSONAR
        });


    }


}
