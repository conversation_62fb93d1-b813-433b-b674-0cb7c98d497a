package com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;

@Entity
@Getter
@Setter
public class DimOrdTtdPersonTargetBO {
    //业务线
    private String buType;
    //邮箱前缀
    private String domainName;
    //考核年
    private String examineYear;
    //考核季
    private String examineQuater;
    //C/T站
    private String CT;
    //考核指标类型
    private String examineMetricType;
    //考核目标
    private Double examineMetric;
    private String q1;
    private String q2;
    private String q3;
    private String q4;
}
