package com.ctrip.tour.business.dashboard.utils;

import static com.ctrip.tour.business.dashboard.grpBusiness.exception.ExceptionEnum.OPERATE_NOT_SUPPORT;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayDeque;
import java.util.Deque;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Stack;

import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;

import com.ctrip.tour.business.dashboard.tktBusiness.exception.ServiceException;
import com.google.common.collect.ImmutableList;

/**
 * <AUTHOR>
 * @Date 2024/12/10
 */
public class Calculator {

    private static final ImmutableList<String> symbolList = ImmutableList.of("+", "-", "*", "/", "(", ")");

    public static BigDecimal parseExpressionAndCalc(String expression, Map<String, Object> rowData) {
        Deque<Character> stack = new ArrayDeque<>();
        List<String> executionOrder = Lists.newArrayList();
        StringBuilder currentToken = new StringBuilder();

        for (int i = 0; i < expression.length(); i++) {
            char ch = expression.charAt(i);

            if (StringUtils.isBlank(String.valueOf(ch).trim())) {
                continue;
            }

            if (Character.isLetterOrDigit(ch) || StringUtils.equals("_", String.valueOf(ch)) ) {
                currentToken.append(ch);
            } else {
                if (currentToken.length() > 0) {
                    String token = currentToken.toString();
                    executionOrder.add(token);
                    currentToken.setLength(0);
                }

                if (ch == '(') {
                    stack.push(ch);
                } else if (ch == ')') {
                    while (!stack.isEmpty() && stack.peek() != '(') {
                        char op = stack.pop();
                        executionOrder.add(String.valueOf(op));
                    }
                    stack.pop();
                } else if (ch == '+' || ch == '-' || ch == '*' || ch == '/') {
                    while (!stack.isEmpty() && precedence(ch) <= precedence(stack.peek())) {
                        char op = stack.pop();
                        executionOrder.add(String.valueOf(op));
                    }
                    stack.push(ch);
                }
            }
        }

        if (currentToken.length() > 0) {
            String token = currentToken.toString();
            executionOrder.add(token);
        }

        while (!stack.isEmpty()) {
            char op = stack.pop();
            executionOrder.add(String.valueOf(op));
        }

        Stack<BigDecimal> calcStack = new Stack<>();
        for (String s : executionOrder) {

            if (!symbolList.contains(s)) {
                Object o = rowData.get(s);
                BigDecimal val = null;
                if (o instanceof Long) {
                    val = BigDecimal.valueOf((long)o);
                } else if (o instanceof Double) {
                    val = BigDecimal.valueOf((double)o);
                } else {
                    val = (BigDecimal) o;
                }
                calcStack.push(val);
            } else {
                BigDecimal a = calcStack.pop();
                BigDecimal b = calcStack.pop();
                BigDecimal apply = apply(b, a, s);
                calcStack.push(apply);
            }

        }


        return calcStack.pop();
    }

    private static BigDecimal apply(BigDecimal a, BigDecimal b, String symbol) {
        switch (symbol) {
            case "+":
                return a.add(b);
            case "-":
                return a.subtract(b);
            case "*":
                return a.multiply(b);
            case "/":
                if (Objects.isNull(b) || b.compareTo(BigDecimal.ZERO) == 0) {
                    return null;
                }
                return a.divide(b, 5,RoundingMode.HALF_UP);
            default:
                throw new ServiceException(OPERATE_NOT_SUPPORT.getCode(), OPERATE_NOT_SUPPORT.getMsg());
        }
    }

    private static int precedence(char operator) {
        switch (operator) {
            case '+':
            case '-':
                return 1;
            case '*':
            case '/':
                return 2;
            default:
                return -1;
        }
    }
}
