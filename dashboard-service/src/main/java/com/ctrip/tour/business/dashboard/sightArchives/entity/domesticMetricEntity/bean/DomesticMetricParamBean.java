package com.ctrip.tour.business.dashboard.sightArchives.entity.domesticMetricEntity.bean;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class DomesticMetricParamBean {
    String d;
    Boolean isPalyAll;
    String metric;
    Integer pageIndex;
    Integer pageSize;
    //下钻维度
    String field;
    //业务线
    List<String> buTypes;
    //使用月份
    List<String> useDate;
    //使用季度
    String useMonth;
    //使用半年
    String useQuarter;
    //使用年份
    String useHalfYear;
    String useYear;
    List<String> domainNames;
    //产品经理
    List<String> prdMeid;
    //业务国家名称
    String businessDomainName;
    //业务大区名称
    List<String> businessRegionName;
    //业务子区域名称
    List<String> businessSubRegionName;
    //景点名称
    List<String> vstName;
    //考核层级
    String examineLevel;
    //考核范围
    String examineRange;





}
