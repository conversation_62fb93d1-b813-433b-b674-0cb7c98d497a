package com.ctrip.tour.business.dashboard.tktBusiness.strategy.subMetricImpl

import com.ctrip.soa._24922.DrillDownFilter
import com.ctrip.soa._24922.GetOverseaDrillDownBaseInfoRequestType
import com.ctrip.soa._24922.GetOverseaTableDataRequestType
import com.ctrip.soa._24922.GetOverseaTrendLineDataRequestType
import com.ctrip.soa._24922.TimeFilter
import com.ctrip.soa._27181.BIBaseReportQueryServiceClient
import com.ctrip.soa._27181.GetRawDataResponseType
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ExamineConfigBean
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBean
import com.ctrip.tour.business.dashboard.tktBusiness.bean.SinglePeriodDataBean
import com.ctrip.tour.business.dashboard.tktBusiness.bean.SubMetricFiledBean
import com.ctrip.tour.business.dashboard.tktBusiness.biz.OverseaSinglePeriodTrendLineBiz
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig
import com.ctrip.tour.business.dashboard.utils.MapperUtil
import com.google.common.util.concurrent.JdkFutureAdapters
import org.springframework.scheduling.annotation.AsyncResult
import spock.lang.Specification

class DestinationBaseStrategyTest extends Specification {

    def baseReportQueryServiceClient = Mock(BIBaseReportQueryServiceClient)
    def remoteConfig = Mock(RemoteConfig)
    def overseaSinglePeriodTrendLineBiz = Mock(OverseaSinglePeriodTrendLineBiz)

    def destinationBaseStrategy = new DestinationBaseStrategy(baseReportQueryServiceClient: baseReportQueryServiceClient,
            remoteConfig: remoteConfig, overseaSinglePeriodTrendLineBiz: overseaSinglePeriodTrendLineBiz)
    def testObj = new DestinationBaseStrategy()

    def setup() {

    }


//    def "getBus101102SubMetricCardDataTest"() {
//
//        given: "设置请求参数"
//        def timeFilter = new TimeFilter(year: "2023", dateType: "half", half: "H1")
//        def metricInfoBean = new OverseaMetricInfoBean(metric: "101", destinationLevel: "海外")
//
//        and: "mock接口请求返回数据"
//        def currentStr = "{\"result\":\"[[1.3592781985E8,1.016936759E7]]\",\"metricList\":[\"ttd_suc_income\",\"ttd_suc_subsidy_rebate_profit\"]}"
//        def targetStr = "{\"result\":\"[[1.52235078E8,1.1738383E7]]\",\"metricList\":[\"ttd_trgt_income\",\"ttd_trgt_profit\"]}"
//        def lastyearStr = "{\"result\":\"[[3.335999403E7,2287794.67]]\",\"metricList\":[\"ttd_suc_income\",\"ttd_suc_subsidy_rebate_profit\"]}"
//        def _2019Str = "{\"result\":\"[[5980474.41,730117.44]]\",\"metricList\":[\"ttd_suc_income\",\"ttd_suc_subsidy_rebate_profit\"]}"
//        def mom1Str = "{\"result\":\"[[9.773813318E7,5345869.04]]\",\"metricList\":[\"ttd_suc_income\",\"ttd_suc_subsidy_rebate_profit\"]}"
//        def mom2Str = "{\"result\":\"[[5.56769037E7,3553407.96]]\",\"metricList\":[\"ttd_suc_income\",\"ttd_suc_subsidy_rebate_profit\"]}"
//        baseReportQueryServiceClient.getRawDataAsync(_) >> getFutureResponse(currentStr) >> getFutureResponse(targetStr) >> getFutureResponse(lastyearStr) >> getFutureResponse(_2019Str) >> getFutureResponse(mom1Str) >> getFutureResponse(mom2Str)
//
//        and: "mock qconfig配置信息"
//        remoteConfig.getConfigValue("oversea") >> "海外"
//        remoteConfig.getConfigValue("region") >> "大区"
//        remoteConfig.getConfigValue("subRegion") >> "子区域"
//
//        when: "调用指标卡接口"
//        def response = destinationBaseStrategy.getBus101102SubMetricCardData(timeFilter, metricInfoBean, "2023-04-23", "101", "destination").get()
//
//
//        then: "验证返回结果"
//        with(response) {
//            subMetric == "destination"
//            actualSubMetric == "destination"
//            defaultField == "region"
//            if (dimData) {
//                dimData.get("ttd_suc_income_2019").intValue() == 21
//            }
//        }
//
//
//    }


    def "getBus101102SubTrendlineDataTest"() {

        given: "设置请求参数"
        def request = new GetOverseaTrendLineDataRequestType(metric: "101", subMetric: "destination",
                timeFilter: new TimeFilter(year: "2023", dateType: "month", month: "04", timeFrame: 6))
        def examineConfigBeanListStr = "[{\"year\":\"2023\",\"month\":\"01\",\"timeMap\":{\"currentTime\":\"2023-01\",\"lastyearTime\":\"2022-01\",\"2019Time\":\"2019-01\"},\"isLastestPeriod\":false,\"limitTimeMap\":{},\"businessDashboardOverseaExamineeConfig\":{\"id\":2106,\"domainName\":\"leisun\",\"year\":\"2023\",\"quarter\":\"Q1\",\"examineMetric\":\"101;102\",\"destinationExamineLevel\":\"海外\",\"destinationExamineRange\":\"\",\"siteExamineRange\":\"\",\"channelExamineRange\":\"\",\"queryD\":\"2023-04-23\",\"datachangeLasttime\":\"Apr 23, 2023 12:33:46 AM\"},\"dateType\":\"month\"},{\"year\":\"2023\",\"month\":\"02\",\"timeMap\":{\"currentTime\":\"2023-02\",\"lastyearTime\":\"2022-02\",\"2019Time\":\"2019-02\"},\"isLastestPeriod\":false,\"limitTimeMap\":{},\"businessDashboardOverseaExamineeConfig\":{\"id\":2106,\"domainName\":\"leisun\",\"year\":\"2023\",\"quarter\":\"Q1\",\"examineMetric\":\"101;102\",\"destinationExamineLevel\":\"海外\",\"destinationExamineRange\":\"\",\"siteExamineRange\":\"\",\"channelExamineRange\":\"\",\"queryD\":\"2023-04-23\",\"datachangeLasttime\":\"Apr 23, 2023 12:33:46 AM\"},\"dateType\":\"month\"},{\"year\":\"2023\",\"month\":\"03\",\"timeMap\":{\"currentTime\":\"2023-03\",\"lastyearTime\":\"2022-03\",\"2019Time\":\"2019-03\"},\"isLastestPeriod\":false,\"limitTimeMap\":{},\"businessDashboardOverseaExamineeConfig\":{\"id\":2106,\"domainName\":\"leisun\",\"year\":\"2023\",\"quarter\":\"Q1\",\"examineMetric\":\"101;102\",\"destinationExamineLevel\":\"海外\",\"destinationExamineRange\":\"\",\"siteExamineRange\":\"\",\"channelExamineRange\":\"\",\"queryD\":\"2023-04-23\",\"datachangeLasttime\":\"Apr 23, 2023 12:33:46 AM\"},\"dateType\":\"month\"},{\"year\":\"2023\",\"month\":\"04\",\"timeMap\":{\"currentTime\":\"2023-04\",\"lastyearTime\":\"2022-04\",\"2019Time\":\"2019-04\"},\"isLastestPeriod\":true,\"limitTimeMap\":{\"popYearTime\":\"2019-04-22\",\"time\":\"2023-04-22\",\"lastYearTime\":\"2022-04-22\"},\"businessDashboardOverseaExamineeConfig\":{\"id\":2131,\"domainName\":\"leisun\",\"year\":\"2023\",\"quarter\":\"Q2\",\"examineMetric\":\"101;102\",\"destinationExamineLevel\":\"海外\",\"destinationExamineRange\":\"\",\"siteExamineRange\":\"\",\"channelExamineRange\":\"\",\"queryD\":\"2023-04-23\",\"datachangeLasttime\":\"Apr 23, 2023 12:33:46 AM\"},\"dateType\":\"month\"}]"
        def examineConfigBeanList = MapperUtil.str2List(examineConfigBeanListStr, ExamineConfigBean.class)


        and: "mock接口请求返回数据"
        def period1Str = "{\"periodReachList\":[[\"2023-01\",5.152155135E7,3974980.76]],\"periodTargetList\":[[\"2023-01\",4.2548205E7,3412738.0]],\"periodLastyearList\":[[\"2023-01\",2.421961251026368,3.2064504107520824]],\"period2019List\":[[\"2023-01\",-0.5519399139329069,-0.482795761809404]],\"reachHeaderList\":[\"time\",\"ttd_suc_income\",\"ttd_suc_subsidy_rebate_profit\"],\"targetHeaderList\":[\"time\",\"ttd_trgt_income\",\"ttd_trgt_profit\"],\"lastyearHeaderList\":[\"time\",\"ttd_suc_income\",\"ttd_suc_subsidy_rebate_profit\"],\"_2019HeaderList\":[\"time\",\"ttd_suc_income\",\"ttd_suc_subsidy_rebate_profit\"]}"
        def period2Str = "{\"periodReachList\":[[\"2023-02\",4.533452563E7,3044043.35]],\"periodTargetList\":[[\"2023-02\",4.9206132E7,4232942.0]],\"periodLastyearList\":[[\"2023-02\",4.742095298046927,4.785634773739476]],\"period2019List\":[[\"2023-02\",-0.7161650077102123,-0.7466759097352278]],\"reachHeaderList\":[\"time\",\"ttd_suc_income\",\"ttd_suc_subsidy_rebate_profit\"],\"targetHeaderList\":[\"time\",\"ttd_trgt_income\",\"ttd_trgt_profit\"],\"lastyearHeaderList\":[\"time\",\"ttd_suc_income\",\"ttd_suc_subsidy_rebate_profit\"],\"_2019HeaderList\":[\"time\",\"ttd_suc_income\",\"ttd_suc_subsidy_rebate_profit\"]}"
        def period3Str = "{\"periodReachList\":[[\"2023-03\",6.344180914E7,3705574.12]],\"periodTargetList\":[[\"2023-03\",4.2846744E7,3519464.0]],\"periodLastyearList\":[[\"2023-03\",6.338458160154977,5.29717657108354]],\"period2019List\":[[\"2023-03\",0.28748430921468304,-0.23592333823614464]],\"reachHeaderList\":[\"time\",\"ttd_suc_income\",\"ttd_suc_subsidy_rebate_profit\"],\"targetHeaderList\":[\"time\",\"ttd_trgt_income\",\"ttd_trgt_profit\"],\"lastyearHeaderList\":[\"time\",\"ttd_suc_income\",\"ttd_suc_subsidy_rebate_profit\"],\"_2019HeaderList\":[\"time\",\"ttd_suc_income\",\"ttd_suc_subsidy_rebate_profit\"]}"
        def period4Str = "{\"periodReachList\":[[\"2023-04\",7.867375694E7,4349025.13]],\"periodTargetList\":[[\"2023-04\",7.032687E7,5994220.0]],\"periodLastyearList\":[[\"2023-04\",12.36546527574408,5.9554677193154735]],\"period2019List\":[[\"2023-04\",-0.07048267877979464,0.1866154280227128]],\"reachHeaderList\":[\"time\",\"ttd_suc_income\",\"ttd_suc_subsidy_rebate_profit\"],\"targetHeaderList\":[\"time\",\"ttd_trgt_income\",\"ttd_trgt_profit\"],\"lastyearHeaderList\":[\"time\",\"ttd_suc_income\",\"ttd_suc_subsidy_rebate_profit\"],\"_2019HeaderList\":[\"time\",\"ttd_suc_income\",\"ttd_suc_subsidy_rebate_profit\"]}"
        overseaSinglePeriodTrendLineBiz.getBus101102SubSinglePeriodTrendLineData(_, _, _, _, _) >>> [getTrendLineFutureResponse(period1Str), getTrendLineFutureResponse(period2Str), getTrendLineFutureResponse(period3Str), getTrendLineFutureResponse(period4Str)]

        and: "mock qconfig配置信息"
        remoteConfig.getConfigValue("oversea") >> "海外"
        remoteConfig.getConfigValue("region") >> "大区"
        remoteConfig.getConfigValue("subRegion") >> "子区域"

        when: "调用趋势线接口"
        def response = destinationBaseStrategy.getBus101102SubTrendlineData(request, '2023-04-23', examineConfigBeanList)

        then: "验证返回结果"
        with(response) {
            if (trendLineDetailInfoList) {
                trendLineDetailInfoList.size() == 4
                def innerList = trendLineDetailInfoList.get(2).getTrendLineDataItemList()
                if (innerList) {
                    innerList.size() == 4
                    innerList.get(0).getValue().intValue() == 2
                }
            }
        }

    }


//    def "getBus101102SubDrillDownBaseInfoTest"() {
//
//        given: "设置请求参数"
//        def request = new GetOverseaDrillDownBaseInfoRequestType(metric: "101", subMetric: "destination",
//                domainName: "leisun", timeFilter: new TimeFilter(year: "2023", dateType: "half", half: "H1"))
//        def metricInfoBean = new OverseaMetricInfoBean(metric: "101", destinationLevel: "海外")
//
//        and: "mock接口请求返回数据"
//        def regionStr = "{\"result\":\"[[100,\\\"North East Asia\\\"],[200,\\\"South East Asia\\\"],[300,\\\"Hong Kong&Macau&Singapore\\\"],[400,\\\"Long Haul\\\"],[500,\\\"Europe\\\"],[600,\\\"Travel Essencials\\\"]]\",\"groupList\":[\"region_id\",\"region_name_en\"],\"metricList\":[]}";
//        def provinceStr = "{\"result\":\"[[1001,\\\"Japan\\\"],[1002,\\\"Korea\\\"],[1003,\\\"Taiwan\\\"],[2001,\\\"Thailand\\\"],[2002,\\\"Malaysia&Indonesia&Brunei Darussalam\\\"],[2003,\\\"Vietnam&Cambodia&Philippines\\\"],[2004,\\\"Other region\\\"],[3001,\\\"Hong Kong&Macau\\\"],[3002,\\\"Singapore\\\"],[4001,\\\"America\\\"],[4002,\\\"Australia&New Zealand\\\"],[4003,\\\"Middle East&Africa\\\"],[4004,\\\"North Asia\\\"],[5001,\\\"Europe\\\"],[6001,\\\"Travel Essencials\\\"]]\",\"groupList\":[\"province_id\",\"province_name_en\"],\"metricList\":[]}"
//        def countryStr = "{\"result\":\"[[-9999,\\\"unkwn\\\"],[330,\\\"Maldives\\\"],[444,\\\"Mauritius\\\"],[1615,\\\"Barbados\\\"],[20340,\\\"Estonia\\\"],[20354,\\\"United Kingdom\\\"],[20372,\\\"Armenia\\\"],[20391,\\\"Mongolia\\\"],[20424,\\\"New Caledonia\\\"],[20435,\\\"French Polynesia\\\"],[21396,\\\"Singapore\\\"],[21600,\\\"Saint Lucia\\\"],[21608,\\\"Jamaica\\\"],[21615,\\\"U.S. Virgin Islands\\\"],[21617,\\\"Puerto Rico\\\"],[100021,\\\"Thailand\\\"],[100022,\\\"Malaysia\\\"],[100023,\\\"Belgium\\\"],[100024,\\\"France\\\"],[100025,\\\"Germany\\\"],[100026,\\\"Italy\\\"],[100027,\\\"Austria\\\"],[100028,\\\"Netherlands\\\"],[100029,\\\"Canada\\\"],[100030,\\\"Egypt\\\"],[100035,\\\"Spain\\\"],[100036,\\\"Greece\\\"],[100041,\\\"Japan\\\"],[100042,\\\"South Korea\\\"],[100044,\\\"Philippines\\\"],[100045,\\\"Indonesia\\\"],[100046,\\\"Vietnam\\\"],[100047,\\\"United States\\\"],[100048,\\\"Australia\\\"],[100049,\\\"South Africa\\\"],[100050,\\\"Switzerland\\\"],[100069,\\\"Denmark\\\"],[100073,\\\"Türkiye\\\"],[100074,\\\"Israel\\\"],[100075,\\\"Malta\\\"],[100078,\\\"New Zealand\\\"],[100079,\\\"Nepal\\\"],[100080,\\\"India\\\"],[100081,\\\"Cambodia\\\"],[100083,\\\"Russia\\\"],[100084,\\\"Sri Lanka\\\"],[100085,\\\"Jordan\\\"],[100087,\\\"Kenya\\\"],[100089,\\\"Brunei Darussalam\\\"],[100090,\\\"Ireland\\\"],[100092,\\\"Finland\\\"],[100094,\\\"Czech Republic\\\"],[100096,\\\"Iceland\\\"],[100097,\\\"Sweden\\\"],[100098,\\\"Norway\\\"],[100099,\\\"United Arab Emirates\\\"],[100101,\\\"Hungary\\\"],[100102,\\\"Fiji\\\"],[100105,\\\"Laos\\\"],[100106,\\\"Portugal\\\"],[100108,\\\"Mexico\\\"],[100109,\\\"Brazil\\\"],[100111,\\\"Argentina\\\"],[100115,\\\"Tunisia\\\"],[100116,\\\"Cyprus\\\"],[100118,\\\"Poland\\\"],[100120,\\\"Romania\\\"],[100121,\\\"Saudi Arabia\\\"],[100126,\\\"Ecuador\\\"],[100129,\\\"Croatia\\\"],[100131,\\\"Monaco\\\"],[100132,\\\"Morocco\\\"],[100134,\\\"Colombia\\\"],[100135,\\\"Panama\\\"],[100137,\\\"Qatar\\\"],[100140,\\\"Costa Rica\\\"],[100145,\\\"Dominican Republic\\\"],[100151,\\\"Namibia\\\"],[100200,\\\"Côte d'Ivoire\\\"],[100215,\\\"Somalia\\\"],[100220,\\\"El Salvador\\\"],[100223,\\\"Albania\\\"],[100229,\\\"Georgia\\\"],[100259,\\\"Bolivia\\\"],[100260,\\\"Uruguay\\\"],[100261,\\\"Luxembourg\\\"],[110000,\\\"China\\\"]]\",\"groupList\":[\"country_id\",\"country_name_en\"],\"metricList\":[]}"
//        def examineeStr = "{\"result\":\"[[\\\"danyangwu\\\",\\\"danyang wu （吴丹阳）\\\"],[\\\"eric.huang\\\",\\\"Eric Huang （黃孟樺）\\\"],[\\\"hongjin\\\",\\\"hong jin （金鸿）\\\"],[\\\"ivy.tan\\\",\\\"Ivy Tan\\\"],[\\\"jack.yee\\\",\\\"Jack Yee （余聪杰）\\\"],[\\\"jaemin.park\\\",\\\"Jaemin Park\\\"],[\\\"jcdong\\\",\\\"Kathy Dong （董纪春）\\\"],[\\\"jcn\\\",\\\"CN CHAN （詹馨恩）\\\"],[\\\"jimmy.aman\\\",\\\"Jimmy Aman\\\"],[\\\"junyoung.kim\\\",\\\"Junyoung Kim\\\"],[\\\"karina.chong\\\",\\\"Karina Chong\\\"],[\\\"kelvin.ong\\\",\\\"Kelvin Ong\\\"],[\\\"kim_sojung\\\",\\\"Sojung Kim\\\"],[\\\"lalita.sincharoenkun\\\",\\\"Lalita Sincharoenkun\\\"],[\\\"masun\\\",\\\"Michelle SUN （孙敏安）\\\"],[\\\"mh_jiang\\\",\\\"muhwa Chiang （江慕华）\\\"],[\\\"ming.li\\\",\\\"Ming Li （李明）\\\"],[\\\"mtxu\\\",\\\"Nicole Xu （许孟婷）\\\"],[\\\"mydinh.ho\\\",\\\"Ho My Dinh (Ho My Dinh)\\\"],[\\\"myra.ho\\\",\\\"Myra Ho\\\"],[\\\"nelson.lee\\\",\\\"Nelson Lee\\\"],[\\\"rennay.lam\\\",\\\"Rennay Lam\\\"],[\\\"seungjoo.baek\\\",\\\"SEUNG JOO BAEK\\\"],[\\\"siqicheng\\\",\\\"Stella Cheng （程思琦）\\\"],[\\\"siriwimon.siemsorn\\\",\\\"Siriwimon Siemsorn\\\"],[\\\"siyili\\\",\\\"Siyi Li （李思怡）\\\"],[\\\"sun_yi\\\",\\\"Yi Sun （孙昳）\\\"],[\\\"swluo\\\",\\\"Tony Luo （罗圣文）\\\"],[\\\"taijung.chung\\\",\\\"PATRICK CHUNG\\\"],[\\\"tianyangma\\\",\\\"Ma Tianyang （馬天揚）\\\"],[\\\"w.ren\\\",\\\"wan ren （任婉）\\\"],[\\\"wangxiye\\\",\\\"Sheila Wang （王玺烨）\\\"],[\\\"wang_jjie\\\",\\\"junjie wang （王骏婕）\\\"],[\\\"wen_luo\\\",\\\"nova luo （罗文）\\\"],[\\\"wjyu\\\",\\\"Serene YU （俞文娟）\\\"],[\\\"xie_c\\\",\\\"Ashley Xie （谢晨）\\\"],[\\\"xinmoli\\\",\\\"Kerina Li （李昕默）\\\"],[\\\"xm.he\\\",\\\"Hesmond He （何旭明）\\\"],[\\\"xsli\\\",\\\"Isabella Li （李晓霜）\\\"],[\\\"yalilin\\\",\\\"Pearl Lin （林雅莉）\\\"],[\\\"yiminli\\\",\\\"yimin li （李一敏）\\\"],[\\\"yuri.misono\\\",\\\"Yuri Misono （御園 有里）\\\"],[\\\"yuying_huang\\\",\\\"Elixir Huang （黄玉莹）\\\"],[\\\"yuyushu\\\",\\\"yuyu shu （舒昱昱）\\\"],[\\\"zhcui\\\",\\\"zhenghua cui （崔正花）\\\"]]\",\"groupList\":[\"examinee\",\"examinee_display\"],\"metricList\":[]}"
//        def viewspotStr = "{\"result\":\"[[\\\"0\\\",\\\"unkwn\\\"],[\\\"100985972\\\",\\\"Kiztopia (Marina Square)\\\"],[\\\"104146685\\\",\\\"Big Fun Museum\\\"],[\\\"104148371\\\",\\\"Sands Expo & Convention Centre\\\"],[\\\"104153893\\\",\\\"Museum of Illusions\\\"],[\\\"107330861\\\",\\\"The Londoner Macao\\\"],[\\\"107420\\\",\\\"Desert Safari Camp\\\"],[\\\"107511\\\",\\\"卡帕多奇亚热气球\\\"],[\\\"107549\\\",\\\"Roppongi Hills\\\"],[\\\"107563\\\",\\\"Acropolis Museum\\\"],[\\\"107564\\\",\\\"River Thames\\\"],[\\\"107593\\\",\\\"Bali Safari and Marine Park\\\"],[\\\"107597\\\",\\\"Nusa Lembongan\\\"],[\\\"107614\\\",\\\"Calypso Cabaret\\\"],[\\\"107616\\\",\\\"Sea Life Bangkok Ocean World\\\"],[\\\"107617\\\",\\\"Sky100 Observation Deck\\\"],[\\\"107852\\\",\\\"Hueree Nature Life Park\\\"],[\\\"108080\\\",\\\"Marina Square\\\"],[\\\"108118\\\",\\\"Orchard Road\\\"],[\\\"108733450\\\",\\\"LEGOLAND® Discovery Centre Hong Kong\\\"],[\\\"109788\\\",\\\"Underwater World Langkawi\\\"],[\\\"109804\\\",\\\"Eco Green Campus [Daegwallyeong Samyang Ranch]\\\"],[\\\"109837\\\",\\\"Mabul Island\\\"],[\\\"109854\\\",\\\"Kapalai\\\"],[\\\"109872\\\",\\\"Similan Islands\\\"],[\\\"109876\\\",\\\"Tokyo Skytree\\\"],[\\\"109964\\\",\\\"The Queen's Gallery, Buckingham Palace\\\"],[\\\"109976\\\",\\\"London Zoo\\\"],[\\\"110100\\\",\\\"Pulau Dayang Bunting\\\"],[\\\"110180\\\",\\\"Chiangmai zoo\\\"],[\\\"110230\\\",\\\"Antelope Canyon\\\"],[\\\"110237\\\",\\\"Flam Train\\\"],[\\\"110284\\\",\\\"National Art Museum of Catalonia\\\"],[\\\"110314\\\",\\\"Asakusa\\\"],[\\\"110385\\\",\\\"Aquarium Barcelona\\\"],[\\\"110448\\\",\\\"Simon Cabaret Phuket\\\"],[\\\"110499\\\",\\\"Yasaka Shrine\\\"],[\\\"110540\\\",\\\"Amanohashidate\\\"],[\\\"113164\\\",\\\"The Peak Tram\\\"],[\\\"113165\\\",\\\"Star Ferry\\\"],[\\\"113372\\\",\\\"Peak Tower\\\"],[\\\"118481279\\\",\\\"Alangka Cruise\\\"],[\\\"11976\\\",\\\"Borghese Gallery and Museum\\\"],[\\\"12018\\\",\\\"Cathedral of Santa Maria del Fiore\\\"],[\\\"12023\\\",\\\"Uffizi Gallery\\\"],[\\\"12070\\\",\\\"Milan Cathedral\\\"],[\\\"12079\\\",\\\"Santa Maria delle Grazie\\\"],[\\\"121152026\\\",\\\"愤怒鸟游乐中心(澳门十六浦店)\\\"],[\\\"122816\\\",\\\"Gold Coast beach\\\"],[\\\"122861\\\",\\\"Fukuoka Tower\\\"]]\",\"groupList\":[\"viewspot_id\",\"viewspot_name_en\"],\"metricList\":[]}"
//        baseReportQueryServiceClient.getRawDataAsync(_) >>> [getFutureResponse(regionStr), getFutureResponse(provinceStr), getFutureResponse(countryStr), getFutureResponse(examineeStr), getFutureResponse(viewspotStr)]
//
//        and: "mock qconfig配置信息"
//        remoteConfig.getConfigValue("oversea") >> "海外"
//        remoteConfig.getConfigValue("region") >> "大区"
//        remoteConfig.getConfigValue("subRegion") >> "子区域"
//        def regionConfigStr = "{\"subMetricList\":[\"destination\",\"destination_c\",\"destination_t\"],\"field\":\"region\",\"baseInfoId\":6,\"baseInfoGroupList\":[\"region_id\",\"region_name\"],\"baseInfoLikeIndexList\":[1],\"tableDataIdMap\":{\"other\":6,\"target\":1},\"bubbleGroupListMap\":{\"other\":[\"region_id\",\"region_name\"],\"target\":[\"region_id\"]},\"tableGroupListMap\":{\"other\":[\"region_id\",\"region_name\"],\"target\":[\"region_id\"]},\"conditionColumn\":\"region_id\",\"needTarget\":true,\"needBubble\":true,\"headerFieldList\":[\"region_name\"],\"showField\":\"region_name\"}"
//        def provinceConfigStr = "{\"subMetricList\":[\"destination\",\"destination_c\",\"destination_t\"],\"field\":\"province\",\"baseInfoId\":6,\"baseInfoGroupList\":[\"province_id\",\"province_name\"],\"baseInfoLikeIndexList\":[1],\"tableDataIdMap\":{\"other\":6,\"target\":1},\"bubbleGroupListMap\":{\"other\":[\"province_id\",\"province_name\"],\"target\":[\"province_id\"]},\"tableGroupListMap\":{\"other\":[\"region_id\",\"region_name\",\"province_id\",\"province_name\"],\"target\":[\"region_id\",\"province_id\"]},\"conditionColumn\":\"province_id\",\"needTarget\":true,\"needBubble\":true,\"headerFieldList\":[\"region_name\",\"province_name\"],\"showField\":\"province_name\"}"
//        def countryConfigStr = "{\"subMetricList\":[\"destination\",\"destination_c\",\"destination_t\"],\"field\":\"country\",\"baseInfoId\":6,\"baseInfoGroupList\":[\"country_id\",\"country_name\"],\"baseInfoLikeIndexList\":[1],\"tableDataIdMap\":{\"other\":6},\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\",\"country_id\",\"country_name\"]},\"conditionColumn\":\"country_id\",\"needTarget\":false,\"needBubble\":false,\"headerFieldList\":[\"region_name\",\"province_name\",\"country_name\"]}"
//        def examineeConfigStr = "{\"subMetricList\":[\"destination\",\"destination_c\",\"destination_t\"],\"field\":\"examinee\",\"baseInfoId\":12,\"baseInfoGroupList\":[\"examinee\",\"examinee_display\"],\"baseInfoLikeIndexList\":[1],\"tableDataIdMap\":{\"2019\":6,\"target\":4,\"lastyear\":6,\"current\":12},\"tableGroupListMap\":{\"2019\":[\"region_id\",\"province_id\"],\"target\":[\"region_id\",\"province_id\",\"examinee\"],\"current\":[\"region_name\",\"region_id\",\"province_name\",\"province_id\",\"examinee_display\",\"examinee\"],\"lastyear\":[\"region_id\",\"province_id\"]},\"conditionColumn\":\"examinee\",\"pagingConditionColumn\":\"province_id\",\"needTarget\":true,\"needBubble\":false,\"headerFieldList\":[\"region_name\",\"province_name\",\"examinee_display\"]}"
//        def viewspotConfigStr = "{\"subMetricList\":[\"destination\",\"destination_c\",\"destination_t\"],\"field\":\"viewspot\",\"baseInfoId\":10,\"baseInfoGroupList\":[\"viewspot_id\",\"viewspot_name\"],\"baseInfoLikeIndexList\":[0,1],\"tableDataIdMap\":{\"current\":10,\"other\":11},\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\",\"country_name\",\"viewspot_id\",\"viewspot_name\"]},\"conditionColumn\":\"viewspot_id\",\"needTarget\":false,\"needBubble\":false,\"headerFieldList\":[\"region_name\",\"province_name\",\"country_name\",\"viewspot_id\",\"viewspot_name\"]}"
//
//        remoteConfig.getSubMetricFiledBean(_, _, "region") >> MapperUtil.str2Obj(regionConfigStr, SubMetricFiledBean.class)
//        remoteConfig.getSubMetricFiledBean(_, _, "province") >> MapperUtil.str2Obj(provinceConfigStr, SubMetricFiledBean.class)
//        remoteConfig.getSubMetricFiledBean(_, _, "country") >> MapperUtil.str2Obj(countryConfigStr, SubMetricFiledBean.class)
//        remoteConfig.getSubMetricFiledBean(_, _, "examinee") >> MapperUtil.str2Obj(examineeConfigStr, SubMetricFiledBean.class)
//        remoteConfig.getSubMetricFiledBean(_, _, "viewspot") >> MapperUtil.str2Obj(viewspotConfigStr, SubMetricFiledBean.class)
//
//
//        when: "调用获取下钻维度接口"
//        def response = destinationBaseStrategy.getBus101102SubDrillDownBaseInfo(request, "2023-04-23", metricInfoBean)
//
//
//        then: "验证返回结果"
//        with(response) {
//            defaultChosenField == "region"
//            if (fieldDataItemList) {
//                def innerObject = fieldDataItemList.get(0)
//                with(innerObject) {
//                    field == "region"
//                    needBubble
//                    fieldValueItemList.size() == 6
//                }
//            }
//        }
//
//
//    }


//    def "getBus101102SubTableDataTest"() {
//
//        given: "设置请求参数"
//        def request = new GetOverseaTableDataRequestType(metric: "101", subMetric: "destination",
//                domainName: "leisun", timeFilter: new TimeFilter(year: "2023", dateType: "half", half: "H1"),
//                drillDownFilter: new DrillDownFilter(field: "region", fieldValueList: []), source: "firstpage")
//        def metricInfoBean = new OverseaMetricInfoBean(metric: "101", destinationLevel: "海外")
//
//        and: "mock接口请求返回数据"
//        def currentStr = "{\"result\":\"[[300,\\\"Hong Kong&Macau&Singapore\\\",1.2406041515E8,5726617.62],[100,\\\"North East Asia\\\",5.102199418E7,3433350.25],[200,\\\"South East Asia\\\",2.876787383E7,2950722.04],[400,\\\"Long Haul\\\",2.24252649E7,1557309.29],[500,\\\"Europe\\\",8512234.18,649108.73],[600,\\\"Travel Essencials\\\",4183860.82,756515.43]]\",\"groupList\":[\"region_id\",\"region_name_en\"],\"metricList\":[\"ttd_suc_income\",\"ttd_suc_subsidy_rebate_profit\"],\"totalNum\":6}"
//        def targetStr = "{\"result\":\"[[200,6.6753221E7,6578311.0],[100,6.5910915E7,5430377.0],[300,1.39081049E8,9196083.0],[500,1.7613524E7,1313907.0],[600,1.3300524E7,2811642.0],[400,3.5999687E7,3109172.0]]\",\"groupList\":[\"region_id\"],\"metricList\":[\"ttd_trgt_income\",\"ttd_trgt_profit\"]}"
//        def lastyearStr = "{\"result\":\"[[500,\\\"Europe\\\",1556434.15,96089.82],[600,\\\"Travel Essencials\\\",586650.54,113361.69],[200,\\\"South East Asia\\\",5538051.82,440400.75],[400,\\\"Long Haul\\\",2442527.23,212189.18],[300,\\\"Hong Kong&Macau&Singapore\\\",1.975970606E7,838132.99],[100,\\\"North East Asia\\\",7599356.64,984653.63]]\",\"groupList\":[\"region_id\",\"region_name_en\"],\"metricList\":[\"ttd_suc_income\",\"ttd_suc_subsidy_rebate_profit\"]}"
//        def _2019Str = "{\"result\":\"[[600,\\\"Travel Essencials\\\",3.536216141E7,7971339.99],[400,\\\"Long Haul\\\",4.649630876E7,3824804.15],[500,\\\"Europe\\\",1.343354924E7,966506.29],[200,\\\"South East Asia\\\",2.519974471E7,2779750.86],[300,\\\"Hong Kong&Macau&Singapore\\\",2.1057947417E8,9552117.45],[100,\\\"North East Asia\\\",7.755337216E7,3122203.29]]\",\"groupList\":[\"region_id\",\"region_name_en\"],\"metricList\":[\"ttd_suc_income\",\"ttd_suc_subsidy_rebate_profit\"]}"
//        def mom1Str = "{\"result\":\"[[400,\\\"Long Haul\\\",7980777.45,468454.31],[600,\\\"Travel Essencials\\\",1417920.77,243225.8],[500,\\\"Europe\\\",4964930.6,353644.63],[200,\\\"South East Asia\\\",1.007016037E7,1093056.42],[300,\\\"Hong Kong&Macau&Singapore\\\",5.387721476E7,2137518.36],[100,\\\"North East Asia\\\",1.942712923E7,1049969.52]]\",\"groupList\":[\"region_id\",\"region_name_en\"],\"metricList\":[\"ttd_suc_income\",\"ttd_suc_subsidy_rebate_profit\"]}"
//        def mom2Str = "{\"result\":\"[[400,\\\"Long Haul\\\",6608386.8,396293.68],[600,\\\"Travel Essencials\\\",1198074.48,208595.02],[500,\\\"Europe\\\",1476464.16,121877.66],[300,\\\"Hong Kong&Macau&Singapore\\\",2.721612396E7,1097250.32],[200,\\\"South East Asia\\\",6264403.44,716759.14],[100,\\\"North East Asia\\\",1.291345086E7,1012632.14]]\",\"groupList\":[\"region_id\",\"region_name_en\"],\"metricList\":[\"ttd_suc_income\",\"ttd_suc_subsidy_rebate_profit\"]}"
//        baseReportQueryServiceClient.getRawDataAsync(_) >> getFutureResponse(currentStr) >> getFutureResponse(targetStr) >> getFutureResponse(lastyearStr) >> getFutureResponse(_2019Str) >> getFutureResponse(mom1Str) >> getFutureResponse(mom2Str)
//
//        and: "mock qconfig配置信息"
//        remoteConfig.getConfigValue("oversea") >> "海外"
//        remoteConfig.getConfigValue("region") >> "大区"
//        remoteConfig.getConfigValue("subRegion") >> "子区域"
//        def regionConfigStr = "{\"subMetricList\":[\"destination\",\"destination_c\",\"destination_t\"],\"field\":\"region\",\"baseInfoId\":6,\"baseInfoGroupList\":[\"region_id\",\"region_name\"],\"baseInfoLikeIndexList\":[1],\"tableDataIdMap\":{\"other\":6,\"target\":1},\"bubbleGroupListMap\":{\"other\":[\"region_id\",\"region_name\"],\"target\":[\"region_id\"]},\"tableGroupListMap\":{\"other\":[\"region_id\",\"region_name\"],\"target\":[\"region_id\"]},\"conditionColumn\":\"region_id\",\"needTarget\":true,\"needBubble\":true,\"headerFieldList\":[\"region_name\"],\"showField\":\"region_name\"}"
//        remoteConfig.getSubMetricFiledBean(_, _, "region") >> MapperUtil.str2Obj(regionConfigStr, SubMetricFiledBean.class)
//
//
//        when: "调用下钻数据接口"
//        def response = destinationBaseStrategy.getBus101102SubTableData(request, "2023-04-23", metricInfoBean)
//
//
//        then: "验证返回结果"
//        with(response) {
//            totalNum == 6
//            if (tableDataItemList) {
//                tableDataItemList.size() == 6
//                tableDataItemList.get(0).getFieldMap().get("region_id") == "300"
//                tableDataItemList.get(0).getDimMap().get("ttd_suc_subsidy_rebate_profit_lastyear").intValue() == 5
//            }
//            tableHeaderList.size() == 6
//            showField == "region_name"
//        }
//
//
//    }


//    @Unroll
//    def "getBus103SubMetricCardDataTest"() {
//        given: "设定相关方法入参"
//        when:
//        def result = testObj.getBus103SubMetricCardData(timeFilter, metricInfoBean, d, metric, subMetric)
//
//        then: "验证返回结果里属性值是否符合预期"
//        result == expectedResult
//        where: "表格方式验证多种分支调用场景"
//        metricInfoBean              | d   | subMetric   | metric   | timeFilter || expectedResult
//        new OverseaMetricInfoBean() | "d" | "subMetric" | "metric" | null       || null
//    }
//
//    @Unroll
//    def "getBus103SubTrendLineDataTest"() {
//        given: "设定相关方法入参"
//        and: "Mock相关接口返回"
//        overseaSinglePeriodTrendLineBiz.getBus103SubSinglePeriodTrendLineData(_, _, _, _, _) >> new SinglePeriodDataBean()
//
//        when:
//        def result = testObj.getBus103SubTrendLineData(request, d, examineConfigBeanList)
//
//        then: "验证返回结果里属性值是否符合预期"
//        result == expectedResult
//        where: "表格方式验证多种分支调用场景"
//        request | d   | examineConfigBeanList                                                                                                                 || expectedResult
//        null    | "d" | [new ExamineConfigBean("year", "month", "quarter", ["String": "String"], Boolean.TRUE, ["String": "String"], null, null, "dateType")] || null
//    }
//
//    @Unroll
//    def "getBus103SubDrillDownBaseInfoTest"() {
//        given: "设定相关方法入参"
//        when:
//        def result = testObj.getBus103SubDrillDownBaseInfo(request, d, metricInfoBean)
//
//        then: "验证返回结果里属性值是否符合预期"
//        result == expectedResult
//        where: "表格方式验证多种分支调用场景"
//        request | d   | metricInfoBean              || expectedResult
//        null    | "d" | new OverseaMetricInfoBean() || null
//    }
//
//    @Unroll
//    def "getBus103SubTableDataTest"() {
//        given: "设定相关方法入参"
//        and: "Mock相关接口返回"
//        remoteConfig.getSubMetricFiledBean(_, _, _) >> new SubMetricFiledBean()
//
//        when:
//        def result = testObj.getBus103SubTableData(request, d, metricInfoBean)
//
//        then: "验证返回结果里属性值是否符合预期"
//        result == expectedResult
//        where: "表格方式验证多种分支调用场景"
//        request | d   | metricInfoBean              || expectedResult
//        null    | "d" | new OverseaMetricInfoBean() || null
//    }
//



    def getFutureResponse(def str) {
        def response = MapperUtil.str2Obj(str, GetRawDataResponseType.class)
        return JdkFutureAdapters.listenInPoolThread(new AsyncResult<>(response))
    }

    def getTrendLineFutureResponse(def str) {
        def response = MapperUtil.str2Obj(str, SinglePeriodDataBean.class)
        return JdkFutureAdapters.listenInPoolThread(new AsyncResult<>(response))
    }
}
