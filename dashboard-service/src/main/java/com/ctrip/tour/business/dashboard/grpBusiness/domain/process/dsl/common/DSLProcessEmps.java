package com.ctrip.tour.business.dashboard.grpBusiness.domain.process.dsl.common;

import com.ctrip.soa._24922.DSLRequestType;
import com.ctrip.soa._24922.EnumOperators;
import com.ctrip.soa._24922.WhereCondition;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.AbstractPreDSLProcess;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.dsl.DSLUtils;
import com.ctrip.tour.business.dashboard.grpBusiness.service.DepTreeCache;
import com.ctrip.tour.business.dashboard.grpBusiness.service.UserInfoService;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class DSLProcessEmps extends AbstractPreDSLProcess {
    private UserInfoService userInfoService;
    private DepTreeCache depTreeCache;

    List<String> empCodes;

    public static AbstractPreDSLProcess getInstance(UserInfoService userInfoService, DepTreeCache depTreeCache, List<String> empCodes) {
        DSLProcessEmps instance = new DSLProcessEmps();
        instance.userInfoService = userInfoService;
        instance.depTreeCache = depTreeCache;
        instance.empCodes = empCodes;
        return instance;
    }

    @Override
    public DSLRequestType process(DSLRequestType dslRequestType, EarlyReturn earlyReturn) {
        // 获取业务线
        WhereCondition businessTypeCondition = DSLUtils.getWhereConditionByFilterName(dslRequestType, "business_line");
        if (businessTypeCondition == null || businessTypeCondition.getFilterValues().isEmpty()) {
            earlyReturn.setStatus(-1);
            return dslRequestType;
        }
        int businessType = Integer.parseInt(businessTypeCondition.getFilterValues().get(0));
        // 定制游不需要用户
        if (businessType == 230) {
            return dslRequestType;
        }
        // 初始化原始用户信息
        String orgEmpCode = null;
        if (empCodes == null || empCodes.isEmpty()) {
            String empMapping = userInfoService.getMappingEmpCode();
            if (empMapping != null && !empMapping.isEmpty()) {
                orgEmpCode = empMapping;
            }
        } else {
            orgEmpCode = empCodes.get(0);
        }
        // 非定制游必须有emp_code
        if (orgEmpCode == null) {
            earlyReturn.setStatus(-1);
            return dslRequestType;
        }
        // 初始化
        if (dslRequestType.getWhereCondition() == null) {
            dslRequestType.setWhereCondition(new WhereCondition());
        }
        if (dslRequestType.getWhereCondition().getSubWhereConditions() == null) {
            dslRequestType.getWhereCondition().setSubWhereConditions(new ArrayList<>());
        }

        // 管理的用户
        List<String> dep = depTreeCache.getManagedDeptByEmpCode(orgEmpCode);
        WhereCondition whereConditionEmpCodes = new WhereCondition(); // emp_codes
        dslRequestType.getWhereCondition().getSubWhereConditions().add(whereConditionEmpCodes);
        whereConditionEmpCodes.setFilterName("emp_codes");
        whereConditionEmpCodes.setOperators(EnumOperators.IN);
        // 获取用户
        List<String> emps = new ArrayList<>();
        if (dep == null || dep.isEmpty()) {
            emps.add(orgEmpCode);
        } else {
            switch (businessType) {
                case 100: // 100: 跟团游, SO010001(跟团游)
                    emps = depTreeCache.getAllEmpCodeByDepIDWithLimit(dep, Collections.emptyList()); // Collections.singletonList("SO010001")
                    break;
                case 210: // 210: 独立出游-私家团, SO010061(私家团)
                    emps = depTreeCache.getAllEmpCodeByDepIDWithLimit(dep, Collections.emptyList()); // Collections.singletonList("SO010061")
                    break;
                case 220: // 220: 独立出游-拼小团, SO013040(拼小团业务)
                    emps = depTreeCache.getAllEmpCodeByDepIDWithLimit(dep, Collections.emptyList()); // Collections.singletonList("SO013040")
                    break;
            }
        }
        whereConditionEmpCodes.setFilterValues(emps);
        // 原始用户
        WhereCondition whereConditionOrgEmpCodes = new WhereCondition(); // emp_codes
        dslRequestType.getWhereCondition().getSubWhereConditions().add(whereConditionOrgEmpCodes);

        whereConditionOrgEmpCodes.setOperators(EnumOperators.IN);
        whereConditionOrgEmpCodes.setFilterName("org_emp_code");
        whereConditionOrgEmpCodes.setFilterValues(Collections.singletonList(orgEmpCode));
        return dslRequestType;
    }
}
