package com.ctrip.tour.business.dashboard.sightArchives.service.Impl;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.sightArchives.dao.commonDao.AdmPrdTtdCpdTripDimInfoUnifiedOutputSRDao;
import com.ctrip.tour.business.dashboard.sightArchives.dao.qualityDao.AdmSevTtdViewspotDefectDetailDfDao;
import com.ctrip.tour.business.dashboard.sightArchives.dao.qualityDao.AdmSevTtdViewspotFileIndexDfDao;
import com.ctrip.tour.business.dashboard.sightArchives.dao.qualityDao.AdmSevTtdViewspotQuestionRankDfDao;
import com.ctrip.tour.business.dashboard.sightArchives.entity.commonEntity.AdmPrdTtdCpdTripDimInfoUnifiedOutput;
import com.ctrip.tour.business.dashboard.sightArchives.proxy.CommentsServiceProxy;
import com.ctrip.tour.business.dashboard.sightArchives.proxy.GargleTranslateServiceProxy;
import com.ctrip.tour.business.dashboard.sightArchives.proxy.TtdProductBasicServiceProxy;
import com.ctrip.tour.business.dashboard.sightArchives.service.CommonService;
import com.ctrip.tour.business.dashboard.sightArchives.service.QualityService;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.ScenicLanguageHelper;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.UserUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class QualityServiceImpl implements QualityService {

    @Autowired
    private CommentsServiceProxy commentsServiceProxy;

    @Autowired
    private AdmSevTtdViewspotQuestionRankDfDao admSevTtdViewspotQuestionRankDfDao;

    @Autowired
    private AdmSevTtdViewspotFileIndexDfDao admSevTtdViewspotFileIndexDfDao;

    @Autowired
    private AdmSevTtdViewspotDefectDetailDfDao admSevTtdViewspotDefectDetailDfDao;
    @Autowired
    private CommonService commonService;
    @Autowired
    private AdmPrdTtdCpdTripDimInfoUnifiedOutputSRDao admPrdTtdCpdTripDimInfoUnifiedOutputSRDao;
    @Autowired
    private RemoteConfig config;
    @Autowired
    TtdProductBasicServiceProxy ttdProductBasicServiceProxy;
    @Autowired
    GargleTranslateServiceProxy gargleTranslateServiceProxy;

    @Override
    public GetFulfillmentQualityMetricResponseType getFulfillmentQualityMetric(GetFulfillmentQualityMetricRequestType requestType) {

        SightArchivesCommonFilter commonFilter = requestType.getCommonFilter();

        GetFulfillmentQualityMetricResponseType responseType = new GetFulfillmentQualityMetricResponseType();
        String queryD = commonService.getQueryD();
        //指标卡
        Map<String, Object> metricCardRawResultMap = admSevTtdViewspotDefectDetailDfDao.queryMetricCard(
                commonFilter.getSightId(),
                commonFilter.getStartDate(),
                commonFilter.getEndDate(),
                commonFilter.getVendorIdList(),
                commonFilter.getBusinessType(),
                commonFilter.isNeedSubSight(),
                queryD
        );
        double weightedDefectCnt = ((BigDecimal)metricCardRawResultMap.get("weighted_defect_cnt")).doubleValue();
        int payOdrCnt = Math.toIntExact((Long) metricCardRawResultMap.get("pay_odr_cnt"));
        double weightedDefectRate = (payOdrCnt != 0) ? (weightedDefectCnt/payOdrCnt) : 0.0;
        responseType.setWeightedDefectCount(weightedDefectCnt);
        responseType.setWeightedDefectRate(weightedDefectRate);
        //Pop
        String popStartDate = null;
        String popEndDate = null;
        try {
            popEndDate = DateUtil.getDayOfInterval(commonFilter.getStartDate(),-1);
            int interval = DateUtil.getDateGap(commonFilter.getStartDate(), commonFilter.getEndDate());
            popStartDate = DateUtil.getDayOfInterval(popEndDate, -1*interval);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }

        Map<String, Object> popMetricCardRawResultMap = admSevTtdViewspotDefectDetailDfDao.queryMetricCard(
                commonFilter.getSightId(),
                popStartDate,
                popEndDate,
                commonFilter.getVendorIdList(),
                commonFilter.getBusinessType(),
                commonFilter.isNeedSubSight(),
                queryD
        );

        double popWeightedDefectCnt = ((BigDecimal)popMetricCardRawResultMap.get("weighted_defect_cnt")).doubleValue();
        int popPayOdrCnt = Math.toIntExact((Long) popMetricCardRawResultMap.get("pay_odr_cnt"));
        double popWeightedDefectRate = (popPayOdrCnt != 0) ? (popWeightedDefectCnt/popPayOdrCnt) : 0.0;
        if(popWeightedDefectRate !=0.0){
            responseType.setWeightedDefectRatePop((weightedDefectRate- popWeightedDefectRate)/popWeightedDefectRate);
        }
//        responseType.setWeightedDefectRatePop(popWeightedDefectRate);
        //Yoy
//        //Pop
//        String yoyStartDate = null;
//        String yoyEndDate = null;
//        try {
//            yoyStartDate = DateUtil.getYoyTime(commonFilter.getStartDate(), 1);
//            yoyEndDate = DateUtil.getYoyTime(commonFilter.getEndDate(), 1);
//        } catch (ParseException e) {
//            throw new RuntimeException(e);
//        }
        Map<String, Object> yoyMetricCardRawResultMap = admSevTtdViewspotDefectDetailDfDao.queryMetricCardYoy(
                commonFilter.getSightId(),
                commonFilter.getStartDate(),
                commonFilter.getEndDate(),
                commonFilter.getVendorIdList(),
                commonFilter.getBusinessType(),
                commonFilter.isNeedSubSight(),
                queryD
        );
        double yoyWeightedDefectCnt = ((BigDecimal)yoyMetricCardRawResultMap.get("weighted_defect_cnt")).doubleValue();
        int yoyPayOdrCnt = Math.toIntExact((Long) yoyMetricCardRawResultMap.get("pay_odr_cnt"));
        double yoyWeightedDefectRate = (yoyPayOdrCnt != 0) ? (yoyWeightedDefectCnt/yoyPayOdrCnt) : 0.0;
        if(yoyWeightedDefectRate !=0.0){
            responseType.setWeightedDefectRateYoy((weightedDefectRate- yoyWeightedDefectRate)/yoyWeightedDefectRate);
        }

        //饼图
        List<Map<String, Object>> pieChartRawResultList = admSevTtdViewspotDefectDetailDfDao.queryPieChart(
                commonFilter.getSightId(),
                commonFilter.getStartDate(),
                commonFilter.getEndDate(),
                commonFilter.getVendorIdList(),
                commonFilter.getBusinessType(),
                commonFilter.isNeedSubSight(),
                queryD
        );

        List<FulfillmentQualityPieChartSegment> pieChartSegmentList = new ArrayList<>();
        double sum = 0.0;
        for(Map<String,Object> pieChartRawResult : pieChartRawResultList){
            FulfillmentQualityPieChartSegment pieChartSegment = new FulfillmentQualityPieChartSegment();
            pieChartSegment.setName((String) pieChartRawResult.get("second_exam_target"));
            pieChartSegment.setValue((Double) pieChartRawResult.get("defect_count"));
            pieChartSegmentList.add(pieChartSegment);

            sum += pieChartSegment.getValue();
        }

        double threshold = sum*0.98;
        double sumValue = 0;
        pieChartSegmentList.sort((o1, o2) -> o2.getValue().compareTo(o1.getValue()));

        for (int i = 0; i < pieChartSegmentList.size(); i++) {
            FulfillmentQualityPieChartSegment pieChartSegment = pieChartSegmentList.get(i);
            if (sumValue >= threshold) {
                pieChartSegment.setName("其它");  //NOSONAR
                pieChartSegment.setValue(sum - sumValue);
                pieChartSegmentList = pieChartSegmentList.subList(0, i + 1);
                break;
            }
            sumValue += pieChartSegment.getValue();
        }


        responseType.setPieChart(pieChartSegmentList);

        if ("T".equals(config.getConfigValue("languageSwitch"))) {
            setMultiLanguage(responseType);
        }

        return responseType;
    }

    private void setMultiLanguage(GetFulfillmentQualityMetricResponseType responseType) {
        List<FulfillmentQualityPieChartSegment> fulfillmentQualityPieChartSegmentList = responseType.getPieChart();
        if (CollectionUtils.isNotEmpty(fulfillmentQualityPieChartSegmentList)) {
            for (FulfillmentQualityPieChartSegment fulfillmentQualityPieChartSegment : fulfillmentQualityPieChartSegmentList) {
                if (StringUtils.isNotBlank(fulfillmentQualityPieChartSegment.getName())) {
                    fulfillmentQualityPieChartSegment.setDisplayName(ScenicLanguageHelper.getMultiLanguage(fulfillmentQualityPieChartSegment.getName(), UserUtil.getVbkLocaleForScenic()));
                }else {
                    fulfillmentQualityPieChartSegment.setName("");
                    fulfillmentQualityPieChartSegment.setDisplayName("");
                }
            }

        }

    }

    @Override
    public GetFulfillmentQualityTableResponseType getFulfillmentQualityTable(GetFulfillmentQualityTableRequestType requestType) {

        if("其它".equals(requestType.getDefectName())){  //NOSONAR
            return getFulfillmentQualityTableSpecial(requestType);
        }

        String queryD = commonService.getQueryD();

        SightArchivesCommonFilter commonFilter = requestType.getCommonFilter();
        List<Map<String, Object>> tableRawResultList = admSevTtdViewspotDefectDetailDfDao.queryProductTable(
                requestType.getDefectName(),
                commonFilter.getSightId(),
                commonFilter.getStartDate(),
                commonFilter.getEndDate(),
                commonFilter.getVendorIdList(),
                commonFilter.getBusinessType(),
                commonFilter.isNeedSubSight(),
                queryD
        );

        GetFulfillmentQualityTableResponseType responseType = new GetFulfillmentQualityTableResponseType();
        List<DefectiveProduct> defectiveProductList = new ArrayList<>();
        for(Map<String,Object> tableRawResult : tableRawResultList){
            DefectiveProduct defectiveProduct = new DefectiveProduct();
            defectiveProduct.setProductId((Long) tableRawResult.get("prd_id"));
            defectiveProduct.setProductName((String) tableRawResult.get("prd_name"));
            defectiveProduct.setWeightedDefectCount((Double)tableRawResult.get("weighted_defect_cnt"));
            defectiveProduct.setWeightedDefectRate(((Long) tableRawResult.get("pay_odr_cnt") != 0) ? ((Double)tableRawResult.get("weighted_defect_cnt"))/ (Long) tableRawResult.get("pay_odr_cnt") : 0.0);
            defectiveProduct.setVendorList((String) tableRawResult.get("provider"));
            defectiveProductList.add(defectiveProduct);
        }

        //产品名称翻译
        if("T".equals(config.getConfigValue("languageSwitch")) && "en-US".equals(UserUtil.getVbkLocale())){
            Map<Long,String> translateResultMap = ttdProductBasicServiceProxy.getProductInfo(defectiveProductList.stream()
                    .map(DefectiveProduct::getProductId)
                    .distinct()
                    .collect(Collectors.toList()));
            for(DefectiveProduct defectiveProduct : defectiveProductList){
                if (translateResultMap.containsKey(defectiveProduct.getProductId()) && translateResultMap.get(defectiveProduct.getProductId())!=null) {
                    defectiveProduct.setProductName(defectiveProduct.getProductId()+"-"+translateResultMap.get(defectiveProduct.getProductId()));
                }
            }
        }

        responseType.setDefectiveProductList(defectiveProductList);
        responseType.setTotalNum(defectiveProductList.size());

        return responseType;
    }

    private GetFulfillmentQualityTableResponseType getFulfillmentQualityTableSpecial(GetFulfillmentQualityTableRequestType requestType){
        GetFulfillmentQualityMetricResponseType metricResponseType = getFulfillmentQualityMetric(new GetFulfillmentQualityMetricRequestType(requestType.getCommonFilter()));
        List<FulfillmentQualityPieChartSegment> fulfillmentQualityPieChartSegmentList = metricResponseType.getPieChart();

        List<String> defectNameList = fulfillmentQualityPieChartSegmentList.stream().map(FulfillmentQualityPieChartSegment::getName).collect(Collectors.toList());

        String queryD = commonService.getQueryD();

        SightArchivesCommonFilter commonFilter = requestType.getCommonFilter();
        List<Map<String, Object>> tableRawResultList = admSevTtdViewspotDefectDetailDfDao.queryProductTableSpecial(
                defectNameList,
                commonFilter.getSightId(),
                commonFilter.getStartDate(),
                commonFilter.getEndDate(),
                commonFilter.getVendorIdList(),
                commonFilter.getBusinessType(),
                commonFilter.isNeedSubSight(),
                queryD
        );

        GetFulfillmentQualityTableResponseType responseType = new GetFulfillmentQualityTableResponseType();
        List<DefectiveProduct> defectiveProductList = new ArrayList<>();
        for(Map<String,Object> tableRawResult : tableRawResultList){
            DefectiveProduct defectiveProduct = new DefectiveProduct();
            defectiveProduct.setProductName((String) tableRawResult.get("prd_name"));
            defectiveProduct.setWeightedDefectCount(((BigDecimal)tableRawResult.get("weighted_defect_cnt")).doubleValue());
            defectiveProduct.setWeightedDefectRate(((Long) tableRawResult.get("pay_odr_cnt") != 0) ? ((BigDecimal)tableRawResult.get("weighted_defect_cnt")).doubleValue()/ (Long) tableRawResult.get("pay_odr_cnt") : 0.0);
            defectiveProduct.setVendorList((String) tableRawResult.get("provider"));
            defectiveProductList.add(defectiveProduct);
        }

        //产品名称翻译
        if("T".equals(config.getConfigValue("languageSwitch")) && "en-US".equals(UserUtil.getVbkLocale())){
            Map<Long,String> translateResultMap = ttdProductBasicServiceProxy.getProductInfo(defectiveProductList.stream()
                    .map(DefectiveProduct::getProductId)
                    .distinct()
                    .collect(Collectors.toList()));
            for(DefectiveProduct defectiveProduct : defectiveProductList){
                if (translateResultMap.containsKey(defectiveProduct.getProductId()) && translateResultMap.get(defectiveProduct.getProductId())!=null) {
                    defectiveProduct.setProductName(defectiveProduct.getProductId()+"-"+translateResultMap.get(defectiveProduct.getProductId()));
                }
            }
        }

        responseType.setDefectiveProductList(defectiveProductList);
        responseType.setTotalNum(defectiveProductList.size());

        return responseType;

    }

    @Override
    public GetCommentMetricResponseType getCommentMetric(GetCommentMetricRequestType requestType) {

        SightArchivesCommonFilter commonFilter = requestType.getCommonFilter();
        Long sightId = commonFilter.getSightId();
        Boolean needSubSight = commonFilter.isNeedSubSight();
        String startDate = commonFilter.getStartDate();
        String endDate = commonFilter.getEndDate();
        Integer dateType = commonFilter.getDateType();
        Integer businessType = commonFilter.getBusinessType();
        List<Long> vendorIdList = commonFilter.getVendorIdList();

        String queryD = commonService.getQueryD();

        AdmPrdTtdCpdTripDimInfoUnifiedOutput sightInfo = admPrdTtdCpdTripDimInfoUnifiedOutputSRDao.querySightInfo(sightId,queryD);
        if (sightInfo == null) {
            return new GetCommentMetricResponseType();
        }
        //点评指标卡
        GetCommentMetricResponseType responseType = new GetCommentMetricResponseType();
        CurrentCommentScore commentScore = commentsServiceProxy.getPoiCommentInfoWithHotTag(sightInfo.getPoiId());
        responseType.setCurrentCommentScore(commentScore);

        //评论趋势
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 解析日期字符串
        LocalDate Ldate = LocalDate.parse(startDate, formatter);
        LocalDate Ndate = LocalDate.parse(endDate, formatter);
        // 计算上个月的第一天
        LocalDate firstDayOfLastMonth = Ldate.minusMonths(1).withDayOfMonth(1);
        startDate = firstDayOfLastMonth.format(formatter);
        // 计算下个月的最后一天
        LocalDate lastDayOfNextMonth = Ndate.plusMonths(1).withDayOfMonth(Ndate.plusMonths(1).lengthOfMonth());
        String lastDayOfNextMonthStr = lastDayOfNextMonth.format(formatter);
        //将endDate赋值为endDate和lastDayOfNextMonthStr的较小值
        endDate = Ndate.isAfter(lastDayOfNextMonth) ? lastDayOfNextMonthStr : endDate;

        List<Map<String, Object>> commentTrendList = admSevTtdViewspotFileIndexDfDao.queryCommentTrend(queryD,sightId, needSubSight, startDate, endDate, dateType, businessType, vendorIdList);
        List<CommentTrendLineItem> commentTrendLineItemList =new ArrayList<>();
        for(Map<String, Object> map : commentTrendList) {
            CommentTrendLineItem commentTrendLineItem = new CommentTrendLineItem();
            commentTrendLineItem.setMonth((String) map.get("date_time"));
            commentTrendLineItem.setCommentCount((Integer) map.get("comment_cnt"));
            commentTrendLineItem.setCommentScore((Double) map.get("comment_score"));
            commentTrendLineItemList.add(commentTrendLineItem);
        }
        //好评关键词
        List<Map<String, Object>> positiveCommentList = admSevTtdViewspotQuestionRankDfDao.queryPositiveComment(queryD, sightId, needSubSight, startDate, endDate, dateType, businessType, vendorIdList);
        List<CommentKeyword> positiveCommentListItemList = new ArrayList<>();
        for(Map<String, Object> map : positiveCommentList) {
            CommentKeyword commentKeyword = new CommentKeyword();
            commentKeyword.setKeyword((String) map.get("good_keywords"));
            commentKeyword.setCount((Integer) map.get("good_keywords_cnt"));
            positiveCommentListItemList.add(commentKeyword);
        }
        //差评关键词
        List<Map<String, Object>> negativeCommentList = admSevTtdViewspotQuestionRankDfDao.queryNegativeComment(queryD, sightId, needSubSight, startDate, endDate, dateType, businessType, vendorIdList);
        List<CommentKeyword> negativeCommentListItemList = new ArrayList<>();
        for(Map<String, Object> map : negativeCommentList) {
            CommentKeyword commentKeyword = new CommentKeyword();
            commentKeyword.setKeyword((String) map.get("bad_keywords"));
            commentKeyword.setCount((Integer) map.get("bad_keywords_cnt"));
            negativeCommentListItemList.add(commentKeyword);
        }
        responseType.setCommentTrendLine(commentTrendLineItemList);
        responseType.setPositiveCommentList(positiveCommentListItemList);
        responseType.setNegativeCommentList(negativeCommentListItemList);

        //翻译好评关键词&差评关键词
        if("T".equals(config.getConfigValue("languageSwitch")) && "en-US".equals(UserUtil.getVbkLocale()) ) {
            List<String> textList = new ArrayList<>();
            for (CommentKeyword commentKeyword : positiveCommentListItemList) {
                if (StringUtils.isNotBlank(commentKeyword.getKeyword())) {
                    textList.add(commentKeyword.getKeyword());
                }
            }
            for (CommentKeyword commentKeyword : negativeCommentListItemList) {
                if (StringUtils.isNotBlank(commentKeyword.getKeyword())) {
                    textList.add(commentKeyword.getKeyword());
                }
            }

            Map<String, String> translateResultMap = gargleTranslateServiceProxy.googleBatchTranslate(textList);
            for (CommentKeyword commentKeyword : positiveCommentListItemList) {
                String text = commentKeyword.getKeyword();
                if (StringUtils.isNotBlank(text) && translateResultMap.containsKey(text)) {
                    commentKeyword.setKeyword(translateResultMap.get(text));
                }
            }
            for (CommentKeyword commentKeyword : negativeCommentListItemList) {
                String text = commentKeyword.getKeyword();
                if (StringUtils.isNotBlank(text) && translateResultMap.containsKey(text)) {
                    commentKeyword.setKeyword(translateResultMap.get(text));
                }
            }

        }


        return responseType;
    }

    @Override
    public GetServiceMetricResponseType getServiceMetric(GetServiceMetricRequestType requestType) {
        GetServiceMetricResponseType serviceMetricResponseType = new GetServiceMetricResponseType();
        SightArchivesCommonFilter commonFilter = requestType.getCommonFilter();
        Long sightId = commonFilter.getSightId();
        Boolean needSubSight = commonFilter.isNeedSubSight();
        String startDate = commonFilter.getStartDate();
        String endDate = commonFilter.getEndDate();
        Integer dateType = commonFilter.getDateType();
        Integer businessType = commonFilter.getBusinessType();
        List<Long> vendorIdList = commonFilter.getVendorIdList();

        String queryD = commonService.getQueryD();

        //cpo 客服电话
        Map<String,Object> resMap = admSevTtdViewspotFileIndexDfDao.queryCpoAndPhone(queryD, sightId, needSubSight, startDate, endDate, dateType, businessType, vendorIdList);
        //咨询top5
        //投诉指标卡
        List<Map<String, Object>> resList = admSevTtdViewspotQuestionRankDfDao.querySevMetric(queryD, sightId, needSubSight, startDate, endDate, dateType, businessType, vendorIdList);
        List<ConsultQuestion> top5SevQuestions = getTop5SevQuestions(resList);

        //cpo同比
        Map<String, Object> resListYoy = admSevTtdViewspotFileIndexDfDao.queryCpoYoy(queryD, sightId, needSubSight, startDate, endDate, dateType, businessType, vendorIdList);
        //cpo同比
        Double cpoYoy;
        if(resListYoy.get("cpo")==null||(Double) resListYoy.get("cpo")==0.0||resMap.get("cpo")==null) {
             cpoYoy = 0.0;//同比
        } else {
             cpoYoy = ((Double) resMap.get("cpo") - (Double) resListYoy.get("cpo"))/((Double) resListYoy.get("cpo"));//同比
        }
        //cpo环比
        Map<String, Object> resListPop = null;
        try {
            String popEndDate = DateUtil.getDayOfInterval(startDate,-1);
            int interval = DateUtil.getDateGap(startDate, endDate);
            String popStartDate = DateUtil.getDayOfInterval(popEndDate, -1*interval);
            resListPop = admSevTtdViewspotFileIndexDfDao.queryCpoAndPhone(queryD, sightId, needSubSight, popStartDate, popEndDate, dateType, businessType, vendorIdList);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        //cpo环比
        Double cpoPop;
        if(resMap.get("cpo") == null || resListPop.get("cpo")==null||(Double) resListPop.get("cpo")==0.0) {
            cpoPop = 0.0;//环比
        } else {
            cpoPop = ((Double) resMap.get("cpo") - (Double) resListPop.get("cpo"))/((Double) resListPop.get("cpo"));//环比
        }
        serviceMetricResponseType.setConsultQuestionList(top5SevQuestions);
        serviceMetricResponseType.setCpoValue((Double) resMap.get("cpo"));
        serviceMetricResponseType.setTelServiceMinutes((Integer) resMap.get("tel_duration"));
        serviceMetricResponseType.setTotalConsultCount((Integer) resMap.get("im_sev_cnt"));
        serviceMetricResponseType.setPreSaleConsultCount((Integer) resMap.get("im_pre_sev_cnt"));
        serviceMetricResponseType.setPostSaleConsultCount((Integer) resMap.get("im_no_pre_sev_cnt"));
        serviceMetricResponseType.setServiceValueAmount((Integer) resMap.get("sev_value"));
        serviceMetricResponseType.setCpoPop(cpoPop);
        serviceMetricResponseType.setCpoYoy(cpoYoy);

        //翻译咨询问题
        if("T".equals(config.getConfigValue("languageSwitch")) && "en-US".equals(UserUtil.getVbkLocale()) ) {
            List<String> textList = new ArrayList<>();
            for (ConsultQuestion consultQuestion : top5SevQuestions) {
                if (StringUtils.isNotBlank(consultQuestion.getQuestion())) {
                    textList.add(consultQuestion.getQuestion());
                }
            }

            Map<String, String> translateResultMap = gargleTranslateServiceProxy.googleBatchTranslate(textList);
            for (ConsultQuestion consultQuestion : top5SevQuestions) {
                String text = consultQuestion.getQuestion();
                if (StringUtils.isNotBlank(text) && translateResultMap.containsKey(text)) {
                    consultQuestion.setQuestion(translateResultMap.get(text));
                }
            }
        }


        return serviceMetricResponseType;
    }

    @Override
    public GetComplaintMetricResponseType getComplaintMetric(GetComplaintMetricRequestType requestType) {
        GetComplaintMetricResponseType complaintMetricResponseType = new GetComplaintMetricResponseType();
        SightArchivesCommonFilter commonFilter = requestType.getCommonFilter();
        Long sightId = commonFilter.getSightId();
        Boolean needSubSight = commonFilter.isNeedSubSight();
        String startDate = commonFilter.getStartDate();
        String endDate = commonFilter.getEndDate();
        Integer dateType = commonFilter.getDateType();
        Integer businessType = commonFilter.getBusinessType();
        List<Long> vendorIdList = commonFilter.getVendorIdList();
        List<ComplaintQuestion> list = null;

        String queryD = commonService.getQueryD();

        //投诉指标卡
        Map<String,Object> resMap = admSevTtdViewspotFileIndexDfDao.queryComplaintMetric(queryD, sightId, needSubSight, startDate, endDate, dateType, businessType, vendorIdList);
        complaintMetricResponseType.setComplaintCount(resMap.get("complain_cnt") == null ? 0 : (Integer) resMap.get("complain_cnt"));
        complaintMetricResponseType.setComplaintRate(resMap.get("complain_rate") == null ? null : (Double) resMap.get("complain_rate"));


        Map<String,Object> popResMap = null;
        try {
            //月环比
            String popEndDate = DateUtil.getDayOfInterval(startDate,-1);
            int interval = DateUtil.getDateGap(startDate, endDate);
            String popStartDate = DateUtil.getDayOfInterval(popEndDate, -1*interval);
            popResMap = admSevTtdViewspotFileIndexDfDao.queryComplaintMetric(queryD, sightId, needSubSight, popStartDate, popEndDate, dateType, businessType, vendorIdList);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        //环比
        if(resMap.get("complain_rate")==null || popResMap.get("complain_rate")==null || (Double) popResMap.get("complain_rate")==0.0) {
            complaintMetricResponseType.setComplaintRatePop(null);
        } else {
            complaintMetricResponseType.setComplaintRatePop(((Double) resMap.get("complain_rate") - (Double) popResMap.get("complain_rate"))/((Double) popResMap.get("complain_rate")));
        }
//        complaintMetricResponseType.setComplaintRatePop(resMap.get("complain_rate") == null ? null : (Double) resMap.get("complain_rate"));

        Map<String, Object> yoyResMap = admSevTtdViewspotFileIndexDfDao.queryComplaintMetricYoy(queryD, sightId, needSubSight, startDate, endDate, dateType, businessType, vendorIdList);
        //同比
        if(resMap.get("complain_rate")==null || yoyResMap.get("complain_rate")==null || (Double) yoyResMap.get("complain_rate")==0.0) {
            complaintMetricResponseType.setComplaintRateYoy(null);
        } else {
            complaintMetricResponseType.setComplaintRateYoy(((Double) resMap.get("complain_rate") - (Double) yoyResMap.get("complain_rate"))/((Double) yoyResMap.get("complain_rate")));
        }

        //投诉问题
        List<Map<String, Object>> resList = admSevTtdViewspotQuestionRankDfDao.queryComplaintMetric(queryD, sightId, needSubSight, startDate, endDate, dateType, businessType, vendorIdList);
        List<ComplaintQuestion> top5ComplaintQuestions = getTop5ComplaintQuestions(resList,complaintMetricResponseType.getComplaintCount() );
        complaintMetricResponseType.setComplaintQuestionList(top5ComplaintQuestions);

        //翻译投诉问题
        if("T".equals(config.getConfigValue("languageSwitch")) && "en-US".equals(UserUtil.getVbkLocale()) ) {
            List<String> textList = new ArrayList<>();
            for (ComplaintQuestion complaintQuestion : top5ComplaintQuestions) {
                if (StringUtils.isNotBlank(complaintQuestion.getQuestion())) {
                    textList.add(complaintQuestion.getQuestion());
                }
            }

            Map<String, String> translateResultMap = gargleTranslateServiceProxy.googleBatchTranslate(textList);
            for (ComplaintQuestion complaintQuestion : top5ComplaintQuestions) {
                String text = complaintQuestion.getQuestion();
                if (StringUtils.isNotBlank(text) && translateResultMap.containsKey(text)) {
                    complaintQuestion.setQuestion(translateResultMap.get(text));
                }
            }
        }

//        //投诉量
//        int complaintCount = 0;
//        for(Map<String, Object> map : resList) {
//             complaintCount += (int) map.get("complain_question_cnt");
//        }
//        //咨询量
//        int consultCount = 0;
//        for(Map<String, Object> map : resList) {
//            consultCount += (int) map.get("sev_question_cnt");
//        }
//
//        //投诉指标卡同比
//        List<Map<String, Object>> resListYoy = admSevTtdViewspotQuestionRankDfDao.queryComplaintMetricYoy(queryD, sightId, needSubSight, startDate, endDate, dateType, businessType, vendorIdList);
//        //投诉量
//        int complaintCountYoy = 0;
//        for(Map<String, Object> map : resListYoy) {
//            complaintCountYoy += (int) map.get("complain_question_cnt");
//        }
//        //咨询量
//        int consultCountYoy = 0;
//        for(Map<String, Object> map : resListYoy) {
//            consultCountYoy += (int) map.get("sev_question_cnt");
//        }
//        Double complaintRateYoy = null;
//        if(consultCount != 0 && consultCountYoy != 0) {
//            complaintRateYoy = ((complaintCount * 1.0 / consultCount) - (complaintCountYoy * 1.0 / consultCountYoy)) / (complaintCountYoy * 1.0 / consultCountYoy);//同比
//        }
//
//        //投诉指标卡环比
//        List<Map<String, Object>> resListPop = admSevTtdViewspotQuestionRankDfDao.queryComplaintMetricPop(queryD, sightId, needSubSight, startDate, endDate, dateType, businessType, vendorIdList);
//        //投诉量
//        int complaintCountPop = 0;
//        for(Map<String, Object> map : resListPop) {
//            complaintCountPop += (int) map.get("complain_question_cnt");
//        }
//        //咨询量
//        int consultCountPop = 0;
//        for(Map<String, Object> map : resListPop) {
//            consultCountPop += (int) map.get("sev_question_cnt");
//        }
//        Double complaintRatePop = null;
//        if(consultCount != 0 && consultCountPop != 0) {
//            complaintRatePop = ((complaintCount * 1.0 / consultCount) - (complaintCountPop * 1.0 / consultCountPop)) / (complaintCountPop * 1.0 / consultCountPop);//环比
//        }




//        complaintMetricResponseType.setComplaintQuestionList(top5ComplaintQuestions);
//        complaintMetricResponseType.setComplaintCount(complaintCount);
//        complaintMetricResponseType.setComplaintRate(consultCount>0 ? complaintCount * 1.0/consultCount : null);
//        complaintMetricResponseType.setComplaintRateYoy(complaintRateYoy);//同比
//        complaintMetricResponseType.setComplaintRatePop(complaintRatePop);//环比
        return complaintMetricResponseType;
    }

    @Override
    public GetVendorQualityTableResponseType getVendorQualityTable(GetVendorQualityTableRequestType requestType) {
        GetVendorQualityTableResponseType vendorQualityTableResponseType = new GetVendorQualityTableResponseType();
        SightArchivesCommonFilter commonFilter = requestType.getCommonFilter();
        Long sightId = commonFilter.getSightId();
        Boolean needSubSight = commonFilter.isNeedSubSight();
        String startDate = commonFilter.getStartDate();
        String endDate = commonFilter.getEndDate();
        Integer dateType = commonFilter.getDateType();
        Integer businessType = commonFilter.getBusinessType();
        List<Long> vendorIdList = commonFilter.getVendorIdList();
        String queryD = commonService.getQueryD();
        String rankColumn = requestType.getRankColumn();
        String rankType = requestType.getRankType();
        if(StringUtils.isBlank(rankColumn) || StringUtils.isBlank(rankType)) {
            rankColumn = "serviceValueAmount";
            rankType = "desc";
        }


        List<VendorQuality> vendorQualityTableList  = admSevTtdViewspotFileIndexDfDao.queryVendorQualityTable(queryD, sightId, needSubSight, startDate, endDate, dateType, businessType, vendorIdList,rankColumn,rankType);

        vendorQualityTableResponseType.setVendorList(vendorQualityTableList);

        return vendorQualityTableResponseType;
    }

    public List<ComplaintQuestion> getTop5ComplaintQuestions(List<Map<String, Object>> queryResult, Integer totalComplaintCount) {
//        // 计算总投诉次数
//        int totalComplaintCount = queryResult.stream()
//                .mapToInt(map -> (int) map.get("complain_question_cnt"))
//                .sum();

        queryResult.removeIf(map ->
                StringUtils.isBlank((String) map.get("complain_question_type")) || "unkwn".equals(map.get("complain_question_type"))
        );

        // 按投诉次数排序并取前五名
        List<ComplaintQuestion> top5Complaints = queryResult.stream()
                .sorted((map1, map2) -> Integer.compare(
                        (int) map2.get("complain_question_cnt"),
                        (int) map1.get("complain_question_cnt")))
                .limit(5)
                .map(map -> {
                    String questionType = (String) map.get("complain_question_type");
                    int complaintCount = (int) map.get("complain_question_cnt");
                    Double complaintCountPercentage = null;
                    if(totalComplaintCount !=null && totalComplaintCount != 0) {
                        complaintCountPercentage = (double) complaintCount / totalComplaintCount ;
                    }
                    return new ComplaintQuestion(questionType, complaintCount, complaintCountPercentage);
                })
                .collect(Collectors.toList());

        return top5Complaints;
    }

    public List<ConsultQuestion> getTop5SevQuestions(List<Map<String, Object>> queryResult) {
        // 计算总投诉次数
        int totalSevCount = queryResult.stream()
                .mapToInt(map -> (int) map.get("sev_question_cnt"))
                .sum();

        queryResult.removeIf(map ->
                StringUtils.isBlank((String) map.get("sev_question_type")) || "unkwn".equals(map.get("sev_question_type"))
        );


        // 按投诉次数排序并取前五名
        List<ConsultQuestion> top5Sevs = queryResult.stream()
                .sorted((map1, map2) -> Integer.compare(
                        (int) map2.get("sev_question_cnt"),
                        (int) map1.get("sev_question_cnt")))
                .limit(5)
                .map(map -> {
                    String questionType = (String) map.get("sev_question_type");
                    int SevCount = (int) map.get("sev_question_cnt");
                    double SevCountPercentage = (double) SevCount / totalSevCount ;
                    return new ConsultQuestion(questionType, SevCount, SevCountPercentage);
                })
                .collect(Collectors.toList());

        return top5Sevs;
    }
}
