package com.ctrip.tour.business.dashboard.tktBusiness.dao;

import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.base.DalTableOperations;
import com.ctrip.platform.dal.dao.base.SQLResult;
import com.ctrip.platform.dal.dao.base.SQLResultSpec;
import com.ctrip.platform.dal.dao.client.DalOperationsFactory;
import com.ctrip.platform.dal.dao.sqlbuilder.*;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.sql.Types;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


import com.ctrip.platform.dal.dao.helper.DalDefaultJpaParser;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardExamineeConfigV2;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import qunar.tc.qclient.redis.command.ResultType;

/**
 * <AUTHOR>
 * @date 2023-02-07
 */
@Repository
public class BusinessDashboardExamineeConfigV2Dao {
	private static final boolean ASC = true;
	private DalTableDao<BusinessDashboardExamineeConfigV2> client;
	DalTableOperations<BusinessDashboardExamineeConfigV2> dalTableOperations = DalOperationsFactory.getDalTableOperations(BusinessDashboardExamineeConfigV2.class);
	
	public BusinessDashboardExamineeConfigV2Dao() throws SQLException {
		this.client = new DalTableDao<>(new DalDefaultJpaParser<>(BusinessDashboardExamineeConfigV2.class));
	}

	/**
	 * Query BusinessDashboardExamineeConfigV2 by complex primary key
	 */
	public BusinessDashboardExamineeConfigV2 queryByPk(Long id, Timestamp datachangeLasttime)
			throws SQLException {
		return queryByPk(id, datachangeLasttime, null);
	}

	/**
	 * Query BusinessDashboardExamineeConfigV2 by complex primary key
	 */
	public BusinessDashboardExamineeConfigV2 queryByPk(Long id, Timestamp datachangeLasttime, DalHints hints)
			throws SQLException {
		hints = DalHints.createIfAbsent(hints);
		BusinessDashboardExamineeConfigV2 pk = new BusinessDashboardExamineeConfigV2();		
		pk.setId(id);
		pk.setDatachangeLasttime(datachangeLasttime);
		return client.queryByPk(pk, hints);
	}

	/**
	 * Query BusinessDashboardExamineeConfigV2 by BusinessDashboardExamineeConfigV2 instance which the primary key is set
	 */
	public BusinessDashboardExamineeConfigV2 queryByPk(BusinessDashboardExamineeConfigV2 pk)
			throws SQLException {
		return queryByPk(pk, null);
	}

	/**
	 * Query BusinessDashboardExamineeConfigV2 by BusinessDashboardExamineeConfigV2 instance which the primary key is set
	 */
	public BusinessDashboardExamineeConfigV2 queryByPk(BusinessDashboardExamineeConfigV2 pk, DalHints hints)
			throws SQLException {
		hints = DalHints.createIfAbsent(hints);
		return client.queryByPk(pk, hints);
	}

	/**
	 * Query against sample pojo. All not null attributes of the passed in pojo
	 * will be used as search criteria.
	 */
	public List<BusinessDashboardExamineeConfigV2> queryBy(BusinessDashboardExamineeConfigV2 sample)
			throws SQLException {
		return queryBy(sample, null);
	}

	/**
	 * Query against sample pojo. All not null attributes of the passed in pojo
	 * will be used as search criteria.
	 */
	public List<BusinessDashboardExamineeConfigV2> queryBy(BusinessDashboardExamineeConfigV2 sample, DalHints hints)
			throws SQLException {
		hints = DalHints.createIfAbsent(hints);
		return client.queryBy(sample, hints);
	}

	/**
	 * Get the all records count
	 */
	public int count() throws SQLException {
		return count(null);
	}

	/**
	 * Get the all records count
	 */
	public int count(DalHints hints) throws SQLException {
		hints = DalHints.createIfAbsent(hints);
		SelectSqlBuilder builder = new SelectSqlBuilder().selectCount();
		return client.count(builder, hints).intValue();
	}

	/**
	 * Query BusinessDashboardExamineeConfigV2 with paging function
	 * The pageSize and pageNo must be greater than zero.
	 */
	public List<BusinessDashboardExamineeConfigV2> queryAllByPage(int pageNo, int pageSize)  throws SQLException {
		return queryAllByPage(pageNo, pageSize, null);
	}

	/**
	 * Query BusinessDashboardExamineeConfigV2 with paging function
	 * The pageSize and pageNo must be greater than zero.
	 */
	public List<BusinessDashboardExamineeConfigV2> queryAllByPage(int pageNo, int pageSize, DalHints hints)  throws SQLException {
		hints = DalHints.createIfAbsent(hints);

		SelectSqlBuilder builder = new SelectSqlBuilder();
		builder.selectAll().atPage(pageNo, pageSize).orderBy("id,DataChange_LastTime", ASC);

		return client.query(builder, hints);
	}

	/**
	 * Get all records from table
	 */
	public List<BusinessDashboardExamineeConfigV2> queryAll() throws SQLException {
		return queryAll(null);
	}

	/**
	 * Get all records from table
	 */
	public List<BusinessDashboardExamineeConfigV2> queryAll(DalHints hints) throws SQLException {
		hints = DalHints.createIfAbsent(hints);
		
		SelectSqlBuilder builder = new SelectSqlBuilder().selectAll().orderBy("id,DataChange_LastTime", ASC);
		
		return client.query(builder, hints);
	}

	/**
	 * Insert single pojo
	 *
	 * @param daoPojo
	 *            pojo to be inserted
	 * @return how many rows been affected
	 * @throws SQLException
	 */
	public int insert(BusinessDashboardExamineeConfigV2 daoPojo) throws SQLException {
		return insert(null, daoPojo);
	}

	/**
	 * Insert single pojo
	 * 
	 * @param hints
	 *            Additional parameters that instruct how DAL Client perform database operation.
	 * @param daoPojo
	 *            pojo to be inserted
	 * @return how many rows been affected
	 * @throws SQLException
	 */
	public int insert(DalHints hints, BusinessDashboardExamineeConfigV2 daoPojo) throws SQLException {
		if (null == daoPojo) {
			return 0;
		}
		hints = DalHints.createIfAbsent(hints);
		return client.insert(hints, daoPojo);
	}

	/**
	 * Insert pojos one by one. If you want to inert them in the batch mode,
	 * user batchInsert instead. You can also use the combinedInsert.
	 *
	 * @param daoPojos
	 *            list of pojos to be inserted
	 * @return how many rows been affected
	 */
	public int[] insert(List<BusinessDashboardExamineeConfigV2> daoPojos) throws SQLException {
		return insert(null, daoPojos);
	}

	/**
	 * Insert pojos one by one. If you want to inert them in the batch mode,
	 * user batchInsert instead. You can also use the combinedInsert.
	 * 
	 * @param hints 
	 *            Additional parameters that instruct how DAL Client perform database operation.
	 *            DalHintEnum.continueOnError can be used
	 *            to indicate that the inserting can be go on if there is any
	 *            failure.
	 * @param daoPojos
	 *            list of pojos to be inserted
	 * @return how many rows been affected
	 */
	public int[] insert(DalHints hints, List<BusinessDashboardExamineeConfigV2> daoPojos) throws SQLException {
		if (null == daoPojos || daoPojos.size() <= 0) {
			return new int[0];
		}
		hints = DalHints.createIfAbsent(hints);
		return client.insert(hints, daoPojos);
	}

	/**
	 * Insert pojo and get the generated PK back in keyHolder.
	 * If the "set no count on" for MS SqlServer is set, the operation may fail.
	 * Please don't pass keyholder for MS SqlServer to avoid the failure in such case.
	 *
	 * @param keyHolder
	 *            holder for generated primary keys
	 * @param daoPojo
	 *            pojo to be inserted
	 * @return how many rows been affected
	 * @throws SQLException
	 */
	public int insertWithKeyHolder(KeyHolder keyHolder, BusinessDashboardExamineeConfigV2 daoPojo) throws SQLException {
		return insert(null, keyHolder, daoPojo);
	}

	/**
	 * Insert pojo and get the generated PK back in keyHolder. 
	 * If the "set no count on" for MS SqlServer is set, the operation may fail.
	 * Please don't pass keyholder for MS SqlServer to avoid the failure in such case.
	 * 
	 * @param hints
	 *            Additional parameters that instruct how DAL Client perform database operation.
	 * @param keyHolder
	 *            holder for generated primary keys
	 * @param daoPojo
	 *            pojo to be inserted
	 * @return how many rows been affected
	 * @throws SQLException
	 */
	public int insert(DalHints hints, KeyHolder keyHolder, BusinessDashboardExamineeConfigV2 daoPojo) throws SQLException {
		if (null == daoPojo) {
			return 0;
		}
		hints = DalHints.createIfAbsent(hints);
		return client.insert(hints, keyHolder, daoPojo);
	}

	/**
	 * Insert pojos and get the generated PK back in keyHolder.
	 * If the "set no count on" for MS SqlServer is set, the operation may fail.
	 * Please don't pass keyholder for MS SqlServer to avoid the failure in such case.
	 *
	 * @param keyHolder
	 *            holder for generated primary keys
	 * @param daoPojos
	 *            list of pojos to be inserted
	 * @return how many rows been affected
	 * @throws SQLException
	 */
	public int[] insertWithKeyHolder(KeyHolder keyHolder, List<BusinessDashboardExamineeConfigV2> daoPojos) throws SQLException {
		return insert(null, keyHolder, daoPojos);
	}

	/**
	 * Insert pojos and get the generated PK back in keyHolder. 
	 * If the "set no count on" for MS SqlServer is set, the operation may fail.
	 * Please don't pass keyholder for MS SqlServer to avoid the failure in such case.
	 * 
	 * @param hints
	 *            Additional parameters that instruct how DAL Client perform database operation.
	 *            DalHintEnum.continueOnError can be used
	 *            to indicate that the inserting can be go on if there is any
	 *            failure.
	 * @param keyHolder
	 *            holder for generated primary keys
	 * @param daoPojos
	 *            list of pojos to be inserted
	 * @return how many rows been affected
	 * @throws SQLException
	 */
	public int[] insert(DalHints hints, KeyHolder keyHolder, List<BusinessDashboardExamineeConfigV2> daoPojos) throws SQLException {
		if (null == daoPojos || daoPojos.size() <= 0) {
			return new int[0];
		}
		hints = DalHints.createIfAbsent(hints);
		return client.insert(hints, keyHolder, daoPojos);
	}

	/**
	 * Insert pojos in batch mode.
	 * The DalDetailResults will be set in hints to allow client know how the operation performed in each of the shard.
	 *
	 * @param daoPojos list of pojos to be inserted
	 * @return how many rows been affected for inserting each of the pojo
	 * @throws SQLException
	 */
	public int[] batchInsert(List<BusinessDashboardExamineeConfigV2> daoPojos) throws SQLException {
		return batchInsert(null, daoPojos);
	}

	/**
	 * Insert pojos in batch mode. 
	 * The DalDetailResults will be set in hints to allow client know how the operation performed in each of the shard.
	 * 
	 * @param hints Additional parameters that instruct how DAL Client perform database operation.
	 * @param daoPojos list of pojos to be inserted
	 * @return how many rows been affected for inserting each of the pojo
	 * @throws SQLException
	 */
	public int[] batchInsert(DalHints hints, List<BusinessDashboardExamineeConfigV2> daoPojos) throws SQLException {
		if (null == daoPojos || daoPojos.size() <= 0) {
			return new int[0];
		}
		hints = DalHints.createIfAbsent(hints);
		return client.batchInsert(hints, daoPojos);
	}

	/**
	 * Insert multiple pojos in one INSERT SQL
	 * The DalDetailResults will be set in hints to allow client know how the operation performed in each of the shard.
	 *
	 * @param daoPojos list of pojos to be inserted
	 * @return how many rows been affected
	 * @throws SQLException
	 */
	public int combinedInsert(List<BusinessDashboardExamineeConfigV2> daoPojos) throws SQLException {
		return combinedInsert(null, daoPojos);
	}

	/**
	 * Insert multiple pojos in one INSERT SQL
	 * The DalDetailResults will be set in hints to allow client know how the operation performed in each of the shard.
	 * 
	 * @param hints Additional parameters that instruct how DAL Client perform database operation.
	 * @param daoPojos list of pojos to be inserted
	 * @return how many rows been affected
	 * @throws SQLException
	 */
	public int combinedInsert(DalHints hints, List<BusinessDashboardExamineeConfigV2> daoPojos) throws SQLException {
		if (null == daoPojos || daoPojos.size() <= 0) {
			return 0;
		}
		hints = DalHints.createIfAbsent(hints);
		return client.combinedInsert(hints, daoPojos);
	}

	/**
	 * Insert multiple pojos in one INSERT SQL and get the generated PK back in keyHolder.
	 * If the "set no count on" for MS SqlServer is set, the operation may fail.
	 * Please don't pass keyholder for MS SqlServer to avoid the failure in such case.
	 * The DalDetailResults will be set in hints to allow client know how the operation performed in each of the shard.
	 *
	 * @param keyHolder holder for generated primary keys
	 * @param daoPojos list of pojos to be inserted
	 * @return how many rows been affected
	 * @throws SQLException
	 */
	public int combinedInsertWithKeyHolder(KeyHolder keyHolder, List<BusinessDashboardExamineeConfigV2> daoPojos) throws SQLException {
		return combinedInsert(null, keyHolder, daoPojos);
	}

	/**
	 * Insert multiple pojos in one INSERT SQL and get the generated PK back in keyHolder.
	 * If the "set no count on" for MS SqlServer is set, the operation may fail.
	 * Please don't pass keyholder for MS SqlServer to avoid the failure in such case.
	 * The DalDetailResults will be set in hints to allow client know how the operation performed in each of the shard.
	 * 
	 * @param hints Additional parameters that instruct how DAL Client perform database operation.
	 * @param keyHolder holder for generated primary keys
	 * @param daoPojos list of pojos to be inserted
	 * @return how many rows been affected
	 * @throws SQLException
	 */
	public int combinedInsert(DalHints hints, KeyHolder keyHolder, List<BusinessDashboardExamineeConfigV2> daoPojos) throws SQLException {
		if (null == daoPojos || daoPojos.size() <= 0) {
			return 0;
		}
		hints = DalHints.createIfAbsent(hints);
		return client.combinedInsert(hints, keyHolder, daoPojos);
	}

	/**
	 * Delete the given pojo.
	 *
	 * @param daoPojo pojo to be deleted
	 * @return how many rows been affected
	 * @throws SQLException
	 */
	public int delete(BusinessDashboardExamineeConfigV2 daoPojo) throws SQLException {
		return delete(null, daoPojo);
	}

	/**
	 * Delete the given pojo.
	 * 
	 * @param hints Additional parameters that instruct how DAL Client perform database operation.
	 * @param daoPojo pojo to be deleted
	 * @return how many rows been affected
	 * @throws SQLException
	 */
	public int delete(DalHints hints, BusinessDashboardExamineeConfigV2 daoPojo) throws SQLException {
		if (null == daoPojo) {
			return 0;
		}
		hints = DalHints.createIfAbsent(hints);
		return client.delete(hints, daoPojo);
	}

	/**
	 * Delete the given pojos list one by one.
	 *
	 * @param daoPojos list of pojos to be deleted
	 * @return how many rows been affected
	 * @throws SQLException
	 */
	public int[] delete(List<BusinessDashboardExamineeConfigV2> daoPojos) throws SQLException {
		return delete(null, daoPojos);
	}

	/**
	 * Delete the given pojos list one by one.
	 * 
	 * @param hints Additional parameters that instruct how DAL Client perform database operation.
	 * @param daoPojos list of pojos to be deleted
	 * @return how many rows been affected
	 * @throws SQLException
	 */
	public int[] delete(DalHints hints, List<BusinessDashboardExamineeConfigV2> daoPojos) throws SQLException {
		if (null == daoPojos || daoPojos.size() <= 0) {
			return new int[0];
		}
		hints = DalHints.createIfAbsent(hints);
		return client.delete(hints, daoPojos);
	}

	/**
	 * Delete the given pojo list in batch.
	 * The DalDetailResults will be set in hints to allow client know how the operation performed in each of the shard.
	 *
	 * @param daoPojos list of pojos to be deleted
	 * @return how many rows been affected for deleting each of the pojo
	 * @throws SQLException
	 */
	public int[] batchDelete(List<BusinessDashboardExamineeConfigV2> daoPojos) throws SQLException {
		return batchDelete(null, daoPojos);
	}

	/**
	 * Delete the given pojo list in batch. 
	 * The DalDetailResults will be set in hints to allow client know how the operation performed in each of the shard.
	 * 
	 * @param hints Additional parameters that instruct how DAL Client perform database operation.
	 * @param daoPojos list of pojos to be deleted
	 * @return how many rows been affected for deleting each of the pojo
	 * @throws SQLException
	 */
	public int[] batchDelete(DalHints hints, List<BusinessDashboardExamineeConfigV2> daoPojos) throws SQLException {
		if (null == daoPojos || daoPojos.size() <= 0) {
			return new int[0];
		}
		hints = DalHints.createIfAbsent(hints);
		return client.batchDelete(hints, daoPojos);
	}
    /**
         * Delete multiple pojos in one DELETE SQL.
         *
         * @param daoPojos list of pojos to be deleted
         * @return how many rows been affected
         * @throws SQLException
         */
    public int combinedDelete(List<BusinessDashboardExamineeConfigV2> daoPojos) throws SQLException {
        return combinedDelete(null, daoPojos);
    }

    /**
     * Delete multiple pojos in one DELETE SQL.
     *
     * @param hints Additional parameters that instruct how DAL Client perform database operation.
     * @param daoPojos list of pojos to be deleted
     * @return how many rows been affected
     * @throws SQLException
     */
    public int combinedDelete(DalHints hints, List<BusinessDashboardExamineeConfigV2> daoPojos) throws SQLException {
        if (null == daoPojos || daoPojos.size() <= 0) {
            return 0;
        }
        hints = DalHints.createIfAbsent(hints);
        return client.combinedDelete(hints, daoPojos);
    }

	/**
	 * Update the given pojo . By default, if a field of pojo is null value,
	 * that field will be ignored, so that it will not be updated. You can
	 * overwrite this by set updateNullField in hints.
	 *
	 * @param daoPojo pojo to be updated
	 * @return how many rows been affected
	 * @throws SQLException
	 */
	public int update(BusinessDashboardExamineeConfigV2 daoPojo) throws SQLException {
		return update(null, daoPojo);
	}

	/**
	 * Update the given pojo . By default, if a field of pojo is null value,
	 * that field will be ignored, so that it will not be updated. You can
	 * overwrite this by set updateNullField in hints.
	 * 
	 * @param hints
	 * 			Additional parameters that instruct how DAL Client perform database operation.
	 *          DalHintEnum.updateNullField can be used
	 *          to indicate that the field of pojo is null value will be update.
	 * @param daoPojo pojo to be updated
	 * @return how many rows been affected
	 * @throws SQLException
	 */
	public int update(DalHints hints, BusinessDashboardExamineeConfigV2 daoPojo) throws SQLException {
		if (null == daoPojo) {
			return 0;
		}
		hints = DalHints.createIfAbsent(hints);
		return client.update(hints, daoPojo);
	}

	/**
	 * Update the given pojo list one by one. By default, if a field of pojo is null value,
	 * that field will be ignored, so that it will not be updated. You can
	 * overwrite this by set updateNullField in hints.
	 *
	 * @param daoPojos list of pojos to be updated
	 * @return how many rows been affected
	 * @throws SQLException
	 */
	public int[] update(List<BusinessDashboardExamineeConfigV2> daoPojos) throws SQLException {
		return update(null, daoPojos);
	}

	/**
	 * Update the given pojo list one by one. By default, if a field of pojo is null value,
	 * that field will be ignored, so that it will not be updated. You can
	 * overwrite this by set updateNullField in hints.
	 * 
	 * @param hints
	 * 			Additional parameters that instruct how DAL Client perform database operation.
	 *          DalHintEnum.updateNullField can be used
	 *          to indicate that the field of pojo is null value will be update.
	 * @param daoPojos list of pojos to be updated
	 * @return how many rows been affected
	 * @throws SQLException
	 */
	public int[] update(DalHints hints, List<BusinessDashboardExamineeConfigV2> daoPojos) throws SQLException {
		if (null == daoPojos || daoPojos.size() <= 0) {
			return new int[0];
		}
		hints = DalHints.createIfAbsent(hints);
		return client.update(hints, daoPojos);
	}

	/**
	 * Update the given pojo list in batch.
	 *
	 * @return how many rows been affected
	 * @throws SQLException
	 */
	public int[] batchUpdate(List<BusinessDashboardExamineeConfigV2> daoPojos) throws SQLException {
		return batchUpdate(null, daoPojos);
	}

	/**
	 * Update the given pojo list in batch. 
	 * 
	 * @return how many rows been affected
	 * @throws SQLException
	 */
	public int[] batchUpdate(DalHints hints, List<BusinessDashboardExamineeConfigV2> daoPojos) throws SQLException {
		if (null == daoPojos || daoPojos.size() <= 0) {
			return new int[0];
		}
		hints = DalHints.createIfAbsent(hints);
		return client.batchUpdate(hints, daoPojos);
	}

	/**
	 * 获取某个考核周期下用户配置的所有指标
	 * 如果是传入角色 则返回该角色下用户是否有配置
	 * @param domainName
	 * @param queryD
	 * @param year
	 * @param quarter
	 * @return
	 * @throws SQLException
	 */
	public List<BusinessDashboardExamineeConfigV2> querySpecificPeriodAllMetricConfig(String domainName,
																					  String queryD,
																					  String year,
																					  String quarter,
																					  Integer role) throws SQLException {
		SelectSqlBuilder builder = new SelectSqlBuilder();
		builder.equal("domain_name", domainName, Types.VARCHAR);
		builder.and().equal("query_d", queryD, Types.VARCHAR);
		builder.and().equal("year", year, Types.VARCHAR);

		if(StringUtils.isNotEmpty(quarter)){
			builder.and().equal("quarter", quarter, Types.VARCHAR);
		}
		if (!GeneralUtil.isEmpty(role)) {
			builder.and().equal("role", role, Types.SMALLINT);
		}
		return client.query(builder, new DalHints());
//		List<BusinessDashboardExamineeConfigV2> originList = client.query(builder, new DalHints());
//		List<BusinessDashboardExamineeConfigV2> newList = originList.stream()
//				.filter(i -> !"12".equals(i.getExamineMetric()))
//				.filter(i -> !("3".equals(i.getExamineMetric()) && i.getExamineType() == 2))
//				.filter(i -> !("11".equals(i.getExamineMetric()) && i.getExamineType() == 2))
//				.collect(Collectors.toList());
//		return newList;
	}
	public List<BusinessDashboardExamineeConfigV2> querySpecificPeriodAllMetricConfig(String domainName,
																					  String queryD,
																					  String year,
																					  List<String> quarter,
																					  Integer role) throws SQLException {
		SelectSqlBuilder builder = new SelectSqlBuilder();
		builder.equal("domain_name", domainName, Types.VARCHAR);
		builder.and().equal("query_d", queryD, Types.VARCHAR);
		builder.and().equal("year", year, Types.VARCHAR);

		if(CollectionUtils.isNotEmpty(quarter)){
			builder.and().in("quarter", quarter, Types.VARCHAR);
		}
		if (!GeneralUtil.isEmpty(role)) {
			builder.and().equal("role", role, Types.SMALLINT);
		}
		return client.query(builder, new DalHints());
//		List<BusinessDashboardExamineeConfigV2> originList = client.query(builder, new DalHints());
//		List<BusinessDashboardExamineeConfigV2> newList = originList.stream()
//				.filter(i -> !"12".equals(i.getExamineMetric()))
//				.filter(i -> !("3".equals(i.getExamineMetric()) && i.getExamineType() == 2))
//				.filter(i -> !("11".equals(i.getExamineMetric()) && i.getExamineType() == 2))
//				.collect(Collectors.toList());
//		return newList;
	}
	/**
	 * 十四期考核配置表信息查询
	 */
	public List<BusinessDashboardExamineeConfigV2> queryExamineConfig(String domainName,
																	  String queryD,
																	  String year,
																	  Map<String, List<String>> inMap,
																	  Integer role) throws SQLException {
		SelectSqlBuilder builder = new SelectSqlBuilder();
		builder.equal("domain_name", domainName, Types.VARCHAR);
		builder.and().equal("query_d", queryD, Types.VARCHAR);
		builder.and().equal("year", year, Types.VARCHAR);
		builder.and().in("quarter", inMap.get("quarter"), Types.VARCHAR);
		if (!GeneralUtil.isEmpty(role)) {
			builder.and().equal("role", role, Types.SMALLINT);
		}
		return client.query(builder, new DalHints());
	}

	/**
	 * 获取所有考核周期下用户某个指标的所有配置
	 * @param domainName
	 * @param queryD
	 * @param metric
	 * @return
	 * @throws SQLException
	 */
	public List<BusinessDashboardExamineeConfigV2> queryMetricAllConfig(String domainName,
																		String queryD,
																		String metric) throws SQLException {
		SelectSqlBuilder builder = new SelectSqlBuilder();
		builder.equal("domain_name", domainName, Types.VARCHAR);
		builder.and().equal("query_d", queryD, Types.VARCHAR);
		builder.and().equal("examine_metric", convertMetric(metric), Types.VARCHAR);
		builder.or().equal("domain_name", domainName, Types.VARCHAR);
		builder.and().equal("query_d", queryD, Types.VARCHAR);
		builder.and().equal("examine_metric", metric, Types.VARCHAR);
		return client.query(builder, new DalHints());
//		List<BusinessDashboardExamineeConfigV2> originList = client.query(builder, new DalHints());
//		List<BusinessDashboardExamineeConfigV2> newList = originList.stream()
//				.filter(i -> !"12".equals(i.getExamineMetric()))
//				.filter(i -> !("3".equals(i.getExamineMetric()) && i.getExamineType() == 2))
//				.filter(i -> !("11".equals(i.getExamineMetric()) && i.getExamineType() == 2))
//				.collect(Collectors.toList());
//		return newList;
	}


	private String convertMetric(String originMetric) {
		switch (originMetric) {
			case "1":
			case "2":
				return "1;2";
			case "5":
			case "6":
			case "7":
				return "5;6;7";
			case "11":
			case "12":
				return "11;12";
			default:
				return originMetric;
		}
	}


	public List<BusinessDashboardExamineeConfigV2> queryMetricConfig(String domainName,
																	 String queryD,
																	 String year,
																	 String quarter,
																	 String metric) throws SQLException {
		SelectSqlBuilder builder = new SelectSqlBuilder();
		builder.equal("domain_name", domainName, Types.VARCHAR);
		builder.and().equal("query_d", queryD, Types.VARCHAR);
		builder.and().equal("year", year, Types.VARCHAR);
		builder.and().equal("quarter", quarter, Types.VARCHAR);
		builder.and().equal("examine_metric",metric,Types.VARCHAR);

		return client.query(builder, new DalHints());
	}

	public boolean isDomesticExamineLevel(String domainName,
										  String queryD,
										  String year,
										  String quarter) throws SQLException {
		String sql = "(examine_level='国内' or act_examine_level='国内' or odt_examine_level='国内') and domain_name=? and year=? and quarter=? and query_d=?"; //NOSONAR
		Number countNumber = dalTableOperations.count(sql, new DalHints(), domainName,year,quarter,queryD);
		return countNumber.longValue()>0;
	}

	public List<String> getTktRegionNames(String domainName,
													String queryD,
													String year,
													String quarter) throws SQLException {

		String sql = "select distinct examine_range from business_dashboard_examinee_config_v2 where examine_level='大区' and domain_name=? and year=? and quarter=? and query_d=?"; //NOSONAR
		List<String> regionNameList =
				dalTableOperations.query(sql, new DalHints(), SQLResult.type(String.class), domainName,year,quarter,queryD);

		return regionNameList;

	}


	//品类活动
	public List<String> getActRegionNames(String domainName,
												   String queryD,
												   String year,
												   String quarter) throws SQLException {

		String sql = "select distinct act_examine_range from business_dashboard_examinee_config_v2 where act_examine_level='大区' and domain_name=? and year=? and quarter=? and query_d=?";   //NOSONAR
		List<String> regionNameList =
				dalTableOperations.query(sql, new DalHints(), SQLResult.type(String.class), domainName,year,quarter,queryD);

		return regionNameList;

	}

	//日游
	public List<String> getOdtRegionNames(String domainName,
										  String queryD,
										  String year,
										  String quarter) throws SQLException {

		String sql = "select distinct odt_examine_range from business_dashboard_examinee_config_v2 where odt_examine_level='大区' and domain_name=? and year=? and quarter=? and query_d=?";  //NOSONAR
		List<String> regionNameList =
				dalTableOperations.query(sql, new DalHints(), SQLResult.type(String.class), domainName,year,quarter,queryD);

		return regionNameList;

	}



	public List<Integer> getRolesByDomainName(String domainName,
											  String queryD,
											  String year,
											  String quarter) throws SQLException {

		String sql = "select distinct role from business_dashboard_examinee_config_v2 where domain_name=? and year=? and quarter=? and query_d=?";

		List<Integer> roleIdList =
				dalTableOperations.query(sql, new DalHints(), SQLResult.type(Integer.class), domainName,year,quarter,queryD);

		return roleIdList;
	}
}
