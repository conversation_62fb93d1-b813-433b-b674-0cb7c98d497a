package com.ctrip.tour.business.dashboard.tktBusiness.helper;

import com.ctrip.soa._24922.TableDataItem;
import com.ctrip.soa._24922.TimeFilter;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;

public class JumpHelper {


    public static void makeUpTargetManageUrl(RemoteConfig remoteConfig,
                                             TimeFilter timeFilter,
                                             MetricInfoBean metricInfoBean,
                                             List<TableDataItem> tableDataItemList) {
        for (TableDataItem item : tableDataItemList) {
            Map<String, String> fieldMap = item.getFieldMap();
            String url = generateTargetManageUrl(remoteConfig, timeFilter, metricInfoBean,
                    fieldMap.get("viewspot_type_id"), fieldMap.get("scenic_class_code"));
            fieldMap.put("url", url);
        }
    }


    private static String generateTargetManageUrl(RemoteConfig remoteConfig,
                                                  TimeFilter timeFilter,
                                                  MetricInfoBean metricInfoBean,
                                                  String viewspotTypeId,
                                                  String scenicClassCode) {
        String urlSuffix = remoteConfig.getConfigValue("targetManageUrlPreffix");
        String region = remoteConfig.getConfigValue("region");
        String province = remoteConfig.getConfigValue("province");
        String year = timeFilter.getYear();
        String quarter = "month".equals(timeFilter.getDateType()) ? DateUtil.getQuarterOfMonth(timeFilter.getMonth()) : timeFilter.getQuarter();

        String metric = metricInfoBean.getMetric();
        String level = metricInfoBean.getLevel();
        List<String> regionList = metricInfoBean.getRegionList();

        StringBuilder sb = new StringBuilder(urlSuffix);
        sb.append("?");
        sb.append("tabtype=detail&");
        sb.append("year=").append(year).append("&");
        sb.append("quarter=").append(quarter).append("&");
        sb.append("targettype=").append(metric).append("&");
        if (region.equals(level)) {
            sb.append("regions=").append(StringUtils.join(regionList, "|")).append("&");
        }
        if (province.equals(level)) {
            sb.append("provinces=").append(StringUtils.join(regionList, "|")).append("&");
        }
        if (GeneralUtil.isNotEmpty(viewspotTypeId)) {
            sb.append("category=").append(viewspotTypeId);
        }
        if (GeneralUtil.isNotEmpty(scenicClassCode)) {
            sb.append("scenicclass=").append(scenicClassCode);
        }
        return sb.toString();
    }
}
