package com.ctrip.tour.business.dashboard.helper

import com.ctrip.tour.business.dashboard.tktBusiness.helper.DimHelper
import spock.lang.Specification

class DimHelperTest extends Specification {
    def dimhelper = new DimHelper()

    def "test getSpecialDimValue"() {
        given:
        def dim = "ttd_weighted_defect_achieved_rate"
        def preffix = ""
        def dimMap = new HashMap()
        dimMap.put("ttd_weighted_defect_cnt", 52878.9D)
        dimMap.put("ttd_pay_odr_cnt", 2.9349905E7D)
        dimMap.put("ttd_weighted_defect_target", 0.009135D)
        dimMap.put("ttd_weighted_defect_rate", 0.001802D)

        def popDimMap = null

        when:
        def result = dimhelper.getSpecialDimValue(dim, preffix, dimMap, popDimMap)

        then:
        with(result){
            result == 1.802736726874658D
        }
    }
}
