package com.ctrip.tour.business.dashboard.tktBusiness.impl;

import com.ctrip.tour.business.dashboard.utils.UserUtil;
import com.ctrip.tour.rights.client.*;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class TaskFlowSyncJobImpl  {


    @Autowired
    private TourRightsServiceClient tourRightsServiceClient;

/*    @Override
    @DalTransactional(logicDbName = "TtdReportDB_W")
    public UpdateTaskFlowTableDataResponseType updateTaskFlowTableData(UpdateTaskFlowTableDataRequestType request) throws Exception {
        //判定是否进入了事务中
//        System.out.println(DalTransactionManager.isInTransaction());

        //只刷当天分区数据  历史分区数据不刷新
        String today = DateUtil.getCurrentDate();

        List<EnumDataType> eumnDataTypeList = getEumnDataTypeList();
        for (EnumDataType enumDataType : eumnDataTypeList) {
            String type = enumDataType.getEnumType();
            for (EnumDataValueType enumValue : enumDataType.getEnumValueList()) {
                String name = enumValue.getName();
                String code = enumValue.getValue();
                busTtdTaskSummaryTDao.updateNameByCode(name, code, type, today);
            }
        }

        return new UpdateTaskFlowTableDataResponseType();
    }*/


    private List<EnumDataType> getEumnDataTypeList() throws Exception {
        GetEnumDataRequestType request = new GetEnumDataRequestType();
        request.setEnumTypeList(Lists.newArrayList("BIZ_CATEGORY","COLLECTION_TYPE","TASK_TYPE"));
        tourRightsServiceClient.setRawLocale(UserUtil.getVbkLocale());
        GetEnumDataResponseType response = tourRightsServiceClient.getEnumData(request);
        return response.getDataList();
    }

    public Map<String, Map<String, String>> getEumnDataTypeMap() throws Exception {
        List<EnumDataType> dataList = getEumnDataTypeList();
        Map<String, Map<String, String>> dataMap = new HashMap<>();
        for (EnumDataType enumDataType : dataList) {
            String type = enumDataType.getEnumType();
            switch (type) {
                case "BIZ_CATEGORY":
                    type = "biz_category_code";
                    break;
                case "COLLECTION_TYPE":
                    type = "task_collection_code";
                    break;
                case "TASK_TYPE":
                    type = "task_type_code";
                    break;
            }
            Map<String, String> enumMap = new HashMap<>();
            for (EnumDataValueType enumValue : enumDataType.getEnumValueList()) {
                String name = enumValue.getName();
                String code = enumValue.getValue();
                enumMap.put(code, name);
            }
            dataMap.put(type, enumMap);
        }
        return dataMap;
    }
}
