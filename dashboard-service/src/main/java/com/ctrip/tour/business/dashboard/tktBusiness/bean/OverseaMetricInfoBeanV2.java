package com.ctrip.tour.business.dashboard.tktBusiness.bean;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
public class OverseaMetricInfoBeanV2 {

    //年份
    private String year;
    //季度
    private List<String> quarters;
    //邮箱前缀
    private String domainName;
    //指标
    private String metric;
    //目的地考核层级
    private List<String> destinationLevelList;
    //目的地考核范围
    private List<String> destinationRangeList;
    //目的地考核范围Map，key为考核层级，value考核层级对应的考核范围
    private Map<String,List<String>> destinationLevelMap;
    //分季度列表信息
    private Map<String,OverseaMetricInfoBeanV2Child> quarterInfoMap;
}
