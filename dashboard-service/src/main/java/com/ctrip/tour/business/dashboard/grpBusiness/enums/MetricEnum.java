package com.ctrip.tour.business.dashboard.grpBusiness.enums;

import com.ctrip.tour.business.dashboard.grpBusiness.common.GrpConstant;

public enum MetricEnum {
    INCOME(GrpConstant.INCOME, "销售额(GMV)", GrpConstant.INCOME, false,"v.page.workbench.salesVolume.tips"),//NOSONAR
    DIY_INCOME(GrpConstant.DIY_INCOME, "销售额(GMV)", GrpConstant.DIY_INCOME, false,"v.page.workbench.diyIncome.tips"),//NOSONAR
    DIY_INCOME_ACHIEVE_RATE(GrpConstant.DIY_INCOME_ACHIEVE_RATE, "销售额(GMV)", GrpConstant.DIY_INCOME_ACHIEVE_RATE, true,"v.page.workbench.diyIncomeAch.tips"),//NOSONAR
    GRP_INCOME_ACHIEVE_RATE(GrpConstant.GRP_INCOME_ACHIEVE_RATE, "销售额(GMV)", GrpConstant.GRP_INCOME_ACHIEVE_RATE, true,"v.page.workbench.grpIncomeAch.tips"),//NOSONAR
    SELF_OPR_INCOME(GrpConstant.SELF_OPR_INCOME, "总自营销售额", GrpConstant.GRP_INCOME_ACHIEVE_RATE, false,"v.page.workbench.selfOprIncome.tips"),//NOSONAR
    SELF_OPR_INCOME_RATIO(GrpConstant.SELF_OPR_INCOME_RATIO, "总自营销售额占比", GrpConstant.GRP_INCOME_ACHIEVE_RATE, true,"v.page.workbench.selfOprIncomeRatio.tips"),//NOSONAR

    PROFIT_PRICE(GrpConstant.PROFIT_PRICE, "毛利",GrpConstant.PROFIT_PRICE, false,"v.page.workbench.grossProfit.tips"),//NOSONAR
    DIY_PROFIT(GrpConstant.DIY_PROFIT, "毛利",GrpConstant.DIY_PROFIT, false,"v.page.workbench.diyPorfit.tips"),//NOSONAR
    DIY_PROFIT_ACHIEVE_RATE(GrpConstant.DIY_PROFIT_ACHIEVE_RATE, "毛利",GrpConstant.DIY_PROFIT_ACHIEVE_RATE, true,"v.page.workbench.diyPorfitAch.tips"),//NOSONAR
    GRP_PROFIT_ACHIEVE_RATE(GrpConstant.GRP_PROFIT_ACHIEVE_RATE, "毛利",GrpConstant.GRP_PROFIT_ACHIEVE_RATE, true,"v.page.workbench.grpProfitAcg.tips"),//NOSONAR
    SELF_OPR_PROFIT(GrpConstant.SELF_OPR_PROFIT, "自营毛利",GrpConstant.SELF_OPR_PROFIT, false,"v.page.workbench.selfOprProfit.tips"),//NOSONAR
    SELF_OPR_PROFIT_RATE(GrpConstant.SELF_OPR_PROFIT_RATE, "自营毛利率",GrpConstant.SELF_OPR_PROFIT_RATE, true,"v.page.workbench.selfOprProfitRate.tips"),//NOSONAR
    SELF_OPR_PROFIT_RATIO(GrpConstant.SELF_OPR_PROFIT_RATIO, "自营毛利额占比 ",GrpConstant.SELF_OPR_PROFIT_RATIO, true,"v.page.workbench.selfOprProfitRatio.tips"),//NOSONAR

    UV(GrpConstant.UV, "访问人数",GrpConstant.UV, false,"v.page.workbench.detailPage.UV.tips"),//NOSONAR
    SELF_OPR_UV(GrpConstant.SELF_OPR_UV, "访问人数",GrpConstant.SELF_OPR_UV, false,"v.page.workbench.detailPage.UV.tips"),//NOSONAR
    CONVERSION_RATE(GrpConstant.CONVERSION_RATE, "详情页转化率",GrpConstant.CONVERSION_RATE, true,"v.page.workbench.detailPage.conversion.tips"),//NOSONAR
    SELF_OPR_CONVERSION_RATE(GrpConstant.SELF_OPR_CONVERSION_RATE, "自营商品详情转化率",GrpConstant.SELF_OPR_CONVERSION_RATE, true,"v.page.workbench.selfOprConversionRate.conversion.tips"),//NOSONAR
    AGENT_CONVERSION_RATE(GrpConstant.AGENT_CONVERSION_RATE, "代理商品详情转化率",GrpConstant.AGENT_CONVERSION_RATE, true,"v.page.workbench.agentConversionRate.conversion.tips"),//NOSONAR

    MULTIPLE_PRICE(GrpConstant.MULTIPLE_PRICE, "价格倍数",GrpConstant.MULTIPLE_PRICE, false,"v.page.workbench.price.multiple.tips"),//NOSONAR
    MULTIPLE_PRICE_MORE_RATE(GrpConstant.MULTIPLE_PRICE_MORE_RATE, "价格倍数>1.0的产品比例",GrpConstant.MULTIPLE_PRICE_MORE_RATE, true,"v.page.workbench.priceMultiple.greaterThan.tips"),//NOSONAR
    MULTIPLE_PRICE_LESS_RATE(GrpConstant.MULTIPLE_PRICE_LESS_RATE, "价格倍数<0.6的产品比例",GrpConstant.MULTIPLE_PRICE_LESS_RATE, true,"v.page.workbench.priceMultiple.lessThan.tips"),//NOSONAR
    UNCOMPARE_RATE(GrpConstant.UNCOMPARE_RATE, "不可比价比例",GrpConstant.UNCOMPARE_RATE, true,"v.page.workbench.umCompareRate.tips"),//NOSONAR
    WEIGHTED_ANOMALY_RATE(GrpConstant.WEIGHTED_ANOMALY_RATE, "加权异常率",GrpConstant.WEIGHTED_ANOMALY_RATE, true,"v.page.workbench.weiAnoamlRate.tips"),//NOSONAR

    SELF_SERVICE_COVERAGE_RATE(GrpConstant.SELF_SERVICE_COVERAGE_RATE, "自服务覆盖率",GrpConstant.SELF_SERVICE_COVERAGE_RATE, true,"v.page.workbench.service.coverage.tips"),//NOSONAR
    SELF_SERVICE_CONVERSION_RATE(GrpConstant.SELF_SERVICE_CONVERSION_RATE, "自服务转化率",GrpConstant.SELF_SERVICE_CONVERSION_RATE, true,"v.page.workbench.service.conversion.tips"),//NOSONAR
    TIMELY_RESPONSE_RATE(GrpConstant.TIMELY_RESPONSE_RATE, "120s及时回复率",GrpConstant.TIMELY_RESPONSE_RATE, true,"v.page.workbench.timelyRespRate.tips"),//NOSONAR
    AVG_TRANSLATION_REVIEW_SCORE(GrpConstant.AVG_TRANSLATION_REVIEW_SCORE, "商家会话点评均分",GrpConstant.AVG_TRANSLATION_REVIEW_SCORE, false,"v.page.workbench.avgTranRevScore.tips"),//NOSONAR
    DISP_FAIL_RATE(GrpConstant.DISP_FAIL_RATE, "商家会话分配失败率",GrpConstant.DISP_FAIL_RATE, true,"v.page.workbench.dispFail.tips"),//NOSONAR

    FITTING_NPS(GrpConstant.FITTING_NPS, "拟合nps",GrpConstant.FITTING_NPS, true,"v.page.workbench.fitting.nps.tips"),//NOSONAR
    RECOMMENDATION_RATE(GrpConstant.RECOMMENDATION_RATE, "推荐率",GrpConstant.RECOMMENDATION_RATE, true,"v.page.workbench.recommendation.rate.tips"),//NOSONAR
    SLANDER_RATE(GrpConstant.SLANDER_RATE, "诋毁率",GrpConstant.SLANDER_RATE, true,"v.page.workbench.defamation.rate.tips"),//NOSONAR
    ORDER_COVERAGE_RATE(GrpConstant.ORDER_COVERAGE_RATE, "订单覆盖率",GrpConstant.ORDER_COVERAGE_RATE, true,"v.page.workbench.order.coverage.rate.tips"),//NOSONAR
    DIY_FITTINGNPS(GrpConstant.DIY_FITTINGNPS, "订单覆盖率",GrpConstant.DIY_FITTINGNPS, true,"v.page.workbench.diyFittingNps.tips"),//NOSONAR
    DIY_RECOMMENDATION_RATE(GrpConstant.DIY_RECOMMENDATION_RATE, "订单覆盖率",GrpConstant.DIY_RECOMMENDATION_RATE, true,"v.page.workbench.diyRecommend.tips"),//NOSONAR
    DIY_SLANDER_RATE(GrpConstant.DIY_SLANDER_RATE, "订单覆盖率",GrpConstant.DIY_SLANDER_RATE, true,"v.page.workbench.diySladerRate.tips"),//NOSONAR

    GRP_DRIVER_CHECKIN_RATE(GrpConstant.GRP_DRIVER_CHECKIN_RATE, "拟合nps",GrpConstant.GRP_DRIVER_CHECKIN_RATE, true,"v.page.workbench.grpDirverCheckIn.tips"),//NOSONAR
    GRP_DRIVER_DISPATCH_RATE(GrpConstant.GRP_DRIVER_DISPATCH_RATE, "推荐率",GrpConstant.GRP_DRIVER_DISPATCH_RATE, true,"v.page.workbench.grpDirverDisp.tips"),//NOSONAR
    GRP_DRIVER_EXECUTION_RATE(GrpConstant.GRP_DRIVER_EXECUTION_RATE, "诋毁率",GrpConstant.GRP_DRIVER_EXECUTION_RATE, true,"v.page.workbench.grpDiriverExe.tips"),//NOSONAR
    DIY_DRIVER_ACTUAL_DISPACTH_RATE(GrpConstant.DIY_DRIVER_ACTUAL_DISPACTH_RATE, "订单覆盖率",GrpConstant.DIY_DRIVER_ACTUAL_DISPACTH_RATE, true,"v.page.workbench.diyDriverActDisp.tips"),//NOSONAR
    DIY_DRIVER_DISPATCH_RATE(GrpConstant.DIY_DRIVER_DISPATCH_RATE, "订单覆盖率",GrpConstant.DIY_DRIVER_DISPATCH_RATE, true,"v.page.workbench.diyDriverDisp.tips"),//NOSONAR
    DIY_DRIVER_EXECUTION_RATE(GrpConstant.DIY_DRIVER_EXECUTION_RATE, "订单覆盖率",GrpConstant.DIY_DRIVER_EXECUTION_RATE, true,"v.page.workbench.diyDriverExe.tips"),//NOSONAR

    GRP_GUIDER_CHECKIN_RATE(GrpConstant.GRP_GUIDER_CHECKIN_RATE, "拟合nps",GrpConstant.GRP_GUIDER_CHECKIN_RATE, true,"v.page.workbench.grpGuiderCheckIn.tips"),//NOSONAR
    GRP_GUIDER_DISPATCH_RATE(GrpConstant.GRP_GUIDER_DISPATCH_RATE, "推荐率",GrpConstant.GRP_GUIDER_DISPATCH_RATE, true,"v.page.workbench.grpGuiderDisp.tips"),//NOSONAR
    GRP_GUIDER_ACTUAL_DISPACTH_RATE(GrpConstant.GRP_GUIDER_ACTUAL_DISPACTH_RATE, "诋毁率",GrpConstant.GRP_GUIDER_ACTUAL_DISPACTH_RATE, true,"v.page.workbench.grpGuiderAct.tips"),//NOSONAR
    DIY_GUIDER_DISPATCH_RATE(GrpConstant.DIY_GUIDER_DISPATCH_RATE, "订单覆盖率",GrpConstant.DIY_GUIDER_DISPATCH_RATE, true,"v.page.workbench.diyGuiderDisp.tips"),//NOSONAR


    AVA_ORDER_CAP(GrpConstant.AVA_ORDER_CAP, "拟合nps",GrpConstant.AVA_ORDER_CAP, false,"v.page.workbench.avaOrderCap.tips"),//NOSONAR
    VENDOR_ORD_ACCEPT_RATE(GrpConstant.VENDOR_ORD_ACCEPT_RATE, "推荐率",GrpConstant.VENDOR_ORD_ACCEPT_RATE, true,"v.page.workbench.venderacctRate.tips"),//NOSONAR
    COMPLETED_ORDERS(GrpConstant.COMPLETED_ORDERS, "诋毁率",GrpConstant.COMPLETED_ORDERS, false,"v.page.workbench.completedOrd.tips"),//NOSONAR
    COMPLETED_ORDERS_RATE(GrpConstant.COMPLETED_ORDERS_RATE, "订单覆盖率",GrpConstant.COMPLETED_ORDERS_RATE, true,"v.page.workbench.completedOrdRate.tips"),//NOSONAR
    ORD_AVG_PRICE(GrpConstant.ORD_AVG_PRICE, "订单覆盖率",GrpConstant.ORD_AVG_PRICE, false,"v.page.workbench.ordAvgPrice.tips"),//NOSONAR
    CPR_COMPLETED_ORDERS_RATE(GrpConstant.CPR_COMPLETED_ORDERS_RATE, "订单覆盖率",GrpConstant.CPR_COMPLETED_ORDERS_RATE, true,"v.page.workbench.cprCompletedOrdRate.tips"),//NOSONAR


    HIGH_QUA_VENDOR_COUNT_WITH_SELFOPRPRD(GrpConstant.HIGH_QUA_VENDOR_COUNT_WITH_SELFOPRPRD, "有自营商品的好商家数量",GrpConstant.HIGH_QUA_VENDOR_COUNT_WITH_SELFOPRPRD, false,"v.page.workbench.weighted.platform.highQuaVendorCountWithSelfOprPrd.tip"),//NOSONAR
    HIGH_QUA_VENDOR_COUNT_WITH_SELFOPR_PRD_FAILTOMEET(GrpConstant.HIGH_QUA_VENDOR_COUNT_WITH_SELFOPR_PRD_FAILTOMEET, "有自营商品但好商家考核不达标的好商家数量",GrpConstant.HIGH_QUA_VENDOR_COUNT_WITH_SELFOPR_PRD_FAILTOMEET, false,"v.page.workbench.weighted.product.highQuaVendorCountWithSelfOprPrdFailToMeet.division.tip"),//NOSONAR

    WEIGHTED_PLATFORM_INFO_SCORE(GrpConstant.WEIGHTED_PLATFORM_INFO_SCORE, "加权平台信息分",GrpConstant.WEIGHTED_PLATFORM_INFO_SCORE, false,"v.page.workbench.weighted.platform.information.tip"),//NOSONAR
    WEIGHTED_PRD_INFO_SCORE(GrpConstant.WEIGHTED_PRD_INFO_SCORE, "加权商品信息分",GrpConstant.WEIGHTED_PRD_INFO_SCORE, false,"v.page.workbench.weighted.product.information.division.tip"),//NOSONAR

    SELF_OPR_SINGLE_UV_VAL_PRD(GrpConstant.SELF_OPR_SINGLE_UV_VAL_PRD, "自营商品单UV价值",GrpConstant.SELF_OPR_SINGLE_UV_VAL_PRD, false,"v.page.workbench.weighted.selfOprSingleUVValPrd.tip"),//NOSONAR
    AGENT_SINGLE_UV_VAL_PRD(GrpConstant.AGENT_SINGLE_UV_VAL_PRD, "代理商品单UV价值",GrpConstant.AGENT_SINGLE_UV_VAL_PRD, false,"v.page.workbench.weighted.product.agentSingleUVValPrd.tip"),//NOSONAR

    SELF_OPR_PARENT_PRD_COUNT(GrpConstant.SELF_OPR_PARENT_PRD_COUNT, "自营母商品数量",GrpConstant.SELF_OPR_PARENT_PRD_COUNT, false,"v.page.workbench.weighted.selfOprParentPrdCount.tip"),//NOSONAR
    ACTIVE_SELF_OPR_PARENT_PRD_COUNT(GrpConstant.ACTIVE_SELF_OPR_PARENT_PRD_COUNT, "有效在线自营母商品数量",GrpConstant.ACTIVE_SELF_OPR_PARENT_PRD_COUNT, false,"v.page.workbench.weighted.product.activeSelfOprParentPrdCount.tip"),//NOSONAR
    ACTIVE_SELF_OPR_PARENT_PRD_RATE(GrpConstant.ACTIVE_SELF_OPR_PARENT_PRD_RATE, "有效在线上线自营母商品比例",GrpConstant.ACTIVE_SELF_OPR_PARENT_PRD_RATE, true,"v.page.workbench.weighted.activeSelfOprParentPrdRate.tip"),//NOSONAR
    SRV_FREQUENCY_INLST30D(GrpConstant.SRV_FREQUENCY_INLST30D, "近 30 天的班期密度",GrpConstant.SRV_FREQUENCY_INLST30D, true,"v.page.workbench.weighted.product.srvFrequencyInLst30d.tip");//NOSONAR


    private String englishName;
    private String chineseName;
    private String serviceName;
    private boolean isRatio;
    private String metricTipsSharkKey;

    MetricEnum(String englishName, String chineseName, String serviceName, boolean isRatio, String metricTipsSharkKey) {
        this.englishName = englishName;
        this.chineseName = chineseName;
        this.serviceName = serviceName;
        this.isRatio = isRatio;
        this.metricTipsSharkKey = metricTipsSharkKey;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public boolean isRatio() {
        return isRatio;
    }

    public void setRatio(boolean ratio) {
        isRatio = ratio;
    }

    public String getEnglishName() {
        return englishName;
    }

    public void setEnglishName(String englishName) {
        this.englishName = englishName;
    }

    public String getChineseName() {
        return chineseName;
    }

    public void setChineseName(String chineseName) {
        this.chineseName = chineseName;
    }

    public String getMetricTipsSharkKey() {
        return metricTipsSharkKey;
    }

    public void setMetricTipsSharkKey(String metricTipsSharkKey) {
        this.metricTipsSharkKey = metricTipsSharkKey;
    }
}
