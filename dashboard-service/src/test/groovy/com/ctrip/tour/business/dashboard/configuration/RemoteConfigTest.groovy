package com.ctrip.tour.business.dashboard.configuration

import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaDrillDownMetadataBean
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig
import com.ctrip.tour.business.dashboard.utils.MapperUtil
import spock.lang.Specification

class RemoteConfigTest extends Specification {
    def overseaDrillDownMetadata = MapperUtil.str2Obj("{\"metricMetadataList\":[{\"metricList\":[\"101\",\"102\"],\"subMetricFieldList\":[{\"subMetricList\":[\"destination\",\"destination_c\",\"destination_t\"],\"field\":\"region\",\"baseInfoId\":6,\"baseInfoGroupList\":[\"region_id\",\"region_name\"],\"baseInfoLikeIndexList\":[1],\"tableDataIdMap\":{\"other\":6,\"target\":1},\"bubbleGroupListMap\":{\"other\":[\"region_id\",\"region_name\"],\"target\":[\"region_id\"]},\"tableGroupListMap\":{\"other\":[\"region_id\",\"region_name\"],\"target\":[\"region_id\"]},\"conditionColumn\":\"region_id\",\"needTarget\":true,\"needBubble\":true,\"headerFieldList\":[\"region_name\"],\"showField\":\"region_name\",\"showFieldId\":\"region_id\"},{\"subMetricList\":[\"destination\",\"destination_c\",\"destination_t\"],\"field\":\"province\",\"baseInfoId\":6,\"baseInfoGroupList\":[\"province_id\",\"province_name\"],\"baseInfoLikeIndexList\":[1],\"tableDataIdMap\":{\"other\":6,\"target\":1},\"bubbleGroupListMap\":{\"other\":[\"province_id\",\"province_name\"],\"target\":[\"province_id\"]},\"tableGroupListMap\":{\"other\":[\"region_id\",\"region_name\",\"province_id\",\"province_name\"],\"target\":[\"region_id\",\"province_id\"]},\"conditionColumn\":\"province_id\",\"needTarget\":true,\"needBubble\":true,\"headerFieldList\":[\"region_name\",\"province_name\"],\"showField\":\"province_name\",\"showFieldId\":\"province_id\"},{\"subMetricList\":[\"destination\",\"destination_c\",\"destination_t\"],\"field\":\"country\",\"baseInfoId\":6,\"baseInfoGroupList\":[\"country_id\",\"country_name\"],\"baseInfoLikeIndexList\":[1],\"tableDataIdMap\":{\"other\":6},\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\",\"country_id\",\"country_name\"]},\"conditionColumn\":\"country_id\",\"needTarget\":false,\"needBubble\":false,\"headerFieldList\":[\"region_name\",\"province_name\",\"country_name\"]},{\"subMetricList\":[\"destination\",\"destination_c\",\"destination_t\"],\"field\":\"examinee\",\"baseInfoId\":12,\"baseInfoGroupList\":[\"examinee\",\"examinee_display\"],\"baseInfoLikeIndexList\":[1],\"tableDataIdMap\":{\"2019\":6,\"target\":4,\"lastyear\":6,\"current\":12},\"tableGroupListMap\":{\"2019\":[\"region_id\",\"province_id\"],\"target\":[\"region_id\",\"province_id\",\"examinee\"],\"current\":[\"region_name\",\"region_id\",\"province_name\",\"province_id\",\"examinee_display\",\"examinee\"],\"lastyear\":[\"region_id\",\"province_id\"]},\"conditionColumn\":\"examinee\",\"pagingConditionColumn\":\"province_id\",\"needTarget\":true,\"needBubble\":false,\"headerFieldList\":[\"region_name\",\"province_name\",\"examinee_display\"]},{\"subMetricList\":[\"destination\",\"destination_c\",\"destination_t\"],\"field\":\"viewspot\",\"baseInfoId\":10,\"baseInfoGroupList\":[\"viewspot_id\",\"viewspot_name\"],\"baseInfoLikeIndexList\":[0,1],\"tableDataIdMap\":{\"current\":10,\"other\":11},\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\",\"country_name\",\"viewspot_id\",\"viewspot_name\"]},\"conditionColumn\":\"viewspot_id\",\"needTarget\":false,\"needBubble\":false,\"headerFieldList\":[\"region_name\",\"province_name\",\"country_name\",\"viewspot_id\",\"viewspot_name\"]},{\"subMetricList\":[\"site\"],\"field\":\"site\",\"baseInfoId\":9,\"baseInfoGroupList\":[\"site_code\",\"site\"],\"baseInfoLikeIndexList\":[1],\"tableDataIdMap\":{\"other\":9,\"target\":2},\"bubbleGroupListMap\":{\"other\":[\"site_code\",\"site\"],\"target\":[\"site_code\"]},\"tableGroupListMap\":{\"other\":[\"site_code\",\"site\"],\"target\":[\"site_code\"]},\"conditionColumn\":\"site_code\",\"needTarget\":true,\"needBubble\":true,\"headerFieldList\":[\"site\"],\"showField\":\"site\",\"showFieldId\":\"site_code\"},{\"subMetricList\":[\"site\"],\"field\":\"locale\",\"baseInfoId\":9,\"baseInfoGroupList\":[\"locale_code\",\"locale\"],\"baseInfoLikeIndexList\":[1],\"tableDataIdMap\":{\"other\":9},\"tableGroupListMap\":{\"other\":[\"site\",\"locale_code\",\"locale\"]},\"conditionColumn\":\"locale_code\",\"needTarget\":false,\"needBubble\":false,\"headerFieldList\":[\"site\",\"locale\"]},{\"subMetricList\":[\"channel\"],\"field\":\"channel\",\"baseInfoId\":9,\"baseInfoGroupList\":[\"channel_code\",\"channel_name\"],\"baseInfoLikeIndexList\":[1],\"tableDataIdMap\":{\"other\":9,\"target\":3},\"bubbleGroupListMap\":{\"other\":[\"channel_code\",\"channel_name\"],\"target\":[\"channel_code\"]},\"tableGroupListMap\":{\"other\":[\"channel_code\",\"channel_name\"],\"target\":[\"channel_code\"]},\"conditionColumn\":\"channel_code\",\"needTarget\":true,\"needBubble\":true,\"headerFieldList\":[\"channel_name\"],\"showField\":\"channel_name\",\"showFieldId\":\"channel_code\"},{\"subMetricList\":[\"channel\"],\"field\":\"site\",\"baseInfoId\":9,\"baseInfoGroupList\":[\"site_code\",\"site\"],\"baseInfoLikeIndexList\":[1],\"tableDataIdMap\":{\"other\":9},\"tableGroupListMap\":{\"other\":[\"channel_name\",\"site\",\"site_code\"]},\"conditionColumn\":\"site_code\",\"needTarget\":false,\"needBubble\":false,\"headerFieldList\":[\"channel_name\",\"site\"]},{\"subMetricList\":[\"channel\"],\"field\":\"locale\",\"baseInfoId\":9,\"baseInfoGroupList\":[\"locale_code\",\"locale\"],\"baseInfoLikeIndexList\":[1],\"tableDataIdMap\":{\"other\":9},\"tableGroupListMap\":{\"other\":[\"channel_name\",\"site\",\"locale_code\",\"locale\"]},\"conditionColumn\":\"locale_code\",\"needTarget\":false,\"needBubble\":false,\"headerFieldList\":[\"channel_name\",\"site\",\"locale\"]}]},{\"metricList\":[\"103\"],\"subMetricFieldList\":[{\"subMetricList\":[\"destination_c\",\"destination_t\"],\"field\":\"region\",\"baseInfoId\":32,\"baseInfoGroupList\":[\"region_id\",\"region_name\"],\"baseInfoLikeIndexList\":[1],\"tableDataIdMap\":{\"other\":32,\"target\":38},\"lineGroupListMap\":{\"current\":[\"region_name\"]},\"tableGroupListMap\":{\"other\":[\"region_id\",\"region_name\"],\"target\":[\"region_id\"]},\"needTarget\":true,\"needLine\":true,\"headerFieldList\":[\"region_name\"],\"conditionColumn\":\"region_id\",\"showField\":\"region_name\",\"showFieldId\":\"region_id\"},{\"subMetricList\":[\"destination_c\",\"destination_t\"],\"field\":\"province\",\"baseInfoId\":32,\"baseInfoGroupList\":[\"province_id\",\"province_name\"],\"baseInfoLikeIndexList\":[1],\"tableDataIdMap\":{\"other\":32,\"target\":38},\"lineGroupListMap\":{\"current\":[\"province_name\"]},\"tableGroupListMap\":{\"other\":[\"region_id\",\"region_name\",\"province_id\",\"province_name\"],\"target\":[\"region_id\",\"province_id\"]},\"needTarget\":true,\"needLine\":true,\"headerFieldList\":[\"region_name\",\"province_name\"],\"conditionColumn\":\"province_id\",\"showField\":\"province_name\",\"showFieldId\":\"province_id\"},{\"subMetricList\":[\"destination_c\",\"destination_t\"],\"field\":\"country\",\"baseInfoId\":32,\"baseInfoGroupList\":[\"country_id\",\"country_name\"],\"baseInfoLikeIndexList\":[1],\"tableDataIdMap\":{\"other\":32},\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\",\"country_id\",\"country_name\"]},\"needTarget\":false,\"needLine\":false,\"headerFieldList\":[\"region_name\",\"province_name\",\"country_name\"],\"conditionColumn\":\"country_id\"},{\"subMetricList\":[\"destination_c\",\"destination_t\"],\"field\":\"examinee\",\"baseInfoId\":33,\"baseInfoGroupList\":[\"examinee\",\"examinee_display\"],\"baseInfoLikeIndexList\":[1],\"tableDataIdMap\":{\"other\":33,\"target\":38},\"tableGroupListMap\":{\"other\":[\"region_name\",\"region_id\",\"province_name\",\"province_id\",\"examinee_display\",\"examinee\"],\"target\":[\"region_id\",\"province_id\"]},\"needTarget\":true,\"needLine\":false,\"headerFieldList\":[\"region_name\",\"province_name\",\"examinee_display\"],\"conditionColumn\":\"examinee\"},{\"subMetricList\":[\"destination_c\",\"destination_t\"],\"field\":\"viewspot\",\"baseInfoId\":34,\"baseInfoGroupList\":[\"viewspot_id\",\"viewspot_name\"],\"baseInfoLikeIndexList\":[0,1],\"tableDataIdMap\":{\"other\":34},\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\",\"country_name\",\"viewspot_id\",\"viewspot_name\"]},\"needTarget\":false,\"needLine\":false,\"headerFieldList\":[\"region_name\",\"province_name\",\"country_name\",\"viewspot_id\",\"viewspot_name\"],\"conditionColumn\":\"viewspot_id\"},{\"subMetricList\":[\"destination_c\",\"destination_t\"],\"field\":\"vendor\",\"baseInfoId\":35,\"baseInfoGroupList\":[\"vendor_id\",\"vendor_name\"],\"baseInfoLikeIndexList\":[0,1],\"tableDataIdMap\":{\"other\":35},\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\",\"vendor_id\",\"vendor_name\"]},\"needTarget\":false,\"needLine\":false,\"headerFieldList\":[\"region_name\",\"province_name\",\"vendor_id\",\"vendor_name\"],\"conditionColumn\":\"vendor_id\"},{\"subMetricList\":[\"site\"],\"field\":\"site\",\"baseInfoId\":37,\"baseInfoGroupList\":[\"site_code\",\"site\"],\"baseInfoLikeIndexList\":[1],\"tableDataIdMap\":{\"other\":37,\"target\":39},\"lineGroupListMap\":{\"current\":[\"site\"]},\"tableGroupListMap\":{\"other\":[\"site_code\",\"site\"],\"target\":[\"site_code\"]},\"needTarget\":true,\"needLine\":true,\"headerFieldList\":[\"site\"],\"conditionColumn\":\"site_code\",\"showField\":\"site\",\"showFieldId\":\"site_code\"},{\"subMetricList\":[\"site\"],\"field\":\"viewspot\",\"baseInfoId\":36,\"baseInfoGroupList\":[\"viewspot_id\",\"viewspot_name\"],\"baseInfoLikeIndexList\":[0,1],\"tableDataIdMap\":{\"other\":36},\"tableGroupListMap\":{\"other\":[\"site\",\"viewspot_id\",\"viewspot_name\"]},\"needTarget\":false,\"needLine\":false,\"headerFieldList\":[\"site\",\"viewspot_id\",\"viewspot_name\"],\"conditionColumn\":\"viewspot_id\"},{\"subMetricList\":[\"site\"],\"field\":\"vendor\",\"baseInfoId\":37,\"baseInfoGroupList\":[\"vendor_id\",\"vendor_name\"],\"baseInfoLikeIndexList\":[0,1],\"tableDataIdMap\":{\"other\":37},\"tableGroupListMap\":{\"other\":[\"site\",\"vendor_id\",\"vendor_name\"]},\"needTarget\":false,\"needLine\":false,\"headerFieldList\":[\"site\",\"vendor_id\",\"vendor_name\"],\"conditionColumn\":\"vendor_id\"}]},{\"metricList\":[\"105\",\"106\",\"107\"],\"subMetricFieldList\":[{\"subMetricList\":[\"tklk\",\"tfly\"],\"field\":\"region\",\"baseInfoGroupList\":[\"region_id\",\"region_name\"],\"baseInfoLikeIndexList\":[1],\"lineGroupListMap\":{\"current\":[\"region_name\"]},\"tableGroupListMap\":{\"other\":[\"region_id\",\"region_name\"]},\"needLine\":true,\"headerFieldList\":[\"region_name\"],\"conditionColumn\":\"region_id\",\"showField\":\"region_name\",\"showFieldId\":\"region_id\"},{\"subMetricList\":[\"tklk\",\"tfly\"],\"field\":\"province\",\"baseInfoGroupList\":[\"province_id\",\"province_name\"],\"baseInfoLikeIndexList\":[1],\"lineGroupListMap\":{\"current\":[\"province_name\"]},\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\"]},\"needLine\":true,\"headerFieldList\":[\"region_name\",\"province_name\"],\"conditionColumn\":\"province_id\",\"showField\":\"province_name\",\"showFieldId\":\"province_id\"},{\"subMetricList\":[\"tklk\",\"tfly\"],\"field\":\"country\",\"baseInfoGroupList\":[\"country_id\",\"country_name\"],\"baseInfoLikeIndexList\":[1],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\",\"country_name\"]},\"needLine\":false,\"headerFieldList\":[\"region_name\",\"province_name\",\"country_name\"],\"conditionColumn\":\"country_id\"},{\"subMetricList\":[\"tklk\",\"tfly\"],\"field\":\"examinee\",\"baseInfoGroupList\":[\"examinee_display\",\"examinee\"],\"baseInfoLikeIndexList\":[1],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\",\"examinee_display\"]},\"needLine\":false,\"headerFieldList\":[\"region_name\",\"province_name\",\"examinee_display\"],\"conditionColumn\":\"examinee\"}]},{\"year\":\"2024\",\"metricList\":[\"103\"],\"subMetricFieldList\":[{\"subMetricList\":[\"destination_c\",\"destination_t\"],\"field\":\"region\",\"baseInfoId\":62,\"baseInfoGroupList\":[\"region_id\",\"region_name\"],\"baseInfoLikeIndexList\":[1],\"tableDataIdMap\":{\"other\":62,\"target\":70},\"lineGroupListMap\":{\"current\":[\"region_name\"]},\"tableGroupListMap\":{\"other\":[\"region_id\",\"region_name\"],\"target\":[\"region_id\"]},\"needTarget\":true,\"needLine\":true,\"headerFieldList\":[\"region_name\"],\"conditionColumn\":\"region_id\",\"showField\":\"region_name\",\"showFieldId\":\"region_id\"},{\"subMetricList\":[\"destination_c\",\"destination_t\"],\"field\":\"province\",\"baseInfoId\":62,\"baseInfoGroupList\":[\"province_id\",\"province_name\"],\"baseInfoLikeIndexList\":[1],\"tableDataIdMap\":{\"other\":62,\"target\":70},\"lineGroupListMap\":{\"current\":[\"province_name\"]},\"tableGroupListMap\":{\"other\":[\"region_id\",\"region_name\",\"province_id\",\"province_name\"],\"target\":[\"region_id\",\"province_id\"]},\"needTarget\":true,\"needLine\":true,\"headerFieldList\":[\"region_name\",\"province_name\"],\"conditionColumn\":\"province_id\",\"showField\":\"province_name\",\"showFieldId\":\"province_id\"},{\"subMetricList\":[\"destination_c\",\"destination_t\"],\"field\":\"country\",\"baseInfoId\":62,\"baseInfoGroupList\":[\"country_id\",\"country_name\"],\"baseInfoLikeIndexList\":[1],\"tableDataIdMap\":{\"other\":62},\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\",\"country_id\",\"country_name\"]},\"needTarget\":false,\"needLine\":false,\"headerFieldList\":[\"region_name\",\"province_name\",\"country_name\"],\"conditionColumn\":\"country_id\"},{\"subMetricList\":[\"destination_c\",\"destination_t\"],\"field\":\"examinee\",\"baseInfoId\":63,\"baseInfoGroupList\":[\"examinee\",\"examinee_display\"],\"baseInfoLikeIndexList\":[1],\"tableDataIdMap\":{\"other\":63,\"target\":70},\"tableGroupListMap\":{\"other\":[\"region_name\",\"region_id\",\"province_name\",\"province_id\",\"examinee_display\",\"examinee\"],\"target\":[\"region_id\",\"province_id\"]},\"needTarget\":true,\"needLine\":false,\"headerFieldList\":[\"region_name\",\"province_name\",\"examinee_display\"],\"conditionColumn\":\"examinee\"},{\"subMetricList\":[\"destination_c\",\"destination_t\"],\"field\":\"viewspot\",\"baseInfoId\":64,\"baseInfoGroupList\":[\"viewspot_id\",\"viewspot_name\"],\"baseInfoLikeIndexList\":[0,1],\"tableDataIdMap\":{\"other\":64},\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\",\"country_name\",\"viewspot_id\",\"viewspot_name\"]},\"needTarget\":false,\"needLine\":false,\"headerFieldList\":[\"region_name\",\"province_name\",\"country_name\",\"viewspot_id\",\"viewspot_name\"],\"conditionColumn\":\"viewspot_id\"},{\"subMetricList\":[\"destination_c\",\"destination_t\"],\"field\":\"vendor\",\"baseInfoId\":65,\"baseInfoGroupList\":[\"vendor_id\",\"vendor_name\"],\"baseInfoLikeIndexList\":[0,1],\"tableDataIdMap\":{\"other\":65},\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\",\"vendor_id\",\"vendor_name\"]},\"needTarget\":false,\"needLine\":false,\"headerFieldList\":[\"region_name\",\"province_name\",\"vendor_id\",\"vendor_name\"],\"conditionColumn\":\"vendor_id\"},{\"subMetricList\":[\"site\"],\"field\":\"site\",\"baseInfoId\":67,\"baseInfoGroupList\":[\"site_code\",\"site\"],\"baseInfoLikeIndexList\":[1],\"tableDataIdMap\":{\"other\":67,\"target\":71},\"lineGroupListMap\":{\"current\":[\"site\"]},\"tableGroupListMap\":{\"other\":[\"site_code\",\"site\"],\"target\":[\"site_code\"]},\"needTarget\":true,\"needLine\":true,\"headerFieldList\":[\"site\"],\"conditionColumn\":\"site_code\",\"showField\":\"site\",\"showFieldId\":\"site_code\"},{\"subMetricList\":[\"site\"],\"field\":\"viewspot\",\"baseInfoId\":66,\"baseInfoGroupList\":[\"viewspot_id\",\"viewspot_name\"],\"baseInfoLikeIndexList\":[0,1],\"tableDataIdMap\":{\"other\":66},\"tableGroupListMap\":{\"other\":[\"site\",\"viewspot_id\",\"viewspot_name\"]},\"needTarget\":false,\"needLine\":false,\"headerFieldList\":[\"site\",\"viewspot_id\",\"viewspot_name\"],\"conditionColumn\":\"viewspot_id\"},{\"subMetricList\":[\"site\"],\"field\":\"vendor\",\"baseInfoId\":67,\"baseInfoGroupList\":[\"vendor_id\",\"vendor_name\"],\"baseInfoLikeIndexList\":[0,1],\"tableDataIdMap\":{\"other\":67},\"tableGroupListMap\":{\"other\":[\"site\",\"vendor_id\",\"vendor_name\"]},\"needTarget\":false,\"needLine\":false,\"headerFieldList\":[\"site\",\"vendor_id\",\"vendor_name\"],\"conditionColumn\":\"vendor_id\"}]}]}", OverseaDrillDownMetadataBean.class)
    def config = new RemoteConfig(overseaDrillDownMetadata: overseaDrillDownMetadata)


    def "getSubMetricFiledBean"() {

        expect: "call"
        config.getSubMetricFiledBean(year, metric, subMetric, field)

        where: "多条件验证"
        year    |   metric  |   subMetric   |   field
        "2024"  |   "103"     |   "destination_c"  |   "region"
    }
}
