package com.ctrip.tour.business.dashboard.tktBusiness.biz.impl;

import com.ctrip.soa._24922.*;
import com.ctrip.soa._27181.BIBaseReportQueryServiceClient;
import com.ctrip.soa._27181.GetRawDataRequestType;
import com.ctrip.soa._27181.GetRawDataResponseType;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.SqlParamterBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.TaskLevelScoreMappingBean;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.DataUpdateBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.TaskFlowBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.TaskFlowMetricCardBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardEmployeeInfoDao;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardExamineeConfigV2Dao;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardOrganizationInfoDao;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardEmployeeInfo;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.TaskFlowHelper;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.TaskFlowSelect43556Helper;
import com.ctrip.tour.business.dashboard.tktBusiness.impl.TaskFlowSyncJobImpl;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.ctrip.tour.business.dashboard.utils.MapperUtil;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.Future;

@Service
public class TaskFlowBizImpl implements TaskFlowBiz {


    @Autowired
    private BusinessDashboardEmployeeInfoDao employeeInfoDao;

    @Autowired
    private BusinessDashboardOrganizationInfoDao organizationInfoDao;


    @Autowired
    private BusinessDashboardExamineeConfigV2Dao examineConfigV2Dao;

    @Autowired
    private RemoteConfig remoteConfig;

    @Autowired
    private DataUpdateBiz dataUpdateBiz;

    @Autowired
    private TaskFlowMetricCardBiz taskFlowMetricCardBiz;
    

    @Autowired
    private TaskFlowSyncJobImpl taskFlowSyncJob;

    @Autowired
    private TaskFlowSelect43556Helper taskFlowSelect43556Helper;

    @Override
    public GetTaskLevelScoreMappingResponseType getTaskLevelScoreMapping(GetTaskLevelScoreMappingRequestType getTaskLevelScoreMappingRequestType) throws Exception {
        GetTaskLevelScoreMappingResponseType responseType = new GetTaskLevelScoreMappingResponseType();
        TaskLevelScoreMappingBean taskLevelScoreMappingBean = remoteConfig.getTaskLevelScoreMappingBean();
        boolean taskLevelSwitch = "1".equals(taskLevelScoreMappingBean.getTaskLevelSwitch());

        Map<String, TaskLevelScoreRange> taskLevelScoreRangeMap = taskLevelScoreMappingBean.getTaskLevelScoreRangeMap();

        responseType.setTaskLevelScoreMapping(taskLevelScoreRangeMap);
        responseType.setTaskLevelSwitch(taskLevelSwitch);
        return responseType;

    }


    @Override
    public GetTaskLevelDimResponseType getTaskLevelDim(GetTaskLevelDimRequestType request,
                                                       String empCode) throws Exception {


        GetTaskLevelDimResponseType response = new GetTaskLevelDimResponseType();

        String d = dataUpdateBiz.getTaskBoardUpdateTime();

        BusinessDashboardEmployeeInfo employeeInfo = TaskFlowHelper.getActualEmployeeInfo(empCode, d, remoteConfig, examineConfigV2Dao, employeeInfoDao);
        if(GeneralUtil.isEmpty(employeeInfo)) {
            response.setTaskDimInfoList(new ArrayList<>());
            return response;
        }

        Map<String, String> andMap = new HashMap<>();
        TaskFlowHelper.setAllSubordinateCondition(andMap, employeeInfo, remoteConfig, organizationInfoDao, employeeInfoDao);


        SqlParamterBean bean = TaskFlowHelper.getDimRequestSqlBean(andMap, d);
        GetRawDataRequestType dimRequest = bean.convertBeanToRequest(false);

        GetRawDataResponseType dimResponse = taskFlowSelect43556Helper.getRawDataBy43556(dimRequest);
        setTaskDimInfoList(dimResponse, response);


        return response;
    }


    private void setTaskDimInfoList(GetRawDataResponseType dimResponse,
                                    GetTaskLevelDimResponseType response) throws Exception {
        Map<String, Map<String, String>> dataMap = taskFlowSyncJob.getEumnDataTypeMap();
        Set<TaskDimInfo> taskDimInfoSet = new HashSet<>();
        Map<String, Set<TaskDimInfo>> taskDimInfoSetMap = new HashMap<>();
        List<List<Object>> objectList = MapperUtil.str2ListList(dimResponse.getResult(), Object.class);
        List<String> groupList = dimResponse.getGroupList();
        for (List<Object> rowList : objectList) {
            for (int i = 0; i < groupList.size(); i = i + 2) {
                String groupType = groupList.get(i);
                String code = String.valueOf(rowList.get(i));
                TaskDimInfo taskDimInfo = new TaskDimInfo();
                taskDimInfo.setCode(code);
                // taskDimInfo.setValue(String.valueOf(rowList.get(i + 1)));
                taskDimInfo.setValue(dataMap.get(groupType).get(code));
                taskDimInfo.setType(groupType);
                if (i == 0) {
                    //一级节点
                    taskDimInfoSet.add(taskDimInfo);
                } else {
                    //其他层级节点
                    String parentCode = String.valueOf(rowList.get(i - 2));
                    Set<TaskDimInfo> childrenSet = taskDimInfoSetMap.get(parentCode);
                    if (GeneralUtil.isEmpty(childrenSet)) {
                        childrenSet = new HashSet<>();
                    }
                    childrenSet.add(taskDimInfo);
                    taskDimInfoSetMap.put(parentCode, childrenSet);
                }
            }
        }
        Deque<TaskDimInfo> deque = new LinkedList<>(taskDimInfoSet);
        while (!deque.isEmpty()) {
            TaskDimInfo taskDimInfo = deque.pollFirst();
            Set<TaskDimInfo> childrenSet = taskDimInfoSetMap.get(taskDimInfo.getCode());
            if (GeneralUtil.isNotEmpty(childrenSet)) {
                deque.addAll(childrenSet);
                taskDimInfo.setChildrenList(Lists.newArrayList(childrenSet));
            } else{
                taskDimInfo.setChildrenList(new ArrayList<>());
            }
            taskDimInfoSetMap.remove(taskDimInfo.getCode());
        }
        response.setTaskDimInfoList(Lists.newArrayList(taskDimInfoSet));
    }



    @Override
    public GetTaskFlowTableDataResponseType getTaskFlowTableData(GetTaskFlowTableDataRequestType request,
                                                                 String empCode) throws Exception {

        String d = dataUpdateBiz.getTaskBoardUpdateTime();

        BusinessDashboardEmployeeInfo employeeInfo = TaskFlowHelper.getActualEmployeeInfo(empCode, d, remoteConfig, examineConfigV2Dao, employeeInfoDao);
        if(GeneralUtil.isEmpty(employeeInfo)) {
            GetTaskFlowTableDataResponseType defaultResponse = new GetTaskFlowTableDataResponseType();
            defaultResponse.setTableDataItemList(new ArrayList<>());
            defaultResponse.setTotalNum(0);
            defaultResponse.setAvearageData(0d);
            return defaultResponse;
        }


        String drillDownType = request.getDrillDownType();


        if("subordinate".equals(drillDownType)) {
            return getSubordinateTaskFlowTableData(request, employeeInfo);
        } else if("task".equals(drillDownType)){
            return getTaskTaskFlowTableData(request, employeeInfo);
        }

        return getRegionTaskFlowTableData(request);
    }

    @Override
    public GetTaskFlowTrendlineDataResponseType getTaskFlowTrendlineData(GetTaskFlowTrendlineDataRequestType requestType, String empCode) throws Exception {

        GetTaskFlowTrendlineDataResponseType responseType = new GetTaskFlowTrendlineDataResponseType();
        List<TrendLineDataItem> trendLineDataItemList = new ArrayList<>();
        responseType.setTrendLineDataItemList(trendLineDataItemList);

        String d = dataUpdateBiz.getTaskBoardUpdateTime();
        BusinessDashboardEmployeeInfo employeeInfo = TaskFlowHelper.getActualEmployeeInfo(empCode, d, remoteConfig, examineConfigV2Dao, employeeInfoDao);
        if(GeneralUtil.isEmpty(employeeInfo)) {
            return responseType;
        }

        Map<String, String> andMap = new HashMap<>();
        TaskFlowHelper.setAllSubordinateCondition(andMap, employeeInfo, remoteConfig, organizationInfoDao, employeeInfoDao);
        SqlParamterBean bean = TaskFlowHelper.getTrendLineBean(requestType, andMap, d);
        GetRawDataRequestType tableRequest = bean.convertBeanToRequest(true);

        GetRawDataResponseType trendLineResponse = taskFlowSelect43556Helper.getRawDataBy43556(tableRequest);
        TaskFlowHelper.processTrendLine(trendLineResponse,trendLineDataItemList,requestType);

        return responseType;
    }

    /**
     * 大区排行 & 按照大区下钻: 按照大区总数据进行计算
     * @param request
     * @return
     * @throws Exception
     */
    private GetTaskFlowTableDataResponseType getRegionTaskFlowTableData(GetTaskFlowTableDataRequestType request) throws Exception {
        GetTaskFlowTableDataResponseType response = new GetTaskFlowTableDataResponseType();
        List<TableDataItem> tableDataItemList = new ArrayList<>();
        response.setTableDataItemList(tableDataItemList);

        Map<String, String> regionManagerMap = MapperUtil.str2Obj(remoteConfig.getExternalConfig("regionalManager"), Map.class);
        Set<String> empCodeList = regionManagerMap.keySet();
        Map<String, Future<GetTaskFlowMetricCardDataResponseType>> futureMap = new HashMap<>();
        for (String empCode : empCodeList) {
            GetTaskFlowMetricCardDataRequestType metricCardRequest = new GetTaskFlowMetricCardDataRequestType();
            metricCardRequest.setDateType(request.getDateType());
            metricCardRequest.setStartDate(request.getStartDate());
            metricCardRequest.setEndDate(request.getEndDate());
            metricCardRequest.setStatisticalScope(request.getStatisticalScope());
            metricCardRequest.setTaskDimInfoList(request.getTaskDimInfoList());
            futureMap.put(empCode, taskFlowMetricCardBiz.getTaskFlowMetricCardData(metricCardRequest, empCode, false));
        }

        for (Map.Entry<String, Future<GetTaskFlowMetricCardDataResponseType>> entry : futureMap.entrySet()) {
            String empCode = entry.getKey();
            GetTaskFlowMetricCardDataResponseType metricCardResponse = entry.getValue().get();
            if ("noData".equals(metricCardResponse.getStatus())) {
                continue;
            }
            TableDataItem tableDataItem = new TableDataItem();
            Map<String, String> fieldMap = new HashMap<>();
            fieldMap.put("emp_code", empCode);
            tableDataItem.setFieldMap(fieldMap);
            tableDataItem.setDimMap(metricCardResponse.getDimData());
            tableDataItemList.add(tableDataItem);
        }
        TaskFlowHelper.sortTableDataItemList(tableDataItemList, request.getDrilldownMetric());
        TaskFlowHelper.makeUpTableRegionData(tableDataItemList, regionManagerMap);

        response.setTotalNum(tableDataItemList.size());
        response.setTableDataItemList(GeneralUtil.getSubList(tableDataItemList, request.getPageNo(), request.getPageSize()));
        setAvearageData(request, response);
        return response;
    }


    /**
     * 大区排行 & 按照大区下钻
     *  因没有把大区总数据算进去，所以该方法舍弃
     * @param request
     * @return
     * @throws Exception
     */
    private GetTaskFlowTableDataResponseType getRegionTaskFlowTableDataError(GetTaskFlowTableDataRequestType request) throws Exception {

        GetTaskFlowTableDataResponseType response = new GetTaskFlowTableDataResponseType();
        List<TableDataItem> tableDataItemList = new ArrayList<>();
        response.setTableDataItemList(tableDataItemList);

        String d = dataUpdateBiz.getTaskBoardUpdateTime();
        SqlParamterBean bean = TaskFlowHelper.getRegionTableBean(request, d, remoteConfig);

        GetRawDataRequestType tableRequest = bean.convertBeanToRequest(true);
        GetRawDataResponseType tableResponse = taskFlowSelect43556Helper.getRawDataBy43556(tableRequest);

        response.setTotalNum(tableResponse.getTotalNum());
        TaskFlowHelper.processTableData(tableResponse, tableDataItemList);

        setAvearageData(request, response);

        return response;
    }


    private void setAvearageData(GetTaskFlowTableDataRequestType request,
                                 GetTaskFlowTableDataResponseType response) throws Exception {
        String drillDownMetric = TaskFlowHelper.getActualMetric(request.getDrilldownMetric());
        //如果是平均处理时长，则需要返回基线  该基线为国内的leader的指标卡数据
        if ("average_process_time".equals(drillDownMetric)) {
            GetTaskFlowMetricCardDataRequestType metricCardRequest = new GetTaskFlowMetricCardDataRequestType();
            metricCardRequest.setDateType(request.getDateType());
            metricCardRequest.setStartDate(request.getStartDate());
            metricCardRequest.setEndDate(request.getEndDate());
            metricCardRequest.setStatisticalScope(request.getStatisticalScope());
            metricCardRequest.setTaskDimInfoList(request.getTaskDimInfoList());

            String empCode = TaskFlowHelper.getDomesticDeptLeader(remoteConfig, organizationInfoDao);

            GetTaskFlowMetricCardDataResponseType metricCardDataResponse = taskFlowMetricCardBiz.getTaskFlowMetricCardData(metricCardRequest, empCode, false).get();

            response.setAvearageData(metricCardDataResponse.getDimData().get("average_process_time"));
        }

    }


    private GetTaskFlowTableDataResponseType getTaskTaskFlowTableData(GetTaskFlowTableDataRequestType request,
                                                                      BusinessDashboardEmployeeInfo employeeInfo) throws Exception {

        GetTaskFlowTableDataResponseType response = new GetTaskFlowTableDataResponseType();
        List<TableDataItem> tableDataItemList = new ArrayList<>();
        response.setTableDataItemList(tableDataItemList);

        Map<String, String> andMap = new HashMap<>();
        TaskFlowHelper.setAllSubordinateCondition(andMap, employeeInfo, remoteConfig, organizationInfoDao, employeeInfoDao);
        String d = dataUpdateBiz.getTaskBoardUpdateTime();
        SqlParamterBean bean = TaskFlowHelper.getTaskTableBean(request, andMap, d);

        GetRawDataRequestType tableRequest = bean.convertBeanToRequest(true);
        GetRawDataResponseType tableResponse = taskFlowSelect43556Helper.getRawDataBy43556(tableRequest, request.getDrilldownMetric());

        response.setTotalNum(tableResponse.getTotalNum());
        TaskFlowHelper.processTableData(tableResponse, tableDataItemList);
        TaskFlowHelper.sortTableDataItemList(tableDataItemList, request.getDrilldownMetric());
        TaskFlowHelper.processGroupName(tableDataItemList, taskFlowSyncJob.getEumnDataTypeMap());
        TaskFlowHelper.makeUpTableData(tableDataItemList, tableResponse);

        return response;
    }



    private GetTaskFlowTableDataResponseType getSubordinateTaskFlowTableData(GetTaskFlowTableDataRequestType request,
                                                                             BusinessDashboardEmployeeInfo employeeInfo) throws Exception {
        String subordinateType = request.getSubordinateType();
        if("allSubordinate".equals(subordinateType)) {
            return getAllSubordinateTaskFlowTableData(request, employeeInfo);
        }
        return getDirectorSubordinateTaskFlowTableData(request, employeeInfo);
    }

    private GetTaskFlowTableDataResponseType getDirectorSubordinateTaskFlowTableData(GetTaskFlowTableDataRequestType request,
                                                                                     BusinessDashboardEmployeeInfo employeeInfo) throws Exception {

        GetTaskFlowTableDataResponseType response = new GetTaskFlowTableDataResponseType();
        List<TableDataItem> tableDataItemList = new ArrayList<>();
        response.setTableDataItemList(tableDataItemList);

        List<String> empCodeList = TaskFlowHelper.getDirectSubordinateEmpCodeList(employeeInfo, remoteConfig, organizationInfoDao, employeeInfoDao);
        Map<String, Future<GetTaskFlowMetricCardDataResponseType>> futureMap = new HashMap<>();
        for (String empCode : empCodeList) {
            GetTaskFlowMetricCardDataRequestType metricCardRequest = new GetTaskFlowMetricCardDataRequestType();
            metricCardRequest.setDateType(request.getDateType());
            metricCardRequest.setStartDate(request.getStartDate());
            metricCardRequest.setEndDate(request.getEndDate());
            metricCardRequest.setStatisticalScope(request.getStatisticalScope());
            metricCardRequest.setTaskDimInfoList(request.getTaskDimInfoList());
            metricCardRequest.setTaskLevelScoreRange(request.getTaskLevelScoreRange());
            futureMap.put(empCode, taskFlowMetricCardBiz.getTaskFlowMetricCardData(metricCardRequest, empCode, false));
        }

        for (Map.Entry<String, Future<GetTaskFlowMetricCardDataResponseType>> entry : futureMap.entrySet()) {
            String empCode = entry.getKey();
            GetTaskFlowMetricCardDataResponseType metricCardResponse = entry.getValue().get();
            if ("noData".equals(metricCardResponse.getStatus())) {
                continue;
            }
            TableDataItem tableDataItem = new TableDataItem();
            Map<String, String> fieldMap = new HashMap<>();
            fieldMap.put("emp_code", empCode);
            tableDataItem.setFieldMap(fieldMap);
            tableDataItem.setDimMap(metricCardResponse.getDimData());
            tableDataItemList.add(tableDataItem);

        }
        TaskFlowHelper.sortTableDataItemList(tableDataItemList, request.getDrilldownMetric());
        TaskFlowHelper.makeUpTableData(tableDataItemList, employeeInfoDao);

        response.setTotalNum(tableDataItemList.size());
        response.setTableDataItemList(GeneralUtil.getSubList(tableDataItemList, request.getPageNo(), request.getPageSize()));

        return response;

    }





    private GetTaskFlowTableDataResponseType getAllSubordinateTaskFlowTableData(GetTaskFlowTableDataRequestType request,
                                                                                BusinessDashboardEmployeeInfo employeeInfo) throws Exception {
        GetTaskFlowTableDataResponseType response = new GetTaskFlowTableDataResponseType();
        List<TableDataItem> tableDataItemList = new ArrayList<>();
        response.setTableDataItemList(tableDataItemList);

        Map<String, String> andMap = new HashMap<>();
        TaskFlowHelper.setAllSubordinateCondition(andMap, employeeInfo, remoteConfig, organizationInfoDao, employeeInfoDao);
        String d = dataUpdateBiz.getTaskBoardUpdateTime();
        SqlParamterBean bean = TaskFlowHelper.getAllSubordinateTableBean(request, andMap, d);

        GetRawDataRequestType tableRequest = bean.convertBeanToRequest(true);
        GetRawDataResponseType tableResponse = taskFlowSelect43556Helper.getRawDataBy43556(tableRequest, request.getDrilldownMetric());

        response.setTotalNum(tableResponse.getTotalNum());
        TaskFlowHelper.processTableData(tableResponse, tableDataItemList);

        TaskFlowHelper.sortTableDataItemList(tableDataItemList, request.getDrilldownMetric());

        return response;
    }



}
