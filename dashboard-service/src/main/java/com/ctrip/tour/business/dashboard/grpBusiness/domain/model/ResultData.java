package com.ctrip.tour.business.dashboard.grpBusiness.domain.model;

import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


@Getter
@Setter
public class ResultData {
    List<Map<String, Object>> data = new ArrayList<>();
    List<ResultMeta> meta = new ArrayList<>();


    public void addMeta(Indicator v) {
        ResultMeta resultMeta = new ResultMeta();
        resultMeta.setIndicatorKey(v.getIndicator_key());
        resultMeta.setIndicatorKeyName(v.getIndicator_key_name());
        resultMeta.setIndicatorType(v.getIndicator_type());
        resultMeta.setIndicatorFormat(v.getIndicator_format());
        resultMeta.setIndicatorName(v.getIndicator_name());
        resultMeta.setIndicatorNameCN(v.getIndicator_name_cn());
        resultMeta.setIndicatorIsDimension(v.getIndicator_is_dimension());
        resultMeta.setIndicatorIsSupportSorting(v.getIndicator_is_support_sorting());

        this.meta.add(resultMeta);
    }
}
