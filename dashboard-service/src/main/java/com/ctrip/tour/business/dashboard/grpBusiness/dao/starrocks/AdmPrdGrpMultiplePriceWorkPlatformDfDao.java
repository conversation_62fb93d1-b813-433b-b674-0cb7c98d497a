package com.ctrip.tour.business.dashboard.grpBusiness.dao.starrocks;


import com.ctrip.soa._27181.PreparedParameterBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Repository
public class AdmPrdGrpMultiplePriceWorkPlatformDfDao {

    @Autowired
    StarRocksDao starRocksDao;

    public List<Map<String, Object>> query(String sql, Map<String, ?> map) throws SQLException {

        List<PreparedParameterBean> parameters = new ArrayList<>();  //todo


        return starRocksDao.getListResult(sql, parameters);
    }


}
