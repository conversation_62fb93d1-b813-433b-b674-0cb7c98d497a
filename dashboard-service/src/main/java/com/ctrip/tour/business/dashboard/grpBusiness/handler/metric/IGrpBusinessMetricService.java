package com.ctrip.tour.business.dashboard.grpBusiness.handler.metric;

import java.util.List;
import java.util.Map;

import com.ctrip.soa._24922.GetGrpMetricDataRequestType;
import com.ctrip.soa._24922.GetGrpMetricDataRequestType;
import com.ctrip.soa._24922.GetGrpMetricDataRequestType;
import com.ctrip.soa._24922.GetGrpMetricDataRequestType;
import com.ctrip.soa._24922.GrpMetric;
import com.ctrip.soa._24922.GrpTrendLinePoint;
import com.ctrip.tour.business.dashboard.grpBusiness.enums.MetricEnum;

/**
 * <AUTHOR>
 * @Date 2024/12/12
 */
public interface IGrpBusinessMetricService {

    GrpMetric queryMetricValue(GetGrpMetricDataRequestType requestType, MetricEnum metricEnum, IGrpBusinessMetricService service,int bizModel);

    List<GrpTrendLinePoint> queryMetricTrendLineP(GetGrpMetricDataRequestType requestType, MetricEnum metricEnum, IGrpBusinessMetricService service, int bizModel);

    Map<String, GrpMetric> queryMetricDillDownResult(GetGrpMetricDataRequestType requestType, MetricEnum metricEnum, int bizModel, IGrpBusinessMetricService service);
}
