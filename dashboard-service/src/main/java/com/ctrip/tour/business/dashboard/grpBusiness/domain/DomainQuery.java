package com.ctrip.tour.business.dashboard.grpBusiness.domain;

import com.ctrip.soa._24922.CompareConfig;
import com.ctrip.soa._24922.DSLRequestType;
import com.ctrip.soa._24922.EnumOperators;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.dsl.DSLUtils;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.model.DomainConfig;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.model.ResultData;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.plan.DefaultDateCompareFunction;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.query.AbstractDataQuery;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.plan.DefaultReduceFunction;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.plan.LogicQueryPlan;
import lombok.extern.slf4j.Slf4j;
import org.yaml.snakeyaml.representer.Representer;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
public class DomainQuery {
    // 模型配置
    private DomainConfig domainConfig;
    private static Map<String, AbstractDataQuery> abstractDataQueryMap;

    private static final Representer representer = new Representer();

    static {
        representer.getPropertyUtils().setSkipMissingProperties(true);
    }

    public static DomainQuery withDomainConfig(DomainConfig domainConfig) {
        DomainQuery domainQuery = new DomainQuery();
        domainQuery.domainConfig = domainConfig;
        return domainQuery;
    }

    public static void RegDataSource(String key, AbstractDataQuery abstractOLAPQuery) {
        if (abstractDataQueryMap == null) {
            DomainQuery.abstractDataQueryMap = new ConcurrentHashMap<>();
        }
        DomainQuery.abstractDataQueryMap.put(key, abstractOLAPQuery);
    }

    // 生成逻辑执行计划
    private List<LogicQueryPlan> explainLogicDSL(DSLRequestType dsl) {
        List<LogicQueryPlan> logicPlans = new ArrayList<>();
        // dsl 数据模型拆解
        DSLRequestType dslCp = DSLUtils.copyDSL(dsl);
        LogicQueryPlan logicPlan = new LogicQueryPlan(dslCp, domainConfig, abstractDataQueryMap)
                .withReduceFunction(new DefaultReduceFunction());
        logicPlans.add(logicPlan);
        // 时间环比
        if (dsl.getCompareConfig() != null) {
            for (CompareConfig compareConfig : dsl.getCompareConfig()) {
                DSLRequestType curDsl = DSLUtils.copyDSL(dsl);
                // 环比,替换参数
                DSLUtils.replaceCondition(curDsl.getWhereCondition(), DSLUtils.initWhereCondition("date", EnumOperators.IN, new ArrayList<>(compareConfig.getCompareDate())));
                LogicQueryPlan logicPlanCompare = new LogicQueryPlan(curDsl, domainConfig, abstractDataQueryMap)
                        .withReduceFunction(new DefaultReduceFunction());
                logicPlans.add(logicPlanCompare);
            }
        }
        return logicPlans;
    }

    // dsl查询
    public ResultData queryWithDSL(DSLRequestType dsl) {
        // dsl 前置处理
        AbstractPreDSLProcess.EarlyReturn earlyReturn = new AbstractPreDSLProcess.EarlyReturn();
        earlyReturn.status = 0;
        if (dslProcesses != null) {
            for (AbstractPreDSLProcess dslProcess : dslProcesses) {
                dsl = dslProcess.process(dsl, earlyReturn);
                if (earlyReturn.status != 0) {
                    return earlyReturn.defaultResultData;
                }
                if (dsl.getExtra() != null && dsl.getExtra().containsKey("domain_config")) {
                    String key = dsl.getExtra().remove("domain_config");
                    domainConfig = DomainConfig.getDomainConfigByDomainID(key);
                }
            }
        }

        // dsl 逻辑执行计划
        List<LogicQueryPlan> logicPlans = explainLogicDSL(dsl);

        // 执行物理计划
        logicPlans.parallelStream().forEach(LogicQueryPlan::doLogicPlan);

        // 数据编排，同环比、union、join等等数据编排
        LogicQueryPlan rootData = logicPlans.get(0);
        if (dsl.getCompareConfig() != null && !dsl.getCompareConfig().isEmpty()) {
            for (int i = 1; i < logicPlans.size(); i++) {
                AbstractOrchestrationFunction defaultCompareFunction = new DefaultDateCompareFunction(dsl.getCompareConfig().get(i - 1), dsl.getGroupBy());
                defaultCompareFunction.compare(logicPlans.get(0), logicPlans.get(i));
            }
        }
        ResultData reduceQueryDataResult = rootData.getReduceQueryDataResult();

        // 数据后置处理
        if (dataProcesses != null) {
            for (AbstractAfterDataProcess dataProcess : dataProcesses) {
                reduceQueryDataResult = dataProcess.process(dsl, reduceQueryDataResult);
            }
        }
        return reduceQueryDataResult;
    }

    // dsl前置处理
    private List<AbstractPreDSLProcess> dslProcesses;

    public DomainQuery withDSLProcess(List<AbstractPreDSLProcess> dslProcess) {
        if (dslProcess == null) {
            return this;
        }
        if (dslProcesses == null) {
            this.dslProcesses = new ArrayList<>();
        }
        dslProcesses.addAll(dslProcess);
        return this;
    }


    // 数据后置处理
    private List<AbstractAfterDataProcess> dataProcesses;

    public DomainQuery withDataProcess(List<AbstractAfterDataProcess> dataProcess) {
        if (dataProcess == null) {
            return this;
        }
        if (dataProcesses == null) {
            this.dataProcesses = new ArrayList<>();
        }
        dataProcesses.addAll(dataProcess);
        return this;
    }
}
