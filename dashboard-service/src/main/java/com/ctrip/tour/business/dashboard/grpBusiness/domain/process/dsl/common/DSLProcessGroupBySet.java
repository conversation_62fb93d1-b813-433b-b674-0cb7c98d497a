package com.ctrip.tour.business.dashboard.grpBusiness.domain.process.dsl.common;

import com.ctrip.soa._24922.DSLRequestType;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.AbstractPreDSLProcess;

import java.util.List;

public class DSLProcessGroupBySet extends AbstractPreDSLProcess {
    private List<String> groupBys;
    private Integer size;

    public static AbstractPreDSLProcess getInstance(List<String> groupBys) {
        DSLProcessGroupBySet dslProcessLimitSet = new DSLProcessGroupBySet();
        dslProcessLimitSet.groupBys = groupBys;
        return dslProcessLimitSet;
    }

    @Override
    public DSLRequestType process(DSLRequestType dslRequestType, EarlyReturn earlyReturn) {
        if(this.groupBys!=null && !this.groupBys.isEmpty()){
            dslRequestType.setGroupBy(this.groupBys);
        }
        return dslRequestType;
    }
}
