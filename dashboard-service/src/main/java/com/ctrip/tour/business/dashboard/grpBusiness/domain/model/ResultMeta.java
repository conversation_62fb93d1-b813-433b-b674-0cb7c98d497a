package com.ctrip.tour.business.dashboard.grpBusiness.domain.model;

import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

@Getter
@Setter
public class ResultMeta {
    String indicatorName;
    String indicatorNameCN;
    String indicatorType;
    String indicatorFormat;
    boolean indicatorIsSupportSorting;
    boolean indicatorIsDimension;
    String indicatorKey;
    String indicatorKeyName;


    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true; // 地址相等
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false; // 对象为空或类不同
        }
        ResultMeta other = (ResultMeta) obj;
        return Objects.equals(indicatorName, other.indicatorName);
    }

    @Override
    public int hashCode() {
        int result = 17;
        return 31 * result + (indicatorName != null ? indicatorName.hashCode() : 0);
    }
}
