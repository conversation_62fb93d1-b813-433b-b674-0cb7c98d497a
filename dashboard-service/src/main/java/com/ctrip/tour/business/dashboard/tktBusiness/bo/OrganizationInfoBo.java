package com.ctrip.tour.business.dashboard.tktBusiness.bo;

import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardOrganizationInfo;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/7/25
 */
public class OrganizationInfoBo {

    public List<String> getChildOrgIdList(List<BusinessDashboardOrganizationInfo> organizationInfoList){
        List<String> organizationIdList = organizationInfoList.stream()
                .map(BusinessDashboardOrganizationInfo::getNodeOrgId)
                .collect(Collectors.toList());
        return organizationIdList;
    }

    public Set<String> getOrgIdSet(List<BusinessDashboardOrganizationInfo> organizationInfoList) {
        Set<String> resultSet = new HashSet<>();
        for (BusinessDashboardOrganizationInfo info : organizationInfoList) {
            String parentOrgId = info.getParentOrgId();
            resultSet.add(parentOrgId);
        }
        return resultSet;
    }
}
