package com.ctrip.tour.business.dashboard.grpBusiness.enums;

import java.util.Map;

import com.ctrip.tour.business.dashboard.grpBusiness.common.GrpConstant;
import com.google.common.collect.ImmutableMap;

/**
 * <AUTHOR>
 * @Date 2024/12/18
 */
public enum DiyMetricDimEnum {

    INCOME(GrpConstant.DIY_INCOME, ImmutableMap.of("PREORDER_DATE", "order_date", "TRIP_DATE","tour_depart_date"),"adm_ord_cus_work_platform_prdt_df"),
    DIY_PROFIT(GrpConstant.DIY_PROFIT, ImmutableMap.of("PREORDER_DATE", "order_date", "TRIP_DATE","tour_depart_date"),"adm_ord_cus_work_platform_prdt_df"),
    DIY_INCOME_ACHIEVE_RATE(GrpConstant.DIY_INCOME_ACHIEVE_RATE, ImmutableMap.of("PREORDER_DATE", "order_date", "TRIP_DATE","tour_depart_date"),"adm_ord_cus_work_platform_prdt_df"),
    DIY_PROFIT_ACHIEVE_RATE(GrpConstant.DIY_PROFIT_ACHIEVE_RATE, ImmutableMap.of("PREORDER_DATE", "order_date", "TRIP_DATE","tour_depart_date"),"adm_ord_cus_work_platform_prdt_df"),
    AVA_ORDER_CAP(GrpConstant.AVA_ORDER_CAP, ImmutableMap.of("PREORDER_DATE", "order_date"),"adm_ord_cus_work_platform_prdt_prvdr_df"),
    CPR_COMPLETED_ORDERS_RATE(GrpConstant.CPR_COMPLETED_ORDERS_RATE, ImmutableMap.of("PREORDER_DATE", "order_date"),"adm_ord_cus_work_platform_prdt_prvdr_df"),
    COMPLETED_ORDERS(GrpConstant.COMPLETED_ORDERS, ImmutableMap.of("PREORDER_DATE", "order_date"),"adm_ord_cus_work_platform_prdt_prvdr_df"),
    COMPLETED_ORDERS_RATE(GrpConstant.COMPLETED_ORDERS_RATE, ImmutableMap.of("PREORDER_DATE", "order_date"),"adm_ord_cus_work_platform_prdt_prvdr_df"),
    VENDOR_ORD_ACCEPT_RATE(GrpConstant.VENDOR_ORD_ACCEPT_RATE, ImmutableMap.of("PREORDER_DATE", "order_date"),"adm_ord_cus_work_platform_prdt_prvdr_df"),
    DIY_DRIVER_ACTUAL_DISPACTH_RATE(GrpConstant.DIY_DRIVER_ACTUAL_DISPACTH_RATE, ImmutableMap.of("RETURN_DATE", "dep_date"),"adm_sev_cus_metrics_tag_to_workbench_df"),
    DIY_FITTINGNPS(GrpConstant.DIY_FITTINGNPS, ImmutableMap.of("RETURN_DATE", "dep_date"),"adm_sev_cus_metrics_tag_to_workbench_df"),
    DIY_DRIVER_DISPATCH_RATE(GrpConstant.DIY_DRIVER_DISPATCH_RATE, ImmutableMap.of("RETURN_DATE", "dep_date"),"adm_sev_cus_metrics_tag_to_workbench_df"),
    DIY_DRIVER_EXECUTION_RATE(GrpConstant.DIY_DRIVER_EXECUTION_RATE, ImmutableMap.of("RETURN_DATE", "dep_date"),"adm_sev_cus_metrics_tag_to_workbench_df"),
    DIY_GUIDER_DISPATCH_RATE(GrpConstant.DIY_GUIDER_DISPATCH_RATE, ImmutableMap.of("RETURN_DATE", "dep_date"),"adm_sev_cus_metrics_tag_to_workbench_df"),
    DIY_RECOMMENDATION_RATE(GrpConstant.DIY_RECOMMENDATION_RATE, ImmutableMap.of("RETURN_DATE", "tour_enddate"),"adm_sev_cus_metrics_tag_to_workbench_df"),
    DIY_SLANDER_RATE(GrpConstant.DIY_SLANDER_RATE, ImmutableMap.of("RETURN_DATE", "tour_enddate"),"adm_sev_cus_metrics_tag_to_workbench_df"),
    TIMELY_RESPONSE_RATE(GrpConstant.TIMELY_RESPONSE_RATE, ImmutableMap.of("RETURN_DATE", "tour_enddate"),"adm_sev_cus_metrics_tag_to_workbench_df"),
    AVG_TRANSLATION_REVIEW_SCORE(GrpConstant.AVG_TRANSLATION_REVIEW_SCORE, ImmutableMap.of("RETURN_DATE", "tour_enddate"),"adm_sev_cus_metrics_tag_to_workbench_df"),
    DISP_FAIL_RATE(GrpConstant.DISP_FAIL_RATE, ImmutableMap.of("RETURN_DATE", "tour_enddate"),"adm_sev_cus_metrics_tag_to_workbench_df"),
    ORD_AVG_PRICE(GrpConstant.ORD_AVG_PRICE, ImmutableMap.of("PREORDER_DATE", "order_date"),"adm_ord_cus_work_platform_prdt_df"),
    ;

    private String metricName;
    private Map<String, String> calcDateNameMap;
    private String tableName;

    public static DiyMetricDimEnum getMetricDimEnumByMetricName(String metricName) {
        for (DiyMetricDimEnum metricDimEnum : DiyMetricDimEnum.values()) {
            if (metricDimEnum.getMetricName().equals(metricName)) {
                return metricDimEnum;
            }
        }
        return null;
    }


    DiyMetricDimEnum(String metricName, Map<String, String> calcDateNameMap, String tableName) {
        this.metricName = metricName;
        this.calcDateNameMap = calcDateNameMap;
        this.tableName = tableName;
    }

    public String getMetricName() {
        return metricName;
    }

    public void setMetricName(String metricName) {
        this.metricName = metricName;
    }

    public Map<String, String> getCalcDateNameMap() {
        return calcDateNameMap;
    }

    public void setCalcDateNameMap(Map<String, String> calcDateNameMap) {
        this.calcDateNameMap = calcDateNameMap;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }
}
