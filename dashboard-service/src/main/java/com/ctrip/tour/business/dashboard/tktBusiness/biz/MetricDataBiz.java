package com.ctrip.tour.business.dashboard.tktBusiness.biz;

import com.ctrip.soa._24922.*;

/**
 * <AUTHOR>
 * @date 2022/7/28
 */
public interface MetricDataBiz {

    GetMetricCardDataResponseType getMetricCardData(GetMetricCardDataRequestType getMetricCardDataRequestType) throws Exception;

    GetTrendLineDataResponseType getTrendLineData(GetTrendLineDataRequestType getTrendLineDataRequestType) throws Exception;

    GetTableDataResponseType getTableData(GetTableDataRequestType getTableDataRequestType) throws Exception;

    GetDrillDownBaseInfoResponseType getDrillDownBaseInfo(GetDrillDownBaseInfoRequestType getDrillDownBaseInfoRequestType) throws Exception;

    GetDomesticDrillDownBaseInfoResponseType getDomesticDrillDownBaseInfo(GetDomesticDrillDownBaseInfoRequestType request) throws Exception ;

    GetDomesticTableDataResponseType getDomesticTableData(GetDomesticTableDataRequestType getDomesticTableDataRequestType) throws Exception;

    GetFirstPageDomesticMetricCardDrillDataResponseType getFirstPageDomesticMetricCardDrillData(GetFirstPageDomesticMetricCardDrillDataRequestType getFirstPageDomesticMetricCardDrillDataRequestType) throws Exception;

    GetDomesticMetricTrendDataResponseType getDomesticMetricTrendData(GetDomesticMetricTrendDataRequestType getDomesticMetricTrendDataRequestType) throws Exception;

    GetDomesticMetricCardDataResponseType getDomesticMetricCardData(GetDomesticMetricCardDataRequestType getDomesticMetricCardDataRequestType) throws Exception;

    GetDomesticMetricSummaryDataResponseType getDomesticMetricSummaryData(GetDomesticMetricSummaryDataRequestType getDomesticMetricSummaryDataRequestType) throws Exception;

    GetFirstPageDomesticMetricCardDataResponseType getFirstPageDomesticMetricCardData(GetFirstPageDomesticMetricCardDataRequestType getFirstPageDomesticMetricCardDataRequestType) throws Exception;
}
