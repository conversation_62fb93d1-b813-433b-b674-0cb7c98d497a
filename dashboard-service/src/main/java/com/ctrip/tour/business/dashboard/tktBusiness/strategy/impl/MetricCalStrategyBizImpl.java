package com.ctrip.tour.business.dashboard.tktBusiness.strategy.impl;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.StrategyException;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.MetricCalStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;


import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;

/**
 * 策略的路由类
 * <AUTHOR>
 * @date 2022/7/29
 */
@Service
@Slf4j
public class MetricCalStrategyBizImpl implements ApplicationContextAware {

    private ApplicationContext applicationContext;

    private ConcurrentHashMap<String, MetricCalStrategy> metricCalStrategyMap = new ConcurrentHashMap<>();

    public Future<MetricDetailInfo> getSingleMetricCardData(String domainName,
                                                            TimeFilter timeFilter,
                                                            MetricInfoBean metricInfoBean,
                                                            String d,
                                                            Boolean needRank) throws Exception {
        String metric = metricInfoBean.getMetric();
        MetricCalStrategy metricCalStrategy = metricCalStrategyMap.get(metric);
        if (metricCalStrategy == null) {
            throw new StrategyException("未找到对应策略!");
        }
        return metricCalStrategy.getSingleMetricCardData(domainName, timeFilter, metricInfoBean, d, needRank);
    }

    public GetTrendLineDataResponseType getSingleTrendlineData(GetTrendLineDataRequestType request,
                                                               MetricInfoBean metricInfoBean,
                                                               String d) throws Exception {
        String metric = request.getMetric();
        MetricCalStrategy metricCalStrategy = metricCalStrategyMap.get(metric);
        if (metricCalStrategy == null) {
            throw new StrategyException("未找到对应策略!");
        }
        return metricCalStrategy.getSingleTrendlineData(request, metricInfoBean, d);
    }

    public GetTableDataResponseType getSingleTableData(GetTableDataRequestType request,
                                                       MetricInfoBean metricInfoBean,
                                                       String d) throws Exception {
        String metric = request.getMetric();
        MetricCalStrategy metricCalStrategy = metricCalStrategyMap.get(metric);
        if (metricCalStrategy == null) {
            throw new StrategyException("未找到对应策略!");
        }
        return metricCalStrategy.getSingleTableData(request, metricInfoBean, d);
    }

    public GetDrillDownBaseInfoResponseType getSingleDrillDownBaseInfo(GetDrillDownBaseInfoRequestType request,
                                                                       MetricInfoBean metricInfoBean,
                                                                       String d) throws Exception {
        String metric = request.getMetric();
        MetricCalStrategy metricCalStrategy = metricCalStrategyMap.get(metric);
        if (metricCalStrategy == null) {
            throw new StrategyException("未找到对应策略!");
        }
        return metricCalStrategy.getSingleDrillDownBaseInfo(request, metricInfoBean, d);
    }



    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
        Map<String, MetricCalStrategy> strategyMap = applicationContext.getBeansOfType(MetricCalStrategy.class);
        strategyMap.values().forEach(e -> metricCalStrategyMap.put(e.getMetricName(), e));
    }
}
