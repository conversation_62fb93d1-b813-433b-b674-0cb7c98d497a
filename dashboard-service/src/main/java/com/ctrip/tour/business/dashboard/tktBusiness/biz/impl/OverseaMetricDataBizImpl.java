package com.ctrip.tour.business.dashboard.tktBusiness.biz.impl;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.sightArchives.dao.overseametric.DimOrdTtdTargetConfigDao;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.DimOrdTtdTargetBO;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBeanV2;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoWithMetricBean;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.DataUpdateBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.OverseaMetricDataBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.bo.OverSeaExamineConfigBO;
import com.ctrip.tour.business.dashboard.tktBusiness.checker.UserChecker;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardOverseaExamineeConfigDao;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardOverseaExamineeConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.enums.BuTypeEnum;
import com.ctrip.tour.business.dashboard.tktBusiness.enums.TrendDrillTypeEnum;
import com.ctrip.tour.business.dashboard.tktBusiness.enums.TrendLineNameEnum;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.OverseaMetricHelper;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.overseaImpl.OverseaMetricCalStrategyBizImpl;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.overseaImplV2.OverseaMetricCalStrategyBizImplV2;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.UserUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

@Service
public class OverseaMetricDataBizImpl implements OverseaMetricDataBiz {


    @Autowired
    private OverseaMetricCalStrategyBizImpl overseaMetricCalStrategyBiz;

    @Autowired
    private OverseaMetricCalStrategyBizImplV2 overseaMetricCalStrategyBizV2;

    @Autowired
    private DataUpdateBiz dataUpdateBiz;

    @Autowired
    private UserChecker userChecker;

    @Autowired
    private BusinessDashboardOverseaExamineeConfigDao overseaConfigDao;

    @Autowired
    private RemoteConfig remoteConfig;

    @Autowired
    private DimOrdTtdTargetConfigDao dimOrdTtdTargetConfigDao;


    @Override
    public GetOverseaMetricCardDataResponseType getOverseaMetricCardData(GetOverseaMetricCardDataRequestType request) throws Exception {

        MDC.put("metric", "0");
        String domainName = request.getDomainName();
        //快速校验传入的domainName是否存在越权问题
        userChecker.fastCheckInputDomainName(domainName);


        GetOverseaMetricCardDataResponseType response = new GetOverseaMetricCardDataResponseType();
        List<MetricDetailInfo> metricDetailInfoList = new ArrayList<>();
        response.setMetricDetailInfoList(metricDetailInfoList);

        String d = dataUpdateBiz.getUpdateTime();
        TimeFilter timeFilter = request.getTimeFilter();
        String year = timeFilter.getYear();
        String dateType = timeFilter.getDateType();
        //对于按半年考核的人  他们两个季度配置的考核数据是一样的  所以任取其一就行了
        String quarter = "quarter".equals(dateType) ? timeFilter.getQuarter() : DateUtil.getFirstQuarterOfHalf(timeFilter.getHalf());

        //获取应该展示的指标列表
        List<BusinessDashboardOverseaExamineeConfig> examineeConfigList = overseaConfigDao.querySpecificPeriodAllMetricConfig(domainName, d, year, quarter);
        OverSeaExamineConfigBO bo = new OverSeaExamineConfigBO();
        List<OverseaMetricInfoBean> metricInfoBeanList = bo.getMetricInfoBeanList(examineeConfigList, remoteConfig);
        //指标卡的基础配置信息
        Map<String, AvailableSubMetric> metricCardConfigMap = bo.getMetricCardConfigMap(examineeConfigList,remoteConfig);

        List<Future<List<MetricDetailInfo>>> futureList = new ArrayList<>();
        for (OverseaMetricInfoBean bean : metricInfoBeanList) {
            futureList.add(overseaMetricCalStrategyBiz.getOverseaSingleMetricCardData(timeFilter, bean, d, metricCardConfigMap));
        }

        for (Future<List<MetricDetailInfo>> futureResult : futureList) {
            metricDetailInfoList.addAll(futureResult.get());
        }
        return response;
    }

    @Override
    public GetOverseaMetricCardDataV2ResponseType getOverseaMetricCardDataV2(GetOverseaMetricCardDataV2RequestType request) throws Exception {
        MDC.put("metric", "0");
        String domainName = request.getDomainName();
        //快速校验传入的domainName是否存在越权问题
        userChecker.fastCheckInputDomainName(domainName);

        GetOverseaMetricCardDataV2ResponseType response = new GetOverseaMetricCardDataV2ResponseType();
        List<OveaseaMetric> metricDetailInfoList = new ArrayList<>();
        response.setMetricList(metricDetailInfoList);

        String d = dataUpdateBiz.getUpdateTime();
//        String d = LocalDate.now().toString();
        request.setTimeFilter(DateUtil.getRealTimeFilter(request.getTimeFilter(),d));
        TimeFilter timeFilter = request.getTimeFilter();
        String year = timeFilter.getYear();
        String dateType = timeFilter.getDateType();

        List<String> quarters = "quarter".equals(dateType) ? Collections.singletonList(timeFilter.getQuarter()) : DateUtil.getQuarterOfHalf(timeFilter.getHalf());
        //查询考核人员配置信息表
        List<OverseaMetricInfoWithMetricBean> overseaMetricInfoWithMetricBeanList = dimOrdTtdTargetConfigDao.queryOverseaPersonInfo(quarters, year, null, null, domainName,null, d);
        OverSeaExamineConfigBO bo = new OverSeaExamineConfigBO();
        //获取考核人员配置表信息（指标维度聚合）
        List<OverseaMetricInfoBeanV2> metricInfoBeanList = bo.convertToMetricInfoBeanV2(overseaMetricInfoWithMetricBeanList);
        //仪表盘海外提取基础配置数据(子指枚举值)
        Map<String, AvailableSubMetric> metricCardConfigMap = bo.getMetricCardConfigMapV2(metricInfoBeanList);

        List<Future<OveaseaMetric>> futureList = new ArrayList<>();
        for (OverseaMetricInfoBeanV2 bean : metricInfoBeanList) {
            futureList.add(overseaMetricCalStrategyBizV2.getOverseaSingleMetricCardData(timeFilter, bean, d, metricCardConfigMap, request));
        }

        for (Future<OveaseaMetric> futureResult : futureList) {
            metricDetailInfoList.add(futureResult.get());
        }
        // 过滤为空的列表
        metricDetailInfoList.removeIf(oveaseaMetric -> CollectionUtils.isEmpty(oveaseaMetric.getSubMetricList()));
        // 排序
        OverseaMetricHelper.sortMetricWithFixedOrder(metricDetailInfoList);
        return response;
    }

    @Override
    public GetOverseaTrendLineDataResponseType getOverseaTrendLineData(GetOverseaTrendLineDataRequestType request) throws Exception {
        String domainName = request.getDomainName();
        //快速校验传入的domainName是否存在越权问题
        userChecker.fastCheckInputDomainName(domainName);
        String metric = request.getMetric();
        String d = dataUpdateBiz.getUpdateTime();
        MDC.put("metric", metric);
        //设置正确的时间
        request.setTimeFilter(OverseaMetricHelper.getActualTimeFilter(request.getTimeFilter(), d));

        return overseaMetricCalStrategyBiz.getOverseaSingleTrendLineData(request, d);
    }

    @Override
    public GetOverseaTrendLineDataV2ResponseType getOverseaTrendLineDataV2(GetOverseaTrendLineDataV2RequestType request) throws Exception {
        String domainName = request.getDomainName();
        //快速校验传入的domainName是否存在越权问题
        userChecker.fastCheckInputDomainName(domainName);
        String metric = request.getMetric();
        String d = dataUpdateBiz.getUpdateTime();
//        String d = LocalDate.now().toString();
        request.setTimeFilter(DateUtil.getRealTimeFilter(request.getTimeFilter(),d));
        MDC.put("metric", metric);
        if (TrendDrillTypeEnum.DRILL_DOWN.getName().equals(request.getQueryType()) && CollectionUtils.isEmpty(request.getDimValueList())) {
            GetOverseaDrillDownBaseInfoV2RequestType drillDownBaseInfoRequest = OverseaMetricHelper.getOverseaDrillDownBaseInfoV2RequestType(request);
            GetOverseaDrillDownBaseInfoV2ResponseType response = getOverseaDrillDownBaseInfoV2(drillDownBaseInfoRequest);
            request.setDimValueList(OverseaMetricHelper.setDimValues(response, request.getDimName(), metric));
        }
        //设置正确的时间
        request.setTimeFilter(OverseaMetricHelper.getActualTimeFilterWithQuarter(request.getTimeFilter(),d));

        return overseaMetricCalStrategyBizV2.getOverseaSingleTrendLineData(request, d);
    }

    @Override
    public GetOverseaDrillDownBaseInfoResponseType getOverseaDrillDownBaseInfo(GetOverseaDrillDownBaseInfoRequestType request) throws Exception {

        String domainName = request.getDomainName();
        //快速校验传入的domainName是否存在越权问题
        userChecker.fastCheckInputDomainName(domainName);

        String metric = request.getMetric();
        String d = dataUpdateBiz.getUpdateTime();
        MDC.put("metric", metric);

        TimeFilter timeFilter = request.getTimeFilter();
        String year = timeFilter.getYear();
        String dateType = timeFilter.getDateType();
        //对于按半年考核的人  他们两个季度配置的考核数据是一样的  所以任取其一就行了
        String quarter = "quarter".equals(dateType) ? timeFilter.getQuarter() : DateUtil.getFirstQuarterOfHalf(timeFilter.getHalf());

        //获取应该展示的指标列表
        List<BusinessDashboardOverseaExamineeConfig> examineeConfigList = overseaConfigDao.querySpecificPeriodAllMetricConfig(domainName, d, year, quarter);
        OverSeaExamineConfigBO bo = new OverSeaExamineConfigBO();
        OverseaMetricInfoBean overseaMetricInfoBean = bo.getSingleMetricInfoBean(examineeConfigList, remoteConfig, metric);
        //筛选配置表里与请求的metric相同的考核内容

        return overseaMetricCalStrategyBiz.getOverseaSingleDrillDownBaseInfo(request, d, overseaMetricInfoBean);
    }

    @Override
    public GetOverseaDrillDownBaseInfoV2ResponseType getOverseaDrillDownBaseInfoV2(GetOverseaDrillDownBaseInfoV2RequestType request) throws Exception {
        String domainName = request.getDomainName();
        //快速校验传入的domainName是否存在越权问题
        userChecker.fastCheckInputDomainName(domainName);
        //考核指标
        String metric = request.getMetric();
        String subMetric = request.getSubMetric();
        String d = dataUpdateBiz.getUpdateTime();//更新时间
//        String d = LocalDate.now().toString();
        MDC.put("metric", metric);

        TimeFilter timeFilter = request.getTimeFilter();
        String year = timeFilter.getYear();
        String dateType = timeFilter.getDateType();

        //获取应该展示的指标列表
        List<String> quarterList = "quarter".equals(dateType) ? Collections.singletonList(timeFilter.getQuarter()) : DateUtil.getQuarterOfHalf(timeFilter.getHalf());

        String tkt=remoteConfig.getConfigValue("tkt");
        String act=remoteConfig.getConfigValue("act");
        String odt=remoteConfig.getConfigValue("odt");
        List<String> buType = BuTypeEnum.getBuTypeList(request.getBusinessLine(),tkt,act,odt);
        List<DimOrdTtdTargetBO> examineeConfigList = dimOrdTtdTargetConfigDao.queryDimOrdTtdTarget(d, buType, domainName, year, quarterList);
        if (CollectionUtils.isEmpty(examineeConfigList)) {
            return new GetOverseaDrillDownBaseInfoV2ResponseType();
        }
        OverSeaExamineConfigBO bo = new OverSeaExamineConfigBO();
        OverseaMetricInfoBean overseaMetricInfoBean = bo.getSingleMetricInfoBeanV2(examineeConfigList, remoteConfig, metric, subMetric);
        GetOverseaDrillDownBaseInfoV2ResponseType response = overseaMetricCalStrategyBizV2.getOverseaSingleDrillDownBaseInfo(request, d, overseaMetricInfoBean);
       return response;
    }


    @Override
    public GetOverseaTableDataResponseType getOverseaTableData(GetOverseaTableDataRequestType request) throws Exception {

        String domainName = request.getDomainName();
        //快速校验传入的domainName是否存在越权问题
        userChecker.fastCheckInputDomainName(domainName);

        String metric = request.getMetric();
        String d = dataUpdateBiz.getUpdateTime();
        MDC.put("metric", metric);

        TimeFilter timeFilter = request.getTimeFilter();
        String year = timeFilter.getYear();
        String dateType = timeFilter.getDateType();
        //对于按半年考核的人  他们两个季度配置的考核数据是一样的  所以任取其一就行了
        String quarter = "quarter".equals(dateType) ? timeFilter.getQuarter() : DateUtil.getFirstQuarterOfHalf(timeFilter.getHalf());

        //获取应该展示的指标列表
        List<BusinessDashboardOverseaExamineeConfig> examineeConfigList = overseaConfigDao.querySpecificPeriodAllMetricConfig(domainName, d, year, quarter);
        OverSeaExamineConfigBO bo = new OverSeaExamineConfigBO();
        OverseaMetricInfoBean overseaMetricInfoBean = bo.getSingleMetricInfoBean(examineeConfigList, remoteConfig, metric);


        return overseaMetricCalStrategyBiz.getOverseaSingleTableData(request, d, overseaMetricInfoBean);
    }

    @Override
    public GetOverseaTableDataV2ResponseType getOverseaTableDataV2(GetOverseaTableDataV2RequestType request) throws Exception {
        String domainName = request.getDomainName();
        //快速校验传入的domainName是否存在越权问题
        userChecker.fastCheckInputDomainName(domainName);

        String metric = request.getMetric();
        String d = dataUpdateBiz.getUpdateTime();
//        String d = LocalDate.now().toString();
        if(request.getPageNo()==null||request.getPageSize()==null){
            request.setPageNo(1);
            request.setPageSize(10);
        }
        MDC.put("metric", metric);

        TimeFilter timeFilter = request.getTimeFilter();
        String year = timeFilter.getYear();
        String dateType = timeFilter.getDateType();
        //获取应该展示的指标列表
        List<String> quarterList = "quarter".equals(dateType) ? Collections.singletonList(timeFilter.getQuarter()) : DateUtil.getQuarterOfHalf(timeFilter.getHalf());

        String tkt = remoteConfig.getConfigValue("tkt");
        String act = remoteConfig.getConfigValue("act");
        String odt = remoteConfig.getConfigValue("odt");
        List<String> buType = BuTypeEnum.getBuTypeList(request.getBusinessLine(), tkt, act, odt);
        List<DimOrdTtdTargetBO> examineeConfigList = dimOrdTtdTargetConfigDao.queryDimOrdTtdTarget(d, buType, domainName, year, quarterList);
        if (CollectionUtils.isEmpty(examineeConfigList)) {
            return new GetOverseaTableDataV2ResponseType();
        }
        OverSeaExamineConfigBO bo = new OverSeaExamineConfigBO();
        OverseaMetricInfoBean overseaMetricInfoBean = bo.getSingleMetricInfoBeanV2(examineeConfigList, remoteConfig, metric, request.getSubMetric());
        GetOverseaTableDataV2ResponseType response = overseaMetricCalStrategyBizV2.getOverseaSingleTableData(request, d, overseaMetricInfoBean);
//        buildResponseIdByName(response, d);
        return response;
    }

    private void buildResponseIdByName(GetOverseaTableDataV2ResponseType response, String d) {
        List<OverseaTableDataRow> rowList = response.getRows();
        if (CollectionUtils.isEmpty(rowList)) {
            return;
        }
        //大区id处理
//        List<String> regionList = rowList.stream().filter(x -> StringUtils.isNotEmpty(x.getRegion())).map(x -> x.getRegion()).collect(Collectors.toList());
//        List<String> subRegionList = rowList.stream().filter(x -> StringUtils.isNotEmpty(x.getSubRegion())).map(x -> x.getSubRegion()).collect(Collectors.toList());
//        Map<String, Long> regionMap = dimPrdTtdRegionMappingDao.getMappingByDimName("region", regionList, d);
//        Map<String, Long> subRegionMap = dimPrdTtdRegionMappingDao.getMappingByDimName("province", subRegionList, d);
//        String other="其他";//NOSONAR
//        for (OverseaTableDataRow row : rowList) {
//            if (regionMap.containsKey(row.getRegion())) {
//                row.setRegionId(regionMap.get(row.getRegion()));
//            }
//            if (subRegionMap.containsKey(row.getSubRegion())) {
//                row.setSubRegionId(subRegionMap.get(row.getSubRegion()));
//            }
//            if("unkwn".equalsIgnoreCase(row.getRegion())){
//                row.setRegion(other);
//            }
//            if("unkwn".equalsIgnoreCase(row.getSubRegion())){
//                row.setSubRegion(other);
//            }
//            if ("unkwn".equalsIgnoreCase(row.getCountry())) {
//                row.setCountry(other);
//            }
//            if ("unkwn".equalsIgnoreCase(row.getExaminee())) {
//                row.setExaminee(other);
//            }
//            if ("unkwn".equalsIgnoreCase(row.getViewspot())) {
//                row.setViewspot(other);
//            }
//            if ("unkwn".equalsIgnoreCase(row.getSite())) {
//                row.setSite(other);
//            }
//            if ("unkwn".equalsIgnoreCase(row.getChannel())) {
//                row.setChannel(other);
//            }
//            if ("unkwn".equalsIgnoreCase(row.getVendorName())) {
//                row.setVendorName(other);
//            }
//        }
    }
}
