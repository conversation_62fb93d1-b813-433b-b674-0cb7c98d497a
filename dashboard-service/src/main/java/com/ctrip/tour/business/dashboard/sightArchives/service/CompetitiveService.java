package com.ctrip.tour.business.dashboard.sightArchives.service;

import com.ctrip.soa._24922.*;

//竞对分析
public interface CompetitiveService {

    //景点对比
    GetSightComparisonResponseType getSightComparison(GetSightComparisonRequestType requestType);

    //产品力对比 - 景点VS竞争圈
    GetSightCompetitiveResponseType getSightCompetitive(GetSightCompetitiveRequestType requestType);

    //产品力对比 - 携程VS竞对 - 景点未覆盖票种清单
    GetUncoveredTicketTypeResponseType getUncoveredTicketType(GetUncoveredTicketTypeRequestType requestType);

    //产品力对比 - 携程VS竞对 - 核心票种商品力下钻表格外层
    GetCoreTicketTypeCompetitiveResponseType getCoreTicketTypeCompetitive(GetCoreTicketTypeCompetitiveRequestType requestType);

    //产品力对比 - 携程VS竞对 - 核心票种商品力下钻表格内层
    GetCoreTicketTypeCompetitiveDetailResponseType getCoreTicketTypeCompetitiveDetail(GetCoreTicketTypeCompetitiveDetailRequestType requestType);

}
