package com.ctrip.tour.business.dashboard.utils;

import java.util.List;

public class ExcelSheetData {
    private String sheetName;
    private List<String> heads;
    private List<List<String>> resultList;

    public ExcelSheetData() {
    }
    public ExcelSheetData(String sheetName, List<String> heads, List<List<String>> resultList) {
        this.sheetName = sheetName;
        this.heads = heads;
        this.resultList = resultList;
    }

    public String getSheetName() {
        return sheetName;
    }

    public void setSheetName(String sheetName) {
        this.sheetName = sheetName;
    }

    public List<String> getHeads() {
        return heads;
    }

    public void setHeads(List<String> heads) {
        this.heads = heads;
    }

    public List<List<String>> getResultList() {
        return resultList;
    }

    public void setResultList(List<List<String>> resultList) {
        this.resultList = resultList;
    }
}