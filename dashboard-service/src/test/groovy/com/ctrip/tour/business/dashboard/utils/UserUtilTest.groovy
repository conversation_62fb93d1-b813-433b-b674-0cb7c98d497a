package com.ctrip.tour.business.dashboard.utils

import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig
import org.junit.runner.RunWith
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.slf4j.MDC
import org.spockframework.runtime.Sputnik
import spock.lang.Specification
import spock.lang.Unroll


@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik.class)
@PowerMockIgnore(["javax.net.ssl.*", "javax.management.*", "javax.crypto.*"])
@PrepareForTest([MDC.class])
class UserUtilTest extends Specification {

    def remoteConfig = Mock(RemoteConfig)

    def setup() {
        PowerMockito.mockStatic(MDC.class)
    }

    @Unroll
    def "test getPkMappingEmpCode"() {
        given: "设置调用"
        remoteConfig.getPkEmployeeRelationValue("S79530") >> "TR018551"
        remoteConfig.getPkEmployeeRelationValue("S79531") >> null
        PowerMockito.when(MDC.get("empCode")).thenReturn(inputEmpCode)

        when: "调用接口"
        def response = UserUtil.getPkMappingEmpCode(remoteConfig)

        then: "验证返回结果"
        response == returnEmpCode

        where: "测试用例"
        inputEmpCode || returnEmpCode
        "S79530"     || "TR018551"
        "S79531"     || "S79531"
    }
}
