package com.ctrip.tour.business.dashboard.sightArchives.dao.overseametric;

import com.ctrip.soa._27181.PreparedParameterBean;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.DimOrdTtdTargetBO;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.DimPrdTtdRegionMappingBO;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.TktStarRocksDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.Types;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Repository
@Slf4j
public class DimPrdTtdRegionMappingDao {


    @Autowired
    private TktStarRocksDao tktStarRocksDao;
    private void appendSql(StringBuilder sql, String sqlAppend, List<PreparedParameterBean> parameters, List<String> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            sql.append(sqlAppend);
            for (int i = 0; i < list.size(); i++) {
                if (i == 0) {
                    sql.append("?");
                } else {
                    sql.append(",?");
                }
                parameters.add(new PreparedParameterBean(list.get(i), Types.VARCHAR));
            }
            sql.append(")");
        }
    }

    public Map<String,Long> getMappingByDimName(String dimName,List<String> regionNameList, String d) {
        if(CollectionUtils.isEmpty(regionNameList)){
            return new HashMap<String,Long>();
        }
        StringBuilder sql = new StringBuilder(" select * from dim_prd_ttd_region_mapping_df where d= ?  ");
        List<PreparedParameterBean> parameters = new java.util.ArrayList<>();
        parameters.add(new PreparedParameterBean(d, Types.VARCHAR));

        if ("region".equalsIgnoreCase(dimName)) {
            appendSql(sql," and business_region_name in ( ",parameters,regionNameList);
        } else if ("province".equalsIgnoreCase(dimName)) {
            appendSql(sql," and business_sub_region_name in ( ",parameters,regionNameList);
        }
        List<Map<String, Object>> result = new ArrayList<>();
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            log.warn("queryOverseaPersonInfo error", e);
        }
        List<DimPrdTtdRegionMappingBO> ttdRegionMappingList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(result)) {
            ttdRegionMappingList = result.stream()
                    .map(bean -> {
                        DimPrdTtdRegionMappingBO ttdRegionMapping = new DimPrdTtdRegionMappingBO();
                        ttdRegionMapping.setBusinessRegionId((Long) bean.get("business_region_id"));
                        ttdRegionMapping.setBusinessRegionName((String) bean.get("business_region_name"));
                        ttdRegionMapping.setBusinessSubRegionId((Long) bean.get("business_sub_region_id"));
                        ttdRegionMapping.setBusinessSubRegionName((String) bean.get("business_sub_region_name"));
                        return ttdRegionMapping;
                    })
                    .collect(Collectors.toList());
        }
        Map<String, Long> map = new HashMap<>();
        if ("region".equalsIgnoreCase(dimName)) {
            map = ttdRegionMappingList.stream().collect(Collectors.toMap(k -> k.getBusinessRegionName(), v -> v.getBusinessRegionId(), (k1, k2) -> k1));
        } else if ("province".equalsIgnoreCase(dimName)) {
            map = ttdRegionMappingList.stream().collect(Collectors.toMap(k -> k.getBusinessSubRegionName(), v -> v.getBusinessSubRegionId(), (k1, k2) -> k1));
        }
        return map;
    }

}
