package com.ctrip.tour.business.dashboard.grpBusiness.bo;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/3/25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustRegionOrgInfoDTO {

    private String regionName;

    private String eid;

    private String regionId;

    private List<AreaInfo> areaInfos;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AreaInfo {

        private String regionName;

        private String eid;

        private String regionId;

        private String areaName;

        private String jobNumber;

        private String areaId;
    }
}
