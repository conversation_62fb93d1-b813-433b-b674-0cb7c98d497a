package com.ctrip.tour.business.dashboard.sightArchives.dao.overseametric;

import com.ctrip.soa._27181.PreparedParameterBean;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.CdmOrdTtdOverseasPerformanceIndexBO;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.bean.OverseasPerformanceInfoParamBean;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.TktStarRocksDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.sql.Types;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Repository
@Slf4j
public class CdmOrdTtdOverseasPerformanceIndexAddrDao {

    @Autowired
    private TktStarRocksDao tktStarRocksDao;
    public List<CdmOrdTtdOverseasPerformanceIndexBO> getMomOverseasProductionResponseInfo(OverseasPerformanceInfoParamBean param) {
        StringBuilder sql = new StringBuilder("select   " +
                "sum(ifnull(ttd_ord_suc_gmv,0)) as ttd_ord_suc_gmv, " +
                "sum(ifnull(ttd_ord_suc_profit,0)) as ttd_ord_suc_profit, " +
                "sum(ifnull(ttd_ord_suc_qty,0)) as ttd_ord_suc_qty , ");

        List<PreparedParameterBean> parameters = new java.util.ArrayList<>();
        String groupBy = null;
        switch (param.getField()) {
            case "region":
                sql.append(" business_region_name ");
                groupBy = "  group by business_region_name ";
                break;
            case "province":
                sql.append(" business_region_name,business_sub_region_name ");
                groupBy = "  group by business_region_name,business_sub_region_name ";
                break;
            case "site":
                sql.append(" site ");
                groupBy = "  group by site ";
                break;
            case "channel":
                sql.append(" dis_channel ");
                groupBy = "  group by dis_channel ";
                break;
        }
        sql.append(" from cdm_ord_ttd_overseas_performance_index_addr_df " +
                "  where d=?  ");
        parameters.add(new PreparedParameterBean(param.getD(), Types.VARCHAR));

        appendSql(sql," and bu_type_name in ( ",parameters,param.getBuTypeName());
        if (CollectionUtils.isNotEmpty(param.getCt())) {
            appendSql(sql," and ct in ( ",parameters,param.getCt());
        }
        if (CollectionUtils.isNotEmpty(param.getBuRegionNames())) {
            appendSql(sql, " and business_region_name in ( ", parameters, param.getBuRegionNames());
        }
        if (CollectionUtils.isNotEmpty(param.getBuSubRegionNames())) {
            appendSql(sql, " and business_sub_region_name in ( ", parameters, param.getBuSubRegionNames());
        }
        if(CollectionUtils.isNotEmpty(param.getDisChannelName())){
            appendSql(sql, " and dis_channel in ( ", parameters, param.getVstNames());
        }
        if(CollectionUtils.isNotEmpty(param.getSites())){
            appendSql(sql, " and site in ( ", parameters, param.getVstNames());
        }
        if(CollectionUtils.isNotEmpty(param.getLocale())){
            appendSql(sql, " and locale in ( ", parameters, param.getVstNames());
        }
        sql.append(" and ( use_date between ? and ? )");
        parameters.add(new PreparedParameterBean(param.getStartDate(), Types.VARCHAR));
        parameters.add(new PreparedParameterBean(param.getEndDate(), Types.VARCHAR));
        sql.append(groupBy);
        List<Map<String, Object>> result = new ArrayList<>();
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            log.warn("queryOverseaPersonInfo error", e);
        }
        List<CdmOrdTtdOverseasPerformanceIndexBO> CdmOrdTtdOverseasPerformanceIndexBOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(result)) {
            CdmOrdTtdOverseasPerformanceIndexBOList = result.stream()
                    .map(bean -> {
                        CdmOrdTtdOverseasPerformanceIndexBO performanceIndexBO = new CdmOrdTtdOverseasPerformanceIndexBO();
                        performanceIndexBO.setGmv(((BigDecimal) bean.get("ttd_ord_suc_gmv")).doubleValue());
                        performanceIndexBO.setProfit(((BigDecimal) bean.get("ttd_ord_suc_profit")).doubleValue());
                        performanceIndexBO.setQty(((BigDecimal) bean.get("ttd_ord_suc_qty")).doubleValue());

                        performanceIndexBO.setBuRegionNames((String) bean.get("business_region_name"));
                        performanceIndexBO.setBuSubRegionNames((String) bean.get("business_sub_region_name"));
                        performanceIndexBO.setSites((String) bean.get("site"));
                        performanceIndexBO.setLocale((String) bean.get("locale"));
                        performanceIndexBO.setDisChannelName((String) bean.get("dis_channel"));
                        return performanceIndexBO;
                    })
                    .collect(Collectors.toList());
        }
        return CdmOrdTtdOverseasPerformanceIndexBOList;
    }

    public List<CdmOrdTtdOverseasPerformanceIndexBO> getOverseasProductionResponseInfo(OverseasPerformanceInfoParamBean param) {
        StringBuilder sql = new StringBuilder("select sum(ifnull(ttd_ord_suc_gmv,0)) as ttd_ord_suc_gmv, sum(ifnull(ttd_ord_suc_profit,0)) as ttd_ord_suc_profit, " +
                "sum(ifnull(ttd_ord_suc_qty,0)) as ttd_ord_suc_qty, " );
        String groupBy = null;
        String filterSql = null;
        switch (param.getField()) {
            case "region":
                sql.append(" business_region_name,business_region_name_en,business_region_id ");
                filterSql=" and business_region_name!='unkwn' ";
                groupBy = "  group by business_region_name,business_region_name_en,business_region_id ";
                break;
            case "province":
                sql.append("  business_region_name,business_sub_region_name,business_region_name_en,business_sub_region_name_en,business_region_id,business_sub_region_id  ");
                filterSql=" and business_region_name!='unkwn'  ";
                groupBy = "  group by business_region_name,business_sub_region_name,business_region_name_en,business_sub_region_name_en,business_region_id,business_sub_region_id ";
                break;
            case "country":
                if("城市".equalsIgnoreCase(param.getExamineLevel())){//NOSONAR
                    sql.append(" business_region_name,business_sub_region_name,dep_ctry_name,dep_prov_name,dep_city_name," +
                            "business_region_name_en,business_sub_region_name_en,dep_ctry_name_en,dep_prov_name_en,dep_city_name_en," +
                            "business_region_id,business_sub_region_id,dep_ctry_id,dep_prov_id,dep_city_id ");
                    filterSql=" and business_region_name!='unkwn' ";
                    groupBy = "  group by business_region_name,business_sub_region_name,dep_ctry_name,dep_prov_name,dep_city_name, " +
                            "  business_region_name_en,business_sub_region_name_en,dep_ctry_name_en,dep_prov_name_en,dep_city_name_en, " +
                            "   business_region_id,business_sub_region_id,dep_ctry_id,dep_prov_id,dep_city_id ";
                }else if("省份".equalsIgnoreCase(param.getExamineLevel())){//NOSONAR
                    sql.append(" business_region_name,business_sub_region_name,dep_ctry_name,dep_prov_name," +
                            "business_region_name_en,business_sub_region_name_en,dep_ctry_name_en,dep_prov_name_en," +
                            "business_region_id,business_sub_region_id,dep_ctry_id,dep_prov_id ");
                    filterSql=" and business_region_name!='unkwn'  ";
                    groupBy = "  group by business_region_name,business_sub_region_name,dep_ctry_name,dep_prov_name, " +
                            " business_region_name_en,business_sub_region_name_en,dep_ctry_name_en,dep_prov_name_en, " +
                            " business_region_id,business_sub_region_id,dep_ctry_id,dep_prov_id ";
                }else {
                    sql.append(" business_region_name,business_sub_region_name,dep_ctry_name," +
                            "business_region_name_en,business_sub_region_name_en,dep_ctry_name_en," +
                            "business_region_id,business_sub_region_id,dep_ctry_id ");
                    filterSql=" and business_region_name!='unkwn'  ";
                    groupBy = "  group by business_region_name,business_sub_region_name,dep_ctry_name, " +
                            " business_region_name_en,business_sub_region_name_en,dep_ctry_name_en, " +
                            " business_region_id,business_sub_region_id,dep_ctry_id ";
                }
                break;
            case "viewspot":
                sql.append(" business_region_name,business_sub_region_name,dep_ctry_name,dep_prov_name,dep_city_name,vst_id,vst_name," +
                        "business_region_name_en,business_sub_region_name_en,dep_ctry_name_en,dep_prov_name_en,dep_city_name_en,vst_name_en," +
                        "business_region_id,business_sub_region_id,dep_ctry_id,dep_prov_id,dep_city_id ");
                filterSql=" and business_region_name!='unkwn'  and vst_name!='unkwn'";
                groupBy = "  group by  business_region_name,business_sub_region_name,dep_ctry_name,dep_prov_name,dep_city_name,vst_id,vst_name, " +
                        " business_region_name_en,business_sub_region_name_en,dep_ctry_name_en,dep_prov_name_en,dep_city_name_en,vst_name_en, " +
                        " business_region_id,business_sub_region_id,dep_ctry_id,dep_prov_id,dep_city_id  ";
                break;
            case "site":
                sql.append(" dis_channel,site ");
                filterSql=" and site!='unkwn' ";
                groupBy = "  group by dis_channel,site ";
                break;
            case "locale":
                sql.append(" dis_channel,site,locale  ");
                filterSql=" and site!='unkwn' ";
                groupBy = "  group by dis_channel,site,locale ";
                break;
            case "channel":
                sql.append(" dis_channel ");
                filterSql="  ";
                groupBy = "  group by dis_channel ";
                break;
        }
        sql.append(" from cdm_ord_ttd_overseas_performance_index_addr_df where d=? and use_year=?  ");
        sql.append(filterSql);
        List<PreparedParameterBean> parameters = new java.util.ArrayList<>();
        buildParameters(sql,parameters,param);
        sql.append(groupBy);
        buildOrderParameters(sql,parameters,param);

        List<Map<String, Object>> result = new ArrayList<>();
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            log.warn("queryOverseaPersonInfo error", e);
        }
        List<CdmOrdTtdOverseasPerformanceIndexBO> CdmOrdTtdOverseasPerformanceIndexBOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(result)) {
            CdmOrdTtdOverseasPerformanceIndexBOList = result.stream()
                    .map(bean -> {
                        CdmOrdTtdOverseasPerformanceIndexBO performanceIndexBO = new CdmOrdTtdOverseasPerformanceIndexBO();
                        performanceIndexBO.setGmv(((BigDecimal) bean.get("ttd_ord_suc_gmv")).doubleValue());
                        performanceIndexBO.setProfit(((BigDecimal) bean.get("ttd_ord_suc_profit")).doubleValue());
                        performanceIndexBO.setQty(((BigDecimal) bean.get("ttd_ord_suc_qty")).doubleValue());

                        performanceIndexBO.setBuRegionId((Long) bean.get("business_region_id"));
                        performanceIndexBO.setBuSubRegionId((Long) bean.get("business_sub_region_id"));
                        performanceIndexBO.setBuRegionNames((String) bean.get("business_region_name"));
                        performanceIndexBO.setBuSubRegionNames((String) bean.get("business_sub_region_name"));
                        performanceIndexBO.setCtryId((Long) bean.get("dep_ctry_id"));
                        performanceIndexBO.setProvId((Long) bean.get("dep_prov_id"));
                        performanceIndexBO.setCityId((Long) bean.get("dep_city_id"));
                        performanceIndexBO.setCtryName((String) bean.get("dep_ctry_name"));
                        performanceIndexBO.setProvName((String) bean.get("dep_prov_name"));
                        performanceIndexBO.setCityName((String) bean.get("dep_city_name"));
                        performanceIndexBO.setBuRegionNameEn((String) bean.get("business_region_name_en"));
                        performanceIndexBO.setBuSubRegionNameEn((String) bean.get("business_sub_region_name_en"));
                        performanceIndexBO.setCtryNameEn((String) bean.get("dep_ctry_name_en"));
                        performanceIndexBO.setProvNameEn((String) bean.get("dep_prov_name_en"));
                        performanceIndexBO.setCityName((String) bean.get("dep_city_name_en"));
                        performanceIndexBO.setVstId((Long) bean.get("vst_id"));
                        performanceIndexBO.setVstName((String) bean.get("vst_name"));
                        performanceIndexBO.setVstNameEn((String) bean.get("vst_name_en"));
                        performanceIndexBO.setSites((String) bean.get("site"));
                        performanceIndexBO.setLocale((String) bean.get("locale"));
                        performanceIndexBO.setDisChannelName((String) bean.get("dis_channel"));
                        return performanceIndexBO;
                    })
                    .collect(Collectors.toList());
        }
        return CdmOrdTtdOverseasPerformanceIndexBOList;
    }

    private void buildOrderParameters(StringBuilder sql, List<PreparedParameterBean> parameters, OverseasPerformanceInfoParamBean param) {
        if (param.getPageIndex() == null || param.getPageSize() == null) {
            return;
        }
        if (StringUtils.isNotEmpty(param.getMetric())) {
            switch (param.getMetric()) {
                case "101":
                    sql.append(" order by ttd_ord_suc_gmv desc ");
                    break;
                case "102":
                    sql.append(" order by ttd_ord_suc_profit desc  ");
                    break;
                case "110":
                    sql.append(" order by ttd_ord_suc_qty  desc ");
                    break;
            }
        }
        if (param.getPageIndex() != null && param.getPageSize() != null) {
            sql.append(" limit ?,? ");
            parameters.add(new PreparedParameterBean(String.valueOf((param.getPageIndex() - 1) * param.getPageSize()), Types.INTEGER));
            parameters.add(new PreparedParameterBean(String.valueOf(param.getPageSize()), Types.INTEGER));
        }
    }

    public void buildParameters(StringBuilder sql,List<PreparedParameterBean> parameters,OverseasPerformanceInfoParamBean param){
        parameters.add(new PreparedParameterBean(param.getD(), Types.VARCHAR));
        parameters.add(new PreparedParameterBean(param.getYear(), Types.VARCHAR));
        if (StringUtils.isNotEmpty(param.getQuarter())) {
            sql.append(" and use_quarter =? ");
            parameters.add(new PreparedParameterBean(param.getQuarter(), Types.VARCHAR));
        }
        if (StringUtils.isNotEmpty(param.getHalfYear())) {
            sql.append(" and use_half_year =? ");
            parameters.add(new PreparedParameterBean(param.getHalfYear(), Types.VARCHAR));
        }

        if (StringUtils.isNotEmpty(param.getStartDate())) {
            sql.append(" and use_date >= ? ");
            parameters.add(new PreparedParameterBean(param.getStartDate(), Types.VARCHAR));
        }

        if (StringUtils.isNotEmpty(param.getEndDate())) {
            sql.append(" and use_date <= ? ");
            parameters.add(new PreparedParameterBean(param.getEndDate(), Types.VARCHAR));
        }

        appendSql(sql, " and bu_type_name in ( ", parameters, param.getBuTypeName());
        if (CollectionUtils.isNotEmpty(param.getCt())) {
            appendSql(sql, " and ct in ( ", parameters, param.getCt());
        }
        if (CollectionUtils.isNotEmpty(param.getBuRegionNames())) {
            appendSql(sql, " and business_region_name in ( ", parameters, param.getBuRegionNames());
        }
        if (CollectionUtils.isNotEmpty(param.getBuRegionNameEns())) {
            appendSql(sql, " and business_region_name_en in ( ", parameters, param.getBuRegionNameEns());
        }
        if (CollectionUtils.isNotEmpty(param.getBuSubRegionNames())) {
            appendSql(sql, " and business_sub_region_name in ( ", parameters, param.getBuSubRegionNames());
        }
        if (CollectionUtils.isNotEmpty(param.getBuSubRegionNameEns())) {
            appendSql(sql, " and business_sub_region_name_en in ( ", parameters, param.getBuSubRegionNameEns());
        }
        if (CollectionUtils.isNotEmpty(param.getCtryNames())) {
            appendSql(sql, " and dep_ctry_name in ( ", parameters, param.getCtryNames());
        }
        if (CollectionUtils.isNotEmpty(param.getCtryNameEns())) {
            appendSql(sql, " and dep_ctry_name_en in ( ", parameters, param.getCtryNameEns());
        }
        if (CollectionUtils.isNotEmpty(param.getProvNames())) {
            appendSql(sql, " and dep_prov_name in ( ", parameters, param.getProvNames());
        }
        if (CollectionUtils.isNotEmpty(param.getProvNameEns())) {
            appendSql(sql, " and dep_prov_name_en in ( ", parameters, param.getProvNames());
        }
        if (CollectionUtils.isNotEmpty(param.getCityNames())) {
            appendSql(sql, " and dep_city_name in ( ", parameters, param.getCityNames());
        }
        if (CollectionUtils.isNotEmpty(param.getCityNameEns())) {
            appendSql(sql, " and dep_city_name_en in ( ", parameters, param.getCityNameEns());
        }
        if (CollectionUtils.isNotEmpty(param.getVstMeids())) {
            appendSql(sql, " and vst_meid in ( ", parameters, param.getVstMeids());
        }
        if (CollectionUtils.isNotEmpty(param.getVstAeids())) {
            appendSql(sql, " and vst_aeid in ( ", parameters, param.getVstAeids());
        }
        if (CollectionUtils.isNotEmpty(param.getVstNames())) {
            appendSql(sql, " and vst_name in ( ", parameters, param.getVstNames());
        }
        if (CollectionUtils.isNotEmpty(param.getVstNameEns())) {
            appendSql(sql, " and vst_name_en in ( ", parameters, param.getVstNameEns());
        }
        if (CollectionUtils.isNotEmpty(param.getDisChannelName())) {
            appendSql(sql, " and dis_channel in ( ", parameters, param.getDisChannelName());
        }
        if (CollectionUtils.isNotEmpty(param.getSites())) {
            appendSql(sql, " and site in ( ", parameters, param.getSites());
        }
        if (CollectionUtils.isNotEmpty(param.getLocale())) {
            appendSql(sql, " and locale in ( ", parameters, param.getLocale().stream().map(x->x.toLowerCase()).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(param.getDimValueBuRegionNames())) {
            appendSql(sql, " and business_region_name in ( ", parameters, param.getDimValueBuRegionNames());
        }
        if (CollectionUtils.isNotEmpty(param.getDimValueBuRegionNameEns())) {
            appendSql(sql, " and business_region_name_en in ( ", parameters, param.getDimValueBuRegionNameEns());
        }
        if (CollectionUtils.isNotEmpty(param.getDimValueBuSubRegionNames())) {
            appendSql(sql, " and business_sub_region_name in ( ", parameters, param.getDimValueBuSubRegionNames());
        }
        if (CollectionUtils.isNotEmpty(param.getDimValueBuSubRegionNameEns())) {
            appendSql(sql, " and business_sub_region_name_en in ( ", parameters, param.getDimValueBuSubRegionNameEns());
        }
        if (CollectionUtils.isNotEmpty(param.getDimVstNames())) {
            appendSql(sql, " and vst_name in ( ", parameters, param.getDimVstNames());
        }
        if (CollectionUtils.isNotEmpty(param.getDimVstNameEns())) {
            appendSql(sql, " and vst_name_en in ( ", parameters, param.getDimVstNameEns());
        }
        if (CollectionUtils.isNotEmpty(param.getDimValueDisChannelName())) {
            appendSql(sql, " and dis_channel_name in ( ", parameters, param.getDimValueDisChannelName());
        }
        if (CollectionUtils.isNotEmpty(param.getDimValueSites())) {
            appendSql(sql, " and site in ( ", parameters, param.getDimValueSites());
        }
        if (CollectionUtils.isNotEmpty(param.getDimValueLocale())) {
            appendSql(sql, " and locale in ( ", parameters, param.getDimValueLocale());
        }
    }

    private void appendSql(StringBuilder sql, String sqlAppend, List<PreparedParameterBean> parameters, List<String> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            sql.append(sqlAppend);
            for (int i = 0; i < list.size(); i++) {
                if (i == 0) {
                    sql.append("?");
                } else {
                    sql.append(",?");
                }
                parameters.add(new PreparedParameterBean(list.get(i), Types.VARCHAR));
            }
            sql.append(")");
        }
    }


    public Integer queryOverseasPerformanceInfoCount(OverseasPerformanceInfoParamBean param) {
        StringBuilder sql = new StringBuilder();
        String filterSql=null;
        switch (param.getField()) {
            case "region":
                sql.append(" select count(distinct business_region_name) as totalNum ");
                filterSql=" and business_region_name!='unkwn' ";
                break;
            case "province":
                sql.append("  select count(distinct business_region_name,business_sub_region_name) as totalNum  ");
                filterSql=" and business_region_name!='unkwn' ";
                break;
            case "country":
                if ("城市".equalsIgnoreCase(param.getExamineLevel())) {//NOSONAR
                    sql.append("  select count(distinct business_region_name,business_sub_region_name,dep_ctry_name,dep_prov_name,dep_city_name) as totalNum  ");
                    filterSql=" and business_region_name!='unkwn'  ";
                } else if ("省份".equalsIgnoreCase(param.getExamineLevel())) {//NOSONAR
                    sql.append("  select count(distinct business_region_name,business_sub_region_name,dep_ctry_name,dep_prov_name) as totalNum  ");
                    filterSql=" and business_region_name!='unkwn'  ";
                } else {
                    sql.append("  select count(distinct business_region_name,business_sub_region_name,dep_ctry_name) as totalNum  ");
                    filterSql=" and business_region_name!='unkwn'  ";
                }
                break;
            case "viewspot":
                sql.append("  select count(distinct business_region_name,business_sub_region_name,vst_name) as totalNum  ");
                filterSql=" and business_region_name!='unkwn' and vst_name!='unkwn' ";
                break;
                //下面为其他考核指标的，不是目的地的
            case "site":
                sql.append("  select count(distinct site) as totalNum  ");
                filterSql=" and site!='unkwn' ";
                break;
            case "locale":
                sql.append("  select count(distinct site,locale) as totalNum  ");
                filterSql=" and site!='unkwn' ";
                break;
            case "channel":
                sql.append("  select count(distinct dis_channel) as totalNum  ");
                filterSql=" ";
                break;
        }
        sql.append(" from cdm_ord_ttd_overseas_performance_index_addr_df where d=? and use_year=?  ");
        sql.append(filterSql);
        List<PreparedParameterBean> parameters = new java.util.ArrayList<>();
        buildParameters(sql, parameters, param);
//        sql.append(groupBy);
        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(),parameters);
        } catch (SQLException e) {
            log.warn("queryUncoveredTicketTypeTotalNum error", e);
        }
        return CollectionUtils.isNotEmpty(result)? Math.toIntExact((Long) result.get(0).getOrDefault("totalNum","0")) :0;
    }
}
