#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 528482304 bytes. Error detail: G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3825), pid=3436, tid=18696
#
# JRE version:  (17.0.11+1) (build )
# Java VM: OpenJDK 64-Bit Server VM (17.0.11+1-b1207.24, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'http://git.dev.sh.ctripcorp.com': 

Host: AMD Ryzen 7 7730U with Radeon Graphics         , 16 cores, 31G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5678)
Time: Wed May 28 19:24:18 2025  Windows 10 , 64 bit Build 19041 (10.0.19041.5678) elapsed time: 0.025206 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x0000018e4a221ac0):  JavaThread "Unknown thread" [_thread_in_vm, id=18696, stack(0x0000001c71d00000,0x0000001c71e00000)]

Stack: [0x0000001c71d00000,0x0000001c71e00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6877f9]
V  [jvm.dll+0x8411aa]
V  [jvm.dll+0x842e2e]
V  [jvm.dll+0x843493]
V  [jvm.dll+0x249fdf]
V  [jvm.dll+0x6845c9]
V  [jvm.dll+0x678e7a]
V  [jvm.dll+0x30ab4b]
V  [jvm.dll+0x311ff6]
V  [jvm.dll+0x361a5e]
V  [jvm.dll+0x361c8f]
V  [jvm.dll+0x2e0978]
V  [jvm.dll+0x2e18e4]
V  [jvm.dll+0x811c71]
V  [jvm.dll+0x36f7c8]
V  [jvm.dll+0x7f05f6]
V  [jvm.dll+0x3f398f]
V  [jvm.dll+0x3f5541]
C  [jli.dll+0x528f]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ff98403efd8, length=0, elements={
}

Java Threads: ( => current thread )

Other Threads:
  0x0000018e4a2cdfd0 GCTaskThread "GC Thread#0" [stack: 0x0000001c71e00000,0x0000001c71f00000] [id=22420]
  0x0000018e4a2de9b0 ConcurrentGCThread "G1 Main Marker" [stack: 0x0000001c71f00000,0x0000001c72000000] [id=33316]
  0x0000018e6fbe0d70 ConcurrentGCThread "G1 Conc#0" [stack: 0x0000001c72000000,0x0000001c72100000] [id=28680]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ff9837f1547]

VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x0000018e4a21c7e0] Heap_lock - owner thread: 0x0000018e4a221ac0

Heap address: 0x000000060ac00000, size: 8020 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
<Empty>

Heap:
 garbage-first heap   total 0K, used 0K [0x000000060ac00000, 0x0000000800000000)
  region size 4096K, 0 young (0K), 0 survivors (0K)
 Metaspace       used 0K, committed 0K, reserved 0K
  class space    used 0K, committed 0K, reserved 0K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)

Card table byte_map: [0x0000018e5e1e0000,0x0000018e5f190000] _byte_map_base: 0x0000018e5b18a000

Marking Bits (Prev, Next): (CMBitMap*) 0x0000018e4a2ce5f0, (CMBitMap*) 0x0000018e4a2ce630
 Prev Bits: [0x0000018e60140000, 0x0000018e67e90000)
 Next Bits: [0x0000018e67e90000, 0x0000018e6fbe0000)

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.011 Loaded shared library D:\soft\IntelliJ IDEA 2024.1.4\jbr\bin\java.dll

Deoptimization events (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

VM Operations (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff77e3f0000 - 0x00007ff77e3fa000 	D:\soft\IntelliJ IDEA 2024.1.4\jbr\bin\java.exe
0x00007ff9d1850000 - 0x00007ff9d1a48000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ff9d1440000 - 0x00007ff9d1502000 	C:\Windows\System32\KERNEL32.DLL
0x00007ff9cf040000 - 0x00007ff9cf336000 	C:\Windows\System32\KERNELBASE.dll
0x00007ff9cf6c0000 - 0x00007ff9cf7c0000 	C:\Windows\System32\ucrtbase.dll
0x00007ff99d7f0000 - 0x00007ff99d807000 	D:\soft\IntelliJ IDEA 2024.1.4\jbr\bin\jli.dll
0x00007ff9b59f0000 - 0x00007ff9b5a0b000 	D:\soft\IntelliJ IDEA 2024.1.4\jbr\bin\VCRUNTIME140.dll
0x00007ff9d0e90000 - 0x00007ff9d102d000 	C:\Windows\System32\USER32.dll
0x00007ff9cf570000 - 0x00007ff9cf592000 	C:\Windows\System32\win32u.dll
0x00007ff9d0c30000 - 0x00007ff9d0c5b000 	C:\Windows\System32\GDI32.dll
0x00007ff9cf5a0000 - 0x00007ff9cf6ba000 	C:\Windows\System32\gdi32full.dll
0x00007ff9cf4d0000 - 0x00007ff9cf56d000 	C:\Windows\System32\msvcp_win.dll
0x00007ff9b5da0000 - 0x00007ff9b603a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.4355_none_60b8b9eb71f62e16\COMCTL32.dll
0x00007ff9d0700000 - 0x00007ff9d079e000 	C:\Windows\System32\msvcrt.dll
0x00007ff9d06d0000 - 0x00007ff9d06ff000 	C:\Windows\System32\IMM32.DLL
0x00007ff9b5250000 - 0x00007ff9b525c000 	D:\soft\IntelliJ IDEA 2024.1.4\jbr\bin\vcruntime140_1.dll
0x00007ff99c610000 - 0x00007ff99c69d000 	D:\soft\IntelliJ IDEA 2024.1.4\jbr\bin\msvcp140.dll
0x00007ff983500000 - 0x00007ff984183000 	D:\soft\IntelliJ IDEA 2024.1.4\jbr\bin\server\jvm.dll
0x00007ff9cf8d0000 - 0x00007ff9cf97f000 	C:\Windows\System32\ADVAPI32.dll
0x00007ff9d1200000 - 0x00007ff9d129f000 	C:\Windows\System32\sechost.dll
0x00007ff9d1310000 - 0x00007ff9d1433000 	C:\Windows\System32\RPCRT4.dll
0x00007ff9cf340000 - 0x00007ff9cf367000 	C:\Windows\System32\bcrypt.dll
0x00007ff9c3ee0000 - 0x00007ff9c3eea000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ff9ce430000 - 0x00007ff9ce47b000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ff9c0a90000 - 0x00007ff9c0ab7000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ff9a1710000 - 0x00007ff9a1719000 	C:\Windows\SYSTEM32\WSOCK32.dll
0x00007ff9d12a0000 - 0x00007ff9d130b000 	C:\Windows\System32\WS2_32.dll
0x00007ff9ce2a0000 - 0x00007ff9ce2b2000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ff9cd520000 - 0x00007ff9cd532000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ff99ed80000 - 0x00007ff99ed8a000 	D:\soft\IntelliJ IDEA 2024.1.4\jbr\bin\jimage.dll
0x00007ff9cd2d0000 - 0x00007ff9cd4b4000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ff9ac3c0000 - 0x00007ff9ac3f4000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ff9cef60000 - 0x00007ff9cefe2000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ff994e40000 - 0x00007ff994e65000 	D:\soft\IntelliJ IDEA 2024.1.4\jbr\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\soft\IntelliJ IDEA 2024.1.4\jbr\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.4355_none_60b8b9eb71f62e16;D:\soft\IntelliJ IDEA 2024.1.4\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'http://git.dev.sh.ctripcorp.com': 
java_class_path (initial): D:/soft/IntelliJ IDEA 2024.1.4/plugins/vcs-git/lib/git4idea-rt.jar;D:/soft/IntelliJ IDEA 2024.1.4/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 528482304                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8409579520                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8409579520                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
CLASSPATH=.;C:\Program Files\Eclipse Adoptium\jdk-8.0.322.6-hotspot\\lib;D:\soft\apache-maven-3.9.9\bin;
PATH=C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;D:\Users\chaoqiangchen\bin;C:\Program Files\Eclipse Adoptium\jdk-8.0.322.6-hotspot\bin;C:\ProgramData\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files\Git\cmd;D:\Users\chaoqiangchen\AppData\Local\Microsoft\WindowsApps;D:\dev\IntelliJ IDEA 2024.1.4\bin;D:\soft\apache-maven-3.9.9\bin
USERNAME=chaoqiangchen
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=xterm-256color
TMPDIR=D:\Users\CHAOQI~1\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 25 Model 80 Stepping 0, AuthenticAMD
TMP=D:\Users\CHAOQI~1\AppData\Local\Temp
TEMP=D:\Users\CHAOQI~1\AppData\Local\Temp



Periodic native trim disabled

JNI global refs:
JNI global refs: 0, weak refs: 0

JNI global refs memory usage: 0, weak refs: 0

Process memory usage:
Resident Set Size: 11804K (0% of 32834628K total physical memory with 4870288K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:


---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5678)
OS uptime: 6 days 2:43 hours
Hyper-V role detected

CPU: total 16 (initial active 16) (16 cores per cpu, 2 threads per core) family 25 model 80 stepping 0 microcode 0x0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, hv
Processor Information for all 16 processors :
  Max Mhz: 2000, Current Mhz: 2000, Mhz Limit: 2000

Memory: 4k page, system-wide physical 32065M (4756M free)
TotalPageFile size 40257M (AvailPageFile size 495M)
current process WorkingSet (physical memory assigned to process): 11M, peak: 11M
current process commit charge ("private bytes"): 71M, peak: 574M

vm_info: OpenJDK 64-Bit Server VM (17.0.11+1-b1207.24) for windows-amd64 JRE (17.0.11+1-b1207.24), built on 2024-05-15 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
