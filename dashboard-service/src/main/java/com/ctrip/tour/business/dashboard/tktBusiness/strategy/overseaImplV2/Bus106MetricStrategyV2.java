package com.ctrip.tour.business.dashboard.tktBusiness.strategy.overseaImplV2;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBeanV2;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.OverseaMetricCalStrategyV2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.Future;

@Component
public class Bus106MetricStrategyV2 implements OverseaMetricCalStrategyV2 {


    @Autowired
    private Bus105And106And107MetricStrategyV2 bus105And106And107MetricStrategy;

    @Override
    public Future<OveaseaMetric> getOverseaSingleMetricCardData(TimeFilter timeFilter,
                                                                OverseaMetricInfoBeanV2 metricInfoBean,
                                                                String d,
                                                                AvailableSubMetric availableSubMetric,
                                                                GetOverseaMetricCardDataV2RequestType request) throws Exception {
        return bus105And106And107MetricStrategy.getOverseaSingleMetricCardData(timeFilter, metricInfoBean, d, availableSubMetric, "106");
    }

    @Override
    public GetOverseaTrendLineDataV2ResponseType getOverseaSingleTrendlineData(GetOverseaTrendLineDataV2RequestType request,
                                                                               String d) throws Exception {
        return bus105And106And107MetricStrategy.getOverseaSingleTrendlineData(request, d);
    }

    @Override
    public GetOverseaDrillDownBaseInfoV2ResponseType getOverseaSingleDrillDownBaseInfo(GetOverseaDrillDownBaseInfoV2RequestType request,
                                                                                     String d,
                                                                                       OverseaMetricInfoBean metricInfoBean) throws Exception {
        return bus105And106And107MetricStrategy.getOverseaSingleDrillDownBaseInfo(request, d, metricInfoBean);
    }

    @Override
    public GetOverseaTableDataV2ResponseType getOverseaSingleTableData(GetOverseaTableDataV2RequestType request,
                                                                     String d,
                                                                       OverseaMetricInfoBean metricInfoBean) throws Exception {
        return bus105And106And107MetricStrategy.getOverseaSingleTableData(request, d, metricInfoBean);
    }

    @Override
    public String getMetricName() {
        return "106";
    }
}
