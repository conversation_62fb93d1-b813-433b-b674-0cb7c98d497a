package com.ctrip.tour.business.dashboard.tktBusiness.helper;

import com.ctrip.soa._24922.TableDataItem;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.ConfigImportException;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.google.common.collect.Lists;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/8/8
 */
public class Bus1TicketActivityHelper {

    public static List<String> getDimList(){
        return Lists.newArrayList("ttd_suc_income","ttd_trgt_income");
    }

    public static List<String> getSingleDimList(){
        return Lists.newArrayList("ttd_suc_income");
    }

    public static List<String> getVirtualDimList(){
        return Lists.newArrayList("ttd_suc_income_virtual");
    }

    public static List<String> getTargetDimList() {
        return Lists.newArrayList("ttd_trgt_income");
    }

    public static List<String> getAllTargetDimList() {
        return Lists.newArrayList("ttd_trgt_income", "ttd_trgt_profit");
    }

    public static void makeUpMetricCardData(Map<String, Double> dimMap) {
        String extraDim1 = "ttd_suc_income|ttd_trgt_income|/";
        Double dim1Fenzi = dimMap.get("ttd_suc_income");
        Double dim1Fenmu = dimMap.get("ttd_trgt_income");
        if (GeneralUtil.isValidDivide(dim1Fenzi, dim1Fenmu)) {
            dimMap.put(extraDim1, dim1Fenzi / dim1Fenmu);
        }
    }

    public static void makeUpTableData(List<TableDataItem> tableDataItemList) {
        for (TableDataItem item : tableDataItemList) {
            makeUpMetricCardData(item.getDimMap());
        }
    }

    public static Map<String, String> getLineChartTrendlineType() {
        Map<String, String> typeMap = new HashMap<>();
        //这里map的顺序不能变  必须先把原始值算出来
        typeMap.put("ttd_suc_income", "barChart");
        typeMap.put("ttd_trgt_income", "barChart");
        typeMap.put("ttd_suc_income|ttd_trgt_income|/", "lineChart");
        return typeMap;
    }

//    public static Map<String, String> getLineChartPopTrendlineType() {
//        Map<String, String> typeMap = new LinkedHashMap<>();
//        //这里map的顺序不能变  必须先把原始值算出来
//        typeMap.put("ttd_suc_income_lastyear", "lineChart");
//        typeMap.put("ttd_suc_income_2019", "lineChart");
//        return typeMap;
//    }


    public static Map<String, String> getLineChartLastYearTrendlineType() {
        Map<String, String> typeMap = new LinkedHashMap<>();
        typeMap.put("ttd_suc_income_lastyear", "lineChart");
        typeMap.put("ttd_suc_income_lastyear_value","lineChart");
        return typeMap;
    }

    public static Map<String, String> getLineChart2019TrendlineType() {
        Map<String, String> typeMap = new LinkedHashMap<>();
        typeMap.put("ttd_suc_income_2019", "lineChart");
        typeMap.put("ttd_suc_income_2019_value","lineChart");
        return typeMap;
    }


    public static void makeUpMetricCardPopData(Map<String, Double> dimMap,
                                               Map<String, Double> popDimMap,
                                               String dimSuffix) {
        String dim = "ttd_suc_income";
        String actualDimPop = dim + dimSuffix;
        String actualDimValue = actualDimPop + "_value";
        Double value = DimHelper.getSpecialDimValue(actualDimPop, "", dimMap, popDimMap);
        dimMap.put(actualDimPop, value);
        dimMap.put(actualDimValue, popDimMap.get(dim));
    }


    //根据考核层级确定可下钻维度
    public static List<String> getFieldList(String level,
                                            MetricInfoBean metricInfoBean) {
        List<String> regionList = metricInfoBean.getRegionList();
        switch (level) {
            case "国内":
            case "三方":
                return Lists.newArrayList("region_name", "province_name", "examinee", "viewspotid");
            case "大区":
                return Lists.newArrayList("province_name", "examinee", "viewspotid");
            case "省份":
                if(regionList.size() > 1){
                    return Lists.newArrayList("province_name", "examinee", "viewspotid");
                }else{
                    return Lists.newArrayList("examinee", "viewspotid");
                }
            case "景点":
                return Lists.newArrayList("examinee", "viewspotid");
            default:
                throw new ConfigImportException("1 config have error level:" + level);
        }
    }


}
