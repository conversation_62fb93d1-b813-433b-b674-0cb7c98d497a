package com.ctrip.tour.business.dashboard.tktBusiness.dao;

import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.StatementParameters;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.ctrip.tour.business.dashboard.utils.SqlUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/8/1
 */
@Repository
public class Bus4Dao {

    @Autowired
    private GeneralDao generalDao;



    //获取选定周期分层进展和目标
//    public List<List<Object>> getScenicData(Map<String, List<String>> inMap,
//                                            List<String> groupTagList) throws SQLException {
//        StringBuilder sb = new StringBuilder();
//        sb.append("select ");
//        SqlUtil.jointGroupCondition(sb, false, groupTagList);
//        sb.append(",sum(ttd_online_sign_scenic_cnt) as ttd_online_sign_scenic_cnt,sum(ttd_trgt_sign) as ttd_trgt_sign ");
//        sb.append(" from bus_4_scenic_sign_level_info_t_new ");
//        StatementParameters parameters = new StatementParameters();
//        SqlUtil.jointWhereCondition(sb, parameters, inMap);
//        SqlUtil.jointGroupCondition(sb, true, groupTagList);
//        return generalDao.getListResult(sb.toString(), parameters);
//    }


    //按月或者季获取分层直签景区数
    public List<List<Object>> getTrendlineSignScenicCnt(Map<String, List<String>> inMap,
                                                        List<String> groupTagList,
                                                        List<String> orderTagList) throws SQLException {
        StringBuilder sb = new StringBuilder();
        StatementParameters parameters = new StatementParameters();
        sb.append("select ");
        SqlUtil.jointGroupCondition(sb, false, groupTagList);
        sb.append(",sum(actual_sign_scenic_cnt) as actual_sign_scenic_cnt ");
        sb.append(" from bus_4_level_scenic_sign_info_t ");
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        SqlUtil.jointGroupCondition(sb, true, groupTagList);
        SqlUtil.jointOrderCondition(sb, orderTagList, "asc");
        return generalDao.getListResult(sb.toString(), parameters);
    }




    //按时间周期获取分层数据
    public List<List<Object>> getLevelScenicTableData(Map<String, List<String>> inMap,
                                                      List<String> groupTagList,
                                                      List<String> orderTagList,
                                                      Integer pageNo,
                                                      Integer pageSize) throws SQLException {
        StringBuilder sb = new StringBuilder();
        StatementParameters parameters = new StatementParameters();
        sb.append("select ");
        SqlUtil.jointGroupCondition(sb, false, groupTagList);
        //分层实际  分层目标
        //分层差值  分层藏宝图
        //分层直签率
        sb.append(",sum(actual_sign_scenic_cnt) as actual_sign_scenic_cnt,sum(target_sign_scenic_cnt) as target_sign_scenic_cnt");
        sb.append(",sum(gap_sign_scenic_cnt) as gap_sign_scenic_cnt,sum(treasuremap_scenic_cnt) as treasuremap_scenic_cnt");
        sb.append(",sum(actual_sign_scenic_cnt)/sum(treasuremap_scenic_cnt) as ttd_cps_sign_rate ");
        sb.append(" from bus_4_level_scenic_sign_info_t ");
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        SqlUtil.jointGroupCondition(sb, true, groupTagList);
        SqlUtil.jointOrderCondition(sb, orderTagList, "asc");
        SqlUtil.jointPagingCondition(sb, parameters, pageNo, pageSize);
        return generalDao.getListResult(sb.toString(), parameters);
    }

    //按时间周期获取分层数据(指标卡用)
    public List<List<Object>> getScenicMetricData(Map<String, List<String>> inMap,
                                                      List<String> groupTagList,
                                                      List<String> orderTagList) throws SQLException {
        StringBuilder sb = new StringBuilder();
        StatementParameters parameters = new StatementParameters();
        sb.append("select ");
        SqlUtil.jointGroupCondition(sb, false, groupTagList);
        //分层实际  分层目标
        //分层差值  分层藏宝图
        //分层直签率
        sb.append(",sum(actual_sign_scenic_cnt) as actual_sign_scenic_cnt,sum(target_sign_scenic_cnt) as target_sign_scenic_cnt");
        sb.append(",sum(gap_sign_scenic_cnt) as gap_sign_scenic_cnt,sum(treasuremap_scenic_cnt) as treasuremap_scenic_cnt");
        sb.append(" from bus_4_level_scenic_sign_info_t ");
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        SqlUtil.jointGroupCondition(sb, true, groupTagList);
        SqlUtil.jointOrderCondition(sb, orderTagList, "asc");
        return generalDao.getListResult(sb.toString(), parameters);
    }



    //获取表格数据总条数
    public Integer getTableReachDataCount(Map<String, List<String>> inMap,
                                          List<String> tagList) throws SQLException {
        StringBuilder sb = new StringBuilder();
        StatementParameters parameters = new StatementParameters();
        sb.append("select count(*) from ( select ");
        SqlUtil.jointGroupCondition(sb, false, tagList);
        //分层实际  分层目标
        //分层差值
        //分层直签率
        sb.append(",sum(actual_sign_scenic_cnt) as actual_sign_scenic_cnt,sum(target_sign_scenic_cnt) as target_sign_scenic_cnt");
        sb.append(",sum(gap_sign_scenic_cnt) as gap_sign_scenic_cnt,sum(treasuremap_scenic_cnt) as treasuremap_scenic_cnt");
        sb.append(",sum(actual_sign_scenic_cnt)/sum(treasuremap_scenic_cnt) as ttd_cps_sign_rate ");
        sb.append(" from bus_4_level_scenic_sign_info_t ");
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        SqlUtil.jointGroupCondition(sb, true, tagList);
        SqlUtil.jointOrderCondition(sb, tagList, "asc");
        sb.append(" ) aa");
        List<List<Object>> resultList = generalDao.getListResult(sb.toString(), parameters);
        return Integer.valueOf(String.valueOf(resultList.get(0).get(0)));
    }



    public List<List<Object>> getFieldList(Map<String, List<String>> inMap,
                                           Map<String, String> likeMap,
                                           List<String> tagList) throws SQLException {
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        SqlUtil.jointGroupCondition(sb, false, tagList);
        sb.append(" from bus_4_level_scenic_sign_info_t ");
        StatementParameters parameters = new StatementParameters();
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        if (!GeneralUtil.isEmpty(likeMap)) {
            SqlUtil.jointLikeCondition(sb, parameters, likeMap, true);
        }
        SqlUtil.jointGroupCondition(sb, true, tagList);
        return generalDao.getListResult(sb.toString(), parameters);
    }

    public void getRankAsync(Map<String, List<String>> inMap,
                             DalHints dalHints) throws SQLException {
        StringBuilder sb = new StringBuilder();
        sb.append("select ranking  from bus_4_8_9_10_rank_t ");
        StatementParameters parameters = new StatementParameters();
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        generalDao.getListResultAsync(sb.toString(), parameters, dalHints);
    }


    /**
     * 获取某个周期的完成率数据(也适用于下钻)
     * @param inMap
     * @param innerGroupTagList
     * @param outerGroupTagList
     * @return
     * @throws SQLException
     */
//    public List<List<Object>> getCompleteRateTrendlineData(Map<String, List<String>> inMap,
//                                                           List<String> innerGroupTagList,
//                                                           List<String> outerGroupTagList) throws SQLException {
//        StringBuilder sb = new StringBuilder();
//        sb.append("select ");
//        if (!GeneralUtil.isEmpty(outerGroupTagList)) {
//            SqlUtil.jointGroupCondition(sb, false, outerGroupTagList);
//            sb.append(",");
//        }
//        sb.append("COALESCE(sum(ttd_weight1_sign_score)/sum(ttd_trgt_weight_sign),0) as ttd_weight1_sign_score_divide_ttd_trgt_weight_sign from (");
//        sb.append("select ");
//        SqlUtil.jointGroupCondition(sb, false, innerGroupTagList);
//        sb.append(",sum(scenic_class_weight) * sum(ttd_online_sign_scenic_cnt) AS ttd_weight1_sign_score, sum(scenic_class_weight) * sum(ttd_trgt_sign) AS ttd_trgt_weight_sign ");
//        sb.append(" from (");
//        sb.append("        select ");
//        SqlUtil.jointGroupCondition(sb, false, innerGroupTagList);
//        sb.append(",sum(ttd_online_sign_scenic_cnt) AS ttd_online_sign_scenic_cnt, sum(ttd_trgt_sign) AS ttd_trgt_sign" +
//                ",scenic_class_weight from bus_4_scenic_sign_level_info_t_new ");
//        StatementParameters parameters = new StatementParameters();
//        SqlUtil.jointWhereCondition(sb, parameters, inMap);
//        SqlUtil.jointGroupCondition(sb, true, innerGroupTagList);
//        sb.append(",scenic_class_weight");
//        sb.append("      ) aa ");
//        SqlUtil.jointGroupCondition(sb, true, innerGroupTagList);
//        sb.append("  ) bb ");
//        if (!GeneralUtil.isEmpty(outerGroupTagList)) {
//            SqlUtil.jointGroupCondition(sb, true, outerGroupTagList);
//        }
//        return generalDao.getListResult(sb.toString(), parameters);
//    }


    /**
     * 获取某个周期的加权达成分 加权目标分 增量完成率(也适用于下钻)
     * @param inMap
     * @param groupTagList
     * @return
     */
    public List<List<Object>> getScenicSignData(Map<String, List<String>> inMap,
                                                List<String> groupTagList) throws SQLException {
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        if (!GeneralUtil.isEmpty(groupTagList)) {
            SqlUtil.jointGroupCondition(sb, false, groupTagList);
            sb.append(",");
        }
        sb.append("sum(complete_score) as complete_score,sum(target_score) as target_socre,sum(delta_complete_score)/sum(delta_target_score) as delta_complete_rate ");
        sb.append("from bus_4_scenic_sign_info_t ");
        StatementParameters parameters = new StatementParameters();
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        if (!GeneralUtil.isEmpty(groupTagList)) {
            SqlUtil.jointGroupCondition(sb, true, groupTagList);
        }
        return generalDao.getListResult(sb.toString(), parameters);
    }


    /**
     * 获取某个周期的完成率数据(也适用于下钻)
     * @param inMap
     * @param innerGroupTagList
     * @param outerGroupTagList
     * @return
     * @throws SQLException
     */
//    public List<List<Object>> getWightScenicData(Map<String, List<String>> inMap,
//                                                 List<String> innerGroupTagList,
//                                                 List<String> outerGroupTagList) throws SQLException {
//        StringBuilder sb = new StringBuilder();
//        sb.append("select ");
//        if (!GeneralUtil.isEmpty(outerGroupTagList)) {
//            SqlUtil.jointGroupCondition(sb, false, outerGroupTagList);
//            sb.append(",");
//        }
//        sb.append("sum(ttd_weight1_sign_score) as ttd_weight1_sign_score,sum(ttd_trgt_weight_sign) as ttd_trgt_weight_sign,COALESCE(sum(ttd_weight1_sign_score)/sum(ttd_trgt_weight_sign),0) as ttd_weight1_sign_score_divide_ttd_trgt_weight_sign from (");
//        sb.append("select ");
//        SqlUtil.jointGroupCondition(sb, false, innerGroupTagList);
//        sb.append(",sum(scenic_class_weight) * sum(ttd_online_sign_scenic_cnt) AS ttd_weight1_sign_score, sum(scenic_class_weight) * sum(ttd_trgt_sign) AS ttd_trgt_weight_sign ");
//        sb.append(" from (");
//        sb.append("        select ");
//        SqlUtil.jointGroupCondition(sb, false, innerGroupTagList);
//        sb.append(",sum(ttd_online_sign_scenic_cnt) AS ttd_online_sign_scenic_cnt, sum(ttd_trgt_sign) AS ttd_trgt_sign" +
//                ",scenic_class_weight from bus_4_scenic_sign_level_info_t_new ");
//        StatementParameters parameters = new StatementParameters();
//        SqlUtil.jointWhereCondition(sb, parameters, inMap);
//        SqlUtil.jointGroupCondition(sb, true, innerGroupTagList);
//        sb.append(",scenic_class_weight");
//        sb.append("      ) aa ");
//        SqlUtil.jointGroupCondition(sb, true, innerGroupTagList);
//        sb.append("  ) bb ");
//        if (!GeneralUtil.isEmpty(outerGroupTagList)) {
//            SqlUtil.jointGroupCondition(sb, true, outerGroupTagList);
//        }
//        return generalDao.getListResult(sb.toString(), parameters);
//    }
}
