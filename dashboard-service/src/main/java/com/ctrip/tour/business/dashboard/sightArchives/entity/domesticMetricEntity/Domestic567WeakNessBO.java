package com.ctrip.tour.business.dashboard.sightArchives.entity.domesticMetricEntity;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;

@Entity
@Getter
@Setter
public class Domestic567WeakNessBO {
    long provinceId;
    String provinceName;
    String businessRegionId;
    String businessRegionName;
    //	考核对象-商拓
    String examineObject;
    String examineName;
    //5
    //核心-景点劣势率
    Double cwViewspotRate;
    //聚焦-景点劣势率
    Double fwViewspotRate;
    //长尾高价值-景点劣势率
    Double lhvwViewspotRate;
    //长尾其他-景点劣势率
    Double lowViewspotRate;
    //6
    //核心-票种劣势率
    Double cwSaleunitRate;
    //聚焦-票种劣势率
    Double fwSaleunitRate;
    //长尾高价值-票种劣势率
    Double lhvwSaleunitRate;
    //长尾其他-票种劣势率
    Double lowSaleunitRate;
    //7
    //核心-商品劣势率
    Double cwCommRate;
    //聚焦-商品劣势率
    Double fwCommRate;
    //长尾高价值-商品劣势率
    Double lhvwCommRate;
    //长尾其他-商品劣势率
    Double lowCommRate;
}
