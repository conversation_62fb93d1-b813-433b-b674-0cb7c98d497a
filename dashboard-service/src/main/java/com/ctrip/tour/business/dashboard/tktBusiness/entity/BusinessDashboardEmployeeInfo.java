package com.ctrip.tour.business.dashboard.tktBusiness.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import java.sql.Types;
import java.sql.Timestamp;

import com.ctrip.platform.dal.dao.DalPojo;

/**
 * <AUTHOR>
 * @date 2022-07-20
 */
@Entity
@Database(name = "TtdReportDB_W")
@Table(name = "business_dashboard_employee_info")
public class BusinessDashboardEmployeeInfo implements DalPojo {

    /**
     * 主键
     */
    @Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long id;

    /**
     * 工号
     */
	@Column(name = "emp_code")
	@Type(value = Types.VARCHAR)
	private String empCode;

    /**
     * 姓名
     */
	@Column(name = "display_name")
	@Type(value = Types.VARCHAR)
	private String displayName;

    /**
     * 域账号(邮箱前缀)
     */
	@Column(name = "domain_name")
	@Type(value = Types.VARCHAR)
	private String domainName;

    /**
     * 主管工号
     */
	@Column(name = "leader_emp_code")
	@Type(value = Types.VARCHAR)
	private String leaderEmpCode;

    /**
     * 主管姓名
     */
	@Column(name = "leader_emp_name")
	@Type(value = Types.VARCHAR)
	private String leaderEmpName;

    /**
     * 当前小组编码
     */
	@Column(name = "team_id")
	@Type(value = Types.VARCHAR)
	private String teamId;

    /**
     * 当前小组中文名
     */
	@Column(name = "team_cname")
	@Type(value = Types.VARCHAR)
	private String teamCname;

    /**
     * 组织ID路径
     */
	@Column(name = "org_id_path")
	@Type(value = Types.VARCHAR)
	private String orgIdPath;

    /**
     * 组织路径
     */
	@Column(name = "org_name_path")
	@Type(value = Types.VARCHAR)
	private String orgNamePath;

    /**
     * 岗位
     */
	@Column(name = "position")
	@Type(value = Types.VARCHAR)
	private String position;

    /**
     * 创建人
     */
	@Column(name = "DataChange_CreateUser")
	@Type(value = Types.VARCHAR)
	private String datachangeCreateuser;

    /**
     * 创建时间
     */
	@Column(name = "DataChange_CreateTime")
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeCreatetime;

    /**
     * 最后修改人
     */
	@Column(name = "DataChange_LastUser")
	@Type(value = Types.VARCHAR)
	private String datachangeLastuser;

    /**
     * 最后修改时间
     */
	@Column(name = "DataChange_LastTime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getEmpCode() {
		return empCode;
	}

	public void setEmpCode(String empCode) {
		this.empCode = empCode;
	}

	public String getDisplayName() {
		return displayName;
	}

	public void setDisplayName(String displayName) {
		this.displayName = displayName;
	}

	public String getDomainName() {
		return domainName;
	}

	public void setDomainName(String domainName) {
		this.domainName = domainName;
	}

	public String getLeaderEmpCode() {
		return leaderEmpCode;
	}

	public void setLeaderEmpCode(String leaderEmpCode) {
		this.leaderEmpCode = leaderEmpCode;
	}

	public String getLeaderEmpName() {
		return leaderEmpName;
	}

	public void setLeaderEmpName(String leaderEmpName) {
		this.leaderEmpName = leaderEmpName;
	}

	public String getTeamId() {
		return teamId;
	}

	public void setTeamId(String teamId) {
		this.teamId = teamId;
	}

	public String getTeamCname() {
		return teamCname;
	}

	public void setTeamCname(String teamCname) {
		this.teamCname = teamCname;
	}

	public String getOrgIdPath() {
		return orgIdPath;
	}

	public void setOrgIdPath(String orgIdPath) {
		this.orgIdPath = orgIdPath;
	}

	public String getOrgNamePath() {
		return orgNamePath;
	}

	public void setOrgNamePath(String orgNamePath) {
		this.orgNamePath = orgNamePath;
	}

	public String getPosition() {
		return position;
	}

	public void setPosition(String position) {
		this.position = position;
	}

	public String getDatachangeCreateuser() {
		return datachangeCreateuser;
	}

	public void setDatachangeCreateuser(String datachangeCreateuser) {
		this.datachangeCreateuser = datachangeCreateuser;
	}

	public Timestamp getDatachangeCreatetime() {
		return datachangeCreatetime;
	}

	public void setDatachangeCreatetime(Timestamp datachangeCreatetime) {
		this.datachangeCreatetime = datachangeCreatetime;
	}

	public String getDatachangeLastuser() {
		return datachangeLastuser;
	}

	public void setDatachangeLastuser(String datachangeLastuser) {
		this.datachangeLastuser = datachangeLastuser;
	}

	public Timestamp getDatachangeLasttime() {
		return datachangeLasttime;
	}

	public void setDatachangeLasttime(Timestamp datachangeLasttime) {
		this.datachangeLasttime = datachangeLasttime;
	}

}
