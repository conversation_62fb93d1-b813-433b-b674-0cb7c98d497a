package com.ctrip.tour.business.dashboard.utils;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;

public class FileUtil {

    /**
     * 读文件
     * @param filePath
     * @return
     * @throws IOException
     */
    public static byte[] readFile(String filePath) throws IOException {
        File file = new File(filePath); // 文件路径
        return Files.readAllBytes(file.toPath()); // 读取文件内容到byte数组
    }


    /**
     * 删除文件
     * @param filePath
     * @return
     */
    public static boolean deleteFile(String filePath) {
        try{
            File file = new File(filePath);
            // 判断文件是否存在
            if (file.exists()) {
                // 删除文件
                file.delete();
            }
            return true;
        }catch (Exception e){
            return false;
        }
    }
}
