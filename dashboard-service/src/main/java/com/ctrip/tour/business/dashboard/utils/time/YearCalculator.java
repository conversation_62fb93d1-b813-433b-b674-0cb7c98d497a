package com.ctrip.tour.business.dashboard.utils.time;

import org.joda.time.DateTime;
import org.joda.time.Days;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 年度计算器类，用于处理年度相关的日期计算、转换及比较
 * 格式示例：2023(2023年全年)
 */
@Data
@AllArgsConstructor
public class YearCalculator implements Cloneable, Comparable<YearCalculator> {
    private Integer year;
    private static final DateTimeFormatter YYYY_MM_DD_FORMATTER = DateTimeFormat.forPattern("yyyy-MM-dd");

    /**
     * 默认构造方法，初始化当前年度
     */
    public YearCalculator() {
        this.year = DateTime.now().getYear();
    }

    /**
     * 根据年份字符串创建YearCalculator实例
     *
     * @param yearString 年份字符串，格式：yyyy
     * @return YearCalculator实例
     */
    public static YearCalculator getByYearString(String yearString) {
        try {
            int year = Integer.parseInt(yearString);
            return new YearCalculator(year);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Invalid year string format, should be yyyy", e);
        }
    }

    /**
     * 获取年度字符串（格式：yyyy）
     *
     * @return 年度字符串，若年为null则返回null
     */
    public String getYearString() {
        return year != null ? year.toString() : null;
    }

    /**
     * 获取年度的第一天（格式：yyyy-MM-dd）
     *
     * @return 年度第一天字符串
     */
    public String getDateStart() {
        return year + "-01-01";
    }

    /**
     * 获取年度的最后一天（格式：yyyy-MM-dd）
     *
     * @return 年度最后一天字符串
     */
    public String getDateEnd() {
        return year + "-12-31";
    }

    /**
     * 获取去年同期年度的YearCalculator实例
     *
     * @return 去年同期年度实例
     */
    public YearCalculator getLastYearSameYear() {
        YearCalculator clone = this.clone();
        clone.year -= 1;
        return clone;
    }

    /**
     * 获取同比日期范围（不考虑时间窗口概念）
     *
     * @return 同比日期范围
     */
    public DateRange getYoYDateRange() {
        YearCalculator lastYearSameYear = getLastYearSameYear();
        return new DateRange(lastYearSameYear.getDateStart(), lastYearSameYear.getDateEnd());
    }

    /**
     * 获取同比日期范围（考虑时间窗口概念）
     *
     * @param windowDate 窗口日期，格式：yyyy-MM-dd
     * @return 对齐时间窗口的同比日期范围
     */
    public DateRange getYoYDateRangeWithWindow(String windowDate) {
        YearCalculator lastYearSameYear = getLastYearSameYear();
        DateTime windowDateTime = YYYY_MM_DD_FORMATTER.parseDateTime(windowDate);
        DateTime currentYearEnd = YYYY_MM_DD_FORMATTER.parseDateTime(getDateEnd());

        // 如果窗口日期在当前年度结束后，返回完整年度
        if (windowDateTime.isAfter(currentYearEnd)) {
            return new DateRange(lastYearSameYear.getDateStart(), lastYearSameYear.getDateEnd());
        }

        // 计算当前年度已过去的天数
        DateTime currentYearStart = YYYY_MM_DD_FORMATTER.parseDateTime(getDateStart());
        int daysPassed = Days.daysBetween(currentYearStart, windowDateTime).getDays() + 1;

        // 计算去年同期年度对应的结束日期
        DateTime yoyStartDate = YYYY_MM_DD_FORMATTER.parseDateTime(lastYearSameYear.getDateStart());
        DateTime yoyEndDate = yoyStartDate.plusDays(daysPassed - 1);

        // 处理天数溢出情况：确保不超过目标年度的最后一天
        DateTime targetYearEnd = YYYY_MM_DD_FORMATTER.parseDateTime(lastYearSameYear.getDateEnd());
        if (yoyEndDate.isAfter(targetYearEnd)) {
            yoyEndDate = targetYearEnd;
        }

        return new DateRange(lastYearSameYear.getDateStart(), yoyEndDate.toString(YYYY_MM_DD_FORMATTER));
    }

    /**
     * 克隆当前实例
     *
     * @return 克隆后的YearCalculator实例
     */
    @Override
    protected YearCalculator clone() {
        return new YearCalculator(year);
    }

    /**
     * 比较两个YearCalculator实例（按年升序）
     *
     * @param o 待比较的实例
     * @return 比较结果（-1：当前实例小，0：相等，1：当前实例大）
     */
    @Override
    public int compareTo(YearCalculator o) {
        return this.year - o.getYear();
    }

    /**
     * 判断两个YearCalculator实例是否相等（基于年）
     *
     * @param o 待比较对象
     * @return 是否相等
     */
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof YearCalculator)) return false;

        YearCalculator that = (YearCalculator) o;

        return getYear() != null ? getYear().equals(that.getYear()) : that.getYear() == null;
    }

    /**
     * 计算哈希码（基于年）
     *
     * @return 哈希码
     */
    @Override
    public int hashCode() {
        return getYear() != null ? getYear().hashCode() : 0;
    }

    /**
     * 转换为字符串（返回年度字符串）
     *
     * @return 年度字符串（格式：yyyy）
     */
    @Override
    public String toString() {
        return getYearString();
    }

    public void setYear(int year) {
        if (year < 1970 || year > 2100) {
            throw new IllegalArgumentException("Invalid year parameter!"); // 不合法的年份参数！ → Invalid year parameter!
        }
        this.year = year;
    }
}
