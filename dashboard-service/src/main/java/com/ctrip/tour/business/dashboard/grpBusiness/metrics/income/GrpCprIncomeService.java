package com.ctrip.tour.business.dashboard.grpBusiness.metrics.income;


import com.ctrip.platform.dal.dao.helper.DalColumnMapRowMapper;
import com.ctrip.tour.business.dashboard.grpBusiness.annotation.IndexAssemblyHandler;
import com.ctrip.tour.business.dashboard.grpBusiness.dao.starrocks.AdmOrdGrpEorkPlatformPrdtDfDao;
import com.ctrip.tour.business.dashboard.grpBusiness.enums.MetricCategoryEnum;
import com.ctrip.tour.business.dashboard.grpBusiness.metrics.IndexCommonQueryAbstractSerice;
import com.ctrip.tour.business.dashboard.utils.SqlBuilder;
import com.google.common.base.Joiner;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

@Service
@IndexAssemblyHandler(calcDateName = "dep_date",
         calcFieldName = "result_value",
        tableName = "adm_ord_grp_work_platform_prdt_df")
@Slf4j
public class GrpCprIncomeService extends IndexCommonQueryAbstractSerice {
    @Autowired
    AdmOrdGrpEorkPlatformPrdtDfDao orderDfDao;

    public GrpCprIncomeService(ApplicationContext ac) {
        super(ac);
    }

    @Override
    protected String otherCon(Map<String, ?> param, List<String> groupByCols, String timeAggType) {
        return doOtherCon(param, MetricCategoryEnum.INCOME_CATEGORY.getEnglishName());
    }

    @Override
    protected SqlBuilder selectColsAssembly(SqlBuilder sqlBuilder, Map<String, ?> param, List<String> groupByCols, String timeAggType) {

        String sql = " sum(suc_income) as result_value";

        if (CollectionUtils.isNotEmpty(groupByCols)) {
            sql = String.join(",", sql, Joiner.on(",").skipNulls().join(groupByCols));
        }

        sqlBuilder.select(sql);

        return sqlBuilder;
    }

    @Override
    protected List<Map<String, Object>> queryData(String sql, Map<String, ?> param, List<String> groupByCols, String timeAggType) {
        List<Map<String, Object>> queryData = null;
        try {
            DalColumnMapRowMapper dalColumnMapRowMapper = new DalColumnMapRowMapper();
            queryData = orderDfDao.query(sql, param);
            return queryData;
        } catch (SQLException e) {
            log.warn("query adm_ord_grp_work_platform_prdt_df error", e);

        }
        return null;
    }

    @Override
    protected List<Map<String, Object>> handleResults(IndexCommonQueryAbstractSerice serice, Map<String, ?> param, String timeAggType, boolean needTimeAgg, List<Map<String, Object>> rowData, List<String> groupCols) {
        return doHandleResults(serice, param, timeAggType, needTimeAgg, rowData);
    }
}
