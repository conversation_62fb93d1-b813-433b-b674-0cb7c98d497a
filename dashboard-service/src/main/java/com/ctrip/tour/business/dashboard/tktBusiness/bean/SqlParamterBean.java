package com.ctrip.tour.business.dashboard.tktBusiness.bean;

import com.ctrip.soa._27181.GetRawDataRequestType;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;


@Getter
@Setter
public class SqlParamterBean {
    //获取数据的id
    private Long id;
    //获取数据的andMap
    private Map<String,String> andMap;
    //获取数据的notInMap
    private Map<String,String> notInMap;
    //获取环比数据的andMap2
    private Map<String,String> andMap2;
    //获取数据的notInMap2
    private Map<String,String> notInMap2;
    //获取数据的likeMap
    private Map<String,String> likeMap;
    //获取数据的notLikeMap
    private Map<String,String> notLikeMap;
    //获取数据的orMap
    Map<String, String> orMap;
//    //获取数据的likeMap
//    private Map<String,String> likeMap2;
//    //获取数据的notLikeMap
//    private Map<String,String> notLikeMap2;
    //获取数据的groupList
    private List<String> groupList;
    //获取数据的orderList
    private List<String> orderList;
    //获取数据的orderTypeList
    private List<String> orderTypeList;
    //分页参数
    Integer pageNo;
    Integer pageSize;

    public GetRawDataRequestType convertBeanToRequest(Boolean needScript){
        GetRawDataRequestType request = new GetRawDataRequestType();
        request.setQueryId(id);
        request.setAndMap(andMap);
        request.setNeedScript(needScript);
        //扩展参数
        request.setLikeMap(likeMap);
        request.setGroupList(groupList);
        request.setOrderList(orderList);
        request.setOrderTypeList(orderTypeList);
        request.setPageNo(pageNo);
        request.setPageSize(pageSize);
        request.setNotInMap(notInMap);
        request.setNotLikeMap(notLikeMap);
        request.setOrMap(orMap);
        return request;
    }

    public GetRawDataRequestType convertBeanToRequest2(Boolean needScript){
        GetRawDataRequestType request = new GetRawDataRequestType();
        request.setQueryId(id);
        request.setAndMap(andMap2);
        request.setNeedScript(needScript);
        //扩展参数
        request.setGroupList(groupList);
        request.setOrderList(orderList);
        request.setOrderTypeList(orderTypeList);
        request.setNotInMap(notInMap2);
        return request;
    }
}
