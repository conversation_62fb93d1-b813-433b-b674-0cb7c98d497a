package com.ctrip.tour.business.dashboard.helper

import com.ctrip.soa._24922.TimeFilter
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ExamineConfigBean
import com.ctrip.tour.business.dashboard.tktBusiness.helper.Bus11Helper
import spock.lang.Specification
import spock.lang.Unroll

class Bus11HelperTest extends Specification {

    @Unroll
    def "getAddtionalGapDay"() {
        when: "请求接口"
        def result = Bus11Helper.getAddtionalGapDay(timeFilter, examineConfigBean, "2024-01-16",baseMap)

        then: "验证多种返回结果"
        with(result) {
            result == 0
            baseMap.size() == 0
        }


        where: "用表格方式验证多种返回结果"
        baseMap         | timeFilter                                                   | examineConfigBean
        new HashMap<>() | new TimeFilter(year: "2023", dateType: "month", month: "12") | null
        new HashMap<>() | new TimeFilter(year: "2024", dateType: "month", month: "01") | null
        new HashMap<>() | null                                                         | new ExamineConfigBean(year: "2024", month: "01", quarter: null, timeMap: null, isLastestPeriod: false, limitTimeMap: null, businessDashboardExamineeConfigV2: null, businessDashboardOverseaExamineeConfig: null, dateType: "month")
        new HashMap<>() | null                                                         | new ExamineConfigBean(year: "2023", month: "12", quarter: null, timeMap: null, isLastestPeriod: false, limitTimeMap: null, businessDashboardExamineeConfigV2: null, businessDashboardOverseaExamineeConfig: null, dateType: "month")
    }
}
