package com.ctrip.tour.business.dashboard.tktBusiness.biz;

import com.ctrip.soa._24922.*;

public interface KeyProjectDashboardBiz {

    CheckEmpBusinessTypeResponseType checkEmpBusinessType(CheckEmpBusinessTypeRequestType checkEmpBusinessTypeRequestType) throws Exception;

    CheckEmpBusinessTypeResponseType checkEmpBusinessTypeNew(CheckEmpBusinessTypeRequestType checkEmpBusinessTypeRequestType) throws Exception;


    GetKeyProjectDashboardConfigResponseType getKeyProjectDashboardConfig(GetKeyProjectDashboardConfigRequestType getKeyProjectDashboardConfigRequestType);

    GetKeyProjectDashboardFilterEnumsResponseType getKeyProjectDashboardFilterEnums(GetKeyProjectDashboardFilterEnumsRequestType getKeyProjectDashboardFilterEnumsRequestType) throws Exception;
}
