package com.ctrip.tour.business.dashboard.grpBusiness.metrics;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2024/12/9
 */
@Component
public interface IndexCommonQueryService {



//    List<Map<String, Object>> queryByCondition(IndexCommonQueryAbstractSerice serice, Map<String, ?> param, List<String> groupByCols,
//                                                      String timeAggType, boolean needTimeAgg);
//
//    List<Map<String, Object>> queryBySql(IndexCommonQueryAbstractSerice serice,String sql, Map<String, ?> param, List<String> groupByCols,
//                                               String timeAggType, boolean needTimeAgg);

    List<Map<String, Object>> queryCardData(IndexCommonQueryAbstractSerice serice, Map<String, ?> param, List<String> groupByCols);

    List<Map<String, Object>> queryDillDownData(IndexCommonQueryAbstractSerice serice, Map<String, ?> param, List<String> groupByCols);

    Map<String, Object> queryTrendLineData(IndexCommonQueryAbstractSerice serice, Map<String, ?> param, List<String> groupByCols, String timeAggType);

}
