package com.ctrip.tour.business.dashboard.grpBusiness.domain.process.dsl.diy;

import com.ctrip.soa._24922.DSLRequestType;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.AbstractPreDSLProcess;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.process.dsl.common.DSLProcessEmps;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;


/**
 * 指标配置
 */
public class DSLProcessDIYIndicatorInject extends AbstractPreDSLProcess {
    private static volatile DSLProcessDIYIndicatorInject instance;

    public static AbstractPreDSLProcess getInstance() {
        if (instance == null) { // 第一次检查
            synchronized (DSLProcessEmps.class) { // 加锁
                if (instance == null) { // 第二次检查
                    instance = new DSLProcessDIYIndicatorInject();
                }
            }
        }
        return instance;
    }

    @Override
    public DSLRequestType process(DSLRequestType dslRequestType, EarlyReturn earlyReturn) {
        // cpr/emp
        Set<String> vendors = new HashSet<>(Arrays.asList("vendor_id", "vendor_name"));
        boolean containsVendors = false;
        if (dslRequestType.getGroupBy() != null && !dslRequestType.getGroupBy().isEmpty()) {
            for (String s : dslRequestType.getGroupBy()) {
                containsVendors = vendors.contains(s) || containsVendors;
            }
        }
        if (containsVendors) {
            dslRequestType.getGroupBy().addAll(vendors);
        }

        // cpr/emp
        Set<String> emps = new HashSet<>(Arrays.asList("pm_eid", "pm_name"));
        boolean containsEmp = false;
        if (dslRequestType.getGroupBy() != null && !dslRequestType.getGroupBy().isEmpty()) {
            for (String s : dslRequestType.getGroupBy()) {
                containsEmp = emps.contains(s) || containsEmp;
            }
        }
        if (containsEmp) {
            dslRequestType.getGroupBy().addAll(emps);
        }

        // cpr/emp
        Set<String> locaEmps = new HashSet<>(Arrays.asList("local_pm_eid", "local_pm_name"));
        boolean containsLocaEmps = false;
        if (dslRequestType.getGroupBy() != null && !dslRequestType.getGroupBy().isEmpty()) {
            for (String s : dslRequestType.getGroupBy()) {
                containsLocaEmps = locaEmps.contains(s) || containsLocaEmps;
            }
        }
        if (containsLocaEmps) {
            dslRequestType.getGroupBy().addAll(locaEmps);
        }
        return dslRequestType;
    }
}
