package com.ctrip.tour.business.dashboard.sightArchives.dao.competitiveDao;


import com.ctrip.soa._24922.CoreTicketTypeCompetitive;
import com.ctrip.soa._24922.UnCoveredTicketType;
import com.ctrip.soa._27181.PreparedParameterBean;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.TktStarRocksDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.sql.Types;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

//携程飞猪对比表
@Repository
@Slf4j
public class AdmPrdTktTflyCmpInfoDfDao {

    //携程飞猪对比表 数仓侧设计文档：http://conf.ctripcorp.com/pages/viewpage.action?pageId=3661536861
    //dw_ticketdb.adm_prd_tkt_tfly_cmp_info_df

    @Autowired
    private TktStarRocksDao tktStarRocksDao;

    public List<UnCoveredTicketType> queryUncoveredTicketType(Long sightId, String startDate, String endDate, Integer pageNo, Integer pageSize){

        String sql = "select "
                + " rival_lv2_saleunit_id "
                + " ,rival_lv2_saleunit_name "
                + " ,cast(sum(is_cover_inferior) as Integer) as cover_inferior_count"
                + " from adm_prd_tkt_tfly_cmp_info_df"
                + " where is_cover_inferior = 1"
                + " and trip_viewspot_id = ?"
                + " and d between ? and ?"
                + " group by rival_lv2_saleunit_id,rival_lv2_saleunit_name"
                + " order by rival_lv2_saleunit_id"
                + " limit ?,?";

        List<PreparedParameterBean> parameters = new ArrayList<>();
        parameters.add(new PreparedParameterBean(String.valueOf(sightId), Types.BIGINT));
        parameters.add(new PreparedParameterBean(startDate, Types.VARCHAR));
        parameters.add(new PreparedParameterBean(endDate, Types.VARCHAR));
        parameters.add(new PreparedParameterBean(String.valueOf((pageNo-1)*pageSize), Types.INTEGER));
        parameters.add(new PreparedParameterBean(String.valueOf(pageSize), Types.INTEGER));

        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql,parameters);
        } catch (SQLException e) {
            log.warn("queryUncoveredTicketType error", e);
        }

        List<UnCoveredTicketType> uncoveredTicketTypeList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(result)){
            for(Map<String, Object> map : result){
                UnCoveredTicketType uncoveredTicketType = new UnCoveredTicketType();
//                uncoveredTicketType.setTicketTypeId((Long) map.getOrDefault("rival_lv2_saleunit_id", "0"));
                uncoveredTicketType.setTicketTypeName(String.valueOf(map.getOrDefault("rival_lv2_saleunit_name","")));
                uncoveredTicketType.setDisadvantageDays((Integer) map.getOrDefault("cover_inferior_count","0"));
                uncoveredTicketTypeList.add(uncoveredTicketType);
            }
        }

        return uncoveredTicketTypeList;
    }

    public Integer queryUncoveredTicketTypeTotalNum(Long sightId, String startDate, String endDate){


        String sql = "select count(1) as totalNum from ("
                + " select "
                + " rival_lv2_saleunit_id "
                + " ,rival_lv2_saleunit_name "
//                + " ,sum(is_cover_inferior) as cover_inferior_count"
                + " from adm_prd_tkt_tfly_cmp_info_df"
                + " where is_cover_inferior = 1"
                + " and trip_viewspot_id = ?"
                + " and d between ? and ?"
                + " group by rival_lv2_saleunit_id,rival_lv2_saleunit_name"
                + ") a";

        List<PreparedParameterBean> parameters = new ArrayList<>();
        parameters.add(new PreparedParameterBean(String.valueOf(sightId), Types.BIGINT));
        parameters.add(new PreparedParameterBean(startDate, Types.VARCHAR));
        parameters.add(new PreparedParameterBean(endDate, Types.VARCHAR));

        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql,parameters);
        } catch (SQLException e) {
            log.warn("queryUncoveredTicketTypeTotalNum error", e);
        }

        return CollectionUtils.isNotEmpty(result)? Math.toIntExact((Long) result.get(0).getOrDefault("totalNum","0")) :0;
    }


    public List<CoreTicketTypeCompetitive> queryCoreTicketType(Long sightId, String startDate, String endDate, Integer pageNo, Integer pageSize){
        String sql = "SELECT coalesce(fly.trip_lv2_saleunit_id, klk.trip_lv2_saleunit_id) AS trip_lv2_saleunit_id\n" +
                "    , coalesce(fly.trip_lv2_saleunit_name, klk.trip_lv2_saleunit_name) AS trip_lv2_saleunit_name, fly.cnt1 as cnt1\n" +
                "    , fly.cnt2  as cnt2, klk.cnt3  as cnt3, klk.cnt4  as cnt4\n" +
                "FROM (\n" +
                "    SELECT trip_lv2_saleunit_id, trip_lv2_saleunit_name, sum(is_comm_inferior) AS cnt1\n" +
                "        , count(DISTINCT d) AS cnt2\n" +
                "    FROM (\n" +
                "        SELECT DISTINCT trip_lv2_saleunit_id, trip_lv2_saleunit_name, is_comm_inferior, d\n" +
                "        FROM adm_prd_tkt_tfly_cmp_info_df \n" +
                "        WHERE d >= ?\n" +
                "            AND d <= ?\n" +
                "            AND trip_viewspot_id = ?\n" +
                "            AND is_cover_inferior = 0\n" +
                "    ) t\n" +
                "    GROUP BY trip_lv2_saleunit_id, trip_lv2_saleunit_name\n" +
                ") fly\n" +
                "    FULL JOIN (\n" +
                "        SELECT trip_lv2_saleunit_id, trip_lv2_saleunit_name, sum(is_comm_inferior) AS cnt3\n" +
                "            , count(DISTINCT d) AS cnt4\n" +
                "        FROM (\n" +
                "            SELECT DISTINCT trip_lv2_saleunit_id, trip_lv2_saleunit_name, is_comm_inferior, d\n" +
                "            FROM adm_prd_tkt_tklk_cmp_info_df \n" +
                "            WHERE d >= ?\n" +
                "                AND d <= ?\n" +
                "                AND trip_viewspot_id = ?\n" +
                "                AND is_cover_inferior = 0\n" +
                "        ) t\n" +
                "        GROUP BY trip_lv2_saleunit_id, trip_lv2_saleunit_name\n" +
                "    ) klk\n" +
                "    ON fly.trip_lv2_saleunit_id = klk.trip_lv2_saleunit_id\n" +
                "        AND fly.trip_lv2_saleunit_name = klk.trip_lv2_saleunit_name"
                + " limit ?,?";

        List<PreparedParameterBean> parameters = new ArrayList<>();
        parameters.add(new PreparedParameterBean(startDate, Types.VARCHAR));
        parameters.add(new PreparedParameterBean(endDate, Types.VARCHAR));
        parameters.add(new PreparedParameterBean(String.valueOf(sightId), Types.BIGINT));
        parameters.add(new PreparedParameterBean(startDate, Types.VARCHAR));
        parameters.add(new PreparedParameterBean(endDate, Types.VARCHAR));
        parameters.add(new PreparedParameterBean(String.valueOf(sightId), Types.BIGINT));
        parameters.add(new PreparedParameterBean(String.valueOf((pageNo-1)*pageSize), Types.INTEGER));
        parameters.add(new PreparedParameterBean(String.valueOf(pageSize), Types.INTEGER));

        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql,parameters);
        } catch (SQLException e) {
            log.warn("queryUncoveredTicketType error", e);
        }

        List<CoreTicketTypeCompetitive> coreTicketTypeCompetitiveList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(result)){
            for(Map<String, Object> map : result){
                CoreTicketTypeCompetitive coreTicketTypeCompetitive = new CoreTicketTypeCompetitive();
                coreTicketTypeCompetitive.setTicketTypeId((Long) map.getOrDefault("trip_lv2_saleunit_id","0"));
                coreTicketTypeCompetitive.setTicketTypeName(String.valueOf(map.getOrDefault("trip_lv2_saleunit_name","")));
                if (map.get("cnt1") == null) {
                    coreTicketTypeCompetitive.setDisadvantageDaysToFZ(null);
                } else {
                    coreTicketTypeCompetitive.setDisadvantageDaysToFZ(Math.toIntExact((Long) map.getOrDefault("cnt1", 0L)));
                }

                if (map.get("cnt2") == null) {
                    coreTicketTypeCompetitive.setTotalDaysToFZ(null);
                } else {
                    coreTicketTypeCompetitive.setTotalDaysToFZ(Math.toIntExact((Long) map.getOrDefault("cnt2", 0L)));
                }
                if (map.get("cnt3") == null) {
                    coreTicketTypeCompetitive.setDisadvantageDaysToKL(null);
                } else {
                    coreTicketTypeCompetitive.setDisadvantageDaysToKL(Math.toIntExact((Long) map.getOrDefault("cnt3", 0L)));
                }
                if (map.get("cnt4") == null) {
                    coreTicketTypeCompetitive.setTotalDaysToKL(null);
                } else {
                    coreTicketTypeCompetitive.setTotalDaysToKL(Math.toIntExact((Long) map.getOrDefault("cnt4", 0L)));
                }
                coreTicketTypeCompetitiveList.add(coreTicketTypeCompetitive);
            }
        }

        return coreTicketTypeCompetitiveList;

    }

    public Integer queryCoreTicketTypeTotalNum(Long sightId, String startDate, String endDate){
        StringBuilder sql = new StringBuilder("select count(1) as totalNum from (" +
        "SELECT coalesce(fly.trip_lv2_saleunit_id, klk.trip_lv2_saleunit_id) AS trip_lv2_saleunit_id\n" +
                "    , coalesce(fly.trip_lv2_saleunit_name, klk.trip_lv2_saleunit_name) AS trip_lv2_saleunit_name, fly.cnt1\n" +
                "    , fly.cnt2, klk.cnt3, klk.cnt4\n" +
                "FROM (\n" +
                "    SELECT trip_lv2_saleunit_id, trip_lv2_saleunit_name, sum(is_comm_inferior) AS cnt1\n" +
                "        , count(DISTINCT d) AS cnt2\n" +
                "    FROM (\n" +
                "        SELECT DISTINCT trip_lv2_saleunit_id, trip_lv2_saleunit_name, is_comm_inferior, d\n" +
                "        FROM adm_prd_tkt_tfly_cmp_info_df \n" +
                "        WHERE d >= '2025-04-17'\n" +
                "            AND d <= '2025-04-23'\n" +
                "            AND trip_viewspot_id = 5719020\n" +
                "            AND is_cover_inferior = 0\n" +
                "    ) t\n" +
                "    GROUP BY trip_lv2_saleunit_id, trip_lv2_saleunit_name\n" +
                ") fly\n" +
                "    FULL JOIN (\n" +
                "        SELECT trip_lv2_saleunit_id, trip_lv2_saleunit_name, sum(is_comm_inferior) AS cnt3\n" +
                "            , count(DISTINCT d) AS cnt4\n" +
                "        FROM (\n" +
                "            SELECT DISTINCT trip_lv2_saleunit_id, trip_lv2_saleunit_name, is_comm_inferior, d\n" +
                "            FROM adm_prd_tkt_tklk_cmp_info_df \n" +
                "            WHERE d >= ?\n" +
                "                AND d <= ?\n" +
                "                AND trip_viewspot_id = ?\n" +
                "                AND is_cover_inferior = 0\n" +
                "        ) t\n" +
                "        GROUP BY trip_lv2_saleunit_id, trip_lv2_saleunit_name\n" +
                "    ) klk\n" +
                "    ON fly.trip_lv2_saleunit_id = klk.trip_lv2_saleunit_id\n" +
                "        AND fly.trip_lv2_saleunit_name = klk.trip_lv2_saleunit_name" +
                ") a");

        List<PreparedParameterBean> parameters = new ArrayList<>();
        parameters.add(new PreparedParameterBean(startDate, Types.VARCHAR));
        parameters.add(new PreparedParameterBean(endDate, Types.VARCHAR));
        parameters.add(new PreparedParameterBean(String.valueOf(sightId), Types.BIGINT));

        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(),parameters);
        } catch (SQLException e) {
            log.warn("queryUncoveredTicketType error", e);
        }

        return CollectionUtils.isNotEmpty(result) ? Math.toIntExact((Long) result.get(0).getOrDefault("totalNum", "0")) : 0;

    }


    public Map<String, Object> queryCoreTicketTypeDetail(Long sightId, String startDate, String endDate, Long ticketTypeId){
        String sql = "select "
                + " trip_lv2_saleunit_id "
                + " ,trip_lv2_saleunit_name "
                + " ,cast(sum(is_comm_inferior) as Integer) as comm_inferior_days"  //商品力劣势天数
                + " ,cast(count(distinct d) as Integer) as days" //总天数
                + " ,cast(sum(is_abt_inferior) as Integer) as abt_inferior_days"  //提前预订时间是否劣势
                + " ,cast(sum(is_ct_inferior) as Integer) as ct_inferior_days"  //确认方式是否劣势
                + " ,cast(sum(is_rc_inferior) as Integer) as rc_inferior_days"  //退改规则是否劣势
                + " ,cast(sum(is_et_inferior) as Integer) as et_inferior_days"  //入园方式是否劣势
                + " ,cast(sum(is_pr_inferior) as Integer) as pr_inferior_days"  //班期是否劣势
                + " ,cast(avg(trip_sale_price) as Double) as trip_sale_price"  //携程价格
                + " ,cast(avg(rival_sale_price) as Double) as rival_sale_price"  //商品力劣势
                + " from ( "
                + " select trip_lv2_saleunit_id,trip_lv2_saleunit_name,d"
                + " ,max(is_comm_inferior) as is_comm_inferior\n" +
                "  ,max(is_abt_inferior) as is_abt_inferior\n" +
                "  ,max(is_rc_inferior) as is_rc_inferior\n" +
                "  ,max(is_et_inferior) as is_et_inferior\n" +
                "  ,max(is_ct_inferior) as is_ct_inferior\n" +
                "  ,max(is_pr_inferior) as is_pr_inferior\n" +
                "  ,avg(trip_sale_price) as trip_sale_price\n" +
                "  ,avg(rival_sale_price) as rival_sale_price "
                + " from adm_prd_tkt_tfly_cmp_info_df"
                + " where trip_viewspot_id = ?"
                + " and trip_lv2_saleunit_id = ?"
                + " and d between ? and ?"
                + " and is_cover_inferior = 0 "
                + " group by trip_lv2_saleunit_id,trip_lv2_saleunit_name,d"
                + " ) a"
                + " group by trip_lv2_saleunit_id,trip_lv2_saleunit_name";

        List<PreparedParameterBean> parameters = new ArrayList<>();
        parameters.add(new PreparedParameterBean(String.valueOf(sightId), Types.BIGINT));
        parameters.add(new PreparedParameterBean(String.valueOf(ticketTypeId), Types.BIGINT));
        parameters.add(new PreparedParameterBean(startDate, Types.VARCHAR));
        parameters.add(new PreparedParameterBean(endDate, Types.VARCHAR));

        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql,parameters);
        } catch (SQLException e) {
            log.warn("queryCoreTicketTypeDetail error", e);
        }

        return CollectionUtils.isNotEmpty(result)?result.get(0):new HashMap<>();

    }

}
