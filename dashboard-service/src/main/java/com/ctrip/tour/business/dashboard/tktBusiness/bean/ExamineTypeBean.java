package com.ctrip.tour.business.dashboard.tktBusiness.bean;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class ExamineTypeBean {

    //对应的考核类型列表
    //枚举值 根据张景昌给的枚举值定义；针对gmv和毛利
    //1-门票+活动；2-单门票；3-单活动；
    //4-门票+国内日游；5-活动+国内日游；6-门票+活动+国内日游；7-单国内日游
    //8-单出境日游；9-门票+出境日游；10-活动+出境日游；11-国内日游+出境日游；
    // 12-门票+活动+出境日游；13-门票+国内日游+出境日游；14-活动+国内日游+出境日游；15-门票+活动+国内日游+出境日游

    // 枚举值 针对质量指标 | 线路覆盖指标
    // 1：考核国内日游   2：考核出境日游  3. 同时考核国内和出境日游
    List<Integer> examineTypeList;
    //收入力中每个考核类型对应可以查看的子指标(业务线)列表
    List<String> subMetricList;

}
