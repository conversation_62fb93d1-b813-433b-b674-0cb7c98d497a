package com.ctrip.tour.business.dashboard.tktBusiness.strategy;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ExamineConfigBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBeanV2;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBeanV3;
import org.springframework.scheduling.annotation.Async;

import java.util.List;
import java.util.concurrent.Future;

public interface TicketQuantityCalStrategyV2 {

    //获取收入力子指标指标卡数据
    @Async("subMetricCardExecutor")
    Future<OveaseaSubMetric> getBus110SubMetricCardData(TimeFilter timeFilter,
                                                        OverseaMetricInfoBeanV2 metricInfoBean,
                                                        String d,
                                                        String subMetric,
                                                        GetOverseaMetricCardDataV2RequestType request) throws Exception;


    //获取收入力子指标趋势线数据
    GetOverseaTrendLineDataV2ResponseType getBus110SubTrendLineData(GetOverseaTrendLineDataV2RequestType request,
                                                                    String d,
                                                                    List<String> timeList) throws Exception;



    //获取收入力子指标下钻基础数据
    GetOverseaDrillDownBaseInfoV2ResponseType getBus110SubDrillDownBaseInfo(GetOverseaDrillDownBaseInfoV2RequestType request,
                                                                             String d,
                                                                            OverseaMetricInfoBean metricInfoBean) throws Exception;


    //获取收入力子指标下钻数据
    GetOverseaTableDataV2ResponseType getBus110SubTableData(GetOverseaTableDataV2RequestType request,
                                                             String d,
                                                          OverseaMetricInfoBean metricInfoBean) throws Exception;


    //获取子指标的名称
    String getSubMetricName();
}
