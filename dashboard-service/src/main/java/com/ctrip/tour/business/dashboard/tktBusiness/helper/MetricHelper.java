package com.ctrip.tour.business.dashboard.tktBusiness.helper;

import com.ctrip.framework.ucs.common.util.StringUtils;
import com.ctrip.soa._24922.*;
import com.ctrip.soa._27181.GetRawDataResponseType;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.ConfigImportException;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.InputArgumentException;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.ctrip.tour.business.dashboard.utils.MapperUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2022/7/28
 */
public class MetricHelper {

    //判断当前选中的下钻维度在某个考核周期是否可以下钻
    public static Boolean canDrillDown(String level,
                                       String field) {
        //国内 三方必然可以下钻
        if ("".equals(level)) {
            return true;
        }
        List<String> drillDownList = Lists.newArrayList("region_name", "province_name", "examinee", "viewspotid");
        int levelIndex = drillDownList.indexOf(level);
        int fieldIndex = drillDownList.indexOf(field);
        return levelIndex <= fieldIndex;
    }


    //考核层级和对应的数据库字段的对应
    public static String getLevelColumnName(String level) {
        switch (level) {
            case "大区":
                return "region_name";
            case "省份":
                return "province_name";
            case "景点":
                return "examinee";
            case "三方":
            case "国内":
                return "";
            default:
                throw new ConfigImportException("import error level:"+level);
        }
    }

    //时间类型对应的数据库字段
    public static String getDateTypeColumnName(String dataType) {
        if ("month".equals(dataType)) {
            return "month";
        } else if("quarter".equals(dataType)){
            return "quarter";
        } else{
            return "half";
        }
    }

    //下钻字段对应的数据库类型
    public static String getDrillDownColumnName(String field){
        switch (field){
            case "大区":
                return "region_name";
            case "省份":
                return "province_name";
            case "商拓":
                return "examinee";
            case "POI":
                return "viewspotid";
            default:
                throw new InputArgumentException("input error drilldown field:"+field);
        }
    }

    //获取某个考核层级对应的下钻维度
    public static List<String> getDomesticDrillDownFieldList(String level,List<String> rangeList) {
        switch (level) {
            case "":
                return Lists.newArrayList("region_name", "province_name");
            case "国内"://NOSONAR
                return Lists.newArrayList("region_name", "province_name");
            case "大区"://NOSONAR
                if (CollectionUtils.isNotEmpty(rangeList) && rangeList.size() > 1) {
                    return Lists.newArrayList("region_name", "province_name");
                } else {
                    return Lists.newArrayList("region_name");
                }
            case "省份"://NOSONAR
                if (CollectionUtils.isNotEmpty(rangeList) && rangeList.size() > 1) {
                    return Lists.newArrayList("province_name");
                }
            default:
                return new ArrayList<>();
        }
    }

    //获取数据库字段对应的下钻字段
    public static String getDataBaseColumnName(String field){
        switch (field){
            case "region_name":
                return "大区";
            case "province_name":
                return "省份";
            case "examinee":
                return "商拓";
            case "viewspotid":
                return "POI";
            default:
                return "";
        }
    }

    //根据field获取对应的中文
    //劣势相关指标下钻需要使用
    public static String getFieldChineseName(String field) {
        switch (field) {
            case "region_name":
                return "大区";
            case "province_name":
                return "省份";
            case "examinee":
                return "景点";
            default:
                return "";
        }
    }

    //获取指标和考核层级默认下钻字段
    public static String getDefaultChosenField(String level,
                                               String metric){
        switch (level){
            case "国内":
            case "三方":
                return "大区";
            case "大区":
                return "省份";
            case "省份":
                List<String> exceptionMetric = Lists.newArrayList("3","4","8","9");
                return exceptionMetric.contains(metric) ? "省份" : "商拓";
            case "景点":
                return "商拓";
            default:
                throw new ConfigImportException("配置中导入了错误的层级:"+level);
        }
    }

    //获取首页门票活动(不限)默认下钻层级
    public static void setMetricCardDrillDownInfo(MetricInfoBean metricInfoBean,
                                                  MetricDetailInfo metricDetailInfo) {

        String level = metricInfoBean.getLevel();
        List<String> regionList = metricInfoBean.getRegionList();
        List<String> bdList = metricInfoBean.getBdList();
        if ("国内".equals(level) || "三方".equals(level)) {
            metricDetailInfo.setNeedDrillDown(true);
            metricDetailInfo.setDefaultField("大区");
        } else if ("大区".equals(level)) {
            metricDetailInfo.setNeedDrillDown(true);
            metricDetailInfo.setDefaultField("省份");
        } else if ("省份".equals(level)) {
            if (regionList.size() > 1) {
                metricDetailInfo.setNeedDrillDown(true);
                metricDetailInfo.setDefaultField("省份");
            } else {
                metricDetailInfo.setNeedDrillDown(false);
            }
        } else {
            if (bdList.size() > 1) {
                metricDetailInfo.setNeedDrillDown(true);
                metricDetailInfo.setDefaultField("商拓");
            } else {
                metricDetailInfo.setNeedDrillDown(false);
            }
        }
    }

    //获取首页门票活动(不限)默认下钻层级V2
    public static void setMetricCardDrillDownInfV2(MetricInfoBean metricInfoBean,
                                                   DomesticMetricDetailInfo metricDetailInfo) {
        String level = metricInfoBean.getLevel();
        List<String> regionList = metricInfoBean.getRegionList();
        List<String> bdList = metricInfoBean.getBdList();
        if ("国内".equals(level)) {//NOSONAR
            metricDetailInfo.setNeedDrillDown(true);
            metricDetailInfo.setDefaultField("大区");//NOSONAR
        } else if ("大区".equals(level)) {//NOSONAR
            metricDetailInfo.setNeedDrillDown(true);
            metricDetailInfo.setDefaultField("省份");//NOSONAR
        } else if ("省份".equals(level)) {//NOSONAR
            if (regionList.size() > 1) {
                metricDetailInfo.setNeedDrillDown(true);
                metricDetailInfo.setDefaultField("省份");//NOSONAR
            } else {
                metricDetailInfo.setNeedDrillDown(false);
            }
        } else {
            if (bdList != null && bdList.size() > 1) {
                metricDetailInfo.setNeedDrillDown(true);
                metricDetailInfo.setDefaultField("商拓");//NOSONAR
            } else {
                metricDetailInfo.setNeedDrillDown(false);
            }
        }
    }

    //获取首页门票活动(不限)默认下钻层级V2
    public static void setActMetricCardDrillDownInfV2(MetricInfoBean metricInfoBean,
                                                   DomesticMetricDetailInfo metricDetailInfo) {
        String level = metricInfoBean.getActLevel();
        List<String> regionList = metricInfoBean.getActRegionList();
        List<String> bdList = metricInfoBean.getActBdList();
        if ("国内".equals(level)) {//NOSONAR
            metricDetailInfo.setNeedDrillDown(true);
            metricDetailInfo.setDefaultField("大区");//NOSONAR
        } else if ("大区".equals(level)) {//NOSONAR
            metricDetailInfo.setNeedDrillDown(true);
            metricDetailInfo.setDefaultField("省份");//NOSONAR
        } else if ("省份".equals(level)) {//NOSONAR
            if (regionList.size() > 1) {
                metricDetailInfo.setNeedDrillDown(true);
                metricDetailInfo.setDefaultField("省份");//NOSONAR
            } else {
                metricDetailInfo.setNeedDrillDown(false);
            }
        } else {
            if (bdList.size() > 1) {
                metricDetailInfo.setNeedDrillDown(true);
                metricDetailInfo.setDefaultField("商拓");//NOSONAR
            } else {
                metricDetailInfo.setNeedDrillDown(false);
            }
        }
    }

    public static void setCommonMetricCardDrillDownInfo(DomesticMetricDetailInfo domesticMetricDetailInfo,
                                                        MetricInfoBean metricInfoBean,
                                                        Integer businessId) {

        switch (metricInfoBean.getBusinessType()) {
            case 0:
            case 1:
                setMetricCardDrillDownInfV2(metricInfoBean, domesticMetricDetailInfo);
                break;
            case 2:
                if (StringUtils.isNotBlank(metricInfoBean.getActLevel())) {
                    setActMetricCardDrillDownInfV2(metricInfoBean, domesticMetricDetailInfo);
                } else {
                    setMetricCardDrillDownInfV2(metricInfoBean, domesticMetricDetailInfo);
                }
                break;
            case 3:
                setActMetricCardDrillDownInfV2(metricInfoBean, domesticMetricDetailInfo);
                break;
            case 4:
                setOdtMetricCardDrillDownInfoV2(metricInfoBean.getOdtLevel(), metricInfoBean.getOdtRegionList(), domesticMetricDetailInfo);
                break;

        }
    }

    //获取首页日游默认下钻层级
    public static void setOdtMetricCardDrillDownInfo(MetricInfoBean metricInfoBean,
                                                     MetricDetailInfo metricDetailInfo,
                                                     RemoteConfig remoteConfig) {

        String domestic = remoteConfig.getConfigValue("domestic");
        String region = remoteConfig.getConfigValue("region");
        String province = remoteConfig.getConfigValue("province");

        String level = metricInfoBean.getOdtLevel();
        List<String> regionList = metricInfoBean.getOdtRegionList();
        if (domestic.equals(level)) {
            metricDetailInfo.setNeedDrillDown(true);
            metricDetailInfo.setDefaultField(region);
        } else if (region.equals(level)) {
            metricDetailInfo.setNeedDrillDown(true);
            metricDetailInfo.setDefaultField(province);
        } else if (province.equals(level)) {
            if (regionList.size() > 1) {
                metricDetailInfo.setNeedDrillDown(true);
                metricDetailInfo.setDefaultField(province);
            } else {
                metricDetailInfo.setNeedDrillDown(false);
            }
        }
    }


    public static void setOdtMetricCardDrillDownInfo(String level,
                                                     List<String> regionList,
                                                     MetricDetailInfo metricDetailInfo,
                                                     RemoteConfig remoteConfig) {

        String domestic = remoteConfig.getConfigValue("domestic");
        String region = remoteConfig.getConfigValue("region");
        String province = remoteConfig.getConfigValue("province");

        if (domestic.equals(level)) {
            metricDetailInfo.setNeedDrillDown(true);
            metricDetailInfo.setDefaultField(region);
        } else if (region.equals(level)) {
            metricDetailInfo.setNeedDrillDown(true);
            metricDetailInfo.setDefaultField(province);
        } else if (province.equals(level)) {
            if (regionList.size() > 1) {
                metricDetailInfo.setNeedDrillDown(true);
                metricDetailInfo.setDefaultField(province);
            } else {
                metricDetailInfo.setNeedDrillDown(false);
            }
        }
    }


    public static void setOdtMetricCardDrillDownInfoV2(String level,
                                                       List<String> regionList,
                                                       DomesticMetricDetailInfo metricDetailInfo) {

        String domestic = "国内";//NOSONAR
        String region = "大区";//NOSONAR
        String province = "省份";//NOSONAR

        if (domestic.equals(level)) {
            metricDetailInfo.setNeedDrillDown(true);
            metricDetailInfo.setDefaultField(region);
        } else if (region.equals(level)) {
            metricDetailInfo.setNeedDrillDown(true);
            metricDetailInfo.setDefaultField(province);
        } else if (province.equals(level)) {
            if (regionList.size() > 1) {
                metricDetailInfo.setNeedDrillDown(true);
                metricDetailInfo.setDefaultField(province);
            } else {
                metricDetailInfo.setNeedDrillDown(false);
            }
        }
    }



    //获取出境日游默认下钻层级
    public static void setOverseaOdtMetricCardDrillDownInfo(MetricInfoBean metricInfoBean,
                                                     MetricDetailInfo metricDetailInfo,
                                                     RemoteConfig remoteConfig) {

        String domestic = remoteConfig.getConfigValue("domestic");
        String region = remoteConfig.getConfigValue("region");
        String province = remoteConfig.getConfigValue("province");

        String level = metricInfoBean.getOverseaOdtLevel();
        List<String> regionList = metricInfoBean.getOverseaOdtRegionList();
        if (domestic.equals(level)) {
            metricDetailInfo.setNeedDrillDown(true);
            metricDetailInfo.setDefaultField(region);
        } else if (region.equals(level)) {
            metricDetailInfo.setNeedDrillDown(true);
            metricDetailInfo.setDefaultField(province);
        } else if (province.equals(level)) {
            if (regionList.size() > 1) {
                metricDetailInfo.setNeedDrillDown(true);
                metricDetailInfo.setDefaultField(province);
            } else {
                metricDetailInfo.setNeedDrillDown(false);
            }
        }
    }

    //当首页需要查看多个子指标数据时  设置默认下钻层级
    //该场景默认可以下钻 且下钻规则与其他场景不同
    public static void setMultiSubMetricDrillDownField(MetricInfoBean metricInfoBean,
                                                       MetricDetailInfo metricDetailInfo,
                                                       RemoteConfig remoteConfig,
                                                       List<String> subMetricList) {
        String domestic = remoteConfig.getConfigValue("domestic");
        String three = remoteConfig.getConfigValue("three");
        String region = remoteConfig.getConfigValue("region");
        String province = remoteConfig.getConfigValue("province");
        String bd = remoteConfig.getConfigValue("bd");

        StringBuilder sb = new StringBuilder();

        for (String subMetric : subMetricList) {
            //如果该子指标本身就是一个复合指标  对应ticketActivity+domesticDayTour的场景
            //此时用门票的考核层级和考核范围判断即可
            //如果子指标是门票也用门票的考核范围判断
            if (subMetric.contains("+") || subMetric.equals("ticketActivity")) {
                String level = metricInfoBean.getLevel();
                if (domestic.equals(level) || three.equals(level)) {
                    sb.append("|").append(region);
                } else if (region.equals(level) || province.equals(level)) {
                    sb.append("|").append(province);
                } else {
                    sb.append("|").append(bd);
                }
            } else if (subMetric.equals("domesticDayTour")) {
                //如果子指标是国内日游
                String odtLevel = metricInfoBean.getOdtLevel();
                if (domestic.equals(odtLevel)) {
                    sb.append("|").append(region);
                } else if (region.equals(odtLevel) || province.equals(odtLevel)) {
                    sb.append("|").append(province);
                }
            } else {
                //如果子指标是出境日游
                String overseaOdtLevel = metricInfoBean.getOverseaOdtLevel();
                if (domestic.equals(overseaOdtLevel)) {
                    sb.append("|").append(region);
                } else if (region.equals(overseaOdtLevel) || province.equals(overseaOdtLevel)) {
                    sb.append("|").append(province);
                }
            }
        }
        //删除多余的|
        sb.deleteCharAt(0);
        metricDetailInfo.setDefaultField(sb.toString());
    }


    /**
     * 获取首页国内收入力展示的简易下钻子指标
     * @param metric
     * @param checkUserPermissionReq
     * @return
     */
    public static List<String> calDrillDownSubMetricList(String metric,
                                                         CheckUserPermissionResponseType checkUserPermissionReq) {
        List<List<String>> resultList = new ArrayList<>();

        List<String> subMetricList = checkUserPermissionReq.getDomesticBasicConfig()
                .getTrendLineConfigMap()
                .get(metric)
                .getSubMetricList();

        //先加入一个元素做基础
        resultList.add(Lists.newArrayList(subMetricList.get(0).split("\\+")));
        //元素个数不超过10个 直接干就行了
        for (int i = 1; i < subMetricList.size(); i++) {
            List<String> currentList = Lists.newArrayList(subMetricList.get(i).split("\\+"));
            boolean needAdd = true;
            for (List<String> list : resultList) {
                if (list.containsAll(currentList)) {
                    needAdd = false;
                    break;
                }
            }
            if (needAdd) {
                resultList.add(currentList);
            }
        }
        return resultList.stream()
                .map(i -> i.stream().collect(Collectors.joining("+")))
                .collect(Collectors.toList());
    }


    //获取首页同时展示门票活动以及日游的默认下钻层级
    //该场景默认可以下钻 且下钻规则与其他场景不同
//    public static void setAllSubMetricTourMetricCardDrillDownInfo(MetricInfoBean metricInfoBean,
//                                                                  MetricDetailInfo metricDetailInfo,
//                                                                  RemoteConfig remoteConfig) {
//
//        String domestic = remoteConfig.getConfigValue("domestic");
//        String three = remoteConfig.getConfigValue("three");
//        String region = remoteConfig.getConfigValue("region");
//        String province = remoteConfig.getConfigValue("province");
//
//        metricDetailInfo.setNeedDrillDown(true);
//        metricDetailInfo.setDefaultSubMetric("ticketActivity|dayTour");
//        StringBuilder sb = new StringBuilder();
//        String level = metricInfoBean.getLevel();
//        if (domestic.equals(level) || three.equals(level)) {
//            sb.append(region);
//        } else if (region.equals(level) || province.equals(level)) {
//            sb.append(province);
//        } else {
//            sb.append(MetricHelper.getDataBaseColumnName(MetricHelper.getLevelColumnName(level)));
//        }
//
//
//        String odtLevel = metricInfoBean.getOdtLevel();
//        if (domestic.equals(odtLevel) || three.equals(odtLevel)) {
//            sb.append("|").append(region);
//        } else if (region.equals(odtLevel) || province.equals(odtLevel)) {
//            sb.append("|").append(province);
//        }
//        metricDetailInfo.setDefaultField(sb.toString());
//    }



    public static void adjustFieldItemOrder(GetDrillDownBaseInfoResponseType response) {
        Map<String, FieldDataItem> fieldDataItemMap = new HashMap<>();
        response.getFieldDataItemList().forEach(i -> {
            String field = i.getField();
            fieldDataItemMap.put(field, i);
        });
        List<String> orderList = Lists.newArrayList("大区", "省份", "商拓", "POI");
        List<FieldDataItem> fieldDataItemList = new ArrayList<>();
        for (String order : orderList) {
            FieldDataItem item = fieldDataItemMap.get(order);
            if (item != null) {
                fieldDataItemList.add(item);
            }
        }
        response.setFieldDataItemList(fieldDataItemList);
    }

    //获取下钻表格时  对下钻维度添加额外的信息
    //region_name  不添加
    //province_name ->  region_name province_name
    //examinee -> region_name province_name  examinee_name examinee
    //viewspotid  region_name province_name  examinee_name examinee viewspot_name   viewspotid
    public static List<String> getTableDrillDownGroupList(String field) {
        switch (field) {
            case "region_name":
                return Lists.newArrayList(field);
            case "province_name":
                return Lists.newArrayList("region_name", field);
            case "examinee":
                return Lists.newArrayList("region_name", "province_name", "examinee_name", field);
            case "viewspotid":
                return Lists.newArrayList("region_name", "province_name", "examinee_name", "examinee", "viewspot_name", field);
            default:
                throw new InputArgumentException("input error drilldown field:" + field);
        }
    }


    public static Integer getGapDays(GetRawDataResponseType response) {
        List<List<Object>> rawResultList = MapperUtil.str2ListList(response.getResult(), Object.class);
        if(GeneralUtil.isEmpty(rawResultList) || GeneralUtil.isEmpty(rawResultList.get(0)) || GeneralUtil.isEmpty(rawResultList.get(0).get(0))){
            return null;
        }
        return Integer.valueOf(String.valueOf(rawResultList.get(0).get(0)));
    }


    public static String getLevelByBusinessId(MetricInfoBean metricInfoBean, Integer businessId, Integer subBusinessId) {
        String level = "";
        switch (businessId) {
            case 1:
                level = metricInfoBean.getLevel();
                break;
            case 2:
                switch (subBusinessId) {
                    case 3:
                        level = metricInfoBean.getActLevel();
                        break;
                    case 4:
                        level = metricInfoBean.getOdtLevel();
                        break;
                    case 5:
                        level = metricInfoBean.getActLevel();
                        break;
                }
                break;
        }
        return level;
    }

    public static List<String> getRangeListByBusinessId(MetricInfoBean metricInfoBean, Integer businessId, Integer subBusinessId) {
        switch (businessId) {
            case 1:
                return metricInfoBean.getRegionList();
            case 2:
                switch (subBusinessId){
                    case 3:
                        return metricInfoBean.getActRegionList();
                    case 4:
                        return metricInfoBean.getOdtRegionList();
                    case 5:
                        return metricInfoBean.getActRegionList();
                }
                break;
        }
        return metricInfoBean.getRegionList();
    }
}
