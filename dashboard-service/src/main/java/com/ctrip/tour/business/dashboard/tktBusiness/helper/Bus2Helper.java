package com.ctrip.tour.business.dashboard.tktBusiness.helper;

import com.ctrip.soa._24922.*;
import com.ctrip.soa._27181.GetRawDataResponseType;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.DrillDownFieldBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ExamineConfigBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.SqlParamterBean;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.InputArgumentException;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.ctrip.tour.business.dashboard.utils.MapperUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class Bus2Helper {

    private static List<String> extraCompleteDimList = Lists.newArrayList("total_suc_profit|total_trgt_profit|/",
            "ttd_cps_tot_profit|ttd_trgt_profit|/", "odt_suc_profit|odt_trgt_profit|/", "odt_ob_suc_profit|odt_ob_trgt_profit|/");

    private static List<String> extraDimList = Lists.newArrayList("total_suc_profit", "ttd_cps_tot_profit", "odt_suc_profit");



    //将dimMap中的元素key值做一些转换
    public static Map<String, Double> convertDimMap(Map<String, Double> originMap,
                                                    String subMetric) {
        Map<String, Double> newMap = new HashMap<>();
        for (Map.Entry<String, Double> entry : originMap.entrySet()) {
            String key = entry.getKey();
            String newKey = generateNewKey(key, subMetric);
            Double value = entry.getValue();

            if (!key.equals(newKey) && GeneralUtil.isNotEmpty(value)) {
                newMap.put(newKey, value);
            }

        }
        return newMap;
    }

    public static Map<String, Double> summaryTwoMapData(Map<String, Double> dimMap1,
                                                        Map<String, Double> dimMap2,
                                                        String momType,
                                                        String prefix) {
        Map<String, Double> newMap = new HashMap<>();
        String dim = prefix + "total_suc_profit";
        String targetDim = prefix + "total_trgt_profit";
        Double dimValue = dimMap1.getOrDefault(dim, 0d) + dimMap2.getOrDefault(dim, 0d);
        newMap.put(dim, dimValue);
        Double targetDimValue = dimMap1.getOrDefault(targetDim, 0d) + dimMap2.getOrDefault(targetDim, 0d);
        newMap.put(targetDim, targetDimValue);
        //非指标卡的完成率不走这个计算方式
        //指标卡prefix为 ""
        if ("".equals(prefix) && GeneralUtil.isValidDivide(dimValue, targetDimValue)) {
            newMap.put("total_suc_profit|total_trgt_profit|/", dimValue / targetDimValue);
        }
        String lastyearDim = dim + "_lastyear_value";
        String _2019Dim = dim + "_2019_value";
        Double lastyearValue = dimMap1.getOrDefault(lastyearDim, 0d) + dimMap2.getOrDefault(lastyearDim, 0d);
        Double _2019DimValue = dimMap1.getOrDefault(_2019Dim, 0d) + dimMap2.getOrDefault(_2019Dim, 0d);
        if (GeneralUtil.isValidDivide(dimValue, lastyearValue)) {
            newMap.put(dim + "_lastyear", dimValue / lastyearValue - 1);
        }
        if (GeneralUtil.isValidDivide(dimValue, _2019DimValue)) {
            newMap.put(dim + "_2019", dimValue / _2019DimValue - 1);
        }
        //如果momType为空 则不需要计算环比
        if (!"".equals(momType)){
            String momFenzi = dim + "_" + momType + "_fenzi_value";
            String momFenmu = dim + "_" + momType + "_fenmu_value";
            Double momFenziValue = dimMap1.getOrDefault(momFenzi, 0d) + dimMap2.getOrDefault(momFenzi, 0d);
            Double momFenmuValue = dimMap1.getOrDefault(momFenmu, 0d) + dimMap2.getOrDefault(momFenmu, 0d);
            if (GeneralUtil.isValidDivide(momFenziValue, momFenmuValue)) {
                newMap.put(dim + "_" + momType, momFenziValue / momFenmuValue - 1);
            }
        }
        if("".equals(prefix)){
            String innerProfitDim = "total_sys_inner_profit";
            String outerProfitDim = "total_sys_outer_profit";
            Double innerProfitValue = dimMap1.getOrDefault(innerProfitDim, 0d) + dimMap2.getOrDefault(innerProfitDim, 0d);
            newMap.put(innerProfitDim, innerProfitValue);
            Double outerProfitValue = dimMap1.getOrDefault(outerProfitDim, 0d) + dimMap2.getOrDefault(outerProfitDim, 0d);
            newMap.put(outerProfitDim, outerProfitValue);
        }
        return newMap;

    }



    /**
     * 合并多个业务线数据
     * @param mapList
     * @param momType
     * @param prefix
     * @return
     */
    public static Map<String, Double> summaryMapListData(List<Map<String, Double>> mapList,
                                                         String momType,
                                                         String prefix) {
        Map<String, Double> newMap = new HashMap<>();
        String dim = prefix + "total_suc_profit";
        String targetDim = prefix + "total_trgt_profit";
        Double dimValue = getDataFromMapList(mapList, dim);
        newMap.put(dim, dimValue);
        Double targetDimValue = getDataFromMapList(mapList, targetDim);
        newMap.put(targetDim, targetDimValue);
        //非指标卡的完成率不走这个计算方式
        //指标卡prefix为 ""
        if ("".equals(prefix) && GeneralUtil.isValidDivide(dimValue, targetDimValue)) {
            newMap.put("total_suc_profit|total_trgt_profit|/", dimValue / targetDimValue);
        }
        String lastyearDim = dim + "_lastyear_value";
        String _2019Dim = dim + "_2019_value";
        Double lastyearValue = getDataFromMapList(mapList, lastyearDim);
        Double _2019DimValue = getDataFromMapList(mapList, _2019Dim);
        if (GeneralUtil.isValidDivide(dimValue, lastyearValue)) {
            newMap.put(dim + "_lastyear", dimValue / lastyearValue - 1);
        }
        if (GeneralUtil.isValidDivide(dimValue, _2019DimValue)) {
            newMap.put(dim + "_2019", dimValue / _2019DimValue - 1);
        }
        //如果momType为空 则不需要计算环比
        if (!"".equals(momType)) {
            String momFenzi = dim + "_" + momType + "_fenzi_value";
            String momFenmu = dim + "_" + momType + "_fenmu_value";
            Double momFenziValue = getDataFromMapList(mapList, momFenzi);
            Double momFenmuValue = getDataFromMapList(mapList, momFenmu);
            if (GeneralUtil.isValidDivide(momFenziValue, momFenmuValue)) {
                newMap.put(dim + "_" + momType, momFenziValue / momFenmuValue - 1);
            }
        }
        if ("".equals(prefix)) {
            String innerProfitDim = "total_sys_inner_profit";
            String outerProfitDim = "total_sys_outer_profit";
            Double innerProfitValue = getDataFromMapList(mapList, innerProfitDim);
            newMap.put(innerProfitDim, innerProfitValue);
            Double outerProfitValue = getDataFromMapList(mapList, outerProfitDim);
            newMap.put(outerProfitDim, outerProfitValue);
        }
        return newMap;

    }


    private static Double getDataFromMapList(List<Map<String, Double>> mapList,
                                             String dim) {
        Double dimValue = 0d;
        for (Map<String, Double> map : mapList) {
            dimValue += map.getOrDefault(dim, 0d);
        }
        return dimValue;
    }



    //将两个业务线趋势线简单合并返回
    public static void unionTrendLineData(GetTrendLineDataResponseType ticketActivityRes,
                                          GetTrendLineDataResponseType dayTourRes,
                                          GetTrendLineDataResponseType notLimitRes){
        List<TrendLineDetailInfo> trendLineDetailInfoList = new ArrayList<>();
        notLimitRes.setTrendLineDetailInfoList(trendLineDetailInfoList);
        trendLineDetailInfoList.addAll(ticketActivityRes.getTrendLineDetailInfoList());
        trendLineDetailInfoList.addAll(dayTourRes.getTrendLineDetailInfoList());
    }



    /**
     * 合并多个子指标趋势线数据
     * @param singleResponseMap
     * @param totalResponse
     * @param timeFilter
     * @param type
     * @throws ParseException
     */
    public static void mergeTrendLineData(Map<String, GetTrendLineDataResponseType> singleResponseMap,
                                          GetTrendLineDataResponseType totalResponse,
                                          TimeFilter timeFilter,
                                          String type) throws ParseException {
        List<TrendLineDetailInfo> trendLineDetailInfoList = new ArrayList<>();
        totalResponse.setTrendLineDetailInfoList(trendLineDetailInfoList);

        List<Map<String, Double>> mapList = new ArrayList<>();
        for (Map.Entry<String, GetTrendLineDataResponseType> entry : singleResponseMap.entrySet()) {
            Map<String, Double> dimMap = conertDataToMap(entry.getValue(), entry.getKey());
            mapList.add(dimMap);
        }
        //有效时间范围
        List<String> timeList = DateUtil.getAllSelectedTime(timeFilter, type);
        Map<String, Double> totalDimMap = new HashMap<>();
        for (String time : timeList) {
            totalDimMap.putAll(summaryMapListData(mapList, "", time + ":"));
        }
        ChartHelper.fillLineChartTrendLineData(null, timeList, totalDimMap,
                trendLineDetailInfoList, getLineChartTrendlineType());
    }


    private static Map<String, Double> conertDataToMap(GetTrendLineDataResponseType res,
                                                       String subMetric) {
        Map<String, Double> dimMap = new HashMap<>();
        for (TrendLineDetailInfo detailInfo : res.getTrendLineDetailInfoList()) {
            String dim = detailInfo.getDim();
            String newDim = generateNewKey(dim, subMetric);
            for (TrendLineDataItem item : detailInfo.getTrendLineDataItemList()) {
                String time = item.getTime();
                Double value = item.getValue();
                if(GeneralUtil.isNotEmpty(value)){
                    dimMap.put(time + ":" + newDim, value);
                }
            }
        }
        return dimMap;
    }


    private static String generateNewKey(String key,
                                         String subMetric) {
        if ("domesticDayTour".equals(subMetric)) {
            return key.replace("odt_suc_profit", "total_suc_profit")
                    .replace("odt_trgt_profit", "total_trgt_profit")
                    .replace("odt_sys_inner_profit", "total_sys_inner_profit")
                    .replace("odt_sys_outer_profit", "total_sys_outer_profit");
        } else if ("overseaDayTour".equals(subMetric)) {
            return key.replace("odt_ob_suc_profit", "total_suc_profit")
                    .replace("odt_ob_trgt_profit", "total_trgt_profit")
                    .replace("odt_ob_sys_inner_profit", "total_sys_inner_profit")
                    .replace("odt_ob_sys_outer_profit", "total_sys_outer_profit");
        } else {
            return key.replace("ttd_cps_tot_profit", "total_suc_profit")
                    .replace("ttd_trgt_profit", "total_trgt_profit")
                    .replace("ttd_sys_inner_profit", "total_sys_inner_profit")
                    .replace("ttd_sys_outer_profit", "total_sys_outer_profit");
        }
    }


    private static Map<String, String> getLineChartTrendlineType() {
        Map<String, String> typeMap = new HashMap<>();
        typeMap.put("total_suc_profit", "barChart");
        typeMap.put("total_suc_profit|total_trgt_profit|/", "lineChart");
        typeMap.put("total_suc_profit_lastyear", "lineChart");
        typeMap.put("total_suc_profit_2019","lineChart");
        return typeMap;
    }



    public static SqlParamterBean getRankingSqlBean(String domainName,
                                                    TimeFilter timeFilter,
                                                    String d) throws ParseException {
        SqlParamterBean bean = new SqlParamterBean();
        bean.setId(48L);
        Map<String, String> andMap = new HashMap<>();
        andMap.put("examinee", domainName);
        andMap.put("query_d", d);
        andMap.put("year", timeFilter.getYear());
        String dateType = MetricHelper.getDateTypeColumnName(timeFilter.getDateType());
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();
        andMap.put("date_type", dateType);
        if ("month".equals(dateType)) {
            andMap.put("period", month);
        } else {
            andMap.put("period", quarter);
        }
        bean.setAndMap(andMap);
        return bean;
    }


    public static void processRankData(GetRawDataResponseType res,
                                       MetricDetailInfo metricDetailInfo) {
        List<List<Object>> rawResultList = MapperUtil.str2ListList(res.getResult(), Object.class);
        if (rawResultList.size() > 0) {
            Object rankObject = rawResultList.get(0).get(1);
            if (rankObject != null) {
                metricDetailInfo.setRank(String.valueOf(rankObject));
            }
        }
    }



    //获取下钻可选维度
    //国内  三方     大区 省份 商拓
    //大区 多个省份   省份 商拓
    //单个省份       商拓
    public static List<String> getDrillDownFieldList(MetricInfoBean metricInfoBean,
                                                     RemoteConfig remoteConfig) {
        String domestic = remoteConfig.getConfigValue("domestic");
        String three = remoteConfig.getConfigValue("three");
        String level = metricInfoBean.getLevel();
        String region = remoteConfig.getConfigValue("region");
        String province = remoteConfig.getConfigValue("province");
        List<String> regionList = metricInfoBean.getRegionList();
        if (domestic.equals(level) || three.equals(level)) {
            return Lists.newArrayList("region_name", "province_name", "examinee");
        } else if (region.equals(level)) {
            return Lists.newArrayList("province_name", "examinee");
        } else if (province.equals(level)) {
            if(regionList.size()>1){
                return Lists.newArrayList("province_name", "examinee");
            }else{
                return Lists.newArrayList("examinee");
            }
        }
        throw new InputArgumentException("invalid metricInfoBean:"+MapperUtil.obj2Str(metricInfoBean));
    }



    //构造子指标sql请求数据(下钻基础数据)
    public static SqlParamterBean getDrillDownBaseInfoSqlBean(String field,
                                                              GetDrillDownBaseInfoRequestType request,
                                                              String d,
                                                              MetricInfoBean metricInfoBean,
                                                              RemoteConfig remoteConfig) throws Exception {
        SqlParamterBean bean = new SqlParamterBean();

        String metric = request.getMetric();
        String subMetric = request.getSubMetric();

        DrillDownFieldBean configBean = remoteConfig.getDrillDownFieldBean(metric, subMetric, field);
        bean.setId(configBean.getBaseInfoId());

        Map<String, String> baseMap = generateBaseMap(request.getTimeFilter(), "current", d, null);
        Map<String, String> notInMap = new HashMap<>();
        Map<String, String> likeMap = new HashMap<>();
        Map<String, String> notLikeMap = new HashMap<>();
        bean.setAndMap(baseMap);
        bean.setNotInMap(notInMap);
        bean.setLikeMap(likeMap);
        bean.setNotLikeMap(notLikeMap);
        //对于考核范围
        //查询商拓数据 like拼接条件
        //查询大区省份数据  in拼接
        setDrillDownExamineRangeValue(field, likeMap, notLikeMap, baseMap,
                notInMap, metricInfoBean, remoteConfig, "current", configBean.getBdSpecificCondition());
        List<String> groupList = configBean.getBaseInfoGroupList();
        if (request.isNeedSearch()) {
            String searchWord = request.getSearchWord();
            List<Integer> likeIndexList = configBean.getBaseInfoLikeIndexList();
            for (Integer index : likeIndexList) {
                likeMap.put(groupList.get(index), searchWord);
            }
        }
        bean.setGroupList(groupList);
        bean.setOrderList(Lists.newArrayList(groupList.get(0)));
        bean.setOrderTypeList(Lists.newArrayList("asc"));
        return bean;
    }

    private static Map<String, String> generateBaseMap(TimeFilter timeFilter,
                                                       String type,
                                                       String d,
                                                       ExamineConfigBean examineConfigBean) throws Exception {
        return OverseaMetricHelper.generateBaseMap(timeFilter, type, d, examineConfigBean);
    }


    private static void setDrillDownExamineRangeValue(String columnName,
                                                      Map<String, String> likeMap,
                                                      Map<String, String> notLikeMap,
                                                      Map<String, String> baseMap,
                                                      Map<String, String> notInMap,
                                                      MetricInfoBean metricInfoBean,
                                                      RemoteConfig remoteConfig,
                                                      String type,
                                                      String bdSpecificCondition) {
        if ("examinee".equals(columnName)) {
            setExamineLikeValue(likeMap, notLikeMap, metricInfoBean, remoteConfig, type, bdSpecificCondition);
        } else {
            setExamineRangeValue(baseMap, notInMap, metricInfoBean, remoteConfig);
        }
    }



    private static void setExamineLikeValue(Map<String, String> likeMap,
                                            Map<String, String> notLikeMap,
                                            MetricInfoBean metricInfoBean,
                                            RemoteConfig remoteConfig,
                                            String type,
                                            String bdSpecificCondition) {


        //如果不是查询当前数据  则不需要考核范围
        if(!"current".equals(type)){
            return;
        }

        String region = remoteConfig.getConfigValue("region");
        String province = remoteConfig.getConfigValue("province");
        String universalStudios = remoteConfig.getConfigValue("universalStudios");

        List<String> regionList = metricInfoBean.getRegionList();
        String level = metricInfoBean.getLevel();
        if (GeneralUtil.isNotEmpty(regionList)) {
            if (region.equals(level)) {
                likeMap.put("region_name", StringUtils.join(regionList, "|"));
            } else if (province.equals(level)) {
                likeMap.put(bdSpecificCondition, StringUtils.join(regionList, "|"));
                //如果考核层级为省份  且needUniversalStudios=0 则不看环球影城的数据
                String needUniversalStudios = metricInfoBean.getNeedUniversalStudios();
                if ("0".equals(needUniversalStudios)) {
                    notLikeMap.put("region_name", universalStudios);
                }
            }
        }

    }

    private static void setExamineRangeValue(Map<String, String> baseMap,
                                             Map<String, String> notInMap,
                                             MetricInfoBean metricInfoBean,
                                             RemoteConfig remoteConfig) {

        String region = remoteConfig.getConfigValue("region");
        String province = remoteConfig.getConfigValue("province");
        String universalStudios = remoteConfig.getConfigValue("universalStudios");

        List<String> regionList = metricInfoBean.getRegionList();
        String level = metricInfoBean.getLevel();
        if (GeneralUtil.isNotEmpty(regionList)) {
            if (region.equals(level)) {
                baseMap.put("region_name", StringUtils.join(regionList, "|"));
            } else if (province.equals(level)) {
                baseMap.put("province_name", StringUtils.join(regionList, "|"));
                //如果考核层级为省份  且needUniversalStudios=0 则不看环球影城的数据
                String needUniversalStudios = metricInfoBean.getNeedUniversalStudios();
                if ("0".equals(needUniversalStudios)) {
                    notInMap.put("region_name", universalStudios);
                }

            }
        }
    }


    //构造获取下钻基础数据请求
    //下钻数据  包括气泡图  和  下钻表格
    //包括首页和详情页
    public static SqlParamterBean getTableDataSqlBean(GetTableDataRequestType request,
                                                      String d,
                                                      MetricInfoBean metricInfoBean,
                                                      DrillDownFieldBean configBean,
                                                      RemoteConfig remoteConfig,
                                                      String type,
                                                      List<String> pagingFieldValueList,
                                                      String momType,
                                                      String subType) throws Exception {
        SqlParamterBean bean = new SqlParamterBean();
        String field = configBean.getField();

        setDrillDownIdAndGroupListForSqlBean(request, bean, configBean, type, field, subType);

        TimeFilter timeFilter = request.getTimeFilter();

        if ("mom".equals(type)) {
            //环比仅存在于  首页的下钻
            //此时下钻维度只有大区  省份
            List<Map<String, String>> baseMapList = generateMomBaseMapList(momType, timeFilter, d);
            Map<String, String> baseMap = baseMapList.get(0);
            Map<String,String> notInMap = new HashMap<>();
            //对于考核范围
            //查询大区省份数据  in拼接
            setDrillDownExamineRangeValue(field, new HashMap<>(), new HashMap<>(), baseMap,
                    notInMap, metricInfoBean, remoteConfig, type, configBean.getBdSpecificCondition());
            bean.setAndMap(baseMap);
            bean.setNotInMap(notInMap);

            Map<String, String> baseMap2 = baseMapList.get(1);
            Map<String, String> notInMap2 = new HashMap<>();
            setDrillDownExamineRangeValue(field, new HashMap<>(), new HashMap<>(), baseMap2,
                    notInMap2, metricInfoBean, remoteConfig, type, configBean.getBdSpecificCondition());
            bean.setAndMap2(baseMap2);
            bean.setNotInMap2(notInMap2);
        } else {
            Map<String, String> baseMap = generateBaseMap(timeFilter, type, d, null);
            Map<String,String> notInMap = new HashMap<>();
            Map<String, String> likeMap = new HashMap<>();
            Map<String,String> notLikeMap = new HashMap<>();
            //对于考核范围
            //查询商拓数据 like拼接条件
            //查询大区省份数据  in拼接
            setDrillDownExamineRangeValue(field, likeMap, notLikeMap, baseMap,
                    notInMap, metricInfoBean, remoteConfig, type, configBean.getBdSpecificCondition());

            DrillDownFilter drillDownFilter = request.getDrillDownFilter();

            setConditionValue(baseMap, field, drillDownFilter.getFieldValueList());
            //获取当前数据时 需要设置前端传入的分页  其他数据不需要
            //同时需要设置排序条件
            if ("current".equals(type)) {
                bean.setPageNo(request.getPageNo());
                bean.setPageSize(request.getPageSize());

                bean.setOrderList(Lists.newArrayList(extraDimList.get(0), field));
                bean.setOrderTypeList(Lists.newArrayList("desc", "desc"));
            }

            //获取同比数据时  需要设置上一步中获取当前数据得到的条件
            if ("lastyear".equals(type) || "2019".equals(type) || "currentpop".equals(type)) {
                setConditionValue(baseMap, field, pagingFieldValueList);
            }
            bean.setAndMap(baseMap);
            bean.setNotInMap(notInMap);
            bean.setLikeMap(likeMap);
            bean.setNotLikeMap(notLikeMap);
        }


        return bean;

    }


    public static void setConditionValue(Map<String, String> baseMap,
                                         String field,
                                         List<String> fieldValueList) {
        if (GeneralUtil.isNotEmpty(fieldValueList)) {
            baseMap.put(field, StringUtils.join(fieldValueList, "|"));
        }
    }


    private static List<Map<String, String>> generateMomBaseMapList(String momType,
                                                                    TimeFilter timeFilter,
                                                                    String d) throws ParseException {

        String lastDay = DateUtil.getDayOfInterval(d, -1);
        String dateType = timeFilter.getDateType();
        String year = timeFilter.getYear();
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();
        Map<String, String> momMap = new HashMap<>();
        Map<String, String> momMap2 = new HashMap<>();
        List<Map<String, String>> resultList = new ArrayList<>();
        resultList.add(momMap);
        resultList.add(momMap2);
        momMap.put("query_d", d);
        momMap2.put("query_d", d);
        if ("7days".equals(momType) || "30days".equals(momType)) {
            int interval = "7days".equals(momType) ? -6 : -29;
            String startDate = DateUtil.getDayOfInterval(lastDay, interval);
            momMap.put("the_date", startDate + "|" + lastDay);

            String momEndDate = DateUtil.getDayOfInterval(startDate, -1);
            String momStartDate = DateUtil.getDayOfInterval(momEndDate, interval);
            momMap2.put("the_date", momStartDate + "|" + momEndDate);
        } else {
            String time = DateUtil.getInputTime(dateType, month, quarter, null);
            momMap.put("year", year);
            momMap.put(dateType, time);
            List<String> lastTimeInfo = DateUtil.getLastTimeInfoV2(timeFilter);

            momMap2.put("year", lastTimeInfo.get(0));
            momMap2.put(dateType, lastTimeInfo.get(1));
        }
        return resultList;
    }

    private static void setDrillDownIdAndGroupListForSqlBean(GetTableDataRequestType request,
                                                             SqlParamterBean bean,
                                                             DrillDownFieldBean configBean,
                                                             String type,
                                                             String field,
                                                             String subType) {
        //按商拓下钻 查询数据主表 选择月和季时为不同的表
        //按商拓下钻 查询同比数据的表  门票活动和日游的不同
        //按商拓下钻 在首页不会露出 所以不需要环比数据
        Long otherId;
        Long id;
        if ("examinee".equals(field)) {
            String dateType = request.getTimeFilter().getDateType();
            otherId = configBean.getTableDataIdMap().get("other" + subType);
            id = configBean.getTableDataIdMap().get(type + dateType);
        } else {
            otherId = configBean.getTableDataIdMap().get("other");
            id = configBean.getTableDataIdMap().get(type);

        }
        bean.setId(GeneralUtil.isEmpty(id) ? otherId : id);

        String source = request.getSource();
        String queryType = request.getQueryType();

        if ("firstpage".equals(source) || ("detailpage".equals(source) && "bubble".equals(queryType))) {
            //首页或者详情页气泡图
            List<String> otherGroupList = configBean.getBubbleGroupListMap().get("other");
            List<String> groupList = configBean.getBubbleGroupListMap().get(type);

            bean.setGroupList(GeneralUtil.isEmpty(groupList) ? otherGroupList : groupList);

        } else {

            //详情页表格
            List<String> otherGroupList = configBean.getTableGroupListMap().get("other");
            List<String> groupList = configBean.getTableGroupListMap().get(type);

            bean.setGroupList(GeneralUtil.isEmpty(groupList) ? otherGroupList : groupList);

        }

    }



    //填充下钻基础数据
    public static void processDrillDownBaseInfo(String field,
                                                GetRawDataResponseType response,
                                                List<FieldDataItem> fieldDataItemList){
        ChartHelper.fillFieldDataItemList(field, MapperUtil.str2ListList(response.getResult(), Object.class), fieldDataItemList);
    }


    public static String getMomType(TimeFilter timeFilter,
                                    String d) throws ParseException {
        String lastDay = DateUtil.getDayOfInterval(d, -1);
        String dateType = timeFilter.getDateType();
        String year = timeFilter.getYear();
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();
        if ("month".equals(dateType)) {
            return DateUtil.isCurrentMonthV2(lastDay, year, month) ? "7days" : "lastmonth";
        } else {
            return DateUtil.isCurrentQuarterV2(lastDay, year, quarter) ? "30days" : "lastquarter";
        }
    }





    //填充表格基础数据
    public static void processTableBaseData(GetRawDataResponseType currentRes,
                                            GetRawDataResponseType targetRes,
                                            List<TableDataItem> tableDataItemList) {
        List<List<Object>> currentList = MapperUtil.str2ListList(currentRes.getResult(), Object.class);
        List<String> currentGroupList = currentRes.getGroupList();
        List<String> currentMetricList = currentRes.getMetricList();

        List<List<Object>> targetList = MapperUtil.str2ListList(targetRes.getResult(), Object.class);
        List<String> targetGroupList = targetRes.getGroupList();
        List<String> targetMetricList = targetRes.getMetricList();

        ChartHelper.fillCommmonTableDataV2(tableDataItemList,
                currentGroupList,
                targetGroupList,
                currentMetricList,
                targetMetricList,
                currentList,
                targetList);

        for (TableDataItem item : tableDataItemList) {
            for (String extraCompleteDim : extraCompleteDimList) {
                Map<String, Double> dimMap = item.getDimMap();
                processMetricCardCompleteData(extraCompleteDim, dimMap);
            }

        }
    }



    //填充表格基础数据
    public static void processTableBaseData(GetRawDataResponseType currentRes,
                                            List<TableDataItem> tableDataItemList) {
        List<List<Object>> currentList = MapperUtil.str2ListList(currentRes.getResult(), Object.class);
        List<String> currentGroupList = currentRes.getGroupList();
        List<String> currentMetricList = currentRes.getMetricList();


        ChartHelper.fillCommmonTableDataV2(tableDataItemList,
                currentGroupList,
                new ArrayList<>(),
                currentMetricList,
                new ArrayList<>(),
                currentList,
                new ArrayList<>());

        for (TableDataItem item : tableDataItemList) {
            for (String extraCompleteDim : extraCompleteDimList) {
                Map<String, Double> dimMap = item.getDimMap();
                processMetricCardCompleteData(extraCompleteDim, dimMap);
            }

        }
    }


    //填充指标卡完成率数据
    public static void processMetricCardCompleteData(String dim,
                                                     Map<String, Double> dimMap) {
        String[] dimArray = dim.split("\\|");
        Double fenzi = dimMap.get(dimArray[0]);
        Double fenmu = dimMap.get(dimArray[1]);
        dimMap.put(dim, GeneralUtil.getComplexResult(fenzi, fenmu, dimArray[2], false));
    }



    //填充表格同比当期数据
    public static void processTableCurrentPopData(GetRawDataResponseType currentpopRes,
                                                  List<TableDataItem> tableDataItemList) {
        List<String> metricList = currentpopRes.getMetricList();
        List<String> virtualMetricList = metricList.stream()
                .map(i -> i + "_virtual")
                .collect(Collectors.toList());
        List<String> groupList = currentpopRes.getGroupList();
        List<List<Object>> objectList = MapperUtil.str2ListList(currentpopRes.getResult(), Object.class);
        ChartHelper.fillCommmonTableDataV2(tableDataItemList,
                new ArrayList<>(),
                groupList,
                new ArrayList<>(),
                virtualMetricList,
                new ArrayList<>(),
                objectList);
    }


    //填充表格同比数据
    public static void processTablePopData(GetRawDataResponseType popRes,
                                           List<TableDataItem> tableDataItemList,
                                           String type) {
        List<List<Object>> popList = MapperUtil.str2ListList(popRes.getResult(), Object.class);
        List<String> popGroupList = popRes.getGroupList();
        List<String> popMetricList = popRes.getMetricList();
        ChartHelper.fillTableSingleDimPopData(tableDataItemList,
                popList,
                popGroupList,
                "_" + type,
                popMetricList,
                "_virtual");
    }


    //填充表格环比数据
    public static void processTableMomData(GetRawDataResponseType momRes,
                                           GetRawDataResponseType momRes2,
                                           List<TableDataItem> tableDataItemList,
                                           String momType) {
        List<List<Object>> momList = MapperUtil.str2ListList(momRes.getResult(), Object.class);
        List<List<Object>> mom2List = MapperUtil.str2ListList(momRes2.getResult(), Object.class);
        List<String> groupList = momRes.getGroupList();
        List<String> metricList = momRes.getMetricList();
        ChartHelper.makeUpTableMomData(tableDataItemList,
                momList,
                mom2List,
                metricList,
                groupList,
                "_" + momType);
    }









}
