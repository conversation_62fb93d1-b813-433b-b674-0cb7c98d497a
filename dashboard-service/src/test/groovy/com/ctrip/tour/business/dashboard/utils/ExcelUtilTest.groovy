package com.ctrip.tour.business.dashboard.utils

import spock.lang.Specification

class ExcelUtilTest extends Specification {
    def "test getExcelStream"() {
        given: "mock"
        def heads = Arrays.asList("a", "b");
        def resultList =  Arrays.asList(
                Arrays.asList("c", "d"),
                Arrays.asList("e", "ggg")
        )

        expect: "call"
        ExcelUtil.getExcelStream("sheetName", heads, resultList)
    }

//    def "test readExcel"() {
//        expect: "call"
//        ExcelUtil.readExcel("D:\\Users\\yyang25\\Desktop\\workhome1\\BI\\business-dashboard\\dashboard-service\\sheetName.xlsx")
//    }

}
