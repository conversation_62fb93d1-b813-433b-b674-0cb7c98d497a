package com.ctrip.tour.business.dashboard.tktBusiness.helper;

import com.ctrip.soa._24922.*;
import com.ctrip.soa._27181.GetRawDataResponseType;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.*;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.ConfigImportException;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.ctrip.tour.business.dashboard.utils.MapperUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;

import java.text.ParseException;
import java.util.*;

public class Bus12Helper {

    //填充指标卡基础数据
    public static void processMetricCardData(GetRawDataResponseType res,
                                             Map<String, Double> dimMap) {
        ChartHelper.fillOverallDimMap(MapperUtil.str2ListList(res.getResult(), Object.class), res.getMetricList(), dimMap);
    }


    //填充排名数据
    public static void processRankData(GetRawDataResponseType res,
                                       MetricDetailInfo metricDetailInfo) {
        List<List<Object>> rawResultList = MapperUtil.str2ListList(res.getResult(), Object.class);
        ChartHelper.fillRankData(metricDetailInfo, rawResultList);
    }


    //计算非趋势线平均数据
    public static void calMetricCardAverageData(Map<String, Double> dimMap,
                                                Integer gapDays,
                                                Integer addtionalGapDays) {

        if (GeneralUtil.isEmpty(gapDays)) {
            return;
        }

        Integer actualGapDays = gapDays - addtionalGapDays;

        for (Map.Entry<String, Double> entry : dimMap.entrySet()) {
            String key = entry.getKey();
            Double value = entry.getValue();
            if (!GeneralUtil.isEmpty(value)) {
                dimMap.put(key, value / actualGapDays);
            }
        }
    }

    //计算表格平均数据
    public static void calTableAverageData(List<TableDataItem> tableDataItemList,
                                           Integer gapDays,
                                           Integer addtionalGapDays) {
        for (TableDataItem item : tableDataItemList) {
            Map<String, Double> dimMap = item.getDimMap();
            calMetricCardAverageData(dimMap, gapDays, addtionalGapDays);
        }
    }

    //补齐表格目标和差值数据
    public static void makeUpTableData(List<TableDataItem> tableDataItemList){
        for (TableDataItem item : tableDataItemList) {
            Map<String, Double> dimMap = item.getDimMap();
            makeUpMetricData(dimMap);
        }
    }



    //补齐目标和差值数据
    public static void makeUpMetricData(Map<String, Double> dimMap) {
        dimMap.put("w_line_rate_trgt", 0.03d);
        Double rate = dimMap.getOrDefault("w_line_rate", 0d);
        dimMap.put("w_line_rate_gap", rate - 0.03d);
    }



    //填充汇总趋势线数据
    public static void processOverallTrendLineData(List<TrendLineDetailInfo> trendLineDetailInfoList,
                                                   PeriodDataBean periodDataBean,
                                                   List<String> timeList) {
        Map<String, Double> dimMap = new HashMap<>();
        Set<String> drillDownSet = new HashSet<>();
        List<List<Object>> weaknessList = periodDataBean.getWeaknessList();
        List<String> weaknessHeaderList = periodDataBean.getWeaknessHeaderList();
        List<String> reachDimList = weaknessHeaderList.subList(1, weaknessHeaderList.size());

        ChartHelper.setMonthlyMetricTrendlineActualReach(dimMap, drillDownSet, weaknessList, Lists.newArrayList("time"), reachDimList);


        Map<String, String> typeMap = new HashMap<>();
        typeMap.put("w_line_rate", "lineChart");

        ChartHelper.fillLineChartTrendLineData(null, timeList, dimMap,
                trendLineDetailInfoList, typeMap);
        makeUpTrendlineData(trendLineDetailInfoList);


    }

    //补齐趋势线数据
    public static void makeUpTrendlineData(List<TrendLineDetailInfo> trendLineDetailInfoList){
        List<TrendLineDetailInfo> gapTrendLineDetailInfoList =
                MapperUtil.str2List(MapperUtil.obj2Str(trendLineDetailInfoList), TrendLineDetailInfo.class);
        for (TrendLineDetailInfo info : gapTrendLineDetailInfoList) {
            String gapDim = info.getDim() + "_gap";
            info.setDim(gapDim);
            Double target = 0.03;
            List<TrendLineDataItem> itemList = info.getTrendLineDataItemList();
            for (TrendLineDataItem item : itemList) {
                Double value = item.getValue();
                if(!GeneralUtil.isEmpty(value)){
                    item.setValue(value - target);
                }
            }
            trendLineDetailInfoList.add(info);
        }
    }

    //填充下钻趋势线数据
    public static void processDrilldownTrendLineData(List<TrendLineDetailInfo> trendLineDetailInfoList,
                                                     PeriodDataBean periodDataBean,
                                                     List<String> timeList) {
        Map<String, Double> dimMap = new HashMap<>();
        Set<String> drillDownSet = new HashSet<>();
        List<List<Object>> weaknessList = periodDataBean.getWeaknessList();
        List<String> weaknessHeaderList = periodDataBean.getWeaknessHeaderList();
        List<String> reachGroupList = weaknessHeaderList.subList(0, 2);
        List<String> reachDimList = weaknessHeaderList.subList(2, weaknessHeaderList.size());

        ChartHelper.setMonthlyMetricTrendlineActualReach(dimMap, drillDownSet, weaknessList, reachGroupList, reachDimList);


        Map<String, String> typeMap = new HashMap<>();
        typeMap.put("w_line_rate", "lineChart");

        ChartHelper.fillLineChartTrendLineDataWithDrillDown(null, timeList, dimMap, trendLineDetailInfoList,
                typeMap, drillDownSet, false);


    }

    //填充下钻基础数据
    public static void processDrillDownBaseInfo(String field,
                                                GetRawDataResponseType response,
                                                List<FieldDataItem> fieldDataItemList) {
        ChartHelper.fillFieldDataItemList(field, MapperUtil.str2ListList(response.getResult(), Object.class), fieldDataItemList);
    }

    //填充表格数据
    public static void processTableData(GetRawDataResponseType res,
                                        List<TableDataItem> tableDataItemList) {
        List<List<Object>> rawResultList = MapperUtil.str2ListList(res.getResult(), Object.class);
        List<String> groupList = res.getGroupList();
        List<String> metricList = res.getMetricList();
        ChartHelper.fillCommmonTableData(tableDataItemList, groupList, metricList, new ArrayList<>(), rawResultList, new ArrayList<>());
    }


    public static SqlParamterBean getMetricCardSqlBean(TimeFilter timeFilter,
                                                       ExamineConfigBean examineConfigBean,
                                                       MetricInfoBean metricInfoBean,
                                                       String domainName,
                                                       String d) {
        SqlParamterBean bean = new SqlParamterBean();

        bean.setId(69L);

        Map<String, String> andMap = new HashMap<>();
        bean.setAndMap(andMap);
        setTimeValue(andMap, timeFilter, examineConfigBean, d);

        andMap.put("statistics_dim_id", "1");
        //config changed
        String level = metricInfoBean.getOdtLevel();
        andMap.put("examine_level", level);
        andMap.put("examinee", domainName);

        return bean;
    }


    public static SqlParamterBean getTrendLineWithDrillDownSqlBean(MetricInfoBean metricInfoBean,
                                                                   String d,
                                                                   DrillDownFilter drillDownFilter,
                                                                   ExamineConfigBean examineConfigBean,
                                                                   RemoteConfig remoteConfig) {
        SqlParamterBean bean = new SqlParamterBean();

        String field = MetricHelper.getDrillDownColumnName(drillDownFilter.getField());


        bean.setId(69L);

        Map<String, String> andMap = new HashMap<>();
        Map<String,String> likeMap = new HashMap<>();
        bean.setAndMap(andMap);
        bean.setLikeMap(likeMap);
        setTimeValue(andMap, null, examineConfigBean, d);

        andMap.put("examine_level", MetricHelper.getFieldChineseName(field));
        if ("region_name".equals(field)) {
            andMap.put("statistics_dim_id", "3");
        } else if ("province_name".equals(field)) {
            andMap.put("statistics_dim_id", "4");
        } else {
            andMap.put("statistics_dim_id", "1");
            //如果按商拓下钻  其实取的是考核层级是省份的数据 所以重新设置考核层级
            String province = remoteConfig.getConfigValue("province");
            andMap.put("examine_level", province);
        }

        String level = MetricHelper.getLevelColumnName(metricInfoBean.getOdtLevel());

        if ("examinee".equals(field)) {
            setExamineLikeValue(likeMap, level, metricInfoBean);
        } else {
            setExamineValue(andMap, level, metricInfoBean);
        }

        List<String> fieldValueList = drillDownFilter.getFieldValueList();
        setDrillDownValue(andMap, field, fieldValueList);

        bean.setGroupList(Lists.newArrayList(field));

        return bean;
    }


    public static SqlParamterBean getDrillDownBaseInfoSqlBean(String field,
                                                              GetDrillDownBaseInfoRequestType request,
                                                              String d,
                                                              MetricInfoBean metricInfoBean,
                                                              RemoteConfig remoteConfig) {
        SqlParamterBean bean = new SqlParamterBean();

        DrillDownFieldBean configBean = remoteConfig.getDrillDownFieldBean("12", null, field);

        bean.setId(configBean.getBaseInfoId());

        List<String> baseInfoGroupList = configBean.getBaseInfoGroupList();
        bean.setGroupList(baseInfoGroupList);
        bean.setOrderList(Lists.newArrayList(baseInfoGroupList.get(0)));
        bean.setOrderTypeList(Lists.newArrayList("asc"));

        Map<String, String> andMap = new HashMap<>();
        setTimeValue(andMap, request.getTimeFilter(), null, d);

        andMap.put("examine_level", MetricHelper.getFieldChineseName(field));
        if ("region_name".equals(field)) {
            andMap.put("statistics_dim_id", "3");
        } else if ("province_name".equals(field)) {
            andMap.put("statistics_dim_id", "4");
        } else {
            andMap.put("statistics_dim_id", "2");
            //如果按商拓下钻  其实取的是考核层级是省份的数据 所以重新设置考核层级
            String province = remoteConfig.getConfigValue("province");
            andMap.put("examine_level", province);
        }


        String level = MetricHelper.getLevelColumnName(metricInfoBean.getOdtLevel());
        setExamineValue(andMap, level, metricInfoBean);
        bean.setAndMap(andMap);

        Boolean needSearch = request.isNeedSearch();
        if (needSearch) {
            String searchWord = request.getSearchWord();
            Map<String, String> likeMap = new HashMap<>();
            List<Integer> likeIndexList = configBean.getBaseInfoLikeIndexList();
            for (Integer index : likeIndexList) {
                likeMap.put(baseInfoGroupList.get(index), searchWord);
            }
            bean.setLikeMap(likeMap);
        }

        return bean;
    }


    public static SqlParamterBean getRankingSqlBean(String domainName,
                                                    TimeFilter timeFilter,
                                                    String d) throws ParseException {
        SqlParamterBean bean = new SqlParamterBean();

        bean.setId(73L);

        Map<String, String> andMap = new HashMap<>();
        bean.setAndMap(andMap);

        andMap.put("examinee", domainName);
        setRankingTimeValue(timeFilter, d, andMap);

        return bean;

    }

    public static SqlParamterBean getTableSqlBean(GetTableDataRequestType request,
                                                  MetricInfoBean metricInfoBean,
                                                  String d,
                                                  RemoteConfig remoteConfig) {
        SqlParamterBean bean = new SqlParamterBean();

        TimeFilter timeFilter = request.getTimeFilter();
        DrillDownFilter drillDownFilter = request.getDrillDownFilter();

        String field = MetricHelper.getDrillDownColumnName(drillDownFilter.getField());

        DrillDownFieldBean configBean = remoteConfig.getDrillDownFieldBean("12", null, field);

        bean.setId(configBean.getBaseInfoId());


        Map<String, String> andMap = new HashMap<>();
        bean.setAndMap(andMap);
        setTimeValue(andMap, timeFilter, null, d);

        andMap.put("examine_level", MetricHelper.getFieldChineseName(field));
        if ("region_name".equals(field)) {
            andMap.put("statistics_dim_id", "3");
        } else if ("province_name".equals(field)) {
            andMap.put("statistics_dim_id", "4");
        } else {
            andMap.put("statistics_dim_id", "2");
            //如果按商拓下钻  其实取的是考核层级是省份的数据 所以重新设置考核层级
            String province = remoteConfig.getConfigValue("province");
            andMap.put("examine_level", province);
        }

        String level = MetricHelper.getLevelColumnName(metricInfoBean.getOdtLevel());

        setExamineValue(andMap, level, metricInfoBean);

        List<String> fieldValueList = drillDownFilter.getFieldValueList();
        setDrillDownValue(andMap, field, fieldValueList);

        String source = "detailpage".equals(request.getSource()) ? "detailpage" : "firstpage";
        List<String> groupList = configBean.getTableGroupListMap().get(source);
        bean.setGroupList(groupList);
        bean.setOrderList(Lists.newArrayList(field));
        bean.setOrderTypeList(Lists.newArrayList("desc"));
        bean.setPageNo(request.getPageNo());
        bean.setPageSize(request.getPageSize());


        return bean;
    }

    private static void setRankingTimeValue(TimeFilter timeFilter,
                                            String d,
                                            Map<String, String> andMap) throws ParseException {
        andMap.put("query_d", d);

        String year = timeFilter.getYear();
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();
        String dateType = timeFilter.getDateType();
        andMap.put("year", year);
        month = "month".equals(dateType) ? month : DateUtil.getMappingMonthByQuarter(d, year, quarter);
        andMap.put("month", month);
    }




    private static void setTimeValue(Map<String, String> andMap,
                                     TimeFilter timeFilter,
                                     ExamineConfigBean examineConfigBean,
                                     String d) {
        String year;
        String month;
        String quarter;
        String dateType;
        if (GeneralUtil.isNotEmpty(timeFilter)) {
            year = timeFilter.getYear();
            month = timeFilter.getMonth();
            quarter = timeFilter.getQuarter();
            dateType = MetricHelper.getDateTypeColumnName(timeFilter.getDateType());
        } else {
            year = examineConfigBean.getYear();
            month = examineConfigBean.getMonth();
            quarter = examineConfigBean.getQuarter();
            dateType = MetricHelper.getDateTypeColumnName(examineConfigBean.getDateType());
        }

        List<String> timeList = DateUtil.getTimeList(dateType, month, quarter);

        andMap.put("year", year);
        andMap.put(dateType, StringUtils.join(timeList, "|"));
        andMap.put("query_d", d);
    }

    private static void setExamineValue(Map<String, String> andMap,
                                        String level,
                                        MetricInfoBean metricInfoBean) {
        List<String> regionList = metricInfoBean.getOdtRegionList();
        if (!"".equals(level)) {
            andMap.put(level, StringUtils.join(regionList, "|"));
        }
    }

    /**
     * 设置like查询条件
     * @param likeMap
     * @param level
     * @param metricInfoBean
     */
    private static void setExamineLikeValue(Map<String, String> likeMap,
                                            String level,
                                            MetricInfoBean metricInfoBean){
        List<String> regionList = metricInfoBean.getOdtRegionList();
        if (!"".equals(level)) {
            //hard code
            String key = "region_name".equals(level) ? "examine_range_region" : "examine_range";
            likeMap.put(key, StringUtils.join(regionList, "|"));
        }
    }

    private static void setDrillDownValue(Map<String, String> andMap,
                                          String field,
                                          List<String> fieldValueList) {
        if (GeneralUtil.isNotEmpty(fieldValueList)) {
            andMap.put(field, StringUtils.join(fieldValueList, "|"));
        }
    }


    public static Integer getGapDays(TimeFilter timeFilter,
                                     ExamineConfigBean examineConfigBean,
                                     String d) throws ParseException {
        String year;
        String month;
        String quarter;
        String half;
        String dateType;
        if (GeneralUtil.isNotEmpty(timeFilter)) {
            year = timeFilter.getYear();
            month = timeFilter.getMonth();
            quarter = timeFilter.getQuarter();
            half = timeFilter.getHalf();
            dateType = MetricHelper.getDateTypeColumnName(timeFilter.getDateType());
        } else {
            year = examineConfigBean.getYear();
            month = examineConfigBean.getMonth();
            quarter = examineConfigBean.getQuarter();
            //计算趋势线不会按照半年为粒度看数据
            half = "";
            dateType = MetricHelper.getDateTypeColumnName(examineConfigBean.getDateType());
        }
        return DateUtil.getOverallDaysOfMonthOrQuarterOrHalf(year, dateType, month, quarter, half, d);
    }


    //获取某个考核层级对应的下钻维度
    public static List<String> getDrillDownFieldList(MetricInfoBean metricInfoBean) {
        String level = MetricHelper.getLevelColumnName(metricInfoBean.getOdtLevel());
        switch (level) {
            case "":
                return Lists.newArrayList("region_name", "province_name", "examinee");
            case "region_name":
                return Lists.newArrayList("province_name", "examinee");
            case "province_name":
                return Lists.newArrayList("province_name", "examinee");
            case "examinee":
                return Lists.newArrayList("examinee");
            default:
                throw new ConfigImportException("metric 10 has error config:" + metricInfoBean.getLevel());
        }
    }


    public static SqlParamterBean getGapDaysSqlBean(String year,
                                                    String dateType,
                                                    String month,
                                                    String quarter,
                                                    String metric,
                                                    String d) {
        SqlParamterBean bean = new SqlParamterBean();

        bean.setId(60L);

        Map<String, String> andMap = new HashMap<>();
        bean.setAndMap(andMap);
        andMap.put("query_d", d);
        andMap.put("metric", metric);
        andMap.put("year", year);

        andMap.put("date_type", dateType);
        if ("month".equals(dateType)) {
            List<String> monthList = DateUtil.getMonthList(month ,false);
            andMap.put("month", StringUtils.join(monthList, "|"));
        } else if ("quarter".equals(dateType)) {
            andMap.put("quarter", quarter);
        }

        return bean;
    }


//    public static Integer getGapDays(GetRawDataResponseType response) {
//        List<List<Object>> rawResultList = MapperUtil.str2ListList(response.getResult(), Object.class);
//        if(GeneralUtil.isEmpty(rawResultList) || GeneralUtil.isEmpty(rawResultList.get(0)) || GeneralUtil.isEmpty(rawResultList.get(0).get(0))){
//            return null;
//        }
//        return Integer.valueOf(String.valueOf(rawResultList.get(0).get(0)));
//    }


    /**
     * 获取查询数据时是否需要扣减额外的天数
     * 2024之前的数据没有剔除5天的逻辑
     *
     * @param timeFilter
     * @param examineConfigBean
     * @param d
     * @param baseMap
     * @return
     * @throws ParseException
     */
    public static Integer getAddtionalGapDay(TimeFilter timeFilter,
                                             ExamineConfigBean examineConfigBean,
                                             String d,
                                             Map<String, String> baseMap) throws ParseException {
        if (GeneralUtil.isNotEmpty(timeFilter)) {
            String year = timeFilter.getYear();
            if(Integer.parseInt(year) < 2024){
                return 0;
            }
            return Bus567Helper.getAddtionalGapDay(null, baseMap, year, timeFilter.getDateType(),
                    timeFilter.getMonth(), timeFilter.getQuarter(), "", d);
        } else {
            String year = examineConfigBean.getYear();
            if(Integer.parseInt(year) < 2024){
                return 0;
            }
            return Bus567Helper.getAddtionalGapDay(null, baseMap, year, examineConfigBean.getDateType(),
                    examineConfigBean.getMonth(), examineConfigBean.getQuarter(), "", d);
        }

    }

}
