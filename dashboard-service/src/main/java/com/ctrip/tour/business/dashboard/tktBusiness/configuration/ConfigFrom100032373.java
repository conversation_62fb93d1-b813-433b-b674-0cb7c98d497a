package com.ctrip.tour.business.dashboard.tktBusiness.configuration;

import com.ctrip.tour.business.dashboard.tktBusiness.bean.BenchRegion;
import com.ctrip.tour.business.dashboard.utils.MapperUtil;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.JsonConfig;
import qunar.tc.qconfig.client.spring.QConfig;
import qunar.tc.qconfig.client.spring.QMapConfig;

import java.util.List;


@Component
public class ConfigFrom100032373 {

//    private static JsonConfig<BenchRegion> persionJsonConfig = JsonConfig.get(,"testjson.json", BenchRegion.class);

    @QConfig(value = "100032373#bench-region-manager-settings.json")
    private List<BenchRegion> benchRegionManagerSettingsStrFrom100032373;

    public List<BenchRegion> getBenchRegionManagerSettings(){
        return MapperUtil.str2List(MapperUtil.obj2Str(benchRegionManagerSettingsStrFrom100032373),BenchRegion.class);
    }
}
