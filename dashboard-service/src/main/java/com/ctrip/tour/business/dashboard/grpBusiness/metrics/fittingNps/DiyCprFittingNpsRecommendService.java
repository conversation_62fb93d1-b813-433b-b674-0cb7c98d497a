package com.ctrip.tour.business.dashboard.grpBusiness.metrics.fittingNps;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import com.ctrip.platform.dal.dao.helper.DalColumnMapRowMapper;
import com.ctrip.tour.business.dashboard.grpBusiness.annotation.IndexAssemblyHandler;
import com.ctrip.tour.business.dashboard.grpBusiness.dao.starrocks.CdmSevGrpCprPlatformSelfSrvCrDfDao;
import com.ctrip.tour.business.dashboard.grpBusiness.enums.MetricCategoryEnum;
import com.ctrip.tour.business.dashboard.grpBusiness.metrics.IndexCommonQueryAbstractSerice;
import com.ctrip.tour.business.dashboard.utils.SqlBuilder;
import com.google.common.base.Joiner;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2024/12/11
 * 推荐率
 */
@Service
@IndexAssemblyHandler(calcDateName = "tour_enddate",
        calcExpression = "sum_rec / sum_odr", calcFieldName = "sum_rec,sum_odr",
        tableName = "adm_sev_cus_metrics_tag_to_workbench_df")
@Slf4j
public class DiyCprFittingNpsRecommendService extends IndexCommonQueryAbstractSerice {

    @Autowired
    CdmSevGrpCprPlatformSelfSrvCrDfDao selfSrvCrDfDao;

    public DiyCprFittingNpsRecommendService(ApplicationContext ac) {
        super(ac);
    }

    @Override
    protected List<Map<String, Object>> handleResults(IndexCommonQueryAbstractSerice serice, Map<String, ?> param, String timeAggType, boolean needTimeAgg, List<Map<String, Object>> rowData, List<String> groupCols) {
        return doHandleResults(serice, param, timeAggType, needTimeAgg, rowData);
    }

    @Override
    protected String otherCon(Map<String, ?> param, List<String> groupByCols, String timeAggType) {
        return doOtherCon(param, MetricCategoryEnum.FITTING_NPS_CATEGORY.getEnglishName());
    }

    @Override
    protected SqlBuilder selectColsAssembly(SqlBuilder sqlBuilder, Map<String, ?> param, List<String> groupByCols, String timeAggType) {

        String sql = " sum(nps_supplier_recommend_v5)  as sum_rec, " +
                " sum(nps_denominator_v5) as sum_odr ";
        if (CollectionUtils.isNotEmpty(groupByCols)) {
            sql = String.join(",", sql, Joiner.on(",").skipNulls().join(groupByCols));
        }
        sqlBuilder.select(sql);

        return sqlBuilder;
    }

    @Override
    protected List<Map<String, Object>> queryData(String sql, Map<String, ?> param, List<String> groupByCols, String timeAggType) {
        List<Map<String, Object>> queryData = null;
        try {
            DalColumnMapRowMapper dalColumnMapRowMapper = new DalColumnMapRowMapper();
            queryData = selfSrvCrDfDao.query(sql, param);
            return queryData;
        } catch (SQLException e) {
            log.warn("query cdm_sev_grp_cpr_platform_fitting_nps_df error", e);

        }
        return null;
    }
}
