package com.ctrip.tour.business.dashboard.tktBusiness.helper;

import com.alibaba.druid.sql.SQLUtils;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.sql.ast.statement.SQLSelectItem;
import com.alibaba.druid.sql.ast.statement.SQLSelectQuery;
import com.alibaba.druid.sql.ast.statement.SQLSelectStatement;
import com.alibaba.druid.sql.dialect.mysql.ast.statement.MySqlSelectQueryBlock;
import com.alibaba.druid.util.JdbcConstants;
import com.ctrip.soa._27181.*;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.TktStarRocksDao;
import com.ctrip.tour.business.dashboard.tktBusiness.select43556.*;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.MapperUtil;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.SQLException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class TaskFlowSelect43556Helper {

    @Autowired
    private ReportQueryBasicConfigDao basicConfigDao;
    @Autowired
    private ReportQueryWhitelistDao whitelistDao;
    @Autowired
    private BIBaseReportQueryServiceClient client;
    @Autowired
    private TktStarRocksDao tktStarRocksDao;
    @Autowired
    private RemoteConfig remoteConfig;


    public List<Map<String, Object>> getStarRocksData(GetRawDataRequestType request, String drillDownMetric) throws SQLException {
        //获取基础配置信息
        Long queryId = request.getQueryId();
        ReportQueryBasicConfig basicConfig = basicConfigDao.getConfigByQueryId(queryId);
        if (GeneralUtil.isEmpty(basicConfig)) {
            throw new InputArgumentException(10001);
        }

        //获取白名单信息
        List<ReportQueryWhitelist> whitelist = whitelistDao.getWhitelistByQueryId(queryId);

        // 根据参数和白名单和基础配置信息拼接sql
        Boolean needScript = request.isNeedScript();
        if(GeneralUtil.isEmpty(needScript)){
            request.setNeedScript(false);
        }
        List<GenerateSqlBean> sqlBeanList = generateSql(request, basicConfig, whitelist,drillDownMetric);

        List<Map<String, Object>> result = tktStarRocksDao.getListResult(sqlBeanList.get(0).getSql(),sqlBeanList.get(0).getPreparedParameters());

        return result;
    }

    public GetRawDataResponseType getRawDataBy43556(GetRawDataRequestType request) throws SQLException {
        return getRawDataBy43556(request,"");
    }

    public GetRawDataResponseType getRawDataBy43556(GetRawDataRequestType request, String drillDownMetric) throws SQLException {

        GetRawDataResponseType rawDataResponseType = new GetRawDataResponseType();

        //获取基础配置信息
        Long queryId = request.getQueryId();
        ReportQueryBasicConfig basicConfig = basicConfigDao.getConfigByQueryId(queryId);
        if (GeneralUtil.isEmpty(basicConfig)) {
            throw new InputArgumentException(10001);
        }

        //获取白名单信息
        List<ReportQueryWhitelist> whitelist = whitelistDao.getWhitelistByQueryId(queryId);

        // 根据参数和白名单和基础配置信息拼接sql
        Boolean needScript = request.isNeedScript();
        if(GeneralUtil.isEmpty(needScript)){
            request.setNeedScript(false);
        }
        List<GenerateSqlBean> sqlBeanList = generateSql(request, basicConfig, whitelist,drillDownMetric);

        Map<String, Object> asyncResult = getResultAsync(sqlBeanList, basicConfig);

        if(asyncResult.get("count")!=null){
            rawDataResponseType.setTotalNum((Integer)asyncResult.get("count"));
        }
        rawDataResponseType.setResult(MapperUtil.obj2Str(asyncResult.get("result")));
        rawDataResponseType.setGroupList(request.getGroupList());
        rawDataResponseType.setMetricList(getMetricList(basicConfig.getScript(), request.isNeedScript()));


        return rawDataResponseType;
    }


    private Map<String, Object> getResultAsync(List<GenerateSqlBean> sqlBeanList,ReportQueryBasicConfig basicConfig){


        String dbname = basicConfig.getDbname();
        String tablename = GeneralUtil.isEmpty(basicConfig.getTablenameAlias())
                ? basicConfig.getTablename() : basicConfig.getTablenameAlias();


        Map<String, Object> map = new HashMap<>();
        Map<GenerateSqlBean, GetStarrocksDataResponseType> futureMap = new HashMap<>();
        for (GenerateSqlBean sqlBean : sqlBeanList) {
            futureMap.put(sqlBean, switchQueryStarRocksData(sqlBean,tablename));
        }
        for (Map.Entry<GenerateSqlBean, GetStarrocksDataResponseType> entry : futureMap.entrySet()) {
            GenerateSqlBean sqlBean = entry.getKey();
            String value = entry.getValue().getResult();
            List<JSONArray> valueList = MapperUtil.str2List(value, JSONArray.class);
            if(sqlBean.getNeedCount()){
                map.put("count", getTotalNum(valueList));
            }else{
                map.put("result", valueList);
            }
        }
        return map;
    }

    private Integer getTotalNum(List<JSONArray> result) {
        return Integer.valueOf(String.valueOf(result.get(0).getInt(0)));
    }

    private GetStarrocksDataResponseType aaa(GenerateSqlBean generateSqlBean,String tablename){


        String sql = generateSqlBean.getSql();
        String dbName = generateSqlBean.getDbName();
        String engine = generateSqlBean.getEngine();

        List<PreparedParameterBean> preparedParameters = generateSqlBean.getPreparedParameters();
        GetStarrocksDataRequestType requestType = new GetStarrocksDataRequestType();
        
        requestType.setOriginQueryEngine(engine);
        requestType.setDBName(dbName);
        requestType.setTableName(tablename);
        requestType.setOriginSQL(sql);
        requestType.setParameters(preparedParameters);

        requestType.setExecuteTime(DateUtil.getCurrentTime("yyyy-MM-dd HH:mm:ss.SSS"));
        requestType.setHashCode(UUID.randomUUID().toString());

        GetStarrocksDataResponseType starrocksDataResponseType = null;
        try {
            starrocksDataResponseType = client.getStarrocksData(requestType);
        } catch (Exception e) {
            System.out.println("getStarrocksData error"+MapperUtil.obj2Str(requestType));
            throw new RuntimeException(e);
        }

        return starrocksDataResponseType;

    }

    private GetStarrocksDataResponseType switchQueryStarRocksData(GenerateSqlBean generateSqlBean,String tablename){
        String switchValue = remoteConfig.getConfigValue("starrocks.xy.switch");
        if("on".equals(switchValue)){
            try {
                JSONArray jsonArray = tktStarRocksDao.getListResult1(generateSqlBean.getSql(),generateSqlBean.getPreparedParameters());
                GetStarrocksDataResponseType responseType = new GetStarrocksDataResponseType();
                responseType.setResult(MapperUtil.obj2Str(jsonArray));
                responseType.setTotalNum(jsonArray.size());
                return responseType;
            } catch (SQLException e) {
                log.error("query starrocks failed! executeSQL:{}, params:{}", generateSqlBean.getSql(), MapperUtil.obj2Str(generateSqlBean.getPreparedParameters()),e);
                throw new RuntimeException(e);
            }
        }else {
            return aaa(generateSqlBean,tablename);
        }
    }


    public static List<String> getMetricList(String script,
                                             Boolean needScript) {

        List<String> metricList = new ArrayList<>();

        if(needScript){
            //目前仅考虑单表查询  将脚本组装成一个虚拟的sql
            String sql = "select " + script + " from xx";
            List<SQLStatement> stmtList = SQLUtils.parseStatements(sql, JdbcConstants.MYSQL);
            SQLSelectStatement stmt0 = (SQLSelectStatement) stmtList.get(0);
            SQLSelectQuery sqlSelectQuery = stmt0.getSelect().getQuery();
            List<SQLSelectItem> selectList = ((MySqlSelectQueryBlock) sqlSelectQuery).getSelectList();
            for (SQLSelectItem item : selectList) {
                metricList.add(GeneralUtil.isEmpty(item.getAlias()) ? item.toString() : item.getAlias());
            }
        }
        return metricList;
    }

    public List<GenerateSqlBean> generateSql(GetRawDataRequestType request,
                                             ReportQueryBasicConfig basicConfig,
                                             List<ReportQueryWhitelist> whitelist,
                                             String drillDownMetric) {

        List<GenerateSqlBean> sqlBeanList = new ArrayList<>();
        List<PreparedParameterBean> parameters = new ArrayList<>();

        Map<String, ReportQueryWhitelist> whiteListMap = whitelist.stream()
                .collect(Collectors.toMap(ReportQueryWhitelist::getColumnName, Function.identity(), (k1, k2) -> k1));

        StringBuilder sb = new StringBuilder();
        //拼接select子句
        JdbcUtil.jointSelectClause(sb, request, basicConfig, whiteListMap, parameters,drillDownMetric);
        //拼接where子句
        JdbcUtil.jointWhereClause(sb, request, whiteListMap, parameters);

        //拼接group by条件
        JdbcUtil.jointGroupClause(sb, request, whiteListMap);
        //拼接order by条件
        JdbcUtil.jointOrderClause(sb, request, whiteListMap);
        //设置count(*)查询 如果必要的话
        JdbcUtil.setCountQuery(sb, request, parameters, basicConfig.getDbname(), sqlBeanList);
        //拼接limit参数
        JdbcUtil.jointLimitClause(sb, request, parameters);
        GenerateSqlBean sqlBean = new GenerateSqlBean();
        sqlBean.setPreparedParameters(parameters);
        sqlBean.setSql(sb.toString());
        sqlBean.setDbName(basicConfig.getDbname());
        sqlBean.setEngine("starrocks");
        sqlBeanList.add(sqlBean);
        return sqlBeanList;
    }

}
