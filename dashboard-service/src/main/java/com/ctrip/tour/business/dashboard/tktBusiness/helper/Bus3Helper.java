package com.ctrip.tour.business.dashboard.tktBusiness.helper;

import com.ctrip.soa._24922.TableDataItem;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.ConfigImportException;
import com.google.common.collect.Lists;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/8/5
 */
public class Bus3Helper {

    public static List<String> getReachDimList() {
        return Lists.newArrayList("ttd_qa_cost", "ttd_suc_income", "ttd_orders", "ttd_qa_cost_rate");
    }

    public static List<String> getReachDimList(String year) {
        int y = year != null ? Integer.parseInt(year) : 0;
        if(y >= 2024){
        // if("2024".equals(year)){
            return Lists.newArrayList("ttd_weighted_defect_cnt", "ttd_pay_odr_cnt", "ttd_weighted_defect_rate");
        }else{
            return Lists.newArrayList("ttd_qa_cost", "ttd_suc_income", "ttd_orders", "ttd_qa_cost_rate");
        }
    }

//    public static List<String> getComplexDimList() {
//        return Lists.newArrayList("ttd_qa_cost_rate");
//    }


    public static List<String> getTargetDimList() {
        return Lists.newArrayList("ttd_trgt_qa_cost_rate");
    }

    public static List<String> getTargetDimList(String year) {
        int y = year != null ? Integer.parseInt(year) : 0;
        if(y >= 2024){
        // if("2024".equals(year)){
            return Lists.newArrayList("ttd_weighted_defect_target");
        }else{
            return Lists.newArrayList("ttd_trgt_qa_cost_rate");
        }
    }

    //对于2022年  对应的字段取ttd_suc_income
    //对于2023年以及以后 对应的字段取ttd_orders
    public static void makeUpMetricCardData(Map<String, Double> dimMap,
                                            String year) {
//        String extraDim1 = "ttd_qa_cost_rate";
//        Double dim1Fenzi = dimMap.get("ttd_qa_cost");
//        Double dim1Fenmu;
//        if ("2022".equals(year)) {
//            dim1Fenmu = dimMap.get("ttd_suc_income");
//        } else {
//            dim1Fenmu = dimMap.get("ttd_orders");
//        }
//
//        if (GeneralUtil.isValidDivide(dim1Fenzi, dim1Fenmu)) {
//            dimMap.put(extraDim1, dim1Fenzi / dim1Fenmu);
//        }
        String extraDim2;
        int y = year != null ? Integer.parseInt(year) : 0;
        if(y >= 2024){
        // if("2024".equals(year)){
            extraDim2 = "ttd_weighted_defect_achieved_rate";
        }else{
            extraDim2 = "ttd_qa_cost_rate_gap";
        }

        Double extraDim2Value = DimHelper.getSpecialDimValue(extraDim2, "", dimMap, null);
        dimMap.put(extraDim2, extraDim2Value);
    }

    public static void makeUpTableData(List<TableDataItem> tableDataItemList, String year) {
        for (TableDataItem item : tableDataItemList) {
            makeUpMetricCardData(item.getDimMap(), year);
        }
    }

    public static void makeUpMetricCardPopData(String year,
                                               Map<String, Double> dimMap,
                                               Map<String, Double> popDimMap,
                                               String preffix) {
        String extraDim;
        int y = year != null ? Integer.parseInt(year) : 0;
        if(y >= 2024){
        // if("2024".equals(year)){
            extraDim = "ttd_weighted_defect_rate_lastyear";
        }else{
            extraDim = "ttd_qa_cost_rate_lastyear";
        }
        Double extraDimValue = DimHelper.getSpecialDimValue(extraDim, preffix, dimMap, popDimMap);
        dimMap.put(extraDim, extraDimValue);
    }

    public static Map<String, String> getLineChartTrendlineType(String year) {
        Map<String, String> typeMap = new LinkedHashMap<>();
        int y = year != null ? Integer.parseInt(year) : 0;
        if(y >= 2024){
        // if("2024".equals(year)){
            typeMap.put("ttd_weighted_defect_rate", "lineChart");
            typeMap.put("ttd_weighted_defect_target", "lineChart");
        }else{
            //这里map的顺序不能变  必须先把原始值算出来
            typeMap.put("ttd_qa_cost_rate", "lineChart");
            typeMap.put("ttd_trgt_qa_cost_rate", "lineChart");
            //typeMap.put("ttd_qa_cost|ttd_suc_income|/_gap", "lineChart");
        }
        return typeMap;
    }

    public static Map<String,String> getLineChartTrendlineTypeWithDrillDown(String year){
        Map<String, String> typeMap = new HashMap<>();
        int y = year != null ? Integer.parseInt(year) : 0;
        if(y >= 2024){
        // if("2024".equals(year)){
            typeMap.put("ttd_weighted_defect_rate", "lineChart");
        }else{
            typeMap.put("ttd_qa_cost_rate", "lineChart");
        }
        return typeMap;
    }

    //根据考核层级确定可下钻维度
    public static List<String> getFieldList(String level){
        switch (level){
            case "国内":
            case "三方":
                return Lists.newArrayList("region_name","province_name");
            case "大区":
            case "省份":
                return Lists.newArrayList("province_name");
            default:
                throw new ConfigImportException("GMV/质量成本中导入了错误的层级:"+level);
        }
    }
}
