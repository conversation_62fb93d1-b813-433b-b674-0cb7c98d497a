package com.ctrip.tour.business.dashboard.grpBusiness.metrics.fittingNps;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.Objects;


import com.ctrip.tour.business.dashboard.grpBusiness.dao.starrocks.CdmSevGrpCprPlatformSelfSrvCrDfDao;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.base.SQLResult;
import com.ctrip.platform.dal.dao.helper.DalColumnMapRowMapper;
import com.ctrip.tour.business.dashboard.grpBusiness.annotation.IndexAssemblyHandler;
import com.ctrip.tour.business.dashboard.grpBusiness.enums.MetricCategoryEnum;
import com.ctrip.tour.business.dashboard.grpBusiness.metrics.IndexCommonQueryAbstractSerice;
import com.ctrip.tour.business.dashboard.utils.SqlBuilder;
import com.google.common.base.Joiner;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2024/12/11
 * 拟合nps
 */
@Service
@IndexAssemblyHandler(calcDateName = "return_date",
        calcExpression = "rec_sla_tru / sum_order_weight", calcFieldName = "rec_sla_tru,sum_order_weight",
        tableName = "cdm_sev_grp_cpr_platform_fitting_nps_df")
@Slf4j
public class GrpCprFittingNpsCntService extends IndexCommonQueryAbstractSerice {

    @Autowired
    CdmSevGrpCprPlatformSelfSrvCrDfDao selfSrvCrDfDao;

    public GrpCprFittingNpsCntService(ApplicationContext ac) {
        super(ac);
    }

    @Override
    protected String otherCon(Map<String, ?> param, List<String> groupByCols, String timeAggType) {
        return doOtherCon(param, MetricCategoryEnum.FITTING_NPS_CATEGORY.getEnglishName());
    }

    @Override
    protected SqlBuilder selectColsAssembly(SqlBuilder sqlBuilder, Map<String, ?> param, List<String> groupByCols, String timeAggType) {

        String sql = " sum(recommend_weight - slander_weight) as rec_sla_tru, " +
                "  sum(order_weight) as sum_order_weight ";

        if (CollectionUtils.isNotEmpty(groupByCols)) {
            sql = String.join(",", sql, Joiner.on(",").skipNulls().join(groupByCols));
        }

        sqlBuilder.select(sql);

        return sqlBuilder;
    }

    @Override
    protected List<Map<String, Object>> queryData(String sql, Map<String, ?> param, List<String> groupByCols, String timeAggType) {
        List<Map<String, Object>> queryData = null;
        try {
            DalColumnMapRowMapper dalColumnMapRowMapper = new DalColumnMapRowMapper();
            queryData = selfSrvCrDfDao.query(sql,param);
            return queryData;
        } catch (SQLException e) {
            log.warn("query cdm_sev_grp_cpr_platform_fitting_nps_df error", e);

        }
        return null;
    }

    @Override
    protected List<Map<String, Object>> handleResults(IndexCommonQueryAbstractSerice serice, Map<String, ?> param, String timeAggType, boolean needTimeAgg, List<Map<String, Object>> rowData, List<String> groupCols) {
        return doHandleResults(serice, param, timeAggType, needTimeAgg, rowData);
    }
}
