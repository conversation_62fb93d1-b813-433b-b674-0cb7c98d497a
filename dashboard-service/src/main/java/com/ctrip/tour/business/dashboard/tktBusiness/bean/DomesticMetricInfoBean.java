package com.ctrip.tour.business.dashboard.tktBusiness.bean;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class DomesticMetricInfoBean {

    //角色
    private Integer role;
    private String  roleRange;
    //指标
    private String metric;
    //门票考核层级
    private String level;
    //门票考核层级对应非景点的配置
    private List<String> regionList;

    //门票考核层级对应景点的配置??
    private List<String> bdList;


    //活动考核层级(仅门票活动)
    private String actLevel;
    //活动考核层级对应非景点的配置(仅门票活动)
    private List<String> actRegionList;
    //活动考核层级对应景点的配置(仅门票活动)
    private List<String> actBdList;

    //国内日游考核层级
    private String odtLevel;
    //出境日游考核层级对应的大区省份配置
    private List<String> odtRegionList;

}
