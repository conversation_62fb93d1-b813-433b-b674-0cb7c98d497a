package com.ctrip.tour.business.dashboard.tktBusiness.helper;

import com.ctrip.soa._24922.*;
import com.ctrip.soa._27181.GetRawDataResponseType;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.*;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.InputArgumentException;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.ctrip.tour.business.dashboard.utils.MapperUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

public class Bus103Helper {


    /**
     * 填充首页指标卡所需的下钻信息
     * @param metricDetailInfo 指标卡信息
     * @param subMetric 子指标
     * @param metricInfoBean 海外指标配置
     * @param remoteConfig  qconfig配置
     */
    public static void setMetricCardDrillDownParmater(MetricDetailInfo metricDetailInfo,
                                                      String subMetric,
                                                      OverseaMetricInfoBean metricInfoBean,
                                                      RemoteConfig remoteConfig){
        Bus101102Helper.setMetricCardDrillDownParmater(metricDetailInfo, subMetric, metricInfoBean, remoteConfig);
    }


    /**
     * 获取指标卡请求参数(目的地维度)
     * @param type  current当前数据  lastyear去年数据  target目标数据
     * @param timeFilter
     * @param metricInfoBean
     * @param d
     * @param subMetric
     * @param remoteConfig
     * @return
     */
    public static SqlParamterBean getDestinationMetricCardSqlBean(String type,
                                                                  TimeFilter timeFilter,
                                                                  OverseaMetricInfoBean metricInfoBean,
                                                                  String d,
                                                                  String subMetric,
                                                                  RemoteConfig remoteConfig) throws Exception {
        SqlParamterBean bean = new SqlParamterBean();
        String year = timeFilter.getYear();
        int y = year != null ? Integer.parseInt(year) : 0;
        Map<String, String> baseMap = OverseaMetricHelper.generateQuarterMetricBaseMap("103", timeFilter, type, d, null);
        if ("target".equals(type)) {
            Long queryId = y >= 2024 ? 70L : 38L;
            bean.setId(queryId);
            OverseaMetricHelper.setDestinationRangeValueWithoutSpilit(baseMap, metricInfoBean, remoteConfig);
            //特殊逻辑  暂时过滤2024目标数据
//            Map<String,String> notInMap = new HashMap<>();
//            notInMap.put("year","2024");
//            bean.setNotInMap(notInMap);
        } else {
            Long queryId = y >= 2024 ? 62L : 32L;
            bean.setId(queryId);
            OverseaMetricHelper.setDestinationRangeValue(baseMap, metricInfoBean, remoteConfig);
        }
        OverseaMetricHelper.setDestinationValue(baseMap, subMetric, remoteConfig);
        bean.setAndMap(baseMap);
        return bean;
    }


    /**
     * 获取指标卡请求参数(站点维度)
     * @param type  current当前数据  lastyear去年数据  target目标数据
     * @param timeFilter
     * @param metricInfoBean
     * @param d
     * @param remoteConfig
     * @return
     */
    public static SqlParamterBean getSiteMetricCardSqlBean(String type,
                                                           TimeFilter timeFilter,
                                                           OverseaMetricInfoBean metricInfoBean,
                                                           String d,
                                                           RemoteConfig remoteConfig) throws Exception {
        SqlParamterBean bean = new SqlParamterBean();
        String year = timeFilter.getYear();
        int y = year != null ? Integer.parseInt(year) : 0;
        Map<String, String> baseMap = OverseaMetricHelper.generateQuarterMetricBaseMap("103", timeFilter, type, d, null);
        if ("target".equals(type)) {
            Long queryId = y >= 2024 ? 71L : 39L;
            bean.setId(queryId);
            OverseaMetricHelper.setSiteRangeValueWithoutSpilit(baseMap, metricInfoBean, remoteConfig, y);
            //特殊逻辑  暂时过滤2024目标数据
//            Map<String,String> notInMap = new HashMap<>();
//            notInMap.put("year","2024");
//            bean.setNotInMap(notInMap);
        } else {
            Long queryId = y >= 2024 ? 66L : 36L;
            bean.setId(queryId);
            OverseaMetricHelper.setSiteRangeValue(baseMap, metricInfoBean, remoteConfig);
        }
        bean.setAndMap(baseMap);
        return bean;
    }

    /**
     * 填充指标卡基础数据
     * @param currentRes
     * @param targetRes
     * @param dimMap
     */
    public static void processMetricCardBaseData(GetRawDataResponseType currentRes,
                                                 GetRawDataResponseType targetRes,
                                                 Map<String, Double> dimMap,
                                                 String year) {
        ChartHelper.fillOverallDimMap(MapperUtil.str2ListList(currentRes.getResult(), Object.class), currentRes.getMetricList(), dimMap);
        ChartHelper.fillOverallDimMap(MapperUtil.str2ListList(targetRes.getResult(), Object.class), targetRes.getMetricList(), dimMap);
        makeUpMetricCardData(dimMap, year);
    }


    /**
     * 额外计算达成差额率
     */
    public static void makeUpMetricCardData(Map<String, Double> dimMap, String year) {
        //24年之前计算达成差额率
        String extraDim = "ttd_qa_cost_rate_gap";
        // 24年计算达成率
        int y = year != null ? Integer.parseInt(year) : 0;
        if(y >= 2024){
            extraDim = "ttd_weighted_defect_achieved_rate";
        }
        Double extraDimValue = DimHelper.getSpecialDimValue(extraDim, "", dimMap, null);
        dimMap.put(extraDim, extraDimValue);
    }




    /**
     * 填充指标卡同比数据
     * @param popRes
     * @param dimMap
     */
    public static void processMetricCardPopData(GetRawDataResponseType popRes,
                                                Map<String, Double> dimMap,
                                                String year) {
        Map<String, Double> popMap = new HashMap<>();
        int y = year != null ? Integer.parseInt(year) : 0;
        ChartHelper.fillOverallDimMap(MapperUtil.str2ListList(popRes.getResult(), Object.class), popRes.getMetricList(), popMap);
        //计算同比
        String extraDim = "ttd_qa_cost_rate_lastyear";
        if(y >= 2024){
            extraDim = "ttd_weighted_defect_rate_lastyear";
        }
        dimMap.put(extraDim, DimHelper.getSpecialDimValue(extraDim, "", dimMap, popMap));
    }



    /**
     * 获取趋势线请求参数(目的地维度)
     * @param type  current当前数据  lastyear去年数据  target目标数据
     * @param metricInfoBean
     * @param d
     * @param remoteConfig
     * @param examineConfigBean
     * @return
     */
    public static SqlParamterBean getDestinationTrendlineSqlBean(GetOverseaTrendLineDataRequestType request,
                                                                 String type,
                                                                 OverseaMetricInfoBean metricInfoBean,
                                                                 String d,
                                                                 RemoteConfig remoteConfig,
                                                                 ExamineConfigBean examineConfigBean) throws Exception {
        SqlParamterBean bean = new SqlParamterBean();
        String year = request.getTimeFilter().getYear();
        int y = year != null ? Integer.parseInt(year) : 0;
        String queryType = request.getQueryType();
        String metric = request.getMetric();
        String subMetric = request.getSubMetric();
        Map<String, String> baseMap = OverseaMetricHelper.generateQuarterMetricBaseMap(metric, null, type, d, examineConfigBean);
        //非下钻场景
        if ("trendline".equals(queryType)) {
            if ("target".equals(type)) {
                Long queryId = y >= 2024 ? 70L : 38L;
                bean.setId(queryId);
                OverseaMetricHelper.setDestinationRangeValueWithoutSpilit(baseMap, metricInfoBean, remoteConfig);
                //特殊逻辑  暂时过滤2024目标数据
//                Map<String,String> notInMap = new HashMap<>();
//                notInMap.put("year","2024");
//                bean.setNotInMap(notInMap);
            } else {
                Long queryId = y >= 2024 ? 62L : 32L;
                bean.setId(queryId);
                OverseaMetricHelper.setDestinationRangeValue(baseMap, metricInfoBean, remoteConfig);
            }
            OverseaMetricHelper.setDestinationValue(baseMap, subMetric, remoteConfig);
        } else {
            //下钻场景 也不需要看目标数据
            DrillDownFilter drillDownFilter = request.getDrillDownFilter();
            String field = drillDownFilter.getField();
            SubMetricFiledBean configBean;
            if(y >= 2024){
                configBean = remoteConfig.getSubMetricFiledBean(year, metric, subMetric, field);
            }else{
                configBean = remoteConfig.getSubMetricFiledBean(metric, subMetric, field);
            }
            if (!configBean.getNeedLine()) {
                throw new InputArgumentException("this param is not support drilldown:metric:" + metric + ",subMetric:" + subMetric + ",field:" + field);
            }
            bean.setId(configBean.getBaseInfoId());
            List<String> lineGroupList = configBean.getLineGroupListMap().get("current");
            bean.setGroupList(OverseaMetricHelper.translateGroupList(lineGroupList));

            OverseaMetricHelper.setDestinationRangeValue(baseMap, metricInfoBean, remoteConfig);
            OverseaMetricHelper.setDestinationValue(baseMap, subMetric, remoteConfig);

            //设置前端传入的条件
            OverseaMetricHelper.setConditionValue(baseMap, configBean, drillDownFilter.getFieldValueList());

        }
        bean.setAndMap(baseMap);
        return bean;
    }



    /**
     * 获取趋势线请求参数(站点维度)
     * @param type  current当前数据  lastyear去年数据  target目标数据
     * @param metricInfoBean
     * @param d
     * @param remoteConfig
     * @param examineConfigBean
     * @return
     */
    public static SqlParamterBean getSiteTrendlineSqlBean(GetOverseaTrendLineDataRequestType request,
                                                          String type,
                                                          OverseaMetricInfoBean metricInfoBean,
                                                          String d,
                                                          RemoteConfig remoteConfig,
                                                          ExamineConfigBean examineConfigBean,
                                                          String realYear) throws Exception {
        SqlParamterBean bean = new SqlParamterBean();
        String year = request.getTimeFilter().getYear();
        int y = year != null ? Integer.parseInt(year) : 0;
        String queryType = request.getQueryType();
        String metric = request.getMetric();
        String subMetric = request.getSubMetric();
        Map<String, String> baseMap = OverseaMetricHelper.generateQuarterMetricBaseMap(metric, null, type, d, examineConfigBean);
        //非下钻场景
        if ("trendline".equals(queryType)) {
            if ("target".equals(type)) {
                Long queryId = y >= 2024 ? 71L : 39L;
                bean.setId(queryId);
                OverseaMetricHelper.setSiteRangeValueWithoutSpilit(baseMap, metricInfoBean, remoteConfig, Integer.parseInt(realYear));
                //特殊逻辑  暂时过滤2024目标数据
//                Map<String,String> notInMap = new HashMap<>();
//                notInMap.put("year","2024");
//                bean.setNotInMap(notInMap);
            } else {
                Long queryId = y >= 2024 ? 66L : 36L;
                bean.setId(queryId);
                OverseaMetricHelper.setSiteRangeValue(baseMap, metricInfoBean, remoteConfig);
            }
        } else {
            //下钻场景 也不需要看目标数据
            DrillDownFilter drillDownFilter = request.getDrillDownFilter();
            String field = drillDownFilter.getField();
            SubMetricFiledBean configBean;
            if(y >= 2024){
                configBean = remoteConfig.getSubMetricFiledBean(year, metric, subMetric, field);
            }else{
                configBean = remoteConfig.getSubMetricFiledBean(metric, subMetric, field);
            }

            if (!configBean.getNeedLine()) {
                throw new InputArgumentException("this param is not support drilldown:metric:" + metric + ",subMetric:" + subMetric + ",field:" + field);
            }
            bean.setId(configBean.getBaseInfoId());
            List<String> lineGroupList = configBean.getLineGroupListMap().get("current");
            bean.setGroupList(OverseaMetricHelper.translateGroupList(lineGroupList));

            OverseaMetricHelper.setSiteRangeValue(baseMap, metricInfoBean, remoteConfig);

            //设置前端传入的条件
            OverseaMetricHelper.setConditionValue(baseMap, configBean, drillDownFilter.getFieldValueList());
        }
        bean.setAndMap(baseMap);
        return bean;
    }


    /**
     * 填充趋势线基础数据
     * @param trendLineDetailInfoList
     * @param periodDataBean
     * @param timeList
     */
    public static void processTrendLineData(List<TrendLineDetailInfo> trendLineDetailInfoList,
                                            PeriodDataBean periodDataBean,
                                            List<String> timeList,
                                            String year) {
        int y = year != null ? Integer.parseInt(year) : 0;
        Map<String, Double> dimMap = new HashMap<>();
        Set<String> drillDownSet = new HashSet<>();
        List<List<Object>> reachList = periodDataBean.getReachList();
        List<List<Object>> targetList = periodDataBean.getTargetList();
        List<String> reachHeaderList = periodDataBean.getReachHeaderList();
        List<String> targetHeaderList = periodDataBean.getTargetHeaderList();
        List<String> reachDimList = reachHeaderList.subList(1, reachHeaderList.size());
        List<String> targetDimList = targetHeaderList.subList(1, targetHeaderList.size());

        ChartHelper.setMonthlyMetricTrendlineActualReach(dimMap, drillDownSet, reachList, Lists.newArrayList("time"), reachDimList);
        ChartHelper.setMonthlyMetricTrendlineActualReach(dimMap, drillDownSet, targetList, Lists.newArrayList("time"), targetDimList);

        Map<String, String> typeMap = new HashMap<>();
        if(y >= 2024){
            typeMap.put("ttd_weighted_defect_rate", "lineChart");
            typeMap.put("ttd_weighted_defect_target" , "lineChart");
        }else{
            typeMap.put("ttd_qa_cost_rate", "lineChart");
            typeMap.put("ttd_trgt_qa_cost_rate" , "lineChart");
        }


        ChartHelper.fillLineChartTrendLineData(null, timeList, dimMap,
                trendLineDetailInfoList, typeMap);

    }


    /**
     * 填充趋下钻势线数据
     * @param trendLineDetailInfoList
     * @param periodDataBean
     * @param timeList
     */
    public static void processDrilldownTrendLineData(List<TrendLineDetailInfo> trendLineDetailInfoList,
                                                     PeriodDataBean periodDataBean,
                                                     List<String> timeList,
                                                     String year) {
        int y = year != null ? Integer.parseInt(year) : 0;
        Map<String, Double> dimMap = new HashMap<>();
        Set<String> drillDownSet = new HashSet<>();
        List<List<Object>> reachList = periodDataBean.getReachList();
        List<String> reachHeaderList = periodDataBean.getReachHeaderList();
        List<String> reachGroupList = reachHeaderList.subList(0, 2);
        List<String> reachDimList = reachHeaderList.subList(2, reachHeaderList.size());

        ChartHelper.setMonthlyMetricTrendlineActualReach(dimMap, drillDownSet, reachList, reachGroupList, reachDimList);


        Map<String, String> typeMap = new HashMap<>();
        if(y >= 2024){
            typeMap.put("ttd_weighted_defect_rate", "lineChart");
        }else{
            typeMap.put("ttd_qa_cost_rate", "lineChart");
        }

        ChartHelper.fillLineChartTrendLineDataWithDrillDown(null, timeList, dimMap, trendLineDetailInfoList,
                typeMap, drillDownSet, false);


    }


    /**
     * 获取下钻待下钻维度列表
     * @param subMetric
     * @param metricInfoBean
     * @param remoteConfig
     * @return
     */
    public static List<String> getDrillDownFieldList(String subMetric,
                                                     OverseaMetricInfoBean metricInfoBean,
                                                     RemoteConfig remoteConfig) {
        String oversea = remoteConfig.getConfigValue("oversea");
        String region = remoteConfig.getConfigValue("region");
        String subRegion = remoteConfig.getConfigValue("subRegion");
        String destinationLevel = metricInfoBean.getDestinationLevel();
        if ("site".equals(subMetric)) {
            List<String> siteList = metricInfoBean.getSiteRangeList();
            //是海外或者多个站点  则可以按照站点、景点、供应商下钻
            if (oversea.equals(destinationLevel)) {
                return Lists.newArrayList("site", "viewspot", "vendor");
            }
            if (GeneralUtil.isNotEmpty(siteList)) {
                if (siteList.size() > 1) {
                    return Lists.newArrayList("site", "viewspot", "vendor");
                } else {
                    return Lists.newArrayList("viewspot", "vendor");
                }
            }
        }
        if (subMetric.startsWith("destination")) {
            //海外  大区、子区域、国家/地区、商拓、景点、供应商
            //多个大区  大区、子区域、国家/地区、商拓、景点、供应商
            //一个大区  子区域、国家/地区、商拓、景点、供应商
            //多个子区域 子区域、国家/地区、商拓、景点、供应商
            //一个子区域 国家/地区、商拓、景点、供应商
            if (oversea.equals(destinationLevel)) {
                return Lists.newArrayList("region", "province", "country", "examinee", "viewspot", "vendor");
            }
            List<String> destinationRangeList = metricInfoBean.getDestinationRangeList();
            if (GeneralUtil.isNotEmpty(destinationRangeList)) {
                if (region.equals(destinationLevel) && destinationRangeList.size() > 1) {
                    return Lists.newArrayList("region", "province", "country", "examinee", "viewspot", "vendor");
                }

                if (region.equals(destinationLevel) && destinationRangeList.size() == 1) {
                    return Lists.newArrayList("province", "country", "examinee", "viewspot", "vendor");
                }

                if (subRegion.equals(destinationLevel) && destinationRangeList.size() > 1) {
                    return Lists.newArrayList("province", "country", "examinee", "viewspot", "vendor");
                }

                if (subRegion.equals(destinationLevel) && destinationRangeList.size() == 1) {
                    return Lists.newArrayList("country", "examinee", "viewspot", "vendor");
                }
            }
        }
        throw new InputArgumentException("invalid subMetric:" + subMetric);
    }

    /**
     * 获取(目的地)下钻基础信息请求参数
     * @param field
     * @param request
     * @param d
     * @param metricInfoBean
     * @param remoteConfig
     * @return
     * @throws Exception
     */
    public static SqlParamterBean getDestinationDrillDownBaseInfoSqlBean(String field,
                                                                         GetOverseaDrillDownBaseInfoRequestType request,
                                                                         String d,
                                                                         OverseaMetricInfoBean metricInfoBean,
                                                                         RemoteConfig remoteConfig) throws Exception {
        SqlParamterBean bean = new SqlParamterBean();

        String metric = request.getMetric();
        String subMetric = request.getSubMetric();
        String year = request.getTimeFilter().getYear();
        int y = year != null ? Integer.parseInt(year) : 0;

        // 兼容2024年质量口径变更逻辑
        // SubMetricFiledBean configBean = remoteConfig.getSubMetricFiledBean(metric, subMetric, field);
        SubMetricFiledBean configBean;
        if(y >= 2024){
            configBean = remoteConfig.getSubMetricFiledBean(request.getTimeFilter().getYear(), metric, subMetric, field);
        }else{
            configBean = remoteConfig.getSubMetricFiledBean(metric, subMetric, field);
        }

        bean.setId(configBean.getBaseInfoId());

        Map<String, String> baseMap = OverseaMetricHelper.generateQuarterMetricBaseMap(metric, request.getTimeFilter(), "current", d, null);
        OverseaMetricHelper.setDestinationValue(baseMap, request.getSubMetric(), remoteConfig);
        OverseaMetricHelper.setDestinationRangeValue(baseMap, metricInfoBean, remoteConfig);
        bean.setAndMap(baseMap);

        List<String> groupList = configBean.getBaseInfoGroupList();
        List<String> translateGroupList = OverseaMetricHelper.translateGroupList(groupList);
        bean.setGroupList(translateGroupList);
        bean.setOrderList(Lists.newArrayList(translateGroupList.get(0)));
        bean.setOrderTypeList(Lists.newArrayList("asc"));

        if (request.isNeedSearch()) {
            //likeMap的key:value对格式为：group1|group2|group3:searchWord
            String searchWord = request.getSearchWord();
            Map<String, String> likeMap = new HashMap<>();
            List<Integer> likeIndexList = configBean.getBaseInfoLikeIndexList();
            StringBuilder likeKeySb = new StringBuilder();
            for (Integer index : likeIndexList) {
                likeKeySb.append(translateGroupList.get(index)).append("|");
            }
            likeKeySb.deleteCharAt(likeKeySb.length() - 1);
            likeMap.put(likeKeySb.toString(), searchWord);
            bean.setLikeMap(likeMap);
        } else {
            //非搜索模式下对于景点数据控制返回只有前50条
            if ("viewspot".equals(field) || "vendor".equals(field)) {
                bean.setPageNo(1);
                bean.setPageSize(50);
            }
        }

        return bean;
    }
    /**
     * 获取(目的地)下钻基础信息请求参数
     * @param field
     * @param request
     * @param d
     * @param metricInfoBean
     * @param remoteConfig
     * @return
     * @throws Exception
     */
    public static SqlParamterBean getDestinationDrillDownBaseInfoSqlBeanV2(String field,
                                                                         GetOverseaDrillDownBaseInfoV2RequestType request,
                                                                         String d,
                                                                         OverseaMetricInfoBean metricInfoBean,
                                                                         RemoteConfig remoteConfig) throws Exception {
        SqlParamterBean bean = new SqlParamterBean();

        String metric = request.getMetric();
        String subMetric = request.getSubMetric();
        String year = request.getTimeFilter().getYear();
        int y = year != null ? Integer.parseInt(year) : 0;

        // 兼容2024年质量口径变更逻辑
        // SubMetricFiledBean configBean = remoteConfig.getSubMetricFiledBean(metric, subMetric, field);
        SubMetricFiledBean configBean;
        if(y >= 2024){
            configBean = remoteConfig.getSubMetricFiledBeanV2(request.getTimeFilter().getYear(), metric, subMetric, field);
        }else{
            configBean = remoteConfig.getSubMetricFiledBeanV2(metric, subMetric, field);
        }

        bean.setId(configBean.getBaseInfoId());

        Map<String, String> baseMap = OverseaMetricHelper.generateQuarterMetricBaseMap(metric, request.getTimeFilter(), "current", d, null);
        OverseaMetricHelper.setDestinationValue(baseMap, request.getSubMetric(), remoteConfig);
        OverseaMetricHelper.setDestinationRangeValue(baseMap, metricInfoBean, remoteConfig);
        bean.setAndMap(baseMap);

        List<String> groupList = configBean.getBaseInfoGroupList();
        List<String> translateGroupList = OverseaMetricHelper.translateGroupList(groupList);
        bean.setGroupList(translateGroupList);
        bean.setOrderList(Lists.newArrayList(translateGroupList.get(0)));
        bean.setOrderTypeList(Lists.newArrayList("asc"));

        if (StringUtils.isNotEmpty(request.getSearchWord())) {
            //likeMap的key:value对格式为：group1|group2|group3:searchWord
            String searchWord = request.getSearchWord();
            Map<String, String> likeMap = new HashMap<>();
            List<Integer> likeIndexList = configBean.getBaseInfoLikeIndexList();
            StringBuilder likeKeySb = new StringBuilder();
            for (Integer index : likeIndexList) {
                likeKeySb.append(translateGroupList.get(index)).append("|");
            }
            likeKeySb.deleteCharAt(likeKeySb.length() - 1);
            likeMap.put(likeKeySb.toString(), searchWord);
            bean.setLikeMap(likeMap);
        } else {
            //非搜索模式下对于景点数据控制返回只有前50条
            if ("viewspot".equals(field) || "vendor".equals(field)) {
                bean.setPageNo(1);
                bean.setPageSize(50);
            }
        }

        return bean;
    }

    /**
     * 获取(站点)下钻基础信息请求参数
     * @param field
     * @param request
     * @param d
     * @param metricInfoBean
     * @param remoteConfig
     * @return
     * @throws Exception
     */
    public static SqlParamterBean getSiteDrillDownBaseInfoSqlBean(String field,
                                                                  GetOverseaDrillDownBaseInfoRequestType request,
                                                                  String d,
                                                                  OverseaMetricInfoBean metricInfoBean,
                                                                  RemoteConfig remoteConfig) throws Exception {
        SqlParamterBean bean = new SqlParamterBean();

        String metric = request.getMetric();
        String subMetric = request.getSubMetric();
        // 兼容2024年质量口径变更逻辑
        // SubMetricFiledBean configBean = remoteConfig.getSubMetricFiledBean(metric, subMetric, field);
        SubMetricFiledBean configBean;
        String year = request.getTimeFilter().getYear();
        int y = year != null ? Integer.parseInt(year) : 0;
        if(y >= 2024){
            configBean = remoteConfig.getSubMetricFiledBean(request.getTimeFilter().getYear(), metric, subMetric, field);
        }else{
            configBean = remoteConfig.getSubMetricFiledBean(metric, subMetric, field);
        }

        bean.setId(configBean.getBaseInfoId());

        Map<String, String> baseMap = OverseaMetricHelper.generateQuarterMetricBaseMap(metric, request.getTimeFilter(), "current", d, null);
        OverseaMetricHelper.setSiteRangeValue(baseMap, metricInfoBean, remoteConfig);
        bean.setAndMap(baseMap);

        List<String> groupList = configBean.getBaseInfoGroupList();
        List<String> translateGroupList = OverseaMetricHelper.translateGroupList(groupList);
        bean.setGroupList(translateGroupList);
        bean.setOrderList(Lists.newArrayList(translateGroupList.get(0)));
        bean.setOrderTypeList(Lists.newArrayList("asc"));

        if (request.isNeedSearch()) {
            //likeMap的key:value对格式为：group1|group2|group3:searchWord
            String searchWord = request.getSearchWord();
            Map<String, String> likeMap = new HashMap<>();
            List<Integer> likeIndexList = configBean.getBaseInfoLikeIndexList();
            StringBuilder likeKeySb = new StringBuilder();
            for (Integer index : likeIndexList) {
                likeKeySb.append(translateGroupList.get(index)).append("|");
            }
            likeKeySb.deleteCharAt(likeKeySb.length() - 1);
            likeMap.put(likeKeySb.toString(), searchWord);
            bean.setLikeMap(likeMap);
        } else {
            //非搜索模式下对于景点数据控制返回只有前50条
            if ("viewspot".equals(field) || "vendor".equals(field)) {
                bean.setPageNo(1);
                bean.setPageSize(50);
            }
        }

        return bean;
    }

    public static SqlParamterBean getSiteDrillDownBaseInfoSqlBeanV2(String field,
                                                                    GetOverseaDrillDownBaseInfoV2RequestType request,
                                                                  String d,
                                                                  OverseaMetricInfoBean metricInfoBean,
                                                                  RemoteConfig remoteConfig) throws Exception {
        SqlParamterBean bean = new SqlParamterBean();

        String metric = request.getMetric();
        String subMetric = request.getSubMetric();
        // 兼容2024年质量口径变更逻辑
        SubMetricFiledBean configBean;
        String year = request.getTimeFilter().getYear();
        int y = year != null ? Integer.parseInt(year) : 0;
        if(y >= 2024){
            configBean = remoteConfig.getSubMetricFiledBean(request.getTimeFilter().getYear(), metric, subMetric, field);
        }else{
            configBean = remoteConfig.getSubMetricFiledBean(metric, subMetric, field);
        }

        bean.setId(configBean.getBaseInfoId());

        Map<String, String> baseMap = OverseaMetricHelper.generateQuarterMetricBaseMap(metric, request.getTimeFilter(), "current", d, null);
        OverseaMetricHelper.setSiteRangeValue(baseMap, metricInfoBean, remoteConfig);
        bean.setAndMap(baseMap);

        List<String> groupList = configBean.getBaseInfoGroupList();
        List<String> translateGroupList = OverseaMetricHelper.translateGroupList(groupList);
        bean.setGroupList(translateGroupList);
        bean.setOrderList(Lists.newArrayList(translateGroupList.get(0)));
        bean.setOrderTypeList(Lists.newArrayList("asc"));

        if (StringUtils.isNotEmpty(request.getSearchWord())) {
            //likeMap的key:value对格式为：group1|group2|group3:searchWord
            String searchWord = request.getSearchWord();
            Map<String, String> likeMap = new HashMap<>();
            List<Integer> likeIndexList = configBean.getBaseInfoLikeIndexList();
            StringBuilder likeKeySb = new StringBuilder();
            for (Integer index : likeIndexList) {
                likeKeySb.append(translateGroupList.get(index)).append("|");
            }
            likeKeySb.deleteCharAt(likeKeySb.length() - 1);
            likeMap.put(likeKeySb.toString(), searchWord);
            bean.setLikeMap(likeMap);
        } else {
            //非搜索模式下对于景点数据控制返回只有前50条
            if ("viewspot".equals(field) || "vendor".equals(field)) {
                bean.setPageNo(1);
                bean.setPageSize(50);
            }
        }

        return bean;
    }


    /**
     * 填充下钻基础数据
     *
     * @param request
     * @param field
     * @param response
     * @param fieldDataItem
     * @param remoteConfig
     */
    public static void processDrillDownBaseInfo(GetOverseaDrillDownBaseInfoRequestType request,
                                                String field,
                                                GetRawDataResponseType response,
                                                FieldDataItem fieldDataItem,
                                                RemoteConfig remoteConfig) {
        Bus101102Helper.processDrillDownBaseInfo(request, field, response, fieldDataItem, remoteConfig);
    }


    /**
     * 获取(目的地)表格数据请求参数
     *
     * @param request
     * @param d
     * @param metricInfoBean
     * @param configBean
     * @param remoteConfig
     * @param type
     * @return
     * @throws Exception
     */

    public static SqlParamterBean getDestinationTableDataSqlBean(GetOverseaTableDataRequestType request,
                                                                 String d,
                                                                 OverseaMetricInfoBean metricInfoBean,
                                                                 SubMetricFiledBean configBean,
                                                                 RemoteConfig remoteConfig,
                                                                 String type) throws Exception {
        SqlParamterBean bean = new SqlParamterBean();

        String metric = request.getMetric();

        setDrillDownIdAndGroupListForSqlBean(bean, configBean, type);

        TimeFilter timeFilter = request.getTimeFilter();
        String subMetric = request.getSubMetric();
        String year = timeFilter.getYear();
        int y = year != null ? Integer.parseInt(year) : 0;


        Map<String, String> baseMap = OverseaMetricHelper.generateQuarterMetricBaseMap(metric, timeFilter, type, d, null);
        OverseaMetricHelper.setDestinationValue(baseMap, subMetric, remoteConfig);
        OverseaMetricHelper.setDestinationRangeValue(baseMap, metricInfoBean, remoteConfig);

        //如果当前查看了目标数据  且是按大区下钻  则限制一下子区域id为-9999 6001或者5001或者7001  即过滤掉子区域额数据
        // 但是要加上一个大区只有一个子区域的情况
        if ("target".equals(type) && "region".equals(configBean.getField())) {
            baseMap.put("province_id", remoteConfig.getConfigValue("provinceId"));
        }
//        if("target".equals(type)){
//            //特殊逻辑  暂时过滤2024目标数据
//            Map<String,String> notInMap = new HashMap<>();
//            notInMap.put("year","2024");
//            bean.setNotInMap(notInMap);
//        }


        DrillDownFilter drillDownFilter = request.getDrillDownFilter();
        //设置前端传入的条件
        OverseaMetricHelper.setConditionValue(baseMap, configBean, drillDownFilter.getFieldValueList());
        //获取当前数据时 需要设置前端传入的分页  其他数据不需要
        //同时需要设置排序条件
        if ("current".equals(type)) {
            bean.setPageNo(request.getPageNo());
            bean.setPageSize(request.getPageSize());
            String orderColumn = y >= 2024 ? "weighted_defect_cnt" : "quality_cost";
            bean.setOrderList(Lists.newArrayList(orderColumn, configBean.getConditionColumn()));
            bean.setOrderTypeList(Lists.newArrayList("desc", "desc"));
        }
        bean.setAndMap(baseMap);

        return bean;
    }

    public static SqlParamterBean getDestinationTableDataSqlBeanV2(GetOverseaTableDataV2RequestType request,
                                                                 String d,
                                                                 OverseaMetricInfoBean metricInfoBean,
                                                                 SubMetricFiledBean configBean,
                                                                 RemoteConfig remoteConfig,
                                                                 String type) throws Exception {
        SqlParamterBean bean = new SqlParamterBean();

        String metric = request.getMetric();

        setDrillDownIdAndGroupListForSqlBean(bean, configBean, type);

        TimeFilter timeFilter = request.getTimeFilter();
        String subMetric = request.getSubMetric();
        String year = timeFilter.getYear();
        int y = year != null ? Integer.parseInt(year) : 0;


        Map<String, String> baseMap = OverseaMetricHelper.generateQuarterMetricBaseMap(metric, timeFilter, type, d, null);
        OverseaMetricHelper.setDestinationValue(baseMap, subMetric, remoteConfig);
        OverseaMetricHelper.setDestinationRangeValue(baseMap, metricInfoBean, remoteConfig);

        //如果当前查看了目标数据  且是按大区下钻  则限制一下子区域id为-9999 6001或者5001或者7001  即过滤掉子区域额数据
        // 但是要加上一个大区只有一个子区域的情况
        if ("target".equals(type) && "region".equals(configBean.getField())) {
            baseMap.put("province_id", remoteConfig.getConfigValue("provinceId"));
        }

        //设置前端传入的条件
        OverseaMetricHelper.setConditionValue(baseMap, configBean, request.getDimValueList());
        //获取当前数据时 需要设置前端传入的分页  其他数据不需要
        //同时需要设置排序条件
        if ("current".equals(type)) {
            bean.setPageNo(request.getPageNo());
            bean.setPageSize(request.getPageSize());
            String orderColumn = y >= 2024 ? "weighted_defect_cnt" : "quality_cost";
            bean.setOrderList(Lists.newArrayList(orderColumn, configBean.getConditionColumn()));
            bean.setOrderTypeList(Lists.newArrayList("desc", "desc"));
        }
        bean.setAndMap(baseMap);
        Map<String, String> notBaseMap = new HashMap<>();
        for (String key : bean.getGroupList()) {
            if("region_name".equalsIgnoreCase(key)){
                notBaseMap.put(key, "unkwn");
            }
            if("examinee_display".equalsIgnoreCase(key)){
                notBaseMap.put(key, "unkwn");
            }
            if("viewspot_name".equalsIgnoreCase(key)){
                notBaseMap.put(key, "unkwn");
            }
            if("examinee".equalsIgnoreCase(key)){
                notBaseMap.put(key, "unkwn");
            }
        }
        bean.setNotInMap(notBaseMap);
        return bean;
    }

    /**
     * 获取(站点)表格数据请求参数
     *
     * @param request
     * @param d
     * @param metricInfoBean
     * @param configBean
     * @param remoteConfig
     * @param type
     * @return
     * @throws Exception
     */

    public static SqlParamterBean getSiteTableDataSqlBean(GetOverseaTableDataRequestType request,
                                                          String d,
                                                          OverseaMetricInfoBean metricInfoBean,
                                                          SubMetricFiledBean configBean,
                                                          RemoteConfig remoteConfig,
                                                          String type) throws Exception {
        SqlParamterBean bean = new SqlParamterBean();

        String metric = request.getMetric();

        setDrillDownIdAndGroupListForSqlBean(bean, configBean, type);

        TimeFilter timeFilter = request.getTimeFilter();
        String year = timeFilter.getYear();
        int y = year != null ? Integer.parseInt(year) : 0;


        Map<String, String> baseMap = OverseaMetricHelper.generateQuarterMetricBaseMap(metric, timeFilter, type, d, null);
        OverseaMetricHelper.setSiteRangeValue(baseMap, metricInfoBean, remoteConfig);

        DrillDownFilter drillDownFilter = request.getDrillDownFilter();
        //设置前端传入的条件
        OverseaMetricHelper.setConditionValue(baseMap, configBean, drillDownFilter.getFieldValueList());
        //获取当前数据时 需要设置前端传入的分页  其他数据不需要
        //同时需要设置排序条件
        if ("current".equals(type)) {
            bean.setPageNo(request.getPageNo());
            bean.setPageSize(request.getPageSize());
            String orderColumn = y >= 2024 ? "weighted_defect_cnt" : "quality_cost";
            bean.setOrderList(Lists.newArrayList(orderColumn, configBean.getConditionColumn()));
            bean.setOrderTypeList(Lists.newArrayList("desc", "desc"));
        }
        bean.setAndMap(baseMap);

//        if("target".equals(type)){
//            //特殊逻辑  暂时过滤2024目标数据
//            Map<String,String> notInMap = new HashMap<>();
//            notInMap.put("year","2024");
//            bean.setNotInMap(notInMap);
//        }

        return bean;
    }
    public static SqlParamterBean getSiteTableDataSqlBeanV2(GetOverseaTableDataV2RequestType request,
                                                          String d,
                                                          OverseaMetricInfoBean metricInfoBean,
                                                          SubMetricFiledBean configBean,
                                                          RemoteConfig remoteConfig,
                                                          String type) throws Exception {
        SqlParamterBean bean = new SqlParamterBean();

        String metric = request.getMetric();

        setDrillDownIdAndGroupListForSqlBean(bean, configBean, type);

        TimeFilter timeFilter = request.getTimeFilter();
        String year = timeFilter.getYear();
        int y = year != null ? Integer.parseInt(year) : 0;


        Map<String, String> baseMap = OverseaMetricHelper.generateQuarterMetricBaseMap(metric, timeFilter, type, d, null);
        OverseaMetricHelper.setSiteRangeValue(baseMap, metricInfoBean, remoteConfig);

        //设置前端传入的条件
        OverseaMetricHelper.setConditionValue(baseMap, configBean, request.getDimValueList());
        //获取当前数据时 需要设置前端传入的分页  其他数据不需要
        //同时需要设置排序条件
        if ("current".equals(type)) {
            bean.setPageNo(request.getPageNo());
            bean.setPageSize(request.getPageSize());
            String orderColumn = y >= 2024 ? "weighted_defect_cnt" : "quality_cost";
            bean.setOrderList(Lists.newArrayList(orderColumn, configBean.getConditionColumn()));
            bean.setOrderTypeList(Lists.newArrayList("desc", "desc"));
        }
        bean.setAndMap(baseMap);

        Map<String, String> notBaseMap = new HashMap<>();
        for (String key : bean.getGroupList()) {
            if("site".equalsIgnoreCase(key)){
                notBaseMap.put(key, "unkwn");
            }
            if("site_code".equalsIgnoreCase(key)){
                notBaseMap.put(key, "unkwn");
            }
            if("viewspot_name".equalsIgnoreCase(key)){
                notBaseMap.put(key, "unkwn");
            }
        }
        bean.setNotInMap(notBaseMap);

        return bean;
    }


    /**
     * 下钻场景设置sqlbean的一些公共信息
     * id和groupList
     * @param bean
     * @param configBean
     * @param type
     */
    private static void setDrillDownIdAndGroupListForSqlBean(SqlParamterBean bean,
                                                             SubMetricFiledBean configBean,
                                                             String type){

        Long otherId = configBean.getTableDataIdMap().get("other");
        Long id = configBean.getTableDataIdMap().get(type);
        bean.setId(GeneralUtil.isEmpty(id) ? otherId : id);

        List<String> otherGroupList = configBean.getTableGroupListMap().get("other");
        List<String> groupList = configBean.getTableGroupListMap().get(type);
        bean.setGroupList(GeneralUtil.isEmpty(groupList) ? OverseaMetricHelper.translateGroupList(otherGroupList)
                : OverseaMetricHelper.translateGroupList(groupList));
    }


    /**
     * 填充表格基础数据
     * @param currentRes
     * @param targetRes
     * @param tableDataItemList
     * @param needTarget
     */
    public static void processTableBaseData(GetRawDataResponseType currentRes,
                                            GetRawDataResponseType targetRes,
                                            List<TableDataItem> tableDataItemList,
                                            Boolean needTarget,
                                            String year) {
        List<List<Object>> currentList = MapperUtil.str2ListList(currentRes.getResult(), Object.class);
        //还原翻译字段
        List<String> currentGroupList = OverseaMetricHelper.revertGroupList(currentRes.getGroupList());
        List<String> currentMetricList = currentRes.getMetricList();

        if (needTarget) {
            List<List<Object>> targetList = MapperUtil.str2ListList(targetRes.getResult(), Object.class);
            List<String> targetGroupList = OverseaMetricHelper.revertGroupList(targetRes.getGroupList());
            List<String> targetMetricList = targetRes.getMetricList();
            ChartHelper.fillCommmonTableDataV2(tableDataItemList,
                    currentGroupList,
                    targetGroupList,
                    currentMetricList,
                    targetMetricList,
                    currentList,
                    targetList);
            for(TableDataItem item : tableDataItemList){
                makeUpMetricCardData(item.getDimMap(), year);
            }
        } else {
            ChartHelper.fillCommmonTableDataV2(tableDataItemList,
                    currentGroupList,
                    new ArrayList<>(),
                    currentMetricList,
                    new ArrayList<>(),
                    currentList,
                    new ArrayList<>());
        }

    }

    public static void processTableBaseDataV2(GetRawDataResponseType currentRes,
                                              GetRawDataResponseType targetRes,
                                              List<OverseaTableDataRow> tableDataItemList,
                                              Boolean needTarget,
                                              String year) {
        List<TableDataItem> tableDataItemOldList = new ArrayList<>();
        processTableBaseData(currentRes, targetRes, tableDataItemOldList, needTarget, year);
        for (TableDataItem item : tableDataItemOldList) {
            OverseaTableDataRow row = new OverseaTableDataRow();
            tableDataItemList.add(row);
            Map<String, String> fieldMap = item.getFieldMap();
            Map<String, Double> dimMap = item.getDimMap();
            //表头部分
            row.setRegion(fieldMap.get("region_name"));
            row.setSubRegion(fieldMap.get("province_name"));
            row.setCountry(fieldMap.get("country_name"));
            row.setViewspot(fieldMap.get("viewspot_name"));
            row.setViewspotId(NumberUtils.toInt(fieldMap.get("viewspot_id"), 0));
            row.setExaminee(fieldMap.get("examinee_display"));
            row.setSite(fieldMap.get("site"));
            row.setChannel(fieldMap.get("channel"));
            row.setLocale(fieldMap.get("locale"));
            row.setVendorId(NumberUtils.toLong(fieldMap.get("vendor_id")));
            row.setVendorName(fieldMap.get("vendor_name"));

            //数据展示部分
            //加权缺陷数ttd_weighted_defect_cnt
            row.setWeightedDefectCnt(dimMap.get("ttd_weighted_defect_cnt"));
            //支付订单量ttd_pay_odr_cnt
            row.setPayOrdCnt(dimMap.get("ttd_pay_odr_cnt"));
            //加权缺陷率ttd_weighted_defect_rate
            row.setWeightedDefectRate(dimMap.get("ttd_weighted_defect_rate"));
            //当季目标 target
            row.setTargetValue(dimMap.get("ttd_weighted_defect_target"));
            // 达成率 完成率 completeRate
            Double rate = dimMap.get("ttd_weighted_defect_rate");
            Double target = dimMap.get("ttd_weighted_defect_target");
            if (GeneralUtil.isValidDivide(rate, target)) {
                Double result = 1 - (dimMap.get("ttd_weighted_defect_rate") - dimMap.get("ttd_weighted_defect_target")) / dimMap.get("ttd_weighted_defect_target");
                row.setCompleteRate(result);
            }
        }
    }

    /**
     * 生成下钻表格表头
     * @param configBean
     * @return
     */
    public static List<String> getTableHeaderList(SubMetricFiledBean configBean, String year) {
        List<String> tableHeaderList = new ArrayList<>();
        tableHeaderList.addAll(configBean.getHeaderFieldList());
        int y = year != null ? Integer.parseInt(year) : 0;
        if(y >= 2024){
            tableHeaderList.add("weightedDefectRate");
            tableHeaderList.add("payOrdCnt");
            tableHeaderList.add("weightedDefectCnt");
            tableHeaderList.add("targetValue");
            tableHeaderList.add("completeRate");
        } else {
            tableHeaderList.add("ttd_qa_cost");
            tableHeaderList.add("ttd_orders");
            tableHeaderList.add("ttd_qa_cost_rate");
            tableHeaderList.add("ttd_trgt_qa_cost_rate");
            tableHeaderList.add("ttd_qa_cost_rate_gap");
        }
        return tableHeaderList;
    }

    /**
     * 兼容质量
     * @param subMetric
     * @return
     */
    public static String transformSubMetricName(String subMetric) {
        if (subMetric.equals("destinationC")) {
            return "destination_c";
        } else if (subMetric.equals("destinationT")) {
            return "destination_t";
        }
        return subMetric;
    }

    /**
     * 兼容质量
     * @param subMetric
     * @return
     */
    public static String transformSubMetricNameReverse(String subMetric) {
        if (subMetric.equals("destination_c")) {
            return "destinationC";
        } else if (subMetric.equals("destination_t")) {
            return "destinationT";
        }
        return subMetric;
    }

}
