package com.ctrip.tour.business.dashboard.tktBusiness.strategy.impl

import com.ctrip.soa._24922.GetDrillDownBaseInfoRequestType
import com.ctrip.soa._24922.GetTableDataRequestType
import com.ctrip.soa._24922.GetTrendLineDataRequestType
import com.ctrip.soa._24922.TimeFilter
import com.ctrip.soa._27181.BIBaseReportQueryServiceClient
import com.ctrip.soa._27181.GetRawDataResponseType
import com.ctrip.tour.business.dashboard.tktBusiness.bean.DrillDownFieldBean
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean
import com.ctrip.tour.business.dashboard.tktBusiness.bean.SinglePeriodDataBean
import com.ctrip.tour.business.dashboard.tktBusiness.biz.SinglePeriodTrendLineBiz
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardExamineeConfigV2Dao
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardExamineeConfigV2
import com.ctrip.tour.business.dashboard.utils.MapperUtil
import com.google.common.util.concurrent.Futures
import org.springframework.scheduling.annotation.AsyncResult

import spock.lang.Specification

class Bus12MetricStrategyTest extends Specification{

    def baseReportQueryServiceClient = Mock(BIBaseReportQueryServiceClient)

    def examineeConfigV2Dao = Mock(BusinessDashboardExamineeConfigV2Dao)

    def singlePeriodTrendLineBiz = Mock(SinglePeriodTrendLineBiz)

    def remoteConfig = Mock(RemoteConfig)

    def bus12MetricStrategy = new Bus12MetricStrategy(examineeConfigV2Dao: examineeConfigV2Dao, singlePeriodTrendLineBiz: singlePeriodTrendLineBiz,
            remoteConfig: remoteConfig, baseReportQueryServiceClient: baseReportQueryServiceClient)

//    def "testGetSingleMetricCardData"(){
//        given: "设置请求参数"
//        def timeFilter = new TimeFilter(dateType: "month", month: "06", year: "2024")
//        def metricInfoBean = new MetricInfoBean(metric: 12, odtLevel: odtLevel, odtRegionList: odtRegionList)
//
//        def response1 = "{\"result\":\"[[\\\"2326\\\",\\\"1.1418070689736481\\\"]]\",\"metricList\":[\"w_line_num\",\"w_line_rate\"]}"
//        baseReportQueryServiceClient.getRawData({i -> i.queryId == 69 }) >> MapperUtil.str2Obj(response1, GetRawDataResponseType.class)
//
//        def response2 = "{\"result\":\"[[\\\"57\\\"]]\",\"metricList\":[\"total_time\"]}"
//        baseReportQueryServiceClient.getRawData({i -> i.queryId == 60 }) >> MapperUtil.str2Obj(response2, GetRawDataResponseType.class)
//
//        def response3 = "{\"result\":\"[[\\\"12/56\\\"]]\",\"metricList\":[\"ranking\"]}"
//        baseReportQueryServiceClient.getRawData({i -> i.queryId == 73 }) >> MapperUtil.str2Obj(response3, GetRawDataResponseType.class)
//
//        remoteConfig.getConfigValue("domestic") >> "国内"
//        remoteConfig.getConfigValue("three") >> "三方"
//        remoteConfig.getConfigValue("region") >> "大区"
//        remoteConfig.getConfigValue("province") >> "省份"
//        remoteConfig.getConfigValue("viewspot") >> "景点"
//
//
//        when: "调用方法"
//        def result = bus12MetricStrategy.getSingleMetricCardData("zheli",timeFilter,metricInfoBean,"2024-06-26",true).get()
//
//        then: "验证返回结果"
//        with(result){
//            rank == "12/56"
//            dimData.size() == 4
//            defaultField == actualDefaultField
//        }
//
//
//        where: "用表格方式验证多种返回结果"
//        odtLevel | odtRegionList || actualDefaultField
//        "国内"   | null          || "大区"
//        "大区"   | ["华东大区"]  || "省份"
//    }



    def "testGetSingleTrendlineDataWithoutDrillDown"(){
        given: "设置请求参数"
        def daoStr = "[{\"id\":36892038,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2024\",\"quarter\":\"Q1\",\"examineMetric\":\"12\",\"examineType\":1,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2024-07-16\",\"odtExamineLevel\":\"国内\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"},{\"id\":36896280,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2024\",\"quarter\":\"Q2\",\"examineMetric\":\"12\",\"examineType\":1,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2024-07-16\",\"odtExamineLevel\":\"国内\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"},{\"id\":36982940,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2024\",\"quarter\":\"Q3\",\"examineMetric\":\"12\",\"examineType\":1,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2024-07-16\",\"odtExamineLevel\":\"国内\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"}]"
        examineeConfigV2Dao.queryMetricAllConfig(_, _, _) >> MapperUtil.str2List(daoStr, BusinessDashboardExamineeConfigV2.class)

        def responseStr1 = "{\"periodWeaknessList\":[[\"2024-04\",56.7,0.02718988543898892]],\"weaknessHeaderList\":[\"time\",\"w_line_num\",\"w_line_rate\"]}"
        def responseStr2 = "{\"periodWeaknessList\":[[\"2024-05\",56.7,0.02718988543898892]],\"weaknessHeaderList\":[\"time\",\"w_line_num\",\"w_line_rate\"]}"
        def responseStr3 = "{\"periodWeaknessList\":[[\"2024-06\",56.7,0.02718988543898892]],\"weaknessHeaderList\":[\"time\",\"w_line_num\",\"w_line_rate\"]}"

        singlePeriodTrendLineBiz.getBus12SinglePeriodTrendLineData(_, _, _, _, _) >>> [new AsyncResult<>(MapperUtil.str2Obj(responseStr1, SinglePeriodDataBean.class)), new AsyncResult<>(MapperUtil.str2Obj(responseStr2, SinglePeriodDataBean.class)), new AsyncResult<>(MapperUtil.str2Obj(responseStr3, SinglePeriodDataBean.class))]

        remoteConfig.getConfigValue("domestic") >> "国内"
        remoteConfig.getConfigValue("three") >> "三方"
        remoteConfig.getConfigValue("region") >> "大区"
        remoteConfig.getConfigValue("province") >> "省份"
        remoteConfig.getConfigValue("viewspot") >> "景点"


        def requestStr = "{\"domainName\":\"zheli\",\"timeFilter\":{\"year\":\"2024\",\"month\":\"06\",\"dateType\":\"month\",\"timeFrame\":\"3\"},\"metric\":\"12\",\"queryType\":\"trendline\"}"
        GetTrendLineDataRequestType request = MapperUtil.str2Obj(requestStr, GetTrendLineDataRequestType.class)

        when: "调用方法"
        def result = bus12MetricStrategy.getSingleTrendlineData(request,null,"2024-06-26")

        then: "验证返回结果"
        with(result){
            trendLineDetailInfoList.size() == 2
        }


    }


    def "testGetSingleTrendlineDataWithDrillDown"(){
        given: "设置请求参数"
        def daoStr = "[{\"id\":36892038,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2024\",\"quarter\":\"Q1\",\"examineMetric\":\"12\",\"examineType\":1,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2024-07-16\",\"odtExamineLevel\":\"国内\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"},{\"id\":36896280,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2024\",\"quarter\":\"Q2\",\"examineMetric\":\"12\",\"examineType\":1,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2024-07-16\",\"odtExamineLevel\":\"国内\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"},{\"id\":36982940,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2024\",\"quarter\":\"Q3\",\"examineMetric\":\"12\",\"examineType\":1,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2024-07-16\",\"odtExamineLevel\":\"国内\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"}]"
        examineeConfigV2Dao.queryMetricAllConfig(_, _, _) >> MapperUtil.str2List(daoStr, BusinessDashboardExamineeConfigV2.class)


        def responseStr1 = "{\"periodWeaknessList\":[[\"2024-04\",\"华西大区\",9.6,0.019909146862795976],[\"2024-04\",\"华南大区\",9.8,0.017761346288536185],[\"2024-04\",\"华北大区\",18.25,0.04542313821938328],[\"2024-04\",\"华东大区\",18.4,0.03630875040662762],[\"2024-04\",\"云南大区\",0.65,0.004669029647380391]],\"weaknessHeaderList\":[\"time\",\"region_name\",\"w_line_num\",\"w_line_rate\"]}"
        def responseStr2 = "{\"periodWeaknessList\":[[\"2024-05\",\"华西大区\",9.6,0.019909146862795976],[\"2024-05\",\"华南大区\",9.8,0.017761346288536185],[\"2024-05\",\"华北大区\",18.25,0.04542313821938328],[\"2024-05\",\"华东大区\",18.4,0.03630875040662762],[\"2024-05\",\"云南大区\",0.65,0.004669029647380391]],\"weaknessHeaderList\":[\"time\",\"region_name\",\"w_line_num\",\"w_line_rate\"]}"
        def responseStr3 = "{\"periodWeaknessList\":[[\"2024-06\",\"华西大区\",9.6,0.019909146862795976],[\"2024-06\",\"华南大区\",9.8,0.017761346288536185],[\"2024-06\",\"华北大区\",18.25,0.04542313821938328],[\"2024-06\",\"华东大区\",18.4,0.03630875040662762],[\"2024-06\",\"云南大区\",0.65,0.004669029647380391]],\"weaknessHeaderList\":[\"time\",\"region_name\",\"w_line_num\",\"w_line_rate\"]}"

        singlePeriodTrendLineBiz.getBus12SinglePeriodTrendLineData(_, _, _, _, _) >>> [new AsyncResult<>(MapperUtil.str2Obj(responseStr1, SinglePeriodDataBean.class)), new AsyncResult<>(MapperUtil.str2Obj(responseStr2, SinglePeriodDataBean.class)), new AsyncResult<>(MapperUtil.str2Obj(responseStr3, SinglePeriodDataBean.class))]

        remoteConfig.getConfigValue("domestic") >> "国内"
        remoteConfig.getConfigValue("three") >> "三方"
        remoteConfig.getConfigValue("region") >> "大区"
        remoteConfig.getConfigValue("province") >> "省份"
        remoteConfig.getConfigValue("viewspot") >> "景点"


        def requestStr = "{\"domainName\":\"zheli\",\"timeFilter\":{\"year\":\"2024\",\"month\":\"06\",\"dateType\":\"month\",\"timeFrame\":\"3\"},\"metric\":\"12\",\"queryType\":\"drilldown\",\"drillDownFilter\":{\"field\":\"大区\",\"fieldValueList\":[]}}"
        GetTrendLineDataRequestType request = MapperUtil.str2Obj(requestStr, GetTrendLineDataRequestType.class)

        when: "调用方法"
        def result = bus12MetricStrategy.getSingleTrendlineData(request,null,"2024-06-26")

        then: "验证返回结果"
        with(result){
            trendLineDetailInfoList.size() == 5
        }

    }


//    def "testGetSingleDrillDownBaseInfo"(){
//        given: "设置请求参数"
//
//        def responseStr = "{\"result\":\"[[\\\"云南大区\\\"]]\",\"groupList\":[\"region_name\"],\"metricList\":[]}"
//        baseReportQueryServiceClient.getRawDataAsync(_) >> Futures.immediateFuture(MapperUtil.str2Obj(responseStr, GetRawDataResponseType.class))
//
//
//        remoteConfig.getConfigValue("domestic") >> "国内"
//        remoteConfig.getConfigValue("three") >> "三方"
//        remoteConfig.getConfigValue("region") >> "大区"
//        remoteConfig.getConfigValue("province") >> "省份"
//        remoteConfig.getConfigValue("viewspot") >> "景点"
//
//
//        String config = "{\"subMetric\":\"domesticDayTour\",\"field\":\"region_name\",\"baseInfoId\":68,\"baseInfoGroupList\":[\"region_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"firstpage\":[\"region_name\"],\"detailpage\":[\"region_name\"]},\"examineTypeList\":[1,3]}"
//        remoteConfig.getDrillDownFieldBean(_, _, _) >> MapperUtil.str2Obj(config, DrillDownFieldBean.class)
//
//
//
//        def requestStr = "{\"domainName\":\"zheli\",\"timeFilter\":{\"year\":\"2024\",\"month\":\"06\",\"dateType\":\"month\"},\"metric\":\"12\",\"needSearch\":true,\"searchWord\":\"云南\",\"searchField\":\"大区\"}"
//        GetDrillDownBaseInfoRequestType request = MapperUtil.str2Obj(requestStr, GetDrillDownBaseInfoRequestType.class)
//        MetricInfoBean metricInfoBean = new MetricInfoBean(metric: 12, odtLevel: "国内")
//
//        when: "调用方法"
//        def result = bus12MetricStrategy.getSingleDrillDownBaseInfo(request,metricInfoBean,"2024-06-26")
//
//        then: "验证返回结果"
//        with(result){
//            fieldDataItemList.size() == 1
//        }
//
//
//    }

//    def "testGetSingleTableData"(){
//        given: "设置请求参数"
//        def responseStr = "{\"result\":\"[[\\\"华西大区\\\",\\\"700\\\",\\\"1.5087570209154084\\\"],[\\\"华南大区\\\",\\\"746\\\",\\\"1.3809403033344538\\\"],[\\\"华北大区\\\",\\\"1288\\\",\\\"3.139270023068843\\\"],[\\\"华东大区\\\",\\\"1892\\\",\\\"3.835916665821895\\\"],[\\\"云南大区\\\",\\\"26\\\",\\\"0.18676118589521565\\\"]]\",\"groupList\":[\"region_name\"],\"metricList\":[\"w_line_num\",\"w_line_rate\"],\"totalNum\":5}"
//        baseReportQueryServiceClient.getRawData({i -> i.queryId == 69 }) >> MapperUtil.str2Obj(responseStr, GetRawDataResponseType.class)
//
//
//        String config = "{\"field\":\"region_name\",\"baseInfoId\":69,\"baseInfoGroupList\":[\"region_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"firstpage\":[\"region_name\"],\"detailpage\":[\"region_name\"]}}"
//        remoteConfig.getDrillDownFieldBean(_, _, _) >> MapperUtil.str2Obj(config, DrillDownFieldBean.class)
//
//
//        def response2 = "{\"result\":\"[[\\\"57\\\"]]\",\"metricList\":[\"total_time\"]}"
//        baseReportQueryServiceClient.getRawData({i -> i.queryId == 60 }) >> MapperUtil.str2Obj(response2, GetRawDataResponseType.class)
//
//
//        remoteConfig.getConfigValue("domestic") >> "国内"
//        remoteConfig.getConfigValue("three") >> "三方"
//        remoteConfig.getConfigValue("region") >> "大区"
//        remoteConfig.getConfigValue("province") >> "省份"
//        remoteConfig.getConfigValue("viewspot") >> "景点"
//
//
//        def requestStr = "{\"domainName\":\"zheli\",\"timeFilter\":{\"year\":\"2024\",\"month\":\"06\",\"dateType\":\"month\"},\"metric\":\"12\",\"drillDownFilter\":{\"field\":\"大区\",\"fieldValueList\":[]},\"source\":\"detailpage\",\"pageNo\":1,\"pageSize\":20}"
//        GetTableDataRequestType request = MapperUtil.str2Obj(requestStr, GetTableDataRequestType.class)
//        MetricInfoBean metricInfoBean = new MetricInfoBean(metric: 12, odtLevel: "国内")
//
//
//        when: "调用方法"
//        def result = bus12MetricStrategy.getSingleTableData(request,metricInfoBean,"2024-06-26")
//
//        then: "验证返回结果"
//        with(result){
//            tableDataItemList.size() == 5
//        }
//
//
//
//
//
//
//
//    }


}

