package com.ctrip.tour.business.dashboard.grpBusiness.bo;

import java.util.List;
import java.util.Map;

public class TrippalDecorateInfoDTO {
    private Long idx;
    private Integer type;
    private String tag;
    private String text;
    private List<TrippalDecorateInfoDTO> children;
    private Map<String, String> attrs;

    public Long getIdx() {
        return idx;
    }

    public void setIdx(Long idx) {
        this.idx = idx;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public List<TrippalDecorateInfoDTO> getChildren() {
        return children;
    }

    public void setChildren(List<TrippalDecorateInfoDTO> children) {
        this.children = children;
    }

    public Map<String, String> getAttrs() {
        return attrs;
    }

    public void setAttrs(Map<String, String> attrs) {
        this.attrs = attrs;
    }
}
