package com.ctrip.tour.business.dashboard.tktBusiness.dao;

import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.sqlbuilder.*;

import java.sql.SQLException;
import java.sql.Types;
import java.util.List;


import com.ctrip.platform.dal.dao.helper.DalDefaultJpaParser;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardEmployeeInfo;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2022-07-20
 */

@Repository
public class BusinessDashboardEmployeeInfoDao {
    private static final boolean ASC = true;
    private DalTableDao<BusinessDashboardEmployeeInfo> client;
    private DalQueryDao queryDao;
    private static final String DATA_BASE = "TtdReportDB_W";

    public BusinessDashboardEmployeeInfoDao() throws SQLException {
        this.client = new DalTableDao<>(new <PERSON><PERSON><PERSON><PERSON>JpaParser<>(BusinessDashboardEmployeeInfo.class));
        this.queryDao = new DalQueryDao(DATA_BASE);
    }

    /**
     * Query BusinessDashboardEmployeeInfo by the specified ID
     * The ID must be a number
     */
    public BusinessDashboardEmployeeInfo queryByPk(Number id)
            throws SQLException {
        return queryByPk(id, null);
    }

    /**
     * Query BusinessDashboardEmployeeInfo by the specified ID
     * The ID must be a number
     */
    public BusinessDashboardEmployeeInfo queryByPk(Number id, DalHints hints)
            throws SQLException {
        hints = DalHints.createIfAbsent(hints);
        return client.queryByPk(id, hints);
    }

    /**
     * Query BusinessDashboardEmployeeInfo by BusinessDashboardEmployeeInfo instance which the primary key is set
     */
    public BusinessDashboardEmployeeInfo queryByPk(BusinessDashboardEmployeeInfo pk)
            throws SQLException {
        return queryByPk(pk, null);
    }

    /**
     * Query BusinessDashboardEmployeeInfo by BusinessDashboardEmployeeInfo instance which the primary key is set
     */
    public BusinessDashboardEmployeeInfo queryByPk(BusinessDashboardEmployeeInfo pk, DalHints hints)
            throws SQLException {
        hints = DalHints.createIfAbsent(hints);
        return client.queryByPk(pk, hints);
    }

    /**
     * Query against sample pojo. All not null attributes of the passed in pojo
     * will be used as search criteria.
     */
    public List<BusinessDashboardEmployeeInfo> queryBy(BusinessDashboardEmployeeInfo sample)
            throws SQLException {
        return queryBy(sample, null);
    }

    /**
     * Query against sample pojo. All not null attributes of the passed in pojo
     * will be used as search criteria.
     */
    public List<BusinessDashboardEmployeeInfo> queryBy(BusinessDashboardEmployeeInfo sample, DalHints hints)
            throws SQLException {
        hints = DalHints.createIfAbsent(hints);
        return client.queryBy(sample, hints);
    }

    /**
     * Get the all records count
     */
    public int count() throws SQLException {
        return count(null);
    }

    /**
     * Get the all records count
     */
    public int count(DalHints hints) throws SQLException {
        hints = DalHints.createIfAbsent(hints);
        SelectSqlBuilder builder = new SelectSqlBuilder().selectCount();
        return client.count(builder, hints).intValue();
    }

    /**
     * Query BusinessDashboardEmployeeInfo with paging function
     * The pageSize and pageNo must be greater than zero.
     */
    public List<BusinessDashboardEmployeeInfo> queryAllByPage(int pageNo, int pageSize) throws SQLException {
        return queryAllByPage(pageNo, pageSize, null);
    }

    /**
     * Query BusinessDashboardEmployeeInfo with paging function
     * The pageSize and pageNo must be greater than zero.
     */
    public List<BusinessDashboardEmployeeInfo> queryAllByPage(int pageNo, int pageSize, DalHints hints) throws SQLException {
        hints = DalHints.createIfAbsent(hints);

        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.selectAll().atPage(pageNo, pageSize).orderBy("id", ASC);

        return client.query(builder, hints);
    }

    /**
     * Get all records from table
     */
    public List<BusinessDashboardEmployeeInfo> queryAll() throws SQLException {
        return queryAll(null);
    }

    /**
     * Get all records from table
     */
    public List<BusinessDashboardEmployeeInfo> queryAll(DalHints hints) throws SQLException {
        hints = DalHints.createIfAbsent(hints);

        SelectSqlBuilder builder = new SelectSqlBuilder().selectAll().orderBy("id", ASC);

        return client.query(builder, hints);
    }

    /**
     * Insert single pojo
     *
     * @param daoPojo pojo to be inserted
     * @return how many rows been affected
     * @throws SQLException
     */
    public int insert(BusinessDashboardEmployeeInfo daoPojo) throws SQLException {
        return insert(null, daoPojo);
    }

    /**
     * Insert single pojo
     *
     * @param hints   Additional parameters that instruct how DAL Client perform database operation.
     * @param daoPojo pojo to be inserted
     * @return how many rows been affected
     * @throws SQLException
     */
    public int insert(DalHints hints, BusinessDashboardEmployeeInfo daoPojo) throws SQLException {
        if (null == daoPojo) {
            return 0;
        }
        hints = DalHints.createIfAbsent(hints);
        return client.insert(hints, daoPojo);
    }

    /**
     * Insert pojos one by one. If you want to inert them in the batch mode,
     * user batchInsert instead. You can also use the combinedInsert.
     *
     * @param daoPojos list of pojos to be inserted
     * @return how many rows been affected
     */
    public int[] insert(List<BusinessDashboardEmployeeInfo> daoPojos) throws SQLException {
        return insert(null, daoPojos);
    }

    /**
     * Insert pojos one by one. If you want to inert them in the batch mode,
     * user batchInsert instead. You can also use the combinedInsert.
     *
     * @param hints    Additional parameters that instruct how DAL Client perform database operation.
     *                 DalHintEnum.continueOnError can be used
     *                 to indicate that the inserting can be go on if there is any
     *                 failure.
     * @param daoPojos list of pojos to be inserted
     * @return how many rows been affected
     */
    public int[] insert(DalHints hints, List<BusinessDashboardEmployeeInfo> daoPojos) throws SQLException {
        if (null == daoPojos || daoPojos.size() <= 0) {
            return new int[0];
        }
        hints = DalHints.createIfAbsent(hints);
        return client.insert(hints, daoPojos);
    }

    /**
     * Insert pojo and get the generated PK back in keyHolder.
     * If the "set no count on" for MS SqlServer is set, the operation may fail.
     * Please don't pass keyholder for MS SqlServer to avoid the failure in such case.
     *
     * @param keyHolder holder for generated primary keys
     * @param daoPojo   pojo to be inserted
     * @return how many rows been affected
     * @throws SQLException
     */
    public int insertWithKeyHolder(KeyHolder keyHolder, BusinessDashboardEmployeeInfo daoPojo) throws SQLException {
        return insert(null, keyHolder, daoPojo);
    }

    /**
     * Insert pojo and get the generated PK back in keyHolder.
     * If the "set no count on" for MS SqlServer is set, the operation may fail.
     * Please don't pass keyholder for MS SqlServer to avoid the failure in such case.
     *
     * @param hints     Additional parameters that instruct how DAL Client perform database operation.
     * @param keyHolder holder for generated primary keys
     * @param daoPojo   pojo to be inserted
     * @return how many rows been affected
     * @throws SQLException
     */
    public int insert(DalHints hints, KeyHolder keyHolder, BusinessDashboardEmployeeInfo daoPojo) throws SQLException {
        if (null == daoPojo) {
            return 0;
        }
        hints = DalHints.createIfAbsent(hints);
        return client.insert(hints, keyHolder, daoPojo);
    }

    /**
     * Insert pojos and get the generated PK back in keyHolder.
     * If the "set no count on" for MS SqlServer is set, the operation may fail.
     * Please don't pass keyholder for MS SqlServer to avoid the failure in such case.
     *
     * @param keyHolder holder for generated primary keys
     * @param daoPojos  list of pojos to be inserted
     * @return how many rows been affected
     * @throws SQLException
     */
    public int[] insertWithKeyHolder(KeyHolder keyHolder, List<BusinessDashboardEmployeeInfo> daoPojos) throws SQLException {
        return insert(null, keyHolder, daoPojos);
    }

    /**
     * Insert pojos and get the generated PK back in keyHolder.
     * If the "set no count on" for MS SqlServer is set, the operation may fail.
     * Please don't pass keyholder for MS SqlServer to avoid the failure in such case.
     *
     * @param hints     Additional parameters that instruct how DAL Client perform database operation.
     *                  DalHintEnum.continueOnError can be used
     *                  to indicate that the inserting can be go on if there is any
     *                  failure.
     * @param keyHolder holder for generated primary keys
     * @param daoPojos  list of pojos to be inserted
     * @return how many rows been affected
     * @throws SQLException
     */
    public int[] insert(DalHints hints, KeyHolder keyHolder, List<BusinessDashboardEmployeeInfo> daoPojos) throws SQLException {
        if (null == daoPojos || daoPojos.size() <= 0) {
            return new int[0];
        }
        hints = DalHints.createIfAbsent(hints);
        return client.insert(hints, keyHolder, daoPojos);
    }

    /**
     * Insert pojos in batch mode.
     * The DalDetailResults will be set in hints to allow client know how the operation performed in each of the shard.
     *
     * @param daoPojos list of pojos to be inserted
     * @return how many rows been affected for inserting each of the pojo
     * @throws SQLException
     */
    public int[] batchInsert(List<BusinessDashboardEmployeeInfo> daoPojos) throws SQLException {
        return batchInsert(null, daoPojos);
    }

    /**
     * Insert pojos in batch mode.
     * The DalDetailResults will be set in hints to allow client know how the operation performed in each of the shard.
     *
     * @param hints    Additional parameters that instruct how DAL Client perform database operation.
     * @param daoPojos list of pojos to be inserted
     * @return how many rows been affected for inserting each of the pojo
     * @throws SQLException
     */
    public int[] batchInsert(DalHints hints, List<BusinessDashboardEmployeeInfo> daoPojos) throws SQLException {
        if (null == daoPojos || daoPojos.size() <= 0) {
            return new int[0];
        }
        hints = DalHints.createIfAbsent(hints);
        return client.batchInsert(hints, daoPojos);
    }

    /**
     * Insert multiple pojos in one INSERT SQL
     * The DalDetailResults will be set in hints to allow client know how the operation performed in each of the shard.
     *
     * @param daoPojos list of pojos to be inserted
     * @return how many rows been affected
     * @throws SQLException
     */
    public int combinedInsert(List<BusinessDashboardEmployeeInfo> daoPojos) throws SQLException {
        return combinedInsert(null, daoPojos);
    }

    /**
     * Insert multiple pojos in one INSERT SQL
     * The DalDetailResults will be set in hints to allow client know how the operation performed in each of the shard.
     *
     * @param hints    Additional parameters that instruct how DAL Client perform database operation.
     * @param daoPojos list of pojos to be inserted
     * @return how many rows been affected
     * @throws SQLException
     */
    public int combinedInsert(DalHints hints, List<BusinessDashboardEmployeeInfo> daoPojos) throws SQLException {
        if (null == daoPojos || daoPojos.size() <= 0) {
            return 0;
        }
        hints = DalHints.createIfAbsent(hints);
        return client.combinedInsert(hints, daoPojos);
    }

    /**
     * Insert multiple pojos in one INSERT SQL and get the generated PK back in keyHolder.
     * If the "set no count on" for MS SqlServer is set, the operation may fail.
     * Please don't pass keyholder for MS SqlServer to avoid the failure in such case.
     * The DalDetailResults will be set in hints to allow client know how the operation performed in each of the shard.
     *
     * @param keyHolder holder for generated primary keys
     * @param daoPojos  list of pojos to be inserted
     * @return how many rows been affected
     * @throws SQLException
     */
    public int combinedInsertWithKeyHolder(KeyHolder keyHolder, List<BusinessDashboardEmployeeInfo> daoPojos) throws SQLException {
        return combinedInsert(null, keyHolder, daoPojos);
    }

    /**
     * Insert multiple pojos in one INSERT SQL and get the generated PK back in keyHolder.
     * If the "set no count on" for MS SqlServer is set, the operation may fail.
     * Please don't pass keyholder for MS SqlServer to avoid the failure in such case.
     * The DalDetailResults will be set in hints to allow client know how the operation performed in each of the shard.
     *
     * @param hints     Additional parameters that instruct how DAL Client perform database operation.
     * @param keyHolder holder for generated primary keys
     * @param daoPojos  list of pojos to be inserted
     * @return how many rows been affected
     * @throws SQLException
     */
    public int combinedInsert(DalHints hints, KeyHolder keyHolder, List<BusinessDashboardEmployeeInfo> daoPojos) throws SQLException {
        if (null == daoPojos || daoPojos.size() <= 0) {
            return 0;
        }
        hints = DalHints.createIfAbsent(hints);
        return client.combinedInsert(hints, keyHolder, daoPojos);
    }

    /**
     * Delete the given pojo.
     *
     * @param daoPojo pojo to be deleted
     * @return how many rows been affected
     * @throws SQLException
     */
    public int delete(BusinessDashboardEmployeeInfo daoPojo) throws SQLException {
        return delete(null, daoPojo);
    }

    /**
     * Delete the given pojo.
     *
     * @param hints   Additional parameters that instruct how DAL Client perform database operation.
     * @param daoPojo pojo to be deleted
     * @return how many rows been affected
     * @throws SQLException
     */
    public int delete(DalHints hints, BusinessDashboardEmployeeInfo daoPojo) throws SQLException {
        if (null == daoPojo) {
            return 0;
        }
        hints = DalHints.createIfAbsent(hints);
        return client.delete(hints, daoPojo);
    }

    /**
     * Delete the given pojos list one by one.
     *
     * @param daoPojos list of pojos to be deleted
     * @return how many rows been affected
     * @throws SQLException
     */
    public int[] delete(List<BusinessDashboardEmployeeInfo> daoPojos) throws SQLException {
        return delete(null, daoPojos);
    }

    /**
     * Delete the given pojos list one by one.
     *
     * @param hints    Additional parameters that instruct how DAL Client perform database operation.
     * @param daoPojos list of pojos to be deleted
     * @return how many rows been affected
     * @throws SQLException
     */
    public int[] delete(DalHints hints, List<BusinessDashboardEmployeeInfo> daoPojos) throws SQLException {
        if (null == daoPojos || daoPojos.size() <= 0) {
            return new int[0];
        }
        hints = DalHints.createIfAbsent(hints);
        return client.delete(hints, daoPojos);
    }

    /**
     * Delete the given pojo list in batch.
     * The DalDetailResults will be set in hints to allow client know how the operation performed in each of the shard.
     *
     * @param daoPojos list of pojos to be deleted
     * @return how many rows been affected for deleting each of the pojo
     * @throws SQLException
     */
    public int[] batchDelete(List<BusinessDashboardEmployeeInfo> daoPojos) throws SQLException {
        return batchDelete(null, daoPojos);
    }

    /**
     * Delete the given pojo list in batch.
     * The DalDetailResults will be set in hints to allow client know how the operation performed in each of the shard.
     *
     * @param hints    Additional parameters that instruct how DAL Client perform database operation.
     * @param daoPojos list of pojos to be deleted
     * @return how many rows been affected for deleting each of the pojo
     * @throws SQLException
     */
    public int[] batchDelete(DalHints hints, List<BusinessDashboardEmployeeInfo> daoPojos) throws SQLException {
        if (null == daoPojos || daoPojos.size() <= 0) {
            return new int[0];
        }
        hints = DalHints.createIfAbsent(hints);
        return client.batchDelete(hints, daoPojos);
    }

    /**
     * Delete multiple pojos in one DELETE SQL.
     *
     * @param daoPojos list of pojos to be deleted
     * @return how many rows been affected
     * @throws SQLException
     */
    public int combinedDelete(List<BusinessDashboardEmployeeInfo> daoPojos) throws SQLException {
        return combinedDelete(null, daoPojos);
    }

    /**
     * Delete multiple pojos in one DELETE SQL.
     *
     * @param hints    Additional parameters that instruct how DAL Client perform database operation.
     * @param daoPojos list of pojos to be deleted
     * @return how many rows been affected
     * @throws SQLException
     */
    public int combinedDelete(DalHints hints, List<BusinessDashboardEmployeeInfo> daoPojos) throws SQLException {
        if (null == daoPojos || daoPojos.size() <= 0) {
            return 0;
        }
        hints = DalHints.createIfAbsent(hints);
        return client.combinedDelete(hints, daoPojos);
    }

    /**
     * Update the given pojo . By default, if a field of pojo is null value,
     * that field will be ignored, so that it will not be updated. You can
     * overwrite this by set updateNullField in hints.
     *
     * @param daoPojo pojo to be updated
     * @return how many rows been affected
     * @throws SQLException
     */
    public int update(BusinessDashboardEmployeeInfo daoPojo) throws SQLException {
        return update(null, daoPojo);
    }

    /**
     * Update the given pojo . By default, if a field of pojo is null value,
     * that field will be ignored, so that it will not be updated. You can
     * overwrite this by set updateNullField in hints.
     *
     * @param hints   Additional parameters that instruct how DAL Client perform database operation.
     *                DalHintEnum.updateNullField can be used
     *                to indicate that the field of pojo is null value will be update.
     * @param daoPojo pojo to be updated
     * @return how many rows been affected
     * @throws SQLException
     */
    public int update(DalHints hints, BusinessDashboardEmployeeInfo daoPojo) throws SQLException {
        if (null == daoPojo) {
            return 0;
        }
        hints = DalHints.createIfAbsent(hints);
        return client.update(hints, daoPojo);
    }

    /**
     * Update the given pojo list one by one. By default, if a field of pojo is null value,
     * that field will be ignored, so that it will not be updated. You can
     * overwrite this by set updateNullField in hints.
     *
     * @param daoPojos list of pojos to be updated
     * @return how many rows been affected
     * @throws SQLException
     */
    public int[] update(List<BusinessDashboardEmployeeInfo> daoPojos) throws SQLException {
        return update(null, daoPojos);
    }

    /**
     * Update the given pojo list one by one. By default, if a field of pojo is null value,
     * that field will be ignored, so that it will not be updated. You can
     * overwrite this by set updateNullField in hints.
     *
     * @param hints    Additional parameters that instruct how DAL Client perform database operation.
     *                 DalHintEnum.updateNullField can be used
     *                 to indicate that the field of pojo is null value will be update.
     * @param daoPojos list of pojos to be updated
     * @return how many rows been affected
     * @throws SQLException
     */
    public int[] update(DalHints hints, List<BusinessDashboardEmployeeInfo> daoPojos) throws SQLException {
        if (null == daoPojos || daoPojos.size() <= 0) {
            return new int[0];
        }
        hints = DalHints.createIfAbsent(hints);
        return client.update(hints, daoPojos);
    }

    /**
     * Update the given pojo list in batch.
     *
     * @return how many rows been affected
     * @throws SQLException
     */
    public int[] batchUpdate(List<BusinessDashboardEmployeeInfo> daoPojos) throws SQLException {
        return batchUpdate(null, daoPojos);
    }

    /**
     * Update the given pojo list in batch.
     *
     * @return how many rows been affected
     * @throws SQLException
     */
    public int[] batchUpdate(DalHints hints, List<BusinessDashboardEmployeeInfo> daoPojos) throws SQLException {
        if (null == daoPojos || daoPojos.size() <= 0) {
            return new int[0];
        }
        hints = DalHints.createIfAbsent(hints);
        return client.batchUpdate(hints, daoPojos);
    }

    public BusinessDashboardEmployeeInfo queryByDomainName(String domainName) throws SQLException {
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.equal("domain_name", domainName, Types.VARCHAR);
        return client.queryFirst(builder, new DalHints());
    }


    public BusinessDashboardEmployeeInfo queryByEmpCode(String empCode) throws SQLException {
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.equal("emp_code", empCode, Types.VARCHAR);
        return client.queryFirst(builder, new DalHints());
    }


    public List<BusinessDashboardEmployeeInfo> queryByEmpCodeList(List<String> empCodeList) throws SQLException {
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.in("emp_code", empCodeList, Types.VARCHAR);
        return client.query(builder, new DalHints());
    }


    public List<BusinessDashboardEmployeeInfo> queryByTeamId(String teamId) throws SQLException {
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.equal("team_id", teamId, Types.VARCHAR);
        return client.query(builder, new DalHints());
    }

    public List<BusinessDashboardEmployeeInfo> likeSearchEmpInfo(String searchWord,
                                                                 String teamId) throws SQLException {
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.equal("team_id", teamId, Types.VARCHAR);
        builder.and().like("display_name", "%" + searchWord + "%", Types.VARCHAR);
        builder.or().equal("team_id", teamId, Types.VARCHAR);
        builder.and().like("domain_name", "%" + searchWord + "%", Types.VARCHAR);
        return client.query(builder, new DalHints());
    }


    public List<BusinessDashboardEmployeeInfo> likeSearchEmpInfo(String searchWord) throws SQLException {
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.like("display_name", "%" + searchWord + "%", Types.VARCHAR);
        builder.or().like("domain_name", "%" + searchWord + "%", Types.VARCHAR);
        return client.query(builder, new DalHints());
    }

    /**
     * 查询某个人对应的直属上级信息
     * @param domainName
     * @return
     * @throws SQLException
     */
    public BusinessDashboardEmployeeInfo getLeaderEmployeeInfo(String domainName) throws SQLException {
        BusinessDashboardEmployeeInfo employeeInfo = queryByDomainName(domainName);
        String leaderEmpCode = employeeInfo.getLeaderEmpCode();
        return queryByEmpCode(leaderEmpCode);
    }


    /**
     * 查询一个人的直属下属数量
     * @param domainName
     * @return
     * @throws SQLException
     */
    public int getDirectSubordinateNumber(String domainName) throws SQLException {
        BusinessDashboardEmployeeInfo employeeInfo = queryByDomainName(domainName);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.equal("leader_emp_code", employeeInfo.getEmpCode(), Types.VARCHAR);
        builder.selectCount();
        return client.count(builder,new DalHints()).intValue();
    }


    /**
     * 查询一个人的直接下属对应的domainNameList
     * @param domainName
     * @return
     * @throws SQLException
     */
    public List<String> getDirectSubordinateEmpCodeList(String domainName) throws SQLException {
        BusinessDashboardEmployeeInfo employeeInfo = queryByDomainName(domainName);
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.equal("leader_emp_code", employeeInfo.getEmpCode(), Types.VARCHAR);
        builder.select("emp_code");
        return client.query(builder,new DalHints(),String.class);
    }


    /**
     * 查询一个人的直属下属数量
     *
     * @param empCode
     * @param orgId
     * @return
     * @throws SQLException
     */
    public int getDirectSubordinateNumber(String empCode,
                                          String orgId) throws SQLException {
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.equal("leader_emp_code", empCode, Types.VARCHAR);
        builder.and().like("org_id_path", "%" + orgId + "%", Types.VARCHAR);
        builder.selectCount();
        return client.count(builder, new DalHints()).intValue();
    }


    /**
     * 查询一个人的直接下属对应的domainNameList
     * @param empCode
     * @param orgId
     * @return
     * @throws SQLException
     */
    public List<String> getDirectSubordinateEmpCodeList(String empCode,
                                                        String orgId) throws SQLException {
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.equal("leader_emp_code", empCode, Types.VARCHAR);
        builder.and().like("org_id_path", "%" + orgId + "%", Types.VARCHAR);
        builder.select("emp_code");
        return client.query(builder, new DalHints(), String.class);
    }






    /**
     * 查询一个人的全部下属数量(不包含自己)
     * @param orgIdList
     * @return
     */
    public int getAllSubordinateNumber(List<String> orgIdList) throws SQLException {
        StringBuilder sb = new StringBuilder("select count(*) from business_dashboard_employee_info where ");
        int index = 1;
        StatementParameters parameters = new StatementParameters();
        for (String orgId : orgIdList) {
            sb.append(" org_id_path like ? or");
            parameters.set(index++, "%" + orgId + "%");
        }
        sb.delete(sb.length() - 2, sb.length());
        return queryDao.queryFirst(sb.toString(), parameters, new DalHints(), Integer.class);
    }


    /**
     * 查询一个人的所有下属对应的domainNameList(不包含自己)
     *
     * @param orgIdList
     * @return
     * @throws SQLException
     */
    public List<String> getAllSubordinateDomainNameList(List<String> orgIdList) throws SQLException {
        StringBuilder sb = new StringBuilder("select domain_name from business_dashboard_employee_info where ");
        int index = 1;
        StatementParameters parameters = new StatementParameters();
        for (String orgId : orgIdList) {
            sb.append(" org_id_path like ? or");
            parameters.set(index++, "%" + orgId + "%");
        }
        sb.delete(sb.length() - 2, sb.length());
        return queryDao.query(sb.toString(), parameters, new DalHints(), String.class);
    }

    /**
     * 查询一个人的所有下属对应的domainNameList(不包含自己)
     *
     * @param orgIdList
     * @return
     * @throws SQLException
     */
    public List<BusinessDashboardEmployeeInfo> getAllSubordinateList(List<String> orgIdList) throws SQLException {
        StringBuilder sb = new StringBuilder("select * from business_dashboard_employee_info where ");
        int index = 1;
        StatementParameters parameters = new StatementParameters();
        for (String orgId : orgIdList) {
            sb.append(" org_id_path like ? or");
            parameters.set(index++, "%" + orgId + "%");
        }
        sb.delete(sb.length() - 2, sb.length());
        return queryDao.query(sb.toString(), parameters, new DalHints(), BusinessDashboardEmployeeInfo.class);
    }


    public List<BusinessDashboardEmployeeInfo> getEmployeeCode(List<String> domainNameList) throws SQLException {
        DalHints hints = new DalHints();
        SelectSqlBuilder builder = new SelectSqlBuilder();
        builder.select("emp_code", "domain_name");
        builder.in("domain_name", domainNameList, Types.VARCHAR);
        return client.query(builder, hints);
    }

//    public List<BusinessDashboardEmployeeInfo> queryAllOverseaEmp() throws SQLException {
//        return queryDao.query("select * from business_dashboard_employee_info where org_name_path like '%国际化业务部%'", new StatementParameters(), new DalHints(), BusinessDashboardEmployeeInfo.class);
//    }

//    public List<BusinessDashboardEmployeeInfo> queryAllDomesticEmp() throws SQLException {
//        return queryDao.query("select * from business_dashboard_employee_info where org_name_path not like '%国际化业务部%'", new StatementParameters(), new DalHints(), BusinessDashboardEmployeeInfo.class);
//    }
}
