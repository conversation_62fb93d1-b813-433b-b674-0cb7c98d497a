package com.ctrip.tour.business.dashboard.tktBusiness.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Sensitive;
import com.ctrip.platform.dal.dao.annotation.Type;
import java.sql.Types;
import java.sql.Timestamp;

import com.ctrip.platform.dal.dao.DalPojo;

/**
 * <AUTHOR>
 * @date 2023-06-27
 */
@Entity
@Database(name = "TtdReportDB_W")
@Table(name = "business_dashboard_examinee_config_v2")
public class BusinessDashboardExamineeConfigV2 implements DalPojo {

	/**
	 * 主键
	 */
	@Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long id;

	/**
	 * 邮箱前缀
	 */
	@Column(name = "domain_name")
	@Type(value = Types.VARCHAR)
	private String domainName;

	/**
	 * 角色（1一线运营/2一线BD/3省长/4省运营/5大区总/6大区运营）
	 */
	@Column(name = "role")
	@Type(value = Types.SMALLINT)
	private Integer role;

	/**
	 * 目标年
	 */
	@Column(name = "year")
	@Type(value = Types.VARCHAR)
	private String year;

	/**
	 * 考核周期
	 */
	@Column(name = "quarter")
	@Type(value = Types.VARCHAR)
	private String quarter;

	/**
	 * 考核指标（多个逗号分割1；2；3）指标类型 1-GMV 2-毛利 3-质量成本 4-直签 5-6-7- 8-活动覆盖 9-品类覆盖
	 */
	@Column(name = "examine_metric")
	@Type(value = Types.VARCHAR)
	private String examineMetric;

	/**
	 * 去掉-考核类型（1-门票+活动；2-单门票；3-单活动）
	 */
	@Column(name = "examine_type")
	@Type(value = Types.SMALLINT)
	private Integer examineType;

	/**
	 * 国内景点考核层级(枚举值 大区 省份 景点 三方 国内)
	 */
	@Column(name = "examine_level")
	@Type(value = Types.VARCHAR)
	private String examineLevel;

	/**
	 * 国内景点考核范围（考核层级为景点时关联BD，大区省份时关联区域）多数据用逗号隔开
	 */
	@Column(name = "examine_range")
	@Type(value = Types.VARCHAR)
	private String examineRange;

	/**
	 * 活动考核层级
	 */
	@Column(name = "act_examine_level")
	@Type(value = Types.VARCHAR)
	private String actExamineLevel;

	/**
	 * 活动考核范围
	 */
	@Column(name = "act_examine_range")
	@Type(value = Types.VARCHAR)
	private String actExamineRange;

	/**
	 * 是否包含环球影城1包含 0-不包含
	 */
	@Column(name = "need_universal_studios")
	@Type(value = Types.VARCHAR)
	private String needUniversalStudios;

	/**
	 * 分区
	 */
	@Column(name = "query_d")
	@Type(value = Types.VARCHAR)
	private String queryD;

	/**
	 * 最后更新时间
	 */
	@Id
	@Column(name = "DataChange_LastTime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;

	/**
	 * 日游考核层级
	 */
	@Column(name = "odt_examine_level")
	@Type(value = Types.VARCHAR)
	private String odtExamineLevel;

	/**
	 * 日游考核范围
	 */
	@Column(name = "odt_examine_range")
	@Type(value = Types.VARCHAR)
	private String odtExamineRange;

	/**
	 * 出境日游考核层级
	 */
	@Column(name = "oversea_odt_examine_level")
	@Type(value = Types.VARCHAR)
	private String overseaOdtExamineLevel;

	/**
	 * 出境日游考核范围
	 */
	@Column(name = "oversea_odt_examine_range")
	@Type(value = Types.VARCHAR)
	private String overseaOdtExamineRange;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getDomainName() {
		return domainName;
	}

	public void setDomainName(String domainName) {
		this.domainName = domainName;
	}

	public Integer getRole() {
		return role;
	}

	public void setRole(Integer role) {
		this.role = role;
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year;
	}

	public String getQuarter() {
		return quarter;
	}

	public void setQuarter(String quarter) {
		this.quarter = quarter;
	}

	public String getExamineMetric() {
		return examineMetric;
	}

	public void setExamineMetric(String examineMetric) {
		this.examineMetric = examineMetric;
	}

	public Integer getExamineType() {
		return examineType;
	}

	public void setExamineType(Integer examineType) {
		this.examineType = examineType;
	}

	public String getExamineLevel() {
		return examineLevel;
	}

	public void setExamineLevel(String examineLevel) {
		this.examineLevel = examineLevel;
	}

	public String getExamineRange() {
		return examineRange;
	}

	public void setExamineRange(String examineRange) {
		this.examineRange = examineRange;
	}

	public String getActExamineLevel() {
		return actExamineLevel;
	}

	public void setActExamineLevel(String actExamineLevel) {
		this.actExamineLevel = actExamineLevel;
	}

	public String getActExamineRange() {
		return actExamineRange;
	}

	public void setActExamineRange(String actExamineRange) {
		this.actExamineRange = actExamineRange;
	}

	public String getNeedUniversalStudios() {
		return needUniversalStudios;
	}

	public void setNeedUniversalStudios(String needUniversalStudios) {
		this.needUniversalStudios = needUniversalStudios;
	}

	public String getQueryD() {
		return queryD;
	}

	public void setQueryD(String queryD) {
		this.queryD = queryD;
	}

	public Timestamp getDatachangeLasttime() {
		return datachangeLasttime;
	}

	public void setDatachangeLasttime(Timestamp datachangeLasttime) {
		this.datachangeLasttime = datachangeLasttime;
	}

	public String getOdtExamineLevel() {
		return odtExamineLevel;
	}

	public void setOdtExamineLevel(String odtExamineLevel) {
		this.odtExamineLevel = odtExamineLevel;
	}

	public String getOdtExamineRange() {
		return odtExamineRange;
	}

	public void setOdtExamineRange(String odtExamineRange) {
		this.odtExamineRange = odtExamineRange;
	}

	public String getOverseaOdtExamineLevel() {
		return overseaOdtExamineLevel;
	}

	public void setOverseaOdtExamineLevel(String overseaOdtExamineLevel) {
		this.overseaOdtExamineLevel = overseaOdtExamineLevel;
	}

	public String getOverseaOdtExamineRange() {
		return overseaOdtExamineRange;
	}

	public void setOverseaOdtExamineRange(String overseaOdtExamineRange) {
		this.overseaOdtExamineRange = overseaOdtExamineRange;
	}

}
