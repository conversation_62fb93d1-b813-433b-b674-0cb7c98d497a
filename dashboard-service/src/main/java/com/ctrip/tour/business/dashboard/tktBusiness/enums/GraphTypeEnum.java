package com.ctrip.tour.business.dashboard.tktBusiness.enums;

public enum GraphTypeEnum {
    LINE_CHART("lineChart"),
    BAR_CHART("barChart");

    private String name;

    GraphTypeEnum(String name) {
        this.name = name;
    }


    public String getName() {
        return name;
    }

    public static GraphTypeEnum getByCode(String code) {
        for (GraphTypeEnum buType : GraphTypeEnum.values()) {
            if (buType.getName().equals(code)) {
                return buType;
            }
        }
        return null;
    }
}
