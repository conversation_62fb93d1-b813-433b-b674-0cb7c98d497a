package com.ctrip.tour.business.dashboard.tktBusiness.strategy.domestic.impl;

import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.grpBusiness.enums.DomesticMetricEnum;
import com.ctrip.tour.business.dashboard.sightArchives.dao.domesticmetric.AdmPrdTtdCategoryCoverInfoDao;
import com.ctrip.tour.business.dashboard.sightArchives.dao.domesticmetric.BoardCategoryCoverTargetDao;
import com.ctrip.tour.business.dashboard.sightArchives.entity.domesticMetricEntity.AdmPrdTtdCategoryCoverInfoBO;
import com.ctrip.tour.business.dashboard.sightArchives.entity.domesticMetricEntity.Domestic567WeakNessBO;
import com.ctrip.tour.business.dashboard.sightArchives.entity.domesticMetricEntity.Domestic9TargetBO;
import com.ctrip.tour.business.dashboard.sightArchives.entity.domesticMetricEntity.bean.Domestic567Param;
import com.ctrip.tour.business.dashboard.sightArchives.entity.domesticMetricEntity.bean.Domestic9Param;
import com.ctrip.tour.business.dashboard.sightArchives.entity.domesticmetric.Domestic9ParamBean;
import com.ctrip.tour.business.dashboard.sightArchives.entity.domesticmetric.Domestic9SearchResult;
import com.ctrip.tour.business.dashboard.sightArchives.entity.domesticmetric.Domestic9TargetParamBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ExamineConfigBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bo.ExamineConfigBo;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.Bus9NewDao;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardExamineeConfigV2Dao;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardExamineeConfigV2;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.*;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.domestic.DomesticMetricCalStrategy;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.google.common.collect.Lists;
import com.google.type.Decimal;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

@Component
public class Bus9DomesticMetricStrategy implements DomesticMetricCalStrategy {

    @Autowired
    private Bus9NewDao bus9NewDao;

    @Autowired
    private BoardCategoryCoverTargetDao boardCategoryCoverTargetDao;

    @Autowired
    AdmPrdTtdCategoryCoverInfoDao admPrdTtdCategoryCoverInfoDao;

    @Autowired
    private BusinessDashboardExamineeConfigV2Dao examineeConfigV2Dao;

    @Autowired
    private RemoteConfig remoteConfig;

    private static final Integer CATEGORY_0=0;
    private static final Integer CATEGORY_1=1;
    private static final Double ZERO=new Double(0);

    @Override
    public Future<DomesticMetricDetailInfo> getSingleMetricCardData(String domainName, TimeFilter timeFilter, List<MetricInfoBean> metricInfoBeanList, String d, Boolean isFirst, Integer businessId) throws Exception {
        DomesticMetricDetailInfo metricDetailInfo = new DomesticMetricDetailInfo();
        metricDetailInfo.setMetricCode(DomesticMetricEnum.getCodeById(getMetricName()));

        if (DomesticMetricHelper.existBusinessLevel(businessId, metricInfoBeanList)) {
            return new AsyncResult<>(new DomesticMetricDetailInfo());
        }

        Map<String,MetricInfoBean> metricQuarterMap = metricInfoBeanList.stream().collect(Collectors.toMap(MetricInfoBean::getQuarter, metricInfoBean -> metricInfoBean));
        if (metricQuarterMap.isEmpty()) {
            return new AsyncResult<>(new DomesticMetricDetailInfo());
        }

        List<DomesticNoDrillTableData> tableDataList = new ArrayList<>();
        DomesticNoDrillTableData keyCat = new DomesticNoDrillTableData();
        DomesticNoDrillTableData featCat = new DomesticNoDrillTableData();

        DomesticMetricHelper.initCategoryBean(keyCat,"keyCat");
        DomesticMetricHelper.initCategoryBean(featCat,"featCat");
        double completeRate = 0.0;

        int featCount = 0;
        int keyCount = 0;
        String maxQuarter = DateUtil.getMaxQuarters(new ArrayList<>(metricQuarterMap.keySet()));
        for (Map.Entry<String, MetricInfoBean> entry : metricQuarterMap.entrySet()) {
            String quarterOrMonth;
            if("month".equals(timeFilter.getDateType())) {
                quarterOrMonth = timeFilter.getMonth();
            }else{
                quarterOrMonth = entry.getKey();
                if (!maxQuarter.equals(quarterOrMonth)) {
                    continue;
                }
            }
            MetricInfoBean metricInfoBean = entry.getValue();
            Domestic9ParamBean paramBean = generate9SearchParamBean(d, timeFilter, metricInfoBean, quarterOrMonth, businessId);
            List<Domestic9SearchResult> result = admPrdTtdCategoryCoverInfoDao.getCategoryCoverInfo(paramBean);
            for (Domestic9SearchResult searchResult : result) {
                if (result.size() == 1) {
                    searchResult.setCategoryWeight(1.00);
                }
                if (0 == searchResult.getCategoryType()) {
                    //特色品类
                    featCat.setCategoryExamineType("featCat");
                    featCat.setCategoryCrashExperienceNum(featCat.getCategoryCrashExperienceNum() + searchResult.getTreasureMapExpCnt());
                    featCat.setCategoryOperatingExperienceNum(featCat.getCategoryOperatingExperienceNum() + searchResult.getOpenExpCnt());
                    featCat.setCategoryCoveredExperienceNum(featCat.getCategoryCoveredExperienceNum() + searchResult.getCoverExpCnt());
                    featCat.setCategoryUncoveredExperienceNum(featCat.getCategoryUncoveredExperienceNum() + searchResult.getUncoverExpCnt());
                    featCat.setCategoryWeight(searchResult.getCategoryWeight());
                    Domestic9TargetParamBean domestic9TargetParamBean = generate9TargetSearchParamBean(d, timeFilter, metricInfoBean, quarterOrMonth, businessId, 0);
                    double target = boardCategoryCoverTargetDao.getCategoryCoverTarget(domestic9TargetParamBean);
                    if (target > 0.00) {
                        featCat.setCategoryTargetCoverageRate(featCat.getCategoryTargetCoverageRate() + target / 100);
                        featCount++;
                    }
                }else{
                    //重点品类
                    keyCat.setCategoryExamineType("keyCat");
                    keyCat.setCategoryCrashExperienceNum(keyCat.getCategoryCrashExperienceNum() + searchResult.getTreasureMapExpCnt());
                    keyCat.setCategoryOperatingExperienceNum(keyCat.getCategoryOperatingExperienceNum() + searchResult.getOpenExpCnt());
                    keyCat.setCategoryCoveredExperienceNum(keyCat.getCategoryCoveredExperienceNum() + searchResult.getCoverExpCnt());
                    keyCat.setCategoryUncoveredExperienceNum(keyCat.getCategoryUncoveredExperienceNum() + searchResult.getUncoverExpCnt());
                    keyCat.setCategoryWeight(searchResult.getCategoryWeight());
                    Domestic9TargetParamBean domestic9TargetParamBean = generate9TargetSearchParamBean(d, timeFilter, metricInfoBean, quarterOrMonth, businessId, 1);
                    double target = boardCategoryCoverTargetDao.getCategoryCoverTarget(domestic9TargetParamBean);
                    if (target > 0.00) {
                        keyCat.setCategoryTargetCoverageRate(keyCat.getCategoryTargetCoverageRate() + target / 100);
                        keyCount++;
                    }
                }
            }
        }
        if (keyCat.getCategoryExamineType() != null) {
            keyCat.setCategoryTargetCoverageRate(keyCount == 0 ? 0.00 : keyCat.getCategoryTargetCoverageRate() / keyCount);
            keyCat.setCategoryCurrentCoverageRate(keyCat.getCategoryOperatingExperienceNum() == 0 ? 0.00 :
                    (double) keyCat.getCategoryCoveredExperienceNum() / keyCat.getCategoryOperatingExperienceNum());
            if (keyCat.getCategoryCurrentCoverageRate() >= keyCat.getCategoryTargetCoverageRate()) {
                completeRate += keyCat.getCategoryWeight() == 0.0 ? 0.00 : keyCat.getCategoryWeight();
            } else {
                completeRate += keyCat.getCategoryTargetCoverageRate() == 0.00 ? 0.00 : (double)keyCat.getCategoryCurrentCoverageRate() / keyCat.getCategoryTargetCoverageRate() * keyCat.getCategoryWeight();
            }
        }

        if (featCat.getCategoryExamineType() != null) {
            featCat.setCategoryTargetCoverageRate(featCount == 0 ? 0.00 : featCat.getCategoryTargetCoverageRate() / featCount);
            featCat.setCategoryCurrentCoverageRate(featCat.getCategoryOperatingExperienceNum() == 0 ? 0.00 :
                    (double)featCat.getCategoryCoveredExperienceNum() / featCat.getCategoryOperatingExperienceNum());
            if (featCat.getCategoryCurrentCoverageRate() >= featCat.getCategoryTargetCoverageRate()) {
                completeRate += featCat.getCategoryWeight() == 0.0 ? 0.00 : featCat.getCategoryWeight();
            } else {
                completeRate += featCat.getCategoryTargetCoverageRate() == 0.00 ? 0.00 : (double)featCat.getCategoryCurrentCoverageRate() / featCat.getCategoryTargetCoverageRate() * featCat.getCategoryWeight();
            }
        }

        featCat.setCategoryCompleteRate(completeRate);
        keyCat.setCategoryCompleteRate(completeRate);
        metricDetailInfo.setCompleteRate(completeRate);

        tableDataList.add(featCat);
        tableDataList.add(keyCat);

        metricDetailInfo.setTableHeaderList(Bus9Helper.getTableList());
        metricDetailInfo.setTableDataItemList(tableDataList);

        //设置默认下钻维度
        MetricHelper.setCommonMetricCardDrillDownInfo(metricDetailInfo, metricQuarterMap.get(maxQuarter), businessId);
        //设置默认下钻维度信息
        DalHints rankDalHints = new DalHints().asyncExecution();
        getRankDataAsync(timeFilter.getDateType(), timeFilter.getYear(), maxQuarter, timeFilter.getMonth(), d, domainName, String.valueOf(DomesticMetricEnum.getIdByCode(metricDetailInfo.getMetricCode())), rankDalHints);
        MetricHelper.setCommonMetricCardDrillDownInfo(metricDetailInfo,metricQuarterMap.get(maxQuarter),businessId);

        //获取排名数据
        ChartHelper.fillRankDataV2(metricDetailInfo, rankDalHints.getListResult());

        return new AsyncResult<>(metricDetailInfo);
    }

    public Domestic9TargetParamBean generate9TargetSearchParamBean(String d, TimeFilter timeFilter,MetricInfoBean metricInfoBean,String quarterOrMonth,Integer businessId, Integer mode) {
        List<String> rangeList = new ArrayList<>();
        switch (businessId) {
            case 0:
                if (metricInfoBean.getRegionList() != null) {
                    rangeList = metricInfoBean.getRegionList();
                }
                if (metricInfoBean.getActRegionList() != null) {
                    rangeList = metricInfoBean.getActRegionList();
                }
                break;
            case 1:
                rangeList = metricInfoBean.getRegionList();
                break;
            case 2:
                rangeList = metricInfoBean.getActRegionList();
                break;
        }
        Domestic9TargetParamBean domestic9TargetParamBean = new Domestic9TargetParamBean();
        domestic9TargetParamBean.setMode(mode);
        domestic9TargetParamBean.setD(d);
        domestic9TargetParamBean.setYear(timeFilter.getYear());
        if ("month".equals(timeFilter.getDateType())) {
            quarterOrMonth = DateUtil.getQuarterByMonth(quarterOrMonth);
        }
        domestic9TargetParamBean.setQuarters(Collections.singletonList(quarterOrMonth));
        if ("大区".equals(metricInfoBean.getLevel())) {//NOSONAR
            domestic9TargetParamBean.setRegionNames(rangeList);
            domestic9TargetParamBean.setDataType(1);
        }else if ("省份".equals(metricInfoBean.getLevel())) {//NOSONAR
            domestic9TargetParamBean.setProvinceNames(rangeList);
            domestic9TargetParamBean.setDataType(0);
        }else if ("国内".equals(metricInfoBean.getLevel())){//NOSONAR
            domestic9TargetParamBean.setRegionNames(Collections.singletonList("国内"));//NOSONAR
            domestic9TargetParamBean.setDataType(2);
        }
        return domestic9TargetParamBean;
    }

    public Domestic9ParamBean generate9SearchParamBean(String d, TimeFilter timeFilter,MetricInfoBean metricInfoBean,String quarterOrMonth,Integer businessId) {
        List<String> rangeList = new ArrayList<>();
        switch (businessId) {
            case 0:
                if (metricInfoBean.getRegionList() != null) {
                    rangeList = metricInfoBean.getRegionList();
                }
                if (metricInfoBean.getActRegionList() != null) {
                    rangeList = metricInfoBean.getActRegionList();
                }
                break;
            case 1:
                rangeList = metricInfoBean.getRegionList();
                break;
            case 2:
                rangeList = metricInfoBean.getActRegionList();
                break;
        }
        Domestic9ParamBean domestic9ParamBean = new Domestic9ParamBean();
        domestic9ParamBean.setD(d);
        domestic9ParamBean.setYear(timeFilter.getYear());
        domestic9ParamBean.setDateType(timeFilter.dateType);
        if ("month".equalsIgnoreCase(timeFilter.getDateType())) {
            domestic9ParamBean.setMonth(Collections.singletonList(quarterOrMonth));
        }else{
            domestic9ParamBean.setQuarters(Collections.singletonList(quarterOrMonth));
        }
        if ("大区".equals(metricInfoBean.getLevel())) {//NOSONAR
            domestic9ParamBean.setRegionNames(rangeList);
        }else if ("省份".equals(metricInfoBean.getLevel())) {//NOSONAR
            domestic9ParamBean.setProvinceNames(rangeList);
        }
        return domestic9ParamBean;
    }


    private void getRankDataAsync(String dateType,
                                  String year,
                                  String quarter,
                                  String month,
                                  String d,
                                  String domainName,
                                  String metric,
                                  DalHints dalHints) throws Exception {
        Map<String, List<String>> inMap = new LinkedHashMap<>();
        inMap.put("query_d", Lists.newArrayList(d));
        inMap.put("examinee", Lists.newArrayList(domainName));
        inMap.put("year", Lists.newArrayList(year));
        if ("month".equals(dateType)) {
            inMap.put("month", Lists.newArrayList(month));
        } else {
            String mappingMonth = DateUtil.getMappingMonthByQuarter(d, year, quarter);
            inMap.put("month", Lists.newArrayList(mappingMonth));
        }
        inMap.put("metric", Lists.newArrayList(metric));
        bus9NewDao.getRankAsync(inMap, dalHints);
    }


    @Override
    public GetDomesticMetricTrendDataResponseType getSingleTrendlineData(GetDomesticMetricTrendDataRequestType request, String d) throws Exception {
        GetDomesticMetricTrendDataResponseType response = new GetDomesticMetricTrendDataResponseType();
        List<TrendLineDetailInfo> trendLineDetailInfoList = new ArrayList<>();
        response.setTrendLineDetailInfoList(trendLineDetailInfoList);
        TimeFilter timeFilter = request.getTimeFilter();
        String domainName = request.getDomainName();

        TimeFilter timeFilterWithDomain = DateUtil.getMaxQuarterTimeFilter(timeFilter, d);
        List<ExamineConfigBean> examineConfigBeanList = MultiPeriodMappingHelper.getMultiPeriodConfigList(timeFilterWithDomain, d, examineeConfigV2Dao.queryMetricAllConfig(domainName, d, String.valueOf(getMetricName())), null);

        TrendLineDetailInfo lineChart = new TrendLineDetailInfo();
        TrendLineDetailInfo stackLineChart = new TrendLineDetailInfo();

        lineChart.setType("lineChart");
        lineChart.setDim(request.getMetricCode());

        stackLineChart.setType("stackLineChart");
        stackLineChart.setDim(request.getMetricCode());

        List<TrendLineDataItem> lineDataItems = new ArrayList<>();
        List<TrendLineDataItem> stackLineDataItems = new ArrayList<>();

        lineChart.setTrendLineDataItemList(lineDataItems);
        stackLineChart.setTrendLineDataItemList(stackLineDataItems);
        for (ExamineConfigBean examineConfigBean : examineConfigBeanList) {
            //只下发25年的
            if (Integer.parseInt(examineConfigBean.getYear()) <= 2024) {
                continue;
            }
            //获取单季或单月的数据
            List<TrendLineDataItem> singleLineDataItems = getSingleTrendLineDataWithQuarterOrMonth(request,examineConfigBean, d);
            for (TrendLineDataItem item : singleLineDataItems) {
                switch (item.getName()) {
                    case "keyCat":
                    case "featCat":
                        stackLineDataItems.add(item);
                        break;
                    case "completeRate":
                        lineDataItems.add(item);
                        break;
                }
            }
        }
        trendLineDetailInfoList.add(lineChart);
        trendLineDetailInfoList.add(stackLineChart);

        return response;
    }

    public List<TrendLineDataItem> getSingleTrendLineDataWithQuarterOrMonth(GetDomesticMetricTrendDataRequestType request,
                                                                            ExamineConfigBean examineConfigBean,
                                                                            String d) throws Exception {
        List<TrendLineDataItem> trendLineDataItems = new ArrayList<>();
        String timeString = DomesticMetricHelper.getTimeFormat(examineConfigBean);
        Integer businessId = DomesticMetricHelper.transformBusinessInfo(request.getBusinessId(),request.getSubBusinessId());

        ExamineConfigBo bo = new ExamineConfigBo();
        BusinessDashboardExamineeConfigV2 examineeConfigV2 = examineConfigBean.getBusinessDashboardExamineeConfigV2();
        List<MetricInfoBean> metricInfoBeanList = bo.getMetricInfoBeanListV2(Collections.singletonList(examineeConfigV2), remoteConfig);
        if (metricInfoBeanList.size() != 1) {
            return trendLineDataItems;
        }

        TimeFilter timeFilter = DomesticMetricHelper.getTimeFilter(examineConfigBean);
        boolean isMonth = "month".equalsIgnoreCase(examineConfigBean.getDateType());
        String quarterOrMonth = isMonth ? examineConfigBean.getMonth() : examineConfigBean.getQuarter();

        DomesticNoDrillTableData keyCat = new DomesticNoDrillTableData();
        DomesticNoDrillTableData featCat = new DomesticNoDrillTableData();
        double completeRate = 0.0;

        Domestic9ParamBean paramBean = generate9SearchParamBean(d, timeFilter, metricInfoBeanList.get(0), quarterOrMonth, businessId);
        List<Domestic9SearchResult> result = admPrdTtdCategoryCoverInfoDao.getCategoryCoverInfo(paramBean);
        DomesticMetricHelper.initCategoryBean(keyCat,"keyCat");
        DomesticMetricHelper.initCategoryBean(featCat,"featCat");
        for (Domestic9SearchResult searchResult : result) {
            if (result.size() == 1) {
                searchResult.setCategoryWeight(1.00);
            }
            if (0 == searchResult.getCategoryType()) {
                //特色品类
                featCat.setCategoryExamineType("featCat");
                featCat.setCategoryWeight(searchResult.getCategoryWeight());
                featCat.setCategoryCrashExperienceNum(searchResult.getTreasureMapExpCnt());
                featCat.setCategoryOperatingExperienceNum(searchResult.getOpenExpCnt());
                featCat.setCategoryCoveredExperienceNum(searchResult.getCoverExpCnt());
                featCat.setCategoryUncoveredExperienceNum(searchResult.getUncoverExpCnt());
                Domestic9TargetParamBean domestic9TargetParamBean = generate9TargetSearchParamBean(d, timeFilter, metricInfoBeanList.get(0), quarterOrMonth, businessId, 0);
                double target = boardCategoryCoverTargetDao.getCategoryCoverTarget(domestic9TargetParamBean);
                featCat.setCategoryTargetCoverageRate(target == 0.00 ? 0.00 : target/100);
            }else{
                //重点品类
                keyCat.setCategoryExamineType("keyCat");
                keyCat.setCategoryWeight(searchResult.getCategoryWeight());
                keyCat.setCategoryCrashExperienceNum(searchResult.getTreasureMapExpCnt());
                keyCat.setCategoryOperatingExperienceNum(searchResult.getOpenExpCnt());
                keyCat.setCategoryCoveredExperienceNum(searchResult.getCoverExpCnt());
                keyCat.setCategoryUncoveredExperienceNum(searchResult.getUncoverExpCnt());
                Domestic9TargetParamBean domestic9TargetParamBean = generate9TargetSearchParamBean(d, timeFilter, metricInfoBeanList.get(0), quarterOrMonth, businessId, 1);
                double target = boardCategoryCoverTargetDao.getCategoryCoverTarget(domestic9TargetParamBean);
                keyCat.setCategoryTargetCoverageRate(target == 0.00 ? 0.00 : target/100);
            }
        }

        // 设置目标覆盖率
        featCat.setCategoryTargetCoverageRate(featCat.getCategoryTargetCoverageRate() == 0 ? 0.00 : featCat.getCategoryTargetCoverageRate());
        keyCat.setCategoryTargetCoverageRate(keyCat.getCategoryTargetCoverageRate() == 0 ? 0.00 : keyCat.getCategoryTargetCoverageRate());

        // 设置当前覆盖率
        featCat.setCategoryCurrentCoverageRate(featCat.getCategoryOperatingExperienceNum() == 0.00 ? 0.00 :
                (double)featCat.getCategoryCoveredExperienceNum() / featCat.getCategoryOperatingExperienceNum());
        keyCat.setCategoryCurrentCoverageRate(keyCat.getCategoryOperatingExperienceNum() == 0.00 ? 0.00 :
                (double)keyCat.getCategoryCoveredExperienceNum() / keyCat.getCategoryOperatingExperienceNum());

        // 加权前完成率如果现状大于目标默认为1
        if (featCat.getCategoryCurrentCoverageRate() >= featCat.getCategoryTargetCoverageRate()) {
            completeRate += featCat.getCategoryWeight();
        } else {
            completeRate = featCat.getCategoryTargetCoverageRate() == 0.00 ? 0.00 : (double)featCat.getCategoryCurrentCoverageRate() / featCat.getCategoryTargetCoverageRate() * featCat.getCategoryWeight();
        }
        if (keyCat.getCategoryCurrentCoverageRate() >= keyCat.getCategoryTargetCoverageRate()) {
            completeRate += keyCat.getCategoryWeight();
        } else {
            completeRate = keyCat.getCategoryTargetCoverageRate() == 0.00 ? 0.00 : (double)keyCat.getCategoryCurrentCoverageRate() / keyCat.getCategoryTargetCoverageRate() * keyCat.getCategoryWeight();
        }

        TrendLineDataItem featCatItem = new TrendLineDataItem();
        TrendLineDataItem keyCatItem = new TrendLineDataItem();
        TrendLineDataItem completeRateItem = new TrendLineDataItem();

        featCatItem.setTime(timeString);
        keyCatItem.setTime(timeString);
        completeRateItem.setTime(timeString);

        featCatItem.setName("featCat");
        keyCatItem.setName("keyCat");
        completeRateItem.setName("completeRate");

        featCatItem.setValue(featCat.getCategoryCoveredExperienceNum().doubleValue());
        keyCatItem.setValue(keyCat.getCategoryCoveredExperienceNum().doubleValue());
        completeRateItem.setValue(completeRate);

        trendLineDataItems.add(featCatItem);
        trendLineDataItems.add(keyCatItem);
        trendLineDataItems.add(completeRateItem);

        return trendLineDataItems;
    }


    @Override
    public GetDomesticTableDataResponseType getSingleTableData(GetDomesticTableDataRequestType request, MetricInfoBean metricInfoBean, String d) throws Exception {
        GetDomesticTableDataResponseType response = new GetDomesticTableDataResponseType();
        List<DomesticTableData> tableDataItemList = new ArrayList<>();
        response.setTableDataItemList(tableDataItemList);
        TimeFilter timeFilter = request.getTimeFilter();
        String year = timeFilter.getYear();
        String level = MetricHelper.getLevelColumnName(metricInfoBean.getLevel());
        DrillDownFilter drillDownFilter = request.getDrillDownFilter();

        String field = MetricHelper.getDrillDownColumnName(drillDownFilter.getField());
        response.setTableHeaderList(getTableHeaderList(field));

        if ("trend".equalsIgnoreCase(request.getQueryType())) {
            List<TrendLineDetailInfo> trendLineDetailInfoList = getDrillThrendLineDetailInfoList(request, metricInfoBean, d);
            response.setTrendLineDetailInfoList(trendLineDetailInfoList);
            return response;
        }

        Domestic9Param param = getDomestic9TableParam(d, request.getPageNo(), request.getPageSize(), year, request.getTimeFilter(), metricInfoBean.getLevel(),
                metricInfoBean.getRegionList(), drillDownFilter.getField(), drillDownFilter.getFieldValueList());
        Integer totalCount = admPrdTtdCategoryCoverInfoDao.getDomesticTableCountByField(param);
        if (totalCount == null || totalCount == 0) {
            return response;
        }
        response.setTotalNum(totalCount);
        List<AdmPrdTtdCategoryCoverInfoBO> currentResult = admPrdTtdCategoryCoverInfoDao.getDomesticTableDateByField(param);
        //target
        List<String> currentParam = null;
        if ("大区".equalsIgnoreCase(request.getDrillDownFilter().getField())) {//NOSONAR
            currentParam = currentResult.stream().map(x -> x.getBusinessRegionName()).collect(Collectors.toList());
        } else if ("省份".equalsIgnoreCase(request.getDrillDownFilter().getField())) {//NOSONAR
            currentParam = currentResult.stream().map(x -> x.getProvinceName()).collect(Collectors.toList());
        }
        Domestic9ParamBean targetParam = getDomestic9TargetParam(request.getTimeFilter(), request.getDrillDownFilter(), d, currentParam);
        targetParam.setField(request.getDrillDownFilter().getField());
        List<Domestic9TargetBO> targetResult = boardCategoryCoverTargetDao.getDomestic9TargetValue(targetParam);
        Map<String, Double> targetValueMap = new HashMap<>();
        for (Domestic9TargetBO bo : targetResult) {
            String key = String.format("%s:%s:%s", bo.getRegionNames(), bo.getProvinceNames(), bo.getCategoryExamineMode());
            targetValueMap.put(key, bo.getTarget());
        }
        //- 现状覆盖率=已覆盖且在营业中的特色体验数/营业中特色体验数
        //- 完成率=重点品类现状覆盖率/目标覆盖率*25%+特色品类现状覆盖率/目标覆盖率*75%
        //- 目标覆盖率为目标值
        build9TableData(tableDataItemList, currentResult, targetValueMap);
        return response;
    }

    private void build9TableData(List<DomesticTableData> tableDataItemList, List<AdmPrdTtdCategoryCoverInfoBO> currentResult, Map<String, Double> targetValueMap) {
        Map<String, List<DomesticTableData>> tableMap = new HashMap<>();
        for (AdmPrdTtdCategoryCoverInfoBO bo : currentResult) {
            double currentCover = bo.getCoverExpCnt().doubleValue() / bo.getOpenExpCnt().doubleValue();
            DomesticTableData row = new DomesticTableData();
            row.setRegionName(bo.getBusinessRegionName());
            row.setProvinceName(bo.getProvinceName());
            //权重
            row.setCategoryWeight(bo.getCategoryWeight());
            //藏宝图特色体验数
            row.setTreasuremapExpCnt(bo.getTreasuremapXxpCnt().intValue());
            //营业中特色体验数
            row.setOpenExpCnt(bo.getOpenExpCnt().intValue());
            //已覆盖特色体验数
            row.setCoverExpCnt(bo.getCoverExpCnt().intValue());
            //未覆盖特色体验数
            row.setUncoverExpCnt(bo.getUncoverExpCnt().intValue());
            if (CATEGORY_0.equals(bo.getCategoryType())) {
                //特色品类
                row.setCategoryType("特色品类");//NOSONAR
            } else if (CATEGORY_1.equals(bo.getCategoryType())) {
                //重点品类
                row.setCategoryType("重点品类");//NOSONAR
            }
            row.setCategoryCurrentCoverageRate(Double.valueOf(currentCover));
            String targetKey = String.format("%s:%s:%s", bo.getBusinessRegionName(), bo.getProvinceName(), bo.getCategoryType());
            row.setTargetRate(targetValueMap.get(targetKey));
            if (!ZERO.equals(row.getTargetRate())) {
                row.setCompleteRate(row.getCategoryCurrentCoverageRate() / row.getTargetRate());
            }

            String key = String.format("%s:%s", bo.getBusinessRegionName(), bo.getProvinceName());
            if (tableMap.containsKey(key)) {
                tableMap.get(key).add(row);
            } else {
                tableMap.put(key, new ArrayList<>());
                tableMap.get(key).add(row);
            }
        }
        Double weight = new Double(0.25);
        for (Map.Entry<String, List<DomesticTableData>> entry : tableMap.entrySet()) {
            DomesticTableData row1 = entry.getValue().get(0);
            DomesticTableData row2 = entry.getValue().get(1);
            if (weight.equals(row1.getCategoryWeight())) {
                tableDataItemList.add(row1);
                tableDataItemList.add(row2);
            } else {
                tableDataItemList.add(row2);
                tableDataItemList.add(row1);
            }
        }
    }

    private List<TrendLineDetailInfo> getDrillThrendLineDetailInfoList(GetDomesticTableDataRequestType request,
                                                                       MetricInfoBean metricInfoBean, String d) throws Exception {
        TimeFilter timeFilter = request.getTimeFilter();
        String domainName = request.getDomainName();
        Integer metric = DomesticMetricEnum.getIdByCode(request.getMetricCode());
        TimeFilter timeFilterWithDomain = DateUtil.getMaxQuarterTimeFilter(timeFilter, d);
        List<ExamineConfigBean> examineConfigBeanList = MultiPeriodMappingHelper.getMultiPeriodConfigList(timeFilterWithDomain, d, examineeConfigV2Dao.queryMetricAllConfig(domainName, d, String.valueOf(metric)), null);
        List<TrendLineDetailInfo> trendLineDetailInfoList = new ArrayList<>();

        Map<String, List<TrendLineDataItem>> dimRegionThrendMap = new HashMap<>();
        for (ExamineConfigBean examineConfigBean : examineConfigBeanList) {
            //只下发25年的
            if (Integer.parseInt(examineConfigBean.getYear()) <= 2024) {
                continue;
            }
            //获取单季或单月的数据
            Map<String, TrendLineDataItem> dimRegionDBMap = getTrendLineDataByDrill(request, examineConfigBean, d);
            for (Map.Entry<String, TrendLineDataItem> item : dimRegionDBMap.entrySet()) {
                if (dimRegionThrendMap.containsKey(item.getKey())) {
                    List<TrendLineDataItem> lineDataItems = dimRegionThrendMap.get(item.getKey());
                    lineDataItems.add(item.getValue());
                } else {
                    List<TrendLineDataItem> lineDataItems =new ArrayList<>();
                    lineDataItems.add(item.getValue());
                    dimRegionThrendMap.put(item.getKey(), lineDataItems);
                    TrendLineDetailInfo lineChartFwRate = new TrendLineDetailInfo();
                    lineChartFwRate.setType("lineChart");
                    lineChartFwRate.setDim("complateRate");
                    lineChartFwRate.setTrendLineDataItemList(lineDataItems);
                    trendLineDetailInfoList.add(lineChartFwRate);
                }
            }
        }
        return trendLineDetailInfoList;
    }

    private Map<String, TrendLineDataItem> getTrendLineDataByDrill(GetDomesticTableDataRequestType request, ExamineConfigBean examineConfigBean, String d) {
        //对不同月份，不同考核层级，不同下钻维度，查出实际劣势率
        String timeString = DomesticMetricHelper.getTimeFormat(examineConfigBean);
        BusinessDashboardExamineeConfigV2 examineeConfigV2 = examineConfigBean.getBusinessDashboardExamineeConfigV2();
        String originLevel = examineeConfigV2.getExamineLevel();
        List<String> regionList = null;
        if (StringUtils.isNotEmpty(examineeConfigV2.getExamineRange())) {
            regionList = Arrays.stream(examineeConfigV2.getExamineRange().split(",")).map(String::trim).collect(Collectors.toList());
        }
        //根据下钻维度获取完成值
        Domestic9Param param = getDomestic9TableParam(d,request.getPageNo(),request.getPageSize(), examineConfigBean.getYear(), request.getTimeFilter(), originLevel,
                regionList, request.getDrillDownFilter().getField(), request.getDrillDownFilter().getFieldValueList());
        List<AdmPrdTtdCategoryCoverInfoBO> currentResult = admPrdTtdCategoryCoverInfoDao.getDomesticTableDateByField(param);
        List<String> currentParam = null;
        if ("大区".equalsIgnoreCase(request.getDrillDownFilter().getField())) {//NOSONAR
            currentParam = currentResult.stream().map(x -> x.getBusinessRegionName()).collect(Collectors.toList());
        } else if ("省份".equalsIgnoreCase(request.getDrillDownFilter().getField())) {//NOSONAR
            currentParam = currentResult.stream().map(x -> x.getProvinceName()).collect(Collectors.toList());
        }
        Domestic9ParamBean targetParam = getDomestic9TargetParam(request.getTimeFilter(), request.getDrillDownFilter(), d, currentParam);
        targetParam.setField(request.getDrillDownFilter().getField());
        List<Domestic9TargetBO> targetResult = boardCategoryCoverTargetDao.getDomestic9TargetValue(targetParam);
        Map<String, Double> targetValueMap=new HashMap<>();
        for(Domestic9TargetBO bo:targetResult){
            String key=String.format("%s:%s:%s",bo.getRegionNames(),bo.getProvinceNames(),bo.getCategoryExamineMode());
            targetValueMap.put(key,bo.getTarget());
        }
        List<DomesticTableData> tableDataItemList = new ArrayList<>();
        build9TableData(tableDataItemList, currentResult, targetValueMap);

        Map<String, TrendLineDataItem> dimRegionThrendMap = new HashMap<>();
        for (DomesticTableData data : tableDataItemList) {
            TrendLineDataItem item = new TrendLineDataItem();
            switch (request.getDrillDownFilter().getField()) {
                case "大区"://NOSONAR
                    item.setName(data.getRegionName());
                    dimRegionThrendMap.put(data.getRegionName(), item);
                    break;
                case "省份"://NOSONAR
                    item.setName(data.getProvinceName());
                    dimRegionThrendMap.put(data.getProvinceName(), item);
                    break;
            }
            item.setTime(timeString);
            item.setValue(data.getCompleteRate());
        }
        return dimRegionThrendMap;
    }

    private List<String> getTableHeaderList(String field) {
        List<String> tableHeaderList = new ArrayList<>();
        switch (field) {
            case "region_name":
                tableHeaderList.add("regionName");
                break;
            case "province_name":
                tableHeaderList.add("regionName");
                tableHeaderList.add("provinceName");
                break;
            case "examinee":
                tableHeaderList.add("regionName");
                tableHeaderList.add("provinceName");
                tableHeaderList.add("examinee");
                break;
        }
        tableHeaderList.add("categoryWeight");
        tableHeaderList.add("treasuremapExpCnt");
        tableHeaderList.add("openExpCnt");
        tableHeaderList.add("coverExpCnt");
        tableHeaderList.add("uncoverExpCnt");
        tableHeaderList.add("categoryType");
        tableHeaderList.add("categoryCurrentCoverageRate");
        tableHeaderList.add("targetRate");
        tableHeaderList.add("completeRate");
        return tableHeaderList;
    }
    private List<String> getFirstTableHeaderList(String field) {
        List<String> tableHeaderList = new ArrayList<>();
        switch (field) {
            case "region_name":
                tableHeaderList.add("regionName");
                break;
            case "province_name":
                tableHeaderList.add("regionName");
                tableHeaderList.add("provinceName");
                break;
            case "examinee":
                tableHeaderList.add("regionName");
                tableHeaderList.add("provinceName");
                tableHeaderList.add("examinee");
                break;
        }
        tableHeaderList.add("completeRate");
        return tableHeaderList;
    }
    private Domestic9ParamBean getDomestic9TargetParam(TimeFilter timeFilter,
                                                       DrillDownFilter drillDownFilter,
                                                       String d,
                                                       List<String> currenParam) {
        Domestic9ParamBean domestic9ParamBean = new Domestic9ParamBean();

        domestic9ParamBean.setD(d);
        domestic9ParamBean.setYear(timeFilter.getYear());
        domestic9ParamBean.setDateType(timeFilter.dateType);
        if ("month".equals(timeFilter.getDateType())) {
            domestic9ParamBean.setQuarters(Arrays.asList(DateUtil.getQuarterByMonth(timeFilter.getMonth())));
        } else if ("quarter".equals(timeFilter.getDateType())) {
            domestic9ParamBean.setQuarters(Arrays.asList(timeFilter.getQuarter()));
        } else if ("half".equals(timeFilter.getDateType())) {
            if ("H1".equalsIgnoreCase(timeFilter.getHalf())) {
                domestic9ParamBean.setQuarters(Arrays.asList("Q1", "Q2"));
            } else {
                domestic9ParamBean.setQuarters(Arrays.asList("Q3", "Q4"));
            }
        }
        if ("大区".equals(drillDownFilter.getField())) {//NOSONAR
            domestic9ParamBean.setRegionNames(currenParam);
            domestic9ParamBean.setDateType("0");
        }else if ("省份".equals(drillDownFilter.getField())) {//NOSONAR
            domestic9ParamBean.setProvinceNames(currenParam);
            domestic9ParamBean.setDateType("1");
        }
        return domestic9ParamBean;
    }

    @Override
    public GetDomesticDrillDownBaseInfoResponseType getSingleDrillDownBaseInfo(GetDomesticDrillDownBaseInfoRequestType request, MetricInfoBean metricInfoBean, String d) throws Exception {
        GetDomesticDrillDownBaseInfoResponseType response = new GetDomesticDrillDownBaseInfoResponseType();
        List<FieldDataItem> fieldDataItemList = new ArrayList<>();
        response.setFieldDataItemList(fieldDataItemList);

        TimeFilter timeFilter = request.getTimeFilter();
        String year = timeFilter.getYear();

        String metric = metricInfoBean.getMetric();
        String level = metricInfoBean.getLevel();//国内  三方  大区  省份  景点
        List<String> fieldList = Bus9Helper.getFieldList(level);

        boolean needSearch = StringUtils.isNotEmpty(request.getSearchWord());

        if (needSearch) {
            String searchField = request.getSearchField();//大区 省份
            if (searchField != null) {
                String searchFieldColumnName = MetricHelper.getDrillDownColumnName(searchField);
                fieldList = Lists.newArrayList(searchFieldColumnName);
            }
        } else {
            response.setDefaultChosenField(MetricHelper.getDefaultChosenField(level, metric));
        }


        for (String field : fieldList) {
            String name = MetricHelper.getDataBaseColumnName(field);
            Domestic9Param param = getDomestic9TableParam(d,null,null, year, request.getTimeFilter(), level,
                    metricInfoBean.getRegionList(), name, null);

            List<String> drillDataList = admPrdTtdCategoryCoverInfoDao.getDomesticDrillDateByField(param);
            FieldDataItem item = new FieldDataItem();
            fieldDataItemList.add(item);
            item.setField(name);
            List<FieldValueItem> fieldValueItemList = new ArrayList<>();
            item.setFieldValueItemList(fieldValueItemList);
            for (String rowResult : drillDataList) {
                FieldValueItem fieldValueItem = new FieldValueItem();
                fieldValueItem.setValue(rowResult);
                fieldValueItemList.add(fieldValueItem);
            }
        }
        return response;
    }

    private Domestic9Param getDomestic9TableParam(String d,Integer pageIndex,Integer pageSize, String year, TimeFilter timeFilter,
                                                  String originLevel, List<String> regionList,
                                                  String field, List<String> fieldValueList) {
        Domestic9Param param=new Domestic9Param();

        param.setD(d);
        param.setPageIndex(pageIndex);
        param.setPageSize(pageSize);
        param.setYear(year);
        String dateType = timeFilter.getDateType();
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();
        if ("month".equals(dateType)) {
            param.setMonth(month);
            param.setDateType("month");
        } else if("quarter".equalsIgnoreCase(dateType)){
            param.setQuarter(Arrays.asList(quarter));
            param.setDateType("quarter");
        }else if("half".equalsIgnoreCase(dateType)){
            param.setDateType("quarter");
            if ("H1".equalsIgnoreCase(timeFilter.getHalf())) {
                param.setQuarter(Arrays.asList("Q1", "Q2"));
            } else {
                param.setQuarter(Arrays.asList("Q3", "Q4"));
            }
        }
        //考核范围+考核层级 String originLevel, List<String> regionList,
        if ("大区".equalsIgnoreCase(originLevel)) {//NOSONAR
            param.setBusinessRegionName(regionList);
        } else if ("省份".equalsIgnoreCase(originLevel)) {//NOSONAR
            param.setProvinceName(regionList);
        }
        param.setField(field);
        if(CollectionUtils.isNotEmpty(fieldValueList)){
            if ("大区".equalsIgnoreCase(field)) {//NOSONAR
                param.setBusinessRegionName(fieldValueList);
            } else if ("省份".equalsIgnoreCase(field)) {//NOSONAR
                param.setProvinceName(fieldValueList);
            }
        }
        return param;
    }

    @Override
    public GetFirstPageDomesticMetricCardDrillDataResponseType getFirstPageDomesticMetricCardDrillData(
            GetFirstPageDomesticMetricCardDrillDataRequestType request,MetricInfoBean metricInfoBean, String d) throws Exception{
        GetFirstPageDomesticMetricCardDrillDataResponseType response = new GetFirstPageDomesticMetricCardDrillDataResponseType();
        List<FirstPageDomesticTableData> tableDataItemList = new ArrayList<>();
        response.setTableDataItemList(tableDataItemList);
        TimeFilter timeFilter = request.getTimeFilter();
        String year = timeFilter.getYear();
        DrillDownFilter drillDownFilter = new DrillDownFilter();
        drillDownFilter.setField(request.getDefaultField());

        String field = MetricHelper.getDrillDownColumnName(drillDownFilter.getField());
        response.setTableHeaderList(getFirstTableHeaderList(field));
        Domestic9Param param = getDomestic9TableParam(d,null,null, year, request.getTimeFilter(), metricInfoBean.getLevel(),
                metricInfoBean.getRegionList(), drillDownFilter.getField(), drillDownFilter.getFieldValueList());
        List<AdmPrdTtdCategoryCoverInfoBO> currentResult = admPrdTtdCategoryCoverInfoDao.getDomesticTableDateByField(param);

        //target
        List<String> currentParam = null;
        if ("大区".equalsIgnoreCase(drillDownFilter.getField())) {//NOSONAR
            currentParam = currentResult.stream().map(x -> x.getBusinessRegionName()).collect(Collectors.toList());
        } else if ("省份".equalsIgnoreCase(drillDownFilter.getField())) {//NOSONAR
            currentParam = currentResult.stream().map(x -> x.getProvinceName()).collect(Collectors.toList());
        }
        Domestic9ParamBean targetParam = getDomestic9TargetParam(request.getTimeFilter(), drillDownFilter, d, currentParam);
        targetParam.setField(request.getDefaultField());
        List<Domestic9TargetBO> targetResult = boardCategoryCoverTargetDao.getDomestic9TargetValue(targetParam);
        Map<String, Double> targetValueMap = new HashMap<>();
        for (Domestic9TargetBO bo : targetResult) {
            String key = String.format("%s:%s:%s", bo.getRegionNames(), bo.getProvinceNames(), bo.getCategoryExamineMode());
            targetValueMap.put(key, bo.getTarget());
        }
        //- 现状覆盖率=已覆盖且在营业中的特色体验数/营业中特色体验数
        //- 完成率=重点品类现状覆盖率/目标覆盖率*25%+特色品类现状覆盖率/目标覆盖率*75%
        //- 目标覆盖率为目标值
        List<DomesticTableData> tableOldItemList = new ArrayList<>();
        build9TableData(tableOldItemList,currentResult,targetValueMap);
        Map<String, List<DomesticTableData>> tableMap = new HashMap<>();
        for (DomesticTableData data : tableOldItemList) {
            String key = String.format("%s:%s", data.getRegionName(), data.getProvinceName());
            if (tableMap.containsKey(key)) {
                tableMap.get(key).add(data);
            } else {
                tableMap.put(key, new ArrayList<>());
                tableMap.get(key).add(data);
            }
        }
        for (Map.Entry<String, List<DomesticTableData>> entry : tableMap.entrySet()) {
            DomesticTableData row1 = entry.getValue().get(0);
            //- 完成率=重点品类现状覆盖率/目标覆盖率*25%+特色品类现状覆盖率/目标覆盖率*75%
            Double complete1 = row1.getCategoryCurrentCoverageRate() / row1.getTargetRate() * Double.valueOf(row1.getCategoryWeight());
            DomesticTableData row2 = entry.getValue().get(1);
            Double complete2 = row2.getCategoryCurrentCoverageRate() / row2.getTargetRate() * Double.valueOf(row2.getCategoryWeight());
            Double complete=complete1+complete2;
            FirstPageDomesticTableData item = new FirstPageDomesticTableData();
            switch (request.getDefaultField()) {
                case "大区"://NOSONAR
                    item.setRegionName(row1.getRegionName());
                    break;
                case "省份"://NOSONAR
                    item.setRegionName(row1.getRegionName());
                    item.setProvinceName(row1.getProvinceName());
                    break;
            }
            item.setCompleteRate(complete);
            tableDataItemList.add(item);
        }
        return response;
    }
    @Override
    public Integer getMetricName() {
        return 9;
    }
}
