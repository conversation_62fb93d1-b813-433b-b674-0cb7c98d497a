include 'BaijiCommonTypes.bjsc'
namespace java 'com.ctrip.soa._24922'

class GetUserDimDetailInfoRequestType{
    //开始日期
    string startDate;

    //结束日期
    string endDate;

    //景点id
    long sightId;

    //日期类型:1-使用日期、2-预订日期(只影响业绩表现、用户分析、履约质量)
    int dateType;

    //true-需要计算子景点/false-无需计算子景点
    bool needSubSight;

    //业务类型:1-全部(默认)、2-门票、3-活动(只影响业绩表现、服务质量)
    int businessType;

    //供应商id
    list<long> vendorIdList;
}

class GetUserDimDetailInfoResponseType {
    BaijiCommonTypes.ResponseStatusType responseStatus;
    list<BookingAdvanceDayInfo> bookingAdvanceDayList;
    list<UserAgeRangeInfo> userAgeRangeList;
    list<UserLevelInfo> userLevelList;
    list<UserCategoryInfo> userCategoryList;
    list<UserCityInfo> userCityList;
    list<ForeignAnddomesticUserInfo> foreignAnddomesticUserInfoList;

    //用户搜索偏好（按照数量排序取前20）
    list<UserSearchPreferenceSearchKeyForUserDimDetailInfo> searchKeyList;
}

class BookingAdvanceDayInfo {
    string bookingDayName;
    long value;
}
class UserAgeRangeInfo {
    string ageRangeName;
    long value;
}
class UserLevelInfo {
    string levelName;
    long value;
}
class UserCategoryInfo {
    string categoryName;
    long value;
}
class UserCityInfo {
    string cityName;
    double value;
}

class ForeignAnddomesticUserInfo {
    string permanentDomain;
    long value;
}

class UserSearchPreferenceSearchKeyForUserDimDetailInfo{
    //搜索关键词
    string searchKey;
    //次数
    int count;
}

class GetServiceQualityDimDetailInfoRequestType{
    //开始日期
    string startDate;

    //结束日期
    string endDate;

    //景点id
    long sightId;

    //日期类型:1-使用日期、2-预订日期(只影响业绩表现、用户分析、履约质量)
    int dateType;

    //true-需要计算子景点/false-无需计算子景点
    bool needSubSight;

    //业务类型:1-全部(默认)、2-门票、3-活动(只影响业绩表现、服务质量)
    int businessType;

    //供应商id
    list<long> vendorIdList;
}

class GetServiceQualityDimDetailInfoResponseType {
    BaijiCommonTypes.ResponseStatusType responseStatus;
    KeepAgreementDetailInfo keepAgreementDetailInfo;
    ReviewDetailInfo reviewDetailInfo;
    ServiceDetailInfo serviceDetail;
    ComplainDetail complainDetail;

    //淡旺季趋势
    SeasonalDemandTrendsForServiceQualityDimDetailInfo seasonalDemandTrends;

    //所在地热度预测
    list<LocationHeatForecastTrendLineItemForServiceQualityDimDetailInfo> marketTrends;
}

class KeepAgreementDetailInfo {
  double  failedOrderWeightedCount;
  double failedOrderWeightedRate;
  list<AgreementDefectReasonInfo> agreementDefectReasonList;
}

class AgreementDefectReasonInfo {
    string keyWord;
    double value;
}

class ReviewDetailInfo {
  long  reviewNum;
  double  avgReviewScore;
  list<RelReviewKeyWordInfo> reviewKeyWordList;
  list<ReviewKeyWordInfo> reviewKeyWordWithLikeList;
  list<ReviewKeyWordInfo> reviewKeyWordWithDislikeList;
}

class RelReviewKeyWordInfo {
    string keyWord;
    double value;
}

class ReviewKeyWordInfo {
    string keyWord;
    long value;
}

class ServiceDetailInfo {
  long  bookingBeforeIMServiceNum;
  long  bookingAfterIMServiceNum;
  long phoneServiceMinute
  double  costPrice;

}

class ComplainDetail {
   long complainNumber;
   list<ComplainDetailInfo> complainDetailList;
}

class ComplainDetailInfo {
    string keyWord;
    long value;
}

class SeasonalDemandTrendsForServiceQualityDimDetailInfo{
    //趋势线
    list<SightArchivesFlowMetricTrendLineItemForServiceQualityDimDetailInfo> dateList;
    //淡季月份
    list<int> offPeakMonthList;
    //旺季月份
    list<int> peakMonthList;
}

class SightArchivesFlowMetricTrendLineItemForServiceQualityDimDetailInfo{
    string date;
    int uv;
    double conversionRate;
}


class LocationHeatForecastTrendLineItemForServiceQualityDimDetailInfo{
    //日期
    string date;
    //本城市热度
    double cityHeat;
    //本城市热度同比
    double cityHeatYoy;
    //机票热度
    double airplaneTicketHeat;
    //火车票热度
    double trainTicketHeat;
    //酒店热度
    double hotelTicketHeat;
}

@serviceName='BIBusinessDashboardService'
@serviceNamespace='http://soa.ctrip.com/_24922'
service BIBusinessDashboardService {
    //获取用户维度数据
    GetUserDimDetailInfoResponseType getUserDimDetailInfo(GetUserDimDetailInfoRequestType request);
    //获取服务质量数据
    GetServiceQualityDimDetailInfoResponseType getServiceQualityDimDetailInfo(GetServiceQualityDimDetailInfoRequestType request);

}