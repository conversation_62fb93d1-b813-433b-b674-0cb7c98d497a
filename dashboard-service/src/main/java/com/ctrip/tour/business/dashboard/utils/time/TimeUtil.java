package com.ctrip.tour.business.dashboard.utils.time;

import com.ctrip.soa._24922.TimeFilter;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.InputArgumentException;

/**
 * 时间工具类，统一处理TimeFilter相关的日期范围计算
 */
public class TimeUtil {

    /**
     * 根据TimeFilter和窗口日期获取同比日期范围（带时间窗口支持）
     *
     * @param timeFilter 时间过滤器，包含日期类型、年份、季度等信息
     * @param windowDate 窗口日期，格式：yyyy-MM-dd
     * @return 同比日期范围（开始日期和结束日期）
     */
    public static DateRange getYoYDateRangeWithWindow(TimeFilter timeFilter, String windowDate) {
        String dateType = timeFilter.getDateType();
        // 年份处理：去除首尾空格
        String year = timeFilter.getYear().trim();

        switch (dateType) {
            case "month":
                // 月份处理：去除首尾空格 + 单数字补零（如"3"→"03"）
                String month = timeFilter.getMonth().trim();
                String formattedMonth = month.length() == 1 ? "0" + month : month;
                MonthCalculator monthCalc = MonthCalculator.getByMonthString(year + "-" + formattedMonth);
                return monthCalc.getYoYDateRangeWithWindow(windowDate);

            case "quarter":
                String quarter = timeFilter.getQuarter().trim();
                // 动态检查Q/q前缀，避免重复添加
                String formattedQuarter = quarter.contains("Q") || quarter.contains("q") ? quarter : "Q" + quarter;
                QuarterCalculator quarterCalc = QuarterCalculator.getByQuarterString(year + formattedQuarter);
                return quarterCalc.getYoYDateRangeWithWindow(windowDate);

            case "half":
                String halfYear = timeFilter.getHalf().trim();
                // 动态检查H/h前缀，避免重复添加
                String formattedHalfYear = halfYear.contains("H") || halfYear.contains("h") ? halfYear : "H" + halfYear;
                HalfYearCalculator halfYearCalc = HalfYearCalculator.getByHalfYearString(year + formattedHalfYear);
                return halfYearCalc.getYoYDateRangeWithWindow(windowDate);

            case "year":
                // 年份已在上方统一trim处理
                YearCalculator yearCalc = YearCalculator.getByYearString(year);
                return yearCalc.getYoYDateRangeWithWindow(windowDate);

            default:
                throw new InputArgumentException("Invalid date type: " + dateType);
        }
    }

    /**
     * 根据TimeFilter获取当前周期的日期范围
     *
     * @param timeFilter 时间过滤器
     * @return 当前周期的日期范围
     */
    public static DateRange getCurrentDateRange(TimeFilter timeFilter) {
        String dateType = timeFilter.getDateType();
        // 年份处理：去除首尾空格
        String year = timeFilter.getYear().trim();

        switch (dateType) {
            case "month":
                // 月份处理：去除首尾空格 + 单数字补零（如"3"→"03"）
                String month = timeFilter.getMonth().trim();
                String formattedMonth = month.length() == 1 ? "0" + month : month;
                MonthCalculator monthCalc = MonthCalculator.getByMonthString(year + "-" + formattedMonth);
                return new DateRange(monthCalc.getDateStart(), monthCalc.getDateEnd());

            case "quarter":
                String quarter = timeFilter.getQuarter().trim();
                // 动态检查Q/q前缀，避免重复添加
                String formattedQuarter = quarter.contains("Q") || quarter.contains("q") ? quarter : "Q" + quarter;
                QuarterCalculator quarterCalc = QuarterCalculator.getByQuarterString(year + formattedQuarter);
                return new DateRange(quarterCalc.getDateStart(), quarterCalc.getDateEnd());

            case "half":
                String halfYear = timeFilter.getHalf().trim();
                // 动态检查H/h前缀，避免重复添加
                String formattedHalfYear = halfYear.contains("H") || halfYear.contains("h") ? halfYear : "H" + halfYear;
                HalfYearCalculator halfYearCalc = HalfYearCalculator.getByHalfYearString(year + formattedHalfYear);
                return new DateRange(halfYearCalc.getDateStart(), halfYearCalc.getDateEnd());

            case "year":
                // 年份已在上方统一trim处理
                YearCalculator yearCalc = YearCalculator.getByYearString(year);
                return new DateRange(yearCalc.getDateStart(), yearCalc.getDateEnd());

            default:
                throw new InputArgumentException("Invalid date type: " + dateType);
        }
    }
}
