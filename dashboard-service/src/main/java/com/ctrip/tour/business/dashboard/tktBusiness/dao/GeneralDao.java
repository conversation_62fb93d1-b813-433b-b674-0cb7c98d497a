package com.ctrip.tour.business.dashboard.tktBusiness.dao;

import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.DalQueryDao;
import com.ctrip.platform.dal.dao.StatementParameters;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.SwitchNewTableHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/29
 */
@Repository
public class GeneralDao {

    private DalQueryDao dalQueryDao;

    private static final String DATA_BASE = "TtdReportDB_W";

    @Autowired
    private SwitchNewTableHelper switchNewTableHelper;

    public GeneralDao() {
        dalQueryDao = new DalQueryDao(DATA_BASE);
    }


    public List<List<Object>> getListResult(String sql,
                                            StatementParameters parameters) throws SQLException {
        sql = switchNewTableHelper.switchLocalDatabase(sql);
        List<List<Object>> rsList =
                dalQueryDao.query(sql, parameters, new DalHints(), (rs, rowNum) -> {
                    ResultSetMetaData meta = rs.getMetaData();
                    List<Object> l = new ArrayList<>();
                    for (int i = 1; i <= meta.getColumnCount(); i++) {
                        l.add(rs.getObject(i));
                    }
                    return l;
                });
        return rsList;
    }


    public void getListResultAsync(String sql,
                                   StatementParameters parameters,
                                   DalHints dalHints) throws SQLException {
        sql = switchNewTableHelper.switchLocalDatabase(sql);
        dalQueryDao.query(sql, parameters, dalHints, (rs, rowNum) -> {
            ResultSetMetaData meta = rs.getMetaData();
            List<Object> l = new ArrayList<>();
            for (int i = 1; i <= meta.getColumnCount(); i++) {
                l.add(rs.getObject(i));
            }
            return l;
        });
    }
}
