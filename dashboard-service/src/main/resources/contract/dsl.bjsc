include 'BaijiCommonTypes.bjsc'
namespace java 'com.ctrip.soa._24922'

// dsl request
class CompareConfig {
    string compareIndicatorName;
    list<string> compareDate;
    string prefix;
    list<string> curDate;
}

enum EnumOperators {
    IN,
    LT,
    LE,
    EQ,
    NE,
    GE,
    GT,
    BETWEEN
}

class Limit {
    int start;
    int size;
}

class OrderBy {
    string orderFiled;
    string orderType;
}


enum WhereConditionOperator {
    OR,
    AND
}

class WhereCondition {
    string filterName;
    EnumOperators operators;
    list<string> filterValues;
    WhereConditionOperator whereConditionOperator; // and/or
    list<WhereCondition> subWhereConditions;
}

class DSLRequestType {
    // 选择的指标
    list<string> indicators;
    // where条件
    WhereCondition whereCondition;
    // 聚合
    list<string> groupBy;
    // 排序
    list<OrderBy> orderBy;
    // limit
    Limit limit;
    // 同环比
    list<CompareConfig> compareConfig;
    // extra
    map<string, string> extra;
}


// dsl response
enum IndicatorType {
    Double,
    Float,
    Int,
    Long,
    String,
    Bool
}

class Meta {
    string indicatorName; // 指标英文名
    string indicatorNameCN; // 指标中文名
    IndicatorType indicatorType; // 指标类型
    string indicatorFormat; // 指标格式化
    bool isDimension; // 是否纬度
    bool isSupportSorting; // 是否支持排序

    // 平台兼容
    string indicatorKey; // 指标key
    string indicatorKeyName; // 指标tips
}

class Data {
    map<string,string> data;
}

class DSLResponseType {
    string uid;
    list<Data> data;
    list<Meta> meta;
}