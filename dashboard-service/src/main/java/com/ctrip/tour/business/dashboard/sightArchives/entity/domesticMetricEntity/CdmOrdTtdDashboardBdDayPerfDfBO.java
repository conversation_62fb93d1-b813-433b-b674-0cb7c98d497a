package com.ctrip.tour.business.dashboard.sightArchives.entity.domesticMetricEntity;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;

@Entity
@Getter
@Setter
public class CdmOrdTtdDashboardBdDayPerfDfBO {
    //使用日期
    String domain_name;
    String role;
    //使用日期
    String buType;
    //使用月份
    String useDate;
    //使用季度
    String useMonth;
    //使用半年
    String useQuarter;
    //使用年份
    String useHalfYear;
    String useYear;
    //考核层级
    String examineLevel;
    //考核范围
    String examineRange;
    //订单收入
    String ttdSucIncome;
    //系统内毛利
    String ttd_sys_inner_profit;
    //系统外毛利
    String ttd_sys_outer_profit;
    //订单毛利
    String ttd_suc_profit;
}
