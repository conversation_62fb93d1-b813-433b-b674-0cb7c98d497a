package com.ctrip.tour.business.dashboard.grpBusiness.handler.metric;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import com.ctrip.soa._24922.ExtraValues;
import com.ctrip.soa._24922.GetGrpMetricDataRequestType;
import com.ctrip.soa._24922.GrpMetric;
import com.ctrip.soa._24922.GrpTrendLinePoint;
import com.ctrip.tour.business.dashboard.grpBusiness.annotation.MetricData;
import com.ctrip.tour.business.dashboard.grpBusiness.enums.MetricEnum;
import com.ctrip.tour.business.dashboard.grpBusiness.enums.TimeAggTypeEnum;
import com.ctrip.tour.business.dashboard.grpBusiness.handler.dim.DimconvertHandler;
import com.ctrip.tour.business.dashboard.grpBusiness.metrics.IndexCommonQueryAbstractSerice;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2024/12/12
 */
@Service
@Slf4j
public abstract class GrpBussinessAbstractMetricService implements IGrpBusinessMetricService {

    private static final String RESULT_VALUE = "result_value";

    private final ApplicationContext ac;

    @Autowired
    public GrpBussinessAbstractMetricService(ApplicationContext ac) {
        this.ac = ac;
    }

    @Override
    public GrpMetric queryMetricValue(GetGrpMetricDataRequestType requestType, MetricEnum metricEnum,
                                      IGrpBusinessMetricService service, int bizMode) {

        MetricData annotation = service.getClass().getAnnotation(MetricData.class);
        boolean ratio = annotation.isRatio();
        boolean needMonthOveMonth = annotation.needMonthOveMonth();
        boolean needWeekOverWeek = annotation.needWeekOverWeek();
        boolean needYearOverYear = annotation.needYearOverYear();
        GrpMetric metric = new GrpMetric();
        metric.setIsRatioValue(ratio);
        Map<String, Object> param = request2Param(requestType, bizMode);
        if (param != null) {
            param.remove("is_slave_flag");
        }

        CompletableFuture<List<Map<String, Object>>> currentFuture = CompletableFuture.supplyAsync(() -> {
            try {
                return queryMetricCardData(param, requestType);
            } catch (Exception e) {
                log.warn("getGrpMetricCard error ", e);
            }
            return null;

        });

        CompletableFuture<List<Map<String, Object>>> lastMonthFuture = null;
        if (needMonthOveMonth) {
            lastMonthFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    HashMap<String, Object> map = Maps.newHashMap();
                    map.putAll(param);
                    Map<String, Object> handledParam = replaceQueryDate4Month(map, requestType.getStartDate(), requestType.getEndDate());
                    return queryMetricCardData(handledParam, requestType);
                } catch (Exception e) {
                    log.warn("getGrpMetricCard error ", e);
                }
                return null;

            });
        }

        CompletableFuture<List<Map<String, Object>>> lastWeekFuture = null;
        if (needWeekOverWeek) {
            lastWeekFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    HashMap<String, Object> map = Maps.newHashMap();
                    map.putAll(param);
                    Map<String, Object> handledParam = replaceQueryDate4Week(map, requestType.getStartDate(), requestType.getEndDate());
                    return queryMetricCardData(handledParam, requestType);
                } catch (Exception e) {
                    log.warn("getGrpMetricCard error ", e);
                }
                return null;

            });
        }

        CompletableFuture<List<Map<String, Object>>> lastYearFuture = null;
        if (needYearOverYear) {
            lastYearFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    HashMap<String, Object> map = Maps.newHashMap();
                    map.putAll(param);
                    Map<String, Object> handledParam = replaceQueryDate4Year(map, requestType.getStartDate(), requestType.getEndDate());
                    return queryMetricCardData(handledParam, requestType);
                } catch (Exception e) {
                    log.warn("getGrpMetricCard error ", e);
                }
                return null;

            });
        }

        try {
            metric.setMetricName(metricEnum.getEnglishName());
            metric.setMetricTipsSharkKey(metricEnum.getMetricTipsSharkKey());
            List<Map<String, Object>> maps = currentFuture.get();
            if (CollectionUtils.isEmpty(maps)) {
                return metric;
            }
            Map<String, Object> currentResultMap = maps.get(0);
            Object o = currentResultMap.get(RESULT_VALUE);
            if (Objects.isNull(o)) {
                return metric;
            }
            BigDecimal currentResult = null;
            if (o instanceof Double) {
                currentResult = BigDecimal.valueOf((Double) o);
            } else if (o instanceof Long) {
                currentResult = BigDecimal.valueOf((Long) o);
            } else {
                currentResult = (BigDecimal) o;
            }

            metric.setMetricValue(currentResult.doubleValue());
            ExtraValues extraValues = new ExtraValues();
            if (needMonthOveMonth) {
                Map<String, Object> lastMonthResultMap = lastMonthFuture.get().get(0);
                Object mo = lastMonthResultMap.get(RESULT_VALUE);
                BigDecimal lastMonthResult = null;
                if (mo instanceof Double) {
                    lastMonthResult = BigDecimal.valueOf((Double) mo);
                } else if (mo instanceof Long) {
                    lastMonthResult = BigDecimal.valueOf((Long) mo);
                } else {
                    lastMonthResult = (BigDecimal) mo;
                }
                BigDecimal mom = calcRatio(lastMonthResult, currentResult);
                extraValues.setMonthOverMonth(Objects.isNull(mom) ? null : mom.doubleValue());
            }
            if (needWeekOverWeek) {
                Map<String, Object> lastWeekResultMap = lastWeekFuture.get().get(0);
                Object wo = lastWeekResultMap.get(RESULT_VALUE);
                BigDecimal lastWeekResult = null;
                if (wo instanceof Double) {
                    lastWeekResult = BigDecimal.valueOf((Double) wo);
                } else if (wo instanceof Long) {
                    lastWeekResult = BigDecimal.valueOf((Long) wo);
                } else {
                    lastWeekResult = (BigDecimal) wo;
                }
                BigDecimal wow = calcRatio(lastWeekResult, currentResult);
                extraValues.setWeekOverWeek(Objects.isNull(wow) ? null : wow.doubleValue());
            }

            if (needYearOverYear) {
                Map<String, Object> lastYearResultMap = lastYearFuture.get().get(0);
                Object yo = lastYearResultMap.get(RESULT_VALUE);
                BigDecimal lastYearResult = null;
                if (yo instanceof Double) {
                    lastYearResult = BigDecimal.valueOf((Double) yo);
                } else if (yo instanceof Long) {
                    lastYearResult = BigDecimal.valueOf((Long) yo);
                } else {
                    lastYearResult = (BigDecimal) yo;
                }
                BigDecimal wow = calcRatio(lastYearResult, currentResult);
                extraValues.setYearOverYear(Objects.isNull(wow) ? null : wow.doubleValue());
            }

            metric.setExtraValues(extraValues);
//                metric.setIsRatioValue(true);

        } catch (Exception e) {
            log.warn("handle futres error", e);
        }


        return metric;

    }


    @Override
    public List<GrpTrendLinePoint> queryMetricTrendLineP(GetGrpMetricDataRequestType requestType, MetricEnum metricEnum,
                                                         IGrpBusinessMetricService service, int bizMode) {

        Map<String, Object> param = request2Param(requestType, bizMode);

        MetricData annotation = service.getClass().getAnnotation(MetricData.class);
        boolean ratio = annotation.isRatio();
        boolean needYearOverYear = annotation.needYearOverYear();

        String aggregationGranularity = requestType.getAggregationGranularity();

        CompletableFuture<Map<String, Object>> currentFuture = CompletableFuture.supplyAsync(() -> {
            try {
                return queryMetricTrendLine(param, requestType);
            } catch (Exception e) {
                log.warn("getGrpMetricCard error ", e);
            }
            return null;

        });

//        CompletableFuture<Map<String, Object>> lastMonthFuture = null;
//        if (needMonthOveMonth) {
//            lastMonthFuture = CompletableFuture.supplyAsync(() -> {
//                try {
//                    Map<String, Object> handledParam = replaceQueryDate4Month(param, requestType.getStartDate(), requestType.getEndDate());
//                    return queryMetricTrendLine(handledParam, requestType);
//                } catch (Exception e) {
//                    log.warn("getGrpMetricCard error ", e);
//                }
//                return null;
//
//            });
//        }

        CompletableFuture<Map<String, Object>> lastYearFuture = null;
        if (needYearOverYear) {
            lastYearFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    Map<String, Object> map = Maps.newHashMap();
                    map.putAll(param);
                    Map<String, Object> handledParam = replaceQueryDate4Year(map, requestType.getStartDate(), requestType.getEndDate());
                    return queryMetricTrendLine(handledParam, requestType);
                } catch (Exception e) {
                    log.warn("getGrpMetricCard error ", e);
                }
                return null;

            });
        }

        List<GrpTrendLinePoint> linePoints = Lists.newArrayList();
        try {
            Map<String, Object> currentResultMap = currentFuture.get();

            CompletableFuture<Map<String, Object>> finalLastYearFuture = lastYearFuture;
            linePoints.addAll(currentResultMap.entrySet()
                    .stream()
                    .map(entry -> {
                        String key = entry.getKey();
                        GrpTrendLinePoint linePoint = new GrpTrendLinePoint();
                        if (StringUtils.equals(TimeAggTypeEnum.MONTH.getName(), aggregationGranularity)) {
                            String s = key.split("~")[0];
                            if (s.length() > 7) {
                                key = s.substring(0, s.lastIndexOf("-"));
                            } else {
                                key = s;
                            }

                        }
                        linePoint.setDate(key);
                        GrpMetric metric = new GrpMetric();

                        if (Objects.nonNull(entry.getValue()) && !(entry.getValue() instanceof String)) {
                            BigDecimal value = (BigDecimal) entry.getValue();
                            metric.setMetricValue(value.doubleValue());
                            ExtraValues extraValues = new ExtraValues();
                            if (needYearOverYear) {
                                Map<String, Object> lastYearResultMap = null;
                                try {
                                    lastYearResultMap = finalLastYearFuture.get();
                                } catch (Exception e) {
                                    log.warn("query trendLastMonth error", e);
                                }
                                if (Objects.nonNull(lastYearResultMap)) {
                                    String lastYearDate = "";
                                    if (StringUtils.equals(aggregationGranularity, TimeAggTypeEnum.DAY.getName())) {
                                        lastYearDate = calcOverYearDate(entry.getKey());
                                    } else {
                                        lastYearDate = getLastWeekDateRangeString(entry.getKey(), 365, aggregationGranularity);
                                    }

                                    Object lastMonthResult = lastYearResultMap.get(lastYearDate);
                                    if (Objects.nonNull(lastMonthResult) && !(lastMonthResult instanceof String)) {
                                        BigDecimal mOverm = calcRatio((BigDecimal) lastMonthResult, value);
                                        extraValues.setYearOverYear(Objects.isNull(mOverm) ? null : mOverm.doubleValue());
                                    }
                                }

                            }
                            metric.setExtraValues(extraValues);
                        }
                        metric.setIsRatioValue(metricEnum.isRatio());
                        metric.setMetricName(metricEnum.getEnglishName());
                        metric.setMetricTipsSharkKey(metricEnum.getMetricTipsSharkKey());
                        linePoint.setTrendLinePoints(Lists.newArrayList(metric));
                        return linePoint;
                    }).collect(Collectors.toList()));

        } catch (Exception e) {
            log.warn("handle futres error", e);
        }

        return linePoints;
    }

    protected String calcOverYearDate(String dateRangeStr) {
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate dateRange = LocalDate.parse(dateRangeStr, dtf).minusYears(1);
        return dtf.format(dateRange);
    }

    private String getLastWeekDateRangeString(String dataRange, int offset, String timeAggType) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        if (StringUtils.equals(timeAggType, TimeAggTypeEnum.DAY.getName())) {
            LocalDate parse = LocalDate.parse(dataRange, dateTimeFormatter);
            return dateTimeFormatter.format(parse.minusDays(offset));
        } else if (StringUtils.equals(timeAggType, TimeAggTypeEnum.WEEK.getName()) ||
                StringUtils.equals(timeAggType, TimeAggTypeEnum.MONTH.getName())) {

            String[] split = dataRange.split("~");
            LocalDate startDate = LocalDate.parse(split[0], dateTimeFormatter);
            LocalDate endDate = LocalDate.parse(split[1], dateTimeFormatter);
            return String.join("~", dateTimeFormatter.format(startDate.minusDays(offset)), dateTimeFormatter.format(endDate.minusDays(offset)));
        }
        return dataRange;
    }

    @Override
    public Map<String, GrpMetric> queryMetricDillDownResult(GetGrpMetricDataRequestType requestType, MetricEnum metricEnum,
                                                            int bizModel, IGrpBusinessMetricService service) {

        Map<String, Object> param = request2Param(requestType, bizModel);

        MetricData annotation = service.getClass().getAnnotation(MetricData.class);
        boolean needYearOverYear = annotation.needYearOverYear();
        Map<String, GrpMetric> results = Maps.newHashMap();

        CompletableFuture<List<Map<String, Object>>> currentFuture = CompletableFuture.supplyAsync(() -> {
            try {
                return queryMetricDillDown(param, requestType);
            } catch (Exception e) {
                log.warn("getGrpMetricCard error ", e);
            }
            return null;

        });

        CompletableFuture<List<Map<String, Object>>> lastYearFuture = null;
        if (needYearOverYear) {
            lastYearFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    Map<String, Object> map = Maps.newHashMap();
                    map.putAll(param);
                    Map<String, Object> handledParam = replaceQueryDate4Year(map, requestType.getStartDate(), requestType.getEndDate());
                    return queryMetricDillDown(handledParam, requestType);
                } catch (Exception e) {
                    log.warn("getGrpMetricCard error ", e);
                }
                return null;

            });
        }


        String dimEnumName = DimconvertHandler.convertDimEnumName(requestType.getDrillDownDim(), requestType.getMetricCategorieName());

        try {
            List<Map<String, Object>> currentRst = currentFuture.get();

            if (CollectionUtils.isEmpty(currentRst)) {
                return results;
            }

            Map<String, Map<String, Object>> groupByDillDownDimMap = currentRst.stream().collect(
                    Collectors.toMap(map -> String.valueOf(map.get(dimEnumName)), Function.identity()));


            Map<String, Map<String, Object>> groupByMonthDDDMap = Maps.newHashMap();
            if (needYearOverYear) {
                List<Map<String, Object>> yearOverRst = lastYearFuture.get();
                if (CollectionUtils.isNotEmpty(yearOverRst)) {

                    groupByMonthDDDMap = yearOverRst.stream().collect(Collectors
                            .toMap(map -> String.valueOf(map.get(dimEnumName)), Function.identity()));

                }
            }


            Map<String, Map<String, Object>> finalGroupByMonthDDDMap = groupByMonthDDDMap;
            results.putAll(groupByDillDownDimMap.entrySet()
                    .stream()
                    .collect(Collectors.toMap(Map.Entry::getKey, entry -> {
                        GrpMetric metric = new GrpMetric();
                        Map<String, Object> rowData = entry.getValue();
//                            BigDecimal metricValue = (BigDecimal)rowData.get(RESULT_VALUE);

                        Object o = rowData.get(RESULT_VALUE);
                        BigDecimal metricValue = null;
                        if (o instanceof Double) {
                            metricValue = BigDecimal.valueOf((Double) o);
                        } else if (o instanceof Long) {
                            metricValue = BigDecimal.valueOf((Long) o);
                        } else {
                            metricValue = (BigDecimal) o;
                        }

                        ExtraValues extraValues = new ExtraValues();
                        if (needYearOverYear) {
                            Map<String, Object> overMonthRd = finalGroupByMonthDDDMap.get(entry.getKey());
                            if (MapUtils.isNotEmpty(overMonthRd)) {
                                //BigDecimal overMonthVal = (BigDecimal)overMonthRd.get(RESULT_VALUE);
                                Object monthO = overMonthRd.get(RESULT_VALUE);
                                BigDecimal overMonthVal = null;
                                if (monthO instanceof Double) {
                                    overMonthVal = BigDecimal.valueOf((Double) monthO);
                                } else if (monthO instanceof Long) {
                                    overMonthVal = BigDecimal.valueOf((Long) monthO);
                                } else {
                                    overMonthVal = (BigDecimal) monthO;
                                }
                                if (Objects.nonNull(overMonthVal)) {
                                    BigDecimal mom = calcRatio(overMonthVal, metricValue);
                                    extraValues.setYearOverYear(Objects.isNull(mom) ? null : mom.doubleValue());
                                }

                            }
                        }
//                            if (needWeekOverWeek) {
//                                Map<String, Object> overWeekRd = finalGroupByWeekDDDMap.get(entry.getKey());
//                                if (MapUtils.isNotEmpty(overWeekRd)) {
//                                    //BigDecimal overWeekVal = (BigDecimal)overWeekRd.get(RESULT_VALUE);
//                                    Object weekO = overWeekRd.get(RESULT_VALUE);
//                                    BigDecimal overWeekVal = null;
//                                    if (weekO instanceof Double ) {
//                                        overWeekVal = BigDecimal.valueOf((Double) weekO);
//                                    } else if(weekO instanceof Long) {
//                                        overWeekVal = BigDecimal.valueOf((Long) weekO);
//                                    } else {
//                                        overWeekVal = (BigDecimal) weekO;
//                                    }
//                                    if (Objects.nonNull(overWeekVal)) {
//                                        BigDecimal wow = calcRatio(metricValue, overWeekVal);
//                                        extraValues.setMonthOverMonth(Objects.isNull(wow)? null : wow.doubleValue());
//                                    }
//
//                                }
//                            }
                        metric.setExtraValues(extraValues);
                        metric.setIsRatioValue(annotation.isRatio());
                        metric.setMetricValue(Objects.isNull(metricValue) ? null : metricValue.doubleValue());
                        metric.setMetricName(metricEnum.getEnglishName());
                        metric.setMetricTipsSharkKey(metricEnum.getMetricTipsSharkKey());
                        return metric;
                    })));

        } catch (Exception e) {
            log.warn("get dilldown data error ", e);
        }

        return results;
    }


    protected abstract Map<String, Object> request2Param(GetGrpMetricDataRequestType requestType, int bizMode);

    private List<String> queryAllTargetEmpCodes(String empCode) {
        return null;
    }

    private String[] calcOverDate(String startDate, String endDate, int offset) {
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate hisStartDate = LocalDate.parse(startDate, dtf).minusDays(offset);
        LocalDate hisEndDate = LocalDate.parse(endDate, dtf).minusDays(offset);
        return new String[]{dtf.format(hisStartDate), dtf.format(hisEndDate)};
    }

    protected abstract Map<String, Object> replaceQueryDate4Week(Map<String, Object> param, String startDate, String endDate);

    protected abstract Map<String, Object> replaceQueryDate4Year(Map<String, Object> param, String startDate, String endDate);

    protected abstract Map<String, Object> replaceQueryDate4Month(Map<String, Object> param, String startDate, String endDate);


    public abstract Map<String, Object> queryMetricTrendLine(Map<String, Object> param, GetGrpMetricDataRequestType requestType);

    public abstract List<Map<String, Object>> queryMetricDillDown(Map<String, Object> param, GetGrpMetricDataRequestType requestType);

    public abstract List<Map<String, Object>> queryMetricCardData(Map<String, Object> param, GetGrpMetricDataRequestType requestType);

    private BigDecimal calcRatio(BigDecimal a, BigDecimal b) {
        if (Objects.isNull(a) || BigDecimal.ZERO.compareTo(a) == 0) {
            return null;
        }
        return b.subtract(a).divide(a, 5, RoundingMode.HALF_UP);
    }

    protected String[] calcOverYearDate(String startDate, String endDate) {
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate hisStartDate = LocalDate.parse(startDate, dtf).minusYears(1);
        LocalDate hisEndDate = LocalDate.parse(endDate, dtf).minusYears(1);
        return new String[]{dtf.format(hisStartDate), dtf.format(hisEndDate)};
    }
}
