//package com.ctrip.tour.business.dashboard.grpBusiness.entity;
//
//import javax.persistence.Column;
//import javax.persistence.Entity;
//import javax.persistence.GeneratedValue;
//import javax.persistence.GenerationType;
//import javax.persistence.Id;
//import javax.persistence.Table;
//import com.ctrip.platform.dal.dao.annotation.Database;
//import com.ctrip.platform.dal.dao.annotation.Sensitive;
//import com.ctrip.platform.dal.dao.annotation.Type;
//import java.sql.Types;
//import java.sql.Timestamp;
//
//import com.ctrip.platform.dal.dao.DalPojo;
//
///**
// * <AUTHOR>
// * @date 2024-12-10
// */
//@Entity
//@Database(name = "TtdReportDB_W")
//@Table(name = "adm_prd_grp_multiple_price_work_platform_df")
//public class AdmPrdGrpMultiplePriceWorkPlatformDf implements DalPojo {
//
//    /**
//     * 产品ID
//     */
//	@Column(name = "productid")
//	@Type(value = Types.BIGINT)
//	private Long productid;
//
//    /**
//     * 产线
//     */
//	@Column(name = "sub_bu_type")
//	@Type(value = Types.VARCHAR)
//	private String subBuType;
//
//    /**
//     * 产品形态id
//     */
//	@Column(name = "prd_pattern_id")
//	@Type(value = Types.INTEGER)
//	private Integer prdPatternId;
//
//    /**
//     * 产品形态名称
//     */
//	@Column(name = "prd_pattern_name")
//	@Type(value = Types.VARCHAR)
//	private String prdPatternName;
//
//    /**
//     * 产品板块
//     */
//	@Column(name = "prd_pattern_plat")
//	@Type(value = Types.VARCHAR)
//	private String prdPatternPlat;
//
//    /**
//     * 目的地区域
//     */
//	@Column(name = "dest_domain")
//	@Type(value = Types.VARCHAR)
//	private String destDomain;
//
//    /**
//     * 销售模式名称
//     */
//	@Column(name = "sale_mode_name")
//	@Type(value = Types.VARCHAR)
//	private String saleModeName;
//
//    /**
//     * 客源地/目的地
//     */
//	@Column(name = "tour_region_type")
//	@Type(value = Types.VARCHAR)
//	private String tourRegionType;
//
//    /**
//     * 产品大区id
//     */
//	@Column(name = "prd_region_id")
//	@Type(value = Types.INTEGER)
//	private Integer prdRegionId;
//
//    /**
//     * 产品大区名称
//     */
//	@Column(name = "prd_region_name")
//	@Type(value = Types.VARCHAR)
//	private String prdRegionName;
//
//    /**
//     * 产品经理eid
//     */
//	@Column(name = "pm_eid")
//	@Type(value = Types.VARCHAR)
//	private String pmEid;
//
//    /**
//     * 运营一级大区
//     */
//	@Column(name = "dest_first_region")
//	@Type(value = Types.VARCHAR)
//	private String destFirstRegion;
//
//    /**
//     * 运营二级大区
//     */
//	@Column(name = "dest_sec_region")
//	@Type(value = Types.VARCHAR)
//	private String destSecRegion;
//
//    /**
//     * 目的地大洲ID
//     */
//	@Column(name = "dest_continent_id")
//	@Type(value = Types.INTEGER)
//	private Integer destContinentId;
//
//    /**
//     * 目的地大洲名称
//     */
//	@Column(name = "dest_continent_name")
//	@Type(value = Types.VARCHAR)
//	private String destContinentName;
//
//    /**
//     * 目的地国家ID
//     */
//	@Column(name = "dest_country_id")
//	@Type(value = Types.INTEGER)
//	private Integer destCountryId;
//
//    /**
//     * 目的地国家名称
//     */
//	@Column(name = "dest_country_name")
//	@Type(value = Types.VARCHAR)
//	private String destCountryName;
//
//    /**
//     * 目的地省份ID
//     */
//	@Column(name = "dest_province_id")
//	@Type(value = Types.INTEGER)
//	private Integer destProvinceId;
//
//    /**
//     * 目的地省份名称
//     */
//	@Column(name = "dest_province_name")
//	@Type(value = Types.VARCHAR)
//	private String destProvinceName;
//
//    /**
//     * 目的地城市ID
//     */
//	@Column(name = "dest_city_id")
//	@Type(value = Types.INTEGER)
//	private Integer destCityId;
//
//    /**
//     * 目的地城市名称
//     */
//	@Column(name = "dest_city_name")
//	@Type(value = Types.VARCHAR)
//	private String destCityName;
//
//    /**
//     * 逻辑供应商ID
//     */
//	@Column(name = "logic_vendor_id")
//	@Type(value = Types.INTEGER)
//	private Integer logicVendorId;
//
//    /**
//     * 逻辑供应商名称
//     */
//	@Column(name = "logic_vendor_name")
//	@Type(value = Types.VARCHAR)
//	private String logicVendorName;
//
//    /**
//     * 驻地业务经理（私家团无）
//     */
//	@Column(name = "local_pm_eid")
//	@Type(value = Types.VARCHAR)
//	private String localPmEid;
//
//    /**
//     * 产品壳上的供应商id
//     */
//	@Column(name = "vendor_id")
//	@Type(value = Types.INTEGER)
//	private Integer vendorId;
//
//    /**
//     * 产品壳上的供应商名称
//     */
//	@Column(name = "vendor_name")
//	@Type(value = Types.VARCHAR)
//	private String vendorName;
//
//    /**
//     * 业务板块
//     */
//	@Column(name = "business_plat")
//	@Type(value = Types.VARCHAR)
//	private String businessPlat;
//
//    /**
//     * 注册地所属大区
//     */
//	@Column(name = "register_domain")
//	@Type(value = Types.VARCHAR)
//	private String registerDomain;
//
//    /**
//     * 浏览日期
//     */
//	@Column(name = "view_date")
//	@Type(value = Types.VARCHAR)
//	private String viewDate;
//
//    /**
//     * 平均价格倍数
//     */
//	@Column(name = "avg_multiple_price")
//	@Type(value = Types.DOUBLE)
//	private Double avgMultiplePrice;
//
//    /**
//     * 点击pv
//     */
//	@Column(name = "click_pv")
//	@Type(value = Types.BIGINT)
//	private Long clickPv;
//
//    /**
//     * 加权价格倍数
//     */
//	@Column(name = "weight_multiple_price")
//	@Type(value = Types.DOUBLE)
//	private Double weightMultiplePrice;
//
//    /**
//     * 价格倍数是否大于1或小于0.6
//     */
//	@Column(name = "is_multiple_price_comform")
//	@Type(value = Types.INTEGER)
//	private Integer isMultiplePriceComform;
//
//    /**
//     * 分区日期
//     */
//	@Column(name = "partition_d")
//	@Type(value = Types.VARCHAR)
//	private String partitionD;
//
//    /**
//     * 更新时间
//     */
//	@Column(name = "datachange_lasttime", insertable = false, updatable = false)
//	@Type(value = Types.TIMESTAMP)
//	private Timestamp datachangeLasttime;
//
//    /**
//     * 主键
//     */
//    @Id
//	@Column(name = "id")
//	@GeneratedValue(strategy = GenerationType.AUTO)
//	@Type(value = Types.BIGINT)
//	private Long id;
//
//	public Long getProductid() {
//		return productid;
//	}
//
//	public void setProductid(Long productid) {
//		this.productid = productid;
//	}
//
//	public String getSubBuType() {
//		return subBuType;
//	}
//
//	public void setSubBuType(String subBuType) {
//		this.subBuType = subBuType;
//	}
//
//	public Integer getPrdPatternId() {
//		return prdPatternId;
//	}
//
//	public void setPrdPatternId(Integer prdPatternId) {
//		this.prdPatternId = prdPatternId;
//	}
//
//	public String getPrdPatternName() {
//		return prdPatternName;
//	}
//
//	public void setPrdPatternName(String prdPatternName) {
//		this.prdPatternName = prdPatternName;
//	}
//
//	public String getPrdPatternPlat() {
//		return prdPatternPlat;
//	}
//
//	public void setPrdPatternPlat(String prdPatternPlat) {
//		this.prdPatternPlat = prdPatternPlat;
//	}
//
//	public String getDestDomain() {
//		return destDomain;
//	}
//
//	public void setDestDomain(String destDomain) {
//		this.destDomain = destDomain;
//	}
//
//	public String getSaleModeName() {
//		return saleModeName;
//	}
//
//	public void setSaleModeName(String saleModeName) {
//		this.saleModeName = saleModeName;
//	}
//
//	public String getTourRegionType() {
//		return tourRegionType;
//	}
//
//	public void setTourRegionType(String tourRegionType) {
//		this.tourRegionType = tourRegionType;
//	}
//
//	public Integer getPrdRegionId() {
//		return prdRegionId;
//	}
//
//	public void setPrdRegionId(Integer prdRegionId) {
//		this.prdRegionId = prdRegionId;
//	}
//
//	public String getPrdRegionName() {
//		return prdRegionName;
//	}
//
//	public void setPrdRegionName(String prdRegionName) {
//		this.prdRegionName = prdRegionName;
//	}
//
//	public String getPmEid() {
//		return pmEid;
//	}
//
//	public void setPmEid(String pmEid) {
//		this.pmEid = pmEid;
//	}
//
//	public String getDestFirstRegion() {
//		return destFirstRegion;
//	}
//
//	public void setDestFirstRegion(String destFirstRegion) {
//		this.destFirstRegion = destFirstRegion;
//	}
//
//	public String getDestSecRegion() {
//		return destSecRegion;
//	}
//
//	public void setDestSecRegion(String destSecRegion) {
//		this.destSecRegion = destSecRegion;
//	}
//
//	public Integer getDestContinentId() {
//		return destContinentId;
//	}
//
//	public void setDestContinentId(Integer destContinentId) {
//		this.destContinentId = destContinentId;
//	}
//
//	public String getDestContinentName() {
//		return destContinentName;
//	}
//
//	public void setDestContinentName(String destContinentName) {
//		this.destContinentName = destContinentName;
//	}
//
//	public Integer getDestCountryId() {
//		return destCountryId;
//	}
//
//	public void setDestCountryId(Integer destCountryId) {
//		this.destCountryId = destCountryId;
//	}
//
//	public String getDestCountryName() {
//		return destCountryName;
//	}
//
//	public void setDestCountryName(String destCountryName) {
//		this.destCountryName = destCountryName;
//	}
//
//	public Integer getDestProvinceId() {
//		return destProvinceId;
//	}
//
//	public void setDestProvinceId(Integer destProvinceId) {
//		this.destProvinceId = destProvinceId;
//	}
//
//	public String getDestProvinceName() {
//		return destProvinceName;
//	}
//
//	public void setDestProvinceName(String destProvinceName) {
//		this.destProvinceName = destProvinceName;
//	}
//
//	public Integer getDestCityId() {
//		return destCityId;
//	}
//
//	public void setDestCityId(Integer destCityId) {
//		this.destCityId = destCityId;
//	}
//
//	public String getDestCityName() {
//		return destCityName;
//	}
//
//	public void setDestCityName(String destCityName) {
//		this.destCityName = destCityName;
//	}
//
//	public Integer getLogicVendorId() {
//		return logicVendorId;
//	}
//
//	public void setLogicVendorId(Integer logicVendorId) {
//		this.logicVendorId = logicVendorId;
//	}
//
//	public String getLogicVendorName() {
//		return logicVendorName;
//	}
//
//	public void setLogicVendorName(String logicVendorName) {
//		this.logicVendorName = logicVendorName;
//	}
//
//	public String getLocalPmEid() {
//		return localPmEid;
//	}
//
//	public void setLocalPmEid(String localPmEid) {
//		this.localPmEid = localPmEid;
//	}
//
//	public Integer getVendorId() {
//		return vendorId;
//	}
//
//	public void setVendorId(Integer vendorId) {
//		this.vendorId = vendorId;
//	}
//
//	public String getVendorName() {
//		return vendorName;
//	}
//
//	public void setVendorName(String vendorName) {
//		this.vendorName = vendorName;
//	}
//
//	public String getBusinessPlat() {
//		return businessPlat;
//	}
//
//	public void setBusinessPlat(String businessPlat) {
//		this.businessPlat = businessPlat;
//	}
//
//	public String getRegisterDomain() {
//		return registerDomain;
//	}
//
//	public void setRegisterDomain(String registerDomain) {
//		this.registerDomain = registerDomain;
//	}
//
//	public String getViewDate() {
//		return viewDate;
//	}
//
//	public void setViewDate(String viewDate) {
//		this.viewDate = viewDate;
//	}
//
//	public Double getAvgMultiplePrice() {
//		return avgMultiplePrice;
//	}
//
//	public void setAvgMultiplePrice(Double avgMultiplePrice) {
//		this.avgMultiplePrice = avgMultiplePrice;
//	}
//
//	public Long getClickPv() {
//		return clickPv;
//	}
//
//	public void setClickPv(Long clickPv) {
//		this.clickPv = clickPv;
//	}
//
//	public Double getWeightMultiplePrice() {
//		return weightMultiplePrice;
//	}
//
//	public void setWeightMultiplePrice(Double weightMultiplePrice) {
//		this.weightMultiplePrice = weightMultiplePrice;
//	}
//
//	public Integer getIsMultiplePriceComform() {
//		return isMultiplePriceComform;
//	}
//
//	public void setIsMultiplePriceComform(Integer isMultiplePriceComform) {
//		this.isMultiplePriceComform = isMultiplePriceComform;
//	}
//
//	public String getPartitionD() {
//		return partitionD;
//	}
//
//	public void setPartitionD(String partitionD) {
//		this.partitionD = partitionD;
//	}
//
//	public Timestamp getDatachangeLasttime() {
//		return datachangeLasttime;
//	}
//
//	public void setDatachangeLasttime(Timestamp datachangeLasttime) {
//		this.datachangeLasttime = datachangeLasttime;
//	}
//
//	public Long getId() {
//		return id;
//	}
//
//	public void setId(Long id) {
//		this.id = id;
//	}
//
//}