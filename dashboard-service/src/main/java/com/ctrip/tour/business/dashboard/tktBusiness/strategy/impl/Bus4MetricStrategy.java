package com.ctrip.tour.business.dashboard.tktBusiness.strategy.impl;

import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ExamineConfigBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.SinglePeriodDataBean;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.SinglePeriodTrendLineBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.Bus4Dao;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardExamineeConfigV2Dao;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.*;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.MetricCalStrategy;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/8/1
 */
@Component
public class Bus4MetricStrategy implements MetricCalStrategy {

    @Autowired
    private Bus4Dao bus4Dao;

    @Autowired
    private RemoteConfig remoteConfig;

    @Autowired
    private BusinessDashboardExamineeConfigV2Dao examineeConfigV2Dao;

    @Autowired
    private SinglePeriodTrendLineBiz singlePeriodTrendLineBiz;


    @Override
    public Future<MetricDetailInfo> getSingleMetricCardData(String domainName,
                                                            TimeFilter timeFilter,
                                                            MetricInfoBean metricInfoBean,
                                                            String d,
                                                            Boolean needRank) throws Exception {

        MetricDetailInfo metricDetailInfo = new MetricDetailInfo();
        String metric = metricInfoBean.getMetric();
        Map<String, Double> dimMap = new HashMap<>();
        metricDetailInfo.setDimData(dimMap);
        metricDetailInfo.setMetric(metric);

        String year = timeFilter.getYear();
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();
        String dateType = MetricHelper.getDateTypeColumnName(timeFilter.getDateType());
        if (fastCheckBus4(year, dateType, quarter, month)) {
            return new AsyncResult<>(metricDetailInfo);
        }


        String level = MetricHelper.getLevelColumnName(metricInfoBean.getLevel());

        List<String> regionList = metricInfoBean.getRegionList();

        DalHints rankDalHints = new DalHints().asyncExecution();
        if (needRank) {
            getRankDataAsync(dateType, year, quarter, month, d, domainName, metric, rankDalHints);
        }

        //由于mysql需要设置索引 因此需要保持顺序
        Map<String, List<String>> reachInMap = new LinkedHashMap<>();
        reachInMap.put("query_d", Lists.newArrayList(d));
        reachInMap.put("year", Lists.newArrayList(year));
        reachInMap.put("date_type", Lists.newArrayList(dateType));
        if ("month".equals(dateType)) {
            reachInMap.put("month", Lists.newArrayList(month));
        } else {
            reachInMap.put("quarter", Lists.newArrayList(quarter));
        }
        if (!"".equals(level)) {
            reachInMap.put(level, regionList);
        }

        List<List<Object>> scenicSignData = bus4Dao.getScenicSignData(reachInMap, new ArrayList<>());


        List<String> dimList = Bus4Helper.getDimList();
        ChartHelper.fillOverallDimMap(scenicSignData, dimList, dimMap);


        MetricHelper.setMetricCardDrillDownInfo(metricInfoBean, metricDetailInfo);

        //获取排名数据
        if (needRank) {
            ChartHelper.fillRankData(metricDetailInfo, rankDalHints.getListResult());
        }

        return new AsyncResult<>(metricDetailInfo);
    }

    @Override
    public GetTrendLineDataResponseType getSingleTrendlineData(GetTrendLineDataRequestType request,
                                                               MetricInfoBean metricInfoBean,
                                                               String d) throws Exception {

        String queryType = request.getQueryType();
        if ("trendline".equals(queryType)) {
            return getSingleTrendlineDataWithoutDrillDown(request, metricInfoBean, d);
        } else {
            return getSingleTrendlineDataWithDrillDown(request, metricInfoBean, d);
        }
    }

    @Override
    public GetTableDataResponseType getSingleTableData(GetTableDataRequestType request,
                                                       MetricInfoBean metricInfoBean,
                                                       String d) throws Exception {

        String queryType = request.getQueryType();
        if (null == queryType) {
            queryType = "drilldown";
        }
        if ("drilldown".equals(queryType)) {
            return getSingleTableDataWithDrillDown(request, metricInfoBean, d);
        }
        return getSingleTableDataWithOutDrillDown(request, metricInfoBean, d);


    }

    @Override
    public GetDrillDownBaseInfoResponseType getSingleDrillDownBaseInfo(GetDrillDownBaseInfoRequestType request,
                                                                       MetricInfoBean metricInfoBean,
                                                                       String d) throws Exception {
        GetDrillDownBaseInfoResponseType response = new GetDrillDownBaseInfoResponseType();
        List<FieldDataItem> fieldDataItemList = new ArrayList<>();
        response.setFieldDataItemList(fieldDataItemList);

        TimeFilter timeFilter = request.getTimeFilter();
        String year = timeFilter.getYear();
        String dateType = timeFilter.getDateType();
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();

        String metric = metricInfoBean.getMetric();
        List<String> regionList = metricInfoBean.getRegionList();
        String level = metricInfoBean.getLevel();//国内  三方  大区  省份
        String levelColumnName = MetricHelper.getLevelColumnName(level);

        boolean needSearch = request.isNeedSearch();
        String searchWord = request.getSearchWord();
        List<String> fieldList = Bus4Helper.getFieldList(level);

        if(needSearch){
            String searchField = request.getSearchField();//大区 省份

            if (searchField != null) {
                String searchFieldColumnName = MetricHelper.getDrillDownColumnName(searchField);
                fieldList = Lists.newArrayList(searchFieldColumnName);
            }
        }else{
            response.setDefaultChosenField(MetricHelper.getDefaultChosenField(level, metric));
        }
        Map<String, List<String>> inMap = new LinkedHashMap<>();
        inMap.put("query_d", Lists.newArrayList(d));
        inMap.put("year", Lists.newArrayList(year));
        if ("month".equals(dateType)) {
            inMap.put("month", Lists.newArrayList(month));
        } else {
            inMap.put("quarter", Lists.newArrayList(quarter));
        }
        if (!"".equals(levelColumnName)) {
            inMap.put(levelColumnName, regionList);
        }
        for (String field : fieldList) {
            List<String> tagList = Lists.newArrayList(field);
            Map<String,String> likeMap = new HashMap<>();
            if(needSearch){
                likeMap.put(field,searchWord);
            }
            List<List<Object>> rawObjectList = bus4Dao.getFieldList(inMap, likeMap, tagList);
            ChartHelper.fillFieldDataItemList(field, rawObjectList, fieldDataItemList);
        }
        return response;
    }


    private GetTableDataResponseType getSingleTableDataWithOutDrillDown(GetTableDataRequestType request,
                                                                        MetricInfoBean metricInfoBean,
                                                                        String d) throws Exception {
        GetTableDataResponseType response = new GetTableDataResponseType();
        List<TableDataItem> tableDataItemList = new ArrayList<>();
        response.setTableDataItemList(tableDataItemList);
        TimeFilter timeFilter = request.getTimeFilter();
        String year = timeFilter.getYear();
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();
        String dateType = MetricHelper.getDateTypeColumnName(timeFilter.getDateType());
        if (fastCheckBus4(year, dateType, quarter, month)) {
            return response;
        }

        String level = MetricHelper.getLevelColumnName(metricInfoBean.getLevel());
        List<String> regionList = metricInfoBean.getRegionList();

        //获取分层进展  目标   差值   直签率
        List<String> groupTagList = Lists.newArrayList("scenic_class_name","scenic_class_code");
        List<String> orderTagList = Lists.newArrayList("scenic_class_id");
        Map<String, List<String>> inMap = new LinkedHashMap<>();
        inMap.put("query_d", Lists.newArrayList(d));
        inMap.put("year", Lists.newArrayList(year));
        inMap.put("date_type", Lists.newArrayList(dateType));
        if ("month".equals(dateType)) {
            inMap.put("month", Lists.newArrayList(month));
        } else {
            inMap.put("quarter", Lists.newArrayList(quarter));
        }
        if (!"".equals(level)) {
            inMap.put(level, regionList);
        }
        List<List<Object>> scenicTableList = bus4Dao.getLevelScenicTableData(inMap,groupTagList,orderTagList,1,999);

        //计算数据总条数
        response.setTotalNum(bus4Dao.getTableReachDataCount(inMap, groupTagList));

        //拼接数据
        List<String> tableDimList = Bus4Helper.getTableDimList();
        ChartHelper.fillCommmonTableData(tableDataItemList, groupTagList,
                tableDimList, new ArrayList<>(), scenicTableList, new ArrayList<>());
        JumpHelper.makeUpTargetManageUrl(remoteConfig, timeFilter, metricInfoBean, tableDataItemList);
        return response;
    }

    private GetTableDataResponseType getSingleTableDataWithDrillDown(GetTableDataRequestType request,
                                                                     MetricInfoBean metricInfoBean,
                                                                     String d) throws Exception {
        GetTableDataResponseType response = new GetTableDataResponseType();
        List<TableDataItem> tableDataItemList = new ArrayList<>();
        response.setTableDataItemList(tableDataItemList);
        TimeFilter timeFilter = request.getTimeFilter();
        String year = timeFilter.getYear();
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();
        String dateType = MetricHelper.getDateTypeColumnName(timeFilter.getDateType());
        if (fastCheckBus4(year, dateType, quarter, month)) {
            return response;
        }


        String level = MetricHelper.getLevelColumnName(metricInfoBean.getLevel());
        List<String> regionList = metricInfoBean.getRegionList();
        Integer pageNo = request.getPageNo();
        Integer pageSize = request.getPageSize();
        if (pageSize == null) {
            pageSize = 18;
        }

        DrillDownFilter drillDownFilter = request.getDrillDownFilter();
        String field = MetricHelper.getDrillDownColumnName(drillDownFilter.getField());
        List<String> fieldValueList = drillDownFilter.getFieldValueList();
        List<String> subjectTagList = MetricHelper.getTableDrillDownGroupList(field);
        subjectTagList.add("scenic_class_name");
        //获取分层进展  目标   差值   直签率
        List<String> orderTagList = Lists.newArrayList(field, "scenic_class_id");
        Map<String, List<String>> inMap = new LinkedHashMap<>();
        inMap.put("query_d", Lists.newArrayList(d));
        inMap.put("year", Lists.newArrayList(year));
        inMap.put("date_type", Lists.newArrayList(dateType));
        if ("month".equals(dateType)) {
            inMap.put("month", Lists.newArrayList(month));
        } else {
            inMap.put("quarter", Lists.newArrayList(quarter));
        }
        if (!"".equals(level)) {
            inMap.put(level, regionList);
        }
        if (!GeneralUtil.isEmpty(fieldValueList)) {
            inMap.put(field, fieldValueList);
        }
        List<List<Object>> scenicTableList = bus4Dao.getLevelScenicTableData(inMap, subjectTagList, orderTagList, pageNo, pageSize);

        //计算数据总条数
        response.setTotalNum(bus4Dao.getTableReachDataCount(inMap, subjectTagList));
        List<String> tableDimList = Bus4Helper.getTableDimList();
        ChartHelper.fillCommmonTableData(tableDataItemList, subjectTagList,
                tableDimList, new ArrayList<>(), scenicTableList, new ArrayList<>());

        //收集下钻字段的值
        List<String> actualFieldList = tableDataItemList.stream()
                .map(i -> i.getFieldMap().get(field))
                .collect(Collectors.toList());
        if (!GeneralUtil.isEmpty(actualFieldList)) {
            inMap.put(field, actualFieldList);
            List<List<Object>> scenicSignDataList = bus4Dao.getScenicSignData(inMap, Lists.newArrayList(field));
            ChartHelper.fillCommmonTableDataV2(tableDataItemList, new ArrayList<>(),
                    Lists.newArrayList(field), new ArrayList<>(), Bus4Helper.getDimList(), new ArrayList<>(), scenicSignDataList);
        }

        return response;
    }

    private GetTrendLineDataResponseType getSingleTrendlineDataWithDrillDown(GetTrendLineDataRequestType request,
                                                                             MetricInfoBean metricInfoBean,
                                                                             String d) throws Exception {
        GetTrendLineDataResponseType response = new GetTrendLineDataResponseType();
        List<TrendLineDetailInfo> trendLineDetailInfoList = new ArrayList<>();
        response.setTrendLineDetailInfoList(trendLineDetailInfoList);

        TimeFilter timeFilter = request.getTimeFilter();
        String year = timeFilter.getYear();
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();
        String dateType = MetricHelper.getDateTypeColumnName(timeFilter.getDateType());
        if (fastCheckBus4(year, dateType, quarter, month)) {
            return response;
        }

        DrillDownFilter drillDownFilter = request.getDrillDownFilter();
        String field = MetricHelper.getDrillDownColumnName(drillDownFilter.getField());

        String domainName = request.getDomainName();
        List<ExamineConfigBean> examineConfigBeanList = MultiPeriodMappingHelper.getMultiPeriodConfigList(timeFilter, d, examineeConfigV2Dao.queryMetricAllConfig(domainName, d, getMetricName()), null);
        List<Future<SinglePeriodDataBean>> futureList = new ArrayList<>();
        for (ExamineConfigBean examineConfigBean : examineConfigBeanList) {
            futureList.add(singlePeriodTrendLineBiz.getBus4SinglePeriodTrendLineData(request, examineConfigBean, d));
        }
        List<List<Object>> reachList = new ArrayList<>();
        for (Future<SinglePeriodDataBean> futureResult : futureList) {
            SinglePeriodDataBean singlePeriodDataBean = futureResult.get();
            List<List<Object>> periodReachList = singlePeriodDataBean.getPeriodReachList();
            if (!GeneralUtil.isEmpty(periodReachList)) {
                reachList.addAll(periodReachList);
            }
        }


        Map<String, Double> dimMap = new HashMap<>();
        Set<String> drillDownSet = new HashSet<>();
        ChartHelper.setMonthlyMetricTrendlineActualReach(dimMap, drillDownSet, reachList, Lists.newArrayList("time", field), Bus4Helper.getDimList());

        //有效时间范围
        List<String> timeList = DateUtil.getAllSelectedTime(timeFilter, "domestic");
        ChartHelper.fillLineChartTrendLineDataWithDrillDown(null, timeList, dimMap,
                trendLineDetailInfoList, Bus4Helper.getLineChartTrendlineTypeWithDrillDown(), drillDownSet, false);
        return response;
    }


    private GetTrendLineDataResponseType getSingleTrendlineDataWithoutDrillDown(GetTrendLineDataRequestType request,
                                                                                MetricInfoBean metricInfoBean,
                                                                                String d) throws Exception {
        GetTrendLineDataResponseType response = new GetTrendLineDataResponseType();
        List<TrendLineDetailInfo> trendLineDetailInfoList = new ArrayList<>();
        response.setTrendLineDetailInfoList(trendLineDetailInfoList);

        TimeFilter timeFilter = request.getTimeFilter();
        String year = timeFilter.getYear();
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();
        String dateType = MetricHelper.getDateTypeColumnName(timeFilter.getDateType());
        if (fastCheckBus4(year, dateType, quarter, month)) {
            return response;
        }


        String domainName = request.getDomainName();
        List<ExamineConfigBean> examineConfigBeanList = MultiPeriodMappingHelper.getMultiPeriodConfigList(timeFilter, d, examineeConfigV2Dao.queryMetricAllConfig(domainName, d, getMetricName()), null);
        List<Future<SinglePeriodDataBean>> futureList = new ArrayList<>();
        for (ExamineConfigBean examineConfigBean : examineConfigBeanList) {
            futureList.add(singlePeriodTrendLineBiz.getBus4SinglePeriodTrendLineData(request, examineConfigBean, d));
        }
        List<List<Object>> reachList = new ArrayList<>();
        List<List<Object>> stackList = new ArrayList<>();
        for (Future<SinglePeriodDataBean> futureResult : futureList) {
            SinglePeriodDataBean singlePeriodDataBean = futureResult.get();
            List<List<Object>> periodReachList = singlePeriodDataBean.getPeriodReachList();
            List<List<Object>> periodStackList = singlePeriodDataBean.getPeriodStackList();
            if (!GeneralUtil.isEmpty(periodReachList)) {
                reachList.addAll(periodReachList);
            }
            if (!GeneralUtil.isEmpty(periodStackList)) {
                stackList.addAll(periodStackList);
            }
        }


        Map<String, Double> dimMap = new HashMap<>();
        Set<String> drillDownSet = new HashSet<>();

        ChartHelper.setMonthlyMetricTrendlineActualReach(dimMap, drillDownSet, reachList, Lists.newArrayList("time"), Bus4Helper.getDimList());
        //有效时间范围
        List<String> timeList = DateUtil.getAllSelectedTime(timeFilter, "domestic");



        //获取景点分层堆叠柱状图
        List<String> groupTagList1 = Lists.newArrayList("time", "scenic_class_name");
        Map<String, Double> dimMap1 = new HashMap<>();
        Set<String> drillDownSet1 = new LinkedHashSet<>();
        List<String> reachDimList1 = Bus4Helper.getStackDimList();
        ChartHelper.setMonthlyMetricTrendlineActualReach(dimMap1, drillDownSet1, stackList, groupTagList1, reachDimList1);
        ChartHelper.fillLineChartTrendLineDataWithDrillDown(null, timeList, dimMap1, trendLineDetailInfoList, Bus4Helper.getStackLineChartTrendLineType(), drillDownSet1,false);

        //特殊排序  完成率放在堆叠柱状图后面
        ChartHelper.fillLineChartTrendLineData(null, timeList, dimMap,
                trendLineDetailInfoList, Bus4Helper.getLineChartTrendlineType());
        return response;
    }


    private void getRankDataAsync(String dateType,
                                  String year,
                                  String quarter,
                                  String month,
                                  String d,
                                  String domainName,
                                  String metric,
                                  DalHints dalHints) throws Exception {
        Map<String, List<String>> inMap = new LinkedHashMap<>();
        inMap.put("query_d", Lists.newArrayList(d));
        inMap.put("examinee", Lists.newArrayList(domainName));
        inMap.put("year", Lists.newArrayList(year));
        if ("month".equals(dateType)) {
            inMap.put("month", Lists.newArrayList(month));
        } else {
            String mappingMonth = DateUtil.getMappingMonthByQuarter(d, year, quarter);
            inMap.put("month", Lists.newArrayList(mappingMonth));
        }
        inMap.put("metric", Lists.newArrayList(metric));
        bus4Dao.getRankAsync(inMap, dalHints);
    }





    private Boolean fastCheckBus4(String year,
                                  String dateType,
                                  String quarter,
                                  String month) {
        Boolean flag = false;
        List<String> monthList = Lists.newArrayList("01", "02", "03");
        if ("2022".equals(year)) {
            if (("quarter".equals(dateType) && "Q1".equals(quarter)) ||
                    ("month".equals(dateType) && monthList.contains(month))) {
                flag = true;
            }
        }
        return flag;
    }

    @Override
    public String getMetricName() {
        return "4";
    }
}
