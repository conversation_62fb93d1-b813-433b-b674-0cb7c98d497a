package com.ctrip.tour.business.dashboard.grpBusiness.enums;

import static com.ctrip.tour.business.dashboard.grpBusiness.enums.DiyMetricCategoryEnum.AVA_ORDER_CAP_CATEGORY;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

public enum DrillDownDim {


    //GMV、毛利：产线、产品形态、区域、销售渠道、销售模式、客源地/目的地、产品形态、产品大区、业务经理、运营大区、目的地、供应商ID、供应商名称、是否秒杀产品，驻地业务经理

    //UV、详情页转化：产线、产品形态、区域、销售渠道、销售模式、客源地/目的地、产品形态、产品大区、业务经理、运营大区、目的地、供应商ID、供应商名称、是否秒杀产品

    //拟合NPS：区域、产线、产品形态、目的地、业务经理、运营大区、驻地业务经理

    //价格倍数：产线、产品形态、产品ID、区域、销售渠道、销售模式、客源地/目的地、产品形态、产品大区
    //       、业务经理、运营大区、目的地、供应商ID、供应商名称、//价格倍数大于1或者小于0.6

    //自服务：产线、产品形态、销售渠道、销售模式、产品ID、供应商ID、供应商名称、产品大区、产品形态、业务经理、运营大区、目的地、是否秒杀产品

    // 产线
    SUB_BU_TYPE("sub_bu_type", "产线"),//NOSONAR
    // 产品形态
    PRD_PATTERN_NAME("prd_pattern_name", "产品形态"),//NOSONAR
    // 区域
    DEST_DOMAIN("dest_domain", "区域"),//NOSONAR
    // 销售渠道
    SALE_CHANNEL_NAME("sale_channel_name", "销售渠道"),//NOSONAR
    // 销售模式
    SALE_MODE_NAME("sale_mode_name", "销售模式"),//NOSONAR
    // 客源地/目的地
    TOUR_REGION_TYPE("tour_region_type", "客源地/目的地"),//NOSONAR
    // 产品大区
    PRD_REGION_NAME("prd_region_name", "产品大区"),//NOSONAR
    // 业务经理
    PM_EID("pm_eid", "业务经理"),//NOSONAR
    // 运营大区
    DEST_FIRST_REGION("dest_first_region", "运营大区"),//NOSONAR
    // 目的地
    DEST_NAME("dest_name", "目的地"),//NOSONAR
    // 供应商ID
    VENDOR_ID("vendor_id", "供应商ID"),//NOSONAR
    // 供应商名称
    VENDOR_NAME("vendor_name", "供应商名称"),//NOSONAR
    // 是否秒杀产品
    IS_SEC_KILL("is_sec_kill", "是否参与秒杀"),//NOSONAR
    // 驻地业务经理
    LOCAL_PM_EID("local_pm_eid", "驻地业务经理"),//NOSONAR
    DOMAIN("domain", "境内外"),//NOSONAR
    BUSINESS_REGION_NAME("business_region_name", "大区"),//NOSONAR
    GRADE_REGION_NAME("grade_region_name", "评级区域"),//NOSONAR
    DESTINATIONLINE("destinationline", "目的地区域"),//NOSONAR
    PLC_SALE_MODE("plc_sale_mode", "下单时销售模式"),//NOSONAR
    BIZ_STAT_SALE_MODE("bu_sale_mode", "业务销售模式"),//NOSONAR
    L1_ROUTE_NAME("l1_route_name", "线路玩法（L1）"),//NOSONAR
    PRD_LEVEL_NAME("prd_level_name", "钻级"),//NOSONAR
    PK_TOUR_LINE("pk_tour_line", "线路玩法·钻级·天数·是否拼小团"),//NOSONAR
    CPR_DEST_AREA("cpr_dest_area", "竞争圈"),//NOSONAR
    VBK_PROVIDER_NAME("vbk_provider_name", "供应商");//NOSONAR



    private String englishName;
    private String chineseName;

    DrillDownDim(String englishName, String chineseName) {
        this.englishName = englishName;
        this.chineseName = chineseName;
    }

    public String getEnglishName() {
        return englishName;
    }

    public void setEnglishName(String englishName) {
        this.englishName = englishName;
    }

    public String getChineseName() {
        return chineseName;
    }

    public void setChineseName(String chineseName) {
        this.chineseName = chineseName;
    }

    public static List<DrillDownDim> getDrillDownDimByMetricCategoryEnum(MetricCategoryEnum metricCategoryEnum) {
        switch (metricCategoryEnum) {
            case INCOME_CATEGORY:
                return Arrays.asList(SUB_BU_TYPE, PRD_PATTERN_NAME, DEST_DOMAIN, SALE_CHANNEL_NAME, SALE_MODE_NAME, TOUR_REGION_TYPE, PRD_REGION_NAME, PM_EID, DEST_FIRST_REGION, DEST_NAME, VENDOR_ID, VENDOR_NAME, IS_SEC_KILL, LOCAL_PM_EID, PLC_SALE_MODE, BIZ_STAT_SALE_MODE,L1_ROUTE_NAME, PRD_LEVEL_NAME, PK_TOUR_LINE, CPR_DEST_AREA);
            case PROFIT_PRICE_CATEGORY:
                return Arrays.asList(SUB_BU_TYPE, PRD_PATTERN_NAME, DEST_DOMAIN, SALE_CHANNEL_NAME, SALE_MODE_NAME, TOUR_REGION_TYPE, PRD_REGION_NAME, PM_EID, DEST_FIRST_REGION, DEST_NAME, VENDOR_ID, VENDOR_NAME, IS_SEC_KILL, LOCAL_PM_EID, PLC_SALE_MODE, BIZ_STAT_SALE_MODE, PRD_LEVEL_NAME, PK_TOUR_LINE, CPR_DEST_AREA);
            case UV_CATEGORY:
                return Arrays.asList(SUB_BU_TYPE, PRD_PATTERN_NAME, DEST_DOMAIN, SALE_CHANNEL_NAME, SALE_MODE_NAME, TOUR_REGION_TYPE, PRD_REGION_NAME, PM_EID, DEST_FIRST_REGION, DEST_NAME, VENDOR_ID, VENDOR_NAME, IS_SEC_KILL);
            case CONVERSION_RATE_CATEGORY:
                return Arrays.asList(SUB_BU_TYPE, PRD_PATTERN_NAME, DEST_DOMAIN, SALE_CHANNEL_NAME, SALE_MODE_NAME, TOUR_REGION_TYPE, PRD_REGION_NAME, PM_EID, DEST_FIRST_REGION, DEST_NAME, VENDOR_ID, VENDOR_NAME, IS_SEC_KILL, PLC_SALE_MODE, BIZ_STAT_SALE_MODE,L1_ROUTE_NAME, PRD_LEVEL_NAME, PK_TOUR_LINE, CPR_DEST_AREA);
            case FITTING_NPS_CATEGORY:
                return Arrays.asList(SUB_BU_TYPE, PRD_PATTERN_NAME, DEST_DOMAIN, SALE_CHANNEL_NAME, SALE_MODE_NAME, TOUR_REGION_TYPE, PRD_REGION_NAME, PM_EID, DEST_FIRST_REGION, DEST_NAME, VENDOR_ID, VENDOR_NAME);
            case MULTIPLE_PRICE_CATEGORY:
                return Arrays.asList(SUB_BU_TYPE, PRD_PATTERN_NAME, SALE_CHANNEL_NAME, SALE_MODE_NAME, VENDOR_ID, VENDOR_NAME, PRD_REGION_NAME, PRD_PATTERN_NAME, PM_EID, DEST_FIRST_REGION, DEST_NAME, IS_SEC_KILL);
            case SELF_SERVICE_CATEGORY:
                return Arrays.asList(SUB_BU_TYPE, PRD_PATTERN_NAME, SALE_CHANNEL_NAME, SALE_MODE_NAME, VENDOR_ID, VENDOR_NAME, PRD_REGION_NAME, PRD_PATTERN_NAME, PM_EID, DEST_FIRST_REGION, DEST_NAME, IS_SEC_KILL);
            case INFO_SCORE_CATEGORY:
                return Arrays.asList(SUB_BU_TYPE, PRD_PATTERN_NAME, SALE_CHANNEL_NAME, SALE_MODE_NAME, VENDOR_ID, VENDOR_NAME, PRD_REGION_NAME, PRD_PATTERN_NAME, PM_EID, DEST_FIRST_REGION, DEST_NAME, IS_SEC_KILL);
            case TOP_RATE_DRIVER_CATEGORY:
                return Arrays.asList(SUB_BU_TYPE, PRD_PATTERN_NAME, SALE_CHANNEL_NAME, SALE_MODE_NAME, VENDOR_ID, VENDOR_NAME, PRD_REGION_NAME, PRD_PATTERN_NAME, PM_EID, DEST_FIRST_REGION, DEST_NAME, IS_SEC_KILL);
            case TOP_RATE_GUIDER_CATEGORY:
                return Arrays.asList(SUB_BU_TYPE, PRD_PATTERN_NAME, SALE_CHANNEL_NAME, SALE_MODE_NAME, VENDOR_ID, VENDOR_NAME, PRD_REGION_NAME, PRD_PATTERN_NAME, PM_EID, DEST_FIRST_REGION, DEST_NAME, IS_SEC_KILL);
            case SELF_OPR_PARENT_PRD_COUNT_CATEGORY:
                return Arrays.asList(SUB_BU_TYPE, PRD_PATTERN_NAME, SALE_CHANNEL_NAME, SALE_MODE_NAME, VENDOR_ID, VENDOR_NAME, PRD_REGION_NAME, PRD_PATTERN_NAME, PM_EID, DEST_FIRST_REGION, DEST_NAME, IS_SEC_KILL,L1_ROUTE_NAME,PRD_LEVEL_NAME, PK_TOUR_LINE, CPR_DEST_AREA);
            case SINGLE_UV_VAL_PRD_CATEGORY:
                return Arrays.asList(SUB_BU_TYPE, PRD_PATTERN_NAME, DEST_DOMAIN, SALE_MODE_NAME, TOUR_REGION_TYPE, PRD_REGION_NAME, PM_EID, DEST_FIRST_REGION, DEST_NAME, VENDOR_ID, VENDOR_NAME, IS_SEC_KILL, LOCAL_PM_EID,L1_ROUTE_NAME, PRD_LEVEL_NAME, PK_TOUR_LINE, CPR_DEST_AREA);
            case HIGH_QUA_VENDOR_COUNT_WITH_SELFOPR_PRD_CATEGORY:
                return Arrays.asList(SUB_BU_TYPE, PRD_PATTERN_NAME, DEST_DOMAIN, SALE_CHANNEL_NAME, SALE_MODE_NAME, TOUR_REGION_TYPE, PRD_REGION_NAME, PM_EID, DEST_FIRST_REGION, DEST_NAME, VENDOR_ID, VENDOR_NAME, IS_SEC_KILL,CPR_DEST_AREA);
                default:
                return new ArrayList<>();

        }
    }

    public static List<DrillDownDim> getDiyDrillDownDimByMetricCategoryEnum(DiyMetricCategoryEnum metricCategoryEnum) {
        switch (metricCategoryEnum) {
            case INCOME_CATEGORY:
                return Arrays.asList(DOMAIN, GRADE_REGION_NAME, BUSINESS_REGION_NAME);
            case PROFIT_PRICE_CATEGORY:
                return Arrays.asList(DOMAIN, GRADE_REGION_NAME, BUSINESS_REGION_NAME);
            case AVA_ORDER_CAP_CATEGORY:
                return Arrays.asList(DOMAIN, GRADE_REGION_NAME, BUSINESS_REGION_NAME, DESTINATIONLINE, VBK_PROVIDER_NAME);
            case TRADING_VOL_CATEGORY:
                return Arrays.asList(DOMAIN, GRADE_REGION_NAME, BUSINESS_REGION_NAME, DESTINATIONLINE, VBK_PROVIDER_NAME);
            case FITTING_NPS_CATEGORY:
                return Arrays.asList(DOMAIN, GRADE_REGION_NAME, BUSINESS_REGION_NAME, DESTINATIONLINE, VBK_PROVIDER_NAME);
            case TOP_RATE_DRIVER_CATEGORY:
                return Arrays.asList(DOMAIN, GRADE_REGION_NAME, BUSINESS_REGION_NAME, DESTINATIONLINE, VBK_PROVIDER_NAME);
            case TOP_RATE_GUIDER_CATEGORY:
                return Arrays.asList(DOMAIN, GRADE_REGION_NAME, BUSINESS_REGION_NAME, DESTINATIONLINE, VBK_PROVIDER_NAME);
            case VENDOR_ORD_ACCEPT_RATE_CATEGORY:
                return Arrays.asList(DOMAIN, GRADE_REGION_NAME, BUSINESS_REGION_NAME, DESTINATIONLINE, VBK_PROVIDER_NAME);
            default:
                return new ArrayList<>();

        }
    }


    public static List<DrillDownDim> getDrillDownDimByMetricCategoryEnglishName(String metricCategoryName, Integer selectTab) {
        if (Objects.equals(selectTab, 1) || Objects.equals(selectTab, 2)) {
            MetricCategoryEnum metricCategoryEnum  = MetricCategoryEnum.getMetricCategoryEnumByEnglishName(metricCategoryName);
            if(metricCategoryEnum == null){
                return new ArrayList<>();
            }
            return getDrillDownDimByMetricCategoryEnum(metricCategoryEnum);
        } else {
            DiyMetricCategoryEnum diyMetricCategoryEnum  = DiyMetricCategoryEnum.getMetricCategoryEnumByEnglishName(metricCategoryName);
            if(diyMetricCategoryEnum == null){
                return new ArrayList<>();
            }
            return getDiyDrillDownDimByMetricCategoryEnum(diyMetricCategoryEnum);
        }


    }

    public static DrillDownDim getDrillDownDimByChineseName(String chineseName) {
        for (DrillDownDim drillDownDim : DrillDownDim.values()) {
            if (drillDownDim.getChineseName().equals(chineseName)) {
                return drillDownDim;
            }
        }
        return null;
    }


}
