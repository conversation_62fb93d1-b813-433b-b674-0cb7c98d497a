package com.ctrip.tour.business.dashboard.sightArchives.entity.domesticMetricEntity;

import com.google.type.Decimal;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;
import java.util.List;

@Entity
@Getter
@Setter
public class Domestic9TargetBO {
    // 大区范围
    String regionNames;
    // 省份范围
    String provinceNames;
    // 类型特色品类0，重点品类1
    Integer categoryExamineMode;
    Double target;
}
