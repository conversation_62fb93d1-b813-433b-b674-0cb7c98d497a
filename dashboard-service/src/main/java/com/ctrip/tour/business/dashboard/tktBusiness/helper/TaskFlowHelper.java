package com.ctrip.tour.business.dashboard.tktBusiness.helper;

import com.alibaba.fastjson.JSON;
import com.ctrip.ibu.platform.shark.sdk.api.Shark;
import com.ctrip.soa._24922.*;
import com.ctrip.soa._27181.GetRawDataResponseType;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.SqlParamterBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bo.ExamineConfigBo;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardEmployeeInfoDao;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardExamineeConfigV2Dao;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardOrganizationInfoDao;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardEmployeeInfo;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardExamineeConfigV2;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardOrganizationInfo;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.ctrip.tour.business.dashboard.utils.MapperUtil;
import com.ctrip.tour.business.dashboard.utils.UserUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.sql.SQLException;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class TaskFlowHelper {


    private static final Map<String,String> metricSortMap = new HashMap<>();

    static {
        metricSortMap.put("ontime_complete_rate","asc");
        metricSortMap.put("overtime_event_cnt","desc");
        metricSortMap.put("average_process_time","desc");
        metricSortMap.put("new_task_cnt","desc");
    }

    private static final Map<String,String> metricTimeColumnMap = new HashMap<>();   //指标对应的特殊时间字段
    static {
        metricTimeColumnMap.put("new_task_cnt","send_date");
    }

    private static final Map<String,List<String>> trendLineMetricMap = new HashMap<>();   //趋势线返回的指标
    static {
        trendLineMetricMap.put("ontime_complete_rate",Lists.newArrayList("ontime_complete_rate","complete_rate","complete_task_cnt","ontime_complete_task_cnt","recieve_task_cnt"));
        trendLineMetricMap.put("overtime_event_cnt",Lists.newArrayList("overtime_event_cnt"));
        trendLineMetricMap.put("average_process_time",Lists.newArrayList("average_process_time"));
        trendLineMetricMap.put("new_task_cnt",Lists.newArrayList("new_task_cnt"));
    }

    private static final Map<String,String> pepGroupMap = new HashMap<>();   //统计范围
    static {
        pepGroupMap.put("domestic","1");  //国内业务
        pepGroupMap.put("oversea","2");   //海外业务
        pepGroupMap.put("operate","3");   //信息运营
    }

    public static Map<String,TaskStatisticalScope> getTaskStatisticalScopeMap(){
        Map<String,TaskStatisticalScope> taskStatisticalScopeMap = new LinkedHashMap<>();

        TaskStatisticalScope domesticScope = new TaskStatisticalScope();
        String domesticValue = Shark.getByLocale(100038120,"taskboard.enum.domestic", UserUtil.getVbkLocale());
        if(StringUtils.isBlank(domesticValue)){
            domesticValue = "国内业务";  //NOSONAR
        }
        domesticScope.setValue(domesticValue);
        domesticScope.setCode("domestic");

        TaskStatisticalScope overseaScope = new TaskStatisticalScope();
        String overseaValue = Shark.getByLocale(100038120,"taskboard.enum.oversea",UserUtil.getVbkLocale());
        if(StringUtils.isBlank(overseaValue)){
            overseaValue = "海外业务";   //NOSONAR
        }
        overseaScope.setValue(overseaValue);
        overseaScope.setCode("oversea");

        TaskStatisticalScope infoOperaScope = new TaskStatisticalScope();
        String operateValue = Shark.getByLocale(100038120,"taskboard.enum.operate",UserUtil.getVbkLocale());
        if(StringUtils.isBlank(operateValue)){
            operateValue = "信息运营";  //NOSONAR
        }
        infoOperaScope.setValue(operateValue);
        infoOperaScope.setCode("operate");

        taskStatisticalScopeMap.put("domesticEmployee",domesticScope);
        taskStatisticalScopeMap.put("overseaEmployee",overseaScope);
        taskStatisticalScopeMap.put("infoOperaEmployee",infoOperaScope);

        return taskStatisticalScopeMap;
    }
    public static String getMetricTimeColumn(String metric){
        return metricTimeColumnMap.getOrDefault(metric,"the_date");
    }


    public static SqlParamterBean getDimRequestSqlBean(Map<String,String> andMap,
                                                       String d){
        SqlParamterBean bean = new SqlParamterBean();
        bean.setId(74L);
        bean.setGroupList(Lists.newArrayList("biz_category_code","biz_category_name","task_collection_code","task_collection_name","task_type_code","task_type_name"));
//        bean.setGroupList(Lists.newArrayList("biz_category_code","task_collection_code","task_type_code"));
        andMap.put("query_d",d);
        bean.setAndMap(andMap);
        return bean;
    }

    public static SqlParamterBean getTrendLineBean(GetTaskFlowTrendlineDataRequestType request,
                                                   Map<String, String> andMap,
                                                   String d) throws ParseException {
        SqlParamterBean bean = new SqlParamterBean();
        bean.setId(74L);

        Map<String, String> orMap = new HashMap<>();
//        setBaseParamters(request.getDateType(),
//                d,
//                false,
//                andMap,
//                request.getTaskDimInfoList(),
//                orMap,
//                bean,
//                request.getStartDate(),
//                request.getEndDate());
        andMap.put("query_d", d);
        String dateRange = DateUtil.getTimeRange(request.getDateType(), d, false, request.getStartDate(), request.getEndDate());  //时间范围拼接

        andMap.put("the_date", dateRange);
        String timeColumn = getMetricTimeColumn(request.getTrendlineMetric());
        andMap.put(timeColumn, dateRange);

        andMap.put("pep_group",pepGroupMap.get(request.getStatisticalScope()));  //统计范围：1-国内业务; 2-海外业务; 3-信息运营
        TaskLevelScoreRange taskLevelScoreRange = request.getTaskLevelScoreRange();
        if(taskLevelScoreRange!=null && taskLevelScoreRange.getMinScore()!=null && taskLevelScoreRange.getMaxScore()!=null){
            andMap.put("task_level_score",taskLevelScoreRange.getMinScore()+"|"+taskLevelScoreRange.getMaxScore());
        }
        bean.setAndMap(andMap);

        setTaskDimInfoCondition(orMap, request.getTaskDimInfoList()); //任务类型筛选条件拼接
        bean.setOrMap(orMap);


        List<String> groupList = Lists.newArrayList(timeColumn);
        bean.setGroupList(groupList);

        bean.setOrderList(Lists.newArrayList(timeColumn));
        bean.setOrderTypeList(Lists.newArrayList("asc"));

        return bean;

    }

    /**
     * 如果不传或者只有一级 那么按一级下钻
     * 如果传了三级 则按三级下钻
     * 如果穿了二级  则按二级下钻
     * @param orMap
     * @return
     */
    private static List<String> getTrendLineList(Map<String, String> orMap) {
        //默认情况是按一级下钻的
//        List<String> groupList = Lists.newArrayList("biz_category_code", "biz_category_name");
//        if (orMap.containsKey("task_type_code")) {
//            //有三级  则要下钻到三级
//            groupList.add("task_collection_code");
//            groupList.add("task_collection_name");
//            groupList.add("task_type_code");
//            groupList.add("task_type_name");
//        } else if (orMap.containsKey("task_collection_code")) {
//            //有二级的时候 则要下钻到二级
//            groupList.add("task_collection_code");
//            groupList.add("task_collection_name");
//        }
//

        List<String> groupList = Lists.newArrayList("the_date");
        return groupList;
    }

    public static SqlParamterBean getMetricCardSqlBean(GetTaskFlowMetricCardDataRequestType request,
                                                       Map<String, String> andMap,
                                                       String d,
                                                       Boolean isPopTime) throws ParseException {
        SqlParamterBean bean = new SqlParamterBean();
        bean.setId(74L);

        Map<String, String> orMap = new HashMap<>();
        setBaseParamters(request.getDateType(),
                d,
                isPopTime,
                andMap,
                request.getTaskDimInfoList(),
                orMap,
                bean,
                request.getStartDate(),
                request.getEndDate(),
                request.getStatisticalScope(),
                request.getTaskLevelScoreRange());

        return bean;
    }

    /**
     * 填充时间和任务类型筛选项
      * @param dateType
     * @param d
     * @param andMap
     * @param taskDimInfoList
     * @param orMap
     */
//    private static void setBaseParamters(String dateType,
//                                         String d,
//                                         Boolean isPopTime,
//                                         Map<String, String> andMap,
//                                         List<TaskDimInfo> taskDimInfoList,
//                                         Map<String, String> orMap,
//                                         SqlParamterBean bean) throws ParseException {
//        andMap.put("query_d", d);
//        String dateRange = DateUtil.getTimeRange(dateType, d, isPopTime,"","");
//        andMap.put("the_date", dateRange);
//        bean.setAndMap(andMap);
//
//        setTaskDimInfoCondition(orMap, taskDimInfoList);
//        bean.setOrMap(orMap);
//    }

    /**
     *
     * @param dateType
     * @param d
     * @param isPopTime
     * @param andMap
     * @param taskDimInfoList
     * @param orMap
     * @param bean
     * @param startDate
     * @param endDate
     */
    private static void setBaseParamters(String dateType,
                                         String d,
                                         Boolean isPopTime,
                                         Map<String, String> andMap,
                                         List<TaskDimInfo> taskDimInfoList,
                                         Map<String, String> orMap,
                                         SqlParamterBean bean,
                                         String startDate,
                                         String endDate,
                                         String pepGroup,
                                         TaskLevelScoreRange taskLevelScoreRange) throws ParseException {
        andMap.put("query_d", d);
        String dateRange = DateUtil.getTimeRange(dateType, d, isPopTime, startDate, endDate);  //时间范围拼接
        andMap.put("the_date", dateRange);
        andMap.put("pep_group",pepGroupMap.get(pepGroup));  //统计范围：1-国内业务/2-海外业务/3-信息运营
        if(taskLevelScoreRange!=null && taskLevelScoreRange.getMinScore()!=null && taskLevelScoreRange.getMaxScore()!=null){
            andMap.put("task_level_score",taskLevelScoreRange.getMinScore()+"|"+taskLevelScoreRange.getMaxScore());
        }
        bean.setAndMap(andMap);

        setTaskDimInfoCondition(orMap, taskDimInfoList); //任务类型筛选条件拼接
        bean.setOrMap(orMap);
    }



    public static SqlParamterBean getAllSubordinateTableBean(GetTaskFlowTableDataRequestType request,
                                                             Map<String, String> andMap,
                                                             String d) throws ParseException {
        SqlParamterBean bean = new SqlParamterBean();
        bean.setId(74L);

        Map<String, String> orMap = new HashMap<>();
        setBaseParamters(request.getDateType(),
                d,
                false,
                andMap,
                request.getTaskDimInfoList(),
                orMap,
                bean,
                request.getStartDate(),
                request.getEndDate(),
                request.getStatisticalScope(),
                request.getTaskLevelScoreRange());

        bean.setGroupList(Lists.newArrayList("domain_name","display_name","region_name","region_id"));

        String drillDownMetric = getActualMetric(request.getDrilldownMetric());
        String sort = metricSortMap.get(drillDownMetric);
        bean.setOrderList(Lists.newArrayList(drillDownMetric,"domain_name"));
        bean.setOrderTypeList(Lists.newArrayList(sort,"desc"));

        bean.setPageNo(request.getPageNo());
        bean.setPageSize(request.getPageSize());

        return bean;
    }


    public static SqlParamterBean getTaskTableBean(GetTaskFlowTableDataRequestType request,
                                                   Map<String, String> andMap,
                                                   String d) throws ParseException {
        SqlParamterBean bean = new SqlParamterBean();
        bean.setId(74L);

        Map<String, String> orMap = new HashMap<>();
        setBaseParamters(request.getDateType(),
                d,
                false,
                andMap,
                request.getTaskDimInfoList(),
                orMap,
                bean,
                request.getStartDate(),
                request.getEndDate(),
                request.getStatisticalScope(),
                request.getTaskLevelScoreRange());

        List<String> groupList = getTaskGroupList(orMap);
        bean.setGroupList(groupList);

        String drillDownMetric = getActualMetric(request.getDrilldownMetric());
        String sort = metricSortMap.get(drillDownMetric);
        bean.setOrderList(Lists.newArrayList(drillDownMetric, groupList.get(groupList.size() - 1)));
        bean.setOrderTypeList(Lists.newArrayList(sort, "desc"));

        bean.setPageNo(request.getPageNo());
        bean.setPageSize(request.getPageSize());

        return bean;

    }

    public static SqlParamterBean getTaskDetailBean(GetTaskFlowMetricCardDataRequestType request,
                                                    Map<String, String> andMap,
                                                    String d) throws ParseException{
        SqlParamterBean bean = new SqlParamterBean();
        bean.setId(75L);

        Map<String, String> orMap = new HashMap<>();
        setBaseParamters(request.getDateType(),
                d,
                false,
                andMap,
                request.getTaskDimInfoList(),
                orMap,
                bean,
                request.getStartDate(),
                request.getEndDate(),
                request.getStatisticalScope(),
                request.getTaskLevelScoreRange());

        return bean;
    }

    public static SqlParamterBean getAllSubordinateTableDownloadBean(GetTaskFlowMetricCardDataRequestType request,
                                                             Map<String, String> andMap,
                                                             String d) throws ParseException {
        SqlParamterBean bean = new SqlParamterBean();
        bean.setId(74L);

        Map<String, String> orMap = new HashMap<>();
        setBaseParamters(request.getDateType(),
                d,
                false,
                andMap,
                request.getTaskDimInfoList(),
                orMap,
                bean,
                request.getStartDate(),
                request.getEndDate(),
                request.getStatisticalScope(),
                request.getTaskLevelScoreRange());

        bean.setGroupList(Lists.newArrayList("domain_name","display_name"));

//        String drillDownMetric = getActualMetric(request.getDrilldownMetric());
//        String sort = metricSortMap.get(drillDownMetric);
//        bean.setOrderList(Lists.newArrayList(drillDownMetric,"domain_name"));
//        bean.setOrderTypeList(Lists.newArrayList(sort,"desc"));

//        bean.setPageNo(request.getPageNo());
//        bean.setPageSize(request.getPageSize());

        return bean;
    }

    public static SqlParamterBean getRegionTableBean(GetTaskFlowTableDataRequestType request,
                                                     String d,
                                                     RemoteConfig remoteConfig) throws ParseException {
        SqlParamterBean bean = new SqlParamterBean();
        bean.setId(74L);

        String drillDownMetric = getActualMetric(request.getDrilldownMetric());
        String sort = metricSortMap.get(drillDownMetric);
        bean.setOrderList(Lists.newArrayList(drillDownMetric, "region_name"));
        bean.setOrderTypeList(Lists.newArrayList(sort, "desc"));
        bean.setGroupList(Lists.newArrayList("region_name", "region_id"));

        Map<String, String> andMap = new HashMap<>();
        String regionRankId = remoteConfig.getExternalConfig("regionRankId");
        andMap.put("region_id", regionRankId);
        bean.setAndMap(andMap);
        Map<String, String> orMap = new HashMap<>();
        setBaseParamters(request.getDateType(),
                d,
                false,
                andMap,
                request.getTaskDimInfoList(),
                orMap,
                bean,
                request.getStartDate(),
                request.getEndDate(),
                request.getStatisticalScope(),
                request.getTaskLevelScoreRange());

        bean.setPageNo(request.getPageNo());
        bean.setPageSize(request.getPageSize());




        return bean;

    }


    /**
     * 如果不传或者只有一级 那么按一级下钻
     * 如果传了三级 则按三级下钻
     * 如果穿了二级  则按二级下钻
     * @param orMap
     * @return
     */
    private static List<String> getTaskGroupList(Map<String, String> orMap) {
        //默认情况是按一级下钻的
        List<String> groupList = Lists.newArrayList("biz_category_code", "biz_category_name");
        if (orMap.containsKey("task_type_code")) {
            //有三级  则要下钻到三级
            groupList.add("task_collection_code");
            groupList.add("task_collection_name");
            groupList.add("task_type_code");
            groupList.add("task_type_name");
        } else if (orMap.containsKey("task_collection_code")) {
            //有二级的时候 则要下钻到二级
            groupList.add("task_collection_code");
            groupList.add("task_collection_name");
        }
        return groupList;
    }


    private static void setTaskDimInfoCondition(Map<String, String> orMap,
                                                List<TaskDimInfo> taskDimInfoList) {

        if (GeneralUtil.isNotEmpty(taskDimInfoList)) {
            Map<String, List<String>> dimListMap = new HashMap<>();
            for (TaskDimInfo taskDimInfo : taskDimInfoList) {
                String type = getActualType(taskDimInfo.getType());
                String code = taskDimInfo.getCode();

                List<String> codeList = dimListMap.get(type);
                if (GeneralUtil.isEmpty(codeList)) {
                    codeList = Lists.newArrayList();
                }
                codeList.add(code);
                dimListMap.put(type, codeList);
            }
            for (Map.Entry<String, List<String>> entry : dimListMap.entrySet()) {
                String type = entry.getKey();
                List<String> codeList = entry.getValue();
                orMap.put(type, StringUtils.join(codeList, "|"));
            }
        }
    }

    private static String getActualType(String type) {
        if ("biz_category_code".equals(type)) {
            return "biz_category_code";
        } else if ("task_collection_code".equals(type)) {
            return "task_collection_code";
        } else {
            return "task_type_code";
        }
    }


    public static String getActualMetric(String metric){
        if ("ontime_complete_rate".equals(metric)) {
            return "ontime_complete_rate";
        } else if ("overtime_event_cnt".equals(metric)) {
            return "overtime_event_cnt";
        } else if("new_task_cnt".equals(metric)){
            return "new_task_cnt";
        }else {
            return "average_process_time";
        }
    }



    /**
     * 填充指标卡数据
     * @param res
     * @param dimMap
     */
    public static void processMetricCardData(GetRawDataResponseType res,
                                             Map<String, Double> dimMap,
                                             String suffix) {
        List<String> originMetricList = res.getMetricList();
        List<String> metricList = originMetricList.stream()
                .map(metric -> metric + suffix)
                .collect(Collectors.toList());
        ChartHelper.fillOverallDimMap(MapperUtil.str2ListList(res.getResult(), Object.class), metricList, dimMap);
    }



    public static void processGroupName(List<TableDataItem> tableDataItemList, Map<String, Map<String, String>> dataMap){
        for (TableDataItem tableDataItem : tableDataItemList) {
            Map<String, String> fieldMap = tableDataItem.getFieldMap();
            for (String key : fieldMap.keySet()) {
                String value = fieldMap.get(key);
                if(StringUtils.isBlank(value)){
                    String k = key.replace("name", "code");
                    String v = fieldMap.get(k);
                    fieldMap.put(key, dataMap.get(k).get(v));
                }
            }
        }
    }

    public static void processTrendLine(GetRawDataResponseType res,
                                        List<TrendLineDataItem> trendLineDataItemList,
                                        GetTaskFlowTrendlineDataRequestType requestType) {
        List<List<Object>> rawResultList = MapperUtil.str2ListList(res.getResult(), Object.class);
        List<String> metricList = res.getMetricList();

        List<TrendLineDataItem> allMetricItemList = new ArrayList<>();
        for(List<Object> rawResult : rawResultList){

            String time = (String) rawResult.get(0);
            for(int i = 0; i<metricList.size(); i++){
                String metricName = metricList.get(i); //指标名
                Object value = rawResult.get(i+1); //指标值

                TrendLineDataItem trendLineDataItem = new TrendLineDataItem();
                trendLineDataItem.setName(metricName);
                trendLineDataItem.setTime(time);
                if(value!=null){
                    trendLineDataItem.setValue(Double.valueOf(String.valueOf(value)));
                }

                allMetricItemList.add(trendLineDataItem);
            }
        }

        String trendLineMetric = requestType.getTrendlineMetric();
        List<String> returnMetricNameList = trendLineMetricMap.get(trendLineMetric);

        for(TrendLineDataItem trendLineDataItem : allMetricItemList){
            if(returnMetricNameList.contains(trendLineDataItem.getName())){
                trendLineDataItemList.add(trendLineDataItem);
            }
        }

    }

    /**
     * 填充表格基础数据
     * @param res
     * @param tableDataItemList
     */
    public static void processTableData(GetRawDataResponseType res,
                                        List<TableDataItem> tableDataItemList) {
        List<List<Object>> rawResultList = MapperUtil.str2ListList(res.getResult(), Object.class);
        List<String> groupList = res.getGroupList();
        List<String> metricList = res.getMetricList();
        ChartHelper.fillCommmonTableData(tableDataItemList, groupList, metricList, new ArrayList<>(), rawResultList, new ArrayList<>());
    }

    /**
     * 将所有下属(包括自己)放入到andMap中
     *
     * @param andMap
     * @param employeeInfo
     * @param remoteConfig
     * @param organizationInfoDao
     * @param employeeInfoDao
     */
    public static void setAllSubordinateCondition(Map<String, String> andMap,
                                                  BusinessDashboardEmployeeInfo employeeInfo,
                                                  RemoteConfig remoteConfig,
                                                  BusinessDashboardOrganizationInfoDao organizationInfoDao,
                                                  BusinessDashboardEmployeeInfoDao employeeInfoDao) throws SQLException {

//        String domesticDeptId = remoteConfig.getExternalConfig("domesticDeptId");
//        String overseaDeptId = remoteConfig.getExternalConfig("overseaDeptId");

        List<BusinessDashboardOrganizationInfo> organizationInfoList = organizationInfoDao.queryByLeaderEmpCode(employeeInfo.getEmpCode());
        List<String> nodeOrgIdList = organizationInfoList.stream().map(BusinessDashboardOrganizationInfo::getNodeOrgId).collect(Collectors.toList());

        //问过kzhangm  可删掉
//        //说明是国内leader 或者是 海外leader  他们的所有下属数据是表里的全部数据(后续设计海外表的时候记得和国内表直接拆开)  因此不需要额外限制条件
//        if (nodeOrgIdList.contains(domesticDeptId) || nodeOrgIdList.contains(overseaDeptId)) {
//            return;
//        }

        //华西大区运营特殊处理
        String specailRegionOperate = remoteConfig.getExternalConfig("specailRegionOperate");
        if (specailRegionOperate.equals(employeeInfo.getDomainName())) {
            setSpecailRegionOperateAllSubordinateCondition(andMap, remoteConfig, employeeInfoDao);
            return;
        }

        List<String> domainNameList;
        if(nodeOrgIdList.isEmpty()){
            //纯打工人
            domainNameList = new ArrayList<>();
        }else{
            domainNameList = employeeInfoDao.getAllSubordinateDomainNameList(nodeOrgIdList);
        }
        //我的所有下属不包含我自己  把我自己加上
        domainNameList.add(employeeInfo.getDomainName());
        andMap.put("domain_name", StringUtils.join(domainNameList, "|"));
    }


    /**
     * 处理华西大区运营的特殊场景
     *
     * @param andMap
     * @param remoteConfig
     * @param employeeInfoDao
     * @throws SQLException
     */
    private static void setSpecailRegionOperateAllSubordinateCondition(Map<String, String> andMap,
                                                                       RemoteConfig remoteConfig,
                                                                       BusinessDashboardEmployeeInfoDao employeeInfoDao) throws SQLException {
        String specialRegionId = remoteConfig.getExternalConfig("specialRegionId");
        List<String> domainNameList = employeeInfoDao.getAllSubordinateDomainNameList(Lists.newArrayList(specialRegionId));
        //宋薇已经在里面了  不需要额外添加了
        andMap.put("domain_name", StringUtils.join(domainNameList, "|"));
    }


    public static List<String> getDirectSubordinateEmpCodeList(BusinessDashboardEmployeeInfo employeeInfo,
                                                               RemoteConfig remoteConfig,
                                                               BusinessDashboardOrganizationInfoDao organizationInfoDao,
                                                               BusinessDashboardEmployeeInfoDao employeeInfoDao) throws SQLException {
        //华西大区运营特殊处理
        String specailRegionOperate = remoteConfig.getExternalConfig("specailRegionOperate");
        if (specailRegionOperate.equals(employeeInfo.getDomainName())) {
            return getSpecialRegionOperateDirectSubordinateEmpCodeList(remoteConfig, organizationInfoDao, employeeInfoDao);
        }

        return employeeInfoDao.getDirectSubordinateEmpCodeList(employeeInfo.getDomainName());
    }


    /**
     * 处理华西大区运营的特殊场景
     * @param remoteConfig
     * @param organizationInfoDao
     * @param employeeInfoDao
     * @return
     * @throws SQLException
     */
    private static List<String> getSpecialRegionOperateDirectSubordinateEmpCodeList(RemoteConfig remoteConfig,
                                                                                    BusinessDashboardOrganizationInfoDao organizationInfoDao,
                                                                                    BusinessDashboardEmployeeInfoDao employeeInfoDao) throws SQLException {

        String domesticDeptId = remoteConfig.getExternalConfig("domesticDeptId");
        String specialRegionId = remoteConfig.getExternalConfig("specialRegionId");
        BusinessDashboardOrganizationInfo organizationInfo = organizationInfoDao.queryByOrgId(domesticDeptId);
        String leaderEmpCode = organizationInfo.getLeaderEmpCode();
        return employeeInfoDao.getDirectSubordinateEmpCodeList(leaderEmpCode, specialRegionId);
    }


    public static String getDomesticDeptLeader(RemoteConfig remoteConfig,
                                               BusinessDashboardOrganizationInfoDao organizationInfoDao) throws SQLException {
        String domesticDeptId = remoteConfig.getExternalConfig("domesticDeptId");
        BusinessDashboardOrganizationInfo organizationInfo = organizationInfoDao.queryByOrgId(domesticDeptId);
        return organizationInfo.getLeaderEmpCode();
    }




    public static String getActualDomainName(String domainName,
                                             List<BusinessDashboardExamineeConfigV2> examineeConfigV2List,
                                             RemoteConfig remoteConfig,
                                             BusinessDashboardEmployeeInfoDao employeeInfoDao) throws SQLException {

        //兜底 如果没有配置业绩看板权限 直接返回
        if (GeneralUtil.isEmpty(examineeConfigV2List)) {
            return domainName;
        }


        //华西大区运营权限
        String specailRegionOperate = remoteConfig.getExternalConfig("specailRegionOperate");
        if (specailRegionOperate.equals(domainName)) {
            return domainName;
        }

        ExamineConfigBo bo = new ExamineConfigBo();
        //去捞最新季度gmv的配置读取角色(所有的人都会配置收入力)
        MetricInfoBean metricInfoBean = bo.getSingleMetricInfoBean(examineeConfigV2List, remoteConfig, "1");
        //角色明细 from 张景昌
        //1 一线运营   2  一线BD   3 省长  4 区域经理
        //5 大区总    6  大区运营  7  中心人员
        Integer role = metricInfoBean.getRole();
        //非华西大区的大区运营需要转化为该大区的大区总
        if (role == 6) {
            BusinessDashboardEmployeeInfo leaderEmployeeInfo = employeeInfoDao.getLeaderEmployeeInfo(domainName);
            return leaderEmployeeInfo.getDomainName();
        }
        //其他人不需要处理
        return domainName;
    }


    public static BusinessDashboardEmployeeInfo getActualEmployeeInfo(String empCode,
                                                                      String d,
                                                                      RemoteConfig remoteConfig,
                                                                      BusinessDashboardExamineeConfigV2Dao examineConfigV2Dao,
                                                                      BusinessDashboardEmployeeInfoDao employeeInfoDao) throws SQLException, ParseException {
        BusinessDashboardEmployeeInfo employeeInfo = employeeInfoDao.queryByEmpCode(empCode);
        //如果不在全量人员表里  直接返回
        if (GeneralUtil.isEmpty(employeeInfo)){
            return null;
        }
        String domainName = employeeInfo.getDomainName();

        String year = DateUtil.getActualYearOfD(d);
        String quarter = DateUtil.getActualQuarterOfD(d);

        //获取国内最新配置(可能是取不到的)
        List<BusinessDashboardExamineeConfigV2> examineeConfigV2List = examineConfigV2Dao.querySpecificPeriodAllMetricConfig(domainName, d, year, quarter, null);

        domainName = getActualDomainName(domainName, examineeConfigV2List, remoteConfig, employeeInfoDao);

        return employeeInfoDao.queryByDomainName(domainName);

    }


    public static void sortTableDataItemList(List<TableDataItem> tableDataItemList,
                                             String drilldownMetric) {

        String actualMetric = getActualMetric(drilldownMetric);
        String sort = metricSortMap.get(actualMetric);
        tableDataItemList.sort(new Comparator<TableDataItem>() {
            @Override
            public int compare(TableDataItem o1, TableDataItem o2) {
                Double value1 = o1.getDimMap().getOrDefault(actualMetric, 0d);
                Double value2 = o2.getDimMap().getOrDefault(actualMetric, 0d);
                String empCode1 = o1.getFieldMap().getOrDefault("emp_code", "");
                String empCode2 = o2.getFieldMap().getOrDefault("emp_code", "");

                // 特殊处理：接收任务数为0的人员排在最后面（无任务的人员不参与主要排序）
                Double recieveTaskCnt1 = o1.getDimMap().getOrDefault("recieve_task_cnt", 0d);
                Double recieveTaskCnt2 = o2.getDimMap().getOrDefault("recieve_task_cnt", 0d);
                boolean isRecieveTask1Zero = Double.compare(recieveTaskCnt1, 0d) == 0;
                boolean isRecieveTask2Zero = Double.compare(recieveTaskCnt2, 0d) == 0;

                // 如果其中一个接收任务数为0，另一个不为0，将0值排在后面
                if (isRecieveTask1Zero && !isRecieveTask2Zero) {
                    return 1;  // o1的接收任务数为0，排在后面
                }
                if (!isRecieveTask1Zero && isRecieveTask2Zero) {
                    return -1; // o2的接收任务数为0，排在后面
                }

                // 如果都没有接收任务，按员工工号升序排序（保证稳定性）
                if (isRecieveTask1Zero && isRecieveTask2Zero) {
                    return empCode1.compareTo(empCode2);
                }

                // 如果都有接收任务，继续执行后面的正常排序逻辑
                if (Double.compare(value1, value2) == 0) {
                    return empCode1.compareTo(empCode2);
                }
                if ("asc".equals(sort)) {
                    return value1.compareTo(value2);
                } else {
                    return value2.compareTo(value1);
                }
            }
        });
    }


    public static void makeUpTableData(List<TableDataItem> tableDataItemList,
                                       BusinessDashboardEmployeeInfoDao employeeInfoDao) throws SQLException {
        if (GeneralUtil.isEmpty(tableDataItemList)) {
            return;
        }
        List<String> empCodeList = tableDataItemList.stream()
                .map(item -> item.getFieldMap().get("emp_code"))
                .collect(Collectors.toList());
        List<BusinessDashboardEmployeeInfo> employeeInfoList = employeeInfoDao.queryByEmpCodeList(empCodeList);
        Map<String, BusinessDashboardEmployeeInfo> employeeInfoMap = employeeInfoList.stream()
                .collect(Collectors.toMap(BusinessDashboardEmployeeInfo::getEmpCode, employeeInfo -> employeeInfo,
                        (oldKey, newKey) -> newKey));

        for (TableDataItem tableDataItem : tableDataItemList) {
            String empCode = tableDataItem.getFieldMap().get("emp_code");
            BusinessDashboardEmployeeInfo employeeInfo = employeeInfoMap.get(empCode);
            tableDataItem.getFieldMap().put("display_name", employeeInfo.getDisplayName());
        }
    }


    public static void makeUpTableRegionData(List<TableDataItem> tableDataItemList, Map<String, String> regionManagerMap){
        if(!GeneralUtil.isEmpty(tableDataItemList)){
            for (TableDataItem tableDataItem : tableDataItemList) {
                Map<String, String> fieldMap = tableDataItem.getFieldMap();
                String regionInfo = regionManagerMap.get(fieldMap.get("emp_code"));
                String[] regionList = regionInfo.split("\\|");
                String regionName = regionList[0];
                String regionId = regionList[1];
                fieldMap.put("region_id", regionId);
                fieldMap.put("region_name", regionName);
            }
        }
    }


    public static void makeUpTableData(List<TableDataItem> tableDataItemList,
                                       GetRawDataResponseType response) {
        if (GeneralUtil.isEmpty(tableDataItemList)) {
            return;
        }
        List<String> groupList = response.getGroupList();
        String displayNameMetric = groupList.get(groupList.size() - 1);
        for (TableDataItem tableDataItem : tableDataItemList) {
            String displayName = tableDataItem.getFieldMap().get(displayNameMetric);
            tableDataItem.getFieldMap().put("display_name", displayName);
            StringBuilder sb = new StringBuilder();
            for (int i = 1; i < groupList.size(); i = i + 2) {
                sb.append(tableDataItem.getFieldMap().get(groupList.get(i))).append("/");
            }
            sb.deleteCharAt(sb.length() - 1);
            tableDataItem.getFieldMap().put("click_name", sb.toString());
        }
    }

}
