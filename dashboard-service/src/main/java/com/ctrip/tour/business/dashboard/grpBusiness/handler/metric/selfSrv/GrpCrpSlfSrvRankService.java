package com.ctrip.tour.business.dashboard.grpBusiness.handler.metric.selfSrv;

import java.sql.SQLException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.IntStream;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ctrip.tour.business.dashboard.grpBusiness.dao.starrocks.StarRocksCommonDao;
import com.google.common.collect.Maps;

/**
 * <AUTHOR>
 * @Date 2025/4/15
 */
@Service
public class GrpCrpSlfSrvRankService {

    @Autowired
    private StarRocksCommonDao starRocksCommonDao;


    public Integer[] getPmRank(String endDate, String empCode) throws SQLException {

        Integer[] ranks = new Integer[4];

        String pdSql = "select \n" +
                "max(partition_d) as maxD\n" +
                "from adm_sev_grp_platform_self_srv_rank_df\n" +
                "where stat_quarter='"+getQuater(endDate)+"' and business_team='"+empCode+"'\n";

        List<Map<String, Object>> pdRst = starRocksCommonDao.query(pdSql, Maps.newHashMap());
        if (CollectionUtils.isEmpty(pdRst)) {
            return null;
        }

        Date maxD = (Date) pdRst.get(0).get("maxD");

        String sql = "select \n" +
                "replace(max(concat(stat_date,self_srv_rank)),max(stat_date),'') as self_srv_rank,area\n" +
                "from adm_sev_grp_platform_self_srv_rank_df\n" +
                "where stat_quarter='"+getQuater(endDate)+"' and business_team='"+empCode+"'\n" +
                "and partition_d='"+ maxD +"' group by area\n";

        List<Map<String, Object>> query = starRocksCommonDao.query(sql, Maps.newHashMap());

        if (CollectionUtils.isEmpty(query)) {
            return null;
        }

        for (Map<String, Object> rankRow : query) {
            String area = (String) rankRow.get("area");
            if (StringUtils.equals(area, "国内")) {//NOSONAR
                String maxSql = "SELECT max(self_srv_rank) as maxRank\n" +
                        "FROM adm_sev_grp_platform_self_srv_rank_df\n" +
                        "WHERE partition_d = '"+maxD+"' and area='国内'\n" +//NOSONAR
                        "    AND stat_quarter = '"+getQuater(endDate)+"'\n";


                List<Map<String, Object>> maxQuery = starRocksCommonDao.query(maxSql, Maps.newHashMap());
            ranks[0] = Integer.parseInt((String) rankRow.get("self_srv_rank"));
            ranks[1] = (Integer) maxQuery.get(0).get("maxRank");

            } else if (StringUtils.equals(area, "海外")) {//NOSONAR
                String maxSql = "SELECT max(self_srv_rank) as maxRank\n" +
                        "FROM adm_sev_grp_platform_self_srv_rank_df\n" +
                        "WHERE partition_d = '"+maxD+"' and area='海外'\n" +//NOSONAR
                        "    AND stat_quarter = '"+getQuater(endDate)+"'\n";


                List<Map<String, Object>> maxQuery = starRocksCommonDao.query(maxSql, Maps.newHashMap());
                ranks[2] = Integer.parseInt((String) rankRow.get("self_srv_rank"));
                ranks[3] =(Integer) maxQuery.get(0).get("maxRank");
            }
        }
        return ranks;

    }

    private String getQuater(String dateStr) {
        LocalDate parse = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        int month = parse.getMonth().getValue();
        if (month <= 3) {
            return "Q1";
        } else if (month <= 6) {
            return "Q2";
        } else if (month <= 9) {
            return "Q3";
        } else {
            return "Q4";
        }
    }
}
