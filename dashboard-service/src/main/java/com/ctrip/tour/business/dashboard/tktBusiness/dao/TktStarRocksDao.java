package com.ctrip.tour.business.dashboard.tktBusiness.dao;


import com.alibaba.fastjson.JSONObject;
import com.ctrip.framework.clogging.common.exception.BusinessException;
import com.ctrip.soa._27181.PreparedParameterBean;
import com.ctrip.tour.business.dashboard.grpBusiness.config.StarRocksConnPool;
import com.ctrip.tour.business.dashboard.tktBusiness.select43556.InputArgumentException;
import com.ctrip.tour.business.dashboard.utils.ThreadUtil;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.*;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class TktStarRocksDao {
    public List<Map<String, Object>> getListResult(String sql, List<PreparedParameterBean> parameters) throws SQLException {
        List<Map<String, Object>> rsMapList = new ArrayList<>();
        //执行完毕释放资源
        try (Connection conn = StarRocksConnPool.getConnection("starrocks.xy");
             PreparedStatement ps = createPreparedStatement(conn, sql, parameters);
             ResultSet rs = ps.executeQuery()) {
            ResultSetMetaData meta = rs.getMetaData();
            int columnCount = meta.getColumnCount();
            // 处理ResultSet结果集
            while (rs.next()) {
                Map<String, Object> map = new HashMap<>();
                for (int i = 1; i <= columnCount; i++) {
                    map.put(meta.getColumnName(i), rs.getObject(i));
                }
                rsMapList.add(map);
            }
        } catch (SQLException e) {
            log.error("StarRocks getListResult error, sql: {}, parameters: {}", sql, parameters, e);
        }
        return rsMapList;
    }


    public JSONArray getListResult1(String sql, List<PreparedParameterBean> parameters) throws SQLException {

        JSONArray array = new JSONArray();

        //执行完毕释放资源
        try (Connection conn = StarRocksConnPool.getConnection("starrocks.xy");
             PreparedStatement ps = createPreparedStatement(conn, sql, parameters);
             ResultSet rs = ps.executeQuery()) {
            ResultSetMetaData meta = rs.getMetaData();
            int columnCount = meta.getColumnCount();
            // 处理ResultSet结果集
            while (rs.next()) {
                JSONArray jsonArray = transferResultToJSONArray(rs, meta, columnCount, 0);
                array.add(jsonArray);

            }
        } catch (SQLException e) {
            log.error("StarRocks getListResult error, sql: {}, parameters: {}", sql, parameters, e);
        }

        return array;
    }

    public static JSONArray transferResultToJSONArray(ResultSet rs, ResultSetMetaData meta, int columnCount, int type) throws SQLException {
        JSONArray jsonArray = new JSONArray();
        for (int columnIndex = 1; columnIndex <= columnCount; columnIndex++) {
            int columnType = meta.getColumnType(columnIndex);
            String result = getColumnValue(columnIndex, columnType, rs, type);
            jsonArray.add(result);
        }
        return jsonArray;
    }

    private static String getColumnValue(int columnIndex, int columnType, ResultSet rs, int type) throws SQLException {
        String resultValue;
        switch (columnType) {
            case Types.DOUBLE:
            case Types.DECIMAL:
                BigDecimal decimalValue = rs.getBigDecimal(columnIndex);
                resultValue = truncateDoubleDecimals(decimalValue, type);
                break;
            default:
                Object objectValue = rs.getObject(columnIndex);
                resultValue = String.valueOf(objectValue);
                break;
        }
        //如果是空值，则返回一个空（去除字符串null）
        if (StringUtils.isNotBlank(resultValue) && "null".equalsIgnoreCase(resultValue)) {
            resultValue = null;
        }
        return resultValue;
    }

    /**
     * @param value
     * @param type:0: 保留4位小数； 1：全部个数位返回
     * @return
     */
    public static String truncateDoubleDecimals(Object value, int type) {
        if (value == null) return null;
        //保留四位小数
        if (type == 0) {
            DecimalFormat df = new DecimalFormat("#.####");
            df.setRoundingMode(RoundingMode.HALF_UP);
            return df.format(value);
        } else {
            return value.toString();
        }

    }


    private PreparedStatement createPreparedStatement(Connection conn, String sql, List<PreparedParameterBean> parameters) throws SQLException {
        PreparedStatement ps = conn.prepareStatement(sql);
        int j = 1;
        for (PreparedParameterBean parameter : parameters) {
            //目前参数四种类型double varchar int long
            int type = parameter.getType();
            switch (type) {
                case 0:
                    ps.setDouble(j++, Double.parseDouble(parameter.getValue()));
                    break;
                case 1:
                    ps.setString(j++, parameter.getValue());
                    break;
                case 2:
                    ps.setInt(j++, Integer.parseInt(parameter.getValue()));
                    break;
                case 3:
                    ps.setLong(j++, Long.parseLong(parameter.getValue()));
                    break;
                default:
                    throw new InputArgumentException(10003);
            }
        }
        return ps;
    }


    public List<Map<String, Object>> getListResultNew(String sql, List<PreparedParameterBean> parameters) throws SQLException {
        List<Map<String, Object>> rsMapList = new ArrayList<>();
        log.info("position:" + ThreadUtil.getThreadStackTrace() + ", getListResultNew: " + sql + ";  parameters:" + JSONObject.toJSONString(parameters));
        //执行完毕释放资源
        try (Connection conn = StarRocksConnPool.getConnection("starrocks.xy");
             PreparedStatement ps = createPreparedStatementNew(conn, sql, parameters);
             ResultSet rs = ps.executeQuery()) {
            ResultSetMetaData meta = rs.getMetaData();
            int columnCount = meta.getColumnCount();
            // 处理ResultSet结果集
            while (rs.next()) {
                Map<String, Object> map = new HashMap<>();
                for (int i = 1; i <= columnCount; i++) {
                    map.put(meta.getColumnName(i), rs.getObject(i));
                }
                rsMapList.add(map);
            }
        } catch (SQLException e) {
            log.error("StarRocks getListResult error, sql: " + sql + ", parameters: " + JSONObject.toJSONString(parameters), e);
            throw new BusinessException("StarRocks getListResult error:" + sql, e);
        }
        return rsMapList;
    }


    private PreparedStatement createPreparedStatementNew(Connection conn, String sql, List<PreparedParameterBean> parameters) throws SQLException {
        PreparedStatement ps = conn.prepareStatement(sql);
        int j = 1;
        for (PreparedParameterBean parameter : parameters) {
            //目前参数四种类型double varchar int long
            int type = parameter.getType();
            switch (type) {
                case Types.DOUBLE:
                    ps.setDouble(j++, Double.parseDouble(parameter.getValue()));
                    break;
                case Types.VARCHAR:
                    ps.setString(j++, parameter.getValue());
                    break;
                case Types.INTEGER:
                    ps.setInt(j++, Integer.parseInt(parameter.getValue()));
                    break;
                case Types.BIGINT:
                    ps.setLong(j++, Long.parseLong(parameter.getValue()));
                    break;
                default:
                    throw new InputArgumentException(10003);
            }
        }
        return ps;
    }

}
