package com.ctrip.tour.business.dashboard.sightArchives.enums.sales;

import java.util.Arrays;
import java.util.List;

public enum SalesPieChartEnumType {

    PRODUCT_TYPE_DISTRIBUTION(1,"productType", "产品类型分布", "saleunit_class",true,true), //NOSONAR
    TICKET_TYPE_DISTRIBUTION(2,"ticketType", "票种人群分布", "property_value_name",true,true), //NOSONAR
    CHANNEL_DISTRIBUTION(3, "channel","渠道分布","dis_channel_type",false,false), //NOSONAR
    PLANE_TRAIN_CROSSOVER_PERCENTAGE(4, "planeTrainCrossover","机酒交叉占比", "is_fhp",false,false); //NOSONAR

    private final int id;
    private final String englishName;
    private final String chineseName;
    private final String drillDownColumn;
    private final boolean restrictTktBuType;
    private final boolean excludeUnkwnData;

    SalesPieChartEnumType(int id, String englishName, String chineseName, String drillDownColumn,boolean restrictTktBuType, boolean excludeUnkwnData) {
        this.id = id;
        this.englishName = englishName;
        this.chineseName = chineseName;
        this.drillDownColumn = drillDownColumn;
        this.restrictTktBuType = restrictTktBuType;
        this.excludeUnkwnData = excludeUnkwnData;
    }

    public int getId() {
        return id;
    }

    public String getEnglishName() {
        return englishName;
    }

    public String getChineseName() {
        return chineseName;
    }

    public String getDrillDownColumn() {
        return drillDownColumn;
    }

    public boolean isRestrictTktBuType() {
        return restrictTktBuType;
    }

    public boolean isExcludeUnkwnData() {
        return excludeUnkwnData;
    }

    public static List<SalesPieChartEnumType> getTicketBuPieChartEnumList() {
        return Arrays.asList(SalesPieChartEnumType.values());
    }

    public static List<SalesPieChartEnumType> getNotTicketBuPieChartEnumList() {
        return Arrays.asList(CHANNEL_DISTRIBUTION, PLANE_TRAIN_CROSSOVER_PERCENTAGE);
    }

}
