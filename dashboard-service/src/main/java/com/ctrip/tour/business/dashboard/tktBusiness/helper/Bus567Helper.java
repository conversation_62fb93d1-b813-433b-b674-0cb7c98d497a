package com.ctrip.tour.business.dashboard.tktBusiness.helper;

import com.ctrip.soa._24922.TableDataItem;
import com.ctrip.soa._24922.TrendLineDataItem;
import com.ctrip.soa._24922.TrendLineDetailInfo;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.ConfigImportException;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.ctrip.tour.business.dashboard.utils.MapperUtil;
import com.google.common.collect.Lists;

import java.text.ParseException;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/8/3
 */
public class Bus567Helper {

    public static List<String> getDimList(){
        return Lists.newArrayList("ci_viewspot_num","ci_viewspot_ratio","fi_viewspot_num",
                "fi_viewspot_ratio","mi_viewspot_num","mi_viewspot_ratio");
    }

    public static Map<String,String> getRatioTrendlineType(){
        Map<String,String> typeMap = new HashMap<>();
        typeMap.put("ci_viewspot_ratio","barChart");
        typeMap.put("fi_viewspot_ratio","lineChart");
        typeMap.put("mi_viewspot_ratio","lineChart");
        return typeMap;
    }



    /**
     * 计算非趋势线平均劣势数 劣势率
     * 并且判断了极端场景下gapDays为null的情况
     * @param dimMap
     * @param gapDays
     * @param addtionalGapDays
     */
    public static void calMetricCardAverageData(Map<String, Double> dimMap,
                                                Integer gapDays,
                                                Integer addtionalGapDays) {
        if (GeneralUtil.isEmpty(gapDays)) {
            return;
        }

        Integer actualGapDays = gapDays - addtionalGapDays;

        for (Map.Entry<String, Double> entry : dimMap.entrySet()) {
            String key = entry.getKey();
            Double value = entry.getValue();
            if (!GeneralUtil.isEmpty(value)) {
                dimMap.put(key, value / actualGapDays);
            }
        }
    }




    //计算表格数据的平均劣势数 劣势率
    public static void calTableAverageData(List<TableDataItem> tableDataItemList,
                                           Integer gapDays,
                                           Integer addtionalGapDays) {
        for (TableDataItem item : tableDataItemList) {
            calMetricCardAverageData(item.getDimMap(), gapDays, addtionalGapDays);
        }
    }

    public static void makeUpMetricData(Map<String, Double> dimMap) {
        String key1 = "ci_viewspot_ratio";
        Double value1 = dimMap.getOrDefault(key1, 0d);
        String key2 = "fi_viewspot_ratio";
        Double value2 = dimMap.getOrDefault(key2, 0d);
        String key3 = "mi_viewspot_ratio";
        Double value3 = dimMap.getOrDefault(key3, 0d);
        //劣势率目标固定为
        //核心 0
        //聚焦 0.01
        //大盘 0.03
        dimMap.put("ci_viewspot_ratio_gap", value1 - 0d);
        dimMap.put("fi_viewspot_ratio_gap", value2 - 0.01d);
        dimMap.put("mi_viewspot_ratio_gap", value3 - 0.03d);
        dimMap.put("ci_viewspot_ratio_trgt", 0d);
        dimMap.put("fi_viewspot_ratio_trgt", 0.01d);
        dimMap.put("mi_viewspot_ratio_trgt", 0.03d);

    }

    public static void makeUpTableData(List<TableDataItem> tableDataItemList) {
        for (TableDataItem item : tableDataItemList) {
            makeUpMetricData(item.getDimMap());
        }
    }

    public static List<TableDataItem> convertTableDataItemList(List<TableDataItem> tableDataItemList) {
        List<TableDataItem> newList = new ArrayList<>();
        for (TableDataItem item : tableDataItemList) {
            Map<String, String> ciFieldMap = new HashMap<>(item.getFieldMap());
            Map<String, String> fiFieldMap = new HashMap<>(item.getFieldMap());
            Map<String, String> miFieldMap = new HashMap<>(item.getFieldMap());
            ciFieldMap.put("ratio_level","核心");
            fiFieldMap.put("ratio_level","聚焦");
            miFieldMap.put("ratio_level","大盘");
            TableDataItem ciItem = new TableDataItem();
            TableDataItem fiItem = new TableDataItem();
            TableDataItem miItem = new TableDataItem();
            ciItem.setFieldMap(ciFieldMap);
            fiItem.setFieldMap(fiFieldMap);
            miItem.setFieldMap(miFieldMap);
            Map<String, Double> dimMap = item.getDimMap();
            Map<String, Double> ciDimMap = new HashMap<>();
            Map<String, Double> fiDimMap = new HashMap<>();
            Map<String, Double> miDimMap = new HashMap<>();
            for (Map.Entry<String, Double> entry : dimMap.entrySet()) {
                String key = entry.getKey();
                Double value = entry.getValue();
                if (key.startsWith("ci")) {
                    ciDimMap.put(key, value);
                } else if (key.startsWith("fi")) {
                    fiDimMap.put(key, value);
                } else {
                    miDimMap.put(key, value);
                }
            }
            ciItem.setDimMap(ciDimMap);
            fiItem.setDimMap(fiDimMap);
            miItem.setDimMap(miDimMap);
            newList.add(ciItem);
            newList.add(fiItem);
            newList.add(miItem);
        }
        return newList;
    }


    public static void makeUpTrendlineData(List<TrendLineDetailInfo> trendLineDetailInfoList) {
        List<TrendLineDetailInfo> gapTrendLineDetailInfoList =
                MapperUtil.str2List(MapperUtil.obj2Str(trendLineDetailInfoList), TrendLineDetailInfo.class);
        for (TrendLineDetailInfo info : gapTrendLineDetailInfoList) {
            String newDim = info.getDim() + "_gap";
            info.setDim(newDim);
            Double target = getTarget(newDim);
            List<TrendLineDataItem> itemList = info.getTrendLineDataItemList();
            for (TrendLineDataItem item : itemList) {
                Double value = item.getValue();
                if(!GeneralUtil.isEmpty(value)){
                    item.setValue(value - target);
                }
            }
            trendLineDetailInfoList.add(info);
        }

    }

    private static Double getTarget(String dim){
        switch (dim){
            case "ci_viewspot_ratio_gap":
                return 0d;
            case "fi_viewspot_ratio_gap":
                return 0.01d;
            case "mi_viewspot_ratio_gap":
                return 0.03d;
            default:
                return 0d;
        }
    }


    //根据考核层级确定可下钻维度
    public static List<String> getFieldList(String level){
        switch (level){
            case "国内":
            case "三方":
                return Lists.newArrayList("region_name","province_name","examinee");
            case "大区":
            case "省份":
                return Lists.newArrayList("province_name","examinee");
            case "景点":
                return Lists.newArrayList("examinee");
            default:
                throw new ConfigImportException("劣势率配置中导入了错误的层级:"+level);
        }
    }

    //获取当前分页需要添加的过滤条件
    public static List<String> getPagingCondition(List<List<Object>> rawResultList,
                                                  Integer pageNo,
                                                  Integer pageSize) {
        //每页取6个
        //每个数据分3层 每页实际总条数18
        Integer innerPageSize = pageSize / 3;
        List<String> fieldList = new ArrayList<>();
        for (List<Object> rowResult : rawResultList) {
            fieldList.add(String.valueOf(rowResult.get(0)));
        }
        int startIndex = (pageNo - 1) * innerPageSize;
        int endIndex = pageNo * innerPageSize;
        int length = fieldList.size();
        //处理最后一页index溢出
        if (endIndex > length) {
            endIndex = length;
        }
        return fieldList.subList(startIndex, endIndex);
    }

    //获取当前数据总条数
    public static Integer getTotalNum(List<List<Object>> rawResultList) {
        return rawResultList.size() * 3;
    }

    //获取查询数据时是否需要扣减额外的天数
    // 如果传入的是最新月 那么不需要加条件限制  如果传入的不是最新月 且该月为某季的最后一个月 那么加条件限制
    // 如果传入的是最新季 那么不需要加条件限制  如果传入的不是最新季 那么加条件限制
    // 如果传入的是最新半年 那么不需要加条件限制  如果传入的不是最新半年 那么加条件限制
    public static Integer getAddtionalGapDay(Map<String, List<String>> inMap,
                                             Map<String, String> baseMap,
                                             String year,
                                             String dateType,
                                             String month,
                                             String quarter,
                                             String half,
                                             String d) throws ParseException {
        Integer additionalGapDay = 0;
        String lastDay = DateUtil.getDayOfInterval(d, -1);
        if ("month".equals(dateType)) {
            if (!DateUtil.isCurrentMonthV2(lastDay, year, month)) {
                List<String> timeList;
                if(GeneralUtil.isNotEmpty(baseMap)){
                    timeList = Lists.newArrayList(baseMap.get(dateType).split("\\|"));
                }else{
                    timeList = inMap.get(dateType);
                }
                if (!GeneralUtil.isEmpty(timeList) && timeList.size() == 3) {
                    if(GeneralUtil.isNotEmpty(baseMap)){
                        baseMap.put("is_coefficient_identifier","0");
                    }else{
                        inMap.put("is_coefficient_identifier", Lists.newArrayList("0"));
                    }
                    additionalGapDay = 5;
                }
            }
        }
        if ("quarter".equals(dateType)) {
            if (!DateUtil.isCurrentQuarterV2(lastDay, year, quarter)) {
                if(GeneralUtil.isNotEmpty(baseMap)){
                    baseMap.put("is_coefficient_identifier","0");
                }else{
                    inMap.put("is_coefficient_identifier", Lists.newArrayList("0"));
                }
                additionalGapDay = 5;
            }
        }
        if ("half".equals(dateType)) {
            if (!DateUtil.isCurrentHalfV2(lastDay, year, half)) {
                if(GeneralUtil.isNotEmpty(baseMap)){
                    baseMap.put("is_coefficient_identifier","0");
                }else{
                    inMap.put("is_coefficient_identifier", Lists.newArrayList("0"));
                }
                additionalGapDay = 10;
            }
        }
        return additionalGapDay;
    }

    //获取查询数据时是否需要扣减额外的天数
    // 如果传入的是最新月 那么不需要加条件限制  如果传入的不是最新月 且该月为某季的最后一个月 那么加条件限制
    // 如果传入的是最新季 那么不需要加条件限制  如果传入的不是最新季 那么加条件限制
    // 如果传入的是最新半年 那么不需要加条件限制  如果传入的不是最新半年 那么加条件限制
    public static int getAdditionalGapDayV4(String year,
                                                String dateType,
                                                List<String> dateInfoList,
                                                String monthParam,
                                                String quarterParam,
                                                String halfParam,
                                                String d) throws ParseException {
        int additionalGapDay = 0;
        String lastDay = DateUtil.getDayOfInterval(d, -1);
        if ("month".equals(dateType)) {
            if (!DateUtil.isCurrentMonthV2(lastDay, year, monthParam)) {
                if (!GeneralUtil.isEmpty(dateInfoList) && dateInfoList.size() == 3) {
                    additionalGapDay = 5;
                }
            }
        }
        if ("quarter".equals(dateType)) {
            if (!DateUtil.isCurrentQuarterV2(lastDay, year, quarterParam)) {
                additionalGapDay = 5;
            }
        }
        if ("half".equals(dateType)) {
            if (!DateUtil.isCurrentHalfV2(lastDay, year, halfParam)) {
                additionalGapDay = 10;
            }
        }
        if ("year".equals(dateType)) {
            if (!DateUtil.isCurrentYear(lastDay, year)) {
                additionalGapDay = 15;
            }
        }
        return additionalGapDay;
    }


    //获取查询数据时是否需要扣减额外的天数
    // 如果传入的是最新季 那么不需要加条件限制  如果传入的不是最新季 那么加条件限制
    // 如果传入的是最新半年 那么不需要加条件限制  如果传入的不是最新半年 那么加条件限制
    public static Integer getAdditionalGapDayV2(String year,
                                                String dateType,
                                                String quarter,
                                                String half,
                                                String d) throws ParseException {
        Integer additionalGapDay = 0;
        String lastDay = DateUtil.getDayOfInterval(d, -1);
        if ("quarter".equals(dateType)) {
            if (!DateUtil.isCurrentQuarterV2(lastDay, year, quarter)) {
                additionalGapDay = 5;
            }
        }
        if ("half".equals(dateType)) {
            if (!DateUtil.isCurrentHalfV2(lastDay, year, half)) {
                additionalGapDay = 10;
            }
        }
        return additionalGapDay;
    }

    /**
     * 判断当前传入季是否包含当前日期
     * @param quarter
     * @param d
     * @param year
     * @return
     * @throws ParseException
     */
    public static Boolean isCurrentQuarter(String quarter,String d,String year) throws ParseException {
        String lastDay = DateUtil.getDayOfInterval(d, -1);
        return !DateUtil.isCurrentQuarterV2(lastDay, year, quarter);
    }

    //生成下钻表格表头
    public static List<String> getTableHeaderList(String mappingField) {
        List<String> tableHeaderList = new ArrayList<>();
        switch (mappingField) {
            case "region_name":
                tableHeaderList.add("regionName");
                break;
            case "province_name":
                tableHeaderList.add("regionName");
                tableHeaderList.add("provinceName");
                break;
            case "examinee":
                tableHeaderList.add("regionName");
                tableHeaderList.add("provinceName");
                tableHeaderList.add("examinee");
                tableHeaderList.add("examineeName");
                break;
        }
        tableHeaderList.add("ratioLevel");
        tableHeaderList.add("targetRate");
        tableHeaderList.add("completeRate");
        tableHeaderList.add("gapValue");
        return tableHeaderList;
    }
    //生成下钻表格表头
    public static List<String> getFirstDrillHeaderList(String mappingField) {
        List<String> tableHeaderList = new ArrayList<>();
        switch (mappingField) {
            case "region_name":
                tableHeaderList.add("regionName");
                break;
            case "province_name":
                tableHeaderList.add("regionName");
                tableHeaderList.add("provinceName");
                break;
            case "examinee":
                tableHeaderList.add("regionName");
                tableHeaderList.add("provinceName");
                tableHeaderList.add("examinee");
                break;
        }
        tableHeaderList.add("completeRate");
        tableHeaderList.add("coreDisadvantageRate");
       // tableHeaderList.add("coreDisadvantageTargetRate");
        tableHeaderList.add("focusDisadvantageRate");
      //  tableHeaderList.add("focusDisadvantageTargetRate");
        tableHeaderList.add("tailHighValueDisadvantageRate");
      //  tableHeaderList.add("tailHighValueDisadvantageTargetRate");
        tableHeaderList.add("tailHighOtherDisadvantageRate");
      //  tableHeaderList.add("tailHighOtherDisadvantageTargetRate");
        return tableHeaderList;
    }
    public static List<String> getStatisticsDimIdList(String type,
                                                      String field) {
        List<String> idList = new ArrayList<>();
        if ("withoutDrillDown".equals(type)) {
            idList.add("1");
        }else{
            if("region_name".equals(field)){
                idList.add("3");
            }else if("province_name".equals(field) || "examinee".equals(field)){
                idList.add("4");
            }
        }
        return idList;
    }

    //获取表头信息
    public static List<String> getTableList(){
        return Arrays.asList("sightRange","sightDisadvantageRate","sightTargetValue","sightGapValue","sightLevelCompleteRate","sightWeight","sightCompleteRate");
    }

}
