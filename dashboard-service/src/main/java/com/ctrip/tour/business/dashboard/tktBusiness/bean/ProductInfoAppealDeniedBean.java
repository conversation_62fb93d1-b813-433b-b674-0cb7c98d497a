package com.ctrip.tour.business.dashboard.tktBusiness.bean;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


/**
 * 照妖镜产品信息申诉驳回场景的json格式
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ProductInfoAppealDeniedBean {
    Long productId;          // 产品id
    String productName;        // 产品名字
    String vendorId;           // 供应商id
    String vendorName;         // 供应商名字
    String rectificationDeadline; // 整改最后完成时间
    String rejectReason; // 驳回理由
}
