package com.ctrip.tour.business.dashboard.grpBusiness.domain.plan;

import com.ctrip.tour.business.dashboard.grpBusiness.domain.AbstractReduceFunction;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.model.ResultData;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.model.ResultMeta;

import java.util.*;
import java.util.stream.Collectors;

public class DefaultReduceFunction extends AbstractReduceFunction {

    ResultData merge(ResultData r1, ResultData r2) {
        // 如果多个结果集的纬度不同？公共纬度
        // 公共的纬度
        Map<String, ResultMeta> r1Meta = r1.getMeta().stream().collect(Collectors.toMap(
                ResultMeta::getIndicatorName,
                v -> v
        ));
        Map<String, ResultMeta> commonMeta = new HashMap<>();
        r2.getMeta().forEach(v -> {
            if (r1Meta.containsKey(v.getIndicatorName())) {
                commonMeta.put(v.getIndicatorName(), v);
            }
        });
        // 基于纬度聚合数据
        Map<String, List<Map<String, Object>>> m1 = new HashMap<>();
        Map<String, List<Map<String, Object>>> m2 = new HashMap<>();

        r1.getData().forEach(v -> {
            StringBuilder commonDims = new StringBuilder();
            commonMeta.forEach(
                    (metaKey, metaValue) -> commonDims.append("[").append(v.getOrDefault(metaKey, "")).append("]")
            );
            if (!m1.containsKey(commonDims.toString())) {
                m1.put(commonDims.toString(), new ArrayList<>());
            }
            m1.get(commonDims.toString()).add(v);
        });
        r2.getData().forEach(v -> {
            StringBuilder commonDims = new StringBuilder();
            commonMeta.forEach(
                    (metaKey, metaValue) -> commonDims.append("[").append(v.getOrDefault(metaKey, "")).append("]")
            );
            if (!m2.containsKey(commonDims.toString())) {
                m2.put(commonDims.toString(), new ArrayList<>());
            }
            m2.get(commonDims.toString()).add(v);
        });
        // 每个纬度的笛卡尔积
        ResultData resultData = new ResultData();
        m1.forEach((k, v) -> {
            if (!m2.containsKey(k)) {
                return;
            }
            // 笛卡尔积
            List<Map<String, Object>> m2L = m2.get(k);
            v.forEach(v1 -> m2L.forEach(v2 -> {
                Map<String, Object> item = new HashMap<>();
                item.putAll(v1);
                item.putAll(v2);
                resultData.getData().add(item);
            }));
        });
        m1.forEach((k, v) -> {
            if (!m2.containsKey(k)) {
                resultData.getData().addAll(v);
            }
        });
        m2.forEach((k, v) -> {
            if (!m1.containsKey(k)) {
                resultData.getData().addAll(v);
            }
        });

        // 纬度聚合
        r2.getMeta().forEach(v -> r1Meta.put(v.getIndicatorName(), v));
        resultData.setMeta(new ArrayList<>(new HashSet<>(r1Meta.values())));
        return resultData;
    }

    // 数据源基于主键聚合
    @Override
    public void reduce(LogicQueryPlan logicPlan) {
        if (logicPlan.getQueryDataResult().isEmpty()) {
            return;
        }
        if (logicPlan.getQueryDataResult().size() == 1) {
            logicPlan.setReduceQueryDataResult(new ArrayList<>(logicPlan.getQueryDataResult().values()).get(0));
        } else {
            ArrayList<Map.Entry<Integer, ResultData>> v = new ArrayList<>(logicPlan.getQueryDataResult().entrySet());
            ResultData merged = v.get(0).getValue();
            for (int i = 1; i < v.size(); i++) {
                merged = merge(merged, v.get(i).getValue());
            }
            logicPlan.setReduceQueryDataResult(merged);
        }
    }
}
