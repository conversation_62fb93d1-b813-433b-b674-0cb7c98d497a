package com.ctrip.tour.business.dashboard.grpBusiness.domain.model;


import com.ctrip.tour.business.dashboard.grpBusiness.config.GrpConfig;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import org.yaml.snakeyaml.representer.Representer;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import org.yaml.snakeyaml.Yaml;

//@Configuration
//@ConfigurationProperties(prefix = "")//properties文件前缀
//@PropertySource(value = "classpath:domain_model_sijiatuan.yml", factory = YamlPropertySourceFactory.class)
public class DomainConfig {
    // 全局模型配置
    @JsonIgnore
    private static Map<String, DomainConfig> domainConfigMap = new ConcurrentHashMap<>();

    // 模型列表
    @Setter
    @Getter
    private ArrayList<LogicModel> domain_info;
    // 纬度-> 模型ID
    @JsonIgnore
    private Map<String, List<Integer>> dimToDomainIDSMap = new HashMap<>();
    // 指标-> 模型ID
    @JsonIgnore
    private Map<String, Integer> indicatorNameToDomainIDMap = new HashMap<>();
    // 指标/纬度 -> 指标信息
    @JsonIgnore
    private Map<String, Indicator> indicatorNameToIndicatorMap = new HashMap<>();
    @JsonIgnore
    private Map<String, Indicator> dimNameToIDimMap = new HashMap<>();
    // 模型ID -> 模型信息
    @JsonIgnore
    private Map<Integer, LogicModel> logicModelMap = new HashMap<>();

    @JsonIgnore
    private static Representer representer = new Representer();

    static {
        representer.getPropertyUtils().setSkipMissingProperties(true);
    }

    /**
     * 更新模板信息
     *
     * @param businessLine 业务线
     * @return 模板集合
     */
    @JsonIgnore
    public static DomainConfig getAndUpdateDomainConfigByBusinessLine(Integer businessLine, GrpConfig grpConfig) {
        String key = getAndUpdateDomainWithBusinessLineWithFileName(businessLine, grpConfig);
        return getDomainConfigByDomainConfigID(key);
    }

    @JsonIgnore
    public static DomainConfig getDomainConfigByDomainID(String key) {
        return getDomainConfigByDomainConfigID(key);
    }

    @JsonIgnore
    public static String getAndUpdateDomainWithBusinessLineWithFileName(Integer businessLine,GrpConfig grpConfig) {
        String fileName;
        switch (businessLine) {
            case 210:
            case 220:
                fileName = "domain_model_sijiatuan.yml";
                break;
            case 230:
                fileName = "domain_model_sijiatuan_dingzhi.yml";
                break;
            case 100:
            default:
                fileName = "domain_model_gentuan.yml";
        }
        String configStr = grpConfig.getDomainConfigStr(fileName);
        DomainConfig recipe = new Yaml(representer).loadAs(configStr, DomainConfig.class);
        updateDomain(recipe, fileName);
        return fileName;
    }

    @JsonIgnore
    public static void updateDomain(DomainConfig recipe, String fileName) {
        if (domainConfigMap == null) {
            domainConfigMap = new ConcurrentHashMap<>();
        }
        // 更新模型
        Map<Integer, LogicModel> logicModelMap = recipe.logicModelMap;
        recipe.getDomain_info().forEach(v -> logicModelMap.put(v.getId(), v));
        // xxx
        Map<String, List<Integer>> dimToDomainIDSMap = recipe.dimToDomainIDSMap;
        Map<String, Integer> indicatorNameToDomainIDMap = recipe.indicatorNameToDomainIDMap;
        Map<String, Indicator> indicatorNameToIndicatorMap = recipe.indicatorNameToIndicatorMap;
        Map<String, Indicator> dimNameToDimMap = recipe.dimNameToIDimMap;
        recipe.logicModelMap.forEach((key, value) -> value.getIndicators().forEach(indicator -> {
            // indicatorNameToIndicatorMap
            indicatorNameToIndicatorMap.put(indicator.getIndicator_name(), indicator);
            if (!indicator.getIndicator_is_dimension()) {
                // indicatorNameToDomainIDMap
                indicatorNameToDomainIDMap.put(indicator.getIndicator_name(), key);
                return;
            }
            // dimToDomainIDSMap
            dimNameToDimMap.put(indicator.getIndicator_name(), indicator);
            if (!dimToDomainIDSMap.containsKey(indicator.getIndicator_name())) {
                dimToDomainIDSMap.put(indicator.getIndicator_name(), new ArrayList<>());
            }
            dimToDomainIDSMap.get(indicator.getIndicator_name()).add(key);
        }));
        // update
        domainConfigMap.put(fileName, recipe);
    }



    /**
     * 基于indicatorName返回指标信息
     *
     * @param indicatorName 指标名称
     * @return 指标信息
     */
    @JsonIgnore
    public Indicator getIndicatorByIndicatorName(String indicatorName) {
        return indicatorNameToIndicatorMap.get(indicatorName);
    }

    @JsonIgnore
    public Indicator getDimByIndicatorName(String indicatorName) {
        return dimNameToIDimMap.get(indicatorName);
    }

    /**
     * 基于indicatorName返回所在模板
     *
     * @param indicatorName 指标名称
     * @return 模板ID
     */
    @JsonIgnore
    public Integer getLogicModelIDByIndicatorName(String indicatorName) {
        return indicatorNameToDomainIDMap.get(indicatorName);
    }

    @JsonIgnore
    public List<Integer> getLogicModelIDByDimName(String indicatorName) {
        return dimToDomainIDSMap.get(indicatorName);
    }

    /**
     * 基于indicatorKeyName返回指标信息
     *
     * @param indicatorKeyName 指标tips
     * @return 指标信息
     */
    @JsonIgnore
    public Indicator getIndicatorsItemByIndicatorKeyName(String indicatorKeyName) {
        for (LogicModel logicModel : domain_info) { // 模板集合
            for (Indicator indicator : logicModel.getIndicators()) { // 模板下的指标
                if (indicator.getIndicator_key_name() != null && indicator.getIndicator_key_name().equals(indicatorKeyName)) {
                    return indicator;
                }
            }
        }
        return null;
    }

    /**
     * 基于模板ID返回模板信息
     *
     * @param domainID 模板ID
     * @return 模板信息
     */
    @JsonIgnore
    public LogicModel getLogicModelByLogicModelID(Integer domainID) {
        return logicModelMap.get(domainID);
    }

    @JsonIgnore
    public static DomainConfig getDomainConfigByDomainConfigID(String ID) {
        return domainConfigMap.get(ID);
    }
}


