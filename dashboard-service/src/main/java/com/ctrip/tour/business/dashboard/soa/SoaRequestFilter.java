package com.ctrip.tour.business.dashboard.soa;

import com.ctrip.soa.caravan.web.filter.CrossDomainFilter;
import com.ctrip.soa.caravan.web.utils.VulDef;
import com.ctrip.tour.business.dashboard.tktBusiness.annotation.NotLoginRequired;
import com.ctrip.tour.business.dashboard.tktBusiness.checker.UserChecker;
import com.ctriposs.baiji.rpc.server.HttpRequestWrapper;
import com.ctriposs.baiji.rpc.server.HttpResponseWrapper;
import com.ctriposs.baiji.rpc.server.ServiceHost;
import com.ctriposs.baiji.rpc.server.filter.RequestFilter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.annotation.Annotation;
import java.util.HashMap;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/7/20
 */
@Component
public class SoaRequestFilter implements RequestFilter {

    @Autowired
    private UserChecker userChecker;


    @Override
    public void apply(ServiceHost host, HttpRequestWrapper request, HttpResponseWrapper response) {
        //目前需要排除权限验证的方法有  checkHealth


        //SLB拉入时会访问checkHealth方法  因此权限控制排除checkHealth方法 否则会导致gateWay访问方式不可用
        //也可以用下面注释的代码做接口排除
        //        String operationName = request.operationName();
        //        if("checkhealth".equals(operationName)){
        //            return;
        //        }

        // 跨域
//        response.setHeader("Access-Control-Allow-Origin", "https://vendor.package.fat29.qa.nt.ctripcorp.com");
//        response.setHeader("Access-Control-Expose-Headers", "appname,content-type");
//        response.setHeader("Access-Control-Allow-Credentials", "true");
//        if ("OPTIONS".equals(request.httpMethod())) {
//            String accessControllRequestMethod = request.getHeader("Access-Control-Request-Method");
//            if (accessControllRequestMethod != null) {
//                response.setHeader("Access-Control-Allow-Methods", VulDef.securityRSHeaderInjection(accessControllRequestMethod));
//            }
//            String accessControllRequestHeaders = request.getHeader("Access-Control-Request-Headers");
//            if (accessControllRequestHeaders != null) {
//                response.setHeader("Access-Control-Allow-Headers", VulDef.securityRSHeaderInjection(accessControllRequestHeaders));
//            }
//            response.setHeader("Access-Control-Max-Age", "3600");
//            response.setStatus(200);
//            response.sendResponse();
//            return;
//        }


        String skipAuth = request.getHeader("skipAuth");
        if (StringUtils.equals(skipAuth, "true")) {
            return;
        }

        MDC.put("metric", "-1");
        String appname = userChecker.getAppname(request);
        MDC.put("appname", appname);
        Annotation annotation = request.operationHandler().getMethod().getAnnotation(NotLoginRequired.class);
        if (annotation != null) {
            return;
        }
        switch (appname) {
            case "dashboardpc":
                checkDashboardPC(request, response);
                break;
            case "dashboardapp":
                checkDashboardAPP(request, response);
                break;
            default:
                setResponseStatus(response);
        }

    }

    /**
     * 校验仪表盘pc端登录权限
     *
     * @param request
     * @param response
     */
    private void checkDashboardPC(HttpRequestWrapper request, HttpResponseWrapper response) {
        //获取用户工号
        String casTicket = userChecker.getCasTicket(request);
        String empCode = userChecker.getEmpCode(casTicket);
        //获取语种
        userChecker.setVBKLocaleLang(request);
        //未获取到工号  返回异常403 前端跳转到登录页
        if ("".equals(empCode)) {
            setResponseStatus(response);
        }
    }

    /**
     * 校验仪表盘app端登录权限
     *
     * @param request
     * @param response
     */
    private void checkDashboardAPP(HttpRequestWrapper request, HttpResponseWrapper response) {
        String cticket = userChecker.getCticket(request);
        String empCode = userChecker.getEmpCode(cticket, "Offline");
        //获取语种
        userChecker.setAppLocaleLang(request);
        //未获取到工号  返回异常403 前端跳转到登录页
        if ("".equals(empCode)) {
            setResponseStatus(response);
        }
    }

    /**
     * 设置没有登录时的返回码状态
     *
     * @param response
     */
    private void setResponseStatus(HttpResponseWrapper response) {
        response.setStatus(403);
        response.sendResponse();
    }
}
