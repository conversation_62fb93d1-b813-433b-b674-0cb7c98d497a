package com.ctrip.tour.business.dashboard.utils;

import credis.java.client.CacheProvider;
import credis.java.client.util.CacheFactory;
import retrofit2.http.GET;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/25
 */
public class RedisUtil {

    private static Map<String, CacheProvider> redisCacheProviderMap = new HashMap<>();

    public static CacheProvider getProvider(String cluster) {
        CacheProvider provider = redisCacheProviderMap.get(cluster);
        if (provider == null) {
            provider = CacheFactory.getProvider(cluster);
            redisCacheProviderMap.put(cluster, provider);
        }
        return provider;
    }

    //判断一个元素是否在某集合中
    public static boolean sismember(CacheProvider provider,
                                    String key,
                                    String member) {
        return provider.sismember(key, member);
    }


    public static void sadd(CacheProvider provider,
                            String key,
                            String member,
                            long expireTime) {
        provider.sadd(key, member);
        provider.expire(key, expireTime);
    }

    public static String getKey(String sceneType,String...inputParams){
        StringBuilder sb = new StringBuilder();
        sb.append(sceneType);
        for(String param:inputParams){
            sb.append(param).append("_");
        }
        sb.deleteCharAt(sb.length()-1);
        return sb.toString();
    }

    public static String get(CacheProvider provider,String key){
      return provider.get(key);
    }

    public static boolean set(CacheProvider provider,String key, String val){
        return provider.set(key,  val);
    }
}
