package com.ctrip.tour.business.dashboard.tktBusiness.strategy.subMetricImplV2;

import com.ctrip.ibu.platform.shark.sdk.api.Shark;
import com.ctrip.soa._24922.*;
import com.ctrip.soa._27181.BIBaseReportQueryServiceClient;
import com.ctrip.soa._27181.GetRawDataRequestType;
import com.ctrip.soa._27181.GetRawDataResponseType;
import com.ctrip.tour.business.dashboard.sightArchives.dao.overseametric.CdmPrdTktDashboardFlyWeaknessStatisticsDfDao;
import com.ctrip.tour.business.dashboard.sightArchives.dao.overseametric.CdmPrdTktDashboardKlkWeaknessStatisticsDfDao;
import com.ctrip.tour.business.dashboard.sightArchives.dao.overseametric.DimOrdTtdTargetConfigDao;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.CdmPrdTktDashboardWeaknessStatisticsBO;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.bean.CdmPrdTktDashboardWeaknessParamBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.*;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.OverseaSinglePeriodTrendLineBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.enums.CompetitorTypeEnumEnum;
import com.ctrip.tour.business.dashboard.tktBusiness.enums.GraphTypeEnum;
import com.ctrip.tour.business.dashboard.tktBusiness.enums.TrendLineNameEnum;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.*;
import com.ctrip.tour.business.dashboard.utils.UserUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

/**
 * 海外竞品基础策略
 */
@Component
public class CompetitorBaseStrategyV2 {


    @Autowired
    private BIBaseReportQueryServiceClient baseReportQueryServiceClient;

    @Autowired
    private OverseaSinglePeriodTrendLineBiz overseaSinglePeriodTrendLineBiz;

    @Autowired
    private SwitchNewTableHelper switchNewTableHelper;

    @Autowired
    private RemoteConfig remoteConfig;

    @Autowired
    private CdmPrdTktDashboardFlyWeaknessStatisticsDfDao cdmPrdTktDashboardFlyWeaknessStatisticsDfDao;

    @Autowired
    private CdmPrdTktDashboardKlkWeaknessStatisticsDfDao cdmPrdTktDashboardKlkWeaknessStatisticsDfDao;

    @Autowired
    private DimOrdTtdTargetConfigDao dimOrdTtdTargetConfigDao;


    public GetOverseaTrendLineDataV2ResponseType getBus105106107SubTrendLineData(GetOverseaTrendLineDataV2RequestType request,
                                                                                 String d,
                                                                                 List<String> timeList) throws Exception {

        GetOverseaTrendLineDataV2ResponseType response = new GetOverseaTrendLineDataV2ResponseType();
        List<OverseaTrendLine> trendLineDetailInfoList = new ArrayList<>();
        response.setTrendlines(trendLineDetailInfoList);

        List<OverseaTrendLineDataItem> cwNumList = new ArrayList<>();
        List<OverseaTrendLineDataItem> fwNumList = new ArrayList<>();
        List<OverseaTrendLineDataItem> twNumList = new ArrayList<>();
        List<OverseaTrendLineDataItem> cNumList = new ArrayList<>();
        List<OverseaTrendLineDataItem> fNumList = new ArrayList<>();
        List<OverseaTrendLineDataItem> tNumList = new ArrayList<>();
        List<OverseaTrendLineDataItem> cwRateList = new ArrayList<>();
        List<OverseaTrendLineDataItem> fwRateList = new ArrayList<>();
        List<OverseaTrendLineDataItem> twRateList = new ArrayList<>();


        for (String bean : timeList) {
            String year = bean.split("-")[0];
            String quarter = bean.split("-")[1];
            request.getTimeFilter().setDateType("quarter");
            request.getTimeFilter().setQuarter(quarter);
            request.getTimeFilter().setYear(year);
            if (Integer.parseInt(year) <= 2024) {
                continue;
            }

            // 区分查找飞猪和客路数据
            CompetitiveType competitiveType = null;
            if ("tfly".equals(request.getSubMetric())) {
                OverseasRelatedSearchParamBean metricTendLineData = Bus105And106And107Helper.generateOverseaInfoSearch(request.getTimeFilter(), null, request.getDomainName(), d, request.getMetric());
                OverseasCompetitionResponseBean metricCardResult = cdmPrdTktDashboardFlyWeaknessStatisticsDfDao.queryFlyWeaknessStatisticInfo(metricTendLineData);
                competitiveType = Bus105And106And107Helper.calculateTrendLineData(CompetitorTypeEnumEnum.FLY, metricCardResult, remoteConfig, request);
            }
            if ("tklk".equals(request.getSubMetric())) {
                OverseasRelatedSearchParamBean metricTendLineData = Bus105And106And107Helper.generateOverseaInfoSearch(request.getTimeFilter(), null, request.getDomainName(), d, request.getMetric());
                OverseasCompetitionResponseBean metricCardResult = cdmPrdTktDashboardKlkWeaknessStatisticsDfDao.queryKlkWeaknessStatisticInfo(metricTendLineData);
                competitiveType = Bus105And106And107Helper.calculateTrendLineData(CompetitorTypeEnumEnum.KLK, metricCardResult, remoteConfig, request);
            }

            String dateInfo = year + "-" + quarter;

            if (competitiveType != null) {
                if (competitiveType.getCoreInfo() != null) {
                    cwNumList.add(new OverseaTrendLineDataItem(null, dateInfo, competitiveType.getCoreInfo().getInferiorNum()));
                    cNumList.add(new OverseaTrendLineDataItem(null, dateInfo, competitiveType.getCoreInfo().getGapValue()));
                    cwRateList.add(new OverseaTrendLineDataItem(null, dateInfo, competitiveType.getCoreInfo().getInferiorRate()));
                }
                if (competitiveType.getFocusInfo() != null) {
                    fwNumList.add(new OverseaTrendLineDataItem(null, dateInfo, competitiveType.getFocusInfo().getInferiorNum()));
                    fNumList.add(new OverseaTrendLineDataItem(null, dateInfo, competitiveType.getFocusInfo().getGapValue()));
                    fwRateList.add(new OverseaTrendLineDataItem(null, dateInfo, competitiveType.getFocusInfo().getInferiorRate()));
                }
                if (competitiveType.getGeneralInfo() != null) {
                    twNumList.add(new OverseaTrendLineDataItem(null, dateInfo, competitiveType.getGeneralInfo().getInferiorNum()));
                    tNumList.add(new OverseaTrendLineDataItem(null, dateInfo, competitiveType.getGeneralInfo().getGapValue()));
                    twRateList.add(new OverseaTrendLineDataItem(null, dateInfo, competitiveType.getGeneralInfo().getInferiorRate()));
                }
            }
        }

        trendLineDetailInfoList.add(new OverseaTrendLine(TrendLineNameEnum.CW_NUM.getName(), GraphTypeEnum.BAR_CHART.getName(), cwNumList));
        trendLineDetailInfoList.add(new OverseaTrendLine(TrendLineNameEnum.CW_GAP.getName(), GraphTypeEnum.LINE_CHART.getName(), cNumList));
        trendLineDetailInfoList.add(new OverseaTrendLine(TrendLineNameEnum.CW_RATE.getName(), GraphTypeEnum.LINE_CHART.getName(), cwRateList));

        trendLineDetailInfoList.add(new OverseaTrendLine(TrendLineNameEnum.FW_NUM.getName(), GraphTypeEnum.BAR_CHART.getName(), fwNumList));
        trendLineDetailInfoList.add(new OverseaTrendLine(TrendLineNameEnum.FW_GAP.getName(), GraphTypeEnum.LINE_CHART.getName(), fNumList));
        trendLineDetailInfoList.add(new OverseaTrendLine(TrendLineNameEnum.FW_RATE.getName(), GraphTypeEnum.LINE_CHART.getName(), fwRateList));

        trendLineDetailInfoList.add(new OverseaTrendLine(TrendLineNameEnum.TW_NUM.getName(), GraphTypeEnum.BAR_CHART.getName(), twNumList));
        trendLineDetailInfoList.add(new OverseaTrendLine(TrendLineNameEnum.TW_GAP.getName(), GraphTypeEnum.LINE_CHART.getName(), tNumList));
        trendLineDetailInfoList.add(new OverseaTrendLine(TrendLineNameEnum.TW_RATE.getName(), GraphTypeEnum.LINE_CHART.getName(), twRateList));

        return response;

    }


    /**
     * 返回匹配的名称
     *
     * @param bo
     * @param dimName
     * @return
     */
    public String getDestinationRange(CdmPrdTktDashboardWeaknessStatisticsBO bo, String dimName) {
        switch (dimName) {
            case "region":
                return bo.getBusinessRegionName();
            case "province":
                return bo.getBusinessSubRegionName();
            default:
                return null;
        }
    }

    public GetOverseaTrendLineDataV2ResponseType getBus105106107SubTrendLineDrillDownData(GetOverseaTrendLineDataV2RequestType request,
                                                                                          String d,
                                                                                          List<String> timeList) throws Exception {

        GetOverseaTrendLineDataV2ResponseType response = new GetOverseaTrendLineDataV2ResponseType();
        List<OverseaTrendLine> trendLineDetailInfoList = new ArrayList<>();
        response.setTrendlines(trendLineDetailInfoList);

        List<OverseaTrendLineDataItem> cwRateList = new ArrayList<>();
        List<OverseaTrendLineDataItem> fwRateList = new ArrayList<>();
        List<OverseaTrendLineDataItem> twRateList = new ArrayList<>();


        for (String bean : timeList) {
            String year = bean.split("-")[0];
            String quarter = bean.split("-")[1];
            request.getTimeFilter().setDateType("quarter");
            request.getTimeFilter().setQuarter(quarter);
            request.getTimeFilter().setYear(year);
            if (Integer.parseInt(year) <= 2024) {
                continue;
            }
            String vbkLocale = UserUtil.getVbkLocale();

            // 区分查找飞猪和客路数据
            List<CdmPrdTktDashboardWeaknessStatisticsBO> metricCardResult = new ArrayList<>();
            if ("tfly".equals(request.getSubMetric())) {
                OverseasRelatedSearchParamBean metricTendLineData = Bus105And106And107Helper.generateOverseaInfoSearch(request.getTimeFilter(), request, request.getDomainName(), d, request.getMetric());
                metricCardResult = "zh-CN".equalsIgnoreCase(vbkLocale) ? cdmPrdTktDashboardFlyWeaknessStatisticsDfDao.queryCdmPrdTktDashboardWeaknessStatisticsBO(metricTendLineData):
                        cdmPrdTktDashboardFlyWeaknessStatisticsDfDao.queryCdmPrdTktDashboardWeaknessStatisticsBOWithFlyEn(metricTendLineData);
            }
            if ("tklk".equals(request.getSubMetric())) {
                OverseasRelatedSearchParamBean metricTendLineData = Bus105And106And107Helper.generateOverseaInfoSearch(request.getTimeFilter(), request, request.getDomainName(), d, request.getMetric());
                metricCardResult = "zh-CN".equalsIgnoreCase(vbkLocale) ? cdmPrdTktDashboardKlkWeaknessStatisticsDfDao.queryCdmPrdTktDashboardWeaknessStatisticsBO(metricTendLineData):
                        cdmPrdTktDashboardKlkWeaknessStatisticsDfDao.queryCdmPrdTktDashboardWeaknessStatisticsBOWithKlkEn(metricTendLineData);
            }

            String dateInfo = year + "-" + quarter;
            for (CdmPrdTktDashboardWeaknessStatisticsBO bo : metricCardResult) {
                String rangeName = getDestinationRange(bo, request.getDimName());

                cwRateList.add(new OverseaTrendLineDataItem(rangeName, dateInfo, bo.getCwRate()));
                fwRateList.add(new OverseaTrendLineDataItem(rangeName, dateInfo, bo.getFwRate()));
                twRateList.add(new OverseaTrendLineDataItem(rangeName, dateInfo, bo.getTwRate()));
            }
        }

        trendLineDetailInfoList.add(new OverseaTrendLine(TrendLineNameEnum.CW_RATE.getName(), GraphTypeEnum.LINE_CHART.getName(), cwRateList));
        trendLineDetailInfoList.add(new OverseaTrendLine(TrendLineNameEnum.FW_RATE.getName(), GraphTypeEnum.LINE_CHART.getName(), fwRateList));
        trendLineDetailInfoList.add(new OverseaTrendLine(TrendLineNameEnum.TW_RATE.getName(), GraphTypeEnum.LINE_CHART.getName(), twRateList));

        return response;
    }


    public GetOverseaDrillDownBaseInfoV2ResponseType getBus105106107SubDrillDownBaseInfo(GetOverseaDrillDownBaseInfoV2RequestType request,
                                                                                         String d,
                                                                                         OverseaMetricInfoBean metricInfoBean) throws Exception {

        GetOverseaDrillDownBaseInfoV2ResponseType response = new GetOverseaDrillDownBaseInfoV2ResponseType();

        Boolean needSearch = StringUtils.isNotEmpty(request.getSearchInDim());
        String subMetric = request.getSubMetric();
        List<String> fieldList = new ArrayList<>();
        if (needSearch) {
            fieldList.add(request.getSearchInDim());
        } else {
            fieldList.addAll(Bus105And106And107Helper.getDrillDownFieldListV2(subMetric, metricInfoBean, remoteConfig));
            response.setDefaultChosenDim(fieldList.get(0));
        }

        Map<String, Future<GetRawDataResponseType>> fieldMap = new HashMap<>();
        for (String field : fieldList) {
            SqlParamterBean sqlParamterBean = Bus105And106And107Helper.getDrillDownBaseInfoSqlBeanV2(field, request, d, metricInfoBean, remoteConfig);
            GetRawDataRequestType fieldRequest = sqlParamterBean.convertBeanToRequest(false);
            fieldMap.put(field, switchNewTableHelper.switchRemoteDatabaseAsync(fieldRequest));
        }
        List<DilldownDim> fieldDataItemList = new ArrayList<>();
        response.setDimList(fieldDataItemList);
        for (String field : fieldList) {
            DilldownDim item = new DilldownDim();
            fieldDataItemList.add(item);
            Bus101102Helper.processDrillDownBaseInfoV2(request, field, fieldMap.get(field).get(), item, remoteConfig);
        }
        return response;
    }


    public GetOverseaTableDataV2ResponseType getBus105106107SubTableData(GetOverseaTableDataV2RequestType request,
                                                                         String d,
                                                                         OverseaMetricInfoBean metricInfoBean) throws Exception {

        GetOverseaTableDataV2ResponseType response = new GetOverseaTableDataV2ResponseType();

        String metric = request.getMetric();
        String subMetric = request.getSubMetric();
        String field = request.getDimName();
        SubMetricFiledBean configBean = remoteConfig.getSubMetricFiledBeanV2(metric, subMetric, field);
        response.setTableHeaderList(Bus105And106And107Helper.getTableHeaderList(configBean));

        CdmPrdTktDashboardWeaknessParamBean currentBean = Bus105And106And107Helper.getTableDataSqlBeanV2(request, d, metricInfoBean);
        Integer totalCount = 0;
        if ("tklk".equalsIgnoreCase(subMetric)) {
            totalCount = cdmPrdTktDashboardKlkWeaknessStatisticsDfDao.queryCdmPrdTktDashboardWeaknessStatisticsBOCount(currentBean);
        } else {
            totalCount = cdmPrdTktDashboardFlyWeaknessStatisticsDfDao.queryCdmPrdTktDashboardWeaknessStatisticsBOCount(currentBean);
        }
        if (totalCount == 0) {
            return response;
        }
        String timeType = Bus105And106And107Helper.getTimeType(request.getTimeFilter());
        Integer twTarget = remoteConfig.getCompetitiveTarget(currentBean.getExamineYear(),
                subMetric, metric, "general", timeType);
        Integer fwTarget = remoteConfig.getCompetitiveTarget(currentBean.getExamineYear(),
                subMetric, metric, "focus", timeType);
        Integer cwTarget = remoteConfig.getCompetitiveTarget(currentBean.getExamineYear(),
                subMetric, metric, "core", timeType);
        String vbkLocale = UserUtil.getVbkLocale();
        Boolean useZhCN = "zh-CN".equalsIgnoreCase(vbkLocale);
        String core = useZhCN ? remoteConfig.getConfigValue("core") : remoteConfig.getConfigValue("coreEn");
        String focus = useZhCN ? remoteConfig.getConfigValue("focus") : remoteConfig.getConfigValue("focusEn");
        String general = useZhCN ? remoteConfig.getConfigValue("general") : remoteConfig.getConfigValue("generalEn");

        List<CdmPrdTktDashboardWeaknessStatisticsBO> result = null;
        if ("tklk".equalsIgnoreCase(subMetric)) {
            result = cdmPrdTktDashboardKlkWeaknessStatisticsDfDao.queryCdmPrdTktDashboardWeaknessStatisticsBO(currentBean);
        } else {
            result = cdmPrdTktDashboardFlyWeaknessStatisticsDfDao.queryCdmPrdTktDashboardWeaknessStatisticsBO(currentBean);
        }

        //获取数据目标为：指定下钻维度，同一个下钻维度在这个下钻维度里会有多条数据，
        List<OverseaTableDataRow> tableDataItemList = new ArrayList<>();
        for (CdmPrdTktDashboardWeaknessStatisticsBO bo : result) {
            tableDataItemList.add(buildOverseaTableDataRow(core, response.getTableHeaderList(), bo, new Double(cwTarget != null ? cwTarget / 100.0 : 0.00)));
            tableDataItemList.add(buildOverseaTableDataRow(focus, response.getTableHeaderList(), bo, new Double(fwTarget != null ? fwTarget / 100.0 : 0.00)));
            tableDataItemList.add(buildOverseaTableDataRow(general, response.getTableHeaderList(), bo, new Double(twTarget != null ? twTarget / 100.0 : 0.00)));
        }
        response.setRows(tableDataItemList);
        response.setTotalNum(totalCount * 3);
        response.setMomType(OverseaMetricHelper.getMomType(request.getTimeFilter(), d));
        return response;
    }

    private OverseaTableDataRow buildOverseaTableDataRow(String viewSpot, List<String> hearderList, CdmPrdTktDashboardWeaknessStatisticsBO bo, Double target) {
        OverseaTableDataRow rowHexin = new OverseaTableDataRow();
        String vbkLocale = UserUtil.getVbkLocale();
        Boolean useZhCN = "zh-CN".equalsIgnoreCase(vbkLocale);
        for (String header : hearderList) {
            if ("region".equalsIgnoreCase(header)) {
                rowHexin.setRegionId(bo.getBusinessRegionId());
                rowHexin.setRegion(useZhCN ? bo.getBusinessRegionName() : bo.getBusinessRegionNameEn());
            } else if ("subRegion".equalsIgnoreCase(header)) {
                rowHexin.setSubRegionId(bo.getBusinessSubRegionId());
                rowHexin.setSubRegion(useZhCN ? bo.getBusinessSubRegionName() : bo.getBusinessSubRegionNameEn());
            } else if ("country".equalsIgnoreCase(header)) {
                rowHexin.setCountry(useZhCN ? bo.getCountryName() : bo.getCountryNameEn());
            } else if ("examinee".equalsIgnoreCase(header)) {
                rowHexin.setExaminee(bo.getDomainName());
            }
        }
        //景点分层
        rowHexin.setViewspot(viewSpot);
        switch (viewSpot.toLowerCase()) {
            case "核心"://NOSONAR
            case "core":
                rowHexin.setTargetValue(target);
                rowHexin.setCompleteValue(bo.getCwRate());
                rowHexin.setInferiorNum(bo.getCwNum());
                break;
            case "聚焦"://NOSONAR
            case "focus":
                rowHexin.setTargetValue(target);
                rowHexin.setCompleteValue(bo.getFwRate());
                rowHexin.setInferiorNum(bo.getFNum());
                break;
            case "大盘"://NOSONAR
            case "overall":
                rowHexin.setTargetValue(target);
                rowHexin.setCompleteValue(bo.getTwRate());
                rowHexin.setInferiorNum(bo.getTNum());
                break;
        }
        //劣势数差值=日均劣势率-目标劣势率
        rowHexin.setGapValue(rowHexin.getCompleteValue() - rowHexin.getTargetValue());
        return rowHexin;
    }

}
