package com.ctrip.tour.business.dashboard.sightArchives.proxy;


import com.ctrip.ibu.gargle.soa.*;
import com.ctrip.tour.business.dashboard.utils.MapperUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QMapConfig;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ibu机翻接口
 */
@Component
@Slf4j
public class GargleTranslateServiceProxy {

    @QMapConfig("config.properties")
    private Map<String,String> configMap;


    //接入及使用说明  http://conf.ctripcorp.com/pages/viewpage.action?pageId=167779924
    GargleServiceClient client = GargleServiceClient.getInstance();


    public String googleTranslate(String text) {

        GargleTranslationRequest request = new GargleTranslationRequest();
        request.setToken(configMap.get("gargleTranslateToken"));
        request.setTargetLan("en");//目标语言 英文
        request.setText(text);  //待翻译内容

        GargleTranslationResponse response = null;
        try {
            log.info("translate request: text={}", text );
            response = client.translate(request);
            log.info("translate response: {}", MapperUtil.obj2Str(response));

        } catch (Exception e) {
            log.warn("Translation failed for text: {}", text, e);
        }
        if(response == null || response.getResult() == null || StringUtils.isBlank(response.getResult().getTranslation())) {
            log.warn("Translation failed for text: {}, response: {}", text, MapperUtil.obj2Str(response));
            return text; //返回原文
        }

        return response.getResult().getTranslation();  //返回译文

    }

    public Map<String,String> googleBatchTranslate(List<String> textList) {

        GargleBatchTranslationRequest request = new GargleBatchTranslationRequest();
        request.setToken(configMap.get("gargleTranslateToken"));
        request.setUseOriginLan(true);//使用源语言
        request.setOriginLan("zh-CN");//源语言 中文
        request.setTargetLan("en-US");//目标语言 英文
        request.setTextList(textList);  //待翻译内容

        GargleBatchTranslationResponse response = null;
        try {
            log.info("translate request: text={}", textList);
            response = client.batchTranslate(request);
            log.info("translate response: {}", MapperUtil.obj2Str(response));

        } catch (Exception e) {
            log.warn("Translation failed for textList: {}", textList, e);
        }
        if (response == null || response.getResults() == null || CollectionUtils.isEmpty(response.getResults())) {
            log.warn("Translation failed for textList: {}, response: {}", textList, MapperUtil.obj2Str(response));
            return new HashMap<>();
        }
        List<GargleBatchTranslationResult> resultList = response.getResults();
        Map<String,String> map = new HashMap<>();
        for(GargleBatchTranslationResult result : resultList) {
            if(StringUtils.isNotBlank(result.getText()) && StringUtils.isNotBlank(result.getTranslation())) {
                map.put(result.getText(), result.getTranslation());
            }
        }
        return map;

    }


}
