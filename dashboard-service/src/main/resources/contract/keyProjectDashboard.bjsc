include 'BaijiCommonTypes.bjsc'
namespace java 'com.ctrip.soa._24922'

class CheckEmpBusinessTypeRequestType{

}

class CheckEmpBusinessTypeResponseType{
    string empBusinessType;
    BaijiCommonTypes.ResponseStatusType responseStatus; 
}

class GetKeyProjectDashboardConfigRequestType{

}
class GetKeyProjectDashboardConfigResponseType{
    list<KeyProjectDashboardConfig> keyProjectDashboardConfigList;
    BaijiCommonTypes.ResponseStatusType responseStatus; 
}
class KeyProjectDashboardConfig{
    //标题
    string title;
    //标题对应的shark
    string titleSharkKey;
    //artNova看板链接
    string url;
    //0-大区 1-省份 2-景点经理 3-角色
    list<int> filterColumns;
    //看板可见范围
    string displayScope;
    //看板在前端的展示高度
    int boardHeight;
    //是否带入默认筛选项
    int needDefaultFilter;
}

class GetKeyProjectDashboardFilterEnumsRequestType{
    //员工域账号
    string domainName;
    //0-大区 1-省份 2-景点经理 3-角色
    list<int> filterColumns;
}

class GetKeyProjectDashboardFilterEnumsResponseType{
    //大区名称
    list<string> regionNames;
    //省份名称
    list<string> provinceNames;
    //景点经理域账号
    list<string> viewspotMeids;
    //角色名称
    list<string> roleEnums;
    BaijiCommonTypes.ResponseStatusType responseStatus; 
}


@serviceName='BIBusinessDashboardService'
@serviceNamespace='http://soa.ctrip.com/_24922'
service BIBusinessDashboardService {
    //登录用户所属的业务范围
    CheckEmpBusinessTypeResponseType checkEmpBusinessType(CheckEmpBusinessTypeRequestType request);
    //看板配置信息
    GetKeyProjectDashboardConfigResponseType getKeyProjectDashboardConfig(GetKeyProjectDashboardConfigRequestType request);
    //所选员工的大区/省份/景点经理/角色信息
    GetKeyProjectDashboardFilterEnumsResponseType getKeyProjectDashboardFilterEnums(GetKeyProjectDashboardFilterEnumsRequestType requestType);

}