package com.ctrip.tour.business.dashboard.tktBusiness.biz.impl;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.grpBusiness.enums.DomesticMetricEnum;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.DataUpdateBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.MetricDataBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.bo.ExamineConfigBo;
import com.ctrip.tour.business.dashboard.tktBusiness.checker.UserChecker;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardExamineeConfigV2Dao;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardExamineeConfigV2;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.DomesticMetricHelper;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.domestic.DomesticMetricCalStrategy;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.domestic.impl.DomesticMetricCalStrategyBizImpl;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.impl.MetricCalStrategyBizImpl;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.ctrip.tour.business.dashboard.utils.MapperUtil;
import com.ctrip.tour.business.dashboard.utils.ParamterCheckUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/7/28
 */
@Slf4j
@Service
public class MetricDataBizImpl implements MetricDataBiz {

    @Autowired
    private UserChecker userChecker;

    @Autowired
    private BusinessDashboardExamineeConfigV2Dao examineeConfigV2Dao;

    @Autowired
    private MetricCalStrategyBizImpl metricCalStrategyBiz;
    @Autowired
    private DomesticMetricCalStrategyBizImpl domesticMetricCalStrategyBiz;

    @Autowired
    private DataUpdateBiz dataUpdateBiz;

    @Autowired
    private RemoteConfig remoteConfig;

    private final static List<String> regionList=Arrays.asList("华东大区","华南大区","华西大区","华北大区","云南大区");//NOSONAR


    @Override
    public GetMetricCardDataResponseType getMetricCardData(GetMetricCardDataRequestType request) throws Exception {
        MDC.put("metric", "0");
        String domainName = request.getDomainName();
        //快速校验传入的domainName是否存在越权问题
        userChecker.fastCheckInputDomainName(domainName);

        GetMetricCardDataResponseType response = new GetMetricCardDataResponseType();
        List<MetricDetailInfo> metricDetailInfoList = new ArrayList<>();
        response.setMetricDetailInfoList(metricDetailInfoList);
        String d = dataUpdateBiz.getUpdateTime();
//        String d = "2025-07-30";
        TimeFilter timeFilter = request.getTimeFilter();
        boolean checkTimeFilter = ParamterCheckUtil.checkTimeFilterSqlInjection(timeFilter);
        boolean checkDomainName = ParamterCheckUtil.checkDomainNameSqlInjection(domainName);
        if(!checkTimeFilter || !checkDomainName) {
            log.warn("timeFilter or domainName is illegal, timeFilter:{},domainName:{}", timeFilter,domainName);
            return response;
        }
        String year = timeFilter.getYear();
        String dateType = timeFilter.getDateType();
        String quarter = "quarter".equals(dateType) ? timeFilter.getQuarter() : DateUtil.getQuarterOfMonth(timeFilter.getMonth());

        //获取选中的是否是一线BD
        Boolean needRank = false;
        List<BusinessDashboardExamineeConfigV2> bdConfigList = examineeConfigV2Dao.querySpecificPeriodAllMetricConfig(domainName, d, year, quarter, 2);
        if (!GeneralUtil.isEmpty(bdConfigList)) {
            needRank = true;
        }
        response.setNeedRank(needRank);


        //获取应该展示的指标列表
        List<BusinessDashboardExamineeConfigV2> examineeConfigV2List = examineeConfigV2Dao.querySpecificPeriodAllMetricConfig(domainName, d, year, quarter, null);
        ExamineConfigBo bo = new ExamineConfigBo();
        List<MetricInfoBean> metricInfoBeanList = bo.getMetricInfoBeanList(examineeConfigV2List, remoteConfig);
        List<Future<MetricDetailInfo>> futureList = new ArrayList<>();
        for (MetricInfoBean bean : metricInfoBeanList) {
            futureList.add(metricCalStrategyBiz.getSingleMetricCardData(domainName, timeFilter, bean, d, needRank));
        }
        for (Future<MetricDetailInfo> futureResult : futureList) {
            metricDetailInfoList.add(futureResult.get());
        }
        return response;
    }

    @Override
    public GetTrendLineDataResponseType getTrendLineData(GetTrendLineDataRequestType request) throws Exception {

        String domainName = request.getDomainName();
        //快速校验传入的domainName是否存在越权问题
        userChecker.fastCheckInputDomainName(domainName);
        String metric = request.getMetric();
        String d = dataUpdateBiz.getUpdateTime();
        MDC.put("metric", metric);
        TimeFilter timeFilter = request.getTimeFilter();
        String year = timeFilter.getYear();
        String dateType = timeFilter.getDateType();
        String quarter = "quarter".equals(dateType) ? timeFilter.getQuarter() : DateUtil.getQuarterOfMonth(timeFilter.getMonth());
        boolean checkTimeFilter = ParamterCheckUtil.checkTimeFilterSqlInjection(timeFilter);
        boolean checkDomainName = ParamterCheckUtil.checkDomainNameSqlInjection(domainName);
        if(!checkTimeFilter || !checkDomainName) {
            log.warn("timeFilter or domainName is illegal, timeFilter:{},domainName:{}", timeFilter,domainName);
            return new GetTrendLineDataResponseType();
        }

        //获取应该展示的指标列表
        List<BusinessDashboardExamineeConfigV2> examineeConfigV2List = examineeConfigV2Dao.querySpecificPeriodAllMetricConfig(domainName, d, year, quarter, null);
        ExamineConfigBo bo = new ExamineConfigBo();
        MetricInfoBean metricInfoBean = bo.getSingleMetricInfoBean(examineeConfigV2List, remoteConfig, metric);


        return metricCalStrategyBiz.getSingleTrendlineData(request, metricInfoBean, d);
    }

    public void tableSorted(GetTableDataRequestType request, GetTableDataResponseType resp){
        DrillDownFilter drillDownFilter = request.getDrillDownFilter();

        if(drillDownFilter != null){
            String field = request.getDrillDownFilter().getField();
            if(StringUtils.isNotBlank(field) && remoteConfig.getConfigValue("region").equals(field)){
                List<String> regionList = MapperUtil.str2List(remoteConfig.getConfigValue("regionList"), String.class);

                // 使用Collections工具类中的sort方法对infoList进行排序
                Collections.sort(resp.getTableDataItemList(), new Comparator<TableDataItem>() {
                    public int compare(TableDataItem info0, TableDataItem info1) {
                        // 获取两个详细信息所属的大区的下标
                        int index0 = regionList.indexOf(info0.getFieldMap().get("region_name"));
                        int index1 = regionList.indexOf(info1.getFieldMap().get("region_name"));

                        // 按照regionList中的顺序进行排序
                        return (index0 == -1 ? regionList.size() : index0) - (index1 == -1 ? regionList.size() : index1);
                    }
                });
            }
        }

    }

    @Override
    public GetTableDataResponseType getTableData(GetTableDataRequestType request) throws Exception {

        String domainName = request.getDomainName();
        //快速校验传入的domainName是否存在越权问题
        userChecker.fastCheckInputDomainName(domainName);
        String metric = request.getMetric();
        String d = dataUpdateBiz.getUpdateTime();
//        d = "2025-07-30";
        MDC.put("metric", metric);
        TimeFilter timeFilter = request.getTimeFilter();
        boolean checkTimeFilter = ParamterCheckUtil.checkTimeFilterSqlInjection(timeFilter);
        boolean checkDomainName = ParamterCheckUtil.checkDomainNameSqlInjection(domainName);
        if(!checkTimeFilter || !checkDomainName) {
            log.warn("timeFilter or domainName is illegal, timeFilter:{},domainName:{}", timeFilter,domainName);
            return new GetTableDataResponseType();
        }
        String year = timeFilter.getYear();
        String dateType = timeFilter.getDateType();
        String quarter = "quarter".equals(dateType) ? timeFilter.getQuarter() : DateUtil.getQuarterOfMonth(timeFilter.getMonth());

        //获取应该展示的指标列表
        List<BusinessDashboardExamineeConfigV2> examineeConfigV2List = examineeConfigV2Dao.querySpecificPeriodAllMetricConfig(domainName, d, year, quarter, null);
        ExamineConfigBo bo = new ExamineConfigBo();
        MetricInfoBean metricInfoBean = bo.getSingleMetricInfoBean(examineeConfigV2List, remoteConfig, metric);
        GetTableDataResponseType resp = metricCalStrategyBiz.getSingleTableData(request, metricInfoBean, d);
        tableSorted(request, resp);
        return resp;
    }

    @Override
    public GetDomesticTableDataResponseType getDomesticTableData(GetDomesticTableDataRequestType request) throws Exception {
        String domainName = request.getDomainName();
        //快速校验传入的domainName是否存在越权问题
        userChecker.fastCheckInputDomainName(domainName);
        String metric = request.getMetricCode();
        String d = dataUpdateBiz.getUpdateTime();
        MDC.put("metric", metric.toString());
        TimeFilter timeFilter = request.getTimeFilter();
//        boolean checkTimeFilter = ParamterCheckUtil.checkTimeFilterSqlInjection(timeFilter);
//        boolean checkDomainName = ParamterCheckUtil.checkDomainNameSqlInjection(domainName);
//        if (!checkTimeFilter || !checkDomainName) {
//            log.warn("timeFilter or domainName is illegal, timeFilter:{},domainName:{}", timeFilter, domainName);
//            return new GetDomesticTableDataResponseType();
//        }
        String year = timeFilter.getYear();
        String dateType = timeFilter.getDateType();
        List<String> quarter = null;

        if ("quarter".equals(dateType)) {
            quarter = Arrays.asList(timeFilter.getQuarter());
        } else if ("month".equals(dateType)) {
            quarter = Arrays.asList(DateUtil.getQuarterOfMonth(timeFilter.getMonth()));
        } else if ("half".equals(dateType)) {
            quarter = DateUtil.getQuarterOfHalf(timeFilter.getHalf());
        }
        //获取应该展示的指标列表
        Integer metricId = DomesticMetricEnum.getIdByCode(request.getMetricCode());
//        d= "2025-07-30";
        List<BusinessDashboardExamineeConfigV2> examineeConfigV2List = examineeConfigV2Dao.querySpecificPeriodAllMetricConfig(domainName, d, year, quarter, null);
        ExamineConfigBo bo = new ExamineConfigBo();
        MetricInfoBean metricInfoBean = bo.getSingleMetricInfoBeanV2(examineeConfigV2List, remoteConfig, metricId.toString());

        GetDomesticTableDataResponseType resp = domesticMetricCalStrategyBiz.getSingleTableData(request, metricInfoBean, d);
        if("大区".equalsIgnoreCase(request.getDrillDownFilter().getField())){//NOSONAR
            orderByRegionName(resp);
        }
        return resp;
    }

    private void orderByRegionName(GetDomesticTableDataResponseType resp) {
        Map<String, List<DomesticTableData>> tableMap = new HashMap<>();
        if (CollectionUtils.isEmpty(resp.getTableDataItemList())) {
            return;
        }
        for (DomesticTableData item : resp.getTableDataItemList()) {
            if (StringUtils.isEmpty(item.getRegionName())) {
                continue;
            }
            if (tableMap.containsKey(item.getRegionName())) {
                List<DomesticTableData> dataList = tableMap.get(item.getRegionName());
                dataList.add(item);
            } else {
                List<DomesticTableData> dataList = new ArrayList<>();
                dataList.add(item);
                tableMap.put(item.getRegionName(), dataList);
            }
        }
        List<DomesticTableData> tableDataItemList = new ArrayList<>();
        for (String regionName : regionList) {
            List<DomesticTableData> dataList = tableMap.get(regionName);
            if (CollectionUtils.isEmpty(dataList)) {
                continue;
            }
            tableDataItemList.addAll(dataList);
        }
        resp.setTableDataItemList(tableDataItemList);
    }

    @Override
    public GetFirstPageDomesticMetricCardDrillDataResponseType getFirstPageDomesticMetricCardDrillData(
            GetFirstPageDomesticMetricCardDrillDataRequestType request) throws Exception {
        String domainName = request.getDomainName();
        //快速校验传入的domainName是否存在越权问题
        userChecker.fastCheckInputDomainName(domainName);
        String metric = request.getMetricCode();
        String d = dataUpdateBiz.getUpdateTime();
        MDC.put("metric", metric.toString());
        TimeFilter timeFilter = request.getTimeFilter();
        boolean checkTimeFilter = ParamterCheckUtil.checkTimeFilterSqlInjection(timeFilter);
        boolean checkDomainName = ParamterCheckUtil.checkDomainNameSqlInjection(domainName);
        if (!checkTimeFilter || !checkDomainName) {
            log.warn("timeFilter or domainName is illegal, timeFilter:{},domainName:{}", timeFilter, domainName);
            return new GetFirstPageDomesticMetricCardDrillDataResponseType();
        }
        String year = timeFilter.getYear();
        String dateType = timeFilter.getDateType();
        List<String> quarter = null;

        if ("quarter".equals(dateType)) {
            quarter = Arrays.asList(timeFilter.getQuarter());
        } else if ("month".equals(dateType)) {
            quarter = Arrays.asList(DateUtil.getQuarterOfMonth(timeFilter.getMonth()));
        } else if ("half".equals(dateType)) {
            quarter = DateUtil.getQuarterOfHalf(timeFilter.getHalf());
        }
        //获取应该展示的指标列表
        Integer metricId = DomesticMetricEnum.getIdByCode(request.getMetricCode());
//        d= "2025-07-30";

        List<BusinessDashboardExamineeConfigV2> examineeConfigV2List = examineeConfigV2Dao.querySpecificPeriodAllMetricConfig(domainName, d, year, quarter, null);
        ExamineConfigBo bo = new ExamineConfigBo();
        MetricInfoBean metricInfoBean = bo.getSingleMetricInfoBeanV2(examineeConfigV2List, remoteConfig, metricId.toString());

        GetFirstPageDomesticMetricCardDrillDataResponseType resp = domesticMetricCalStrategyBiz.getFirstPageDomesticMetricCardDrillData(request, metricInfoBean, d);
        if ("大区".equalsIgnoreCase(request.getDefaultField())) {//NOSONAR
            orderByRegion(resp);
        }
        return resp;
    }

    private void orderByRegion(GetFirstPageDomesticMetricCardDrillDataResponseType resp) {
        if (CollectionUtils.isEmpty(resp.getTableDataItemList())) {
            return;
        }
        Map<String, List<FirstPageDomesticTableData>> tableMap = new HashMap<>();

        for (FirstPageDomesticTableData item : resp.getTableDataItemList()) {
            if (StringUtils.isEmpty(item.getRegionName())) {
                continue;
            }
            if (tableMap.containsKey(item.getRegionName())) {
                List<FirstPageDomesticTableData> dataList = tableMap.get(item.getRegionName());
                dataList.add(item);
            } else {
                List<FirstPageDomesticTableData> dataList = new ArrayList<>();
                dataList.add(item);
                tableMap.put(item.getRegionName(), dataList);
            }
        }
        List<FirstPageDomesticTableData> tableDataItemListNew = new ArrayList<>();
        for (String regionName : regionList) {
            List<FirstPageDomesticTableData> dataList = tableMap.get(regionName);
            if (CollectionUtils.isEmpty(dataList)) {
                continue;
            }
            tableDataItemListNew.addAll(dataList);
        }
        resp.setTableDataItemList(tableDataItemListNew);
    }

    @Override
    public GetDrillDownBaseInfoResponseType getDrillDownBaseInfo(GetDrillDownBaseInfoRequestType request) throws Exception {

        String domainName = request.getDomainName();
        //快速校验传入的domainName是否存在越权问题
        userChecker.fastCheckInputDomainName(domainName);
        String metric = request.getMetric();
        String d = dataUpdateBiz.getUpdateTime();
        MDC.put("metric", metric);
//d="2025-07-30";
        TimeFilter timeFilter = request.getTimeFilter();
        //兼容一期的情况 如果没有传入时间  则默认置为当前最新时间周期(
        //二期上线后可添加对应业务校验代码并删除该逻辑
        if (GeneralUtil.isEmpty(timeFilter)) {
            timeFilter = new TimeFilter();
            timeFilter.setDateType("quarter");
            timeFilter.setQuarter(DateUtil.getActualQuarterOfD(d));
            timeFilter.setYear(DateUtil.getActualYearOfD(d));
            request.setTimeFilter(timeFilter);
        }
        boolean checkTimeFilter = ParamterCheckUtil.checkTimeFilterSqlInjection(timeFilter);
        boolean checkDomainName = ParamterCheckUtil.checkDomainNameSqlInjection(domainName);
        if(!checkTimeFilter || !checkDomainName) {
            log.warn("timeFilter or domainName is illegal, timeFilter:{},domainName:{}", timeFilter,domainName);
            return new GetDrillDownBaseInfoResponseType();
        }
        String year = timeFilter.getYear();
        String dateType = timeFilter.getDateType();
        String quarter = "quarter".equals(dateType) ? timeFilter.getQuarter() : DateUtil.getQuarterOfMonth(timeFilter.getMonth());

        //获取应该展示的指标列表
        List<BusinessDashboardExamineeConfigV2> examineeConfigV2List = examineeConfigV2Dao.querySpecificPeriodAllMetricConfig(domainName, d, year, quarter, null);
        ExamineConfigBo bo = new ExamineConfigBo();
        MetricInfoBean metricInfoBean = bo.getSingleMetricInfoBean(examineeConfigV2List, remoteConfig, metric);

        // 在这步之前的功能：1. 根据人查询指标权限；2. 根据传参指标查询对应指标的详细信息[其实就是查数仓的表对field进行group]
        return metricCalStrategyBiz.getSingleDrillDownBaseInfo(request, metricInfoBean, d);

    }

    @Override
    public GetDomesticDrillDownBaseInfoResponseType getDomesticDrillDownBaseInfo(GetDomesticDrillDownBaseInfoRequestType request) throws Exception {

        String domainName = request.getDomainName();
        userChecker.fastCheckInputDomainName(domainName);
        String metric = request.getMetricCode();
        String d = dataUpdateBiz.getUpdateTime();
//        d= "2025-07-30";
        TimeFilter timeFilter = request.getTimeFilter();
        if (GeneralUtil.isEmpty(timeFilter)) {
            timeFilter = new TimeFilter();
            timeFilter.setDateType("quarter");
            timeFilter.setQuarter(DateUtil.getActualQuarterOfD(d));
            timeFilter.setYear(DateUtil.getActualYearOfD(d));
            request.setTimeFilter(timeFilter);
        }
//        boolean checkTimeFilter = ParamterCheckUtil.checkTimeFilterSqlInjection(timeFilter);
//        boolean checkDomainName = ParamterCheckUtil.checkDomainNameSqlInjection(domainName);
//        if(!checkTimeFilter || !checkDomainName) {
//            return new GetDomesticDrillDownBaseInfoResponseType();
//        }
        String year = timeFilter.getYear();
        String dateType = timeFilter.getDateType();
        List<String> quarter = null;

        if ("quarter".equals(dateType)) {
            quarter = Arrays.asList(timeFilter.getQuarter());
        } else if ("month".equals(dateType)) {
            quarter = Arrays.asList(DateUtil.getQuarterOfMonth(timeFilter.getMonth()));
        } else if ("half".equals(dateType)) {
            quarter = DateUtil.getQuarterOfHalf(timeFilter.getHalf());
        }

        Integer metricId = DomesticMetricEnum.getIdByCode(request.getMetricCode());
        //获取应该展示的指标列表
        List<BusinessDashboardExamineeConfigV2> examineeConfigV2List = examineeConfigV2Dao.querySpecificPeriodAllMetricConfig(domainName, d, year, quarter, null);
        ExamineConfigBo bo = new ExamineConfigBo();
        MetricInfoBean metricInfoBean = bo.getSingleMetricInfoBeanV2(examineeConfigV2List, remoteConfig, metricId.toString());
        GetDomesticDrillDownBaseInfoResponseType response = domesticMetricCalStrategyBiz.getSingleDrillDownBaseInfo(request, metricInfoBean, d);
        orderRegionValue(response);
        return response;
    }

    private void orderRegionValue(GetDomesticDrillDownBaseInfoResponseType response) {
        if(CollectionUtils.isEmpty(response.getFieldDataItemList())){
            return;
        }
        for(FieldDataItem item:response.getFieldDataItemList()){
            if(!"大区".equalsIgnoreCase(item.getField())){//NOSONAR
                continue;
            }
            List<FieldValueItem> fieldValueItemListNew=new ArrayList<>();
            Map<String,FieldValueItem> fieldValueMap=new HashMap<>();
            for(FieldValueItem fieldValueItem:item.getFieldValueItemList()){
                fieldValueMap.put(fieldValueItem.getValue(),fieldValueItem);
            }
            for(String region:regionList){
                FieldValueItem item1=fieldValueMap.get(region);
                fieldValueItemListNew.add(item1);
            }
            item.setFieldValueItemList(fieldValueItemListNew);
        }
    }


    @Override
    public GetDomesticMetricTrendDataResponseType getDomesticMetricTrendData(GetDomesticMetricTrendDataRequestType request) throws Exception {
        String domainName = request.getDomainName();
        //快速校验传入的domainName是否存在越权问题
        userChecker.fastCheckInputDomainName(domainName);
        String metric = request.getMetricCode();
        String d = dataUpdateBiz.getUpdateTime();
//        String d = "2025-07-30";
        MDC.put("metric", metric);
        TimeFilter timeFilter = request.getTimeFilter();
        boolean checkTimeFilter = ParamterCheckUtil.checkTimeFilterSqlInjectionV2(timeFilter);
        boolean checkDomainName = ParamterCheckUtil.checkDomainNameSqlInjection(domainName);
        if(!checkTimeFilter || !checkDomainName) {
            log.warn("timeFilter or domainName is illegal, timeFilter:{},domainName:{}", timeFilter,domainName);
            return new GetDomesticMetricTrendDataResponseType();
        }
        return domesticMetricCalStrategyBiz.getSingleTrendlineData(request, d);
    }

    @Override
    public GetFirstPageDomesticMetricCardDataResponseType getFirstPageDomesticMetricCardData(GetFirstPageDomesticMetricCardDataRequestType request) throws Exception {
        GetFirstPageDomesticMetricCardDataResponseType response = new GetFirstPageDomesticMetricCardDataResponseType();
        response.setMetricDetailInfoList(getCommonDomesticMetricDetailInfo(request.getTimeFilter(),request.getDomainName(),0,0));
        response.setDateType(request.getTimeFilter().getDateType());
        return response;
    }

    @Override
    public GetDomesticMetricSummaryDataResponseType getDomesticMetricSummaryData(GetDomesticMetricSummaryDataRequestType request) throws Exception {
        GetDomesticMetricSummaryDataResponseType responseType = new GetDomesticMetricSummaryDataResponseType();
        responseType.setMetricDetailInfoList(getCommonDomesticMetricDetailInfo(request.getTimeFilter(),request.getDomainName(),1,0));
        return responseType;
    }

    @Override
    public GetDomesticMetricCardDataResponseType getDomesticMetricCardData(GetDomesticMetricCardDataRequestType request) throws Exception {
        GetDomesticMetricCardDataResponseType responseType = new GetDomesticMetricCardDataResponseType();
        responseType.setMetricDetailInfoList(getCommonDomesticMetricDetailInfo(request.getTimeFilter(),request.getDomainName(),2, request.getBusinessId()));
        return responseType;
    }

    /**
     * 获取指标卡信息（公共部分）
     * @param timeFilter
     * @param domainName
     * @param type 0首页 1大盘页 2详情页
     * @return
     * @throws Exception
     */
    public List<DomesticMetricDetailInfo> getCommonDomesticMetricDetailInfo(TimeFilter timeFilter, String domainName, Integer type, Integer businessId) throws Exception {
        MDC.put("metric", "0");
        //快速校验传入的domainName是否存在越权问题
        userChecker.fastCheckInputDomainName(domainName);

        List<DomesticMetricDetailInfo> metricDetailInfoList = new ArrayList<>();
        String d = dataUpdateBiz.getUpdateTime();
//        String d = "2025-07-30";
        boolean checkTimeFilter = ParamterCheckUtil.checkTimeFilterSqlInjectionV2(timeFilter);
        boolean checkDomainName = ParamterCheckUtil.checkDomainNameSqlInjection(domainName);
        if(!checkTimeFilter || !checkDomainName) {
            log.warn("timeFilter or domainName is illegal, timeFilter:{},domainName:{}", timeFilter,domainName);
            return new ArrayList<>();
        }
        String year = timeFilter.getYear();
        List<String> quarters = DateUtil.getQuarterListWithDiffDateType(timeFilter,d);

        //获取应该展示的指标列表
        Map<String, List<String>> inMap = new HashMap<>();
        inMap.put("quarter", quarters);
        List<BusinessDashboardExamineeConfigV2> examineeConfigV2List = examineeConfigV2Dao.queryExamineConfig(domainName, d, year, inMap, null);
        ExamineConfigBo bo = new ExamineConfigBo();

        List<MetricInfoBean> metricInfoBeanList = bo.getMetricInfoBeanListV2(examineeConfigV2List, remoteConfig);
        Map<String, List<MetricInfoBean>> metricInfoBeanMap = metricInfoBeanList.stream()
                .collect(Collectors.groupingBy(MetricInfoBean::getMetric));
        DomesticMetricHelper.setDomesticMetricCalStrategyMap(metricInfoBeanList,metricInfoBeanMap);
        List<Future<DomesticMetricDetailInfo>> futureList = new ArrayList<>();
        for (Map.Entry<String, List<MetricInfoBean>> beanList : metricInfoBeanMap.entrySet()) {
            boolean isFirst = type == 0 || type == 1;
            futureList.add(domesticMetricCalStrategyBiz.getSingleMetricCardData(domainName, timeFilter, beanList.getValue(), d, isFirst, type, businessId, Integer.parseInt(beanList.getKey())));
        }
        for (Future<DomesticMetricDetailInfo> futureResult : futureList) {
            DomesticMetricDetailInfo bean = futureResult.get();
            if (bean.getMetricCode() != null) {
                metricDetailInfoList.add(futureResult.get());
            }
        }
        removeMetricWithNullTarget(metricDetailInfoList);
        return metricDetailInfoList;
    }

    public void removeMetricWithNullTarget(List<DomesticMetricDetailInfo> metricDetailInfoList) {
        // 移除target为null的指标
        metricDetailInfoList.removeIf(metricDetailInfo -> {
            boolean isRemove = false;
            switch (metricDetailInfo.getMetricCode()) {
                case "gmv":
                case "profit":
                case "quality":
                    if (metricDetailInfo.getTargetValue().compareTo(-1.00) == 0) {
                        isRemove = true;
                    }
                    break;
                case "directSign":
                    if (metricDetailInfo.getTableDataItemList() == null) {
                        isRemove = true;
                    }
                    if (metricDetailInfo.getTableDataItemList().stream().allMatch(sub -> sub.getDirectTargetScore().compareTo(-1) == 0)) {
                        isRemove = true;
                    }
                    break;
                case "categoryCover":
                    if (metricDetailInfo.getTableDataItemList() == null) {
                        isRemove = true;
                    }
                    if (metricDetailInfo.getTableDataItemList().stream().allMatch(sub -> sub.getCategoryTargetCoverageRate().compareTo(0.00) == 0)) {
                        isRemove = true;
                    }
                    break;
                case "ticGoods":
                    if (metricDetailInfo.getSubMetricDetailInfoList() != null) {
                        metricDetailInfo.getSubMetricDetailInfoList().removeIf(subMetricDetailInfo -> {
                            if (subMetricDetailInfo.getTableDataItemList() != null && "directSign".equals(subMetricDetailInfo.getMetricCode())) {
                                return subMetricDetailInfo.getTableDataItemList().stream().allMatch(sub -> sub.getDirectTargetScore().compareTo(-1) == 0);
                            }
                            return false;
                        });
                    }
                    break;
            }
            return isRemove;
        });
    }

}
