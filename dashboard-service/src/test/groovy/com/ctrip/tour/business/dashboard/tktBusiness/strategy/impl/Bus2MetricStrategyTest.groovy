package com.ctrip.tour.business.dashboard.tktBusiness.strategy.impl

import com.ctrip.soa._24922.CheckUserPermissionResponseType
import com.ctrip.soa._24922.GetTrendLineDataRequestType
import com.ctrip.soa._24922.GetTrendLineDataResponseType
import com.ctrip.soa._24922.MetricDetailInfo
import com.ctrip.soa._24922.TimeFilter
import com.ctrip.soa._27181.BIBaseReportQueryServiceClient
import com.ctrip.soa._27181.GetRawDataResponseType
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean
import com.ctrip.tour.business.dashboard.tktBusiness.biz.UserPermissionBiz
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig
import com.ctrip.tour.business.dashboard.utils.MapperUtil
import spock.lang.Specification
import spock.lang.Unroll

class Bus2MetricStrategyTest  extends Specification {


    def bus2TicketActivityStrategy = Mock(Bus2TicketActivityStrategy)

    def bus1And2DayTourStrategy = Mock(Bus1And2DayTourStrategy)

    def userPermissionBiz = Mock(UserPermissionBiz)

    def remoteConfig = Mock(RemoteConfig)

    def baseReportQueryServiceClient = Mock(BIBaseReportQueryServiceClient)

    def bus2MetricStrategy = new Bus2MetricStrategy(bus2TicketActivityStrategy:bus2TicketActivityStrategy, bus1And2DayTourStrategy:bus1And2DayTourStrategy, userPermissionBiz:userPermissionBiz,
            remoteConfig:remoteConfig, baseReportQueryServiceClient:baseReportQueryServiceClient)

//    def "测试指标卡接口"() {
//        given: "设置mock数据结果"
//        String permissionStr1 = "{\"domesticBasicConfig\":{\"metricCardConfigMap\":{\"1\":{\"subMetricList\":[\"ticketActivity+domesticDayTour+overseaDayTour\",\"ticketActivity\",\"domesticDayTour\",\"overseaDayTour\"]},\"2\":{\"subMetricList\":[\"ticketActivity+domesticDayTour+overseaDayTour\",\"ticketActivity\",\"domesticDayTour\",\"overseaDayTour\"]}},\"trendLineConfigMap\":{\"1\":{\"subMetricList\":[\"ticketActivity+domesticDayTour+overseaDayTour\",\"ticketActivity+domesticDayTour\",\"ticketActivity\",\"domesticDayTour\",\"overseaDayTour\"]},\"2\":{\"subMetricList\":[\"ticketActivity+domesticDayTour+overseaDayTour\",\"ticketActivity+domesticDayTour\",\"ticketActivity\",\"domesticDayTour\",\"overseaDayTour\"]}}}}"
//        String permissionStr2 = "{\"domesticBasicConfig\":{\"metricCardConfigMap\":{\"1\":{\"subMetricList\":[\"ticketActivity\"]},\"2\":{\"subMetricList\":[\"ticketActivity\"]}},\"trendLineConfigMap\":{\"1\":{\"subMetricList\":[\"ticketActivity\"]},\"2\":{\"subMetricList\":[\"ticketActivity\"]}}}}"
//        String permissionStr3 = "{\"domesticBasicConfig\":{\"metricCardConfigMap\":{\"1\":{\"subMetricList\":[\"domesticDayTour\"]},\"2\":{\"subMetricList\":[\"domesticDayTour\"]}},\"trendLineConfigMap\":{\"1\":{\"subMetricList\":[\"domesticDayTour\"]},\"2\":{\"subMetricList\":[\"domesticDayTour\"]}}}}"
//
//        userPermissionBiz.checkUserPermission(_) >> MapperUtil.str2Obj(permissionStr1, CheckUserPermissionResponseType.class) >> MapperUtil.str2Obj(permissionStr2, CheckUserPermissionResponseType.class) >> MapperUtil.str2Obj(permissionStr3, CheckUserPermissionResponseType.class)
//
//
//        String ticketActivityStr = "{\"subMetric\":\"ticketActivity\",\"dimData\":{\"ttd_cps_tot_profit|ttd_trgt_profit|/\":0.****************,\"ttd_cps_tot_profit_7days_fenmu_value\":4974816.18,\"ttd_cps_tot_profit_2019\":3.391717576132799,\"ttd_cps_tot_profit_7days\":0.9943604368513574,\"ttd_cps_tot_profit_lastyear\":2.8318615242229384,\"ttd_cps_tot_profit_7days_fenzi_value\":9921576.57,\"ttd_trgt_profit\":2.6533993E7,\"ttd_cps_tot_profit_lastyear_value\":3645356.11,\"ttd_cps_tot_profit\":1.396849982E7,\"ttd_suc_subsidy_rebate_profit\":7333298.83,\"ttd_cps_tot_profit_2019_value\":3180646.2,\"ttd_suc_finext_profit\":6635200.99},\"momType\":\"7days\",\"level\":\"国内\",\"needDrillDown\":true,\"defaultField\":\"大区\"}"
//        bus2TicketActivityStrategy.getSingleMetricCardData(_, _, _, _) >> MapperUtil.str2Obj(ticketActivityStr, MetricDetailInfo.class)
//
//        String domesticDayTourStr = "{\"subMetric\":\"domesticDayTour\",\"dimData\":{\"odt_sys_inner_profit\":2286214.32,\"odt_sys_outer_profit\":0.0,\"odt_trgt_income\":5.6946317E7,\"odt_suc_profit_lastyear_value\":146438.08,\"odt_suc_profit_7days\":-0.10810779836687223,\"odt_suc_income\":1.91073747E7,\"odt_suc_profit_7days_fenzi_value\":1246734.93,\"odt_suc_profit|odt_trgt_profit|/\":0.3701207960380389,\"odt_suc_profit_7days_fenmu_value\":1397853.83,\"odt_suc_profit_2019\":-0.12789479646671364,\"odt_trgt_profit\":6176941.0,\"odt_suc_profit\":2286214.32,\"odt_suc_profit_2019_value\":2621489.14,\"odt_suc_profit_lastyear\":14.612157165677125},\"momType\":\"7days\"}"
//        bus1And2DayTourStrategy.getSingleMetricCardData(_, _, _, _, "domesticDayTour") >> MapperUtil.str2Obj(domesticDayTourStr, MetricDetailInfo.class)
//
//        String overseaDayTourStr = "{\"subMetric\":\"overseaDayTour\",\"dimData\":{\"odt_ob_suc_profit_7days\":-0.10810779836687223,\"odt_ob_suc_profit\":2286214.32,\"odt_ob_trgt_profit\":6176941.0,\"odt_ob_suc_profit_2019\":-0.12789479646671364,\"odt_ob_suc_profit_lastyear_value\":146438.08,\"odt_ob_suc_profit_7days_fenmu_value\":1397853.83,\"odt_ob_suc_profit_2019_value\":2621489.14,\"odt_ob_suc_income\":1.91073747E7,\"odt_ob_trgt_income\":5.6946317E7,\"odt_ob_sys_inner_profit\":2286214.32,\"odt_ob_suc_profit|odt_ob_trgt_profit|/\":0.3701207960380389,\"odt_ob_sys_outer_profit\":0.0,\"odt_ob_suc_profit_lastyear\":14.612157165677125,\"odt_ob_suc_profit_7days_fenzi_value\":1246734.93},\"momType\":\"7days\"}"
//        bus1And2DayTourStrategy.getSingleMetricCardData(_, _, _, _, "overseaDayTour") >> MapperUtil.str2Obj(overseaDayTourStr, MetricDetailInfo.class)
//
//        String rankingStr = "{\"result\":\"[[\\\"80/134\\\",\\\"63/134\\\"]]\",\"metricList\":[\"ranking_gmv\",\"ranking_profit\"]}"
//        baseReportQueryServiceClient.getRawData(_) >> MapperUtil.str2Obj(rankingStr, GetRawDataResponseType.class)
//
//
//
//        and: "设置请求参数"
//        TimeFilter timeFilter = new TimeFilter(dateType: "month", month: "11", year: "2023")
//        MetricInfoBean metricInfoBean = new MetricInfoBean(level: "国内", odtLevel: "国内", overseaOdtLevel: "国内");
//
//        and: "设置qconfig配置"
//        remoteConfig.getConfigValue("domestic") >> "国内"
//        remoteConfig.getConfigValue("region") >> "大区"
//        remoteConfig.getConfigValue("province") >> "省份"
//
//
//        when: "调用指标卡接口"
//        def response1 = bus2MetricStrategy.getSingleMetricCardData("zheli", timeFilter, metricInfoBean, "2023-11-16", false).get()
//        def response2 = bus2MetricStrategy.getSingleMetricCardData("zheli", timeFilter, metricInfoBean, "2023-11-16", true).get()
//        def response3 = bus2MetricStrategy.getSingleMetricCardData("zheli", timeFilter, metricInfoBean, "2023-11-16", true).get()
//
//        then: "验证返回结果"
//        with(response1) {
//            subMetric == "ticketActivity+domesticDayTour+overseaDayTour"
//            defaultField == "大区"
//            dimData.size() == 8
//            subMetricDetailInfoList.size() == 3
//        }
//
//        with(response2) {
//            subMetric == "ticketActivity"
//            defaultField == "大区"
//            dimData.size() == 12
//            subMetricDetailInfoList.size() == 1
//            rank == "63/134"
//        }
//
//        with(response3) {
//            subMetric == "domesticDayTour"
//            defaultField == "大区"
//            dimData.size() == 12
//            subMetricDetailInfoList.size() == 1
//            rank == "63/134"
//        }
//
//
//    }


//    @Unroll
//    def "测试趋势线接口"() {
//        given: "设置mock数据结果"
//        def ticketActivityStr = "{\"level\":\"国内\",\"trendLineDetailInfoList\":[{\"type\":\"lineChart\",\"dim\":\"ttd_cps_tot_profit|ttd_trgt_profit|/\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":1.0331956238627953},{\"time\":\"2023-10\",\"value\":0.8215507939143524},{\"time\":\"2023-11\",\"value\":0.****************}]},{\"type\":\"barChart\",\"dim\":\"ttd_trgt_profit\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":3.9047496E7},{\"time\":\"2023-10\",\"value\":6.8380298E7},{\"time\":\"2023-11\",\"value\":2.6533993E7}]},{\"type\":\"barChart\",\"dim\":\"ttd_cps_tot_profit\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":4.034370199E7},{\"time\":\"2023-10\",\"value\":5.617788811E7},{\"time\":\"2023-11\",\"value\":1.396849982E7}]},{\"type\":\"lineChart\",\"dim\":\"ttd_cps_tot_profit_lastyear\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":1.5705373845072388},{\"time\":\"2023-10\",\"value\":1.5360867430069813},{\"time\":\"2023-11\",\"value\":2.8318615242229384}]},{\"type\":\"lineChart\",\"dim\":\"ttd_cps_tot_profit_lastyear_value\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":1.569465678E7},{\"time\":\"2023-10\",\"value\":2.21514064E7},{\"time\":\"2023-11\",\"value\":3645356.11}]},{\"type\":\"lineChart\",\"dim\":\"ttd_cps_tot_profit_2019\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":0.3682852271994559},{\"time\":\"2023-10\",\"value\":0.6803947644558301},{\"time\":\"2023-11\",\"value\":3.391717576132799}]},{\"type\":\"lineChart\",\"dim\":\"ttd_cps_tot_profit_2019_value\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":2.948486265E7},{\"time\":\"2023-10\",\"value\":3.343136345E7},{\"time\":\"2023-11\",\"value\":3180646.2}]}]}"
//        bus2TicketActivityStrategy.getSingleTrendlineData(_, _, _) >> MapperUtil.str2Obj(ticketActivityStr, GetTrendLineDataResponseType.class)
//
//
//        String domesticDayTourStr = "{\"trendLineDetailInfoList\":[{\"type\":\"lineChart\",\"dim\":\"odt_suc_profit|odt_trgt_profit|/\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":0.9946373564138236},{\"time\":\"2023-10\",\"value\":1.200645286622844},{\"time\":\"2023-11\",\"value\":0.3701207960380389}]},{\"type\":\"lineChart\",\"dim\":\"odt_trgt_profit\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":9340645.0},{\"time\":\"2023-10\",\"value\":1.3303964E7},{\"time\":\"2023-11\",\"value\":6176941.0}]},{\"type\":\"barChart\",\"dim\":\"odt_suc_profit\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":9290554.45},{\"time\":\"2023-10\",\"value\":1.597334167E7},{\"time\":\"2023-11\",\"value\":2286214.32}]},{\"type\":\"lineChart\",\"dim\":\"odt_suc_profit_lastyear\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":5.599733157953346},{\"time\":\"2023-10\",\"value\":9.557841858835758},{\"time\":\"2023-11\",\"value\":14.612157165677125}]},{\"type\":\"lineChart\",\"dim\":\"odt_suc_profit_lastyear_value\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":1407716.68},{\"time\":\"2023-10\",\"value\":1512936.25},{\"time\":\"2023-11\",\"value\":146438.08}]},{\"type\":\"lineChart\",\"dim\":\"odt_suc_profit_2019\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":0.16277916677303983},{\"time\":\"2023-10\",\"value\":0.3090650118041447},{\"time\":\"2023-11\",\"value\":-0.12789479646671364}]},{\"type\":\"lineChart\",\"dim\":\"odt_suc_profit_2019_value\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":7989956.06},{\"time\":\"2023-10\",\"value\":1.220209961E7},{\"time\":\"2023-11\",\"value\":2621489.14}]}]}"
//        bus1And2DayTourStrategy.getSingleTrendlineData({ i -> i.subMetric == "domesticDayTour" }, _) >> MapperUtil.str2Obj(domesticDayTourStr, GetTrendLineDataResponseType.class)
//
//        String overseaDayTourStr = "{\"trendLineDetailInfoList\":[{\"type\":\"barChart\",\"dim\":\"odt_ob_suc_profit\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":9290554.45},{\"time\":\"2023-10\",\"value\":1.597334167E7},{\"time\":\"2023-11\",\"value\":2286214.32}]},{\"type\":\"lineChart\",\"dim\":\"odt_ob_trgt_profit\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":9340645.0},{\"time\":\"2023-10\",\"value\":1.3303964E7},{\"time\":\"2023-11\",\"value\":6176941.0}]},{\"type\":\"lineChart\",\"dim\":\"odt_ob_suc_profit|odt_ob_trgt_profit|/\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":0.9946373564138236},{\"time\":\"2023-10\",\"value\":1.200645286622844},{\"time\":\"2023-11\",\"value\":0.3701207960380389}]},{\"type\":\"lineChart\",\"dim\":\"odt_ob_suc_profit_lastyear_value\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":1407716.68},{\"time\":\"2023-10\",\"value\":1512936.25},{\"time\":\"2023-11\",\"value\":146438.08}]},{\"type\":\"lineChart\",\"dim\":\"odt_ob_suc_profit_lastyear\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":5.599733157953346},{\"time\":\"2023-10\",\"value\":9.557841858835758},{\"time\":\"2023-11\",\"value\":14.612157165677125}]},{\"type\":\"lineChart\",\"dim\":\"odt_ob_suc_profit_2019\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":0.16277916677303983},{\"time\":\"2023-10\",\"value\":0.3090650118041447},{\"time\":\"2023-11\",\"value\":-0.12789479646671364}]},{\"type\":\"lineChart\",\"dim\":\"odt_ob_suc_profit_2019_value\",\"trendLineDataItemList\":[{\"time\":\"2023-09\",\"value\":7989956.06},{\"time\":\"2023-10\",\"value\":1.220209961E7},{\"time\":\"2023-11\",\"value\":2621489.14}]}]}"
//        bus1And2DayTourStrategy.getSingleTrendlineData({ i -> i.subMetric == "overseaDayTour" }, _) >> MapperUtil.str2Obj(overseaDayTourStr, GetTrendLineDataResponseType.class)
//
//
//        and: "设置请求参数"
//        def request = new GetTrendLineDataRequestType(domainName: "zheli", metric: "1", subMetric: subMetric, timeFilter: new TimeFilter(month: 11, year: 2023, dateType: "month", timeFrame: 3))
//        def metricInfoBean = new MetricInfoBean(level: "国内", odtLevel: "国内", overseaOdtLevel: "国内");
//
//        when: "调用指标卡接口"
//        def response = bus2MetricStrategy.getSingleTrendlineData(request, metricInfoBean, "2023-11-16")
//
//        then: "验证结果"
//        with(response){
//            trendLineDetailInfoList.size() == actualSize
//            level == actualLevel
////            trendLineDetailInfoList.get(0).getTrendLineDataItemList().size() == 3
//        }
//
//
//        where: "用表格方式验证多种返回结果"
//        subMetric                                       | actualSize | actualLevel
//        "ticketActivity+domesticDayTour+overseaDayTour" | 4          | null
//        "ticketActivity+domesticDayTour"                | 4          | null
//        "ticketActivity"                                | 7          | "国内"
//        "domesticDayTour"                               | 7          | null
//        "overseaDayTour"                                | 7          | null
//
//    }
}
