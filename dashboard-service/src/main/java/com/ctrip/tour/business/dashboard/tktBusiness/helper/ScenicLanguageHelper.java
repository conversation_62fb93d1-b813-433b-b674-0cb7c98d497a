package com.ctrip.tour.business.dashboard.tktBusiness.helper;

import com.ctrip.ibu.platform.shark.sdk.api.Shark;
import com.ctrip.tour.business.dashboard.utils.UserUtil;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.lang3.StringUtils;

public class ScenicLanguageHelper {

    private static final ImmutableMap<String, String> sharkeyMap = ImmutableMap.<String, String>builder()
            .put("机酒交叉", "key.dashboard.salessituation.pie.flighthotelcross.jijiu") //NOSONAR
            .put("非机酒交叉", "key.dashboard.salessituation.pie.flighthotelcross.feijijiu")//NOSONAR
            .put("二销", "key.dashboard.salessituation.pie.producttype.erxiao")//NOSONAR
            .put("套票", "key.dashboard.salessituation.pie.producttype.taopiao")//NOSONAR
            .put("门票", "key.dashboard.salessituation.pie.producttype.menpiao")//NOSONAR
            .put("其他玩乐", "key.dashboard.salessituation.pie.producttype.qitawanle")//NOSONAR
            .put("分销", "key.dashboard.salessituation.pie.saleschannel.fenxiao")//NOSONAR
            .put("直销", "key.dashboard.salessituation.pie.saleschannel.zhixiao")//NOSONAR
            .put("线下门店", "key.dashboard.salessituation.pie.saleschannel.xianxiamendian")//NOSONAR
            .put("线下（地推票机等）", "key.dashboard.salessituation.pie.saleschannel.xianxiaditui")//NOSONAR
            .put("亲子票1大1小", "key.dashboard.salessituation.pie.tickettype.qinzipiao")//NOSONAR
            .put("其他", "key.dashboard.salessituation.pie.tickettype.qita")//NOSONAR
            .put("大搜", "key.dashboard.trafficanalysis.pie.trafficsource.dasou")//NOSONAR
            .put("攻略其他", "key.dashboard.trafficanalysis.pie.trafficsource.gonglueqita")//NOSONAR
            .put("携程大首页", "key.dashboard.trafficanalysis.pie.trafficsource.xiechengdashouye")//NOSONAR
            .put("攻略内容-笔记", "key.dashboard.trafficanalysis.pie.trafficsource.gonglueneirongbiji")//NOSONAR
            .put("攻略行程", "key.dashboard.trafficanalysis.pie.trafficsource.gongluexingcheng")//NOSONAR
            .put("火车票", "key.dashboard.trafficanalysis.pie.trafficsource.huochepiao")//NOSONAR
            .put("汽车票", "key.dashboard.trafficanalysis.pie.trafficsource.qichepiao")//NOSONAR
            .put("营销", "key.dashboard.trafficanalysis.pie.trafficsource.yingxiao")//NOSONAR
            .put("机票", "key.dashboard.trafficanalysis.pie.trafficsource.jipiao")//NOSONAR
            .put("用车", "key.dashboard.trafficanalysis.pie.trafficsource.yongche")//NOSONAR
            .put("APP其他", "key.dashboard.trafficanalysis.pie.trafficsource.appqita")//NOSONAR
            .put("旅游", "key.dashboard.trafficanalysis.pie.trafficsource.lvyou")//NOSONAR
            .put("酒店", "key.dashboard.trafficanalysis.pie.trafficsource.jiudian")//NOSONAR
            .put("其它", "key.dashboard.trafficanalysis.pie.trafficsource.qita")//NOSONAR
            .put("普通", "key.dashboard.userprofile.pie.userlevel.putong")//NOSONAR
            .put("白银", "key.dashboard.userprofile.pie.userlevel.baiyin")//NOSONAR
            .put("金牌", "key.dashboard.userprofile.pie.userlevel.jinpai")//NOSONAR
            .put("白金", "key.dashboard.userprofile.pie.userlevel.baijin")//NOSONAR
            .put("钻石", "key.dashboard.userprofile.pie.userlevel.zuanshi")//NOSONAR
            .put("金钻", "key.dashboard.userprofile.pie.userlevel.jinzuan")//NOSONAR
            .put("黑钻", "key.dashboard.userprofile.pie.userlevel.heizuan")//NOSONAR
            .put("独自出行", "key.dashboard.userprofile.pie.usertype.duzichuxing")//NOSONAR
            .put("家庭出行", "key.dashboard.userprofile.pie.usertype.jiatingchuxing")//NOSONAR
            .put("结伴出行", "key.dashboard.userprofile.pie.usertype.jiebanchuxing")//NOSONAR
            .put("周边", "key.dashboard.userprofile.pie.local.zhoubian")//NOSONAR
            .put("本地", "key.dashboard.userprofile.pie.local.bendi")//NOSONAR
            .put("境内", "key.dashboard.userprofile.pie.userproportion.jingnei")//NOSONAR
            .put("境外", "key.dashboard.userprofile.pie.userproportion.jingwai")//NOSONAR
            .put("异地", "key.dashboard.userprofile.pie.userproportion.yidi")//NOSONAR
            .put("订单超时取消", "key.dashboard.servicequality.fulfillmentquality.pie.dingdanchaoshi")//NOSONAR
            .put("确认前推翻", "key.dashboard.servicequality.fulfillmentquality.pie.querenqiantuifan")//NOSONAR
            .put("到场无票", "key.dashboard.servicequality.fulfillmentquality.pie.daochangwupiao")//NOSONAR
            .put("抢票失败", "key.dashboard.servicequality.fulfillmentquality.pie.qiangpiaoshibai")//NOSONAR
            .put("凭证发送不及时", "key.dashboard.servicequality.fulfillmentquality.pie.pingzhengbujishi")//NOSONAR
            .put("确认后推翻", "key.dashboard.servicequality.fulfillmentquality.pie.querenhoutuifan")//NOSONAR
            .put("到场无车", "key.dashboard.servicequality.fulfillmentquality.pie.daochangwuche")//NOSONAR
            .put("到场无活动", "key.dashboard.servicequality.fulfillmentquality.pie.daochangwuhuodong")//NOSONAR
            .put("到场无wifi", "key.dashboard.servicequality.fulfillmentquality.pie.daochangwuwifi")//NOSONAR
            .put("资源使用障碍", "key.dashboard.servicequality.fulfillmentquality.pie.ziyuanzhangai")//NOSONAR
            .put("入园障碍", "key.dashboard.servicequality.fulfillmentquality.pie.ruyuanzhangai")//NOSONAR
            .put("集合纠纷", "key.dashboard.servicequality.fulfillmentquality.pie.jihejiufen")//NOSONAR
            .put("司机迟到", "key.dashboard.servicequality.fulfillmentquality.pie.sijichidao")//NOSONAR
            .put("导游服务不符", "key.dashboard.servicequality.fulfillmentquality.pie.daoyoufuwubufu")//NOSONAR
            .put("服务中止", "key.dashboard.servicequality.fulfillmentquality.pie.wufuzhongzhi")//NOSONAR
            .put("接送服务不符", "key.dashboard.servicequality.fulfillmentquality.pie.jiesongfuwubufu")//NOSONAR
            .put("景点不符", "key.dashboard.servicequality.fulfillmentquality.pie.jingdianbufu")//NOSONAR
            .put("游览时间不符", "key.dashboard.servicequality.fulfillmentquality.pie.youlanshijianbufu")//NOSONAR
            .put("游览顺序不符", "key.dashboard.servicequality.fulfillmentquality.pie.youlanshunxubufu")//NOSONAR
            .put("餐食不符", "key.dashboard.servicequality.fulfillmentquality.pie.canshibufu")//NOSONAR
            .put("增加/强制购物", "key.dashboard.servicequality.fulfillmentquality.pie.zengjiaqiangzhigouwu")//NOSONAR
            .put("增加/强制自费", "key.dashboard.servicequality.fulfillmentquality.pie.zengjiaqiangzhizifei")//NOSONAR
            .put("凭证语言不符", "key.dashboard.servicequality.fulfillmentquality.pie.pingzhengyuyanbufu")//NOSONAR
            .put("wifi机器使用不满", "key.dashboard.servicequality.fulfillmentquality.pie.wifibuman")//NOSONAR
            .put("对餐食不满", "key.dashboard.servicequality.fulfillmentquality.pie.duicanshibuman")//NOSONAR
            .put("对车辆不满", "key.dashboard.servicequality.fulfillmentquality.pie.duicheliangbuman")//NOSONAR
            .put("对成团人数不满", "key.dashboard.servicequality.fulfillmentquality.pie.duichengtuanrenshubuman")//NOSONAR
            .put("对景点不满", "key.dashboard.servicequality.fulfillmentquality.pie.duijingdianbuman")//NOSONAR
            .put("对礼品不满", "key.dashboard.servicequality.fulfillmentquality.pie.duilipinbuman")//NOSONAR
            .put("对体验项目不满", "key.dashboard.servicequality.fulfillmentquality.pie.tiyanxiangmubuman")//NOSONAR
            .put("对行程安排不满", "key.dashboard.servicequality.fulfillmentquality.pie.xingchenganpaibuman")//NOSONAR
            .put("对特殊要求未满足不满", "key.dashboard.servicequality.fulfillmentquality.pie.teshuyaoqiubuman")//NOSONAR
            .put("对司导服务不满", "key.dashboard.servicequality.fulfillmentquality.pie.sidaofuwubuman")//NOSONAR
            .put("导游超时未联系", "key.dashboard.servicequality.fulfillmentquality.pie.daoyouchaoshiweilianxi")//NOSONAR
            .put("行车安全", "key.dashboard.servicequality.fulfillmentquality.pie.xingcheanquan")//NOSONAR
            .put("对车上推销商品不满", "key.dashboard.servicequality.fulfillmentquality.pie.cheshangtuixiaobuman")//NOSONAR
            .put("强制好评", "key.dashboard.servicequality.fulfillmentquality.pie.qiangzhihaoping")//NOSONAR
            .put("产品信息错误/缺失", "key.dashboard.servicequality.fulfillmentquality.pie.chanpincuoque")//NOSONAR
            .put("人群信息错误/缺失", "key.dashboard.servicequality.fulfillmentquality.pie.renquncuoque")//NOSONAR
            .put("退改信息错误/缺失", "key.dashboard.servicequality.fulfillmentquality.pie.tuigaicuoque")//NOSONAR
            .put("价格倒挂", "key.dashboard.servicequality.fulfillmentquality.pie.jiagedaogua")//NOSONAR
            .put("价格劣势", "key.dashboard.servicequality.fulfillmentquality.pie.jiagelieshi")//NOSONAR
            .put("成人票", "key.dashboard.competitiveanalysis.productpowercomparison.table.chengrenpiao")//NOSONAR
            .put("儿童票", "key.dashboard.competitiveanalysis.productpowercomparison.table.ertongpiao")//NOSONAR
            .put("老人票", "key.dashboard.competitiveanalysis.productpowercomparison.table.laorenpiao")//NOSONAR
            .put("学生票", "key.dashboard.competitiveanalysis.productpowercomparison.table.xueshengpiao")//NOSONAR
            .put("优待票", "key.dashboard.competitiveanalysis.productpowercomparison.table.youdaipiao")//NOSONAR
            .put("女士票", "key.dashboard.competitiveanalysis.productpowercomparison.table.nvshipiao")//NOSONAR
            .put("情侣票", "key.dashboard.competitiveanalysis.productpowercomparison.table.qinglvpiao")//NOSONAR
            .put("四人票", "key.dashboard.competitiveanalysis.productpowercomparison.table.sirenpiao")//NOSONAR
            .put("医护人员票", "key.dashboard.competitiveanalysis.productpowercomparison.table.yihurenyuan")//NOSONAR
            .put("男士票", "key.dashboard.competitiveanalysis.productpowercomparison.table.nanshipiao")//NOSONAR
            .put("团队票", "key.dashboard.competitiveanalysis.productpowercomparison.table.tuanduipiao")//NOSONAR
            .put("免费票", "key.dashboard.competitiveanalysis.productpowercomparison.table.mianfeipiao")//NOSONAR
            .put("青少年票", "key.dashboard.competitiveanalysis.productpowercomparison.table.qingshaonianpiao")//NOSONAR
            .put("双人票", "key.dashboard.competitiveanalysis.productpowercomparison.table.shuangrenpiao")//NOSONAR
            .put("军人票", "key.dashboard.competitiveanalysis.productpowercomparison.table.junrenpiao")//NOSONAR
            .put("不限人群", "key.dashboard.competitiveanalysis.productpowercomparison.table.buxianrenqun")//NOSONAR
            .put("亲子家庭票", "key.dashboard.competitiveanalysis.productpowercomparison.table.qinzijiatingpiao")//NOSONAR
            .put("覆盖", "key.dashboard.competitiveanalysis.productpowercomparison.table.fugai")//NOSONAR
            .put("缺失", "key.dashboard.competitiveanalysis.productpowercomparison.table.queshi")//NOSONAR
            .put("当天可订", "key.dashboard.competitiveanalysis.productpowercomparison.attractionproduct.dangtiankeding")//NOSONAR
            .put("下单立即确认", "key.dashboard.competitiveanalysis.productpowercomparison.attractionproduct.xiadanlijiqueren")//NOSONAR
            .put("出票立即可用", "key.dashboard.competitiveanalysis.productpowercomparison.attractionproduct.chupiaolijikeyong")//NOSONAR
            .put("扫码/刷证直接入园", "key.dashboard.competitiveanalysis.productpowercomparison.attractionproduct.zhijieriyuan")//NOSONAR
            .put("随时退", "key.dashboard.competitiveanalysis.productpowercomparison.attractionproduct.suishitui")//NOSONAR
            .put("未来15天可订天数12天以上", "key.dashboard.competitiveanalysis.productpowercomparison.attractionproduct.weilaikedingtianshu")//NOSONAR
            .put("APP门票玩乐订单详情页底部banner", "key.dashboard.performancemetrics.placementeffectranking.getadvertisingplacement.appticbottom")//NOSONAR
            .put("APP门票-首页中部banner", "key.dashboard.performancemetrics.placementeffectranking.getadvertisingplacement.appticmiddle")//NOSONAR
            .put("APP携程一日游首页弹窗", "key.dashboard.performancemetrics.placementeffectranking.getadvertisingplacement.apptrip")//NOSONAR
            .put("APP新版一日游首页顶部banner", "key.dashboard.performancemetrics.placementeffectranking.getadvertisingplacement.appnew")//NOSONAR
            .put("APP门票玩乐首页弹窗", "key.dashboard.performancemetrics.placementeffectranking.getadvertisingplacement.appticactfir")//NOSONAR
            .put("APP门票玩乐搜索页banner", "key.dashboard.performancemetrics.placementeffectranking.getadvertisingplacement.appticactsearch")//NOSONAR
            .put("APP门票-景玩列表页信息流", "key.dashboard.performancemetrics.placementeffectranking.getadvertisingplacement.appticlist")//NOSONAR
            .put("门票景点列表页banner", "key.dashboard.performancemetrics.placementeffectranking.getadvertisingplacement.ticactlist")//NOSONAR
            .put("APP门票玩乐首页浮标", "key.dashboard.performancemetrics.placementeffectranking.getadvertisingplacement.appticactfloat")//NOSONAR
            .put("景色", "key.dashboard.servicequality.reviews.getcommentmetric.jingdian")//NOSONAR
            .put("趣味", "key.dashboard.servicequality.reviews.getcommentmetric.quwei")//NOSONAR
            .put("性价比", "key.dashboard.servicequality.reviews.getcommentmetric.xingjiabi")//NOSONAR
            .put("口味", "key.dashboard.servicequality.reviews.getcommentmetric.kouwei")//NOSONAR
            .put("环境", "key.dashboard.servicequality.reviews.getcommentmetric.huanjing")//NOSONAR
            .put("服务", "key.dashboard.servicequality.reviews.getcommentmetric.fuwu")//NOSONAR
            .put("商品", "key.dashboard.servicequality.reviews.getcommentmetric.shangpin")//NOSONAR
            .put("华南大区", "key.dashboard.businessregionname.translate.getsightinfo.huanan")//NOSONAR
            .put("东南亚", "key.dashboard.businessregionname.translate.getsightinfo.dongnanya")//NOSONAR
            .put("华东大区", "key.dashboard.businessregionname.translate.getsightinfo.huadong")//NOSONAR
            .put("欧洲", "key.dashboard.businessregionname.translate.getsightinfo.ouzhou")//NOSONAR
            .put("华西大区", "key.dashboard.businessregionname.translate.getsightinfo.huaxi")//NOSONAR
            .put("海长大区", "key.dashboard.businessregionname.translate.getsightinfo.haichang")//NOSONAR
            .put("泰国", "key.dashboard.businessregionname.translate.getsightinfo.taiguo")//NOSONAR
            .put("云南大区", "key.dashboard.businessregionname.translate.getsightinfo.yunnan")//NOSONAR
            .put("港澳新", "key.dashboard.businessregionname.translate.getsightinfo.gangaoxin")//NOSONAR
            .put("东北亚", "key.dashboard.businessregionname.translate.getsightinfo.dongbeiya")//NOSONAR
            .put("华北大区", "key.dashboard.businessregionname.translate.getsightinfo.huabei")//NOSONAR
            .put("核心", "key.dashboard.viewspotclass.translate.coreticketcompetitive.hexin")//NOSONAR
            .put("聚焦", "key.dashboard.viewspotclass.translate.coreticketcompetitive.jujiao")//NOSONAR
            .put("长尾", "key.dashboard.viewspotclass.translate.coreticketcompetitive.changwei")//NOSONAR
            .put("当天", "key.dashboard.useranalysis.translate.getuserprofilehistogram.currentday")//NOSONAR
            .put("提前1天", "key.dashboard.useranalysis.translate.getuserprofilehistogram.last1")//NOSONAR
            .put("提前2天", "key.dashboard.useranalysis.translate.getuserprofilehistogram.last2")//NOSONAR
            .put("提前3天", "key.dashboard.useranalysis.translate.getuserprofilehistogram.last3")//NOSONAR
            .put("提前4-7天", "key.dashboard.useranalysis.translate.getuserprofilehistogram.last47")//NOSONAR
            .put("提前8-14天", "key.dashboard.useranalysis.translate.getuserprofilehistogram.last814")//NOSONAR
            .put("提前15-31天", "key.dashboard.useranalysis.translate.getuserprofilehistogram.last1531")//NOSONAR
            .put("提前31天以上", "key.dashboard.useranalysis.translate.getuserprofilehistogram.last31up")//NOSONAR
            .put("18岁以下", "key.dashboard.useranalysis.translate.getuserprofilehistogram.year18")//NOSONAR
            .put("18-24岁", "key.dashboard.useranalysis.translate.getuserprofilehistogram.year1824")//NOSONAR
            .put("25-29岁", "key.dashboard.useranalysis.translate.getuserprofilehistogram.year2529")//NOSONAR
            .put("30-34岁", "key.dashboard.useranalysis.translate.getuserprofilehistogram.year3034")//NOSONAR
            .put("35-39岁", "key.dashboard.useranalysis.translate.getuserprofilehistogram.year3539")//NOSONAR
            .put("40-44岁", "key.dashboard.useranalysis.translate.getuserprofilehistogram.year4044")//NOSONAR
            .put("45-49岁", "key.dashboard.useranalysis.translate.getuserprofilehistogram.year4549")//NOSONAR
            .put("50-59岁", "key.dashboard.useranalysis.translate.getuserprofilehistogram.year5059")//NOSONAR
            .put("60岁以上", "key.dashboard.useranalysis.translate.getuserprofilehistogram.year60up")//NOSONAR
            .put("总代", "key.dashboard.performancemetrics.translate.getCooperativeProjectOutput.zongdai")//NOSONAR
            .put("包票", "key.dashboard.performancemetrics.translate.getCooperativeProjectOutput.baopiao")//NOSONAR
            .put("地推", "key.dashboard.performancemetrics.translate.getCooperativeProjectOutput.ditui")//NOSONAR
            .put("票机", "key.dashboard.performancemetrics.translate.getCooperativeProjectOutput.piaoji")//NOSONAR
            .put("优选", "key.dashboard.performancemetrics.translate.getCooperativeProjectOutput.youxuan")//NOSONAR
            .build();



    public static String getMultiLanguage(String chineseValue, String transLanguage) {
        if (StringUtils.isBlank(transLanguage)) {
            return chineseValue;
        }
        String sharkey = sharkeyMap.get(chineseValue);
        if (null == sharkey) {
            return chineseValue;
        }
        String enSharkValue = Shark.getByLocale(100038120, sharkey, "en-US");
        String sharkValue = Shark.getByLocale(100038120, sharkey, transLanguage);
        if (StringUtils.isBlank(sharkValue)) {
            return chineseValue;
        }
        if (StringUtils.isBlank(sharkValue) && !transLanguage.equals("zh-CN") && StringUtils.isNotBlank(enSharkValue)) {
            return enSharkValue;
        }
        return sharkValue;
    }

}
