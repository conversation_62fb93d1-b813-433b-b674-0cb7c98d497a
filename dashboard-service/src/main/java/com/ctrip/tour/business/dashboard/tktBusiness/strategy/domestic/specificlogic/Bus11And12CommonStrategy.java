package com.ctrip.tour.business.dashboard.tktBusiness.strategy.domestic.specificlogic;

import com.alibaba.fastjson.JSONObject;
import com.ctrip.soa._24922.*;
import com.ctrip.soa._27181.GetRawDataRequestType;
import com.ctrip.soa._27181.GetRawDataResponseType;
import com.ctrip.tour.business.dashboard.grpBusiness.enums.DomesticMetricEnum;
import com.ctrip.tour.business.dashboard.sightArchives.dao.domesticmetric.CdmPrdOdtCpdDashboardTmtWeaknessStatisticsDfDao;
import com.ctrip.tour.business.dashboard.sightArchives.dao.domesticmetric.Domestic11And12WeaknessDao;
import com.ctrip.tour.business.dashboard.sightArchives.entity.domesticMetricEntity.AdmPrdTtdCategoryCoverInfoBO;
import com.ctrip.tour.business.dashboard.sightArchives.entity.domesticMetricEntity.Domestic11And12WeaknessBO;
import com.ctrip.tour.business.dashboard.sightArchives.entity.domesticMetricEntity.Domestic9TargetBO;
import com.ctrip.tour.business.dashboard.sightArchives.entity.domesticMetricEntity.bean.Domestic11And12WeaknessParam;
import com.ctrip.tour.business.dashboard.sightArchives.entity.domesticMetricEntity.bean.Domestic9Param;
import com.ctrip.tour.business.dashboard.sightArchives.entity.domesticmetric.*;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.DomesticSingelDrillBaseInfo;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ExamineConfigBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.SqlParamterBean;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.Bus567Dao;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardExamineeConfigV2Dao;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardExamineeConfigV2;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.*;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.*;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class Bus11And12CommonStrategy {

    @Autowired
    private CdmPrdOdtCpdDashboardTmtWeaknessStatisticsDfDao cdmPrdOdtCpdDashboardTmtWeaknessStatisticsDfDao;

    @Autowired
    private SwitchNewTableHelper switchNewTableHelper;

    @Autowired
    Domestic11And12WeaknessDao domestic11And12WeaknessDao;

    @Autowired
    private RemoteConfig remoteConfig;

    @Autowired
    private Bus567Dao bus567Dao;

    @Autowired
    private BusinessDashboardExamineeConfigV2Dao examineeConfigV2Dao;

    public DomesticMetricDetailInfo getSingleMetricCardData(String domainName, TimeFilter timeFilter, List<MetricInfoBean> metricInfoBeanList, String d, Boolean isFirst, Integer metricId) throws Exception {
        DomesticMetricDetailInfo metricDetailInfo = new DomesticMetricDetailInfo();
        metricDetailInfo.setMetricCode(DomesticMetricEnum.getCodeById(metricId));
        Map<String,MetricInfoBean> metricQuarterMap = metricInfoBeanList.stream().collect(Collectors.toMap(MetricInfoBean::getQuarter, metricInfoBean -> metricInfoBean));
        if (metricQuarterMap.isEmpty()) {
            return new DomesticMetricDetailInfo();
        }

        List<String> dateTypeList = new ArrayList<>(metricQuarterMap.keySet());
        if ("month".equals(timeFilter.getDateType())) {
            dateTypeList = Collections.singletonList(timeFilter.getMonth());
        }
        Domestic1112ParamBean domestic1112ParamBean = generate567SearchParamBean(d, timeFilter.getYear(), "1", dateTypeList, domainName, "month".equals(timeFilter.getDateType()),metricId, timeFilter.getMonth());
        Domestic567SearchResult result = cdmPrdOdtCpdDashboardTmtWeaknessStatisticsDfDao.getTmtWeaknessInfo(domestic1112ParamBean);

        int additionalGapDay = Bus567Helper.getAdditionalGapDayV4(timeFilter.getYear(), timeFilter.getDateType(), dateTypeList, timeFilter.getMonth(),timeFilter.getQuarter(),timeFilter.getHalf(), d);
        Integer gapDays = bus567Dao.getGapDaysV2(String.valueOf(metricId), timeFilter.getYear(), timeFilter.getDateType(), d, dateTypeList);
        if (gapDays == null || gapDays <= 0) {
            return new DomesticMetricDetailInfo();
        }
        gapDays = gapDays - additionalGapDay;

        metricDetailInfo.setDisadvantageRate(result.getSightDisadvantageRate() / gapDays);
        metricDetailInfo.setTargetValue(0.03);
        metricDetailInfo.setGapValue(metricDetailInfo.getDisadvantageRate() - 0.03);
        metricDetailInfo.setCompleteRate(computeLevelCompleteRate(metricDetailInfo.getGapValue()));
        metricDetailInfo.setOutSystemCompleteValue(result.getSightDisadvantageCount() / gapDays);

        //设置默认下钻维度
        String maxQuarter = DateUtil.getMaxQuarters(new ArrayList<>(metricQuarterMap.keySet()));
        MetricHelper.setOdtMetricCardDrillDownInfoV2(metricQuarterMap.get(maxQuarter).getOdtLevel(), metricQuarterMap.get(maxQuarter).getOdtRegionList(), metricDetailInfo);
        //获取排名数据
        TimeFilter rankingTimeFilter = DateUtil.getMaxQuarterTimeFilter(timeFilter,d);
        SqlParamterBean rankingBean = Bus11Helper.getRankingSqlBean(domainName, rankingTimeFilter, d,"domesticDayTour");
        GetRawDataRequestType rankingReq = rankingBean.convertBeanToRequest(true);
        GetRawDataResponseType rankingRes = switchNewTableHelper.switchRemoteDatabase(rankingReq);
        Bus11Helper.processRankDataV2(rankingRes, metricDetailInfo);

        return metricDetailInfo;
    }

    // 计算分层完成率
    public double computeLevelCompleteRate(double gap) {
        if (gap <= 0.005) {
            return Math.min(1.2, -40 * gap + 1);
        }else if (gap > 0.005 && gap <= 0.015) {
            return -20 * gap + 0.9;
        }else{
            return 0;
        }
    }

    public Domestic1112ParamBean generate567SearchParamBean(String d,
                                                            String year,
                                                            String statisticDimId,
                                                            List<String> dateTypeList,
                                                            String domainName,
                                                            Boolean isMonth,
                                                            Integer metricId,
                                                            String monthParam) {
        Domestic1112ParamBean paramBean = new Domestic1112ParamBean();
        paramBean.setD(d);
        paramBean.setYear(year);
        paramBean.setStatisticDimId(statisticDimId);
        if (isMonth) {
            paramBean.setMonth(dateTypeList.stream().filter(StringUtils::isNotEmpty).map(e -> e.substring(1)).collect(Collectors.toList()));
        }else{
            paramBean.setQuarters(dateTypeList.stream().filter(StringUtils::isNotEmpty).map(e -> e.substring(1)).collect(Collectors.toList()));
        }
        paramBean.setDomainName(domainName);
        paramBean.setMetricId(String.valueOf(metricId));
        paramBean.setMonthParam(monthParam);
        return paramBean;
    }


    public GetDomesticMetricTrendDataResponseType getTrendLineData(GetDomesticMetricTrendDataRequestType request, String d, Integer metricId) throws Exception {
        GetDomesticMetricTrendDataResponseType response = new GetDomesticMetricTrendDataResponseType();
        List<TrendLineDetailInfo> trendLineDetailInfoList = new ArrayList<>();
        response.setTrendLineDetailInfoList(trendLineDetailInfoList);
        TimeFilter timeFilter = request.getTimeFilter();
        String domainName = request.getDomainName();

        TimeFilter timeFilterWithDomain = DateUtil.getMaxQuarterTimeFilter(timeFilter, d);
        List<ExamineConfigBean> examineConfigBeanList = MultiPeriodMappingHelper.getMultiPeriodConfigList(timeFilterWithDomain, d, examineeConfigV2Dao.queryMetricAllConfig(domainName, d, String.valueOf(metricId)), null);

        TrendLineDetailInfo lineChart = new TrendLineDetailInfo();

        lineChart.setType("lineChart");
        lineChart.setDim(request.getMetricCode());

        List<TrendLineDataItem> lineDataItems = new ArrayList<>();
        lineChart.setTrendLineDataItemList(lineDataItems);
        for (ExamineConfigBean examineConfigBean : examineConfigBeanList) {
            //只下发25年的
            if (Integer.parseInt(examineConfigBean.getYear()) <= 2024) {
                continue;
            }
            //获取单季或单月的数据
            List<TrendLineDataItem> singleLineDataItems = getSingleTrendLineDataWithQuarterOrMonth(request,examineConfigBean, d, metricId);
            lineDataItems.addAll(singleLineDataItems);
        }
        trendLineDetailInfoList.add(lineChart);
        return response;
    }

    public List<TrendLineDataItem> getSingleTrendLineDataWithQuarterOrMonth(GetDomesticMetricTrendDataRequestType request,
                                                                            ExamineConfigBean examineConfigBean,
                                                                            String d,
                                                                            Integer metricId) throws Exception {
        List<TrendLineDataItem> trendLineDataItems = new ArrayList<>();
        String domainName = request.getDomainName();

        String timeString = DomesticMetricHelper.getTimeFormat(examineConfigBean);

        boolean isMonth = "month".equals(examineConfigBean.getDateType());
        List<String> timeList = isMonth ? DateUtil.getMonthList(examineConfigBean.getMonth(), false) : Collections.singletonList(examineConfigBean.getQuarter());

        Domestic1112ParamBean domestic1112ParamBean = generate567SearchParamBean(d, examineConfigBean.getYear(), "1", timeList, domainName, isMonth, DomesticMetricEnum.getIdByCode(request.getMetricCode()), request.getTimeFilter().getMonth());
        Domestic567SearchResult result = cdmPrdOdtCpdDashboardTmtWeaknessStatisticsDfDao.getTmtWeaknessInfo(domestic1112ParamBean);

        int additionalGapDay = Bus567Helper.getAdditionalGapDayV4(request.getTimeFilter().getYear(),request.getTimeFilter().getDateType(),timeList, examineConfigBean.getMonth(),request.getTimeFilter().getQuarter(),request.getTimeFilter().getHalf(),d);
        Integer gapDays = bus567Dao.getGapDaysV2(String.valueOf(metricId), request.getTimeFilter().getYear(), request.getTimeFilter().getDateType(), d, timeList);
        if (gapDays == null || gapDays <= 0) {
            return new ArrayList<>();
        }
        gapDays = gapDays - additionalGapDay;

        TrendLineDataItem disadvantageRateItem = new TrendLineDataItem();
        TrendLineDataItem DisadvantageRateGapItem = new TrendLineDataItem();

        disadvantageRateItem.setTime(timeString);
        DisadvantageRateGapItem.setTime(timeString);

        disadvantageRateItem.setValue(result.getSightDisadvantageRate() / gapDays);
        DisadvantageRateGapItem.setValue(result.getSightDisadvantageRate() / gapDays - 0.03);

        disadvantageRateItem.setName("disadvantageRate");
        DisadvantageRateGapItem.setName("disadvantageRateGap");

        trendLineDataItems.add(disadvantageRateItem);
        trendLineDataItems.add(DisadvantageRateGapItem);

        return trendLineDataItems;
    }


    public static List<String> getTableHeaderList(String field) {
        List<String> tableHeaderList = new ArrayList<>();
        switch (field) {
            case "region_name":
            case "大区"://NOSONAR
                tableHeaderList.add("regionName");
                break;
            case "province_name":
            case "省份"://NOSONAR
                tableHeaderList.add("regionName");
                tableHeaderList.add("provinceName");
                break;
            case "examinee":
            case "商拓"://NOSONAR
                tableHeaderList.add("regionName");
                tableHeaderList.add("provinceName");
                tableHeaderList.add("examinee");
                break;
        }
        tableHeaderList.add("completeRate");
        tableHeaderList.add("targetRate");
        tableHeaderList.add("gapValue");
        return tableHeaderList;
    }


    public static List<String> getFirstTableHeaderList(String field) {
        List<String> tableHeaderList = new ArrayList<>();
        switch (field) {
            case "region_name":
            case "大区"://NOSONAR
                tableHeaderList.add("regionName");
                break;
            case "province_name":
            case "省份"://NOSONAR
                tableHeaderList.add("regionName");
                tableHeaderList.add("provinceName");
                break;
            case "examinee":
            case "商拓"://NOSONAR
                tableHeaderList.add("regionName");
                tableHeaderList.add("provinceName");
                tableHeaderList.add("examinee");
                break;
        }
        tableHeaderList.add("completeRate");
        tableHeaderList.add("disadvantageRate");
        return tableHeaderList;
    }

    public GetDomesticTableDataResponseType getSingleTableData(GetDomesticTableDataRequestType request, MetricInfoBean metricInfoBean, String d) throws Exception {
        GetDomesticTableDataResponseType response = new GetDomesticTableDataResponseType();
        List<DomesticTableData> tableDataItemList = new ArrayList<>();
        response.setTableDataItemList(tableDataItemList);
        Integer metric = DomesticMetricEnum.getIdByCode(request.getMetricCode());
        String field = request.getDrillDownFilter().getField();

        response.setTableHeaderList(getTableHeaderList(field));
        if ("trend".equalsIgnoreCase(request.getQueryType())) {
            List<TrendLineDetailInfo> trendLineDetailInfoList = getDrillThrendLineDetailInfoList(request, metricInfoBean, d);
            response.setTrendLineDetailInfoList(trendLineDetailInfoList);
            return response;
        }

        Domestic11And12WeaknessParam param = getDomestic11And12WeaknessParam(request.getPageNo(),request.getPageSize(),
                request.getTimeFilter(),metric,request.getDrillDownFilter().getField(),metricInfoBean,d);
        param.setMetric(metric.toString());
        //是否剔除top5
        Map<String, String> baseMap = new HashMap<>();
        Bus11Helper.setTimeValue(baseMap,request.getTimeFilter(),null,d);
        Integer addtionalGapDay = Bus11Helper.getAddtionalGapDay(request.getTimeFilter(), null, d, baseMap);
        if (baseMap.size() > 3) {
            param.setIsCoefficientIdentifier(baseMap.get("is_coefficient_identifier"));
        }
        Integer gapDays = getGapDays(request.getTimeFilter(),metric.toString(), d);

        Integer totalCount = domestic11And12WeaknessDao.getDomestic11And12TotalCountByField(param);
        response.setTotalNum(totalCount);
        if (totalCount == null || totalCount == 0) {
            return response;
        }

        List<Domestic11And12WeaknessBO> currentResult = domestic11And12WeaknessDao.getDomestic11And12TableDataByField(param);

        Integer actualGapDays =0;
        if(gapDays!=null&&addtionalGapDay!=null){
            actualGapDays= gapDays - addtionalGapDay;
        }
        //组装数据
        buildDomestic11And12WeaknessBO(actualGapDays,tableDataItemList,currentResult,field);
        return response;
    }

    private void buildDomestic11And12WeaknessBO(Integer actualGapDays,List<DomesticTableData> tableDataItemList,
                                                List<Domestic11And12WeaknessBO> currentResult,
                                                String field) {
        for (Domestic11And12WeaknessBO bo : currentResult) {
            DomesticTableData row = new DomesticTableData();
            switch (field) {
                case "大区"://NOSONAR
                    row.setRegionName(bo.getBusinessRegionName());
                    break;
                case "省份"://NOSONAR
                    row.setRegionName(bo.getBusinessRegionName());
                    row.setProvinceName(bo.getProvinceName());
                    break;
                case "商拓"://NOSONAR
                    row.setRegionName(bo.getBusinessRegionName());
                    row.setProvinceName(bo.getProvinceName());
                    row.setExaminee(bo.getExamine());
                    break;
            }
            //实际劣势率
            //    row.setDisadvantageRate(bo.getWLineRate());
            row.setTargetRate(new Double(0.03));
            row.setCompleteRate(bo.getWLineRate());
            if (actualGapDays != 0) {
                row.setCompleteRate(bo.getWLineRate() / actualGapDays);
            }
            row.setGapValue(row.getCompleteRate() - row.getTargetRate());
            tableDataItemList.add(row);
        }
    }

    private List<TrendLineDetailInfo> getDrillThrendLineDetailInfoList(GetDomesticTableDataRequestType request, MetricInfoBean metricInfoBean, String d) throws Exception{
        TimeFilter timeFilter = request.getTimeFilter();
        String domainName = request.getDomainName();
        Integer metric = DomesticMetricEnum.getIdByCode(request.getMetricCode());
        TimeFilter timeFilterWithDomain = DateUtil.getMaxQuarterTimeFilter(timeFilter, d);
        List<ExamineConfigBean> examineConfigBeanList = MultiPeriodMappingHelper.getMultiPeriodConfigList(timeFilterWithDomain, d, examineeConfigV2Dao.queryMetricAllConfig(domainName, d, String.valueOf(metric)), null);
        List<TrendLineDetailInfo> trendLineDetailInfoList = new ArrayList<>();

        Map<String, List<TrendLineDataItem>> dimRegionThrendMap = new HashMap<>();
        for (ExamineConfigBean examineConfigBean : examineConfigBeanList) {
            //只下发25年的
            if (Integer.parseInt(examineConfigBean.getYear()) <= 2024) {
                continue;
            }
            //获取单季或单月的数据
            Map<String, TrendLineDataItem> dimRegionDBMap = getTrendLineDataByDrill(request, examineConfigBean, d);
            for (Map.Entry<String, TrendLineDataItem> item : dimRegionDBMap.entrySet()) {
                if (dimRegionThrendMap.containsKey(item.getKey())) {
                    List<TrendLineDataItem> lineDataItems = dimRegionThrendMap.get(item.getKey());
                    lineDataItems.add(item.getValue());
                } else {
                    List<TrendLineDataItem> lineDataItems =new ArrayList<>();
                    lineDataItems.add(item.getValue());
                    dimRegionThrendMap.put(item.getKey(), lineDataItems);
                    TrendLineDetailInfo lineChartFwRate = new TrendLineDetailInfo();
                    lineChartFwRate.setType("lineChart");
                    lineChartFwRate.setDim("disadvantageRate");
                    lineChartFwRate.setTrendLineDataItemList(lineDataItems);
                    trendLineDetailInfoList.add(lineChartFwRate);
                }
            }
        }
        return trendLineDetailInfoList;

    }

    private Map<String, TrendLineDataItem> getTrendLineDataByDrill(GetDomesticTableDataRequestType request, ExamineConfigBean examineConfigBean, String d) throws Exception{
        //对不同月份，不同考核层级，不同下钻维度，查出实际劣势率
        String timeString = DomesticMetricHelper.getTimeFormat(examineConfigBean);
        BusinessDashboardExamineeConfigV2 examineeConfigV2 = examineConfigBean.getBusinessDashboardExamineeConfigV2();
        String originLevel = examineeConfigV2.getExamineLevel();
        List<String> regionList = null;
        if (StringUtils.isNotEmpty(examineeConfigV2.getExamineRange())) {
            regionList = Arrays.stream(examineeConfigV2.getExamineRange().split(",")).map(String::trim).collect(Collectors.toList());
        }
        Integer metric = DomesticMetricEnum.getIdByCode(request.getMetricCode());
        MetricInfoBean metricInfoBean=new MetricInfoBean();
        metricInfoBean.setActLevel(originLevel);
        metricInfoBean.setActRegionList(regionList);

        //根据下钻维度获取完成值
        Domestic11And12WeaknessParam param = getDomestic11And12WeaknessParam(request.getPageNo(),request.getPageSize(),
                request.getTimeFilter(),metric,request.getDrillDownFilter().getField(),metricInfoBean,d);
        //是否剔除top5
        Map<String, String> baseMap = new HashMap<>();
        Integer addtionalGapDay = Bus11Helper.getAddtionalGapDay(request.getTimeFilter(), null, d, baseMap);
        if (baseMap.size() > 1) {
            param.setIsCoefficientIdentifier(baseMap.get("is_coefficient_identifier"));
        }
        Integer gapDays = getGapDays(request.getTimeFilter(),metric.toString(), d);

        List<Domestic11And12WeaknessBO> currentResult = domestic11And12WeaknessDao.getDomestic11And12TableDataByField(param);
        Integer actualGapDays = 0;
        if (gapDays != null && addtionalGapDay != null) {
            actualGapDays = gapDays - addtionalGapDay;
        }

        Map<String, TrendLineDataItem> dimRegionThrendMap = new HashMap<>();
        for (Domestic11And12WeaknessBO data : currentResult) {
            TrendLineDataItem item = new TrendLineDataItem();
            switch (request.getDrillDownFilter().getField()) {
                case "大区"://NOSONAR
                    item.setName(data.getBusinessRegionName());
                    dimRegionThrendMap.put(data.getBusinessRegionName(), item);
                    break;
                case "省份"://NOSONAR
                    item.setName(data.getProvinceName());
                    dimRegionThrendMap.put(data.getProvinceName(), item);
                    break;
                case "商拓"://NOSONAR
                    item.setName(data.getExamine());
                    dimRegionThrendMap.put(data.getExamine(), item);
                    break;
            }
            item.setTime(timeString);
            item.setValue(data.getWLineRate());
            if (actualGapDays != 0) {
                item.setValue(data.getWLineRate() / actualGapDays);
            }
        }
        return dimRegionThrendMap;
    }

    private Domestic11And12WeaknessParam getDomestic11And12WeaknessParam(Integer pageIndex,Integer pageSize,
                                                                         TimeFilter timeFilter,
                                                                         Integer metric,
                                                                         String field,
                                                                         MetricInfoBean metricInfoBean,
                                                                         String d)  {
        Domestic11And12WeaknessParam param = new Domestic11And12WeaknessParam();

        param.setD(d);
        param.setPageIndex(pageIndex);
        param.setPageSize(pageSize);
        param.setExamineYear(timeFilter.getYear());
        param.setExamineMetricList(metric.toString());
        param.setField(field);
        if ("half".equalsIgnoreCase(timeFilter.getDateType())) {
            if ("H1".equalsIgnoreCase(timeFilter.getHalf())) {
                param.setExamineQuarter(Arrays.asList("1", "2"));
            } else {
                param.setExamineQuarter(Arrays.asList("3", "4"));
            }
        } else if ("quarter".equalsIgnoreCase(timeFilter.getDateType())) {
            switch (timeFilter.getQuarter()){
                case "Q1":
                    param.setExamineQuarter(Arrays.asList("1"));
                    break;
                case "Q2":
                    param.setExamineQuarter(Arrays.asList("2"));
                    break;
                case "Q3":
                    param.setExamineQuarter(Arrays.asList("3"));
                    break;
                case "Q4":
                    param.setExamineQuarter(Arrays.asList("4"));
                    break;
            }
        } else if ("month".equalsIgnoreCase(timeFilter.getDateType())) {
            param.setExamineMonth(timeFilter.getMonth());
        }
        //下钻维度是否选择，选择后限制对应的范围
        if ("大区".equalsIgnoreCase(field)) {//NOSONAR
            param.setExamineLevel("大区");//NOSONAR
            param.setStatisticsDimId("3");
        } else if ("省份".equalsIgnoreCase(field)) {//NOSONAR
            param.setExamineLevel("省份");//NOSONAR
            param.setStatisticsDimId("4");
        } else if ("商拓".equalsIgnoreCase(field)) {//NOSONAR
            param.setExamineLevel("省份");//NOSONAR
            param.setStatisticsDimId("2");
        }
        //考核范围和考核层级限制:大区限制大区范围，省份限制省份范围
        if (StringUtils.isNotEmpty(metricInfoBean.getActLevel())) {
            switch (metricInfoBean.getActLevel()) {
                case "大区"://NOSONAR
                    param.setBusinessRegionNameList(metricInfoBean.getActRegionList());
                    break;
                case "省份"://NOSONAR
                    param.setProvinceNameList(metricInfoBean.getActRegionList());
                    break;
                case "商拓"://NOSONAR
                    param.setExamine(metricInfoBean.getActRegionList());
                    break;
            }
        } else {
            switch (metricInfoBean.getOdtLevel()) {
                case "大区"://NOSONAR
                    param.setBusinessRegionNameList(metricInfoBean.getOdtRegionList());
                    break;
                case "省份"://NOSONAR
                    param.setProvinceNameList(metricInfoBean.getOdtRegionList());
                    break;
                case "商拓"://NOSONAR
                    param.setExamine(metricInfoBean.getOdtRegionList());
                    break;
            }
        }


        return param;
    }

    public Integer getGapDays(TimeFilter timeFilter,String metric,
                              String d) throws Exception {
        SqlParamterBean gapDaysSqlBean = Bus11Helper.getGapDaysSqlBean(timeFilter.getYear(), timeFilter.getDateType(),
                timeFilter.getMonth(), timeFilter.getQuarter(), metric, d);
        GetRawDataRequestType gapDaysReq = gapDaysSqlBean.convertBeanToRequest(true);
        GetRawDataResponseType gapDaysRes = switchNewTableHelper.switchRemoteDatabase(gapDaysReq);
        return MetricHelper.getGapDays(gapDaysRes);
    }

    public GetDomesticDrillDownBaseInfoResponseType getSingleDrillDownBaseInfo(GetDomesticDrillDownBaseInfoRequestType request, MetricInfoBean metricInfoBean, String d) throws Exception {
        GetDomesticDrillDownBaseInfoResponseType response = new GetDomesticDrillDownBaseInfoResponseType();
        List<FieldDataItem> fieldDataItemList = new ArrayList<>();
        response.setFieldDataItemList(fieldDataItemList);
        Integer metric = DomesticMetricEnum.getIdByCode(request.getMetricCode());

        Boolean needSearch = StringUtils.isNotEmpty(request.getSearchField());
        List<String> fieldList = new ArrayList<>();
        if (needSearch) {
            fieldList.add(MetricHelper.getDrillDownColumnName(request.getSearchField()));
        } else {
            //活动日游考核范围一样，取其中odt范围即可
            fieldList.addAll(MetricHelper.getDomesticDrillDownFieldList(metricInfoBean.getOdtLevel(),metricInfoBean.getOdtRegionList()));
            response.setDefaultChosenField(MetricHelper.getDataBaseColumnName(fieldList.get(0)));
        }
        for (String field : fieldList) {
            String name = MetricHelper.getDataBaseColumnName(field);
            Domestic11And12WeaknessParam param = getDomestic11And12WeaknessParam(null,null,request.getTimeFilter(), metric, name, metricInfoBean, d);
            List<String> rawObjectList= domestic11And12WeaknessDao.getDomestic11And12DrillDataByFeild(param);
            FieldDataItem item = new FieldDataItem();
            item.setNeedBubble(false);
            item.setNeedLine(false);
            DomesticSingelDrillBaseInfo domesticSingelDrillBaseInfo = remoteConfig.getDrillDownFieldBeanV2(request.getMetricCode(), request.getBusinessId(), request.getSubBusinessId(),  name);
            if (domesticSingelDrillBaseInfo != null) {
                item.setNeedBubble(domesticSingelDrillBaseInfo.getNeedBubble());
                item.setNeedLine(domesticSingelDrillBaseInfo.getNeedTrend());
            }
            fieldDataItemList.add(item);
            item.setField(name);
            List<FieldValueItem> fieldValueItemList = new ArrayList<>();
            item.setFieldValueItemList(fieldValueItemList);
            for (String rowResult : rawObjectList) {
                FieldValueItem fieldValueItem = new FieldValueItem();
                fieldValueItem.setValue(rowResult);
                fieldValueItemList.add(fieldValueItem);
            }
        }
        return response;
    }

    public GetFirstPageDomesticMetricCardDrillDataResponseType getFirstPageDomesticMetricCardDrillData(
            GetFirstPageDomesticMetricCardDrillDataRequestType request, MetricInfoBean metricInfoBean, String d) throws Exception {
        GetFirstPageDomesticMetricCardDrillDataResponseType response = new GetFirstPageDomesticMetricCardDrillDataResponseType();
        List<FirstPageDomesticTableData> tableDataItemList = new ArrayList<>();
        response.setTableDataItemList(tableDataItemList);
        Integer metric = DomesticMetricEnum.getIdByCode(request.getMetricCode());
        String field = request.getDefaultField();

        response.setTableHeaderList(getFirstTableHeaderList(field));
        Domestic11And12WeaknessParam param = getDomestic11And12WeaknessParam(null,null,request.getTimeFilter(), metric, request.getDefaultField(), metricInfoBean, d);
        //是否剔除top5
        Map<String, String> baseMap = new HashMap<>();
        Integer addtionalGapDay = Bus11Helper.getAddtionalGapDay(request.getTimeFilter(), null, d, baseMap);
        if (baseMap.size() > 1) {
            param.setIsCoefficientIdentifier(baseMap.get("is_coefficient_identifier"));
        }
        Integer gapDays = getGapDays(request.getTimeFilter(),metric.toString(), d);

        List<Domestic11And12WeaknessBO> currentResult = domestic11And12WeaknessDao.getDomestic11And12TableDataByField(param);
        Integer actualGapDays = 0;
        if (gapDays != null && addtionalGapDay != null) {
            actualGapDays = gapDays - addtionalGapDay;
        }
        //组装数据
        for (Domestic11And12WeaknessBO bo : currentResult) {
            FirstPageDomesticTableData row = new FirstPageDomesticTableData();
            //大区、完成率、劣势率
            switch (field) {
                case "大区"://NOSONAR
                    row.setRegionName(bo.getBusinessRegionName());
                    break;
                case "省份"://NOSONAR
                    row.setRegionName(bo.getBusinessRegionName());
                    row.setProvinceName(bo.getProvinceName());
                    break;
                case "商拓"://NOSONAR
                    row.setRegionName(bo.getBusinessRegionName());
                    row.setProvinceName(bo.getProvinceName());
                    row.setExaminee(bo.getExamine());
                    break;
            }
            //实际劣势率
            row.setDisadvantageRate(bo.getWLineRate());
            row.setDisadvantageTargetRate(new Double(0.03));
            row.setCompleteRate(bo.getWLineCoefficient());
            if (actualGapDays != 0) {
                row.setCompleteRate(bo.getWLineCoefficient() / actualGapDays);
            }
            tableDataItemList.add(row);
        }
        return response;

    }
}
