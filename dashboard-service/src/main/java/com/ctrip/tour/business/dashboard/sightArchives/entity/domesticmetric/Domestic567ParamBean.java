package com.ctrip.tour.business.dashboard.sightArchives.entity.domesticmetric;

import lombok.Data;

import java.util.List;

@Data
public class Domestic567ParamBean {
    // 查询时间
    String d;
    // 年份
    String year;
    // 统计维度（默认1）
    String statisticDimId;
    // 邮箱前缀
    String domainName;
    // 季度列表
    List<String> quarters;
    // 月份
    List<String> month;
    // 指标
    Integer metricId;
    // 传入的月份
    String monthParam;
}
