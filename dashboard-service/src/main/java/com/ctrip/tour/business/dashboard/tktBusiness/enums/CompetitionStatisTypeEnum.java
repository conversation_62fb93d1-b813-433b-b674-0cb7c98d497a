package com.ctrip.tour.business.dashboard.tktBusiness.enums;

public enum CompetitionStatisTypeEnum {
    METRIC_CARD(1, "basic"),
    EXAMINEE(2, "examinee"),
    REGION(3, "region"),
    PROVINCE(4, "province"),
    COUNTRY(5, "country");

    private int code;
    private String name;

    CompetitionStatisTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static CompetitionStatisTypeEnum getByCode(int code) {
        for (CompetitionStatisTypeEnum buType : CompetitionStatisTypeEnum.values()) {
            if (buType.getCode() == code) {
                return buType;
            }
        }
        return METRIC_CARD;
    }
}
