package com.ctrip.tour.business.dashboard.biz.impl

import com.ctrip.tour.business.dashboard.tktBusiness.bean.ContractEventContentSceneBean
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ContractEventSceneBean
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ProductInfoAppealDeniedBean
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ProductInfoRecificationBean
import com.ctrip.tour.business.dashboard.tktBusiness.biz.impl.PushMessageToTaskBizImpl
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig
import com.ctrip.tour.business.dashboard.utils.MapperUtil
import com.ctrip.tour.rights.client.TourRightsServiceClient
import com.ctrip.tour.rights.client.WbCreateTaskRequestType
import com.ctrip.tour.rights.client.WbCreateTaskResponseType
import spock.lang.Specification

class PushMessageToTaskBizImplTest extends Specification {

    def pushClient = Mock(TourRightsServiceClient)
    def remoteConfig = Mock(RemoteConfig)

    def impl = new PushMessageToTaskBizImpl(pushClient: pushClient, remoteConfig: remoteConfig)

    def "test getTemplateMap"() {
        given:
        def templateList = MapperUtil.str2List("[\"product[ID+Name]\",\"changeTimeLimit\",\"rejectReason\"]", String.class)
        def messageList = MapperUtil.str2List("[{\"productId\":51244737,\"productName\":\"【长沙市】向日葵美术馆馆藏展“百年巨匠”系列之“大师归来”吴昌硕、齐白石、黄宾虹、潘天寿大师花鸟画展\",\"vendorId\":\"314316\",\"vendorName\":\"测试代理同步\",\"rectificationDeadline\":\"2024-05-31\",\"rejectReason\":\"hahah, 逗你玩\"}]", ProductInfoAppealDeniedBean.class);

        when:
        def resp = impl.getTemplateMap(templateList, messageList)

        then:
        with(resp) {
            resp.size() > 0
        }

    }

    def "test getTemplate"() {
        given:
        def serviceInfo = MapperUtil.str2Obj("{\"contentType\":1,\"length\":4000,\"templateStr\":\"您有%s供应商产品信息需整改，可点击查看详情\",\"isplace\":true,\"templateList\":[\"count\"]}", ContractEventContentSceneBean.class)
        def templateMap = MapperUtil.str2Obj("{\"count\":\"1\"}", Map.class)


        when:
        def resp = impl.getTemplate(serviceInfo, templateMap)

        then:
        with(resp) {
            resp != null
        }

    }

    def "test getContent"() {
        given:
        def serviceInfo = MapperUtil.str2Obj("{\"contentType\":" + contentType + ",\"length\":" + length + ",\"templateStr\":\"您名下产品将被处理，如有异议请及时申诉\",\"isplace\":false,\"headers\":{\"违规原因\":[\"problemType\"],\"产品\":[\"productId\",\"productName\"],\"最晚整改完成时间\":[\"rectificationDeadline\"],\"申诉链接\":[\"problemUrl\"]}}", ContractEventContentSceneBean.class)
        def messageList = MapperUtil.str2List("[{\"productId\":1,\"productName\":\"八音盒真品陈列馆门票成人票\",\"problemType\":\"违规原因\",\"rectificationDeadline\":\"整改最后完成时间\",\"problemUrl\":\"申诉链接\"}]", ProductInfoRecificationBean.class)

        when:
        def resp = impl.getContent(serviceInfo, messageList)


        then:
        with(resp) {
            resp != null
        }

        where: "多条件查询"
        contentType | length
        1           | 4000
        2           | 3000
    }


    def "object2List"() {
        given: "mock"
        def headers = MapperUtil.str2Obj("{\"违规原因\":[\"problemType\"],\"产品\":[\"productId\",\"productName\"],\"最晚整改完成时间\":[\"rectificationDeadline\"],\"申诉链接\":[\"problemUrl\"]}", Map.class)
        def messageList = MapperUtil.str2List("[{\"productId\":1,\"productName\":\"八音盒真品陈列馆门票成人票\",\"problemType\":\"违规原因\",\"rectificationDeadline\":\"整改最后完成时间\",\"problemUrl\":\"申诉链接\"}]", ProductInfoRecificationBean.class)
        when: "call"
        def resp = impl.object2List(headers, messageList)

        then: "verify"
        with(resp) {
            resp != null
        }
    }

    def "getServiceNumShowInfo"(){
        given: "mock"
        def sceneBean = MapperUtil.str2Obj("{\"sceneType\":\"PRODUCT_INFO_APPEAL_DENIED_BUSINESS_DELIVERY\",\"isSplitScene\":false,\"className\":\"com.ctrip.tour.business.dashboard.bean.ProductInfoAppealDeniedBean\",\"splitType\":0,\"messageType\":2,\"title\":\"产品信息申诉驳回\",\"taskType\":\"TASK_SUPPLIER_REWARD_PUNISHMENT_02\",\"taskName\":\"商品质量信息申诉\",\"eventType\":\"EVENT_SUPPLIER_REWARD_PUNISHMENT_06\",\"serviceNumInfo\":{\"contentType\":1,\"length\":4000,\"templateStr\":\"产品：%s<br/><br/>改正期限：%s<br/><br/>驳回原因：%s\",\"isplace\":true,\"templateList\":[\"product[ID+Name]\",\"changeTimeLimit\",\"rejectReason\"]},\"detailInfo\":{\"contentType\":1,\"length\":4000,\"templateStr\":\"您名下产品信息申诉已驳回，请提醒供应商在改正期限内修改完成<br/><br/>产品：%s<br/><br/>改正期限：%s<br/><br/>驳回原因：%s\",\"isplace\":true,\"templateList\":[\"product[ID+Name]\",\"changeTimeLimit\",\"rejectReason\"]}}", ContractEventSceneBean.class)
        def messageList = MapperUtil.str2List("[{\"productId\":51244737,\"productName\":\"【长沙市】向日葵美术馆馆藏展“百年巨匠”系列之“大师归来”吴昌硕、齐白石、黄宾虹、潘天寿大师花鸟画展\",\"vendorId\":\"314316\",\"vendorName\":\"测试代理同步\",\"rectificationDeadline\":\"2024-05-31\",\"rejectReason\":\"hahah, 逗你玩\"}]", ProductInfoAppealDeniedBean.class)

        when: "call"
        def resp = impl.getServiceNumShowInfo(sceneBean, messageList)

        then: "verify"
        with(resp){
            resp.title != null
        }
    }

    /*def "test getAndUploadAttachment"() {
        given:
        def sceneBean = MapperUtil.str2Obj("{\"sceneType\":\"PRODUCT_INFO_RECTIFICATION_SUPPLIER_DELIVERY\",\"isSplitScene\":false,\"className\":\"com.ctrip.tour.business.dashboard.bean.ProductInfoRecificationBean\",\"splitType\":0,\"messageType\":3,\"title\":\"供应商产品信息整改\",\"taskType\":\"TASK_SUPPLIER_REWARD_PUNISHMENT_01\",\"taskName\":\"商品质量信息整改\",\"eventType\":\"EVENT_SUPPLIER_REWARD_PUNISHMENT_01\",\"serviceNumInfo\":{\"contentType\":1,\"length\":10,\"templateStr\":\"您有%s家供应商产品信息需整改，可点击查看详情\",\"isplace\":true,\"templateList\":[\"count\"]},\"detailInfo\":{\"contentType\":2,\"length\":10,\"templateStr\":\"您名下供应商有产品将被处理，如有异议请联系供应商及时申诉\",\"isplace\":false,\"headers\":{\"违规原因\":[\"problemType\"],\"产品\":[\"productId\",\"productName\"],\"最晚整改完成时间\":[\"rectificationDeadline\"],\"申诉链接\":[\"problemUrl\"]}}}", ContractEventSceneBean.class)
        def messageList = MapperUtil.str2List("[{\"productId\":14952675,\"productName\":\"穿越时空遇见你—致敬作曲大师主题音乐会\",\"problemType\":\"信安文本检测\"},{\"productId\":32903378,\"productName\":\"上海+武陵源一日游【hht产品张家界总统府立即确认两日游】\",\"problemType\":\"信安文本检测\"}]", ProductInfoRecificationBean.class)
        FileServerUtil.upload(_,_,_,_) >> MapperUtil.str2Obj("{\"fileUrl\": \"aaaaaa.xlsx\"}", UploadResult.class)
        when:
        def fileList = impl.getAndUploadAttachment(sceneBean, messageList)

        then:
        with(fileList) {
            fileList.size() > 0
        }
    }*/


    def "handleResponse"() {
        expect: "call && verify"
        impl.handleResponse(request, response)

        where: "多条件验证"
        response                                                                                                                                           | request
        MapperUtil.str2Obj("{\"responseStatus\":{\"ack\":\"Success\",\"errors\":[],\"extension\":[]},\"taskId\":2674792}", WbCreateTaskResponseType.class) | MapperUtil.str2Obj("{\"taskName\":\"商品质量信息申诉\",\"taskType\":\"TASK_SUPPLIER_REWARD_PUNISHMENT_02\",\"taskPropertyList\":[{\"key\":\"EVENT_SUPPLIER_REWARD_PUNISHMENT_06\",\"value\":\"[{\\\"processoreidlist\\\":\\\"TR005148\\\",\\\"content\\\":\\\"您名下产品信息申诉已驳回，请提醒供应商在改正期限内修改完成<br/><br/>产品：51244737_【长沙市】向日葵美术馆馆藏展“百年巨匠”系列之“大师归来”吴昌硕、齐白石、黄宾虹、潘天寿大师花鸟画展<br/><br/>改正期限：2024-05-31<br/><br/>驳回原因：hahah, 逗你玩\\\",\\\"listfiles\\\":[],\\\"trippalinfo\\\":{\\\"title\\\":\\\"产品信息申诉驳回\\\",\\\"content\\\":[{\\\"idx\\\":0,\\\"type\\\":1,\\\"tag\\\":\\\"p\\\",\\\"children\\\":[{\\\"idx\\\":0,\\\"type\\\":0,\\\"text\\\":\\\"产品：51244737_【长沙市】向日葵美术馆馆藏展“百年巨匠”系列之“大师归来”吴昌硕、齐白石、黄宾虹、潘天寿大师花鸟画展<br/><br/>改正期限：2024-05-31<br/><br/>驳回原因：hahah, 逗你玩\\\"}]}]}}]\"}]}", WbCreateTaskRequestType.class)
        MapperUtil.str2Obj("{\"responseStatus\":{\"ack\":\"Success\",\"errors\":[],\"extension\":[]},\"taskId\":null}", WbCreateTaskResponseType.class)    | MapperUtil.str2Obj("", WbCreateTaskRequestType.class)
    }

    def "test pushMessage"() {
        given: "mock"
        def messageList = MapperUtil.str2List("[{\"productId\":1,\"productName\":\"八音盒真品陈列馆门票成人票\",\"problemType\":\"违规原因\",\"rectificationDeadline\":\"整改最后完成时间\",\"problemUrl\":\"申诉链接\"}]", ProductInfoRecificationBean.class)
        def sceneBean = MapperUtil.str2Obj("{\"messageType\":1,\"sceneType\":\"PRODUCT_INFO_RECTIFICATION_BUSINESS_DELIVERY\",\"className\":\"com.ctrip.tour.business.dashboard.bean.ProductInfoRecificationBean\",\"title\":\"产品信息整改\",\"taskType\":\"TASK_SUPPLIER_REWARD_PUNISHMENT_01\",\"taskName\":\"商品质量信息整改\",\"eventType\":\"EVENT_SUPPLIER_REWARD_PUNISHMENT_04\",\"contractEventAggTimeBean\":{\"endTime\":9,\"interval\":1,\"endType\":1},\"serviceNumInfo\":{\"contentType\":1,\"length\":4000,\"templateStr\":\"您有%s家供应商产品信息需整改，可点击查看详情\",\"isplace\":true,\"templateList\":[\"count\"]},\"detailInfo\":{\"contentType\":2,\"length\":4000,\"templateStr\":\"您负责的产品涉嫌违规将被处理，请尽快整改，如有异议，请在最晚整改完成时间前联系供应商及时申诉，逾期不予受理，明细如下：\",\"isplace\":false,\"headers\":{\"供应商\":[\"vendorId\",\"vendorName\"],\"违规原因\":[\"problemType\"],\"产品\":[\"productId\",\"productName\",\"productUrl\"],\"问题描述\":[\"problemDesc\"],\"最晚整改完成时间\":[\"rectificationDeadline\"],\"申诉链接\":[\"problemUrl\"]},\"hyperLinks\":[{\"type\":1,\"apparentIndex\":2,\"head\":\"产品\"},{\"type\":2,\"custom\":\"点击申诉\",\"head\":\"申诉链接\"}]}}", ContractEventSceneBean.class)
        pushClient.wbCreateTask(_) >> MapperUtil.str2Obj("", WbCreateTaskResponseType.class)

        and: "mock qconfig"
        remoteConfig.getConfigValue("isPushSceneMessage") >> true


        expect: "call & verify"
        impl.pushMessage("S20736", messageList, sceneBean)

    }

}
