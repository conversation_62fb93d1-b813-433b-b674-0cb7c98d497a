//package com.ctrip.tour.business.dashboard.grpBusiness.entity;
//
//import javax.persistence.Column;
//import javax.persistence.Entity;
//import javax.persistence.GeneratedValue;
//import javax.persistence.GenerationType;
//import javax.persistence.Id;
//import javax.persistence.Table;
//import com.ctrip.platform.dal.dao.annotation.Database;
//import com.ctrip.platform.dal.dao.annotation.Sensitive;
//import com.ctrip.platform.dal.dao.annotation.Type;
//import java.sql.Types;
//import java.sql.Timestamp;
//
//import com.ctrip.platform.dal.dao.DalPojo;
//
///**
// * <AUTHOR>
// * @date 2024-12-11
// */
//@Entity
//@Database(name = "TtdReportDB_W")
//@Table(name = "cdm_sev_grp_cpr_platform_self_srv_cr_df")
//public class CdmSevGrpCprPlatformSelfSrvCrDf implements DalPojo {
//
//    /**
//     * 主键
//     */
//    @Id
//	@Column(name = "id")
//	@GeneratedValue(strategy = GenerationType.AUTO)
//	@Type(value = Types.BIGINT)
//	private Long id;
//
//    /**
//     * 业务线
//     */
//	@Column(name = "bu_type")
//	@Type(value = Types.VARCHAR)
//	private String buType;
//
//    /**
//     * 产线
//     */
//	@Column(name = "sub_bu_type")
//	@Type(value = Types.VARCHAR)
//	private String subBuType;
//
//    /**
//     * 产品类型ID
//     */
//	@Column(name = "prd_category_id")
//	@Type(value = Types.BIGINT)
//	private Long prdCategoryId;
//
//    /**
//     * 产品类型
//     */
//	@Column(name = "prd_category_name")
//	@Type(value = Types.VARCHAR)
//	private String prdCategoryName;
//
//    /**
//     * 产品形态ID
//     */
//	@Column(name = "prd_pattern_id")
//	@Type(value = Types.BIGINT)
//	private Long prdPatternId;
//
//    /**
//     * 产品形态名称
//     */
//	@Column(name = "prd_pattern_name")
//	@Type(value = Types.VARCHAR)
//	private String prdPatternName;
//
//    /**
//     * 目的地区域
//     */
//	@Column(name = "dest_domain")
//	@Type(value = Types.VARCHAR)
//	private String destDomain;
//
//    /**
//     * 销售渠道
//     */
//	@Column(name = "sale_channel_name")
//	@Type(value = Types.VARCHAR)
//	private String saleChannelName;
//
//    /**
//     * 销售模式
//     */
//	@Column(name = "sale_mode_name")
//	@Type(value = Types.VARCHAR)
//	private String saleModeName;
//
//    /**
//     * 客源地目的地
//     */
//	@Column(name = "tour_region_type")
//	@Type(value = Types.VARCHAR)
//	private String tourRegionType;
//
//    /**
//     * 产品大区ID
//     */
//	@Column(name = "prd_region_id")
//	@Type(value = Types.BIGINT)
//	private Long prdRegionId;
//
//    /**
//     * 产品大区名称
//     */
//	@Column(name = "prd_region_name")
//	@Type(value = Types.VARCHAR)
//	private String prdRegionName;
//
//    /**
//     * 产品经理ID
//     */
//	@Column(name = "pm_eid")
//	@Type(value = Types.VARCHAR)
//	private String pmEid;
//
//    /**
//     * 驻地业务经理
//     */
//	@Column(name = "local_pm_eid")
//	@Type(value = Types.VARCHAR)
//	private String localPmEid;
//
//    /**
//     * 运营一级大区
//     */
//	@Column(name = "dest_first_region")
//	@Type(value = Types.VARCHAR)
//	private String destFirstRegion;
//
//    /**
//     * 目的地城市ID
//     */
//	@Column(name = "dest_city_id")
//	@Type(value = Types.BIGINT)
//	private Long destCityId;
//
//    /**
//     * 目的地城市
//     */
//	@Column(name = "dest_city_name")
//	@Type(value = Types.VARCHAR)
//	private String destCityName;
//
//    /**
//     * 目的地省份ID
//     */
//	@Column(name = "dest_province_id")
//	@Type(value = Types.BIGINT)
//	private Long destProvinceId;
//
//    /**
//     * 目的地省份
//     */
//	@Column(name = "dest_province_name")
//	@Type(value = Types.VARCHAR)
//	private String destProvinceName;
//
//    /**
//     * 目的地国家ID
//     */
//	@Column(name = "dest_country_id")
//	@Type(value = Types.BIGINT)
//	private Long destCountryId;
//
//    /**
//     * 目的地国家
//     */
//	@Column(name = "dest_country_name")
//	@Type(value = Types.VARCHAR)
//	private String destCountryName;
//
//    /**
//     * 产品供应商ID
//     */
//	@Column(name = "vendor_id")
//	@Type(value = Types.BIGINT)
//	private Long vendorId;
//
//    /**
//     * 产品供应商
//     */
//	@Column(name = "vendor_name")
//	@Type(value = Types.VARCHAR)
//	private String vendorName;
//
//    /**
//     * 是否秒杀产品
//     */
//	@Column(name = "is_sec_kill")
//	@Type(value = Types.TINYINT)
//	private Integer isSecKill;
//
//    /**
//     * 订单数
//     */
//	@Column(name = "order_cnt")
//	@Type(value = Types.BIGINT)
//	private Long orderCnt;
//
//    /**
//     * 用户id
//     */
//	@Column(name = "uid")
//	@Type(value = Types.VARCHAR)
//	private String uid;
//
//    /**
//     * 会话类型
//     */
//	@Column(name = "second_session_type")
//	@Type(value = Types.VARCHAR)
//	private String secondSessionType;
//
//    /**
//     * 会话发生日期
//     */
//	@Column(name = "chat_create_date")
//	@Type(value = Types.VARCHAR)
//	private String chatCreateDate;
//
//    /**
//     * 更新时间
//     */
//	@Column(name = "DataChange_LastTime", insertable = false, updatable = false)
//	@Type(value = Types.TIMESTAMP)
//	private Timestamp datachangeLasttime;
//
//	public Long getId() {
//		return id;
//	}
//
//	public void setId(Long id) {
//		this.id = id;
//	}
//
//	public String getBuType() {
//		return buType;
//	}
//
//	public void setBuType(String buType) {
//		this.buType = buType;
//	}
//
//	public String getSubBuType() {
//		return subBuType;
//	}
//
//	public void setSubBuType(String subBuType) {
//		this.subBuType = subBuType;
//	}
//
//	public Long getPrdCategoryId() {
//		return prdCategoryId;
//	}
//
//	public void setPrdCategoryId(Long prdCategoryId) {
//		this.prdCategoryId = prdCategoryId;
//	}
//
//	public String getPrdCategoryName() {
//		return prdCategoryName;
//	}
//
//	public void setPrdCategoryName(String prdCategoryName) {
//		this.prdCategoryName = prdCategoryName;
//	}
//
//	public Long getPrdPatternId() {
//		return prdPatternId;
//	}
//
//	public void setPrdPatternId(Long prdPatternId) {
//		this.prdPatternId = prdPatternId;
//	}
//
//	public String getPrdPatternName() {
//		return prdPatternName;
//	}
//
//	public void setPrdPatternName(String prdPatternName) {
//		this.prdPatternName = prdPatternName;
//	}
//
//	public String getDestDomain() {
//		return destDomain;
//	}
//
//	public void setDestDomain(String destDomain) {
//		this.destDomain = destDomain;
//	}
//
//	public String getSaleChannelName() {
//		return saleChannelName;
//	}
//
//	public void setSaleChannelName(String saleChannelName) {
//		this.saleChannelName = saleChannelName;
//	}
//
//	public String getSaleModeName() {
//		return saleModeName;
//	}
//
//	public void setSaleModeName(String saleModeName) {
//		this.saleModeName = saleModeName;
//	}
//
//	public String getTourRegionType() {
//		return tourRegionType;
//	}
//
//	public void setTourRegionType(String tourRegionType) {
//		this.tourRegionType = tourRegionType;
//	}
//
//	public Long getPrdRegionId() {
//		return prdRegionId;
//	}
//
//	public void setPrdRegionId(Long prdRegionId) {
//		this.prdRegionId = prdRegionId;
//	}
//
//	public String getPrdRegionName() {
//		return prdRegionName;
//	}
//
//	public void setPrdRegionName(String prdRegionName) {
//		this.prdRegionName = prdRegionName;
//	}
//
//	public String getPmEid() {
//		return pmEid;
//	}
//
//	public void setPmEid(String pmEid) {
//		this.pmEid = pmEid;
//	}
//
//	public String getLocalPmEid() {
//		return localPmEid;
//	}
//
//	public void setLocalPmEid(String localPmEid) {
//		this.localPmEid = localPmEid;
//	}
//
//	public String getDestFirstRegion() {
//		return destFirstRegion;
//	}
//
//	public void setDestFirstRegion(String destFirstRegion) {
//		this.destFirstRegion = destFirstRegion;
//	}
//
//	public Long getDestCityId() {
//		return destCityId;
//	}
//
//	public void setDestCityId(Long destCityId) {
//		this.destCityId = destCityId;
//	}
//
//	public String getDestCityName() {
//		return destCityName;
//	}
//
//	public void setDestCityName(String destCityName) {
//		this.destCityName = destCityName;
//	}
//
//	public Long getDestProvinceId() {
//		return destProvinceId;
//	}
//
//	public void setDestProvinceId(Long destProvinceId) {
//		this.destProvinceId = destProvinceId;
//	}
//
//	public String getDestProvinceName() {
//		return destProvinceName;
//	}
//
//	public void setDestProvinceName(String destProvinceName) {
//		this.destProvinceName = destProvinceName;
//	}
//
//	public Long getDestCountryId() {
//		return destCountryId;
//	}
//
//	public void setDestCountryId(Long destCountryId) {
//		this.destCountryId = destCountryId;
//	}
//
//	public String getDestCountryName() {
//		return destCountryName;
//	}
//
//	public void setDestCountryName(String destCountryName) {
//		this.destCountryName = destCountryName;
//	}
//
//	public Long getVendorId() {
//		return vendorId;
//	}
//
//	public void setVendorId(Long vendorId) {
//		this.vendorId = vendorId;
//	}
//
//	public String getVendorName() {
//		return vendorName;
//	}
//
//	public void setVendorName(String vendorName) {
//		this.vendorName = vendorName;
//	}
//
//	public Integer getIsSecKill() {
//		return isSecKill;
//	}
//
//	public void setIsSecKill(Integer isSecKill) {
//		this.isSecKill = isSecKill;
//	}
//
//	public Long getOrderCnt() {
//		return orderCnt;
//	}
//
//	public void setOrderCnt(Long orderCnt) {
//		this.orderCnt = orderCnt;
//	}
//
//	public String getUid() {
//		return uid;
//	}
//
//	public void setUid(String uid) {
//		this.uid = uid;
//	}
//
//	public String getSecondSessionType() {
//		return secondSessionType;
//	}
//
//	public void setSecondSessionType(String secondSessionType) {
//		this.secondSessionType = secondSessionType;
//	}
//
//	public String getChatCreateDate() {
//		return chatCreateDate;
//	}
//
//	public void setChatCreateDate(String chatCreateDate) {
//		this.chatCreateDate = chatCreateDate;
//	}
//
//	public Timestamp getDatachangeLasttime() {
//		return datachangeLasttime;
//	}
//
//	public void setDatachangeLasttime(Timestamp datachangeLasttime) {
//		this.datachangeLasttime = datachangeLasttime;
//	}
//
//}