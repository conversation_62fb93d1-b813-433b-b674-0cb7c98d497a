package com.ctrip.tour.business.dashboard.tktBusiness.strategy.domestic.impl;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.domestic.DomesticMetricCalStrategy;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.domestic.specificlogic.Bus11And12CommonStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.Future;

@Component
public class Bus12DomesticMetricStrategy implements DomesticMetricCalStrategy {

    @Autowired
    private Bus11And12CommonStrategy bus11And12CommonStrategy;

    @Override
    public Future<DomesticMetricDetailInfo> getSingleMetricCardData(String domainName, TimeFilter timeFilter, List<MetricInfoBean> metricInfoBeanList, String d, <PERSON><PERSON><PERSON> isFirst, Integer businessId) throws Exception {
        return new AsyncResult<>(bus11And12CommonStrategy.getSingleMetricCardData(
                domainName, timeFilter, metricInfoBeanList, d, isFirst, getMetricName()));
    }

    @Override
    public GetDomesticMetricTrendDataResponseType getSingleTrendlineData(GetDomesticMetricTrendDataRequestType request, String d) throws Exception {
        return bus11And12CommonStrategy.getTrendLineData(request, d, getMetricName());
    }
    @Override
    public GetFirstPageDomesticMetricCardDrillDataResponseType getFirstPageDomesticMetricCardDrillData(
            GetFirstPageDomesticMetricCardDrillDataRequestType request,MetricInfoBean metricInfoBean, String d) throws Exception{
        return bus11And12CommonStrategy.getFirstPageDomesticMetricCardDrillData(request,metricInfoBean,d);
    }
    @Override
    public GetDomesticTableDataResponseType getSingleTableData(GetDomesticTableDataRequestType request, MetricInfoBean metricInfoBean, String d) throws Exception {
        return bus11And12CommonStrategy.getSingleTableData(request,metricInfoBean,d);
    }

    @Override
    public GetDomesticDrillDownBaseInfoResponseType getSingleDrillDownBaseInfo(GetDomesticDrillDownBaseInfoRequestType request, MetricInfoBean metricInfoBean, String d) throws Exception {
        return bus11And12CommonStrategy.getSingleDrillDownBaseInfo(request, metricInfoBean, d);
    }

    @Override
    public Integer getMetricName() {
        return 12;
    }
}
