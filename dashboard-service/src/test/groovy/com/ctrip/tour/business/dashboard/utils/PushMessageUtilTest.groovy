package com.ctrip.tour.business.dashboard.utils

import com.ctrip.tour.business.dashboard.tktBusiness.bean.ContractHyperLinkBean
import spock.lang.Specification

class PushMessageUtilTest extends Specification {
    def "GetTagHtml"() {
    }

    def "GetBody"() {
    }

    def "VerifyLen"() {
        expect: "call & verify"
        result == PushMessageUtil.verifyLen(html, length)

        where: "多条件验证"
        result  |   html    |   length
        false    |   "11111" |   4
        true    |   "11111" |   7

    }

    def "GetPushMessage"() {
        given: "mock param"
        def result = new ArrayList<>()
        result.add(Arrays.asList("1", "zhangsan", "0.8", "0.7", "120", "2000"));
        result.add(Arrays.asList("2", "lisi", "0.7", "", "110", "11000"));
        result.add(Arrays.asList("2", "lisi", "0.7", "0.9", "110", "10000"));
        result.add(Arrays.asList("2", "lisi", "0.7", "0.9", "110", "0"));

        def headList = Arrays.asList("供应商ID", "供应商名称", "120S回复率", "差评率", "总会话量", "2月GMV");

        when: "call"
        def html = PushMessageUtil.getPushMessage(title, result, headList, length)

        then: "verify"

        where: "多条件验证"
        length  |  title
        4000    | "您名下供应商在XX月份的考核中低于平台预警考核线标准，已被预警。平台预警线：供应商的120s回复率的预警线为【】，差评率预警线为【】"
        10      |   "您名下供应商在XX月份的考核中低于平台预警考核线标准，已被预警。平台预警线：供应商的120s回复率的预警线为【】，差评率预警线为【】"
    }

    def "isOverLength"(){
        expect: "call & verify"
        reps == PushMessageUtil.isOverLength(title, MapperUtil.str2List(result, List.class), MapperUtil.str2List(headList, String.class), length, new ArrayList<ContractHyperLinkBean>())

        where: "多条件"
        reps    |   result  |   headList    |   length      |   title
        true    |   "[[\"1\",\"zhangsan\",\"0.8\",\"0.7\",\"120\",\"2000\"],[\"2\",\"lisi\",\"0.7\",\"\",\"110\",\"11000\"],[\"2\",\"lisi\",\"0.7\",\"0.9\",\"110\",\"10000\"],[\"2\",\"lisi\",\"0.7\",\"0.9\",\"110\",\"0\"]]" |   "[\"供应商ID\",\"供应商名称\",\"120S回复率\",\"差评率\",\"总会话量\",\"AA月GMV\"]" | 4000| "您名下供应商在XX月份的考核中低于平台预警考核线标准，已被预警。平台预警线：供应商的120s回复率的预警线为【】，差评率预警线为【】"
    }

}
