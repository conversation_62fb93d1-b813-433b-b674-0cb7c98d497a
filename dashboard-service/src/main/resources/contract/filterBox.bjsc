include 'BaijiCommonTypes.bjsc'
namespace java 'com.ctrip.soa._24922'

class GetOrganizationByFilterRequestType{
    //上级组织id 不传则获取当前人员信息的直接上级组织
    string orgId;
    //是否需要搜索 搜索模式下为true
    bool needSearch;
    //搜索词
    string searchWord;
    //是否需要返回全量组织架构
    bool needFullData;
    //归属的看板  枚举值有businessDashboard(业绩看板) pkDashboard(竞争力pk看板)
    string belongingBoard;
}


class GetOrganizationByFilterResponseType{
    list<OrganizationItem> organizationItemList;
    BaijiCommonTypes.ResponseStatusType responseStatus; 
}

class OrganizationItem{
    string orgId;
    string orgName;
    //子节点列表
    list<OrganizationItem> children;
    //该节点是否为叶子节点
    bool isLeaf;
}

class GetEmployeeByFilterRequestType{
    //组织id  不传则直接返回当前用户信息
    string orgId;
    //是否需要搜索 搜索模式下为true
    bool needSearch;
    //是否需要递归搜索
    bool needRecursion;
    //搜索词
    string searchWord;
    //该请求的来源  firstpage(首页 后续app端也算是首页的一种) detailpage(详情页)
    string source;
    //当是首页请求的时候 标识一下该首页看板是国内看板还是海外看板 枚举值 oversea 和  domestic
    string boardType;
    //归属的看板  枚举值有businessDashboard(业绩看板) pkDashboard(竞争力pk看板)
    string belongingBoard;
}

class GetEmployeeByFilterResponseType{
    list<EmployeeItem> employeeItemList;
    BaijiCommonTypes.ResponseStatusType responseStatus; 
}

class EmployeeItem{
    //员工号
    string empCode;
    //展示名 trip上展示的名字
    string displayName;
    //邮箱前缀
    string domainName;
    //职位
    string postion;
    //所属组织id
    string orgId;
    //所属组织名称
    string orgName;
}



@serviceName='BIBusinessDashboardService'
@serviceNamespace='http://soa.ctrip.com/_24922'
service BIBusinessDashboardService {
    //获取组织架构
    GetOrganizationByFilterResponseType getOrganizationByFilter(GetOrganizationByFilterRequestType request);
    //获取该组织下对应的人员
    GetEmployeeByFilterResponseType getEmployeeByFilter(GetEmployeeByFilterRequestType request);
}