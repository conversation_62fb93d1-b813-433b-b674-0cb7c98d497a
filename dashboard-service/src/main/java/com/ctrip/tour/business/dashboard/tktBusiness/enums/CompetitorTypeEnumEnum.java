package com.ctrip.tour.business.dashboard.tktBusiness.enums;

public enum CompetitorTypeEnumEnum {
    KLK("tklk"),
    FLY("tfly");

    private String name;

    CompetitorTypeEnumEnum(String name) {
        this.name = name;
    }


    public String getName() {
        return name;
    }

    public static CompetitorTypeEnumEnum getByCode(String code) {
        for (CompetitorTypeEnumEnum buType : CompetitorTypeEnumEnum.values()) {
            if (buType.getName().equals(code)) {
                return buType;
            }
        }
        return null;
    }
}
