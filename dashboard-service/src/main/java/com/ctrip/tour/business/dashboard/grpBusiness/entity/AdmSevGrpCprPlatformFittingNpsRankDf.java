package com.ctrip.tour.business.dashboard.grpBusiness.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Sensitive;
import com.ctrip.platform.dal.dao.annotation.Type;
import java.sql.Types;
import java.sql.Timestamp;

import com.ctrip.platform.dal.dao.DalPojo;

/**
 * <AUTHOR>
 * @date 2024-12-15
 */
@Entity
@Database(name = "TtdReportDB_W")
@Table(name = "adm_sev_grp_cpr_platform_fitting_nps_rank_df")
public class AdmSevGrpCprPlatformFittingNpsRankDf implements DalPojo {

    /**
     * 自增主键
     */
    @Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long id;

    /**
     * 业务主键
     */
	@Column(name = "biz_id")
	@Type(value = Types.VARCHAR)
	private String bizId;

    /**
     * 产线
     */
	@Column(name = "sub_bu_type")
	@Type(value = Types.VARCHAR)
	private String subBuType;

    /**
     * 产品大区/业务经理
     */
	@Column(name = "business_team")
	@Type(value = Types.VARCHAR)
	private String businessTeam;

    /**
     * 统计年份
     */
	@Column(name = "stat_year")
	@Type(value = Types.VARCHAR)
	private String statYear;

    /**
     * 统计月份
     */
	@Column(name = "stat_month")
	@Type(value = Types.VARCHAR)
	private String statMonth;

    /**
     * nps拟合
     */
	@Column(name = "fitting_nps_rate")
	@Type(value = Types.DOUBLE)
	private Double fittingNpsRate;

    /**
     * nps拟合排名
     */
	@Column(name = "fitting_nps_rate_rank")
	@Type(value = Types.BIGINT)
	private Long fittingNpsRateRank;

    /**
     * 参与人数
     */
	@Column(name = "total_cnt")
	@Type(value = Types.BIGINT)
	private Long totalCnt;

    /**
     * 产品形态名称
     */
	@Column(name = "prd_pattern_name")
	@Type(value = Types.VARCHAR)
	private String prdPatternName;

    /**
     * 更新时间
     */
	@Column(name = "datachange_lasttime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;

    /**
     * 分区日期
     */
	@Column(name = "partition_d")
	@Type(value = Types.VARCHAR)
	private String partitionD;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getBizId() {
		return bizId;
	}

	public void setBizId(String bizId) {
		this.bizId = bizId;
	}

	public String getSubBuType() {
		return subBuType;
	}

	public void setSubBuType(String subBuType) {
		this.subBuType = subBuType;
	}

	public String getBusinessTeam() {
		return businessTeam;
	}

	public void setBusinessTeam(String businessTeam) {
		this.businessTeam = businessTeam;
	}

	public String getStatYear() {
		return statYear;
	}

	public void setStatYear(String statYear) {
		this.statYear = statYear;
	}

	public String getStatMonth() {
		return statMonth;
	}

	public void setStatMonth(String statMonth) {
		this.statMonth = statMonth;
	}

	public Double getFittingNpsRate() {
		return fittingNpsRate;
	}

	public void setFittingNpsRate(Double fittingNpsRate) {
		this.fittingNpsRate = fittingNpsRate;
	}

	public Long getFittingNpsRateRank() {
		return fittingNpsRateRank;
	}

	public void setFittingNpsRateRank(Long fittingNpsRateRank) {
		this.fittingNpsRateRank = fittingNpsRateRank;
	}

	public Long getTotalCnt() {
		return totalCnt;
	}

	public void setTotalCnt(Long totalCnt) {
		this.totalCnt = totalCnt;
	}

	public String getPrdPatternName() {
		return prdPatternName;
	}

	public void setPrdPatternName(String prdPatternName) {
		this.prdPatternName = prdPatternName;
	}

	public Timestamp getDatachangeLasttime() {
		return datachangeLasttime;
	}

	public void setDatachangeLasttime(Timestamp datachangeLasttime) {
		this.datachangeLasttime = datachangeLasttime;
	}

	public String getPartitionD() {
		return partitionD;
	}

	public void setPartitionD(String partitionD) {
		this.partitionD = partitionD;
	}

}