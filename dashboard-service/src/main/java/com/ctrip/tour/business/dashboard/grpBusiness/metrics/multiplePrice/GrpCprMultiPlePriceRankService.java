package com.ctrip.tour.business.dashboard.grpBusiness.metrics.multiplePrice;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.base.SQLResult;
import com.ctrip.platform.dal.dao.helper.DalColumnMapRowMapper;
import com.ctrip.tour.business.dashboard.grpBusiness.annotation.IndexAssemblyHandler;
import com.ctrip.tour.business.dashboard.grpBusiness.dao.mysql.AdmPrdGrpMultiplePriceRankWorkPlatformDfDao;
import com.ctrip.tour.business.dashboard.grpBusiness.dao.mysql.AdmSevGrpCprPlatformSelfSrvCrRankDfDao;
import com.ctrip.tour.business.dashboard.grpBusiness.metrics.IndexCommonQueryAbstractSerice;
import com.ctrip.tour.business.dashboard.utils.SqlBuilder;
import com.google.common.base.Joiner;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2024/12/11
 * 自服务覆盖率
 */
@Service
@IndexAssemblyHandler(
        calcFieldName = "rank_val,total_cnt",
        tableName = "adm_prd_grp_avg_multiple_price_work_platform_df")
@Slf4j
public class GrpCprMultiPlePriceRankService extends IndexCommonQueryAbstractSerice {


    private final static String PM_EID = "pm_eid";
    private final static String LOCAL_PM_EID = "local_pm_eid";
    private final static String BU_TYPE = "bu_type";

    @Autowired
    AdmPrdGrpMultiplePriceRankWorkPlatformDfDao multiplePriceRankWorkPlatformDfDao;

    public GrpCprMultiPlePriceRankService(ApplicationContext ac) {
        super(ac);
    }

    @Override
    protected String otherCon(Map<String, ?> param, List<String> groupByCols, String timeAggType) {
//        Object bizMode = param.get("bizMode");
//        Object empCodes = param.get("empCodes");
//        if (bizMode != null && Objects.nonNull(empCodes) && Objects.equals(1, bizMode)) {
//
//            String teamSql = BU_TYPE + " = " + "团队游" + " and (" + PM_EID + " in (" + Joiner.on(",").join((List<String>)empCodes) + ")"
//                    + " or " + LOCAL_PM_EID + " in (" + Joiner.on(",").join((List<String>)empCodes) + "))";
//
//            String priSql = BU_TYPE + " = " + "私家团" + " and " + PM_EID + " in (" + Joiner.on(",").join((List<String>)empCodes) + ")";
//
//            return " ((" + teamSql + ") or (" + priSql + ")) ";
//        }
        return null;
    }

    @Override
    protected SqlBuilder selectColsAssembly(SqlBuilder sqlBuilder, Map<String, ?> param, List<String> groupByCols, String timeAggType) {

        String sql = " price_rank as rank_val, " +
                " total_price_cnt as total_cnt ";

        if (CollectionUtils.isNotEmpty(groupByCols)) {
            sql = String.join(",", sql, Joiner.on(",").skipNulls().join(groupByCols));
        }

        sqlBuilder.select(sql);

        return sqlBuilder;
    }

    @Override
    protected List<Map<String, Object>> queryData(String sql, Map<String, ?> param, List<String> groupByCols, String timeAggType) {
        List<Map<String, Object>> queryData = null;
        try {
            DalColumnMapRowMapper dalColumnMapRowMapper = new DalColumnMapRowMapper();
            queryData = multiplePriceRankWorkPlatformDfDao.query(sql, new DalHints(), SQLResult.mapper(dalColumnMapRowMapper));
            return queryData;
        } catch (SQLException e) {
            log.warn("query adm_prd_grp_multiple_price_rank_work_platform_df error", e);

        }
        return null;
    }

    @Override
    protected List<Map<String, Object>> handleResults(IndexCommonQueryAbstractSerice serice, Map<String, ?> param, String timeAggType, boolean needTimeAgg, List<Map<String, Object>> rowData, List<String> groupCols) {
        return doHandleResults(serice, param, timeAggType, needTimeAgg, rowData);
    }
}
