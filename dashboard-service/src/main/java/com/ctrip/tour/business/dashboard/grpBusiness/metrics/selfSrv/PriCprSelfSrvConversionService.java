package com.ctrip.tour.business.dashboard.grpBusiness.metrics.selfSrv;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import com.ctrip.platform.dal.dao.helper.DalColumnMapRowMapper;
import com.ctrip.tour.business.dashboard.grpBusiness.annotation.IndexAssemblyHandler;
import com.ctrip.tour.business.dashboard.grpBusiness.dao.starrocks.CdmSevGrpCprPlatformSelfSrvCrDfDao;
import com.ctrip.tour.business.dashboard.grpBusiness.enums.MetricCategoryEnum;
import com.ctrip.tour.business.dashboard.grpBusiness.metrics.IndexCommonQueryAbstractSerice;
import com.ctrip.tour.business.dashboard.utils.SqlBuilder;
import com.google.common.base.Joiner;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2024/12/11
 * 自服务覆盖率
 */
@Service
@IndexAssemblyHandler(calcDateName = "chat_create_date",
        calcExpression = "order_count / ct_user_count", calcFieldName = "order_count,ct_user_count",
        tableName = "cdm_sev_grp_cpr_platform_self_srv_cr_df")
@Slf4j
public class PriCprSelfSrvConversionService extends IndexCommonQueryAbstractSerice {


    private final static String PM_EID = "pm_eid";
    private final static String LOCAL_PM_EID = "local_pm_eid";
    private final static String BU_TYPE = "bu_type";

    @Autowired
    CdmSevGrpCprPlatformSelfSrvCrDfDao selfSrvCrDfDao;

    public PriCprSelfSrvConversionService(ApplicationContext ac) {
        super(ac);
    }

    @Override
    protected String otherCon(Map<String, ?> param, List<String> groupByCols, String timeAggType) {
        return doOtherCon(param, MetricCategoryEnum.SELF_SERVICE_CATEGORY.getEnglishName());

    }

    @Override
    protected SqlBuilder selectColsAssembly(SqlBuilder sqlBuilder, Map<String, ?> param, List<String> groupByCols, String timeAggType) {

        String sql = " sum(case when second_session_type = '商户自服务会话' then ord_cnt end ) as order_count, " +//NOSONAR
                " count(distinct case when second_session_type = '商户自服务会话' then uid end ) as ct_user_count ";//NOSONAR

        if (CollectionUtils.isNotEmpty(groupByCols)) {
            sql = String.join(",", sql, Joiner.on(",").skipNulls().join(groupByCols));
        }

        sqlBuilder.select(sql);

        return sqlBuilder;
    }

    @Override
    protected List<Map<String, Object>> queryData(String sql, Map<String, ?> param, List<String> groupByCols, String timeAggType) {
        List<Map<String, Object>> queryData = null;
        try {
            DalColumnMapRowMapper dalColumnMapRowMapper = new DalColumnMapRowMapper();
            queryData = selfSrvCrDfDao.query(sql, param);
            return queryData;
        } catch (SQLException e) {
            log.warn("query cdm_sev_grp_cpr_platform_self_srv_cr_df error", e);

        }
        return null;
    }

    @Override
    protected List<Map<String, Object>> handleResults(IndexCommonQueryAbstractSerice serice, Map<String, ?> param, String timeAggType, boolean needTimeAgg, List<Map<String, Object>> rowData, List<String> groupCols) {
        return doHandleResults(serice, param, timeAggType, needTimeAgg, rowData);
    }
}
