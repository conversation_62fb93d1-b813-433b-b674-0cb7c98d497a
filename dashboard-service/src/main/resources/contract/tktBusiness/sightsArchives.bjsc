include 'BaijiCommonTypes.bjsc'
namespace java 'com.ctrip.soa._24922'


class CheckSightArchivesPermissionRequestType{

}
class CheckSightArchivesPermissionResponseType{
    BaijiCommonTypes.ResponseStatusType responseStatus;
    bool havePermission;
}


class GetSightArchivesUpdateTimeRequestType{

}
class GetSightArchivesUpdateTimeResponseType{
    BaijiCommonTypes.ResponseStatusType responseStatus;
    string updateTime;
}


class SearchSightListRequestType{
    string searchKey;
}
class SearchSightListResponseType{
    BaijiCommonTypes.ResponseStatusType responseStatus;
    list<Sight> sightList;
}
class Sight{
    long sightId;
    string sightName;
}


class GetSightInfoRequestType{
    long sightId;
}
class GetSightInfoResponseType{
    SightInfo sightInfo;
    BaijiCommonTypes.ResponseStatusType responseStatus;
}
class SightInfo{
    //景点id
    long sightId;
    //景点名称
    string sightName;
    //true-国内、false-海外
    bool domestic;
    //是否拥有子景点
    bool haveSubSight;
    //景点状态
    bool valid;
    //景点等级
    string sightHierarchy;
    //景点分层
    string sightLevel;
    //业务板块
    string businessSegments;
    //景点经理姓名
    list<string> mEmpName;
    //景点助理姓名
    list<string> aEmpName;
    //是否有编辑权限 true-有权限、false-无权限
    bool haveEditPermission;
    //年入园量
    double annualIntakeValue;
    //上榜情况
    list<string> rankDescList;
}


class UpdateAnnualIntakeRequestType{
    //景点id
    long sightId;
    //年入园量
    double annualIntakeValue;
}
class UpdateAnnualIntakeResponseType{
    BaijiCommonTypes.ResponseStatusType responseStatus;
    bool success;
    double newAnnualIntakeValue;
}

class SearchVendorListRequestType{
    long sightId;
    string searchKey;
}
class SearchVendorListResponseType{
    BaijiCommonTypes.ResponseStatusType responseStatus;
    list<Vendor> vendorList;
}
class Vendor{
    long vendorId;
    string vendorName;
}

class SightArchivesCommonFilter{
    //景点id
    long sightId;
    //开始日期
    string startDate;
    //结束日期 
    string endDate;
    //日期类型:1-使用日期、2-预订日期(只影响业绩表现、用户分析、履约质量)
    int dateType;
    //true-需要计算子景点/false-无需计算子景点
    bool needSubSight;
    //业务类型:1-全部(默认)、2-门票、3-活动(只影响业绩表现、服务质量)
    int businessType;
    //供应商id
    list<long> vendorIdList;
}

class GetSightArchivesReportSummaryRequestType{
    SightArchivesCommonFilter commonFilter;
}
class GetSightArchivesReportSummaryResponseType{
    BaijiCommonTypes.ResponseStatusType responseStatus;
    list<RadarMetricItem> RadarMetricList;
    SightArchivesSummaryTextData summaryTextData;
}
class RadarMetricItem{
    //指标名: 
    string metricName;
    //指标值
    double value;
    //上限
    double upperLimit;
    //指标下限
    double lowerLimit;
}
class SightArchivesSummaryTextData{
    int gmv;
    //gmv同比
    double gmvYoy;
    //uv同比
    double uvYoy;
    //商品力劣势
    list<string> competitivenessDisadvantageList;
    //票种缺失项
    list<string> uncoveredTicketTypeList;
}


class GetSalesMetricCardRequestType{
    SightArchivesCommonFilter commonFilter;
}
class GetSalesMetricCardResponseType{
    BaijiCommonTypes.ResponseStatusType responseStatus;
    list<SightArchivesSalesMetric> metricList;
}
class SightArchivesSalesMetric{
    //指标名称
    string metricName;
    //指标值
    double metricValue;
    //环比上周期
    double popValue;
    //同比去年
    double yoyValue;
}

class GetSalesMetricTrendLineRequestType{
    SightArchivesCommonFilter commonFilter;
    string metricName;
}
class GetSalesMetricTrendLineResponseType{
    BaijiCommonTypes.ResponseStatusType responseStatus;
    list<SightArchivesSalesMetricTrendLineItem> dateList;
}
class SightArchivesSalesMetricTrendLineItem{
    //日期
    string date;
    //指标值
    double metricValue;
    //去年同期指标值
    double lastYearMetricValue;
    //同比去年
    double yoyValue;
}

class GetSalesMetricPieChartRequestType{
    SightArchivesCommonFilter commonFilter;
    string metricName;
}
class GetSalesMetricPieChartResponseType{
    BaijiCommonTypes.ResponseStatusType responseStatus;
    //饼图
    list<SightArchivesSalesMetricPieChart> pieChartList;
}
class SightArchivesSalesMetricPieChart{
    //(返回英文)产品类型-productType, 票种人群-ticketType, 渠道-channel, 机酒交叉-planeTrainCrossover
    string dillDownDim;
    list<SalesPieChartSegment> pieChartSegmentList;
}
class SalesPieChartSegment{
    //维度下枚举值名称,即饼图中各小块名称
    string name;
    //指标占比值
    double metricValue;
    //上周期环比
    double popValue;
    //去年同比
    double yoyValue;
    //毛利率
    double profitRate;
    //(退订率分母)提交订单量
    int submitOrderCount;
}

class GetSalesMetricRankTableRequestType{
    SightArchivesCommonFilter commonFilter;
    string metricName;
    //下钻维度：1-票种、2-供应商、3-分销商
    int drillDownDim;
    int pageNo;
    int pageSize;
}
class GetSalesMetricRankTableResponseType{
    BaijiCommonTypes.ResponseStatusType responseStatus;
    list<SalesMetricTableRow> tableRowList;
    int totalNum;
}
class SalesMetricTableRow{
    //排名
    int rank;
    //ID
    long id;
    //维度下枚举值名称
    string name;
    //是否直签（仅存在于供应商维度表格）
    bool directContract;
    //gmv
    int gmv;
    //gmv占比
    double gmvPercentage;
    //毛利
    int profit;
    //毛利占比
    double profitPercentage;
    //毛利率
    double profitRate;
    //客单价
    double averageOrderValue;
    //退订订单量
    int fullyRefundedOrderCount;
    //订单量
    int orderCount;
    //退订率
    double fullyRefundedOrderRate;
    //订单量占比
    double orderCountPercentage;
    //票量
    int soldTicketCount;
    //票量占比
    double soldTicketCountPercentage;
}


class GetCooperativeProjectOutputRequestType{
    SightArchivesCommonFilter commonFilter;
}
class GetCooperativeProjectOutputResponseType{
    BaijiCommonTypes.ResponseStatusType responseStatus;
    list<CooperativeProject> cooperativeProjectList;
}
class CooperativeProject{
    //合作项目名称
    string name;
    //gmv
    int gmv;
    //gmv占比
    double gmvPercentage;
    //毛利
    int profit;
    //毛利占比
    double profitPercentage;
    //票量
    int soldTicketCount;
    //票量占比
    double soldTicketCountPercentage;
}


class GetMarketingCampaignRequestType{
    SightArchivesCommonFilter commonFilter;
}
class GetMarketingCampaignResponseType{
    BaijiCommonTypes.ResponseStatusType responseStatus;
    list<MarketingCampaign> marketingCampaignList;
}
class MarketingCampaign{
    //营销主活动-名称
    string marketingCampaignName;
    //营销主活动-时间
    string dateRange;
    //营销主活动-曝光量(uv)
    int uv;
    //营销子活动-名称
    string name;
    //营销子活动-订单量
    int orderCount;
    //营销子活动-gmv
    int gmv;
}
class SubMarketingCampaign{
    //营销子活动名称
    string name;
    //订单量
    int orderCount;
    //gmv
    int gmv;
}

class GetAdvertisingPlacementRequestType{
    SightArchivesCommonFilter commonFilter;
    int pageNo;
    int pageSize;
}
class GetAdvertisingPlacementResponseType{
    BaijiCommonTypes.ResponseStatusType responseStatus;
    //投放页面表格
    list<DeliveryPage> deliveryPageList;
    int totalNum;
    //投放省份名单
    list<DeliveryProvince> deliveryProvinceList;
}
class DeliveryPage{
    //排名
    int rank;
    //投放页面位置
    string deliveryPageName;
    //投放时间
    string dateRange;
    //曝光次数
    int exposureCount;
    //点击次数
    int clickCount;
    //点击率
    double clickRate;
    //访客量
    int visitorCount;
    //点击用户量
    int clickedUserCount;
}
class DeliveryProvince{
    //投放地名称
    string name;
    //投放地名称(多语言)
    string translateName;
    int count;
}

class GetFlowMetricRequestType{
    SightArchivesCommonFilter commonFilter;
}
class GetFlowMetricResponseType{
    BaijiCommonTypes.ResponseStatusType responseStatus;
    //流量来源分布饼图
    list<FlowSource> flowSourceList;
    //流量转化漏斗
    FlowConversionFunnel flowConversionFunnel;
}
class FlowSource{
    //渠道名称
    string channelName;
    //流量来源占比
    double flowSourcePercentage;
    //去年同比
    double yoyValue;
}
class FlowConversionFunnel{
    //详情页UV
    int detailPageUv;
    //订单页UV
    int orderPageUv;
    //提交订单量
    int submitOrderCount;
    //下单转化率
    double orderConversionRate;
    //成交转化率
    double transactionConversionRate;
}


class GetFlowMetricTrendLineRequestType{
    SightArchivesCommonFilter commonFilter;
}
class GetFlowMetricTrendLineResponseType{
    BaijiCommonTypes.ResponseStatusType responseStatus;
    //趋势线
    list<SightArchivesFlowMetricTrendLineItem> dateList;
    //淡季月份
    list<int> offPeakMonthList;
    //旺季月份
    list<int> peakMonthList;
}
class SightArchivesFlowMetricTrendLineItem{
    string date;
    int uv;
    double conversionRate;
}


class GetUserProfileHistogramRequestType{
    SightArchivesCommonFilter commonFilter;
}
class GetUserProfileHistogramResponseType{
    BaijiCommonTypes.ResponseStatusType responseStatus;
    //提前预订天数-订单量柱状图
    list<UserProfileHistogramItem> advanceBookingDayList;
    //用户年龄段-订单量柱状图
    list<UserProfileHistogramItem> userAgeGroupList;
}
class UserProfileHistogramItem{
    string segmentName;
    double orderCount;
    long orderNum;
    double lastYearOrderCount;
}

class GetUserProfilePieChartRequestType{
    SightArchivesCommonFilter commonFilter;
}
class GetUserProfilePieChartResponseType{
    BaijiCommonTypes.ResponseStatusType responseStatus;
    list<UserProfilePieChart> pieChartList;
}
class UserProfilePieChart{
    //(返回英文)客户等级-userLevel/用户类型-userType/本异地用户-localAndForeign/境内外用户-domesticAndOversea
    string chartName;
    //饼图中各段
    list<UserProfilePieChartSegment> segmentList;
    //饼图中各段(去年)
    list<UserProfilePieChartSegment> lastYearSegmentList;
}
class UserProfilePieChartSegment{
    string name;
    double value;
    long count;
}

class GetUserResidenceDistributionRequestType{
    SightArchivesCommonFilter commonFilter;
}
class GetUserResidenceDistributionResponseType{
    BaijiCommonTypes.ResponseStatusType responseStatus;
    list<UserResidenceItem> cityList;
    list<UserResidenceItem> provinceList;
    list<UserResidenceItem> countryList;
}
class UserResidenceItem{
    long countryId;
    long provinceId;
    long cityId;
    string countryName;
    string provinceName;
    string cityName;
    //国家名称(多语言)
    string translateCountryName;
    //省份名称(多语言)
    string translateProvinceName;
    //城市名称(多语言)
    string translateCityName;
    double orderCountPercentage;
}

class GetUserSearchPreferenceRequestType{
    SightArchivesCommonFilter commonFilter;
}
class GetUserSearchPreferenceResponseType{
    BaijiCommonTypes.ResponseStatusType responseStatus;
    list<UserSearchPreferenceSearchKey> searchKeyList;
}
class UserSearchPreferenceSearchKey{
    //搜索关键词
    string searchKey;
    //次数
    int count;
}


class GetFulfillmentQualityMetricRequestType {
    SightArchivesCommonFilter commonFilter;
}
class GetFulfillmentQualityMetricResponseType {
    BaijiCommonTypes.ResponseStatusType responseStatus;
    //加权缺陷数
    double weightedDefectCount;
    //加权缺陷率
    double weightedDefectRate;
    //加权缺陷率环比
    double weightedDefectRatePop;
    //加权缺陷率同比
    double weightedDefectRateYoy;
    list<FulfillmentQualityPieChartSegment> pieChart;
}
class FulfillmentQualityPieChartSegment{
    //缺陷原因
    string name;
    //缺陷原因展示名称
    string displayName;
    //缺陷原因占比
    double value;
}

class GetFulfillmentQualityTableRequestType {
    SightArchivesCommonFilter commonFilter;
    //缺陷原因
    string defectName;
    int pageNo;
    int pageSize;
}
class GetFulfillmentQualityTableResponseType {
    BaijiCommonTypes.ResponseStatusType responseStatus;
    list<DefectiveProduct> defectiveProductList;
    int totalNum;
}
class DefectiveProduct{
    long productId;
    string productName;
    string vendorList;
    double weightedDefectCount;
    double weightedDefectRate;
}


class GetCommentMetricRequestType {
    SightArchivesCommonFilter commonFilter;

}
class GetCommentMetricResponseType {
    BaijiCommonTypes.ResponseStatusType responseStatus;
    //当前点评分
    CurrentCommentScore currentCommentScore;
    //点评趋势
    list<CommentTrendLineItem> CommentTrendLine;
    //好评关键词
    list<CommentKeyword> positiveCommentList;
    //差评关键词
    list<CommentKeyword> negativeCommentList;
}
class CurrentCommentScore{
    //点评分
    double commentScore;
    //点评分描述
    string commentScoreDesc;
    //点评分细项
    list<DetailCommentScore> detailCommentScore;
    //点评数
    int commentCount;
}
class DetailCommentScore{
    //细项名称
    string name;
    //细项分数
    double score;
}
class CommentTrendLineItem{
    //月份
    string month;
    //点评均分
    double commentScore;
    //点评数
    int commentCount;
}
class CommentKeyword{
    //关键词
    string keyword;
    //次数
    int count;
}

class GetServiceMetricRequestType {
    SightArchivesCommonFilter commonFilter;
}
class GetServiceMetricResponseType {
    BaijiCommonTypes.ResponseStatusType responseStatus;
    //cpo指标值
    double cpoValue;
    //cpo环比
    double cpoPop;
    //cpo同比
    double cpoYoy;
    //携程客服电话服务时长(分钟)
    int telServiceMinutes;
    //携程客服线上咨询服务次数
    int totalConsultCount;
    //携程客服线上咨询服务次数(售前)
    int preSaleConsultCount;
    //携程客服线上咨询服务次数(售后)
    int postSaleConsultCount;
    //折合服务价值(单位:人民币)
    int serviceValueAmount;
    //用户咨询高频问题top5
    list<ConsultQuestion> consultQuestionList;
}
class ConsultQuestion{
    string question;
    //咨询量
    int consultCount;
    //咨询量占比
    double consultCountPercentage;
}

class GetComplaintMetricRequestType {
    SightArchivesCommonFilter commonFilter;
}
class GetComplaintMetricResponseType {
    BaijiCommonTypes.ResponseStatusType responseStatus;
    //投诉量
    int complaintCount;
    //投诉率
    double complaintRate;
    //投诉率同比
    double complaintRateYoy;
    //投诉率环比
    double complaintRatePop;
    //投诉高频问题top5
    list<ComplaintQuestion> complaintQuestionList;
}
class ComplaintQuestion{
    string question;
    //投诉量
    int complaintCount;
    //投诉量占比
    double complaintCountPercentage;
}

class GetVendorQualityTableRequestType {
    SightArchivesCommonFilter commonFilter;
    //排序列
    string rankColumn;
    //升序"asc"、降序"desc"
    string rankType;
}
class GetVendorQualityTableResponseType {
    BaijiCommonTypes.ResponseStatusType responseStatus;
    list<VendorQuality> vendorList;
}
class VendorQuality{
    //id拼名称
    string vendorName;
    //支付订单数
    int paidOrderCount;
    //加权缺陷数
    double weightedDefectCount;
    //加权缺陷率
    double weightedDefectRate;
    //服务引发率(CPO)
    double cpo;
    //服务引发数(事件量)
    double eventCount;
    //客户进线量(电话)
    int customerTelIntakeCount;
    //客户进线量(IM)
    int customerIMIntakeCount;
    //投诉量
    int complaintCount;
    //投诉率
    double complaintRate;
    //折合服务价值(单位:人民币)
    int serviceValueAmount;
    //携程赔款金额(单位:人民币)
    int ctripPayoutAmount;
}

class GetSightComparisonRequestType {
    SightArchivesCommonFilter commonFilter;
    //1-同品类竞争圈、2-同地域竞争圈
    int competitorType;
}
class GetSightComparisonResponseType {
    BaijiCommonTypes.ResponseStatusType responseStatus;
    list<SightCompetitorRank> rankList;
}
class SightCompetitorRank{
    //(返回英文)搜索热度-searchHeat,用户关注度-userAttention,用户吸引力-userAttraction
    //,景区客流-scenicSpotFlow,客单价-averageOrderValue,游客评价-touristEvaluation
    //,履约质量-performanceQuality,退订率-refundRate,投诉率-complaintRate
    string rankName;
    list<SightCompetitorRankItem> rankItemList;
}
class SightCompetitorRankItem{
    long sightId;
    string sightName;
    list<SightCompetitorRankItemMetric> metricList;
}
class SightCompetitorRankItemMetric{
    //detailUv(详情页uv)、conversionRate(转化率)、soldTicketCount(购票量)、soldTicketCountGrowthRate(票量增长率)、averageOrderValue(客单价)、commentScore(点评分)、weightedDefectRate(加权缺陷率)、fullyRefundedOrderRate(退订率)、complaintRate(投诉率)
    string metricName;
    double metricValue;
}

class GetSightCompetitiveRequestType {
    SightArchivesCommonFilter commonFilter;
    //1-同品类竞争圈、2-同地域竞争圈
    int competitorType;
}
class GetSightCompetitiveResponseType {
    BaijiCommonTypes.ResponseStatusType responseStatus;
    //景点人群覆盖对比
    list<SightCompetitiveItem> coveredUserGroupList;
    //景点商品力对比
    list<SightCompetitiveItem> competiveItemList;
}
class SightCompetitiveItem{
    string name;
    //本景点表现 true-覆盖、false-缺失
    bool selfPerformance;
    //覆盖景点数
    int coveredSightCount;
    //竞争圈景点数
    int competitiveSightCount;
    //竞争圈覆盖率
    double coveredSightRate;
}


class GetUncoveredTicketTypeRequestType {
    SightArchivesCommonFilter commonFilter;
    //竞对: 1-美团、2-飞猪、3-客路
    int competitor;
    int pageNo;
    int pageSize;
}
class GetUncoveredTicketTypeResponseType {
    BaijiCommonTypes.ResponseStatusType responseStatus;
    list<UnCoveredTicketType> unCoveredTicketTypeList;
    int totalNum;
}
class UnCoveredTicketType{
    //未覆盖票种id
    long ticketTypeId;
    //未覆盖票种名称
    string ticketTypeName;
    //劣势天数
    int disadvantageDays;
    //劣势天数产量(仅国内有)
    int disadvantageDaysGmv;
}

class GetCoreTicketTypeCompetitiveRequestType {
    SightArchivesCommonFilter commonFilter;
    //竞对:1-美团 2-客路 3-飞猪
    int competitor;
    int pageNo;
    int pageSize;
}
class GetCoreTicketTypeCompetitiveResponseType {
    BaijiCommonTypes.ResponseStatusType responseStatus;
    list<CoreTicketTypeCompetitive> coreTicketTypeCompetitiveList;
    int totalNum;
}
class CoreTicketTypeCompetitive{
    //票种id
    long ticketTypeId;
    //票种名称
    string ticketTypeName;
    //对美团劣势天数
    int disadvantageDaysToMT;
    //对美团总天数
    int totalDaysToMT;
    //对飞猪劣势天数
    int disadvantageDaysToFZ;
    //对飞猪总天数
    int totalDaysToFZ;
    //对客路劣势天数
    int disadvantageDaysToKL;
    //对客路总天数
    int totalDaysToKL;
}

class GetCoreTicketTypeCompetitiveDetailRequestType {
    SightArchivesCommonFilter commonFilter;
    long ticketTypeId;
}
class GetCoreTicketTypeCompetitiveDetailResponseType {
    BaijiCommonTypes.ResponseStatusType responseStatus;
    list<CoreTicketTypeCompetitiveDetail> coreTicketTypeCompetitiveDetailList;
    //携程均价
    double xcPrice;
    //美团均价
    double mtPrice;
    //飞猪均价
    double fzPrice;
    //客路均价
    double klPrice;
}
class CoreTicketTypeCompetitiveDetail{
    //商品力对比项(提前预订时间、确认方式、预订生效时间=0、入园方式、退改规则、未来15天班期)
    string competitiveItemName;
    //对美团劣势天数
    int disadvantageDaysToMT;
    //对美团总天数
    int totalDaysToMT;
    //对飞猪劣势天数
    int disadvantageDaysToFZ;
    //对飞猪总天数
    int totalDaysToFZ;
    //对客路劣势天数
    int disadvantageDaysToKL;
    //对客路总天数
    int totalDaysToKL;
}



class GetLocationHeatForecastRequestType {
    SightArchivesCommonFilter commonFilter;
}
class GetLocationHeatForecastResponseType {
    BaijiCommonTypes.ResponseStatusType responseStatus;
    list<LocationHeatForecastTrendLineItem> heatForecastTrendLine;
}
class LocationHeatForecastTrendLineItem{
    //日期
    string date;
    //本城市热度
    double cityHeat;
    //本城市热度同比
    double cityHeatYoy;
    //机票热度
    double airplaneTicketHeat;
    //火车票热度
    double trainTicketHeat;
    //酒店热度
    double hotelTicketHeat;
}

class GetPopularSightsRequestType {
    SightArchivesCommonFilter commonFilter;
    //1-新开业景点、2-热门景点
    int rankType;
    //1-同城、2-同省、3-全国
    int rankScope;
}
class GetPopularSightsResponseType {
    BaijiCommonTypes.ResponseStatusType responseStatus;
    list<PopularSight> popularSightList;
}
class PopularSight{
    int sightId;
    //景点名称
    string sightName;
    //热度分
    double hotScore;
    //热度分变化描述
    string hotScoreChangeDesc;
}

class GetAiModuleSummaryRequestType{
    SightArchivesCommonFilter commonFilter;
    //1-业绩表现、2-流量转化、3-用户分析、4-服务质量、5-对竞分析、6-市场趋势
    int module;
    //首次调用时不用传
    string messageId;
    //首次调用时不用传
    string sessionId;
    //首次调用时不用传
    int index;
    //竞对类型:1-同品类竞争圈、2-同地域竞争圈(module=5时需传)
    int competitorType;
}

class GetAiModuleSummaryResponseType{
    BaijiCommonTypes.ResponseStatusType responseStatus;
    //算法侧提供的会话ID（当天一天的一个poiID 为同1个会话)
    string sessionId;
    //算法侧提供的消息ID
    string messageId;
    //ai接口响应状态:200-正常,500-异常
    int status;
    //文案生成状态:0-生成中,1-生成结束,2-生成异常
    int genStatus;
    //流式输出的索引生成结果列表
    list<ContentItem> tagContentItemList;
    //当时已经生成的流式数据，完整拼好的回答
    string completedMessage;
}
class ContentItem{
    //生成内容索引
    int index;
    //生成内容片段
    string content;
}


@serviceName='BIBusinessDashboardService'
@serviceNamespace='http://soa.ctrip.com/_24922'
service BIBusinessDashboardService {
    //景点档案tab可见权限
    CheckSightArchivesPermissionResponseType checkSightArchivesPermission(CheckSightArchivesPermissionRequestType request);
    //获取数据更新时间
    GetSightArchivesUpdateTimeResponseType getSightArchivesUpdateTime(GetSightArchivesUpdateTimeRequestType request);
    //景点概况 - 景点id/名称搜索
    SearchSightListResponseType searchSightList(SearchSightListRequestType request);
    //景点概况 - 景点信息
    GetSightInfoResponseType getSightInfo(GetSightInfoRequestType request);
    //景点概况 - 年入园量编辑
    UpdateAnnualIntakeResponseType updateAnnualIntake(UpdateAnnualIntakeRequestType request);
    //筛选项 - 供应商id/名称搜索
    SearchVendorListResponseType searchVendorList(SearchVendorListRequestType request);
    //报告总结 - 雷达图数据&文案内数据
    GetSightArchivesReportSummaryResponseType getSightArchivesReportSummary(GetSightArchivesReportSummaryRequestType request);

    //售卖情况 - 指标卡
    GetSalesMetricCardResponseType getSalesMetricCard(GetSalesMetricCardRequestType request);
    //售卖情况 - 趋势图
    GetSalesMetricTrendLineResponseType getSalesMetricTrendLine(GetSalesMetricTrendLineRequestType request);
    //售卖情况 - 饼图（毛利率指标时按排名顺序返回）
    GetSalesMetricPieChartResponseType getSalesMetricPieChart(GetSalesMetricPieChartRequestType request);
    //售卖情况 - 票种排行/供应商排行/分销商排行
    GetSalesMetricRankTableResponseType getSalesMetricRankTable(GetSalesMetricRankTableRequestType request);


    //合作项目产出情况
    GetCooperativeProjectOutputResponseType  getCooperativeProjectOutput(GetCooperativeProjectOutputRequestType request);
    //营销活动表现
    GetMarketingCampaignResponseType getMarketingCampaign(GetMarketingCampaignRequestType request);
    //广告投放情况 - 分布（地图）&排名（表格）
    GetAdvertisingPlacementResponseType getAdvertisingPlacement(GetAdvertisingPlacementRequestType request);


    //流量分析 - 流量来源分布（饼图）&流量转化漏斗
    GetFlowMetricResponseType getFlowMetric(GetFlowMetricRequestType request);
    //近一年淡旺季分析 - 转化率&UV趋势、淡旺季判断
    GetFlowMetricTrendLineResponseType getFlowMetricTrendLine(GetFlowMetricTrendLineRequestType request);


    //用户画像 - 提前预订天数&用户年龄分布（柱状图）
    GetUserProfileHistogramResponseType getUserProfileHistogram(GetUserProfileHistogramRequestType request);
    //用户画像 - 客户等级&用户类型&本异地%境内外用户分布（饼图）
    GetUserProfilePieChartResponseType getUserProfilePieChart(GetUserProfilePieChartRequestType request)
    //用户画像 - 客源城市分布（地图&表格）
    GetUserResidenceDistributionResponseType getUserResidenceDistribution(GetUserResidenceDistributionRequestType request)
    //用户搜索偏好
    GetUserSearchPreferenceResponseType getUserSearchPreference(GetUserSearchPreferenceRequestType request);

    //履约质量 - 指标卡&缺陷原因占比饼图
    GetFulfillmentQualityMetricResponseType getFulfillmentQualityMetric(GetFulfillmentQualityMetricRequestType request);
    //履约质量 - 按缺陷原因下钻表格
    GetFulfillmentQualityTableResponseType getFulfillmentQualityTable(GetFulfillmentQualityTableRequestType request);
    //点评 - 当前点评分&点评趋势&好评关键词&差评关键词
    GetCommentMetricResponseType getCommentMetric(GetCommentMetricRequestType request);
    //服务 - 指标卡&高频问题Top5
    GetServiceMetricResponseType getServiceMetric(GetServiceMetricRequestType request);
    //投诉 - 指标卡&高频问题Top5
    GetComplaintMetricResponseType getComplaintMetric(GetComplaintMetricRequestType request);
    //景区各供应商质量情况
    GetVendorQualityTableResponseType getVendorQualityTable(GetVendorQualityTableRequestType request);

    //景点对比
    GetSightComparisonResponseType getSightComparison(GetSightComparisonRequestType request);
    //产品力对比 - 景点VS竞争圈
    GetSightCompetitiveResponseType getSightCompetitive(GetSightCompetitiveRequestType request);
    //产品力对比 - 携程VS竞对 - 景点未覆盖票种清单
    GetUncoveredTicketTypeResponseType getUncoveredTicketType(GetUncoveredTicketTypeRequestType request);
    //产品力对比 - 携程VS竞对 - 核心票种商品力下钻表格外层
    GetCoreTicketTypeCompetitiveResponseType getCoreTicketTypeCompetitive(GetCoreTicketTypeCompetitiveRequestType request);
    //产品力对比 - 携程VS竞对 - 核心票种商品力下钻表格内层
    GetCoreTicketTypeCompetitiveDetailResponseType getCoreTicketTypeCompetitiveDetail(GetCoreTicketTypeCompetitiveDetailRequestType request);

    //所在地热度预测
    GetLocationHeatForecastResponseType getLocationHeatForecast(GetLocationHeatForecastRequestType request);
    //行业动态 - 新开业景点top5、热门景点top5
    GetPopularSightsResponseType getPopularSights(GetPopularSightsRequestType request);
    //ai模块小结
    GetAiModuleSummaryResponseType getAiModuleSummary(GetAiModuleSummaryRequestType request);
}