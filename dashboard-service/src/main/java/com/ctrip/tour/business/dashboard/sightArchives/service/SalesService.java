package com.ctrip.tour.business.dashboard.sightArchives.service;

import com.ctrip.soa._24922.*;

//业绩表现
public interface SalesService {

    //售卖情况 - 指标卡
    GetSalesMetricCardResponseType getSalesMetricCard(GetSalesMetricCardRequestType getSalesMetricCardRequestType, Boolean needFormatPercentage);

    //售卖情况 - 趋势图
    GetSalesMetricTrendLineResponseType getSalesMetricTrendLine(GetSalesMetricTrendLineRequestType getSalesMetricTrendLineRequestType);

    //售卖情况 - 饼图（毛利率指标时按排名顺序返回）
    GetSalesMetricPieChartResponseType getSalesMetricPieChart(GetSalesMetricPieChartRequestType getSalesMetricPieChartRequestType);

    //售卖情况 - 票种排行/供应商排行/分销商排行
    GetSalesMetricRankTableResponseType getSalesMetricRankTable(GetSalesMetricRankTableRequestType getSalesMetricRankTableRequestType);

    //合作项目产出情况
    GetCooperativeProjectOutputResponseType getCooperativeProjectOutput(GetCooperativeProjectOutputRequestType getCooperativeProjectOutputRequestType);

    //营销活动表现
    GetMarketingCampaignResponseType getMarketingCampaign(GetMarketingCampaignRequestType getMarketingCampaignRequestType);

    //广告投放情况 - 分布（地图）&排名（表格）
    GetAdvertisingPlacementResponseType getAdvertisingPlacement(GetAdvertisingPlacementRequestType getAdvertisingPlacementRequestType);

}
