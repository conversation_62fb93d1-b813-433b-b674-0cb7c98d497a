package com.ctrip.tour.business.dashboard.grpBusiness.job;//package com.ctrip.tour.business.dashboard.grpBusiness.job;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.tour.business.dashboard.grpBusiness.dao.mysql.OdsDataUploadNewNpsTargetLatestDao;
import com.ctrip.tour.business.dashboard.grpBusiness.dao.starrocks.StarRocksCommonDao;
import com.ctrip.tour.business.dashboard.grpBusiness.entity.OdsDataUploadNewNpsTargetLatest;
import com.ctrip.tour.business.dashboard.grpBusiness.service.CustEmpOrgInfoService;
import com.ctrip.tour.business.dashboard.grpBusiness.service.HrOrgEmpInfoService;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.group.workbenchsvc.contract.StructuredTableInfoType;
import com.ctrip.tour.group.workbenchsvc.contract.StructuredTableRowInfoType;
import com.ctrip.tour.rights.client.GroupWorkbenchServiceClient;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2025/3/25
 */
@Service
@Slf4j
public class DiyCompleteOrdRateAlertNotifyService extends CommonAlertNotifyService {

    private static final String TASK_DIY_PROFIT_ACH_RATE_ALERT = "TASK_ORDER_SUCCESS_RATE_ALERT";
    private static final String EVENT_DIY_PROFIT_ACH_RATE_ALERT = "EVENT_ORDER_SUCCESS_RATE_ALERT";
    private static final String EVENT_DIY_PROFIT_ACH_RATE_STRUCTURED_TABLE = "EVENT_ORDER_SUCCESS_RATE_ALERT_STRUCTURED_TABLE";
    @Autowired
    private StarRocksCommonDao starRocksCommonDao;
    @Autowired
    private OdsDataUploadNewNpsTargetLatestDao npsTargetLatestDao;

    private static String NOTIFY_EVENT_EMPCODES = "notify.event.empcodes";

    private GroupWorkbenchServiceClient client = GroupWorkbenchServiceClient.getInstance();

    @Resource
    private CustEmpOrgInfoService custEmpOrgInfoService;

    @Autowired
    private RemoteConfig remoteConfig;

    public void handleSelfSrvCov() throws Exception {

        String whiteEmpCodesStr = remoteConfig.getExternalConfig(NOTIFY_EVENT_EMPCODES);

        List<String> whiteEmpCodes = Lists.newArrayList();
        if (StringUtils.isNotBlank(whiteEmpCodesStr)) {
            whiteEmpCodes = Splitter.on(",").splitToList(whiteEmpCodesStr);
        }


        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        String sql = "SELECT \n" +
                "    grade_region_name ,\n" +
                "    SUM(CASE \n" +
                "            WHEN YEAR(order_date) = YEAR(CURRENT_DATE) \n" +
                "                 AND MONTH(order_date) = MONTH(CURRENT_DATE) \n" +
                "                 AND DAY(order_date) <= DAY(CURRENT_DATE) \n" +
                "            THEN cpr1_taken_deal_ord_cnt \n" +
                "            ELSE 0 \n" +
                "        END) /\n" +
                "    SUM(CASE \n" +
                "            WHEN YEAR(order_date) = YEAR(CURRENT_DATE)\n" +
                "                 AND MONTH(order_date) = MONTH(CURRENT_DATE) \n" +
                "                 AND DAY(order_date) <= DAY(CURRENT_DATE) \n" +
                "            THEN cus_taken_ord_cnt \n" +
                "            ELSE 0 \n" +
                "        END) AS current_ach_rate,\n" +
                "    SUM(CASE \n" +
                "            WHEN YEAR(order_date) = YEAR(CURRENT_DATE)-1 \n" +
                "                 AND MONTH(order_date) = MONTH(CURRENT_DATE) \n" +
                "                 AND DAY(order_date) <= DAY(CURRENT_DATE) \n" +
                "            THEN cpr1_taken_deal_ord_cnt \n" +
                "            ELSE 0 \n" +
                "        END) /\n" +
                "    SUM(CASE \n" +
                "            WHEN YEAR(order_date) = YEAR(CURRENT_DATE) - 1 \n" +
                "                 AND MONTH(order_date) = MONTH(CURRENT_DATE) \n" +
                "                 AND DAY(order_date) <= DAY(CURRENT_DATE) \n" +
                "            THEN cus_taken_ord_cnt \n" +
                "            ELSE 0 \n" +
                "        END) AS last_year_ach_rate\n" +
                "FROM adm_ord_cus_work_platform_prdt_prvdr_df where partition_d='"+LocalDate.now().format(dtf)+"'\n" +
                "GROUP BY grade_region_name;";

        List<Map<String, Object>> resultList = starRocksCommonDao.query(sql, Maps.newHashMap());
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }


        Map<String, List<Map<String, Object>>> resultGroupByRegion = resultList.stream()
                .collect(Collectors.groupingBy(map -> (String) map.get("grade_region_name")));

//        List<EdwHrEmpVacation> edwHrEmpVacations = hrOrgEmpInfoService.queryEmpByDomainNames(Lists.newArrayList(resultGroupByPmEid.keySet()));
//        if (CollectionUtils.isEmpty(edwHrEmpVacations)) {
//            log.warn("can not find emp info");
//            return;
//        }
//        Map<String, String> domai2EmpCodeMap = edwHrEmpVacations.stream().collect(Collectors.toMap(EdwHrEmpVacation::getDomainName, EdwHrEmpVacation::getEmpCode));
        Map<String, List<String>> empNoByAreaNames = custEmpOrgInfoService.getEmpNoByAreaNames(new ArrayList<>(resultGroupByRegion.keySet()), false);

        for (Map.Entry<String, List<String>> entry : empNoByAreaNames.entrySet()) {
            String k = entry.getKey();
            List<String> v = entry.getValue();
            try {

                List<StructuredTableRowInfoType> profitUnAchRows = Lists.newArrayList();
                for (String areaName : v) {
                    List<Map<String, Object>> realResult = resultGroupByRegion.get(areaName);
                    if (CollectionUtils.isEmpty(realResult)) {
                        continue;
                    }
                    Map<String, Object> realResultMap = realResult.get(0);
                    Double current_ach_rate = (Double) realResultMap.get("current_ach_rate");
                    Double last_year_ach_rate = (Double) realResultMap.get("last_year_ach_rate");

                    if (current_ach_rate < last_year_ach_rate) {
                        double yoy = (current_ach_rate - last_year_ach_rate) / last_year_ach_rate;
                        List<String> cols = Lists.newArrayList(getDataRatioStr(current_ach_rate) , getDataRatioStr(yoy) , "成单率低于去年同期");//NOSONAR
                        StructuredTableRowInfoType rowInfoType = new StructuredTableRowInfoType();
                        rowInfoType.setColList(cols);
                        profitUnAchRows.add(rowInfoType);
                    }

                }

                if (CollectionUtils.isNotEmpty(profitUnAchRows)) {
                    StructuredTableInfoType structuredTableInfoType = new StructuredTableInfoType();
                    structuredTableInfoType.setRowList(profitUnAchRows);
                    structuredTableInfoType.setHeaderList(Lists.newArrayList("达成率", "同比", "异常原因"));//NOSONAR
                    String content = "成单率低于去年同期，且未达成目标预警，请关注达成情况。";//NOSONAR
                    List<String> tpInfos = Lists.newArrayList("成单率低于去年同期，且未达成目标预警，请关注达成情况。");//NOSONAR
                    notifyEmp(k, structuredTableInfoType, "成单率告警通知",//NOSONAR
                            TASK_DIY_PROFIT_ACH_RATE_ALERT, EVENT_DIY_PROFIT_ACH_RATE_ALERT, EVENT_DIY_PROFIT_ACH_RATE_STRUCTURED_TABLE, content, tpInfos, "1");//NOSONAR
                }

            } catch (Exception e) {
                log.warn("handleSelfSrvCov error", e);
            }


        }

    }



}