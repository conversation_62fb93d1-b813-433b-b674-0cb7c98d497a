package com.ctrip.tour.business.dashboard.tktBusiness.strategy.subMetricImpl;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ExamineConfigBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.IncomeSubMetricCalStrategy;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.QuantitySubMetricCalStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.Future;

@Component
public class DestinationCMetricStrategy implements IncomeSubMetricCalStrategy, QuantitySubMetricCalStrategy {

    @Autowired
    private DestinationBaseStrategy destinationBaseStrategy;

    @Override
    public Future<MetricDetailInfo> getBus101102SubMetricCardData(TimeFilter timeFilter,
                                                                  OverseaMetricInfoBean metricInfoBean,
                                                                  String d,
                                                                  String metric,
                                                                  String subMetric) throws Exception {
        return destinationBaseStrategy.getBus101102SubMetricCardData(timeFilter,
                metricInfoBean, d, metric, subMetric);
    }

    @Override
    public GetOverseaTrendLineDataResponseType getBus101102SubTrendLineData(GetOverseaTrendLineDataRequestType request,
                                                                            String d,
                                                                            List<ExamineConfigBean> examineConfigBeanList) throws Exception {
        return destinationBaseStrategy.getBus101102SubTrendlineData(request, d, examineConfigBeanList);
    }

    @Override
    public GetOverseaDrillDownBaseInfoResponseType getBus101102SubDrillDownBaseInfo(GetOverseaDrillDownBaseInfoRequestType request,
                                                                                    String d,
                                                                                    OverseaMetricInfoBean metricInfoBean) throws Exception {
        return destinationBaseStrategy.getBus101102SubDrillDownBaseInfo(request, d, metricInfoBean);
    }

    @Override
    public GetOverseaTableDataResponseType getBus101102SubTableData(GetOverseaTableDataRequestType request,
                                                                    String d,
                                                                    OverseaMetricInfoBean metricInfoBean) throws Exception {
        return destinationBaseStrategy.getBus101102SubTableData(request, d, metricInfoBean);
    }


    @Override
    public Future<MetricDetailInfo> getBus103SubMetricCardData(TimeFilter timeFilter,
                                                               OverseaMetricInfoBean metricInfoBean,
                                                               String d,
                                                               String metric,
                                                               String subMetric) throws Exception {
        return destinationBaseStrategy.getBus103SubMetricCardData(timeFilter, metricInfoBean, d, metric, subMetric);
    }

    @Override
    public GetOverseaTrendLineDataResponseType getBus103SubTrendLineData(GetOverseaTrendLineDataRequestType request,
                                                                         String d,
                                                                         List<ExamineConfigBean> examineConfigBeanList) throws Exception {
        return destinationBaseStrategy.getBus103SubTrendLineData(request, d, examineConfigBeanList);
    }

    @Override
    public GetOverseaDrillDownBaseInfoResponseType getBus103SubDrillDownBaseInfo(GetOverseaDrillDownBaseInfoRequestType request,
                                                                                 String d,
                                                                                 OverseaMetricInfoBean metricInfoBean) throws Exception {
        return destinationBaseStrategy.getBus103SubDrillDownBaseInfo(request, d, metricInfoBean);
    }

    @Override
    public GetOverseaTableDataResponseType getBus103SubTableData(GetOverseaTableDataRequestType request,
                                                                 String d,
                                                                 OverseaMetricInfoBean metricInfoBean) throws Exception {
        return destinationBaseStrategy.getBus103SubTableData(request, d, metricInfoBean);
    }

    @Override
    public String getSubMetricName() {
        return "destination_c";
    }
}
