package com.ctrip.tour.business.dashboard.grpBusiness.job;

import static com.ctrip.tour.business.dashboard.grpBusiness.common.GrpConstant.SELF_SERVICE_COVERAGE_RATE_SETTING;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ctrip.tour.business.dashboard.grpBusiness.common.GrpConstant;
import com.ctrip.tour.business.dashboard.grpBusiness.dao.starrocks.StarRocksCommonDao;
import com.ctrip.tour.business.dashboard.grpBusiness.service.CustEmpOrgInfoService;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.group.workbenchsvc.contract.GetAlertThresholdSettingListInfoType;
import com.ctrip.tour.group.workbenchsvc.contract.GetAlertThresholdSettingListRequestType;
import com.ctrip.tour.group.workbenchsvc.contract.GetAlertThresholdSettingListResponseType;
import com.ctrip.tour.group.workbenchsvc.contract.StructuredTableInfoType;
import com.ctrip.tour.group.workbenchsvc.contract.StructuredTableRowInfoType;
import com.ctrip.tour.rights.client.GroupWorkbenchServiceClient;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2025/3/25
 */
@Service
@Slf4j
public class DiyProfitAlertNotifyService extends CommonAlertNotifyService {

    private static final String TASK_DIY_PROFIT_ACH_RATE_ALERT = "TASK_BENEFIT_ACHIEVEMENT_ALERT";
    private static final String EVENT_DIY_PROFIT_ACH_RATE_ALERT = "EVENT_BENEFIT_ACHIEVEMENT_ALERT";
    private static final String EVENT_DIY_PROFIT_ACH_RATE_STRUCTURED_TABLE = "EVENT_BENEFIT_ACHIEVEMENT_ALERT_STRUCTURED_TABLE";
    private static final String TASK_DIY_PROFIT_YOY_ALERT = "TASK_BENEFIT_ACHIEVEMENT_ALERT";
    private static final String EVENT_DIY_PROFIT_YOY_ALERT = "EVENT_BENEFIT_ACHIEVEMENT_ALERT";
    private static final String EVENT_DIY_PROFIT_YOY_STRUCTURED_TABLE = "EVENT_BENEFIT_ACHIEVEMENT_ALERT_STRUCTURED_TABLE";
    @Autowired
    private CustEmpOrgInfoService hrOrgEmpInfoService;
    @Autowired
    private StarRocksCommonDao starRocksCommonDao;

    private static String NOTIFY_EVENT_EMPCODES = "notify.event.empcodes";

    private GroupWorkbenchServiceClient client = GroupWorkbenchServiceClient.getInstance();

    @Autowired
    private RemoteConfig remoteConfig;
    public void handleSelfSrvCov() throws Exception {

        String whiteEmpCodesStr = remoteConfig.getExternalConfig(NOTIFY_EVENT_EMPCODES);





        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        String sql = "SELECT\n" +
                "    grade_region_name ,\n" +
                "    SUM(CASE                                  \n" +
                "        WHEN YEAR(order_date) = YEAR(CURRENT_DATE)                                       \n" +
                "        AND MONTH(order_date) = MONTH(CURRENT_DATE)                                       \n" +
                "        AND DAY(order_date) <= DAY(CURRENT_DATE)              THEN cus_suc_income                                  \n" +
                "        ELSE 0                      \n" +
                "    END) AS sumIncome,\n" +
                "    SUM(CASE                                  \n" +
                "        WHEN YEAR(order_date) = YEAR(CURRENT_DATE) - 1                                       \n" +
                "        AND MONTH(order_date) = MONTH(CURRENT_DATE)                                       \n" +
                "        AND DAY(order_date) <= DAY(CURRENT_DATE)              THEN cus_suc_income                                  \n" +
                "        ELSE 0                      \n" +
                "    END) AS lastYearSumIncome,\n" +
                "     SUM(CASE                                  \n" +
                "        WHEN YEAR(order_date) = YEAR(CURRENT_DATE)                                       \n" +
                "        AND MONTH(order_date) = MONTH(CURRENT_DATE)                                       \n" +
                "        AND DAY(order_date) <= DAY(CURRENT_DATE)              THEN cus_suc_profit                                  \n" +
                "        ELSE 0                      \n" +
                "    END) AS sumProfit,\n" +
                "    SUM(CASE                                  \n" +
                "        WHEN YEAR(order_date) = YEAR(CURRENT_DATE) - 1                                       \n" +
                "        AND MONTH(order_date) = MONTH(CURRENT_DATE)                                       \n" +
                "        AND DAY(order_date) <= DAY(CURRENT_DATE)              THEN cus_suc_profit                                  \n" +
                "        ELSE 0                      \n" +
                "    END) AS lastYearSumProfit     \n" +
                "FROM\n" +
                "    adm_ord_cus_work_platform_prdt_df     \n" +
                "where\n" +
                "    partition_d='"+dtf.format(LocalDate.now())+"'     \n" +
                "GROUP BY\n" +
                "    grade_region_name;";

        List<Map<String, Object>> resultList = starRocksCommonDao.query(sql, Maps.newHashMap());
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }

        String queryTgtSql = "SELECT \n" +
                "    grade_region_name ,\n" +
                "    SUM(CASE \n" +
                "            WHEN tou_date = substr(CURRENT_DATE,1,7) \n" +
                "            THEN depat_gmv_target \n" +
                "            ELSE 0 \n" +
                "        END) AS tgtGmv,\n" +
                "    SUM(CASE \n" +
                "            WHEN tou_date = substr(CURRENT_DATE,1,7) \n" +
                "            THEN depat_profit_target \n" +
                "            ELSE 0 \n" +
                "        END) AS tgtProfit,\n" +
                "SUM(CASE \n" +
                "            WHEN tou_date = DATE_SUB(CURRENT_DATE, INTERVAL 1 YEAR)\t\n" +
                "            THEN lst_dep_est_bsin_icm_deal_amt \n" +
                "            ELSE 0 \n" +
                "        END) AS lastYearIncomeTgt,\n" +
                "SUM(CASE \n" +
                "            WHEN tou_date = DATE_SUB(CURRENT_DATE, INTERVAL 1 YEAR)\t\n" +
                "            THEN lst_dep_est_deal_rev \n" +
                "            ELSE 0 \n" +
                "        END) AS lastYearProfitTgt\n" +
                "FROM adm_ord_cus_work_platform_prdt_target_achieve_df\n" +
                "GROUP BY grade_region_name;";
        List<Map<String, Object>> tagResultList = starRocksCommonDao.query(queryTgtSql, Maps.newHashMap());
        if (CollectionUtils.isEmpty(tagResultList)) {
            return;
        }
        Map<String, List<Map<String, Object>>> resultGroupByRegion = resultList.stream()
                .collect(Collectors.groupingBy(map -> (String) map.get("grade_region_name")));
        Map<String, List<Map<String, Object>>> tgtResultGroupByRegion = tagResultList.stream()
                .collect(Collectors.groupingBy(map -> (String) map.get("grade_region_name")));

//        List<EdwHrEmpVacation> edwHrEmpVacations = hrOrgEmpInfoService.queryEmpByDomainNames(Lists.newArrayList(resultGroupByPmEid.keySet()));
//        if (CollectionUtils.isEmpty(edwHrEmpVacations)) {
//            log.warn("can not find emp info");
//            return;
//        }
//        Map<String, String> domai2EmpCodeMap = edwHrEmpVacations.stream().collect(Collectors.toMap(EdwHrEmpVacation::getDomainName, EdwHrEmpVacation::getEmpCode));
        Map<String, List<String>> empNoByAreaNames = hrOrgEmpInfoService.getEmpNoByAreaNames(new ArrayList<>(resultGroupByRegion.keySet()), false);

        empNoByAreaNames
                .forEach((k, v) -> {
                    try {

                        List<StructuredTableRowInfoType> profitYoyRows = Lists.newArrayList();
                        List<StructuredTableRowInfoType> profitUnAchRows = Lists.newArrayList();
                        for (String areaName : v) {
                            List<Map<String, Object>> realResult = resultGroupByRegion.get(areaName);
                            List<Map<String, Object>> tgtResult = tgtResultGroupByRegion.get(areaName);
                            if (CollectionUtils.isEmpty(realResult) || CollectionUtils.isEmpty(tgtResult)) {
                                continue;
                            }
                            Map<String, Object> realResultMap = realResult.get(0);
                            Map<String, Object> tgtResultMap = tgtResult.get(0);
                            Double sumProfit = (Double) realResultMap.get("sumProfit");
                            Double tgtProfit = (Double) tgtResultMap.get("tgtProfit");
                            String gradeRegionName = (String)realResultMap.get("grade_region_name");
                            Double lastYearSumProfit = (Double) realResultMap.get("lastYearSumProfit");
                            Double lastYearProfitTgt = (Double) tgtResultMap.get("lastYearProfitTgt");

                            if (sumProfit < tgtProfit || sumProfit/tgtProfit < lastYearSumProfit/lastYearProfitTgt) {
                                Double sumIncome = (Double) realResultMap.get("sumIncome");
                                Double incomeTgt = (Double) tgtResultMap.get("tgtGmv");
                                List<String> cols = Lists.newArrayList(gradeRegionName, getPrettyDataStr(sumIncome), getPrettyDataStr(sumProfit), getDataRatioStr(sumIncome / incomeTgt), getDataRatioStr(sumProfit / tgtProfit));
                                StructuredTableRowInfoType rowInfoType = new StructuredTableRowInfoType();
                                rowInfoType.setColList(cols);
                                if (sumProfit < tgtProfit) {
                                    profitUnAchRows.add(rowInfoType);
                                } else {
                                    profitYoyRows.add(rowInfoType);
                                }

                            }

                        }

                        if (CollectionUtils.isNotEmpty(profitYoyRows)) {
                            StructuredTableInfoType structuredTableInfoType = new StructuredTableInfoType();
                            structuredTableInfoType.setRowList(profitYoyRows);
                            structuredTableInfoType.setHeaderList(Lists.newArrayList("评级区域","GMV", "毛利", "GMV达成率", "毛利达成率"));//NOSONAR
                            String content = "毛利指标未达成预警，请及时关注毛利达成情况。";//NOSONAR
                            List<String> tpInfos = Lists.newArrayList("毛利指标未达成预警，请及时关注毛利达成情况。");//NOSONAR
                            notifyEmp(k, structuredTableInfoType, "毛利未达成预警通知",//NOSONAR
                                    TASK_DIY_PROFIT_ACH_RATE_ALERT, EVENT_DIY_PROFIT_ACH_RATE_ALERT, EVENT_DIY_PROFIT_ACH_RATE_STRUCTURED_TABLE, content, tpInfos, "1");//NOSONAR
                        }
                        if (CollectionUtils.isNotEmpty(profitUnAchRows)) {
                            StructuredTableInfoType structuredTableInfoType = new StructuredTableInfoType();
                            structuredTableInfoType.setRowList(profitUnAchRows);
                            structuredTableInfoType.setHeaderList(Lists.newArrayList("评级区域","GMV", "毛利", "GMV达成率", "毛利达成率"));//NOSONAR
                            String content = "毛利成进度低于去年同期，请关注达成情况。";//NOSONAR
                            List<String> tpInfos = Lists.newArrayList("毛利成进度低于去年同期，请关注达成情况。");//NOSONAR
                            notifyEmp(k, structuredTableInfoType, "毛利达成低于去年同期完成率预警通知",//NOSONAR
                                    TASK_DIY_PROFIT_YOY_ALERT, EVENT_DIY_PROFIT_YOY_ALERT, EVENT_DIY_PROFIT_YOY_STRUCTURED_TABLE, content, tpInfos, "1");//NOSONAR
                        }

                    } catch (Exception e) {
                        log.warn("handleSelfSrvCov error", e);
                    }


                });

    }


}
