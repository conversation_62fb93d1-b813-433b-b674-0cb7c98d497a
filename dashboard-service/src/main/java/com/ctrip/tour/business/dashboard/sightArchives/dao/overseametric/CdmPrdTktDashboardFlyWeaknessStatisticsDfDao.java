package com.ctrip.tour.business.dashboard.sightArchives.dao.overseametric;

import com.ctrip.soa._27181.PreparedParameterBean;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.CdmPrdTktDashboardWeaknessStatisticsBO;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.bean.CdmPrdTktDashboardWeaknessParamBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseasCompetitionResponseBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseasRelatedSearchParamBean;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.TktStarRocksDao;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.OverseaMetricHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.Types;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Repository
@Slf4j
public class CdmPrdTktDashboardFlyWeaknessStatisticsDfDao {

    @Autowired
    private TktStarRocksDao tktStarRocksDao;

    public List<CdmPrdTktDashboardWeaknessStatisticsBO> queryCdmPrdTktDashboardWeaknessStatisticsBO(OverseasRelatedSearchParamBean param) {
        StringBuilder sql = new StringBuilder("select   " +
                "avg(cw_num) as cw_num, avg(c_num) as c_num, avg(cw_rate) as cw_rate,  " +
                "avg(fw_num) as fw_num, avg(f_num) as f_num, avg(fw_rate) as fw_rate,  " +
                "avg(tw_num) as tw_num, avg(t_num) as t_num, avg(tw_rate) as tw_rate, ");

        List<PreparedParameterBean> parameters = new java.util.ArrayList<>();
        String groupBy = null;
        switch (param.getDimValue()) {
            case "region":
                sql.append(" business_region_name");
                groupBy = "  group by business_region_name";
                break;
            case "province":
                sql.append(" business_region_name,business_sub_region_name ");
                groupBy = "  group by business_region_name,business_sub_region_name";
                break;
            case "country":
                sql.append(" business_region_name,business_sub_region_name,country_name ");
                groupBy = "  group by business_region_name,business_sub_region_name,country_name";
                break;
            case "examinee":
                sql.append(" business_region_name,business_sub_region_name,domain_name ");
                groupBy = "  group by business_region_name,business_sub_region_name,domain_name";
                break;
        }
        sql.append(" from cdm_prd_tkt_dashboard_fly_weakness_statistics_df " +
                "  where examine_year=?   and examine_type=?  and statistics_dim_id=? ");

        parameters.add(new PreparedParameterBean(param.getYear(), Types.VARCHAR));
        parameters.add(new PreparedParameterBean(param.getExamineType(), Types.INTEGER));
        parameters.add(new PreparedParameterBean(param.getStatisticsDimId(), Types.INTEGER));

        if(CollectionUtils.isNotEmpty(param.getQuarters())){
            sql.append(" and ( ");
            for(String quarter : param.getQuarters()){
                if (!isCurrentQuarter(quarter, param.getYear())) {//历史季度数据剔除top5 =1是top5数据
                    sql.append(" ( examine_quarter=?  ");
                    sql.append(" and is_c_coefficient_identifier = 0 and is_f_coefficient_identifier = 0 and is_t_coefficient_identifier = 0 )");
                }else {
                    sql.append(" ( examine_quarter=? ) ");
                }
                parameters.add(new PreparedParameterBean(quarter, Types.VARCHAR));
                sql.append(" or ");
            }
            int orLength = " or ".length();
            sql.setLength(sql.length() - orLength);
            sql.append(" ) ");
        }

        appendDomainName(parameters, sql, param.getDomainName(), param.getStatisticsDimId());
        appendDomainNameList(parameters,sql,param.getDomainNameList(), param.getStatisticsDimId());
        appendBusinessRegionName(parameters, sql, param.getBusinessRegionName());
        appendBusinessSubRegionName(parameters, sql, param.getBusinessSubRegionNames());
        appendCountryNames(parameters, sql, param.getCountryNames());
        appendD(parameters, sql, param.getD());
        sql.append(groupBy);
        List<Map<String, Object>> result = new ArrayList<>();
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            log.warn("queryCdmPrdTktDashboardWeaknessStatisticsBO error", e);
        }
        List<CdmPrdTktDashboardWeaknessStatisticsBO> tktList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(result)) {
            tktList = result.stream()
                    .map(bean -> {
                        CdmPrdTktDashboardWeaknessStatisticsBO tktBo = new CdmPrdTktDashboardWeaknessStatisticsBO();
                        tktBo.setBusinessRegionName((String) bean.get("business_region_name"));
                        tktBo.setBusinessSubRegionName((String) bean.get("business_sub_region_name"));
                        tktBo.setCountryName((String) bean.get("country_name"));
                        tktBo.setDomainName((String) bean.get("domain_name"));

                        tktBo.setCwNum((Double) bean.get("cw_num"));
                        tktBo.setCNum((Double) bean.get("c_num"));
                        tktBo.setCwRate((Double) bean.get("cw_rate"));

                        tktBo.setFwNum((Double) bean.get("fw_num"));
                        tktBo.setFwRate((Double) bean.get("fw_rate"));
                        tktBo.setFNum((Double) bean.get("f_num"));

                        tktBo.setTwNum((Double) bean.get("tw_num"));
                        tktBo.setTwRate((Double) bean.get("tw_rate"));
                        tktBo.setTNum((Double) bean.get("t_num"));

                        return tktBo;
                    })
                    .collect(Collectors.toList());
        }
        return tktList;
    }

    /**
     * 覆盖商品力飞猪英文下钻
     * @param param
     * @return
     */
    public List<CdmPrdTktDashboardWeaknessStatisticsBO> queryCdmPrdTktDashboardWeaknessStatisticsBOWithFlyEn(OverseasRelatedSearchParamBean param) {
        StringBuilder sql = new StringBuilder("select   " +
                "avg(cw_num) as cw_num, avg(c_num) as c_num, avg(cw_rate) as cw_rate,  " +
                "avg(fw_num) as fw_num, avg(f_num) as f_num, avg(fw_rate) as fw_rate,  " +
                "avg(tw_num) as tw_num, avg(t_num) as t_num, avg(tw_rate) as tw_rate, ");

        List<PreparedParameterBean> parameters = new java.util.ArrayList<>();
        String groupBy = null;
        switch (param.getDimValue()) {
            case "region":
                sql.append(" business_region_name_en");
                groupBy = "  group by business_region_name_en";
                break;
            case "province":
                sql.append(" business_region_name_en,business_sub_region_name_en");
                groupBy = "  group by business_region_name_en,business_sub_region_name_en";
                break;
        }
        sql.append(" from cdm_prd_tkt_dashboard_fly_weakness_statistics_df " +
                "  where examine_year=?   and examine_type=?  and statistics_dim_id=? ");

        parameters.add(new PreparedParameterBean(param.getYear(), Types.VARCHAR));
        parameters.add(new PreparedParameterBean(param.getExamineType(), Types.INTEGER));
        parameters.add(new PreparedParameterBean(param.getStatisticsDimId(), Types.INTEGER));

        if(CollectionUtils.isNotEmpty(param.getQuarters())){
            sql.append(" and ( ");
            for(String quarter : param.getQuarters()){
                if (!isCurrentQuarter(quarter, param.getYear())) {//历史季度数据剔除top5 =1是top5数据
                    sql.append(" ( examine_quarter=?  ");
                    sql.append(" and is_c_coefficient_identifier = 0 and is_f_coefficient_identifier = 0 and is_t_coefficient_identifier = 0 )");
                }else {
                    sql.append(" ( examine_quarter=? ) ");
                }
                parameters.add(new PreparedParameterBean(quarter, Types.VARCHAR));
                sql.append(" or ");
            }
            int orLength = " or ".length();
            sql.setLength(sql.length() - orLength);
            sql.append(" ) ");
        }

        appendDomainName(parameters, sql, param.getDomainName(), param.getStatisticsDimId());
        appendDomainNameList(parameters,sql,param.getDomainNameList(), param.getStatisticsDimId());
        appendInfoWithInput(parameters, sql, "business_region_name_en", param.getBusinessRegionName());
        appendInfoWithInput(parameters, sql, "business_sub_region_name_en", param.getBusinessSubRegionNames());
        appendD(parameters, sql, param.getD());
        sql.append(groupBy);
        List<Map<String, Object>> result = new ArrayList<>();
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            log.warn("queryCdmPrdTktDashboardWeaknessStatisticsBOWithFlyEn error", e);
        }
        List<CdmPrdTktDashboardWeaknessStatisticsBO> tktList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(result)) {
            tktList = result.stream()
                    .map(bean -> {
                        CdmPrdTktDashboardWeaknessStatisticsBO tktBo = new CdmPrdTktDashboardWeaknessStatisticsBO();
                        tktBo.setBusinessRegionName((String) bean.get("business_region_name_en"));
                        tktBo.setBusinessSubRegionName((String) bean.get("business_sub_region_name_en"));

                        tktBo.setCwNum((Double) bean.get("cw_num"));
                        tktBo.setCNum((Double) bean.get("c_num"));
                        tktBo.setCwRate((Double) bean.get("cw_rate"));

                        tktBo.setFwNum((Double) bean.get("fw_num"));
                        tktBo.setFwRate((Double) bean.get("fw_rate"));
                        tktBo.setFNum((Double) bean.get("f_num"));

                        tktBo.setTwNum((Double) bean.get("tw_num"));
                        tktBo.setTwRate((Double) bean.get("tw_rate"));
                        tktBo.setTNum((Double) bean.get("t_num"));

                        return tktBo;
                    })
                    .collect(Collectors.toList());
        }
        return tktList;
    }



    /**
     * 获取商品竞争力分析数据(飞猪)
     *
     * @return
     */
    public OverseasCompetitionResponseBean queryFlyWeaknessStatisticInfo(OverseasRelatedSearchParamBean searchParamBean) {
        StringBuilder sql = new StringBuilder("SELECT " +
                "avg(cw_num) as cw_num, avg(c_num) as c_num, avg(cw_rate) as cw_rate, " +
                "avg(fw_num) as fw_num, avg(f_num) as f_num, avg(fw_rate) as fw_rate, " +
                "avg(tw_num) as tw_num, avg(t_num) as t_num, avg(tw_rate) as tw_rate " +
                " FROM cdm_prd_tkt_dashboard_fly_weakness_statistics_df where 1 = 1 ");

        List<PreparedParameterBean> parameters = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(searchParamBean.getQuarters())){
            sql.append(" and ( ");
            for(String quarter : searchParamBean.getQuarters()){
                if (!isCurrentQuarter(quarter, searchParamBean.getYear())) {//历史季度数据剔除top5 =1是top5数据
                    sql.append(" ( examine_quarter=?  ");
                    sql.append(" and is_c_coefficient_identifier = 0 and is_f_coefficient_identifier = 0 and is_t_coefficient_identifier = 0 )");
                }else {
                    sql.append(" ( examine_quarter=? ) ");
                }
                parameters.add(new PreparedParameterBean(quarter, Types.VARCHAR));
                sql.append(" or ");
            }
            int orLength = " or ".length();
            sql.setLength(sql.length() - orLength);
            sql.append(" ) ");
        }
        appendYear(parameters, sql, searchParamBean.getYear());
        appendDomainName(parameters, sql, searchParamBean.getDomainName(),"1");
        appendStatisticDimId(parameters, sql, searchParamBean.getStatisticsDimId());
        appendExamineType(parameters, sql, searchParamBean.getExamineType());
        appendD(parameters, sql, searchParamBean.getD());

        List<Map<String, Object>> result = new ArrayList<>();
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            log.warn("queryFlyWeaknessStatisticInfo error", e);
        }
        OverseasCompetitionResponseBean overseasCompetitionResponseBean = new OverseasCompetitionResponseBean();
        if (CollectionUtils.isNotEmpty(result) && result.size() == 1) {
            Map<String, Object> data = result.get(0);
            overseasCompetitionResponseBean.setCwNum(OverseaMetricHelper.safeGetDouble(data, "cw_num"));
            overseasCompetitionResponseBean.setCNumber(OverseaMetricHelper.safeGetDouble(data, "c_num"));
            overseasCompetitionResponseBean.setCwRate(OverseaMetricHelper.safeGetDouble(data, "cw_rate"));

            overseasCompetitionResponseBean.setFwNum(OverseaMetricHelper.safeGetDouble(data, "fw_num"));
            overseasCompetitionResponseBean.setFNumber(OverseaMetricHelper.safeGetDouble(data, "f_num"));
            overseasCompetitionResponseBean.setFwRate(OverseaMetricHelper.safeGetDouble(data, "fw_rate"));

            overseasCompetitionResponseBean.setTwNum(OverseaMetricHelper.safeGetDouble(data, "tw_num"));
            overseasCompetitionResponseBean.setTNumber(OverseaMetricHelper.safeGetDouble(data, "t_num"));
            overseasCompetitionResponseBean.setTwRate(OverseaMetricHelper.safeGetDouble(data, "tw_rate"));
        }
        return overseasCompetitionResponseBean;
    }

    /**
     * 公共单个查询方法
     * @param parameters
     * @param sql
     * @param field
     * @param valueList
     */
    private void appendInfoWithInput(List<PreparedParameterBean> parameters, StringBuilder sql, String field, List<String> valueList) {
        if(field != null && CollectionUtils.isNotEmpty(valueList)){
            sql.append(" and ").append(field).append("  in (");
            for(int i = 0; i < valueList.size(); i++){
                if(i == 0){
                    sql.append("?");
                }else {
                    sql.append(",?");
                }
                parameters.add(new PreparedParameterBean(valueList.get(i), Types.VARCHAR));
            }
            sql.append(")");
        }
    }


    //拼人维度
    private void appendDomainName(List<PreparedParameterBean> parameters, StringBuilder sql, String domainName, String statisticId){
        if ("1".equals(statisticId) && domainName != null) {
            sql.append(" and domain_name = ?");
            parameters.add(new PreparedParameterBean(domainName, Types.VARCHAR));
        }
    }

    //拼业务大区
    private void appendDomainNameList(List<PreparedParameterBean> parameters, StringBuilder sql, List<String> domainList, String statisticId){
        if(CollectionUtils.isNotEmpty(domainList) && "2".equals(statisticId)){
            sql.append(" and domain_name in (");
            for(int i = 0; i < domainList.size(); i++){
                if(i == 0){
                    sql.append("?");
                }else {
                    sql.append(",?");
                }
                parameters.add(new PreparedParameterBean(domainList.get(i), Types.VARCHAR));
            }
            sql.append(")");
        }
    }

    //拼年份
    private void appendYear(List<PreparedParameterBean> parameters, StringBuilder sql, String year) {
        if (year != null) {
            sql.append(" and examine_year = ?");
            parameters.add(new PreparedParameterBean(year, Types.VARCHAR));
        }
    }

    //拼统计维度ID
    private void appendStatisticDimId(List<PreparedParameterBean> parameters, StringBuilder sql, String statisticsDimId) {
        if (statisticsDimId != null) {
            sql.append(" and statistics_dim_id = ?");
            parameters.add(new PreparedParameterBean(statisticsDimId, Types.INTEGER));
        }
    }

    //拼指标
    private void appendExamineType(List<PreparedParameterBean> parameters, StringBuilder sql, String examineType) {
        if (examineType != null) {
            sql.append(" and examine_type = ?");
            parameters.add(new PreparedParameterBean(examineType, Types.INTEGER));
        }
    }


    //拼业务大区
    private void appendBusinessRegionName(List<PreparedParameterBean> parameters, StringBuilder sql, List<String> businessRegionName) {
        if (CollectionUtils.isNotEmpty(businessRegionName)) {
            sql.append(" and business_region_name in (");
            for (int i = 0; i < businessRegionName.size(); i++) {
                if (i == 0) {
                    sql.append("?");
                } else {
                    sql.append(",?");
                }
                parameters.add(new PreparedParameterBean(businessRegionName.get(i), Types.VARCHAR));
            }
            sql.append(")");
        }
    }

    //拼业务子区域
    private void appendBusinessSubRegionName(List<PreparedParameterBean> parameters, StringBuilder sql, List<String> businessSubRegionNames) {
        if (CollectionUtils.isNotEmpty(businessSubRegionNames)) {
            sql.append(" and business_sub_region_name in (");
            for (int i = 0; i < businessSubRegionNames.size(); i++) {
                if (i == 0) {
                    sql.append("?");
                } else {
                    sql.append(",?");
                }
                parameters.add(new PreparedParameterBean(businessSubRegionNames.get(i), Types.VARCHAR));
            }
            sql.append(")");
        }
    }

    //拼国家
    private void appendCountryNames(List<PreparedParameterBean> parameters, StringBuilder sql, List<String> countryNames) {
        if (CollectionUtils.isNotEmpty(countryNames)) {
            sql.append(" and country_name in (");
            for (int i = 0; i < countryNames.size(); i++) {
                if (i == 0) {
                    sql.append("?");
                } else {
                    sql.append(",?");
                }
                parameters.add(new PreparedParameterBean(countryNames.get(i), Types.VARCHAR));
            }
            sql.append(")");
        }
    }

    // 拼时间
    public void appendD(List<PreparedParameterBean> parameters, StringBuilder sql, String d) {
        if (d != null) {
            sql.append(" and d=?");
            parameters.add(new PreparedParameterBean(d, Types.VARCHAR));
        }
    }


    public List<CdmPrdTktDashboardWeaknessStatisticsBO> queryCdmPrdTktDashboardWeaknessStatisticsBO(CdmPrdTktDashboardWeaknessParamBean param) {
        StringBuilder sql = new StringBuilder("select   " +
                "avg(cw_num) as cw_num, avg(c_num) as c_num, avg(cw_rate) as cw_rate,  " +
                "avg(fw_num) as fw_num, avg(f_num) as f_num, avg(fw_rate) as fw_rate,  " +
                "avg(tw_num) as tw_num, avg(t_num) as t_num, avg(tw_rate) as tw_rate, ");

        List<PreparedParameterBean> parameters = new java.util.ArrayList<>();
        String groupBy = null;
        switch (param.getDimName()) {
            case "region":
                sql.append(" business_region_name,business_region_name_en,business_region_id ");
                groupBy = "  group by business_region_name,business_region_name_en,business_region_id ";
                break;
            case "province":
                sql.append(" business_region_name,business_sub_region_name,business_region_name_en,business_sub_region_name_en," +
                        "business_region_id,business_sub_region_id ");
                groupBy = "  group by business_region_name,business_sub_region_name,business_region_name_en,business_sub_region_name_en," +
                        "business_region_id,business_sub_region_id ";
                break;
            case "country":
                sql.append(" business_region_name,business_sub_region_name,country_name,business_region_name_en,business_sub_region_name_en,country_name_en," +
                        "business_region_id,business_sub_region_id,country_id ");
                groupBy = "  group by business_region_name,business_sub_region_name,country_name,business_region_name_en,business_sub_region_name_en,country_name_en," +
                        "business_region_id,business_sub_region_id,country_id ";
                break;
            case "examinee":
                sql.append(" business_region_name,business_sub_region_name,domain_name,business_region_name_en,business_sub_region_name_en," +
                        "business_region_id,business_sub_region_id ");
                groupBy = "  group by business_region_name,business_sub_region_name,domain_name,business_region_name_en,business_sub_region_name_en," +
                        "business_region_id,business_sub_region_id ";
                break;
        }
        sql.append(" from cdm_prd_tkt_dashboard_fly_weakness_statistics_df " +
                "  where d=? and  examine_year=?   and examine_type=?  and statistics_dim_id=? ");

        parameters.add(new PreparedParameterBean(param.getD(), Types.VARCHAR));
        parameters.add(new PreparedParameterBean(param.getExamineYear(), Types.VARCHAR));
        parameters.add(new PreparedParameterBean(param.getExamineType().toString(), Types.INTEGER));
        parameters.add(new PreparedParameterBean(param.getStatisticsDimId().toString(), Types.INTEGER));

        if(CollectionUtils.isNotEmpty(param.getExamineQuarter())){
            sql.append(" and ( ");
            for(String quarter : param.getExamineQuarter()){
                if (!isCurrentQuarter(quarter, param.getExamineYear())) {//历史季度数据剔除top5 =1是top5数据
                    sql.append(" ( examine_quarter=?  ");
                    sql.append(" and is_c_coefficient_identifier = 0 and is_f_coefficient_identifier = 0 and is_t_coefficient_identifier = 0 )");
                }else {
                    sql.append(" ( examine_quarter=? ) ");
                }
                parameters.add(new PreparedParameterBean(quarter, Types.VARCHAR));
                sql.append(" or ");
            }
            int orLength = " or ".length();
            sql.setLength(sql.length() - orLength);
            sql.append(" ) ");
        }
        if (CollectionUtils.isNotEmpty(param.getBusinessRegionName())) {
            appendSql(sql," and business_region_name in ( ",parameters,param.getBusinessRegionName());
        }
        if (CollectionUtils.isNotEmpty(param.getBusinessSubRegionName())) {
            appendSql(sql," and business_sub_region_name in ( ",parameters,param.getBusinessSubRegionName());
        }
        if (CollectionUtils.isNotEmpty(param.getCountryName())) {
            appendSql(sql," and country_name in ( ",parameters,param.getCountryName());
        }
        if (CollectionUtils.isNotEmpty(param.getBusinessRegionNameEn())) {
            appendSql(sql," and business_region_name_en in ( ",parameters,param.getBusinessRegionNameEn());
        }
        if (CollectionUtils.isNotEmpty(param.getBusinessSubRegionNameEn())) {
            appendSql(sql," and business_sub_region_name_en in ( ",parameters,param.getBusinessSubRegionNameEn());
        }
        if (CollectionUtils.isNotEmpty(param.getCountryNameEn())) {
            appendSql(sql," and country_name_en in ( ",parameters,param.getCountryNameEn());
        }
        if (CollectionUtils.isNotEmpty(param.getDomainNames())) {
            appendSql(sql," and domain_name in ( ",parameters,param.getDomainNames());
        }

        sql.append(groupBy);
        if (param.getPageIndex() != null && param.getPageSize() != null) {
            sql.append(" limit ?,? ");
            parameters.add(new PreparedParameterBean(String.valueOf((param.getPageIndex() - 1) * param.getPageSize()), Types.INTEGER));
            parameters.add(new PreparedParameterBean(String.valueOf(param.getPageSize()), Types.INTEGER));
        }
        List<Map<String, Object>> result = new ArrayList<>();
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            log.warn("queryOverseaPersonInfo error", e);
        }
        List<CdmPrdTktDashboardWeaknessStatisticsBO> tktList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(result)) {
            tktList = result.stream()
                    .map(bean -> {
                        CdmPrdTktDashboardWeaknessStatisticsBO tktBo = new CdmPrdTktDashboardWeaknessStatisticsBO();
                        tktBo.setBusinessRegionName((String) bean.get("business_region_name"));
                        tktBo.setBusinessSubRegionName((String) bean.get("business_sub_region_name"));
                        tktBo.setCountryName((String) bean.get("country_name"));
                        tktBo.setBusinessRegionNameEn((String) bean.get("business_region_name_en"));
                        tktBo.setBusinessSubRegionNameEn((String) bean.get("business_sub_region_name_en"));
                        tktBo.setCountryNameEn((String) bean.get("country_name_en"));
                        tktBo.setBusinessRegionId((Long) bean.get("business_region_id"));
                        tktBo.setBusinessSubRegionId((Long) bean.get("business_sub_region_id"));
                        tktBo.setCountryId((Long) bean.get("country_id"));
                        tktBo.setDomainName((String) bean.get("domain_name"));

                        tktBo.setCwNum((Double) bean.get("cw_num"));
                        tktBo.setCNum((Double) bean.get("c_num"));
                        tktBo.setCwRate((Double) bean.get("cw_rate"));

                        tktBo.setFwNum((Double) bean.get("fw_num"));
                        tktBo.setFwRate((Double) bean.get("fw_rate"));
                        tktBo.setFNum((Double) bean.get("f_num"));

                        tktBo.setTwNum((Double) bean.get("tw_num"));
                        tktBo.setTwRate((Double) bean.get("tw_rate"));
                        tktBo.setTNum((Double) bean.get("t_num"));

                        return tktBo;
                    })
                    .collect(Collectors.toList());
        }
        return tktList;
    }

    private boolean isCurrentQuarter(String quarter, String year) {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();

        // 获取当前年份和季度
        int currentYear = currentDate.getYear();
        int currentMonth = currentDate.getMonthValue();
        int currentQuarter = (currentMonth - 1) / 3 + 1;

        // 将当前年份转换为字符串以便比较
        String currentYearStr = String.valueOf(currentYear);

        // 检查年份是否匹配
        if (!year.equals(currentYearStr)) {
            return false;
        }

        String currentQuarterStr = "Q" + currentQuarter;
        return quarter.equalsIgnoreCase(currentQuarterStr);
    }


    private void appendSql(StringBuilder sql, String sqlAppend, List<PreparedParameterBean> parameters, List<String> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            sql.append(sqlAppend);
            for (int i = 0; i < list.size(); i++) {
                if (i == 0) {
                    sql.append("?");
                } else {
                    sql.append(",?");
                }
                parameters.add(new PreparedParameterBean(list.get(i), Types.VARCHAR));
            }
            sql.append(")");
        }
    }

    public Integer queryCdmPrdTktDashboardWeaknessStatisticsBOCount(CdmPrdTktDashboardWeaknessParamBean param) {
        StringBuilder sql = new StringBuilder();

        List<PreparedParameterBean> parameters = new java.util.ArrayList<>();
        switch (param.getDimName()) {
            case "region":
                sql.append("select  count(distinct business_region_name) as totalNum ");
                break;
            case "province":
                sql.append("select  count(distinct business_region_name,business_sub_region_name) as totalNum ");
                break;
            case "country":
                sql.append("select  count(distinct business_region_name,business_sub_region_name,country_name) as totalNum ");
                break;
            case "examinee":
                sql.append("select  count(distinct business_region_name,business_sub_region_name,domain_name) as totalNum ");
                break;
        }
        sql.append("from cdm_prd_tkt_dashboard_fly_weakness_statistics_df " +
                "  where d=? and  examine_year=?   and examine_type=?  and statistics_dim_id=? ");

        parameters.add(new PreparedParameterBean(param.getD(), Types.VARCHAR));
        parameters.add(new PreparedParameterBean(param.getExamineYear(), Types.VARCHAR));
        parameters.add(new PreparedParameterBean(param.getExamineType().toString(), Types.INTEGER));
        parameters.add(new PreparedParameterBean(param.getStatisticsDimId().toString(), Types.INTEGER));
        if(CollectionUtils.isNotEmpty(param.getExamineQuarter())){
            sql.append(" and ( ");
            for(String quarter : param.getExamineQuarter()){
                if (!isCurrentQuarter(quarter, param.getExamineYear())) {//历史季度数据剔除top5 =1是top5数据
                    sql.append(" ( examine_quarter=?  ");
                    sql.append(" and is_c_coefficient_identifier = 0 and is_f_coefficient_identifier = 0 and is_t_coefficient_identifier = 0 )");
                }else {
                    sql.append(" ( examine_quarter=? ) ");
                }
                parameters.add(new PreparedParameterBean(quarter, Types.VARCHAR));
                sql.append(" or ");
            }
            int orLength = " or ".length();
            sql.setLength(sql.length() - orLength);
            sql.append(" ) ");
        }
        if (CollectionUtils.isNotEmpty(param.getBusinessRegionName())) {
            appendSql(sql," and business_region_name in ( ",parameters,param.getBusinessRegionName());
        }
        if (CollectionUtils.isNotEmpty(param.getBusinessSubRegionName())) {
            appendSql(sql," and business_sub_region_name in ( ",parameters,param.getBusinessSubRegionName());
        }
        if (CollectionUtils.isNotEmpty(param.getCountryName())) {
            appendSql(sql," and country_name in ( ",parameters,param.getCountryName());
        }
        if (CollectionUtils.isNotEmpty(param.getBusinessRegionNameEn())) {
            appendSql(sql," and business_region_name_en in ( ",parameters,param.getBusinessRegionNameEn());
        }
        if (CollectionUtils.isNotEmpty(param.getBusinessSubRegionNameEn())) {
            appendSql(sql," and business_sub_region_name_en in ( ",parameters,param.getBusinessSubRegionNameEn());
        }
        if (CollectionUtils.isNotEmpty(param.getCountryNameEn())) {
            appendSql(sql," and country_name_en in ( ",parameters,param.getCountryNameEn());
        }
        if (CollectionUtils.isNotEmpty(param.getDomainNames())) {
            appendSql(sql," and domain_name in ( ",parameters,param.getDomainNames());
        }
        List<Map<String, Object>> result = new ArrayList<>();
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            log.warn("queryOverseaPersonInfo error", e);
        }

        return CollectionUtils.isNotEmpty(result) ? Math.toIntExact((Long) result.get(0).getOrDefault("totalNum", "0")) : 0;
    }
}
