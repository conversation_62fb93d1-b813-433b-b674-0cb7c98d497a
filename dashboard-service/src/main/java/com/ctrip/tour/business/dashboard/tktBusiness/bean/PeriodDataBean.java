package com.ctrip.tour.business.dashboard.tktBusiness.bean;

import com.ctrip.tour.business.dashboard.utils.GeneralUtil;


import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Future;


public class PeriodDataBean {

    //基础的数据单元
    //达成数据
    List<List<Object>> reachList;
    //目标数据
    List<List<Object>> targetList;
    //去年同比数据
    List<List<Object>> lastyearList;
    //2019年同比数据
    List<List<Object>> _2019List;
    //劣势数据
    List<List<Object>> weaknessList;

    //数据表头
    List<String> reachHeaderList;
    List<String> targetHeaderList;
    List<String> lastyearHeaderList;
    List<String> _2019HeaderList;
    List<String> weaknessHeaderList;

    //下钻维度
    List<String> groupList;

    public void setBeanData(List<Future<SinglePeriodDataBean>> futureList) throws Exception {

        reachList = new ArrayList<>();
        targetList = new ArrayList<>();
        lastyearList = new ArrayList<>();
        _2019List = new ArrayList<>();
        weaknessList = new ArrayList<>();
        reachHeaderList = new ArrayList<>();
        targetHeaderList = new ArrayList<>();
        lastyearHeaderList = new ArrayList<>();
        _2019HeaderList = new ArrayList<>();
        weaknessHeaderList = new ArrayList<>();


        for (Future<SinglePeriodDataBean> futureResult : futureList) {
            SinglePeriodDataBean bean = futureResult.get();
            List<List<Object>> periodReachList = bean.getPeriodReachList();
            List<List<Object>> periodTargetList = bean.getPeriodTargetList();
            List<List<Object>> periodLastyearList = bean.getPeriodLastyearList();
            List<List<Object>> period2019List = bean.getPeriod2019List();
            List<List<Object>> periodWeaknessList = bean.getPeriodWeaknessList();
            if (!GeneralUtil.isEmpty(periodReachList)) {
                reachList.addAll(periodReachList);
            }
            if (!GeneralUtil.isEmpty(periodTargetList)) {
                targetList.addAll(periodTargetList);
            }
            if (!GeneralUtil.isEmpty(periodLastyearList)) {
                lastyearList.addAll(periodLastyearList);
            }
            if (!GeneralUtil.isEmpty(period2019List)) {
                _2019List.addAll(period2019List);
            }
            if(GeneralUtil.isNotEmpty(periodWeaknessList)){
                weaknessList.addAll(periodWeaknessList);
            }

        }

        //表头数据取最后一个元素数据即可
        SinglePeriodDataBean singlePeriodDataBean = futureList.get(futureList.size() - 1).get();

        List<String> periodReachHeaderList = singlePeriodDataBean.getReachHeaderList();
        List<String> periodTargetHeaderList = singlePeriodDataBean.getTargetHeaderList();
        List<String> periodLastyearHeaderList = singlePeriodDataBean.getLastyearHeaderList();
        List<String> period2019HeaderList = singlePeriodDataBean.get2019HeaderList();
        List<String> periodWeaknessHeaderList = singlePeriodDataBean.getWeaknessHeaderList();

        if(GeneralUtil.isNotEmpty(periodReachHeaderList)){
            reachHeaderList.addAll(periodReachHeaderList);
        }
        if(GeneralUtil.isNotEmpty(periodTargetHeaderList)){
            targetHeaderList.addAll(periodTargetHeaderList);
        }
        if(GeneralUtil.isNotEmpty(periodLastyearHeaderList)){
            lastyearHeaderList.addAll(periodLastyearHeaderList);
        }
        if(GeneralUtil.isNotEmpty(period2019HeaderList)){
            _2019HeaderList.addAll(period2019HeaderList);
        }
        if(GeneralUtil.isNotEmpty(periodWeaknessHeaderList)){
            weaknessHeaderList.addAll(periodWeaknessHeaderList);
        }
    }

    public List<List<Object>> getReachList() {
        return reachList;
    }

    public List<List<Object>> getTargetList() {
        return targetList;
    }

    public List<List<Object>> getLastyearList() {
        return lastyearList;
    }

    public List<List<Object>> get2019List() {
        return _2019List;
    }

    public List<List<Object>> getWeaknessList() {
        return weaknessList;
    }

    public List<String> getReachHeaderList() {
        return reachHeaderList;
    }

    public List<String> getTargetHeaderList() {
        return targetHeaderList;
    }

    public List<String> getLastyearHeaderList() {
        return lastyearHeaderList;
    }

    public List<String> get2019HeaderList() {
        return _2019HeaderList;
    }

    public List<String> getWeaknessHeaderList() {
        return weaknessHeaderList;
    }
}
