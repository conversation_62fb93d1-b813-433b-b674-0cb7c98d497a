package com.ctrip.tour.business.dashboard.tktBusiness.bean;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;


@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class PushMessageBean {
    @SerializedName(value = "orderid")
    String orderId;
    @SerializedName(value = "sender")
    String sender;
    @SerializedName(value = "providerid")
    String providerId;
    @SerializedName(value = "processoreidlist")
    String processoreIdList;
    @SerializedName(value = "content")
    String content;
    @SerializedName(value = "jumpurl")
    String jumpUrl;
    @SerializedName(value = "completejumpurl")
    String completeJumpUrl;
    @SerializedName(value = "listfiles")
    List<PushMessageFileBean> listFiles;
    @SerializedName(value = "productid")
    String productId;
    @SerializedName(value = "h5jumpurl")
    String h5JumpUrl;
    @SerializedName(value = "h5completejumpurl")
    String h5CompleteJumpUrl;
    @SerializedName(value = "extend")
    String extend;
    @SerializedName(value = "trippalinfo")
    PushMessageServiceNumberTrippalInfoBean trippalInfo;
}


