package com.ctrip.tour.business.dashboard.tktBusiness.general;


import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.utils.UserUtil;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/8/18
 */
@Component
public class LogInfo{

    @Autowired
    RemoteConfig remoteConfig;

    public Map<String, String> createClickhouseTagMap(String request,
                                                      String response,
                                                      String responseType,
                                                      Long quertTime) {
        Map<String, String> tagMap = new HashMap<>();
        String empCode = MDC.get("empCode");
        String mappingEmpCode = UserUtil.getMappingEmpCode(remoteConfig);
        String operationName = MDC.get("operationName");
        String metric = MDC.get("metric");
        String appname = MDC.get("appname");
        //待后续逻辑处理
        String isCore = "unkwn";
        tagMap.put("emp_code", empCode);
        tagMap.put("mapping_emp_code", mappingEmpCode);
        tagMap.put("is_core", isCore);
        tagMap.put("request", request);
        tagMap.put("response", response);
        tagMap.put("operation_name", operationName);
        tagMap.put("query_time", String.valueOf(quertTime));
        tagMap.put("response_type", responseType);
        tagMap.put("metric", metric);
        tagMap.put("appname", appname);
        return tagMap;
    }

    public Map<String, String> createHickWallTagMap(String responseType) {
        Map<String, String> tagMap = new HashMap<>();
        String empCode = MDC.get("empCode");
        String mappingEmpCode = UserUtil.getMappingEmpCode(remoteConfig);
        String operationName = MDC.get("operationName");
        String metric = MDC.get("metric");

        if (!"normal".equals(responseType)) {
            tagMap.put("response_type", responseType);
        }
        tagMap.put("emp_code", empCode);
        tagMap.put("mapping_emp_code", mappingEmpCode);
        tagMap.put("operation_name", operationName);
        tagMap.put("metric", metric);

        return tagMap;
    }
}
