package com.ctrip.tour.business.dashboard.tktBusiness.bean;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * 合同事件工作台 发送消息内容的类，支持服务号和详情页
 */


@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ContractEventContentSceneBean {
    int contentType; // 内容类型，穷举值：1：文本；2：文本包含表格
    int length;   // 内容限制长度
    String templateStr; // 模板字符串，可能包含占位符；如果contentType是表格，这里是表格之上的字符串
    boolean isplace; // 模板字符串是否包含占位符
    List<String> templateList; // 模板字符串如果包含占位符，这里是取值的内容
    Map<String, List<String>> headers; // 表头，如果contentType是表格，就有数据
    List<ContractHyperLinkBean> hyperLinks;   // 超链接map，根据表头判断是否需要对内容进行超链接设置

}
