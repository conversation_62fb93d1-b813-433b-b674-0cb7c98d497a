package com.ctrip.tour.business.dashboard.tktBusiness.helper;


import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ExamineConfigBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBeanV2Child;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.SubMetricFiledBean;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.enums.BuTypeEnum;
import com.ctrip.tour.business.dashboard.tktBusiness.enums.ChannelSiteTypeEnum;
import com.ctrip.tour.business.dashboard.tktBusiness.enums.DestinationLevelTypeEnum;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.ConfigImportException;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.InputArgumentException;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.ctrip.tour.business.dashboard.utils.UserUtil;
import org.apache.commons.lang.StringUtils;

import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

public class OverseaMetricHelper {

    /**
     * 生成季度指标的baseMap
     * 如果传进来的时间是月 则需要把月做映射多个月
     * 如果传进来的时间是季度/半年 则不需要做映射
     *
     * @param metric
     * @param timeFilter
     * @param type
     * @param d
     * @param examineConfigBean
     * @return
     * @throws Exception
     */
    public static Map<String, String> generateQuarterMetricBaseMap(String metric,
                                                                   TimeFilter timeFilter,
                                                                   String type,
                                                                   String d,
                                                                   ExamineConfigBean examineConfigBean) throws Exception {
        if (GeneralUtil.isNotEmpty(examineConfigBean)) {
            return generateQuarterMetricBaseMapWithExamineConfigBean(metric, examineConfigBean, type, d);
        }
        return generateQuaterMetricBaseMapWithTimeFilter(metric, timeFilter, type, d);
    }


    /**
     * 用timeFilter生成季度指标的baseMap
     *
     * @param metric
     * @param timeFilter
     * @param type
     * @param d
     * @return
     * @throws Exception
     */
    public static Map<String, String> generateQuaterMetricBaseMapWithTimeFilter(String metric,
                                                                                TimeFilter timeFilter,
                                                                                String type,
                                                                                String d) throws Exception {
        Map<String, String> baseMap = new HashMap<>();
        String year = timeFilter.getYear();
        String dateType = timeFilter.getDateType();
        String time = DateUtil.getQuarterMetricInputTime(dateType, timeFilter.getMonth(), timeFilter.getQuarter(), timeFilter.getHalf());
        //非目标数据均需要分区
        if (!"target".equals(type)) {
            baseMap.put("query_d", d);
        }
        //如果是当期数据
        if ("current".equals(type)) {
            baseMap.put("year", year);
            baseMap.put(dateType, time);
        }
        //如果是目标数据 且为质量成本指标
        if ("target".equals(type) && "103".equals(metric)) {
            baseMap.put("year", year);
            baseMap.put("date_type", DateUtil.getQuarterMetricTargetTime(dateType, timeFilter.getMonth(), timeFilter.getQuarter(), timeFilter.getHalf()));
        }
        //如果是同比去年
        if ("lastyear".equals(type)) {
            String lastyear = DateUtil.getLastYear(year);
            baseMap.put("year", lastyear);
            baseMap.put(dateType, time);
        }
        return baseMap;
    }


    private static Map<String, String> generateQuarterMetricBaseMapWithExamineConfigBean(String metric,
                                                                                         ExamineConfigBean examineConfigBean,
                                                                                         String type,
                                                                                         String d) throws Exception {
        Map<String, String> baseMap = new HashMap<>();
        String year = examineConfigBean.getYear();
        String dateType = examineConfigBean.getDateType();
        String currentTime = DateUtil.getQuarterMetricInputTime(dateType, examineConfigBean.getMonth(), examineConfigBean.getQuarter(), null);
        //非目标数据均需要分区
        if (!"target".equals(type)) {
            baseMap.put("query_d", d);
        }
        //如果是当期数据
        if ("current".equals(type)) {
            baseMap.put("year", year);
            baseMap.put(dateType, currentTime);
        }
        //如果是目标数据 且为质量成本指标
        if ("target".equals(type) && "103".equals(metric)) {
            baseMap.put("year", year);
            baseMap.put("date_type", DateUtil.getQuarterMetricTargetTime(dateType, examineConfigBean.getMonth(), examineConfigBean.getQuarter(), null));
        }

        return baseMap;

    }




    /**
     * 生成月度指标的baseMap
     * @param timeFilter
     * @param type
     * @param d
     * @param examineConfigBean
     * @return
     * @throws Exception
     */
    public static Map<String, String> generateBaseMap(TimeFilter timeFilter,
                                                      String type,
                                                      String d,
                                                      ExamineConfigBean examineConfigBean) throws Exception {
        if (GeneralUtil.isNotEmpty(examineConfigBean)) {
            return generateBaseMapWithExamineConfigBean(examineConfigBean, type, d);
        }
        return generateBaseMapWithTimeFilter(timeFilter, type, d);
    }


    /**
     * 用examineConfigBean生成基础的map
     * @param examineConfigBean
     * @param type
     * @param d
     * @return
     */
    private static Map<String, String> generateBaseMapWithExamineConfigBean(ExamineConfigBean examineConfigBean,
                                                                            String type,
                                                                            String d) throws Exception {
        Map<String, String> baseMap = new HashMap<>();
        String lastDay = DateUtil.getDayOfInterval(d, -1);
        String year = examineConfigBean.getYear();
        String dateType = examineConfigBean.getDateType();
        String currentTime = DateUtil.getInputTime(dateType, examineConfigBean.getMonth(), examineConfigBean.getQuarter(), null);
        Boolean isLastestPeriod = examineConfigBean.getIsLastestPeriod();
        //非目标数据均需要分区
        if (!"target".equals(type)) {
            baseMap.put("query_d", d);
        }
        //如果是求当期数据或者目标数据
        if ("current".equals(type) || "target".equals(type)) {
            baseMap.put("year", year);
            baseMap.put(dateType, currentTime);
        }
        //如果求的是同比 去年或者同比2019
        if ("lastyear".equals(type)) {
            String popTime = examineConfigBean.getTimeMap().get("lastyearTime");
            String lastyear = popTime.split("-")[0];
            String monthOrQuarter = popTime.split("-")[1];
            baseMap.put("year", lastyear);
            baseMap.put(dateType, monthOrQuarter);
            if (isLastestPeriod) {
                baseMap.put("the_date", StringUtils.join(DateUtil.getPopTimeRange(dateType, lastDay, monthOrQuarter, monthOrQuarter, lastyear, false), "|"));
            }
        }
        if ("2019".equals(type)) {
            String popTime = examineConfigBean.getTimeMap().get("2019Time");
            String lastyear = popTime.split("-")[0];
            String monthOrQuarter = popTime.split("-")[1];
            baseMap.put("year", lastyear);
            baseMap.put(dateType, monthOrQuarter);
            if (isLastestPeriod) {
                baseMap.put("the_date", StringUtils.join(DateUtil.getPopTimeRange(dateType, lastDay, monthOrQuarter, monthOrQuarter, lastyear, false), "|"));
            }
        }
        return baseMap;

    }


    /**
     * 用timeFilter生成基础的map
     * @param timeFilter
     * @param type
     * @param d
     * @return
     * @throws Exception
     */
    public static Map<String, String> generateBaseMapWithTimeFilter(TimeFilter timeFilter,
                                                                    String type,
                                                                    String d) throws Exception{
        Map<String, String> baseMap = new HashMap<>();
        String lastDay = DateUtil.getDayOfInterval(d, -1);
        String year = timeFilter.getYear();
        String dateType = timeFilter.getDateType();
        String time = DateUtil.getInputTime(dateType, timeFilter.getMonth(), timeFilter.getQuarter(), timeFilter.getHalf());
        //非目标数据均需要分区
        if (!"target".equals(type)) {
            baseMap.put("query_d", d);
        }
        //如果是求当期数据或者目标数据
        if ("current".equals(type) || "target".equals(type)) {
            baseMap.put("year", year);
            baseMap.put(dateType, time);
        }
        //如果求的是同比 去年或者同比2019
        if ("lastyear".equals(type)) {
            String lastyear = DateUtil.getLastYear(year);
            baseMap.put("year", lastyear);
            baseMap.put(dateType, time);
            if (DateUtil.isLastestTime(lastDay, timeFilter)) {
                baseMap.put("the_date", DateUtil.getPopTimeRangeV2(timeFilter, lastDay, lastyear, false));
            }
        }
        if ("2019".equals(type)) {
            baseMap.put("year", "2019");
            baseMap.put(dateType, time);
            if (DateUtil.isLastestTime(lastDay, timeFilter)) {
                baseMap.put("the_date", DateUtil.getPopTimeRangeV2(timeFilter, lastDay, "2019", false));
            }
        }
        if ("currentpop".equals(type)) {
            baseMap.put("year", year);
            baseMap.put(dateType, time);
            if (DateUtil.isLastestTime(lastDay, timeFilter)) {
                baseMap.put("the_date", DateUtil.getPopTimeRangeV2(timeFilter, lastDay, year, false));
            }
        }
        return baseMap;
    }


    public static List<Map<String, String>> generateMomBaseMapList(TimeFilter timeFilter,
                                                                   String d) throws ParseException {
        String lastDay = DateUtil.getDayOfInterval(d, -1);
        String dateType = timeFilter.getDateType();
        String year = timeFilter.getYear();
        Map<String, String> momMap = new HashMap<>();
        Map<String, String> momMap2 = new HashMap<>();
        List<Map<String, String>> resultList = new ArrayList<>();
        resultList.add(momMap);
        resultList.add(momMap2);
        momMap.put("query_d", d);
        momMap2.put("query_d", d);
        if (DateUtil.isLastestTime(d, timeFilter)) {
            //最新时间 算30天环比
            String startDate = DateUtil.getDayOfInterval(lastDay, -29);
            momMap.put("the_date", startDate + "|" + lastDay);
            String momEndDate = DateUtil.getDayOfInterval(startDate, -1);
            String momStartDate = DateUtil.getDayOfInterval(momEndDate, -29);
            momMap2.put("the_date", momStartDate + "|" + momEndDate);
        } else {
            //非当前季 环比上个季
            //非当前半年 环比上个半年
            String time = DateUtil.getInputTime(dateType, timeFilter.getMonth(), timeFilter.getQuarter(), timeFilter.getHalf());
            momMap.put("year", year);
            momMap.put(dateType, time);
            List<String> lastTimeInfo = DateUtil.getLastTimeInfoV2(timeFilter);
            momMap2.put("year", lastTimeInfo.get(0));
            momMap2.put(dateType, lastTimeInfo.get(1));
        }
        return resultList;
    }

    public static void setDestinationValue(Map<String, String> baseMap,
                                           String subMetric,
                                           RemoteConfig remoteConfig) {
        String destinationC = remoteConfig.getConfigValue("destinationC");
        String destinationT = remoteConfig.getConfigValue("destinationT");
        if ("destination_c".equals(subMetric)) {
            baseMap.put("destination", destinationC);
        } else if ("destination_t".equals(subMetric)) {
            baseMap.put("destination", destinationT);
        }else if ("destinationC".equals(subMetric)) {
            baseMap.put("destination", destinationC);
        } else if ("destinationT".equals(subMetric)) {
            baseMap.put("destination", destinationT);
        }
    }

    public static void setDestinationRangeValue(Map<String, String> baseMap,
                                                OverseaMetricInfoBean metricInfoBean,
                                                RemoteConfig remoteConfig) {
        String region = remoteConfig.getConfigValue("region");
        String subRegion = remoteConfig.getConfigValue("subRegion");
        String country = remoteConfig.getConfigValue("country");
        String destinationLevel = metricInfoBean.getDestinationLevel();
        List<String> destinationRangeList = metricInfoBean.getDestinationRangeList();
        if (GeneralUtil.isNotEmpty(destinationRangeList)) {
            if (region.equals(destinationLevel)) {
                baseMap.put("region_name", StringUtils.join(destinationRangeList, "|"));
            } else if (subRegion.equals(destinationLevel)) {
                baseMap.put("province_name", StringUtils.join(destinationRangeList, "|"));
            } else if (country.equals(destinationLevel)) {
                baseMap.put("country_name", StringUtils.join(destinationRangeList, "|"));
            }
        }
    }



    /**
     * 填充目的地数据  不拆分考核范围(用于质量成本目标)
     * 如果考核层级为海外   则region_name=海外
     * 如果考核层级为大区   则region_name=考核范围
     * 如果考核层级为子区域  则province_name=考核范围
     * 注意:不要对考核范围做任何处理
     *
     * @param baseMap
     * @param metricInfoBean
     * @param remoteConfig
     */
    public static void setDestinationRangeValueWithoutSpilit(Map<String, String> baseMap,
                                                             OverseaMetricInfoBean metricInfoBean,
                                                             RemoteConfig remoteConfig) {
        String oversea = remoteConfig.getConfigValue("oversea");
        String region = remoteConfig.getConfigValue("region");
        String subRegion = remoteConfig.getConfigValue("subRegion");
        String destinationLevel = metricInfoBean.getDestinationLevel();
        List<String> destinationRangeList = metricInfoBean.getDestinationRangeList();

        if (oversea.equals(destinationLevel)) {
            baseMap.put("region_name", oversea);
        } else if (region.equals(destinationLevel)) {
            baseMap.put("region_name", StringUtils.join(destinationRangeList, ";"));
            //特殊逻辑 必须要用大区+子区域才能唯一确定数据
            baseMap.put("province_name", StringUtils.join(destinationRangeList, ";"));
        } else if (subRegion.equals(destinationLevel)) {
            baseMap.put("province_name", StringUtils.join(destinationRangeList, ";"));
        }
    }


    /**
     * 填充站点数据  不拆分考核范围(用于质量成本目标)
     * 如果考核层级为海外   则region_name=海外
     * 如果考核层级为大区   则region_name=考核范围
     * 如果考核层级为子区域  则province_name=考核范围
     * 注意:不要对考核范围做任何处理
     *
     * @param baseMap
     * @param metricInfoBean
     * @param remoteConfig
     */
    public static void setSiteRangeValueWithoutSpilit(Map<String, String> baseMap,
                                                      OverseaMetricInfoBean metricInfoBean,
                                                      RemoteConfig remoteConfig,
                                                      int y) {
        String oversea = remoteConfig.getConfigValue("oversea");
        String destinationLevel = metricInfoBean.getDestinationLevel();
        List<String> siteRangeList = metricInfoBean.getSiteRangeList();

        if (oversea.equals(destinationLevel)) {
            if (y <= 2024) {
                baseMap.put("site", oversea);
            }else{
                baseMap.put("site", "ALL");
            }
        } else if (GeneralUtil.isNotEmpty(siteRangeList)) {
            baseMap.put("site", StringUtils.join(siteRangeList, ";"));
        }
    }



    public static void setSiteRangeValue(Map<String, String> baseMap,
                                         OverseaMetricInfoBean metricInfoBean,
                                         RemoteConfig remoteConfig) {
        String oversea = remoteConfig.getConfigValue("oversea");
        String destinationLevel = metricInfoBean.getDestinationLevel();
        List<String> siteRangeList = metricInfoBean.getSiteRangeList();
        if (GeneralUtil.isNotEmpty(siteRangeList) && !siteRangeList.contains("ALL") && !oversea.equals(destinationLevel)) {
            baseMap.put("site", StringUtils.join(siteRangeList, "|"));
        }
    }


    public static void setChannelRangeValue(Map<String, String> baseMap,
                                            OverseaMetricInfoBean metricInfoBean,
                                            RemoteConfig remoteConfig) {
        String oversea = remoteConfig.getConfigValue("oversea");
        String destinationLevel = metricInfoBean.getDestinationLevel();
        List<String> channelRangeList = metricInfoBean.getChannelRangeList();
        if (GeneralUtil.isNotEmpty(channelRangeList) && !oversea.equals(destinationLevel)) {
            baseMap.put("channel_name", StringUtils.join(channelRangeList, "|"));
        }
    }


    public static void setConditionValue(Map<String, String> baseMap,
                                         SubMetricFiledBean configBean,
                                         List<String> fieldValueList) {
        String columnCondition = configBean.getConditionColumn();
        if (GeneralUtil.isNotEmpty(fieldValueList)) {
            baseMap.put(columnCondition, StringUtils.join(fieldValueList, "|"));
        }
    }

    public static void setPagingConditionValue(Map<String, String> baseMap,
                                               String field,
                                               List<String> fieldValueList) {
        baseMap.put(field, StringUtils.join(fieldValueList, "|"));
    }

    public static String getMomType(TimeFilter timeFilter,
                                    String d) throws ParseException {
        String lastDay = DateUtil.getDayOfInterval(d, -1);
        String dateType = timeFilter.getDateType();
        if (DateUtil.isLastestTime(lastDay, timeFilter)) {
            return "30days";
        }else if("quarter".equals(dateType)){
            return "lastquarter";
        }else{
            return "lasthalf";
        }
    }



    //对于海外指标 在趋势线传参时 前端没有办法把精确的时间做转化
    //此时需要前置将前端传入的时间参数处理为精确的时间(海外仅支持按月查看数据)
    public static TimeFilter getActualTimeFilter(TimeFilter originFilter,
                                                 String d) throws ParseException {

        TimeFilter actualFilter = new TimeFilter();
        actualFilter.setTimeFrame(originFilter.getTimeFrame());
        actualFilter.setDateType("month");
        actualFilter.setYear(originFilter.getYear());

        String lastDay = DateUtil.getDayOfInterval(d, -1);
        if (DateUtil.isLastestTime(lastDay, originFilter)) {
            //如果处于最新的时间周期  那么直接根据lastDay推算最新的month
            actualFilter.setMonth(DateUtil.getActualMonthOfD(d));
        } else {
            //如果不处于最新时间周期  那么month的值就是传入季的最后一个月或者半年的最后一个月
            String datetype = originFilter.getDateType();
            if ("quarter".equals(datetype)) {
                actualFilter.setMonth(DateUtil.getLastMonthOfQuarter(originFilter.getQuarter()));
            } else {
                actualFilter.setMonth(DateUtil.getLastMonthOfHalf(originFilter.getHalf()));
            }
        }

        return actualFilter;
    }

    //对于海外指标 在趋势线传参时 前端没有办法把精确的时间做转化
    //此时需要前置将前端传入的时间参数处理为精确的时间(海外数据下表仅展示到季度维度)
    public static TimeFilter getActualTimeFilterWithQuarter(TimeFilter originFilter,
                                                 String d) throws ParseException {

        TimeFilter actualFilter = new TimeFilter();
        actualFilter.setTimeFrame(4);
        actualFilter.setDateType("quarter");
        actualFilter.setYear(originFilter.getYear());

        if (originFilter.getQuarter() != null) {
            // 按照季维度下发
            actualFilter.setQuarter(originFilter.getQuarter());
        }else{
            // 按照half维度下发
            if (originFilter.getHalf() != null) {
                actualFilter.setQuarter("H1".equals(originFilter.getHalf()) ? "Q2" : "Q4");
            }
        }
        return actualFilter;
    }

    /**
     * 获取完整的枚举值查询列表
     * @param businessId
     * @return
     */
    public static List<String> getBuType(Integer businessId) {
        List<String> buTypeList = new ArrayList<>();
        if (businessId == 0) {
            buTypeList.add(BuTypeEnum.TICKET.getName());
            buTypeList.add(BuTypeEnum.ACTIVITY_ACT.getName());
            buTypeList.add(BuTypeEnum.ACTIVITY_DAY.getName());
        }else if (businessId == 1) {
            buTypeList.add(BuTypeEnum.TICKET.getName());
        }else{
            buTypeList.add(BuTypeEnum.ACTIVITY_ACT.getName());
            buTypeList.add(BuTypeEnum.ACTIVITY_DAY.getName());
        }
        return buTypeList;
    }

    /**
     * 获取完整的枚举值查询列表
     * @param businessId
     * @return
     */
    public static List<String> getBuTypeWithTarget(Integer businessId) {
        List<String> buTypeList = new ArrayList<>();
        if (businessId == 0) {
            buTypeList.add(BuTypeEnum.TICKET.getName());
            buTypeList.add(BuTypeEnum.ACTIVITY.getName());
        }else if (businessId == 1) {
            buTypeList.add(BuTypeEnum.TICKET.getName());
        }else{
            buTypeList.add(BuTypeEnum.ACTIVITY.getName());
        }
        return buTypeList;
    }


    //将groupList的字段转化语种
    public static List<String> translateGroupList(List<String> groupList) {
        String vbkLocale = UserUtil.getVbkLocale();
        //中文不转
        if ("zh-CN".equals(vbkLocale)) {
            return groupList;
        }
        String suffix = "_" + vbkLocale.split("-")[0];
        List<String> newGroupList = new ArrayList<>();
        for (String groupTag : groupList) {
            if (groupTag.endsWith("name")) {
                newGroupList.add(groupTag + suffix);
            } else {
                newGroupList.add(groupTag);
            }
        }
        return newGroupList;
    }

    //还原groupList的语种
    public static List<String> revertGroupList(List<String> groupList) {
        String vbkLocale = UserUtil.getVbkLocale();
        //中文不转
        if ("zh-CN".equals(vbkLocale)) {
            return groupList;
        }
        String suffix = "_" + vbkLocale.split("-")[0];
        List<String> newGroupList = new ArrayList<>();
        for (String groupTag : groupList) {
            if (groupTag.endsWith(suffix)) {
                newGroupList.add(groupTag.substring(0, groupTag.length() - suffix.length()));
            } else {
                newGroupList.add(groupTag);
            }
        }
        return newGroupList;
    }

    //校验传入的下钻维度是否能获取气泡图
    public static void checkBubble(SubMetricFiledBean configBean,
                                   GetOverseaTableDataRequestType request) {
        if (GeneralUtil.isEmpty(configBean)) {
            throw new ConfigImportException("the drilldown config is not exist,please check it!");
        }
        Boolean needBubble = configBean.getNeedBubble();
        String type = request.getQueryType();
        if ("bubble".equals(type) && !needBubble) {
            throw new InputArgumentException("this filed can't get bubble,please check it!");
        }
    }

    /**
     * 判断某组考核数据在某个考核周期是否可以展示下钻趋势线
     * @param field
     * @param destinationLevel
     * @param subMetric
     * @param remoteConfig
     * @return
     */
    public static Boolean checkLine(String field,
                                    String destinationLevel,
                                    String subMetric,
                                    RemoteConfig remoteConfig) {
        //子指标为站点或者渠道不需要做判断
        if (subMetric.equals("site") || subMetric.equals("channel")) {
            return true;
        }
        //考核层级为海外的不需要做判断
        String oversea = remoteConfig.getConfigValue("oversea");
        if (oversea.equals(destinationLevel)) {
            return true;
        }
        //考核层级为子区域的只要当前下钻的不是大区就行了
        String subRegion = remoteConfig.getConfigValue("subRegion");
        if (subRegion.equals(destinationLevel) && "region".equals(field)) {
            return false;
        }
        return true;
    }

    /**
     * 判断考核层级是否在子区域及以上,匹配所有字符串都不是下面三个值返回false
     * @param destinationLevels
     * @return
     */
    public static Boolean selectTargetFrom(List<String> destinationLevels) {
        for (String level : destinationLevels) {
            if (ChannelSiteTypeEnum.SITE.getName().equals(level) || ChannelSiteTypeEnum.CHANNEL.getName().equals(level)) {
                continue;
            }
            if (!DestinationLevelTypeEnum.OVERSEA.getName().equals(level)
                    && !DestinationLevelTypeEnum.REGION.getName().equals(level)
                    && !DestinationLevelTypeEnum.SUB_REGION.getName().equals(level)) {
                return false;
            }
        }
        return true;
    }


    /**
     * 判断CT
     * @param subMetric
     * @return
     */
    public static String judgeCT(String subMetric) {
        if ("destinationC".equals(subMetric)) {
            return "c";
        }else if ("destinationT".equals(subMetric)) {
            return "t";
        }else{
            return null;
        }
    }

    /**
     * 获取季度列表
     * @param half
     * @return
     */
    public static List<String> getQWithHalf(String half) {
        if ("H1".equals(half)) {
            return Arrays.asList("Q1", "Q2");
        } else if ("H2".equals(half)) {
            return Arrays.asList("Q3", "Q4");
        } else {
            throw new InputArgumentException("the half is not correct,please check it!");
        }
    }
    /**
     * 判断CT
     * @param subMetric
     * @return
     */
    public static List<String> convertCT(RemoteConfig remoteConfig, String subMetric) {
        String C = remoteConfig.getConfigValue("destinationC");
        String T = remoteConfig.getConfigValue("destinationT");
        if ("destinationC".equals(subMetric)) {
            return Arrays.asList(C);
        } else if ("destinationT".equals(subMetric)) {
            return Arrays.asList(T);
        } else {
            return Arrays.asList(C, T);
        }
    }
    /**
     * 判断CT
     * @param subMetric
     * @return
     */
    public static List<String> convertCTdestination(RemoteConfig remoteConfig, String subMetric) {
        String C = remoteConfig.getConfigValue("Cdestination");
        String T = remoteConfig.getConfigValue("Tdestination");
        if ("destinationC".equals(subMetric)) {
            return Arrays.asList(C);
        } else if ("destinationT".equals(subMetric)) {
            return Arrays.asList(T);
        } else {
            return Arrays.asList(C, T);
        }
    }

    public static Integer safeGetInteger(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return value != null ? Integer.parseInt(value.toString()) : 0;
    }

    public static  Double safeGetDouble(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        return value != null ? Double.parseDouble(value.toString()) : 0.0;
    }

    /**
     * 获取海外（非站点渠道）的考核层级（仅限于子区域以上返回正常，其他返回默认第一个，空就返回other）
     * @param child
     * @return
     */
    public static String getDestinationLevelWithOverseaMetricChild(OverseaMetricInfoBeanV2Child child) {
        for (String level : child.getDestinationLevelList()) {
            if ("站点".equals(level) || "渠道".equals(level)) {//NOSONAR
                continue;
            }
            if ("海外".equals(level) || "大区".equals(level) || "子区域".equals(level)) {//NOSONAR
                return level;
            }
        }
        return Optional.ofNullable(child.getDestinationLevelList().get(0)).orElse("other");
    }

    /**
     * 指标卡数据返回顺序排序
     * @param metricList
     */
    public static void sortMetricWithFixedOrder(List<OveaseaMetric> metricList) {
        // 指标固定顺序
        List<String> metricOrder = Arrays.asList("101", "102", "110", "103", "105", "106", "107");
        // 子指标固定顺序
        List<String> subMetricOrder = Arrays.asList("destinationCT", "destinationC", "destinationT", "site", "channel", "tklk", "tfly");
        // 指标自定义比较器
        Comparator<OveaseaMetric> comparatorMetric = (m1, m2) -> {
            int index1 = metricOrder.indexOf(m1.metric);
            int index2 = metricOrder.indexOf(m2.metric);

            // 处理不在列表中的 metric（排到最后）
            if (index1 == -1) index1 = Integer.MAX_VALUE;
            if (index2 == -1) index2 = Integer.MAX_VALUE;

            return Integer.compare(index1, index2);
        };
        // 子指标自定义比较器
        Comparator<OveaseaSubMetric> comparatorSubMetric = (m1, m2) -> {
            int index1 = subMetricOrder.indexOf(m1.subMetric);
            int index2 = subMetricOrder.indexOf(m2.subMetric);

            // 处理不在列表中的 metric（排到最后）
            if (index1 == -1) index1 = Integer.MAX_VALUE;
            if (index2 == -1) index2 = Integer.MAX_VALUE;

            return Integer.compare(index1, index2);
        };
        metricList.forEach(metric -> {
            // 对每个指标的子指标进行排序
            metric.getSubMetricList().sort(comparatorSubMetric);
        });

        metricList.sort(comparatorMetric);
    }

    /**
     * 设置趋势线接口下钻如果没有传入下钻枚举的默认值
     */
    public static GetOverseaDrillDownBaseInfoV2RequestType getOverseaDrillDownBaseInfoV2RequestType(GetOverseaTrendLineDataV2RequestType trendLineRequest) {
        GetOverseaDrillDownBaseInfoV2RequestType drillRequest = new GetOverseaDrillDownBaseInfoV2RequestType();
        drillRequest.setMetric(trendLineRequest.getMetric());
        drillRequest.setSubMetric(trendLineRequest.getSubMetric());
        drillRequest.setDomainName(trendLineRequest.getDomainName());
        drillRequest.setTimeFilter(trendLineRequest.getTimeFilter());
        drillRequest.setBusinessLine(trendLineRequest.getBusinessLine());
        return drillRequest;
    }

    /**
     * 设置下钻维度的枚举值
     * @param response
     * @param dimName
     * @return
     */
    public static List<String> setDimValues(GetOverseaDrillDownBaseInfoV2ResponseType response, String dimName, String metric) {
        if (response == null || response.getDimList() == null) {
            return Collections.emptyList();
        }
        for (DilldownDim drill : response.getDimList()) {
            if (dimName.equals(drill.getDimName())) {
                List<String> dimValues;
                if ("103".equals(metric)) {
                    dimValues = drill.getDimValueList().stream().map(DimValueType::getId).filter(Objects::nonNull).map(String::valueOf).collect(Collectors.toList());
                }else{
                    dimValues = drill.getDimValueList().stream().map(DimValueType::getDimValue).filter(Objects::nonNull).collect(Collectors.toList());
                }
                if (dimValues.isEmpty()) {
                    return Collections.emptyList();
                }
                return dimValues;
            }
        }
        return Collections.emptyList();
    }
}
