package com.ctrip.tour.business.dashboard.tktBusiness.enums;

public enum FileTypeEnum {
    CSV("csv", "text/csv", "text/csv"),
    XLS("xls", "application/vnd.ms-excel", "application/vnd.ms-excel"),
    XLSX("xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "application/vnd.ms-excel"),
    PNG("png","image/png", "image/png"),
    JPG("jpg","image/jpeg", "image/jpeg");

    private String code;
    private String contentType;
    private String pushType;

    FileTypeEnum(String code, String contentType, String pushType) {
        this.code = code;
        this.contentType = contentType;
        this.pushType = pushType;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public String getPushType() {
        return pushType;
    }

    public void setPushType(String pushType) {
        this.pushType = pushType;
    }
}