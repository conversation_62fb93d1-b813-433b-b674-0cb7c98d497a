package com.ctrip.tour.business.dashboard.sightArchives.proxy;

import com.ctrip.gs.cranking.ranking.contract.*;
import com.ctrip.gs.cranking.ranking.service.contract.RequestHeadType;
import com.ctrip.tour.business.dashboard.utils.UserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class CRankingRankingServiceProxy {

    public List<String> getRankingItemRank(Long poiId) {
        List<String> rankDescList = new ArrayList<>();

        CRankingRankingServiceClient cRankingRankingServiceClient = CRankingRankingServiceClient.getInstance();
        GetRankingItemRankRequestType requestType = new GetRankingItemRankRequestType();
        requestType.setNeedAll(true);
        BusinessInfoType businessInfoType = new BusinessInfoType();
        businessInfoType.setBusinessId(String.valueOf(poiId));
        businessInfoType.setBusinessType("sight");
        requestType.setBusinessInfoList(Collections.singletonList(businessInfoType));
        RequestHeadType head = new RequestHeadType();
        head.setPlatform("09");
        head.setSource("tour_business_dashboard_search_rank");
        head.setLocale(UserUtil.getVbkLocale());
        requestType.setHead(head);

        GetRankingItemRankResponseType responseType = null;
        try {
            log.info("getRankingItemRank requestType: {}", requestType);
            responseType = cRankingRankingServiceClient.getRankingItemRank(requestType);
            log.info("getRankingItemRank responseType: {}", responseType);
            List<RankInfoType> rankingInfoTypeList = responseType.getRankingItemRankList().get(0).getRankInfoList();
            rankDescList = rankingInfoTypeList.stream().map(RankInfoType::getRankDesc).collect(Collectors.toList());
        } catch (Exception e) {
            log.warn("getRankingItemRank failed", e);
        }

        return rankDescList;
    }

}
