package com.ctrip.tour.business.dashboard.tktBusiness.strategy.impl;

import com.ctrip.soa._24922.*;
import com.ctrip.soa._27181.BIBaseReportQueryServiceClient;
import com.ctrip.soa._27181.GetRawDataRequestType;
import com.ctrip.soa._27181.GetRawDataResponseType;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.DrillDownFieldBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.SqlParamterBean;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.UserPermissionBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.Bus2Helper;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.MetricHelper;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.SwitchNewTableHelper;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.MetricCalStrategy;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ListenableFuture;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

@Component
public class Bus2MetricStrategy implements MetricCalStrategy {

    @Autowired
    private Bus2TicketActivityStrategy bus2TicketActivityStrategy;


    @Autowired
    private Bus1And2DayTourStrategy bus1And2DayTourStrategy;

    @Autowired
    private UserPermissionBiz userPermissionBiz;

    @Autowired
    private RemoteConfig remoteConfig;

    @Autowired
    private BIBaseReportQueryServiceClient baseReportQueryServiceClient;

    @Autowired
    private SwitchNewTableHelper switchNewTableHelper;

    @Override
    public Future<MetricDetailInfo> getSingleMetricCardData(String domainName,
                                                            TimeFilter timeFilter,
                                                            MetricInfoBean metricInfoBean,
                                                            String d,
                                                            Boolean needRank) throws Exception {
        MetricDetailInfo metricDetailInfo = new MetricDetailInfo();
        List<MetricDetailInfo> subMetricDetailInfoList = new ArrayList<>();
        metricDetailInfo.setSubMetricDetailInfoList(subMetricDetailInfoList);
        metricDetailInfo.setMetric(getMetricName());

        CheckUserPermissionResponseType checkUserPermissionReq = userPermissionBiz.checkUserPermission(new CheckUserPermissionRequestType(timeFilter, domainName, null, null));
        List<String> subMetricList = checkUserPermissionReq.getDomesticBasicConfig()
                .getMetricCardConfigMap()
                .get(getMetricName())
                .getSubMetricList();

        if (subMetricList.size() == 1) {
            //只考核单一业务线
            String subMetric = subMetricList.get(0);
            MetricDetailInfo subMetricDetailInfo;
            if ("ticketActivity".equals(subMetric)) {
                subMetricDetailInfo = bus2TicketActivityStrategy.getSingleMetricCardData(domainName, timeFilter, metricInfoBean, d);
            } else {
                subMetricDetailInfo = bus1And2DayTourStrategy.getSingleMetricCardData(timeFilter, metricInfoBean, d, getMetricName(), subMetric);
            }
            subMetricDetailInfoList.add(subMetricDetailInfo);
            metricDetailInfo.setSubMetric(subMetric);
            metricDetailInfo.setMomType(subMetricDetailInfo.getMomType());
            metricDetailInfo.setDimData(Bus2Helper.convertDimMap(subMetricDetailInfo.getDimData(), subMetric));
        } else {
            //考核多个业务线 从第二个元素开始遍历(第一个元素是复合值)
            List<Map<String, Double>> mapList = new ArrayList<>();
            for (int i = 1; i < subMetricList.size(); i++) {
                String subMetric = subMetricList.get(i);
                MetricDetailInfo subMetricDetailInfo;
                if ("ticketActivity".equals(subMetric)) {
                    subMetricDetailInfo = bus2TicketActivityStrategy.getSingleMetricCardData(domainName, timeFilter, metricInfoBean, d);
                } else {
                    subMetricDetailInfo = bus1And2DayTourStrategy.getSingleMetricCardData(timeFilter, metricInfoBean, d, getMetricName(), subMetric);
                }
                subMetricDetailInfoList.add(subMetricDetailInfo);
                mapList.add(Bus2Helper.convertDimMap(subMetricDetailInfo.getDimData(), subMetric));
            }
            metricDetailInfo.setSubMetric(subMetricList.get(0));
            metricDetailInfo.setMomType(subMetricDetailInfoList.get(0).getMomType());
            metricDetailInfo.setDimData(Bus2Helper.summaryMapListData(mapList, metricDetailInfo.getMomType(), ""));

        }


        setMetricCardDrillDownInfo(metricDetailInfo, metricInfoBean, checkUserPermissionReq);

        //获取排名
        if (needRank) {
            SqlParamterBean rankingBean = Bus2Helper.getRankingSqlBean(domainName, timeFilter, d);
            GetRawDataRequestType rankingReq = rankingBean.convertBeanToRequest(true);
            GetRawDataResponseType rankingRes = switchNewTableHelper.switchRemoteDatabase(rankingReq);
            Bus2Helper.processRankData(rankingRes, metricDetailInfo);
        }


        return new AsyncResult<>(metricDetailInfo);
    }


    private void setMetricCardDrillDownInfo(MetricDetailInfo metricDetailInfo,
                                            MetricInfoBean metricInfoBean,
                                            CheckUserPermissionResponseType checkUserPermissionReq) {
        //首页应该展示的简易子指标列表
        List<String> subMetricList = MetricHelper.calDrillDownSubMetricList(getMetricName(),checkUserPermissionReq);

        if (subMetricList.size() == 1) {
            String subMetric = subMetricList.get(0);
            metricDetailInfo.setDefaultSubMetric(subMetric);
            //是复合类型的子指标
            if (subMetric.contains("+")) {
                MetricHelper.setMetricCardDrillDownInfo(metricInfoBean, metricDetailInfo);
            } else {
                if ("ticketActivity".equals(subMetric)) {
                    MetricHelper.setMetricCardDrillDownInfo(metricInfoBean, metricDetailInfo);
                } else {
                    MetricHelper.setOdtMetricCardDrillDownInfo(metricInfoBean, metricDetailInfo, remoteConfig);
                }
            }
        } else {
            //如果超过一个字指标  为了保证首页展示数据的一致性 此时必定可以下钻
            //逻辑会与上面的单个指标不一致
            metricDetailInfo.setNeedDrillDown(true);
            metricDetailInfo.setDefaultSubMetric(StringUtils.join(subMetricList, "|"));
            MetricHelper.setMultiSubMetricDrillDownField(metricInfoBean, metricDetailInfo, remoteConfig, subMetricList);
        }

    }



    @Override
    public GetTrendLineDataResponseType getSingleTrendlineData(GetTrendLineDataRequestType request,
                                                               MetricInfoBean metricInfoBean,
                                                               String d) throws Exception {
        String subMetric = request.getSubMetric();
        if (subMetric.contains("+")) {
            //复合子指标的场景
            List<String> subMetricList = Lists.newArrayList(subMetric.split("\\+"));
            Map<String, GetTrendLineDataResponseType> singleResponseMap = new HashMap<>();
            for (String item : subMetricList) {
                //每次调用前把子指标还原为原子指标(如果以后改成并行操作 这里要修改)
                request.setSubMetric(item);
                if ("ticketActivity".equals(item)) {
                    singleResponseMap.put(item, bus2TicketActivityStrategy.getSingleTrendlineData(request, metricInfoBean, d));
                } else {
                    singleResponseMap.put(item, bus1And2DayTourStrategy.getSingleTrendlineData(request, d));
                }
            }
            GetTrendLineDataResponseType totalResponse = new GetTrendLineDataResponseType();
            //根据是否包含出境日游确定不同的数据展示范围
            String type = "oversea";
            if (subMetricList.contains("overseaDayTour")) {
                type = "overseaDayTour";
            }
            Bus2Helper.mergeTrendLineData(singleResponseMap, totalResponse, request.getTimeFilter(), type);
            return totalResponse;

        } else if ("ticketActivity".equals(subMetric)) {
            //单门票活动
            return bus2TicketActivityStrategy.getSingleTrendlineData(request, metricInfoBean, d);
        }
        //单日游(国内/出境)
        return bus1And2DayTourStrategy.getSingleTrendlineData(request, d);
    }

    @Override
    public GetTableDataResponseType getSingleTableData(GetTableDataRequestType request,
                                                       MetricInfoBean metricInfoBean,
                                                       String d) throws Exception {
        String subMetric = request.getSubMetric();
        if (subMetric.contains("+")) {
            return getTotalSingleTableData(request, metricInfoBean, d);
        } else if ("ticketActivity".equals(subMetric)) {
            return bus2TicketActivityStrategy.getSingleTableData(request, metricInfoBean, d);
        }
        return bus1And2DayTourStrategy.getSingleTableData(request, metricInfoBean, d);
    }



    private GetTableDataResponseType getTotalSingleTableData(GetTableDataRequestType request,
                                                             MetricInfoBean metricInfoBean,
                                                             String d) throws Exception{
        GetTableDataResponseType response = new GetTableDataResponseType();
        List<TableDataItem> tableDataItemList = new ArrayList<>();
        response.setTableDataItemList(tableDataItemList);
        String momType = Bus2Helper.getMomType(request.getTimeFilter(), d);
        response.setMomType(momType);

        DrillDownFilter drillDownFilter = request.getDrillDownFilter();
        String field = MetricHelper.getDrillDownColumnName(drillDownFilter.getField());
        String metric = request.getMetric();
        String subMetric = request.getSubMetric();
        DrillDownFieldBean configBean = remoteConfig.getDrillDownFieldBean(metric, subMetric, field);

        //如果按商拓下钻 判定是不是需要展示同比数据
        Boolean needShowExamineePopData = DateUtil.isLastestQuarter(request.getTimeFilter(), d);
        response.setNeedShowExamineePopData(needShowExamineePopData);

        //获取当前数据
        SqlParamterBean currentBean = Bus2Helper.getTableDataSqlBean(request, d, metricInfoBean, configBean,
                remoteConfig, "current", null, momType, "");
        GetRawDataRequestType currentReq = currentBean.convertBeanToRequest(true);
        ListenableFuture<GetRawDataResponseType> currentResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(currentReq);

        //商拓直接从主表获取目标数据 而不需要再从单独的目标表中获取
        if ("examinee".equals(field)) {
            Bus2Helper.processTableBaseData(currentResFuture.get(), tableDataItemList);
        } else {
            //获取目标数据
            SqlParamterBean targetBean = Bus2Helper.getTableDataSqlBean(request, d, metricInfoBean, configBean,
                    remoteConfig, "target", null, momType, "");
            GetRawDataRequestType targetReq = targetBean.convertBeanToRequest(true);
            ListenableFuture<GetRawDataResponseType> targetResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(targetReq);

            Bus2Helper.processTableBaseData(currentResFuture.get(), targetResFuture.get(), tableDataItemList);
        }

        response.setTotalNum(currentResFuture.get().getTotalNum());

        //收集下钻字段的数据
        List<String> pagingFieldValueList = tableDataItemList.stream()
                .map(i -> i.getFieldMap().get(field))
                .collect(Collectors.toList());

        //如果当前有数据 则去获取同环比
        if (GeneralUtil.isNotEmpty(pagingFieldValueList)) {
            //下钻层级是商拓 且不是最新季度数据  不需要展示同环比
            if ("examinee".equals(field) && !needShowExamineePopData) {
                return response;
            }

            if ("examinee".equals(field)) {
                List<String> bdSpecificList = configBean.getBdSpecificList();
                for (String item : bdSpecificList) {
                    setTablePopData(request, metricInfoBean, d, configBean,
                            momType, item, tableDataItemList);
                }
            } else {
                setTablePopData(request, metricInfoBean, d, configBean,
                        momType, "", tableDataItemList);
            }

            //只有首页需要获取环比数据
            // 按商拓下钻不获取环比数据
            if ("firstpage".equals(request.getSource()) && !"examinee".equals(field)) {
                //获取环比数据
                SqlParamterBean momBean = Bus2Helper.getTableDataSqlBean(request, d, metricInfoBean, configBean,
                        remoteConfig, "mom", null, momType,"");
                //环比的当前数据
                GetRawDataRequestType momReq = momBean.convertBeanToRequest(true);
                ListenableFuture<GetRawDataResponseType> momResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(momReq);
                //环比的上一期数据
                GetRawDataRequestType momReq2 = momBean.convertBeanToRequest2(true);
                ListenableFuture<GetRawDataResponseType> momRes2Future = switchNewTableHelper.switchRemoteDatabaseAsync(momReq2);
                Bus2Helper.processTableMomData(momResFuture.get(), momRes2Future.get(), tableDataItemList, momType);
            }

        }

        return response;
    }


    //设置表格同比数据
    private void setTablePopData(GetTableDataRequestType request,
                                 MetricInfoBean metricInfoBean,
                                 String d,
                                 DrillDownFieldBean configBean,
                                 String momType,
                                 String subType,
                                 List<TableDataItem> tableDataItemList) throws Exception {
        //获取同比当期数据
        SqlParamterBean currentpopBean = Bus2Helper.getTableDataSqlBean(request, d, metricInfoBean, configBean,
                remoteConfig, "currentpop", null, momType, subType);
        GetRawDataRequestType currentpopReq = currentpopBean.convertBeanToRequest(true);
        ListenableFuture<GetRawDataResponseType> currentpopResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(currentpopReq);

        //获取去年数据
        SqlParamterBean lastyearBean = Bus2Helper.getTableDataSqlBean(request, d, metricInfoBean, configBean,
                remoteConfig, "lastyear", null, momType, subType);
        GetRawDataRequestType lastyearReq = lastyearBean.convertBeanToRequest(true);
        ListenableFuture<GetRawDataResponseType> lastyearResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(lastyearReq);

        //获取2019年数据
        SqlParamterBean _2019Bean = Bus2Helper.getTableDataSqlBean(request, d, metricInfoBean, configBean,
                remoteConfig, "2019", null, momType, subType);
        GetRawDataRequestType _2019Req = _2019Bean.convertBeanToRequest(true);
        ListenableFuture<GetRawDataResponseType> _2019ResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(_2019Req);

        Bus2Helper.processTableCurrentPopData(currentpopResFuture.get(), tableDataItemList);
        Bus2Helper.processTablePopData(lastyearResFuture.get(), tableDataItemList, "lastyear");
        Bus2Helper.processTablePopData(_2019ResFuture.get(), tableDataItemList, "2019");
    }



    @Override
    public GetDrillDownBaseInfoResponseType getSingleDrillDownBaseInfo(GetDrillDownBaseInfoRequestType request,
                                                                       MetricInfoBean metricInfoBean,
                                                                       String d) throws Exception {
        String subMetric = request.getSubMetric();
        if (subMetric.contains("+")) {
            return getTotalSingleDrillDownBaseInfo(request, metricInfoBean, d);
        } else if ("ticketActivity".equals(subMetric)) {
            return bus2TicketActivityStrategy.getSingleDrillDownBaseInfo(request, metricInfoBean, d);
        }
        return bus1And2DayTourStrategy.getSingleDrillDownBaseInfo(request, metricInfoBean, d);
    }



    private GetDrillDownBaseInfoResponseType getTotalSingleDrillDownBaseInfo(GetDrillDownBaseInfoRequestType request,
                                                                             MetricInfoBean metricInfoBean,
                                                                             String d) throws Exception {
        GetDrillDownBaseInfoResponseType response = new GetDrillDownBaseInfoResponseType();

        Boolean needSearch = request.isNeedSearch();
        if (GeneralUtil.isEmpty(needSearch)) {
            request.setNeedSearch(false);
            needSearch = false;
        }
        List<String> fieldList = new ArrayList<>();
        if (needSearch) {
            fieldList.add(MetricHelper.getDrillDownColumnName(request.getSearchField()));
        } else {
            fieldList.addAll(Bus2Helper.getDrillDownFieldList(metricInfoBean, remoteConfig));
            response.setDefaultChosenField(MetricHelper.getDataBaseColumnName(fieldList.get(0)));
        }
        Map<String, Future<GetRawDataResponseType>> fieldMap = new HashMap<>();
        for (String field : fieldList) {
            SqlParamterBean sqlParamterBean = Bus2Helper.getDrillDownBaseInfoSqlBean(field, request, d, metricInfoBean, remoteConfig);
            GetRawDataRequestType fieldRequest = sqlParamterBean.convertBeanToRequest(false);
            fieldMap.put(field, switchNewTableHelper.switchRemoteDatabaseAsync(fieldRequest));
        }
        List<FieldDataItem> fieldDataItemList = new ArrayList<>();
        response.setFieldDataItemList(fieldDataItemList);
        for (String field : fieldList) {
            Bus2Helper.processDrillDownBaseInfo(field, fieldMap.get(field).get(), fieldDataItemList);
        }

        return response;
    }

    @Override
    public String getMetricName() {
        return "2";
    }
}
