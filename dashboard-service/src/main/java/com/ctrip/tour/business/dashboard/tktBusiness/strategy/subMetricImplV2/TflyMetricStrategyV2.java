package com.ctrip.tour.business.dashboard.tktBusiness.strategy.subMetricImplV2;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.sightArchives.dao.overseametric.CdmPrdTktDashboardFlyWeaknessStatisticsDfDao;
import com.ctrip.tour.business.dashboard.sightArchives.dao.overseametric.CdmPrdTktDashboardKlkWeaknessStatisticsDfDao;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.*;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.enums.CompetitorTypeEnumEnum;
import com.ctrip.tour.business.dashboard.tktBusiness.enums.TrendDrillTypeEnum;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.Bus105And106And107Helper;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.Bus567Helper;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.CompetitorSubMetricCalStrategy;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.CompetitorSubMetricCalStrategyV2;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.subMetricImpl.CompetitorBaseStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.Future;

/**
 * 海外竞品飞猪子策略
 */
@Component
public class TflyMetricStrategyV2 implements CompetitorSubMetricCalStrategyV2 {

    @Autowired
    private CompetitorBaseStrategyV2 competitorBaseStrategy;

    @Autowired
    private CdmPrdTktDashboardFlyWeaknessStatisticsDfDao cdmPrdTktDashboardFlyWeaknessStatisticsDfDao;

    @Autowired
    private RemoteConfig remoteConfig;


    @Override
    public Future<OveaseaSubMetric> getBus105106107SubMetricCardData(TimeFilter timeFilter,
                                                                     OverseaMetricInfoBeanV2 metricInfoBean,
                                                                     String d,
                                                                     String metric,
                                                                     String subMetric) throws Exception {
        OveaseaSubMetric oveaseaSubMetric = new OveaseaSubMetric();
        oveaseaSubMetric.setSubMetric(subMetric);
        // 设置基础外层基础数据
        Bus105And106And107Helper.setMetricCardDrillDownParmaterV2(oveaseaSubMetric, subMetric, metricInfoBean, remoteConfig, timeFilter);

        // 查SR信息获取劣势数和劣势率
        OverseasRelatedSearchParamBean metricCardData = Bus105And106And107Helper.generateOverseaInfoSearch(timeFilter, null, metricInfoBean.getDomainName(),d, metric);
        OverseasCompetitionResponseBean metricCardResult = cdmPrdTktDashboardFlyWeaknessStatisticsDfDao.queryFlyWeaknessStatisticInfo(metricCardData);

        Bus105And106And107Helper.calculateMetricCardData(CompetitorTypeEnumEnum.FLY, metricCardResult, oveaseaSubMetric, remoteConfig, timeFilter, metric);
        return new AsyncResult<>(oveaseaSubMetric);
    }

    @Override
    public GetOverseaTrendLineDataV2ResponseType getBus105106107SubTrendLineData(GetOverseaTrendLineDataV2RequestType request,
                                                                                 String d,
                                                                                 List<String> timeList) throws Exception {
        return TrendDrillTypeEnum.TREND_LINE.getName().equals(request.getQueryType()) ?
                competitorBaseStrategy.getBus105106107SubTrendLineData(request, d, timeList) :
                competitorBaseStrategy.getBus105106107SubTrendLineDrillDownData(request, d, timeList);
    }

    @Override
    public GetOverseaDrillDownBaseInfoV2ResponseType getBus105106107SubDrillDownBaseInfo(GetOverseaDrillDownBaseInfoV2RequestType request,
                                                                                       String d,
                                                                                       OverseaMetricInfoBean metricInfoBean) throws Exception {
        return competitorBaseStrategy.getBus105106107SubDrillDownBaseInfo(request, d, metricInfoBean);
    }

    @Override
    public GetOverseaTableDataV2ResponseType getBus105106107SubTableData(GetOverseaTableDataV2RequestType request,
                                                                       String d,
                                                                       OverseaMetricInfoBean metricInfoBean) throws Exception {
        return competitorBaseStrategy.getBus105106107SubTableData(request, d, metricInfoBean);
    }

    @Override
    public String getSubMetricName() {
        return "tfly";
    }
}
