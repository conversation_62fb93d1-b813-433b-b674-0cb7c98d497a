include 'BaijiCommonTypes.bjsc'
include 'getMetricData.bjsc'
namespace java 'com.ctrip.soa._24922'


class GetOverseaMetricCardDataRequestType{
    //邮箱前缀
    string domainName;
    //时间选择器
    getMetricData.TimeFilter timeFilter;
}


class GetOverseaMetricCardDataResponseType{
    //指标列表具体数据
    list<getMetricData.MetricDetailInfo> metricDetailInfoList;
    BaijiCommonTypes.ResponseStatusType responseStatus;
}


class GetOverseaTrendLineDataRequestType{
    //指标枚举值
    string metric;
    //子指标枚举值
    string subMetric
    //邮箱前缀
    string domainName;
    //时间选择器
    getMetricData.TimeFilter timeFilter;
    //下钻选择器
    getMetricData.DrillDownFilter drillDownFilter;
    //来源
    string source;
    //查询类型 trendline drilldown
    string queryType;
}

class GetOverseaTrendLineDataResponseType{
    //趋势线数据
    list<getMetricData.TrendLineDetailInfo> trendLineDetailInfoList;
    BaijiCommonTypes.ResponseStatusType responseStatus; 
}


class GetOverseaDrillDownBaseInfoRequestType{
    //时间选择器
    getMetricData.TimeFilter timeFilter;
    //指标枚举值
    string metric;
    //子指标枚举值
    string subMetric
    //邮箱前缀
    string domainName;
    //是否为搜索模式
    bool needSearch;
    //搜索的field 
    string searchField;
    //搜索词
    string searchWord;
}

class GetOverseaDrillDownBaseInfoResponseType{
    //当前选择用户应该默认选中的下钻字段 
    string defaultChosenField;
    //所有可能的下钻字段以及其可下钻的值
    list<getMetricData.FieldDataItem> fieldDataItemList;
    BaijiCommonTypes.ResponseStatusType responseStatus;
}


class GetOverseaTableDataRequestType{
    //指标枚举值
    string metric;
    //子指标枚举值
    string subMetric
    //邮箱前缀
    string domainName;
    //时间选择器
    getMetricData.TimeFilter timeFilter;
    //下钻选择器
    getMetricData.DrillDownFilter drillDownFilter;
    //当前页数  
    int pageNo;
    //分页条数  对于首页以及详情页的气泡图可以传999
    int pageSize;
    //该请求的来源  firstpage(首页 后续app端也算是首页的一种) detailpage(详情页)
    string source;
    //bubble 气泡图   table真正的表格
    string queryType;
}

class GetOverseaTableDataResponseType{
    //总数据条数
    int totalNum;
    //环比类型  枚举值 7days  30days  lastmonth lastquarter 
    string momType;
    //表格数据
    list<getMetricData.TableDataItem> tableDataItemList;
    //表头数据(按照产品要求定义)
    list<string> tableHeaderList;
    //气泡图或者首页前端需要锚定的维度字段名称
    string showField;
    //气泡图或者首页前端需要锚定的维度字段名称id
    string showFieldId;
    BaijiCommonTypes.ResponseStatusType responseStatus;
}



@serviceName='BIBusinessDashboardService'
@serviceNamespace='http://soa.ctrip.com/_24922'
service BIBusinessDashboardService {
    //获取海外指标卡
    GetOverseaMetricCardDataResponseType getOverseaMetricCardData(GetOverseaMetricCardDataRequestType request);
    //获取海外趋势线数据
    GetOverseaTrendLineDataResponseType getOverseaTrendLineData(GetOverseaTrendLineDataRequestType request);
    //获取海外可下钻字段以及其值
    GetOverseaDrillDownBaseInfoResponseType getOverseaDrillDownBaseInfo(GetOverseaDrillDownBaseInfoRequestType request);
    //获取海外表格数据
    GetOverseaTableDataResponseType getOverseaTableData(GetOverseaTableDataRequestType request);
    
}