package com.ctrip.tour.business.dashboard.tktBusiness.select43556;

import java.util.stream.Stream;

public enum ErrorType {

    QUERYID_ERROR("The queryId is undefined or not approved!",10001),
    STRATERY_ERROR("The strategy is undefined  or not approved!",10002),
    PARAMTER_ERROR("The input paramter is error,please check it!",10003),
    CONFIG_ERROR("The config is error,please check it!",10004),
    DEFAULT_ERROR("inner error!",99999);

    private String message;
    private Integer code;

    ErrorType(String message, Integer code) {
        this.message = message;
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public static ErrorType toType(Integer code) {
        return Stream.of(ErrorType.values())
                .filter(c -> c.code.equals(code))
                .findAny()
                .orElse(DEFAULT_ERROR);
    }
}
