package com.ctrip.tour.business.dashboard.sightArchives.service;

import com.ctrip.soa._24922.GetFlowMetricRequestType;
import com.ctrip.soa._24922.GetFlowMetricResponseType;
import com.ctrip.soa._24922.GetFlowMetricTrendLineRequestType;
import com.ctrip.soa._24922.GetFlowMetricTrendLineResponseType;

//流量转化
public interface FlowService {

    //流量分析 - 流量来源分布（饼图）&流量转化漏斗
    GetFlowMetricResponseType getFlowMetric(GetFlowMetricRequestType getFlowMetricRequestType);

    //近一年淡旺季分析 - 转化率&UV趋势、淡旺季判断
    GetFlowMetricTrendLineResponseType getFlowMetricTrendLine(GetFlowMetricTrendLineRequestType getFlowMetricTrendLineRequestType);

}
