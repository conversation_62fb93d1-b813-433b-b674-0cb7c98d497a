package com.ctrip.tour.business.dashboard.grpBusiness.handler.metric.income;


import static com.ctrip.tour.business.dashboard.grpBusiness.common.GrpConstant.CALC_DATE_NAME;
import static com.ctrip.tour.business.dashboard.grpBusiness.exception.ExceptionEnum.EMP_CAN_NOT_FIND;

import java.sql.SQLException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import com.ctrip.soa._24922.CustTourRegionInfo;
import com.ctrip.soa._24922.GetGrpMetricDataRequestType;
import com.ctrip.tour.business.dashboard.grpBusiness.annotation.MetricData;
import com.ctrip.tour.business.dashboard.grpBusiness.common.GrpConstant;
import com.ctrip.tour.business.dashboard.grpBusiness.enums.CustTourDateTypeEnum;
import com.ctrip.tour.business.dashboard.grpBusiness.handler.metric.GrpBussinessAbstractMetricService;
import com.ctrip.tour.business.dashboard.grpBusiness.metrics.income.DiyCprIncomeService;
import com.ctrip.tour.business.dashboard.grpBusiness.service.CustEmpOrgInfoService;
import com.ctrip.tour.business.dashboard.grpBusiness.service.HrOrgEmpInfoService;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.ServiceException;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import lombok.extern.slf4j.Slf4j;

@Service(value = GrpConstant.DIY_INCOME)
@Slf4j
@MetricData(value = GrpConstant.DIY_INCOME, needYearOverYear = true, needMonthOveMonth = true)
public class DiyCrpIncomeQueryService extends GrpBussinessAbstractMetricService {

    private final static String TOUR_DEPART_DATE = "tour_depart_date";
    private final static String TOUR_CREATE_DATE = "tour_create_date";
    private final static String ORDER_DATE = "order_date";
    private final static String GRADE_REGION_NAME = "grade_region_name";

    @Autowired
    private RemoteConfig remoteConfig;


    @Autowired
    private DiyCprIncomeService diyCprIncomeService;
    @Autowired
    private CustEmpOrgInfoService custEmpOrgInfoService;

    public DiyCrpIncomeQueryService(ApplicationContext ac) {
        super(ac);
    }

    @Override
    protected Map<String, Object> request2Param(GetGrpMetricDataRequestType requestType, int bizMode) {
        Map<String, Object> param = Maps.newHashMap();
        String realDateName = convertDateTypeToDateName(requestType.getDateType());
        try {
            List<String> grdAreas = requestType.getAreas();
            if (CollectionUtils.isEmpty(requestType.getAreas())) {
                List<CustTourRegionInfo> custTourRegionList = custEmpOrgInfoService.getCustTourRegionList(requestType.getEmpCode());
                grdAreas = custTourRegionList.stream().flatMap(custTourRegionInfo -> custTourRegionInfo.getAreas().stream())
                        .collect(Collectors.toList());
            }

            param.put(GRADE_REGION_NAME, grdAreas);
           param.put(CALC_DATE_NAME, realDateName);
        } catch (Exception e) {
            log.warn("query empcode error" + requestType.getEmpCode(), e);
            throw new ServiceException(EMP_CAN_NOT_FIND.getCode(),EMP_CAN_NOT_FIND.getMsg());
        }

        param.put(realDateName, new String[]{requestType.getStartDate(), requestType.getEndDate()});
        return param;
    }

    private String convertDateTypeToDateName(String dateType) {
        switch (dateType) {
            case "PREORDER_DATE":
                return ORDER_DATE;
            case "TRIP_DATE":
                return TOUR_DEPART_DATE;
            default:
                return "";
        }
    }


    private String[] calcOverDate(String startDate, String endDate, int offset) {
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate hisStartDate = LocalDate.parse(startDate, dtf).minusDays(offset);
        LocalDate hisEndDate = LocalDate.parse(endDate, dtf).minusDays(offset);
        return new String[]{dtf.format(hisStartDate), dtf.format(hisEndDate)};
    }

    @Override
    protected Map<String, Object> replaceQueryDate4Week(Map<String, Object> param , String startDate, String endDate) {
        param.put(String.valueOf(param.get(CALC_DATE_NAME)), calcOverDate(startDate, endDate, 7));
        return param;
    }

    @Override
    protected Map<String, Object> replaceQueryDate4Month(Map<String, Object> param, String startDate, String endDate) {
        param.put(String.valueOf(param.get(CALC_DATE_NAME)), calcOverDate(startDate, endDate, 30));
        return param;
    }

    @Override
    protected Map<String, Object> replaceQueryDate4Year(Map<String, Object> param, String startDate, String endDate) {
        param.put(String.valueOf(param.get(CALC_DATE_NAME)), calcOverYearDate(startDate, endDate));
        return param;
    }


    @Override
    public Map<String, Object> queryMetricTrendLine(Map<String, Object> param, GetGrpMetricDataRequestType requestType) {

        String aggregationGranularity = requestType.getAggregationGranularity();

        Map<String, Object> result = diyCprIncomeService.queryTrendLineData(diyCprIncomeService, param,
                Lists.newArrayList(String.valueOf(param.get(CALC_DATE_NAME))), aggregationGranularity);

        return result;
    }

    @Override
    public List<Map<String, Object>> queryMetricDillDown(Map<String, Object> param, GetGrpMetricDataRequestType requestType) {

        String drillDownDim = requestType.getDrillDownDim();

        List<Map<String, Object>> result = diyCprIncomeService.queryDillDownData(diyCprIncomeService, param,
                Lists.newArrayList(drillDownDim));

        return result;
    }

    @Override
    public List<Map<String, Object>> queryMetricCardData(Map<String, Object> param, GetGrpMetricDataRequestType requestType) {
        List<Map<String, Object>> result = diyCprIncomeService.queryCardData(diyCprIncomeService, param,
                null);
        return result;
    }


    private List<String> queryAllTargetEmpCodes(String empCode) {
        return null;
    }
}
