package com.ctrip.tour.business.dashboard.tktBusiness.strategy.overseaImplV2;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ExamineConfigBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBeanV2;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.DataUpdateBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardOverseaExamineeConfigDao;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardOverseaExamineeConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.MultiPeriodMappingHelper;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.OverseaMetricCalStrategyV2;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.subMetricImplV2.OverseaSubMetricCalStategyBizImplV2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Future;

@Component
public class Bus103MetricStrategyV2 implements OverseaMetricCalStrategyV2 {

    @Autowired
    private OverseaSubMetricCalStategyBizImplV2 overseaSubMetricCalStategyBiz;

    @Autowired
    private BusinessDashboardOverseaExamineeConfigDao examineeConfigDao;

    @Autowired
    private DataUpdateBiz dataUpdateBiz;


    @Override
    public Future<OveaseaMetric> getOverseaSingleMetricCardData(TimeFilter timeFilter,
                                                                OverseaMetricInfoBeanV2 metricInfoBean,
                                                                String d,
                                                                AvailableSubMetric availableSubMetric,
                                                                GetOverseaMetricCardDataV2RequestType request) throws Exception {
        d = dataUpdateBiz.getUpdateTime();
        List<String> subMetricList = availableSubMetric.getSubMetricList();
        List<Future<OveaseaSubMetric>> futureList = new ArrayList<>();
        for (String subMetric : subMetricList) {
            futureList.add(overseaSubMetricCalStategyBiz.getBus103SubMetricCardData(timeFilter, metricInfoBean, d, "103", subMetric));
        }
        OveaseaMetric overSeaMetrics = new OveaseaMetric();
        overSeaMetrics.setMetric("103");
        List<OveaseaSubMetric> overSeaMetricsList = new ArrayList<>();
        for (Future<OveaseaSubMetric> futureResult : futureList) {
            overSeaMetricsList.add(futureResult.get());
        }
        overSeaMetrics.setSubMetricList(overSeaMetricsList);
        return new AsyncResult<>(overSeaMetrics);
    }

    @Override
    public GetOverseaTrendLineDataV2ResponseType getOverseaSingleTrendlineData(GetOverseaTrendLineDataV2RequestType request,
                                                                             String d) throws Exception {
        d = dataUpdateBiz.getUpdateTime();
        String domainName = request.getDomainName();
        String metric = request.getMetric();
        List<BusinessDashboardOverseaExamineeConfig> examineeConfigList = examineeConfigDao.queryMetricAllConfig(domainName, d, metric);
        List<ExamineConfigBean> examineConfigBeanList = MultiPeriodMappingHelper.getMultiPeriodConfigList(request.getTimeFilter(), d, null, examineeConfigList);

        GetOverseaTrendLineDataV2ResponseType response = overseaSubMetricCalStategyBiz.getBus103SubTrendlineData(request, d, examineConfigBeanList);
        return response;

    }

    @Override
    public GetOverseaDrillDownBaseInfoV2ResponseType getOverseaSingleDrillDownBaseInfo(GetOverseaDrillDownBaseInfoV2RequestType request,
                                                                                     String d,
                                                                                       OverseaMetricInfoBean metricInfoBean) throws Exception {
//        d="2025-03-31";
        return overseaSubMetricCalStategyBiz.getBus103SubDrillDownBaseInfo(request, d, metricInfoBean);
    }

    @Override
    public GetOverseaTableDataV2ResponseType getOverseaSingleTableData(GetOverseaTableDataV2RequestType request,
                                                                     String d,
                                                                       OverseaMetricInfoBean metricInfoBean) throws Exception {
//        d="2025-03-31";
        return overseaSubMetricCalStategyBiz.getBus103SubTableData(request, d, metricInfoBean);
    }

    @Override
    public String getMetricName() {
        return "103";
    }
}
