package com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;

@Entity
@Getter
@Setter
public class CdmPrdTktDashboardWeaknessStatisticsBO {
    //邮箱前缀
    String domainName;
    //考核层级
    String examineLevel;
    //考核类型，105-景点 106-票种 107-商品力
    Integer examineType;
    //业务大区ID
    Long businessRegionId;
    //业务大区
    String businessRegionName;
    String businessRegionNameEn;
    //业务子区域ID
    Long businessSubRegionId;
    //业务子区域
    String businessSubRegionName;
    String businessSubRegionNameEn;
    //国家ID
    Long countryId;
    //国家名称
    String countryName;
    String countryNameEn;
    //统计维度ID
    Integer statisticsDimId;
    //统计维度名称
    String statisticsDimName;
    //核心-序号小于等于5的标识符
    Integer isCcoefficientIdentifier;
    //聚焦-序号小于等于5的标识符
    Integer isFcoefficientIdentifier;
    //大盘-序号小于等于5的标识符
    Integer isTcoefficientIdentifier;
    //核心-劣势数量
    Double cwNum;
    //	核心-总数量
    Double cNum;
    //核心-劣势率
    Double cwRate;
    //聚焦-劣势数量
    Double fwNum;
    //聚焦-总数量
    Double fNum;
    //聚焦-劣势率
    Double fwRate;
    //大盘-劣势数量
    Double twNum;
    //大盘-总数量
    Double tNum;
    //大盘-劣势率
    Double twRate;
    //考核年份
    String examineYear;
    //考核季度 枚举值：Q1 Q2 Q3 Q4
    String examineQuarter;
    //考核月份
    String examineMonth;
    //考核日期
    String examineDate;
}
