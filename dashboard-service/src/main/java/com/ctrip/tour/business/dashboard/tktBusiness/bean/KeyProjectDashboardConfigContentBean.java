package com.ctrip.tour.business.dashboard.tktBusiness.bean;

import com.ctrip.soa._24922.KeyProjectDashboardConfig;
import org.apache.commons.collections.MapUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class KeyProjectDashboardConfigContentBean {

    String title;

    String titleSharkKey;

    String url;

    Integer displayScope;

    List<Integer> filterColumns;

    Integer boardHeight;

    Boolean needDefaultFilter;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitleSharkKey() {
        return titleSharkKey;
    }

    public void setTitleSharkKey(String titleSharkKey) {
        this.titleSharkKey = titleSharkKey;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Integer getDisplayScope() {
        return displayScope;
    }

    public void setDisplayScope(Integer displayScope) {
        this.displayScope = displayScope;
    }

    public List<Integer> getFilterColumns() {
        return filterColumns;
    }

    public void setFilterColumns(List<Integer> filterColumns) {
        this.filterColumns = filterColumns;
    }

    public Integer getBoardHeight() {
        return boardHeight;
    }

    public void setBoardHeight(Integer boardHeight) {
        this.boardHeight = boardHeight;
    }

    public Boolean getNeedDefaultFilter() {
        return needDefaultFilter;
    }

    public void setNeedDefaultFilter(Boolean needDefaultFilter) {
        this.needDefaultFilter = needDefaultFilter;
    }



    private static Map<Integer,String> displayScopeMap = new HashMap<>();
    static {
        displayScopeMap.put(0,"domestic");
        displayScopeMap.put(1,"oversea");
        displayScopeMap.put(2,"all");
    }

    public static List<KeyProjectDashboardConfig> convertToResponseDto(List<KeyProjectDashboardConfigContentBean> configContentBeanList){
        List<KeyProjectDashboardConfig> keyProjectDashboardConfigList = new ArrayList<>();

        for(KeyProjectDashboardConfigContentBean configContentBean : configContentBeanList){
            KeyProjectDashboardConfig keyProjectDashboardConfig = new KeyProjectDashboardConfig();
            keyProjectDashboardConfig.setTitle(configContentBean.getTitle());
            keyProjectDashboardConfig.setTitleSharkKey(configContentBean.getTitleSharkKey());
            keyProjectDashboardConfig.setUrl(configContentBean.getUrl());
            keyProjectDashboardConfig.setDisplayScope(displayScopeMap.getOrDefault(configContentBean.getDisplayScope(),""));
            keyProjectDashboardConfig.setFilterColumns(configContentBean.getFilterColumns());
            keyProjectDashboardConfig.setBoardHeight(configContentBean.getBoardHeight());
            keyProjectDashboardConfig.setBoardHeight(configContentBean.getBoardHeight());
            keyProjectDashboardConfig.setNeedDefaultFilter(configContentBean.getNeedDefaultFilter() ? 1 : 0);

            keyProjectDashboardConfigList.add(keyProjectDashboardConfig);
        }

        return keyProjectDashboardConfigList;
    }


}
