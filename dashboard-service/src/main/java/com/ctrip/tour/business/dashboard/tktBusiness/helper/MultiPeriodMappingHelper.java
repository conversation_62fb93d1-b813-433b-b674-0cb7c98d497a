package com.ctrip.tour.business.dashboard.tktBusiness.helper;

import com.ctrip.soa._24922.TimeFilter;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ExamineConfigBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBeanV2;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoWithMetricBean;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardExamineeConfigV2;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardOverseaExamineeConfig;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class MultiPeriodMappingHelper {


    /**
     * 该函数按照  year month/quarter 配置的格式返回数据  且只返回需要获取数据的年  月/季度配置
     * 国内和海外一次只能传入一个
     *
     * @param timeFilter          时间选择器
     * @param metricAllConfigList 某个国内指标所有考核周期的配置数据
     * @param overseaMetricAllConfigList 某个海外指标所有考核周期的配置数据
     * @return
     */
    public static List<ExamineConfigBean> getMultiPeriodConfigList(TimeFilter timeFilter,
                                                                   String d,
                                                                   List<BusinessDashboardExamineeConfigV2> metricAllConfigList,
                                                                   List<BusinessDashboardOverseaExamineeConfig> overseaMetricAllConfigList) throws ParseException {
        String type = GeneralUtil.isNotEmpty(metricAllConfigList) ? "domestic" : "oversea";
        Table<String, String, BusinessDashboardExamineeConfigV2> configMap = HashBasedTable.create();
        Table<String, String, BusinessDashboardOverseaExamineeConfig> overseaConfigMap = HashBasedTable.create();
        if (GeneralUtil.isNotEmpty(metricAllConfigList)) {
            for (BusinessDashboardExamineeConfigV2 bean : metricAllConfigList) {
                configMap.put(bean.getYear(), bean.getQuarter(), bean);
            }
        }
        if (GeneralUtil.isNotEmpty(overseaMetricAllConfigList)) {
            for (BusinessDashboardOverseaExamineeConfig bean : overseaMetricAllConfigList) {
                overseaConfigMap.put(bean.getYear(), bean.getQuarter(), bean);
            }
        }
        //最新的数据时间
        String lastDay = DateUtil.getDayOfInterval(d, -1);
        String dateType = timeFilter.getDateType();
        List<ExamineConfigBean> examineConfigBeanList = new ArrayList<>();
        List<String> timeList = DateUtil.getAllSelectedTime(timeFilter, type);
//        List<String> popTimeList = DateUtil.getPopTime(dateType, timeList, "2019");
        for (int i = 0; i < timeList.size(); i++) {
            String time = timeList.get(i);
            String[] array = time.split("-");
            String year = array[0];
            String monthOrQuarter = array[1];
            String lastYear = DateUtil.getLastYear(year);
            String lastYearTime = lastYear + "-" + monthOrQuarter;
            String _2019YearTime = "2019" + "-" + monthOrQuarter;
//            String popYearTime = popTimeList.get(i);
            Map<String, String> timeMap = new HashMap<>();
            timeMap.put("currentTime", time);
            timeMap.put("lastyearTime", lastYearTime);
            timeMap.put("2019Time", _2019YearTime);

            Map<String, String> limitTimeMap = new HashMap<>();
            Boolean isLastestPeriod = false;
            if ("month".equals(dateType)) {
                isLastestPeriod = DateUtil.isCurrentMonthV2(lastDay, year, monthOrQuarter);
                ExamineConfigBean bean = new ExamineConfigBean(year, monthOrQuarter, null, timeMap,
                        isLastestPeriod, limitTimeMap, configMap.get(year, DateUtil.getQuarterOfMonth(monthOrQuarter)), overseaConfigMap.get(year, DateUtil.getQuarterOfMonth(monthOrQuarter)), dateType);
                examineConfigBeanList.add(bean);
            } else {
                isLastestPeriod = DateUtil.isCurrentQuarterV2(lastDay, year, monthOrQuarter);
                ExamineConfigBean bean = new ExamineConfigBean(year, null, monthOrQuarter, timeMap,
                        isLastestPeriod, limitTimeMap, configMap.get(year, monthOrQuarter), overseaConfigMap.get(year, monthOrQuarter), dateType);
                examineConfigBeanList.add(bean);
            }

            if (isLastestPeriod) {
                limitTimeMap.put("time", lastDay);
                limitTimeMap.put("lastYearTime", lastYearTime.substring(0, 4) + "-" + lastDay.substring(5));
                limitTimeMap.put("popYearTime", "2019" + "-" + lastDay.substring(5));
//                limitTimeMap.put("popYearTime", popYearTime.substring(0, 4) + "-" + lastDay.substring(5));
            }


        }
        if ("domestic".equals(type)) {
            return examineConfigBeanList.stream()
                    .filter(i -> GeneralUtil.isNotEmpty(i.getBusinessDashboardExamineeConfigV2()))
                    .collect(Collectors.toList());
        } else {
            return examineConfigBeanList.stream()
                    .filter(i -> GeneralUtil.isNotEmpty(i.getBusinessDashboardOverseaExamineeConfig()))
                    .collect(Collectors.toList());
        }

    }
}
