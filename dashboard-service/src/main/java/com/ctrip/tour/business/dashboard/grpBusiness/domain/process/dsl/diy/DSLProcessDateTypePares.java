package com.ctrip.tour.business.dashboard.grpBusiness.domain.process.dsl.diy;

import com.ctrip.soa._24922.DSLRequestType;
import com.ctrip.soa._24922.EnumOperators;
import com.ctrip.soa._24922.WhereCondition;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.AbstractPreDSLProcess;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.dsl.DSLUtils;

import java.util.ArrayList;
import java.util.Collections;


/**
 * 日期类型
 */
public class DSLProcessDateTypePares extends AbstractPreDSLProcess {
    private String dateType;

    public static AbstractPreDSLProcess getInstance(String dateType) {
        DSLProcessDateTypePares dslProcessLimitSet = new DSLProcessDateTypePares();
        dslProcessLimitSet.dateType = dateType;
        return dslProcessLimitSet;
    }

    @Override
    public DSLRequestType process(DSLRequestType dsl, EarlyReturn earlyReturn) {
        // 获取业务线
        WhereCondition businessTypeCondition = DSLUtils.getWhereConditionByFilterName(dsl, "business_line");
        if (businessTypeCondition == null || businessTypeCondition.getFilterValues().isEmpty()) {
            earlyReturn.setStatus(-1);
            return dsl;
        }
        int businessType = Integer.parseInt(businessTypeCondition.getFilterValues().get(0));
        // 非定制游直接返回
        if (businessType != 230) {
            return dsl;
        }
        // 获取业务线
         if (dateType == null || dateType.isEmpty()) {
             dateType = "PREORDER_DATE";
//             earlyReturn.setStatus(-1);
//             return dsl;
         }
        // 初始化
        if (dsl.getWhereCondition() == null) {
            dsl.setWhereCondition(new WhereCondition());
        }
        if (dsl.getWhereCondition().getSubWhereConditions() == null) {
            dsl.getWhereCondition().setSubWhereConditions(new ArrayList<>());
        }
        // PREORDER_DATE: 预定
        // TRIP_DATE: 出行
        // RETURN_DATE: 返程
        switch (dateType) {
            case "PREORDER_DATE":
                break;
            case "TRIP_DATE":
                dsl.getIndicators().remove("completed_orders");
                dsl.getIndicators().remove("completed_orders_rate");
                dsl.getIndicators().remove("cpr_completed_orders_rate");
                break;
            case "RETURN_DATE":
                break;
        }

        WhereCondition dateTypeCondition = new WhereCondition();
        dateTypeCondition.setFilterName("date_type");
        dateTypeCondition.setOperators(EnumOperators.IN);
        dateTypeCondition.setFilterValues(Collections.singletonList(dateType));
        dsl.getWhereCondition().getSubWhereConditions().add(dateTypeCondition);
        return dsl;
    }
}
