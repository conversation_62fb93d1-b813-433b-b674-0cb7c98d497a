package com.ctrip.tour.business.dashboard.grpBusiness.domain.process.data;

import com.ctrip.soa._24922.DSLRequestType;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.AbstractAfterDataProcess;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.model.ResultData;
import com.ctrip.tour.business.dashboard.grpBusiness.service.DepTreeCache;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

public class AfterDataProcessMetaSort extends AbstractAfterDataProcess {
    private static volatile AfterDataProcessMetaSort instance;

    public static AbstractAfterDataProcess getInstance() {
        if (instance == null) { // 第一次检查
            synchronized (AfterDataProcessMetaSort.class) { // 加锁
                if (instance == null) { // 第二次检查
                    instance = new AfterDataProcessMetaSort();
                }
            }
        }
        return instance;
    }

    @Override
    public ResultData process(DSLRequestType dslRequestType, ResultData data) {
        ArrayList<String> groupBys = new ArrayList<>(dslRequestType.getGroupBy());
        groupBys.addAll(dslRequestType.getIndicators());
        // 去重复
        List<String> sortedIndicators = groupBys.stream().distinct().collect(Collectors.toList());
        // 排序
        data.getMeta().sort(Comparator.comparingInt(o -> sortedIndicators.indexOf(o.getIndicatorName())));
        return data;
    }
}
