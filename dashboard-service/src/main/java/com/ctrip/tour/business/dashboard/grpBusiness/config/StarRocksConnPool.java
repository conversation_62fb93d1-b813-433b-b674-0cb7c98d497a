package com.ctrip.tour.business.dashboard.grpBusiness.config;

import com.alibaba.druid.pool.DruidDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.Configuration;
import qunar.tc.qconfig.client.MapConfig;
import qunar.tc.qconfig.client.spring.QMapConfig;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
public class StarRocksConnPool {

    public static Map<String, String> configMap = new HashMap<>();

    private static final Map<String, DataSource> dataSourceMap = new HashMap<>();

    static {
        MapConfig mapConfig = MapConfig.get("config.properties");
        configMap = new HashMap<>(mapConfig.asMap());
        mapConfig.addListener(new Configuration.ConfigListener<Map<String, String>>() {
            @Override //配置发生变更的时候会触发
            public void onLoad(Map<String, String> conf) {
                for (Map.Entry<String, String> entry : conf.entrySet()) {
                    configMap.put(entry.getKey(), entry.getValue());
                    log.info(entry.getKey() + "==>" + entry.getValue());
                }
            }
        });
    }

     static {
        DruidDataSource starrocksDataSource = new DruidDataSource();
        starrocksDataSource.setUsername(configMap.get("starrocks.xy.user"));
        starrocksDataSource.setPassword(configMap.get("starrocks.xy.password"));
        starrocksDataSource.setUrl(configMap.get("starrocks.xy.url"));
        starrocksDataSource.setDriverClassName(configMap.get("starrocks.xy.className"));
        starrocksDataSource.setInitialSize(Integer.parseInt(configMap.getOrDefault("starrocks.xy.initialSize","8")));
        starrocksDataSource.setMinIdle(Integer.parseInt(configMap.getOrDefault("starrocks.xy.minIdle","8")));
        starrocksDataSource.setMaxActive(Integer.parseInt(configMap.getOrDefault("starrocks.xy.maxActive","32")));
        starrocksDataSource.setMaxWait(Long.parseLong(configMap.getOrDefault("starrocks.xy.maxWait","5000")));
        starrocksDataSource.setRemoveAbandoned(Boolean.parseBoolean(configMap.getOrDefault("starrocks.xy.removeAbandoned","true")));
        starrocksDataSource.setRemoveAbandonedTimeout(Integer.parseInt(configMap.getOrDefault("starrocks.xy.removeAbandonedTimeout","120")));
        dataSourceMap.put("starrocks.xy",starrocksDataSource);

    }

    public static Connection getConnection(String engine) throws SQLException {
        DataSource dataSource = dataSourceMap.get(engine);
        return dataSource.getConnection();
    }

}
