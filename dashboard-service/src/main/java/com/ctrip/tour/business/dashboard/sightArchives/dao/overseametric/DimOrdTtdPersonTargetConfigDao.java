package com.ctrip.tour.business.dashboard.sightArchives.dao.overseametric;

import com.ctrip.soa._27181.PreparedParameterBean;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.CdmOrdTtdOverseasPerformanceIndexBO;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.DimOrdTtdPersonTargetBO;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.bean.DimOrdTtdPersonTargetParamBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseasRelatedSearchParamBean;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.TktStarRocksDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.Types;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.Objects;

@Repository
@Slf4j
public class DimOrdTtdPersonTargetConfigDao {

    @Autowired
    private TktStarRocksDao tktStarRocksDao;

    /**
     * 获取海外人维度目标数据
     *
     * @return
     */
    public Double queryOverseaDestinationTargetInfo(OverseasRelatedSearchParamBean searchParamBean) {
        StringBuilder sql = new StringBuilder("SELECT");
        switch (searchParamBean.getQuarter()) {
            case "Q1":
                sql.append("  SUM(COALESCE(CAST(q1 AS DOUBLE), 0)) as personTarget");
                break;
            case "Q2":
                sql.append("  SUM(COALESCE(CAST(q2 AS DOUBLE), 0)) as personTarget");
                break;
            case "Q3":
                sql.append("  SUM(COALESCE(CAST(q3 AS DOUBLE), 0)) as personTarget");
                break;
            case "Q4":
                sql.append("  SUM(COALESCE(CAST(q4 AS DOUBLE), 0)) as personTarget");
                break;
            default:
                return null;
        }
        sql.append(" FROM ods_data_upload_new_performance_personal_latest WHERE 1=1");
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendYear(parameters, sql, searchParamBean.getYear());
        appendBuTypes(parameters, sql, searchParamBean.getBuTypeNames());
        appendExamineMetricType(parameters, sql, searchParamBean.getExamineMetricType());
        appendCTName(parameters, sql, searchParamBean.getCt());
        appendDomainName(parameters, sql, searchParamBean.getDomainName());
        appendD(parameters, sql, searchParamBean.getD());
        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            log.warn("queryOverseaDestinationTargetInfo error", e);
        }
        if (!CollectionUtils.isEmpty(result)) {
            return result.stream()
                    .map(bean -> bean.get("personTarget"))
                    .filter(Objects::nonNull)
                    .map(value -> Double.parseDouble(value.toString()))
                    .findFirst()
                    .orElse(null);
        }
        return null;
    }

    //拼时间
    private void appendD(List<PreparedParameterBean> parameters, StringBuilder sql, String d) {
        if (d != null) {
            sql.append(" and d=?");
            parameters.add(new PreparedParameterBean(d, Types.VARCHAR));
        }
    }

    //拼业务线
    private void appendBuTypes(List<PreparedParameterBean> parameters, StringBuilder sql, List<String> buTypeNames) {
        if (CollectionUtils.isNotEmpty(buTypeNames)) {
            sql.append(" and bu_type in (");
            for (int i = 0; i < buTypeNames.size(); i++) {
                if (i == 0) {
                    sql.append("?");
                } else {
                    sql.append(",?");
                }
                parameters.add(new PreparedParameterBean(buTypeNames.get(i), Types.VARCHAR));
            }
            sql.append(")");
        }
    }

    //拼人维度
    private void appendDomainName(List<PreparedParameterBean> parameters, StringBuilder sql, String domainName) {
        if (domainName != null) {
            sql.append(" and domain_name = ?");
            parameters.add(new PreparedParameterBean(domainName, Types.VARCHAR));
        }
    }

    //拼年份
    private void appendYear(List<PreparedParameterBean> parameters, StringBuilder sql, String year) {
        if (year != null) {
            sql.append(" and examine_year = ?");
            parameters.add(new PreparedParameterBean(year, Types.VARCHAR));
        }
    }

    //拼季度
    public void appendQuarter(List<PreparedParameterBean> parameters, StringBuilder sql, String quarter) {
        if (quarter != null) {
            sql.append(" and use_quarter=?");
            parameters.add(new PreparedParameterBean(quarter, Types.VARCHAR));
        }
    }

    //拼ct站点
    private void appendCTName(List<PreparedParameterBean> parameters, StringBuilder sql, String ctName) {
        if (ctName != null) {
            if ("c".equals(ctName)) {
                ctName = "C目的地";//NOSONAR
                sql.append(" and ct = ?");
                parameters.add(new PreparedParameterBean(ctName, Types.VARCHAR));
            } else if ("t".equals(ctName)) {
                ctName = "T目的地";//NOSONAR
                sql.append(" and ct = ?");
                parameters.add(new PreparedParameterBean(ctName, Types.VARCHAR));
            }
        }
    }


    //拼考核指标类型
    private void appendExamineMetricType(List<PreparedParameterBean> parameters, StringBuilder sql, String examineMetricType) {
        if (examineMetricType != null) {
            sql.append(" and examine_metric_type = ?");
            parameters.add(new PreparedParameterBean(examineMetricType, Types.VARCHAR));
        }
    }

    private void appendSql(StringBuilder sql, String sqlAppend, List<PreparedParameterBean> parameters, List<String> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            sql.append(sqlAppend);
            for (int i = 0; i < list.size(); i++) {
                if (i == 0) {
                    sql.append("?");
                } else {
                    sql.append(",?");
                }
                parameters.add(new PreparedParameterBean(list.get(i), Types.VARCHAR));
            }
            sql.append(")");
        }
    }
    public List<DimOrdTtdPersonTargetBO> queryDimOrdTtdPersonTargetBOs(DimOrdTtdPersonTargetParamBean param) {
        StringBuilder sql = new StringBuilder("select * from ods_data_upload_new_performance_personal_latest " +
                "where d=? and  examine_year=? and examine_metric_type=? " );

        List<PreparedParameterBean> parameters = new java.util.ArrayList<>();
        parameters.add(new PreparedParameterBean(param.getD(), Types.VARCHAR));
        parameters.add(new PreparedParameterBean(param.getExamineYear(), Types.VARCHAR));
        parameters.add(new PreparedParameterBean(param.getExamineMetricType(), Types.VARCHAR));
        appendSql(sql," and bu_type in ( ",parameters,param.getBuType());
        appendSql(sql," and ct in ( ",parameters,param.getCT());
        appendSql(sql," and domain_name in ( ",parameters,param.getDomainName());

        List<Map<String, Object>> result = new ArrayList<>();
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            log.warn("queryOverseaPersonInfo error", e);
        }
        List<DimOrdTtdPersonTargetBO> personTargetList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(result)) {
            personTargetList = result.stream()
                    .map(bean -> {
                        DimOrdTtdPersonTargetBO personTarget = new DimOrdTtdPersonTargetBO();
                        personTarget.setDomainName((String) bean.get("domain_name"));
                        personTarget.setExamineMetricType((String) bean.get("examine_metric_type"));
                        personTarget.setQ1((String)bean.get("q1"));
                        personTarget.setQ2((String)bean.get("q2"));
                        personTarget.setQ3((String)bean.get("q3"));
                        personTarget.setQ4((String)bean.get("q4"));
                        return personTarget;
                    })
                    .collect(Collectors.toList());
        }
        return personTargetList;
    }
}
