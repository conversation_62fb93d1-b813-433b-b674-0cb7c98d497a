include 'BaijiCommonTypes.bjsc'
namespace java 'com.ctrip.soa._24922'

class GetPreviewInfoRequestType{
    string vbkBuType;
    string mixProductId;
}

class GetPreviewInfoResponseType{
    BaijiCommonTypes.ResponseStatusType responseStatus;
    /* 响应基本信息 */
    GetResponseBaseDataType responseBaseData;
    /* 预览信息 */
    PreviewInfo previewInfo;
}
class GetResponseBaseDataType {
    /* 是否成功，部分失败返回false */
    bool success;
    /* 错误编码 */
    string errorCode;
    /* 业务错误信息 */
    string message;
}

class PreviewInfo {
    /* 是否预览按钮置灰 true:置灰 false: 不置灰 */
    bool grayPreviewButton;
    /* 提示话术 */
    list<string> tips;

    /* 站点（翻译中台可选的翻译件） */
    list<LocaleInfo> localeList;

    /* 拼接链接 */
    // ctrip预览链接App
    string ctripPreviewUrlApp;
    // ctrip 预览链接H5
    string ctripPreviewUrlH5;
    // ctrip 预览链接Online
    string ctripPreviewUrlOnline;

    // trip预览链接 App
    string tripPreviewUrlApp;
    // trip预览链接H5
    string tripPreviewUrlH5;
    // trip 预览链接Online
    string tripPreviewUrlOnline;

    /* ctrip门票预览-APP */
    string ctripTicketPreviewUrlApp;
    /* ctrip门票预览-H5 */
    string ctripTicketPreviewUrlH5;
    /* 套餐信息 */
    list<PackageGenerateInfo> packageInfoList;
    /* 是否为门票产品: true-是,false-false */
    bool ticketProduct;
    /* 模板-0-标准版，1-简化版 2-对接专用*/
    int template;

}

class PackageGenerateInfo{
    /*套餐ID */
    long packageId;
    /* 套餐名称 */
    string packageName;
}

class LocaleInfo {
    /* 24: 国际化online, 25:国际化无线APP, 26:国际化无线H5 */
    int platformId;
    /* 翻译中台可选的翻译件 */
    list<string> localeList;
}


@serviceName='BIBusinessDashboardService'
@serviceNamespace='http://soa.ctrip.com/_24922'
service BIBusinessDashboardService {
       /* 获取预览信息 */
    GetPreviewInfoResponseType getPreviewInfo(GetPreviewInfoRequestType request);
}