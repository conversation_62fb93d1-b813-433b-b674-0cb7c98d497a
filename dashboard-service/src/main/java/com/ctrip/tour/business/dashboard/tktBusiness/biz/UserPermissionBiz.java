package com.ctrip.tour.business.dashboard.tktBusiness.biz;

import com.ctrip.soa._24922.*;

/**
 * <AUTHOR>
 * @date 2022/7/25
 */
public interface UserPermissionBiz {

    CheckUserPermissionResponseType checkUserPermission(CheckUserPermissionRequestType checkUserPermissionRequestType) throws Exception;

    CheckAdminPermissionResponseType checkAdminPermission(CheckAdminPermissionRequestType checkAdminPermissionRequestType) throws Exception;

    CheckTaskFlowPermissionResponseType checkTaskFlowPermission(CheckTaskFlowPermissionRequestType checkTaskFlowPermissionRequestType, String empCode) throws Exception;

    Boolean isInDepartment41999(CheckUserPermissionRequestType requestType) throws Exception;

    Boolean isOverseaboard(String domainName) throws Exception;
}
