package com.ctrip.tour.business.dashboard.qmq

import com.ctrip.tour.business.dashboard.tktBusiness.bean.ContractEventSceneBean
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ProductInfoRecificationBean
import com.ctrip.tour.business.dashboard.tktBusiness.biz.PushMessageToTaskBiz
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.KmsConfiguration
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardEmployeeInfoDao
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardNoticeInfoDao
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardEmployeeInfo
import com.ctrip.tour.business.dashboard.tktBusiness.qmq.QmqContractEventConsumer
import com.ctrip.tour.business.dashboard.utils.MapperUtil
import com.ctrip.tour.ttd.product.soa.GetProductInfoResponseType
import com.ctrip.tour.ttd.product.soa.TtdProductBasicServiceClient
import spock.lang.Specification

class QmqContractEventConsumerTest extends Specification {
    // mock
    def employeeInfoDao = Mock(BusinessDashboardEmployeeInfoDao)
    def noticeInfoDao = Mock(BusinessDashboardNoticeInfoDao)
    def remoteConfig = Mock(RemoteConfig)
    def productClient = Mock(TtdProductBasicServiceClient)
    def messageToTaskBiz = Mock(PushMessageToTaskBiz)
    def kmsConfiguration = Mock(KmsConfiguration)
    def consumer = new QmqContractEventConsumer(employeeInfoDao: employeeInfoDao, noticeInfoDao: noticeInfoDao, remoteConfig: remoteConfig, kmsConfiguration: kmsConfiguration, productClient: productClient, messageToTaskBiz: messageToTaskBiz)

    // call
//    def employeeInfoDao = new BusinessDashboardEmployeeInfoDao()
//    def noticeInfoDao = new BusinessDashboardNoticeInfoDao()
//    def remoteConfig = Mock(RemoteConfig)
//    def productClient = TtdProductBasicServiceClient.getInstance()
//    def pushClient = TourRightsServiceClient.getInstance()
//    def messageToTaskBiz = new PushMessageToTaskBizImpl(pushClient: pushClient, remoteConfig: remoteConfig)
//    def kmsConfiguration = Mock(KmsConfiguration)
//    def consumer = new QmqContractEventConsumer(employeeInfoDao: employeeInfoDao, noticeInfoDao: noticeInfoDao, remoteConfig: remoteConfig, kmsConfiguration: kmsConfiguration, productClient: productClient, messageToTaskBiz: messageToTaskBiz)


    /**
     * 覆盖：handleMessage | preprocessingOfGetEmployee
     * @return
     */
    def "test handleMessage"() {
        given: "mock"
        remoteConfig.getConfigValue("isPushSceneMessage") >> true
        remoteConfig.getConfigValue("scheduleConfig") >> "[{\"sceneType\":\"PRODUCT_INFO_RECTIFICATION\",\"isSplitScene\":true,\"className\":\"com.ctrip.tour.business.dashboard.bean.ProductInfoRecificationBean\",\"splitType\":1,\"splitSceneMap\":{\"supplier\":{\"sceneType\":\"PRODUCT_INFO_RECTIFICATION_SUPPLIER_DELIVERY\",\"className\":\"com.ctrip.tour.business.dashboard.bean.ProductInfoRecificationBean\",\"messageType\":3,\"title\":\"供应商产品信息整改\",\"taskType\":\"TASK_SUPPLIER_REWARD_PUNISHMENT_01\",\"taskName\":\"商品质量信息整改\",\"eventType\":\"EVENT_SUPPLIER_REWARD_PUNISHMENT_01\",\"serviceNumInfo\":{\"contentType\":1,\"length\":4000,\"templateStr\":\"您有%s家供应商产品信息需整改，可点击查看详情\",\"isplace\":true,\"templateList\":[\"count\"]},\"detailInfo\":{\"contentType\":2,\"length\":4000,\"templateStr\":\"您名下供应商有产品将被处理，如有异议请联系供应商及时申诉\",\"isplace\":false,\"headers\":{\"违规原因\":[\"problemType\"],\"产品\":[\"productId\",\"productName\"],\"最晚整改完成时间\":[\"rectificationDeadline\"],\"申诉链接\":[\"problemUrl\"]}}},\"business\":{\"messageType\":2,\"sceneType\":\"PRODUCT_INFO_RECTIFICATION_BUSINESS_DELIVERY\",\"className\":\"com.ctrip.tour.business.dashboard.bean.ProductInfoRecificationBean\",\"title\":\"产品信息整改\",\"taskType\":\"TASK_SUPPLIER_REWARD_PUNISHMENT_01\",\"taskName\":\"商品质量信息整改\",\"eventType\":\"EVENT_SUPPLIER_REWARD_PUNISHMENT_04\",\"serviceNumInfo\":{\"contentType\":1,\"length\":4000,\"templateStr\":\"您有%s家供应商产品信息需整改，可点击查看详情\",\"isplace\":true,\"templateList\":[\"count\"]},\"detailInfo\":{\"contentType\":2,\"length\":4000,\"templateStr\":\"您名下产品将被处理，如有异议请及时申诉\",\"isplace\":false,\"headers\":{\"违规原因\":[\"problemType\"],\"产品\":[\"productId\",\"productName\"],\"最晚整改完成时间\":[\"rectificationDeadline\"],\"申诉链接\":[\"problemUrl\"]}}}}},{\"sceneType\":\"PRODUCT_INFO_APPEAL_PASSED\",\"isSplitScene\":true,\"className\":\"com.ctrip.tour.business.dashboard.bean.ProductInfoAppealPassedBean\",\"splitType\":1,\"splitSceneMap\":{\"supplier\":{\"sceneType\":\"PRODUCT_INFO_APPEAL_PASSED_SUPPLIER_DELIVERY\",\"className\":\"com.ctrip.tour.business.dashboard.bean.ProductInfoAppealPassedBean\",\"messageType\":1,\"title\":\"供应商产品信息申诉通过\",\"taskType\":\"TASK_SUPPLIER_REWARD_PUNISHMENT_02\",\"taskName\":\"商品质量信息申诉\",\"eventType\":\"EVENT_SUPPLIER_REWARD_PUNISHMENT_02\",\"serviceNumInfo\":{\"contentType\":1,\"length\":4000,\"templateStr\":\"您有%s家供应商产品信息整改申诉通过，可点击查看详情\",\"isplace\":true,\"templateList\":[\"count\"]},\"detailInfo\":{\"contentType\":2,\"length\":4000,\"templateStr\":\"您名下供应商产品信息申诉已通过，如供应商重新编辑产品，该产品将从白名单中剔除，重新触发数据监控\",\"isplace\":false,\"headers\":{\"供应商\":[\"vendorId\",\"vendorName\"],\"产品\":[\"productId\",\"productName\"]}}},\"business\":{\"messageType\":1,\"sceneType\":\"PRODUCT_INFO_APPEAL_PASSED_BUSINESS_DELIVERY\",\"className\":\"com.ctrip.tour.business.dashboard.bean.ProductInfoAppealPassedBean\",\"title\":\"产品信息申诉通过\",\"taskType\":\"TASK_SUPPLIER_REWARD_PUNISHMENT_02\",\"taskName\":\"商品质量信息申诉\",\"eventType\":\"EVENT_SUPPLIER_REWARD_PUNISHMENT_05\",\"serviceNumInfo\":{\"contentType\":1,\"length\":4000,\"templateStr\":\"您有%s家供应商产品信息整改申诉通过，可点击查看详情\",\"isplace\":true,\"templateList\":[\"count\"]},\"detailInfo\":{\"contentType\":2,\"length\":4000,\"templateStr\":\"您名下产品信息申诉已通过，如果您重新编辑产品，该产品将从白名单中剔除，重新触发数据监控\",\"isplace\":false,\"headers\":{\"供应商\":[\"vendorId\",\"vendorName\"],\"产品\":[\"productId\",\"productName\"]}}}}},{\"sceneType\":\"PRODUCT_INFO_APPEAL_DENIED\",\"isSplitScene\":true,\"className\":\"com.ctrip.tour.business.dashboard.bean.ProductInfoAppealDeniedBean\",\"splitType\":1,\"splitSceneMap\":{\"supplier\":{\"sceneType\":\"PRODUCT_INFO_APPEAL_DENIED_SUPPLIER_DELIVERY\",\"className\":\"com.ctrip.tour.business.dashboard.bean.ProductInfoAppealDeniedBean\",\"messageType\":1,\"title\":\"供应商产品信息申诉驳回\",\"taskType\":\"TASK_SUPPLIER_REWARD_PUNISHMENT_02\",\"taskName\":\"商品质量信息申诉\",\"eventType\":\"EVENT_SUPPLIER_REWARD_PUNISHMENT_03\",\"serviceNumInfo\":{\"contentType\":1,\"length\":4000,\"templateStr\":\"您有%s家供应商产品信息整改申诉驳回，可点击查看详情\",\"isplace\":true,\"templateList\":[\"count\"]},\"detailInfo\":{\"contentType\":2,\"length\":4000,\"templateStr\":\"您名下供应商产品信息申诉已驳回，请提醒供应商在改正期限内修改完成\",\"isplace\":false,\"headers\":{\"供应商\":[\"vendorId\",\"vendorName\"],\"产品\":[\"productId\",\"productName\"],\"驳回理由\":[\"rejectReason\"],\"改正期限\":[\"rectificationDeadline\"]}}},\"business\":{\"messageType\":2,\"sceneType\":\"PRODUCT_INFO_APPEAL_DENIED_BUSINESS_DELIVERY\",\"className\":\"com.ctrip.tour.business.dashboard.bean.ProductInfoAppealDeniedBean\",\"title\":\"产品信息申诉驳回\",\"taskType\":\"TASK_SUPPLIER_REWARD_PUNISHMENT_02\",\"taskName\":\"商品质量信息申诉\",\"eventType\":\"EVENT_SUPPLIER_REWARD_PUNISHMENT_06\",\"serviceNumInfo\":{\"contentType\":1,\"length\":4000,\"templateStr\":\"产品：%s<br/><br/>改正期限：%s<br/><br/>驳回原因：%s\",\"isplace\":true,\"templateList\":[\"product[ID+Name]\",\"changeTimeLimit\",\"rejectReason\"]},\"detailInfo\":{\"contentType\":1,\"length\":4000,\"templateStr\":\"您名下产品信息申诉已驳回，请提醒供应商在改正期限内修改完成<br/><br/>产品：%s<br/><br/>改正期限：%s<br/><br/>驳回原因：%s\",\"isplace\":true,\"templateList\":[\"product[ID+Name]\",\"changeTimeLimit\",\"rejectReason\"]}}}}]"
        employeeInfoDao.getEmployeeCode(_) >> MapperUtil.str2List("[{\"empCode\":\"S00155\",\"domainName\":\"wrjiao\"}]", BusinessDashboardEmployeeInfo.class)
        productClient.getProductInfo(_) >>MapperUtil.str2Obj("{\"productInfoList\":[{\"productId\":1,\"basicInfo\":{\"productId\":1,\"categoryId\":0,\"template\":0,\"active\":false,\"online\":true,\"name\":\"test\",\"priceMode\":0,\"sourceLocale\":\"\",\"saleMode\":0,\"source\":0,\"regionId\":0,\"peopleApplicable\":false,\"peopleApplicableRemark\":\"\",\"searchHide\":false,\"promotionRule\":0,\"travelDurationMax\":0.00,\"travelDurationMin\":0.00,\"travelDurationUnit\":0,\"coverImageId\":0,\"joinSecKill\":false,\"mustShow\":false,\"type\":0,\"additionSaleType\":0,\"subTemplate\":0,\"priceInputType\":0,\"controlType\":0,\"createRole\":0,\"userGroupId\":0,\"firstOnlineDate\":1635932169523,\"pmRuleId\":0,\"salesAttributeType\":0,\"useClauseCentral\":false,\"packingUseDateSame\":1,\"onlyPackingSale\":0,\"creationMode\":0,\"superDayTrip\":false},\"locale\":\"\"}]}", GetProductInfoResponseType.class)
        noticeInfoDao.batchInsert(_,_) >> [1,1]
        consumer.getEmployeeNumberByEmailGroup(_) >> ["TR005148"]


        expect: "call & verify"
        consumer.handleMessage(messageId, sceneType, messageInfo, receivers)


        where: "多条件验证"
        messageId   |   sceneType   |   receivers   |   messageInfo
        // "[\"my.fu\", \"mt_zhou\",\"chens\",\"qinglinwu\",\"yyang25\",\"haochenliu\", \"yanaliu\"]"
        // "[\"yyang25\",\"haochenliu\", \"yanaliu\", \"my.fu\"]"
        // 包含三个直接触发的任务场景
        "240530.102400.**********.88456.12"     |   "PRODUCT_INFO_RECTIFICATION"    |   "[\"yyang25\",\"haochenliu\"]"  |  "[{\"productId\":14952675,\"productName\":\"穿越时空遇见你—致敬作曲大师主题音乐会\",\"problemType\":\"信安文本检测\"},{\"productId\":32903378,\"productName\":\"上海+武陵源一日游【hht产品张家界总统府立即确认两日游】\",\"problemType\":\"信安文本检测\"},{\"productId\":33009752,\"productName\":\"zff-异常测试\",\"problemType\":\"信安文本检测\"},{\"productId\":56008820,\"productName\":\"北海公园单日票（23:55开演）\",\"problemType\":\"日游违规披露联系方式\"},{\"productId\":56008820,\"productName\":\"北海公园单日票（23:55开演）\",\"problemType\":\"日游违规披露联系方式\"},{\"productId\":56008821,\"productName\":\"北海公园单日票（19:40开演）\",\"problemType\":\"日游违规披露联系方式\"},{\"productId\":56008821,\"productName\":\"北海公园单日票（19:40开演）\",\"problemType\":\"日游违规披露联系方式\"},{\"productId\":56008824,\"productName\":\"北海公园单日票（22:30开演）\",\"problemType\":\"日游违规披露联系方式\"},{\"productId\":56008824,\"productName\":\"北海公园单日票（22:30开演）\",\"problemType\":\"日游违规披露联系方式\"},{\"productId\":56008862,\"productName\":\"北海公园单日票（21:05开演）\",\"problemType\":\"日游违规披露联系方式\"},{\"productId\":58198273,\"productName\":\"上海迪士尼度假区赵月专享区成人票\",\"problemType\":\"信安文本检测\"},{\"productId\":58198280,\"productName\":\"上海迪士尼度假区赵月专享区成人票\",\"problemType\":\"信安文本检测\"}]"
        "240530.102504.**********.121204.5"     |   "PRODUCT_INFO_APPEAL_DENIED"    |   "[\"yyang25\",\"haochenliu\"]"  |   "[{\"productId\":51244737,\"productName\":\"【长沙市】向日葵美术馆馆藏展“百年巨匠”系列之“大师归来”吴昌硕、齐白石、黄宾虹、潘天寿大师花鸟画展\",\"vendorId\":\"314316\",\"vendorName\":\"测试代理同步\",\"rectificationDeadline\":\"2024-05-31\",\"rectificationStartTime\":\"2024-05-30\", \"rejectReason\": \"hahah, 逗你玩\"}]"

        // 包含三个不直接触发的通知场景
        "240530.102520.**********.121204.8"     |   "PRODUCT_INFO_APPEAL_DENIED"    |   "[\"yyang25\",\"haochenliu\"]"      |   "[{\"productId\":33149275,\"productName\":\"\",\"vendorId\":\"314273\",\"vendorName\":\"地面零售测试供应商（勿改）\",\"rectificationDeadline\":\"2024-05-30\"}]"
        "240530.102520.**********.121204.7"     |   "PRODUCT_INFO_APPEAL_PASSED"    |   "[\"yyang25\",\"haochenliu\"]"      |   "[{\"vendorId\":\"314316\",\"vendorName\":\"测试代理同步\",\"productId\":51244518,\"productName\":\"【杭州市】【杭州】菊次郎的夏天—久石让轻音乐之旅钢琴音乐会\"}]"
        "240529.163354.**********.110240.8"     |   "PRODUCT_INFO_APPEAL_PASSED"    |   "[\"yyang25\",\"haochenliu\"]"      |   "[{\"vendorId\":\"314273\",\"vendorName\":\"地面零售测试供应商（勿改）\",\"productId\":33498858,\"productName\":\"\"}]"





    }

    def "test configGroupBy"() {
        given:
        remoteConfig.getConfigValue("scheduleConfig") >> "[{\"sceneType\":\"PRODUCT_INFO_RECTIFICATION\",\"isSplitScene\":true,\"className\":\"com.ctrip.tour.business.dashboard.bean.ProductInfoRecificationBean\",\"splitType\":1,\"splitSceneMap\":{\"supplier\":{\"sceneType\":\"PRODUCT_INFO_RECTIFICATION_SUPPLIER_DELIVERY\",\"className\":\"com.ctrip.tour.business.dashboard.bean.ProductInfoRecificationBean\",\"messageType\":2,\"title\":\"供应商产品信息整改\",\"serviceNumInfo\":{\"contentType\":1,\"length\":4000,\"templateStr\":\"您有%s供应商产品信息需整改，可点击查看详情\",\"isplace\":true,\"templateList\":[\"count\"]},\"detailInfo\":{\"contentType\":2,\"length\":4000,\"templateStr\":\"您名下供应商有产品将被处理，如有异议请联系供应商及时申诉\",\"isplace\":false,\"headers\":{\"违规原因\":[\"problemType\"],\"产品\":[\"productId\",\"productName\"],\"最晚整改完成时间\":[\"rectificationDeadline\"],\"申诉链接\":[\"problemUrl\"]}}},\"business\":{\"messageType\":2,\"sceneType\":\"PRODUCT_INFO_RECTIFICATION_BUSINESS_DELIVERY\",\"className\":\"com.ctrip.tour.business.dashboard.bean.ProductInfoRecificationBean\",\"title\":\"产品信息整改\",\"serviceNumInfo\":{\"contentType\":1,\"length\":4000,\"templateStr\":\"您有%s供应商产品信息需整改，可点击查看详情\",\"isplace\":true,\"templateList\":[\"count\"]},\"detailInfo\":{\"contentType\":2,\"length\":4000,\"templateStr\":\"您名下产品将被处理，如有异议请及时申诉\",\"isplace\":false,\"headers\":{\"违规原因\":[\"problemType\"],\"产品\":[\"productId\",\"productName\"],\"最晚整改完成时间\":[\"rectificationDeadline\"],\"申诉链接\":[\"problemUrl\"]}}}}},{\"sceneType\":\"PRODUCT_INFO_APPEAL_PASSED\",\"isSplitScene\":true,\"className\":\"com.ctrip.tour.business.dashboard.bean.ProductInfoAppealPassedBean\",\"splitType\":1,\"splitSceneMap\":{\"supplier\":{\"sceneType\":\"PRODUCT_INFO_APPEAL_PASSED_SUPPLIER_DELIVERY\",\"className\":\"com.ctrip.tour.business.dashboard.bean.ProductInfoAppealPassedBean\",\"messageType\":1,\"title\":\"供应商产品信息申诉通过\",\"serviceNumInfo\":{\"contentType\":1,\"length\":4000,\"templateStr\":\"您有%s供应商产品信息整改申诉通过，可点击查看详情\",\"isplace\":true,\"templateList\":[\"count\"]},\"detailInfo\":{\"contentType\":2,\"length\":4000,\"templateStr\":\"您名下供应商产品信息申诉已通过，如供应商重新编辑产品，该产品将从白名单中剔除，重新触发数据监控\",\"isplace\":false,\"headers\":{\"供应商\":[\"vendorId\",\"vendorName\"],\"产品\":[\"productId\",\"productName\"]}}},\"business\":{\"messageType\":1,\"sceneType\":\"PRODUCT_INFO_APPEAL_PASSED_BUSINESS_DELIVERY\",\"className\":\"com.ctrip.tour.business.dashboard.bean.ProductInfoAppealPassedBean\",\"title\":\"产品信息申诉通过\",\"serviceNumInfo\":{\"contentType\":1,\"length\":4000,\"templateStr\":\"您有%s供应商产品信息整改申诉通过，可点击查看详情\",\"isplace\":true,\"templateList\":[\"count\"]},\"detailInfo\":{\"contentType\":2,\"length\":4000,\"templateStr\":\"您名下产品信息申诉已通过，如果您重新编辑产品，该产品将从白名单中剔除，重新触发数据监控\",\"isplace\":false,\"headers\":{\"供应商\":[\"vendorId\",\"vendorName\"],\"产品\":[\"productId\",\"productName\"]}}}}},{\"sceneType\":\"PRODUCT_INFO_APPEAL_DENIED\",\"isSplitScene\":true,\"className\":\"com.ctrip.tour.business.dashboard.bean.ProductInfoAppealDeniedBean\",\"splitType\":1,\"splitSceneMap\":{\"supplier\":{\"sceneType\":\"PRODUCT_INFO_APPEAL_DENIED_SUPPLIER_DELIVERY\",\"className\":\"com.ctrip.tour.business.dashboard.bean.ProductInfoAppealDeniedBean\",\"messageType\":1,\"title\":\"供应商产品信息申诉驳回\",\"serviceNumInfo\":{\"contentType\":1,\"length\":4000,\"templateStr\":\"您有%s供应商产品信息整改申诉驳回，可点击查看详情\",\"isplace\":true,\"templateList\":[\"count\"]},\"detailInfo\":{\"contentType\":2,\"length\":4000,\"templateStr\":\"您名下供应商产品信息申诉已驳回，请提醒供应商在改正期限内修改完成\",\"isplace\":false,\"headers\":{\"供应商\":[\"vendorId\",\"vendorName\"],\"产品\":[\"productId\",\"productName\"],\"驳回理由\":[\"rejectReason\"],\"改正期限\":[\"rectificationDeadline\"]}}},\"business\":{\"messageType\":2,\"sceneType\":\"PRODUCT_INFO_APPEAL_DENIED_BUSINESS_DELIVERY\",\"className\":\"com.ctrip.tour.business.dashboard.bean.ProductInfoAppealDeniedBean\",\"title\":\"产品信息申诉驳回\",\"serviceNumInfo\":{\"contentType\":1,\"length\":4000,\"templateStr\":\"产品：%s\\n\\n改正期限：%s\",\"isplace\":true,\"templateList\":[\"product[ID+Name]\",\"changeTimeLimit\"]},\"detailInfo\":{\"contentType\":1,\"length\":4000,\"templateStr\":\"您名下产品信息申诉已驳回，请提醒供应商在改正期限内修改完成\\n\\n产品：%s\\n\\n改正期限：%s\",\"isplace\":true,\"templateList\":[\"product[ID+Name]\",\"changeTimeLimit\"]}}}}]"

        when:
        def resp = consumer.configGroupBy()

        then:
        with(resp){
            resp.size() != 0
        }
    }


    def "preprocessingOfSplit"(){
        given: "mock"
        def sceneMessageList = MapperUtil.str2List("[{\"productId\":14952675,\"productName\":\"穿越时空遇见你—致敬作曲大师主题音乐会\",\"problemType\":\"信安文本检测\"},{\"productId\":32903378,\"productName\":\"上海+武陵源一日游【hht产品张家界总统府立即确认两日游】\",\"problemType\":\"信安文本检测\"}]", ProductInfoRecificationBean.class)
        def receiverList = ["TR005148"]
        def sceneConfig = MapperUtil.str2Obj("{\"sceneType\":\"PRODUCT_INFO_RECTIFICATION_SUPPLIER_DELIVERY\",\"isSplitScene\":false,\"className\":\"com.ctrip.tour.business.dashboard.bean.ProductInfoRecificationBean\",\"splitType\":0,\"messageType\":3,\"title\":\"供应商产品信息整改\",\"taskType\":\"TASK_SUPPLIER_REWARD_PUNISHMENT_01\",\"taskName\":\"商品质量信息整改\",\"eventType\":\"EVENT_SUPPLIER_REWARD_PUNISHMENT_01\",\"serviceNumInfo\":{\"contentType\":1,\"length\":4000,\"templateStr\":\"您有%s家供应商产品信息需整改，可点击查看详情\",\"isplace\":true,\"templateList\":[\"count\"]},\"detailInfo\":{\"contentType\":2,\"length\":4000,\"templateStr\":\"您名下供应商有产品将被处理，如有异议请联系供应商及时申诉\",\"isplace\":false,\"headers\":{\"违规原因\":[\"problemType\"],\"产品\":[\"productId\",\"productName\"],\"最晚整改完成时间\":[\"rectificationDeadline\"],\"申诉链接\":[\"problemUrl\"]}}}", ContractEventSceneBean.class)
        noticeInfoDao.batchInsert(_,_) >> [1]

        expect: "call & verify"
        consumer.preprocessingOfSplit(sceneMessageList, receiverList, "id", sceneConfig)

    }


    def "monitor"(){
        expect: "call"
        consumer.monitor(sceneType, messageInfo, name)

        where: "多条件验证"
        sceneType   |   messageInfo     |   name
        "PRODUCT_INFO_RECTIFICATION_SUPPLIER_DELIVERY"  |   "aaaaa"     |   "split"
    }

}
