include 'BaijiCommonTypes.bjsc'
namespace java 'com.ctrip.soa._24922'

class TimeFilter{
    //时间类型 枚举值 month或者quarter或者half
    string dateType;
    //所属年 2021 2022
    string year;
    //时间类型是month时传入 所属月 01 02 03
    string month;
    //时间类型是quarter时传入 所属季 Q1 Q2 Q3 Q4
    string quarter;
    //时间类型是half时传入  枚举值为H1 H2
    string half;
    //趋势线或者下钻时展示的时间范围 选择月时枚举值3 6 12 选择季时枚举值4
    int timeFrame;
}


class GetMetricCardDataRequestType{
    //邮箱前缀
    string domainName;
    //时间选择器
    TimeFilter timeFilter;
}

class GetMetricCardDataResponseType{
    //是否需要排名  为true时需要展示排名数据
    bool needRank;
    //指标列表具体数据
    list<MetricDetailInfo> metricDetailInfoList;
    BaijiCommonTypes.ResponseStatusType responseStatus;
    
}

class MetricDetailInfo{
    //指标名称
    string metric;
    //子指标名称(前端关注 映射后)
    string subMetric;
    //子指标名称(映射前 站点和渠道)
    string actualSubMetric;
    //返回除排名外的维度数据
    map<string,double> dimData;
    //排名 格式为A/B A为个人排名 B为参加该指标排名总人数
    string rank;
    //环比类型  枚举值 7days  30days  lastmonth lastquarter  gmv和毛利指标需要该字段
    string momType;
    //考核层级  指标2当考核层级是三方的时候没有弹出层
    string level;
    //是否需要展示下钻标识
    bool needDrillDown;
    //默认下钻维度 (对于国内收入力部分) 可能需要用|分割
    string defaultField;
    //默认下钻子指标(仅国内收入力部分) 可能需要用|分割
    string defaultSubMetric;
    //国内收入力的子指标数据(分别代表门票活动和日游部分)
    list<MetricDetailInfo> subMetricDetailInfoList;
    //详情页指标卡是否可以跳转(仅国内收入力起作用)
    bool canJump;
}



class GetTrendLineDataRequestType{
    //指标枚举值
    string metric;
    //子指标枚举值
    string subMetric;
    //数据来源  枚举值有firstpage 和 detailpage
    string source;
    //邮箱前缀
    string domainName;
    //时间选择器
    TimeFilter timeFilter;
    //查询类型 枚举值为trendLine(趋势线)和的drillDown(下钻趋势线)
    string queryType;
    //下钻选择器
    DrillDownFilter drillDownFilter;
}

class DrillDownFilter{
    //下钻字段
    string field;
    //下钻字段选中的值 未选中需传[]
    list<string> fieldValueList;
}


class GetTrendLineDataResponseType{
    //考核层级  指标1和2当考核层级是景点时 整体趋势线只需要GMV+完成率
    string level;
    //趋势线数据
    list<TrendLineDetailInfo> trendLineDetailInfoList;
    BaijiCommonTypes.ResponseStatusType responseStatus; 
}


class TrendLineDetailInfo{
    //趋势线类型  lineChart折线图 barChart柱状图  stackBarChart堆叠柱状图
    string type;
    //维度名称 标识当前趋势线所属的是哪个具体指标
    string dim;
    //实际数据
    list<TrendLineDataItem> trendLineDataItemList;
}

class TrendLineDataItem{
    //在需要多个同类型趋势线时生效(例如下钻 堆叠柱状图等)
    string name;
    //对应时间
    string time;
    //对应值
    double value;
}

class GetDrillDownBaseInfoRequestType{
    //时间选择器
    TimeFilter timeFilter;
    //指标枚举值
    string metric;
    //子指标枚举值
    string subMetric;
    //邮箱前缀
    string domainName;
    //是否为搜索模式
    bool needSearch;
    //搜索field 没有可以不填
    string searchField;
    //搜索词
    string searchWord;
}


class GetDrillDownBaseInfoResponseType{
    //当前选择用户应该默认选中的下钻字段 
    string defaultChosenField;
    //所有可能的下钻字段以及其可下钻的值
    list<FieldDataItem> fieldDataItemList;
    BaijiCommonTypes.ResponseStatusType responseStatus;
}





class FieldDataItem{
    //当前字段
    string field;
    //是否需要气泡图
    bool needBubble;
    //是否需要趋势线
    bool needLine;
    //当前字段的所有可能值(大区 省份 商拓 POI) poi需要value和relationValue
    list<FieldValueItem> fieldValueItemList;
} 

class FieldValueItem{
    string value;
    string relationValue;
}




class GetTableDataRequestType{
    //数据来源  枚举值有firstpage 和 detailpage
    string source;
    //指标枚举值
    string metric;
    //子指标枚举值
    string subMetric;
    //邮箱前缀
    string domainName;
    //时间选择器
    TimeFilter timeFilter;
    //查询类型 枚举值为overall(总体表格)和的drilldown(下钻表格)
    string queryType;
    //下钻选择器
    DrillDownFilter drillDownFilter;
    //当前页数  
    int pageNo;
    //分页条数  对于首页以及详情页的气泡图可以传999
    int pageSize;
}


class GetTableDataResponseType{
    //总数据条数
    int totalNum;
    //环比类型  枚举值 7days  30days  lastmonth lastquarter  gmv和毛利指标需要该字段
    string momType;
    //品类个数  品类指标需要该字段
    int categoryNum;
    //按商拓下钻时是否需要展示同比
    bool needShowExamineePopData;
    //表格数据
    list<TableDataItem> tableDataItemList;
    BaijiCommonTypes.ResponseStatusType responseStatus;
}

class TableDataItem{
    map<string,string> fieldMap;
    map<string,double> dimMap;
}

class DownloadTableDataRequestType{
    //指标枚举值
    string metric;
    //子指标枚举值
    string subMetric;
    //邮箱前缀
    string domainName;
    //时间选择器
    TimeFilter timeFilter;
    //查询类型 枚举值为overall(总体表格)和的drilldown(下钻表格)
    string queryType;
    //下钻选择器
    DrillDownFilter drillDownFilter;
}
class DownloadTableDataResponseType{
    string fileUrl;
    BaijiCommonTypes.ResponseStatusType responseStatus;
}

@serviceName='BIBusinessDashboardService'
@serviceNamespace='http://soa.ctrip.com/_24922'
service BIBusinessDashboardService {
    //获取指标卡
    GetMetricCardDataResponseType getMetricCardData(GetMetricCardDataRequestType request);
    //获取趋势线数据
    GetTrendLineDataResponseType getTrendLineData(GetTrendLineDataRequestType request);
    //获取可下钻字段以及其值
    GetDrillDownBaseInfoResponseType getDrillDownBaseInfo(GetDrillDownBaseInfoRequestType request);
    //获取表格数据
    GetTableDataResponseType getTableData(GetTableDataRequestType request);
    //表格数据下载
    DownloadTableDataResponseType downloadTableData(DownloadTableDataRequestType request);
}