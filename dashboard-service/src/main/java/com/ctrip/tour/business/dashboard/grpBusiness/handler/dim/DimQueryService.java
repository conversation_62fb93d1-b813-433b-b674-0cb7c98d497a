package com.ctrip.tour.business.dashboard.grpBusiness.handler.dim;

import static com.ctrip.tour.business.dashboard.grpBusiness.exception.ExceptionEnum.EMP_CAN_NOT_FIND;

import java.sql.SQLException;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ctrip.soa._24922.CustTourRegionInfo;
import com.ctrip.soa._24922.GetGrpDillDownDimEnumRequestType;
import com.ctrip.tour.business.dashboard.grpBusiness.dao.starrocks.StarRocksDao;
import com.ctrip.tour.business.dashboard.grpBusiness.enums.DiyMetricCategoryEnum;
import com.ctrip.tour.business.dashboard.grpBusiness.enums.DiyMetricDimEnum;
import com.ctrip.tour.business.dashboard.grpBusiness.enums.MetricCategoryEnum;
import com.ctrip.tour.business.dashboard.grpBusiness.enums.MetricDimEnum;
import com.ctrip.tour.business.dashboard.grpBusiness.enums.MetricEnum;
import com.ctrip.tour.business.dashboard.grpBusiness.service.CustEmpOrgInfoService;
import com.ctrip.tour.business.dashboard.grpBusiness.service.HrOrgEmpInfoService;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.ServiceException;
import com.ctrip.tour.business.dashboard.utils.SqlBuilder;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2024/12/18
 */
@Service
@Slf4j
public class DimQueryService {

    private final static String SUB_BU_TYPE = "sub_bu_type";
    private final static String BU_TYPE = "bu_type";
    private final static String PM_EID = "pm_eid";
    private final static String LOCAL_PM_EID = "local_pmeid";
    private final static String ORDER_ID = "order_id";
    private final static String UID = "uid";
    private final static String SECOND_SESSION_TYPE = "second_session_type";
    private final static String CHAT_CREATE_DATE = "page_date";
    private final static String PRD_REGION_ID = "prd_region_id";
    private final static String PRD_PATTERN_ID = "prd_pattern_name";
    private final static String BIZ_MODE = "biz_mode";
    private final static String EMP_CODES = "empCodes";
    private final static String OR = "OR";


    @Autowired
    private StarRocksDao starRocksDao;


    private final static String ADMIN_EID = "grp.admin.eid";
    private final static String GRP_GRP_ADMIN_EID = "grp.grp.admin.eid";
    private final static String GRP_PRV_ADMIN_EID = "grp.prv.admin.eid";
    private final static String GRADE_REGION_NAME = "grade_region_name";

    @Autowired
    private RemoteConfig remoteConfig;
    @Autowired
    private HrOrgEmpInfoService hrOrgEmpInfoService;
    @Autowired
    private CustEmpOrgInfoService custEmpOrgInfoService;

    public List<String> queryDimData(GetGrpDillDownDimEnumRequestType requestType) {
        Integer selectTab = requestType.getSelectTab();
        if (Objects.equals(selectTab , 1) || Objects.equals(selectTab , 2)) {
            return queryGrpDimData(requestType);
        } else {
            return queryDiyDimData(requestType);
        }
    }

    public List<String> queryDiyDimData(GetGrpDillDownDimEnumRequestType requestType) {


        List<MetricEnum> metricEnums = DiyMetricCategoryEnum.getMetricEnumsByCategoryEnglishName(requestType.getMetricCategorieName(), requestType.getDateType());

        List<String> dimEnumVals = metricEnums.stream()
                .map(metricEnum -> {
                    DiyMetricDimEnum metricDimEnum = DiyMetricDimEnum.getMetricDimEnumByMetricName(metricEnum.getEnglishName());
                    if (Objects.isNull(metricDimEnum)) {
                        return null;
                    }
                    Map<String, String> calcDateNameMap = metricDimEnum.getCalcDateNameMap();
                    String calcDateName = calcDateNameMap.get(requestType.getDateType());
                    Map<String, Object> param = null;
                    try {
                        param = request2Param4Diy(requestType, calcDateName);
                    } catch (Exception e) {
                       log.info("convert param error", e);
                       return null;
                    }
                    String sql = buildQuerySql(param, DimconvertHandler.convertDimEnumName(requestType.getDrillDownDim(), requestType.getMetricCategorieName()),
                            metricDimEnum.getTableName(), requestType.getMetricCategorieName());
                    List<Map<String, Object>> listResult = Lists.newArrayList();
                    try {
                        listResult = starRocksDao.getListResult(sql, Lists.newArrayList());
                    } catch (SQLException e) {
                        log.warn("query data error", e);
                    }
                    return listResult.stream().map(map -> String.valueOf(map.get("result_val"))).collect(Collectors.toList());

                }).filter(Objects::nonNull).flatMap(Collection::stream).distinct().collect(Collectors.toList());
        return dimEnumVals;

    }

    public List<String> queryGrpDimData(GetGrpDillDownDimEnumRequestType requestType) {


        List<MetricEnum> metricEnums = MetricCategoryEnum.getMetricEnumsByCategoryEnglishName(requestType.getMetricCategorieName());

        List<String> dimEnumVals = metricEnums.stream()
                .map(metricEnum -> {
                    MetricDimEnum metricDimEnum = MetricDimEnum.getMetricDimEnumByMetricName(metricEnum.getEnglishName());
                    if (Objects.isNull(metricDimEnum)) {
                        return null;
                    }

                    Map<String, Object> param = request2Param(requestType, requestType.getBusinessLine(), metricDimEnum.getCalcDateName());
                    String sql = buildQuerySql(param, DimconvertHandler.convertDimEnumName(requestType.getDrillDownDim(), requestType.getMetricCategorieName()),
                            metricDimEnum.getTableName(), requestType.getMetricCategorieName());
                    List<Map<String, Object>> listResult = Lists.newArrayList();
                    try {
                        listResult = starRocksDao.getListResult(sql, Lists.newArrayList());
                    } catch (SQLException e) {
                        log.warn("query data error", e);
                    }
                    return listResult.stream().map(map -> String.valueOf(map.get("result_val"))).collect(Collectors.toList());

                }).filter(Objects::nonNull).flatMap(Collection::stream).distinct().collect(Collectors.toList());
        return dimEnumVals;

    }

    private Map<String, Object> request2Param4Diy(GetGrpDillDownDimEnumRequestType requestType, String calcDateName) throws Exception {
        Map<String, Object> param = Maps.newHashMap();
        param.put(calcDateName, new String[]{requestType.getStartDate(), requestType.getEndDate()});
        List<String> grdAreas = requestType.getAreas();
        if (CollectionUtils.isEmpty(requestType.getAreas())) {
            List<CustTourRegionInfo> custTourRegionList = custEmpOrgInfoService.getCustTourRegionList(requestType.getEmpCode());
            grdAreas = custTourRegionList.stream().flatMap(custTourRegionInfo -> custTourRegionInfo.getAreas().stream())
                    .collect(Collectors.toList());
        }

        param.put(GRADE_REGION_NAME, grdAreas);
        return param;
    }

    protected Map<String, Object> request2Param(GetGrpDillDownDimEnumRequestType requestType, Integer bizMode, String calcDateName) {
        Map<String, Object> param = Maps.newHashMap();

        List<String> empCodes = Lists.newArrayList();
        try {

            String adminEid = remoteConfig.getExternalConfig(ADMIN_EID);

            if (StringUtils.equals(requestType.getEmpCode(), adminEid)) {
                String grpAdminEid = remoteConfig.getExternalConfig(GRP_GRP_ADMIN_EID);
                String prvAdminEid = remoteConfig.getExternalConfig(GRP_PRV_ADMIN_EID);
                if (Objects.equals(bizMode, 0)) {
                    empCodes.addAll(hrOrgEmpInfoService.getAllSubordinateEmpCode(grpAdminEid));
                    empCodes.addAll(hrOrgEmpInfoService.getAllSubordinateEmpCode(prvAdminEid));
                } else if (Objects.equals(bizMode, 1) || Objects.equals(bizMode, 3)) {
                    empCodes.addAll(hrOrgEmpInfoService.getAllSubordinateEmpCode(grpAdminEid));
                } else if (Objects.equals(bizMode, 2) || Objects.equals(bizMode, 4)) {
                    empCodes.addAll(hrOrgEmpInfoService.getAllSubordinateEmpCode(prvAdminEid));
                }
            } else {
                empCodes.addAll(hrOrgEmpInfoService.getAllSubordinateEmpCode(requestType.getEmpCode()));
            }
        } catch (SQLException e) {
            log.warn("query empcode error" + requestType.getEmpCode(), e);
            throw new ServiceException(EMP_CAN_NOT_FIND.getCode(),EMP_CAN_NOT_FIND.getMsg());
        }

        Integer businessLine = requestType.getBusinessLine();

        if (Objects.equals(1, businessLine)) {
            param.put(BIZ_MODE, 1);
            param.put(EMP_CODES, empCodes);
        } else if (Objects.equals(2, businessLine)) { //跟团
            param.put(Joiner.on("-").join(OR, PM_EID, DimconvertHandler.getLocalPmEidName(requestType.getMetricCategorieName())), new List[]{empCodes, empCodes});
        } else if (Objects.equals(3, businessLine)) {//私家团
            param.put(PM_EID, empCodes);
        }


        param.put(PRD_PATTERN_ID, requestType.getProductPattern());
        param.put(calcDateName, new String[]{requestType.getStartDate(), requestType.getEndDate()});
        if (businessLine == 1 || businessLine == 2) {
            param.put(SUB_BU_TYPE, businessLine == 1?"跟团游":"独立出游");//NOSONAR
        }

        return param;
    }

    protected SqlBuilder selectColsAssembly(SqlBuilder sqlBuilder, String dimEnumName) {

        String sql = "  distinct " + dimEnumName +"  as result_val ";

        sqlBuilder.select(sql);

        return sqlBuilder;
    }

    private String buildQuerySql(Map<String, ?> param, String dimEnum, String tableName, String categoryName) {

        SqlBuilder sqlBuilder = new SqlBuilder();
        SqlBuilder.Condition condition = new SqlBuilder.Condition();

        sqlBuilder = selectColsAssembly(sqlBuilder, dimEnum);
        sqlBuilder.from(tableName);

        for (Map.Entry<String, ?> entry : param.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if (StringUtils.equals("biz_mode", key) || StringUtils.equals("empCodes", key)) {
                continue;
            }
            if (key.startsWith(OR)) {
                String[] splitKey = key.split("-");

                SqlBuilder.Condition orConditon = new SqlBuilder.Condition();
                for (int i = 1; i < splitKey.length; i++) {
                    SqlBuilder.Condition subCondition = buildCondition(splitKey[i], ((Object[]) value)[i-1]);
                    orConditon.or(subCondition);
                }
                String sqlWithParams = orConditon.getSqlWithParams();
                sqlBuilder.and("(" + sqlWithParams + ")");
            } else {
                SqlBuilder.Condition newCondition = buildCondition(key, value);
                condition.and(newCondition);
            }

        }
        String otherCon = doOtherCon(param,categoryName);
        if (StringUtils.isNotBlank(otherCon)) {
            condition.and(otherCon);
        }
        sqlBuilder.whereWithCondition(condition);
        return sqlBuilder.getSql();
    }

    protected String doOtherCon(Map<String, ?> param, String categoryName) {
        Object bizMode = param.get("biz_mode");
        Object empCodes = param.get("empCodes");
        if (bizMode != null && Objects.nonNull(empCodes) && CollectionUtils.isNotEmpty((List<String>) empCodes) && Objects.equals(1, bizMode)) {

            List<String> empcodeFormat = ((List<?>) empCodes).stream().map(empCode -> "'" + empCode + "'")
                    .collect(Collectors.toList());

            String teamSql = SUB_BU_TYPE + " = " + "'跟团游'" + " and (" + PM_EID + " in (" + Joiner.on(",").join(empcodeFormat) + ")"//NOSONAR
                    + " or " + DimconvertHandler.getLocalPmEidName(categoryName) + " in (" + Joiner.on(",").join(empcodeFormat) + "))";

            String priSql = SUB_BU_TYPE + " = " + "'独立出游'" + " and " + PM_EID + " in (" + Joiner.on(",").join(empcodeFormat) + ")";//NOSONAR

            return " ((" + teamSql + ") or (" + priSql + ")) ";
        }
        return null;
    }

    private SqlBuilder.Condition buildCondition(String paramKey, Object paramVal) {

        if (StringUtils.isBlank(paramKey) || Objects.isNull(paramVal)) {
            return null;
        }

        SqlBuilder.Condition condition = new SqlBuilder.Condition();

        if (paramVal instanceof String) {
            condition.eq(paramKey, "'" + paramVal + "'");
        }else if ( paramVal instanceof Number) {
            condition.eq(paramKey, paramVal);
        } else if (paramVal instanceof Collection) {
            condition.in(paramKey, (List<?>) paramVal);
        } else if (paramVal.getClass().isArray()) {
            Object[] arrayParam = (Object[]) paramVal;
            SqlBuilder.Condition lteCon = new SqlBuilder.Condition();
            if (Objects.nonNull(arrayParam[0])) {
                if (arrayParam[0] instanceof String) {
                    condition.gte(paramKey, "'" + arrayParam[0] + "'");
                } else {
                    condition.gte(paramKey, arrayParam[0]);
                }

            }
            condition.and(lteCon.lte(paramKey, arrayParam[0] instanceof String ?"'" + arrayParam[1] + "'"
                    : arrayParam[1]), () ->arrayParam.length > 1 && Objects.nonNull(arrayParam[1]));
        } else {
            throw new ServiceException("1000400", "value type is not supported");
        }
        return condition;
    }
}
