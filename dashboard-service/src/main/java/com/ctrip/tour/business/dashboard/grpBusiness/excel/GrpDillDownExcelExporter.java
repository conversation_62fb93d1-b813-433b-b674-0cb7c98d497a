package com.ctrip.tour.business.dashboard.grpBusiness.excel;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import com.ctrip.soa._24922.ExtraValues;
import com.ctrip.soa._24922.GrpMetric;
import com.ctrip.soa._24922.GrpTableRow;
import com.ctrip.tour.business.dashboard.grpBusiness.common.GrpConstant;
import com.ctrip.tour.business.dashboard.grpBusiness.enums.MetricCategoryEnum;
import com.ctrip.tour.business.dashboard.grpBusiness.enums.MetricEnum;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.UploadResult;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.enums.FileTypeEnum;
import com.ctrip.tour.business.dashboard.utils.FileServerUtil;
import com.ctriposs.baiji.rpc.server.HttpResponseWrapper;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;

/**
 * <AUTHOR>
 * @Date 2024/12/20
 */
@Component
public class GrpDillDownExcelExporter {

    @Autowired
    RemoteConfig remoteConfig;

    static ImmutableList<String> dillDownHearders = ImmutableList.of("全部", "跟团游", "独立出游");//NOSONAR

    public String getExportDillDownFileUrl(List<GrpTableRow> tableRows, String metricCategory, String dimName) throws IOException, NoSuchAlgorithmException {
// 创建一个新的工作簿
        SXSSFWorkbook workbook = new SXSSFWorkbook(30000);

        // 创建一个工作表
        Sheet sheet = workbook.createSheet("Sheet1");

        List<List<Object>> dataList = tableRows.stream().map(row -> getRowData(row, metricCategory))
                .collect(Collectors.toList());

        List<String> header = getHeader(metricCategory, dimName);
        List<MetricEnum> metricEnums = MetricCategoryEnum.getMetricEnumsByCategoryEnglishName(metricCategory);
        int merticEnumSize = metricEnums.size();

        int firstRowNum = 0;
        int firstColNum = 1;
        Row firstRow = sheet.createRow(firstRowNum);
        for (String dillDownHearder : dillDownHearders) {
            int totalColNum = 0;
            if (merticEnumSize == 1) {
                totalColNum = 2;
            } else {
                totalColNum = merticEnumSize;
            }
            for (int j = 0; j < totalColNum; j++) {
                Cell cell = firstRow.createCell(firstColNum++);
                cell.setCellValue(dillDownHearder);
            }

        }



        int secondRowNum = 1;
        int secondColNum = 0;
        Row secondRow = sheet.createRow(secondRowNum);
        for (String s : header) {
            Cell cell = secondRow.createCell(secondColNum++);
            cell.setCellValue(s);
        }
        // 创建行和单元格并填充数据
        int dataRow = 2;
        for (List<Object> rowData : dataList) {
            int dataCol = 0;
            Row row = sheet.createRow(dataRow);
                for (Object field : rowData) {
                    Cell cell = row.createCell(dataCol++);
                    if (Objects.isNull(field)) {
                        cell.setCellValue("");
                    } else if (field instanceof String) {
                        cell.setCellValue((String) field);
                    } else if (field instanceof Long) {
                        cell.setCellValue(BigDecimal.valueOf((long) field).setScale(5, RoundingMode.HALF_UP).doubleValue());
                    } else if (field instanceof Double) {
                        cell.setCellValue(BigDecimal.valueOf((Double) field).setScale(5, RoundingMode.HALF_UP).doubleValue());
                    }
                }
                dataRow++;
        }
        CellRangeAddress mergedRegion = null;


        // 合并单元格
        // 例如，合并第一行的第一列到第三列（即标题行）
        int startCol = 1;
        for (int i = 0; i < 3; i++) {
            int endCol;
            if (merticEnumSize == 1) {
                endCol = startCol + 1;
            } else {
                endCol = startCol + merticEnumSize  - 1;
            }
            mergedRegion = new CellRangeAddress(0, 0, startCol, endCol);

            sheet.addMergedRegion(mergedRegion);
            startCol = endCol + 1;
        }



        // 创建一个样式来居中对齐合并后的单元格
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 应用样式到合并后的单元格
        Row headerRow = sheet.getRow(0);
        Cell headerCell = headerRow.getCell(1);
        headerCell.setCellStyle(style);

        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        try {
            workbook.write(bos);
        } catch (Exception e){
            return "";
        }finally {
            bos.close();
        }
        byte[] bytes = bos.toByteArray();
        UploadResult uploadResult = FileServerUtil.upload(bytes, FileTypeEnum.XLSX, remoteConfig.getConfigValue("fileChannel"), remoteConfig.getConfigValue("hostName"),null);
        if (Objects.nonNull(uploadResult)) {
            return uploadResult.getFileUrl();
        }
        return "";
        // 自动调整列宽
//        for (int i = 0; i < dataList.get(0).size(); i++) {
//            sheet.autoSizeColumn(i);
//        }

//        String title = "跟团工作台看板下钻详情" + DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now());
//        outputExcel(workbook, responseWrapper, title);

    }

    private List<Object> getRowData(GrpTableRow row, String metricCategory) {

        List<MetricEnum> metricEnums = MetricCategoryEnum.getMetricEnumsByCategoryEnglishName(metricCategory);

        String dimEnum = row.getDimEnum();
        List<GrpMetric> metricList = row.getMetricList();
        List<GrpMetric> busLine1MetricList = row.getBusLine1MetricList();
        List<GrpMetric> busLine2MetricList = row.getBusLine2MetricList();
        List<Object> rowData = Lists.newArrayList();
        rowData.add(dimEnum);

        for (List<GrpMetric> metrics : Lists.newArrayList(metricList, busLine1MetricList, busLine2MetricList)) {
            if (metricEnums.size() == 1) {
                GrpMetric metric = metrics.get(0);
                Double metricValue = metric.getMetricValue();
                rowData.add(metricValue);
                ExtraValues extraValues = metric.getExtraValues();
                if (Objects.nonNull(extraValues)) {
                    Double yearOverYear = extraValues.getYearOverYear();
                    rowData.add(yearOverYear);
                } else {
                    rowData.add(null);
                }
            } else {
                for (GrpMetric metric : metrics) {
                    rowData.add(metric.getMetricValue());
                }
            }
        }
        return rowData;
    }

    private  List<String> getHeader(String metricCategory, String dimName) {

        List<MetricEnum> metricEnums = MetricCategoryEnum.getMetricEnumsByCategoryEnglishName(metricCategory);

        List<String> header = Lists.newArrayList();
        header.add(dimName);//NOSONAR

        for (int i = 0; i < 3; i++) {
            if (metricEnums.size() == 1) {
                MetricEnum metricEnum = metricEnums.get(0);
                header.add(metricEnum.getChineseName());
                header.add("同比去年");//NOSONAR
            } else {
                for (MetricEnum metricEnum : metricEnums) {
                    header.add(metricEnum.getChineseName());
                }
            }

        }
        return header;
    }

    //下载
    public  void outputExcel(SXSSFWorkbook workbook,
                                   HttpResponseWrapper responseWrapper, String title) throws IOException {
        String fileName = URLEncoder.encode(title, "UTF-8");
        responseWrapper.setHeader("Content-Disposition", "attachment; filename=" + fileName + ".xlsx");
        responseWrapper.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
        responseWrapper.setHeader("Access-Control-Allow-Origin", "*");
        responseWrapper.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        OutputStream out = responseWrapper.getResponseStream();
        workbook.write(out);
        out.close();
        workbook.dispose();
    }

}
