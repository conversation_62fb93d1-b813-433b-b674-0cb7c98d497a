package com.ctrip.tour.business.dashboard.grpBusiness.service;


import com.ctrip.tour.business.dashboard.grpBusiness.config.GrpUserMappingConfig;
import org.apache.commons.lang.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class UserInfoService {
    @Autowired
    GrpUserMappingConfig grpUserMappingConfig;


    public String getActualEmpCode() {
        return MDC.get("empCode");
    }

    public String getMappingEmpCode() {
        String empCode = getActualEmpCode();
        String mappingEmpCode = grpUserMappingConfig.getGrpUserMappingResult(empCode);

        if (StringUtils.isBlank(mappingEmpCode)) {
            mappingEmpCode = empCode;
        }
        return mappingEmpCode;

    }

}
