package com.ctrip.tour.business.dashboard.tktBusiness.helper;

import com.ctrip.soa._24922.DomesticNoDrillTableData;
import com.ctrip.soa._24922.TimeFilter;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ExamineConfigBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class DomesticMetricHelper {

    /**
     * 接口业务线转为系统业务线
     * @param businessId
     * @param subBusinessId
     * @return
     */
    public static Integer transformBusinessInfo(Integer businessId,Integer subBusinessId) {
        int businessLine = 0;
        if (businessId == 1) {
            // 门票
            businessLine = 1;
        }else{
            // 玩乐
            businessLine = 2;
            if (subBusinessId != null) {
                if (subBusinessId == 3) {
                    // 活动
                    businessLine = 3;
                }else if (subBusinessId == 4){
                    // 日游
                    businessLine = 4;
                }
            }
        }
        return businessLine;
    }

    /**
     * 获取时间列表
     * @param examineConfigBean
     * @return
     */
    public static String getTimeFormat(ExamineConfigBean examineConfigBean) {
        String month;
        String quarter;
        StringBuilder timeString = new StringBuilder(examineConfigBean.getYear());
        timeString.append("-");

        if ("month".equals(examineConfigBean.getDateType())) {
            month = examineConfigBean.getMonth();
            timeString.append(month);
        } else {
            quarter = examineConfigBean.getQuarter();
            timeString.append(quarter);
        }
        return timeString.toString();
    }

    /**
     * 设置时间过滤器
     * @param examineConfigBean
     * @return
     */
    public static TimeFilter getTimeFilter(ExamineConfigBean examineConfigBean){
        TimeFilter timeFilter = new TimeFilter();
        timeFilter.setYear(examineConfigBean.getYear());
        timeFilter.setQuarter(examineConfigBean.getQuarter());
        timeFilter.setMonth(examineConfigBean.getMonth());
        timeFilter.setDateType(examineConfigBean.getDateType());
        return timeFilter;
    }

    /**
     * 设置质量时间过滤器
     * @return
     */
    public static TimeFilter getTimeFilterWithQuality(MetricInfoBean bean, TimeFilter originTimeFilter){
        TimeFilter timeFilter = new TimeFilter();
        timeFilter.setDateType("quarter");
        timeFilter.setYear(originTimeFilter.getYear());
        timeFilter.setQuarter(bean.getQuarter());
        return timeFilter;
    }

    /**
     * 设置1314指标
     * @param metricInfoBeanList
     * @param metricInfoBeanMap
     */
    public static void setDomesticMetricCalStrategyMap(List<MetricInfoBean> metricInfoBeanList, Map<String, List<MetricInfoBean>> metricInfoBeanMap) {
        List<MetricInfoBean> metricInfoBean13 = new ArrayList<>();
        List<MetricInfoBean> metricInfoBean14 = new ArrayList<>();
        for (MetricInfoBean bean : metricInfoBeanList) {
            if ("4".equals(bean.getMetric()) ||"5".equals(bean.getMetric()) || "6".equals(bean.getMetric()) || "7".equals(bean.getMetric())) {
                metricInfoBean13.add(bean);
            }
            if ("11".equals(bean.getMetric()) || "12".equals(bean.getMetric())) {
                metricInfoBean14.add(bean);
            }
        }
        if (!metricInfoBean13.isEmpty()) {
            metricInfoBeanMap.put("13", metricInfoBean13);
        }
        if (!metricInfoBean14.isEmpty()) {
            metricInfoBeanMap.put("14", metricInfoBean14);
        }
    }

    public static void initCategoryBean(DomesticNoDrillTableData bean,String name) {
        bean.setCategoryWeight(0.00);
        bean.setCategoryCompleteRate(0.00);
        bean.setCategoryTargetCoverageRate(0.00);
        bean.setCategoryCurrentCoverageRate(0.00);
        bean.setCategoryUncoveredExperienceNum(0);
        bean.setCategoryOperatingExperienceNum(0);
        bean.setCategoryCoveredExperienceNum(0);
        bean.setCategoryCrashExperienceNum(0);
        bean.setCategoryExamineType(name);
    }

    public static void initDirectBean(DomesticNoDrillTableData bean,String name) {
        bean.setDirectSightGap(0);
        bean.setDirectCompleteRate(0.00);
        bean.setDirectRate(0.00);
        bean.setDirectWeight(0.00);
        bean.setDirectCompleteScore(0);
        bean.setDirectCrashSightNum(0);
        bean.setDirectRealSightNum(0);
        bean.setDirectTargetScore(0);
        bean.setDirectTargetSightNum(0);
        bean.setDirectSightRange(name);
    }

    // 判断是否考核层级都为空
    public static boolean existBusinessLevel(Integer businessId, List<MetricInfoBean> metricInfoBeanList) {
        switch (businessId) {
            case 0:
                return metricInfoBeanList.stream().allMatch(e -> (e.getLevel() == null || StringUtils.isBlank(e.getLevel())) && (e.getOdtLevel() == null || StringUtils.isBlank(e.getOdtLevel())) && (e.getActLevel() == null || StringUtils.isBlank(e.getActLevel())));
            case 1:
                return metricInfoBeanList.stream().allMatch(e -> e.getLevel() == null || StringUtils.isBlank(e.getLevel()));
            case 2:
                return metricInfoBeanList.stream().allMatch(e -> (e.getOdtLevel() == null || StringUtils.isBlank(e.getOdtLevel())) && (e.getActLevel() == null || StringUtils.isBlank(e.getActLevel())));
            case 3:
                return metricInfoBeanList.stream().allMatch(e -> e.getActLevel() == null || StringUtils.isBlank(e.getActLevel()));
            case 4:
                return metricInfoBeanList.stream().allMatch(e -> e.getOdtLevel() == null || StringUtils.isBlank(e.getOdtLevel()));
        }
        return false;
    }
}
