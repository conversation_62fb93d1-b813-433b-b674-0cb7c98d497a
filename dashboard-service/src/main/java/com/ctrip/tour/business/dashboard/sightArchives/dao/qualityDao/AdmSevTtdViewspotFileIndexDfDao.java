package com.ctrip.tour.business.dashboard.sightArchives.dao.qualityDao;

import com.ctrip.soa._24922.VendorQuality;
import com.ctrip.soa._27181.PreparedParameterBean;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.TktStarRocksDao;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.Types;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class AdmSevTtdViewspotFileIndexDfDao {
    @Autowired
    private TktStarRocksDao tktStarRocksDao;



    public Map<String, Object> queryComplaintMetric(String queryD,Long sightId, Boolean needSubSight, String startDate, String endDate, Integer dateType, Integer businessType, List<Long> vendorIdList) {
        StringBuilder sql = new StringBuilder("select " +
                "cast(sum(complain_cnt) as Integer) as complain_cnt," +
                "cast(sum(complain_cnt)/sum(odr_cnt) as Double) as complain_rate" +
                " from adm_sev_ttd_viewspot_file_index_df");
        List<PreparedParameterBean> parameters = new java.util.ArrayList<>();
        appendSightId(parameters, sql, sightId, needSubSight);
        appendQueryD(parameters, sql, queryD);
        appendDateRange(parameters, sql, dateType, startDate, endDate);
        appendBusinessType(parameters, sql, businessType);
        appendVendorIdList(parameters, sql, vendorIdList);
        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            log.warn("queryComplaintMetric error", e);
        }
        if (CollectionUtils.isEmpty(result)) {
            return new HashMap<>();
        }
        return result.get(0);
    }


    public Map<String, Object> queryComplaintMetricYoy(String queryD,Long sightId, Boolean needSubSight, String startDate, String endDate, Integer dateType, Integer businessType, List<Long> vendorIdList) {
        StringBuilder sql = new StringBuilder("select " +
                " cast(sum(complain_cnt)/sum(odr_cnt) as Double) as complain_rate" +
                " from adm_sev_ttd_viewspot_file_index_df t1 inner join v_dim_date t2 on t1.biz_date = t2.date_lastyear");
        List<PreparedParameterBean> parameters = new java.util.ArrayList<>();
        appendSightId(parameters, sql, sightId, needSubSight);
        sql.append(" and t1.d = ?");
        parameters.add(new PreparedParameterBean(queryD, Types.VARCHAR));
        appendDateRangeOfLastYear(parameters, sql, startDate, endDate);
        appendBusinessType(parameters, sql, businessType);
        appendVendorIdList(parameters, sql, vendorIdList);
        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            log.warn("queryCpoYoy error", e);
        }
        if (CollectionUtils.isEmpty(result)) {
            return new HashMap<>();
        }
        return result.get(0);
    }



    public Map<String, Object> queryCpoAndPhone(String queryD,Long sightId, Boolean needSubSight, String startDate, String endDate, Integer dateType, Integer businessType, List<Long> vendorIdList) {
        StringBuilder sql = new StringBuilder("select " +
                "cast(sum(sev_cnt)/sum(cpo_odr_cnt) as Double) as cpo," +
                "cast(sum(tel_duration) as Integer) as tel_duration," +
                "cast(sum(im_sev_cnt) as Integer) as im_sev_cnt," +//咨询次数
                "cast(sum(im_pre_sev_cnt) as Integer) as im_pre_sev_cnt," +//售前
                "cast(sum(im_no_pre_sev_cnt) as Integer) as im_no_pre_sev_cnt," +//售后
                "cast(sum(sev_value) as Integer) as sev_value" +//服务价值
                " from adm_sev_ttd_viewspot_file_index_df");
        List<PreparedParameterBean> parameters = new java.util.ArrayList<>();
        appendSightId(parameters, sql, sightId, needSubSight);
        appendQueryD(parameters, sql, queryD);
        appendDateRange(parameters, sql, dateType, startDate, endDate);
        appendBusinessType(parameters, sql, businessType);
        appendVendorIdList(parameters, sql, vendorIdList);
        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            log.warn("queryCpoAndPhone error", e);
        }
        if (CollectionUtils.isEmpty(result)) {
            return new HashMap<>();
        }
        return result.get(0);
    }
    //拼景点id
    private void appendSightId(List<PreparedParameterBean> parameters, StringBuilder sql, Long sightId, Boolean needSubSight){
        if(needSubSight){
            sql.append(" where viewspotid = ?");
        }else {
            sql.append(" where sub_viewspotid = ?");
        }
        parameters.add(new PreparedParameterBean(String.valueOf(sightId), Types.BIGINT));
    }
    //拼日期范围
    private void appendDateRange(List<PreparedParameterBean> parameters, StringBuilder sql, Integer dateType, String startDate, String endDate){


        sql.append(" and biz_date between ? and ?");

        parameters.add(new PreparedParameterBean(startDate, Types.VARCHAR));
        parameters.add(new PreparedParameterBean(endDate, Types.VARCHAR));

    }

    //拼业务类型
    private void appendBusinessType(List<PreparedParameterBean> parameters, StringBuilder sql, Integer businessType){
        if(businessType != 1){
            sql.append(" and bu_type = ?");
            parameters.add(new PreparedParameterBean(businessType==2?"门票":"活动", Types.VARCHAR)); //NOSONAR
        }

    }
    //拼供应商id列表
    private void appendVendorIdList(List<PreparedParameterBean> parameters, StringBuilder sql, List<Long> vendorIdList){
        if(CollectionUtils.isNotEmpty(vendorIdList)){
            sql.append(" and provider_id in (");
            for(int i = 0; i < vendorIdList.size(); i++){
                if(i == 0){
                    sql.append("?");
                }else {
                    sql.append(",?");
                }
                parameters.add(new PreparedParameterBean(String.valueOf(vendorIdList.get(i)), Types.BIGINT));
            }
            sql.append(")");
        }
    }

    public Map<String, Object> queryCpoYoy(String queryD, Long sightId, Boolean needSubSight, String startDate, String endDate, Integer dateType, Integer businessType, List<Long> vendorIdList) {
        StringBuilder sql = new StringBuilder("select " +
                " sum(sev_cnt)/sum(cpo_odr_cnt) as cpo" +
                " from adm_sev_ttd_viewspot_file_index_df t1 inner join v_dim_date t2 on t1.biz_date = t2.date_lastyear");
        List<PreparedParameterBean> parameters = new java.util.ArrayList<>();
        appendSightId(parameters, sql, sightId, needSubSight);
        sql.append(" and t1.d = ?");
        parameters.add(new PreparedParameterBean(queryD, Types.VARCHAR));
        appendDateRangeOfLastYear(parameters, sql, startDate, endDate);
        appendBusinessType(parameters, sql, businessType);
        appendVendorIdList(parameters, sql, vendorIdList);
        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            log.warn("queryCpoYoy error", e);
        }
        if (CollectionUtils.isEmpty(result)) {
            return new HashMap<>();
        }
        return result.get(0);
    }
    private void appendDateRangeOfLastYear(List<PreparedParameterBean> lyparameters, StringBuilder sql, String startDate, String endDate) {
        sql.append(" and t2.date_solar between ? and ?");
        lyparameters.add(new PreparedParameterBean(startDate, Types.VARCHAR));
        lyparameters.add(new PreparedParameterBean(endDate, Types.VARCHAR));
    }

//    public Map<String, Object> queryCpoPop(String queryD, Long sightId, Boolean needSubSight, String startDate, String endDate, Integer dateType, Integer businessType, List<Long> vendorIdList) {
//        StringBuilder sql = new StringBuilder("select " +
//                " cast(sum(sev_cnt)/sum(cpo_odr_cnt) as Double) as cpo" +
//                " from adm_sev_ttd_viewspot_file_index_df");
//        List<PreparedParameterBean> parameters = new java.util.ArrayList<>();
//        appendSightId(parameters, sql, sightId, needSubSight);
//        appendQueryD(parameters, sql, queryD);
//        try {
//            appendDateRange(parameters, sql, dateType, DateUtil.getPopTime(startDate, 1), DateUtil.getPopTime(endDate, 1));
//        } catch (ParseException e) {
//            throw new RuntimeException(e);
//        }
//        appendBusinessType(parameters, sql, businessType);
//        appendVendorIdList(parameters, sql, vendorIdList);
//        List<Map<String, Object>> result = null;
//        try {
//            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
//        } catch (Exception e) {
//            log.warn("queryCpoPop error", e);
//        }
//        return result.get(0);
//    }

    public List<Map<String, Object>> queryCommentTrend(String queryD,Long sightId, Boolean needSubSight, String startDate, String endDate, Integer dateType, Integer businessType, List<Long> vendorIdList) {
//        StringBuilder sql = new StringBuilder("select DATE_FORMAT(STR_TO_DATE(biz_date, '%Y-%m-%d'), '%Y%m') as date_time" +
        StringBuilder sql = new StringBuilder("select biz_date as date_time" +
                ",cast(sum(comment_cnt) as Integer) as comment_cnt" +
                ",cast(sum(comment_score)/sum(comment_cnt) as Double) as comment_score" +
                " from adm_sev_ttd_viewspot_file_index_df");
        List<PreparedParameterBean> parameters = new java.util.ArrayList<>();
        appendSightId(parameters, sql, sightId, needSubSight);
        appendQueryD(parameters, sql, queryD);
        appendDate(parameters, sql, dateType, startDate, endDate);
        appendBusinessType(parameters, sql, businessType);
        appendVendorIdList(parameters, sql, vendorIdList);
        sql.append(" group by date_time");
        sql.append(" order by date_time asc ");
        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            log.warn("queryCommentTrend error", e);
        }
        return result;
    }

    private void appendQueryD(List<PreparedParameterBean> parameters, StringBuilder sql, String queryD){
        sql.append(" and d = ?");
        parameters.add(new PreparedParameterBean(queryD, Types.VARCHAR));
    }

    private void appendDate(List<PreparedParameterBean> parameters, StringBuilder sql, Integer dateType, String startDate, String endDate) {
//        sql.append(" and biz_date between ? and ?");
//        parameters.add(new PreparedParameterBean(startDate, Types.VARCHAR));
//        parameters.add(new PreparedParameterBean(endDate, Types.VARCHAR));
        sql.append(" and biz_date between ? and ?");

        parameters.add(new PreparedParameterBean(startDate, Types.VARCHAR));
        parameters.add(new PreparedParameterBean(endDate, Types.VARCHAR));
    }// 上个月

    public List<VendorQuality> queryVendorQualityTable(String queryD,Long sightId, Boolean needSubSight, String startDate, String endDate, Integer dateType, Integer businessType, List<Long> vendorIdList,String rankColumn, String rankType) {
        StringBuilder sql = new StringBuilder("select CONCAT(provider_id, '-', provider_name) as vendorName," +
                "cast(sum(cpo_odr_cnt) as Integer) as paidOrderCount," +
                "cast(sum(weight_defect_cnt) as Double) as weightedDefectCount," +//加权缺陷数
                "cast(sum(weight_defect_cnt)/sum(cpo_odr_cnt) as Double) as weightedDefectRate," +//加权缺陷率
                "cast(sum(sev_cnt)/sum(cpo_odr_cnt) as Double) as cpo," +
                "cast(sum(sev_cnt) as Double) as eventCount," +//服务引发数
                "cast(sum(tel_sev_cnt) as Integer) as customerTelIntakeCount," +//进线量
                "cast(sum(im_sev_cnt) as Integer) as customerIMIntakeCount," +//进线量im
                "cast(sum(complain_cnt) as Integer) as complaintCount," +//投诉量
                "cast(sum(complain_cnt)/sum(odr_cnt) as Double) as complaintRate," +//投诉率
                "cast(sum(sev_value) as Integer) as serviceValueAmount," +//服务价值
                "cast(sum(trip_pay_amount) as Integer) as ctripPayoutAmount" +//赔款金额
                " from adm_sev_ttd_viewspot_file_index_df");
        List<PreparedParameterBean> parameters = new java.util.ArrayList<>();
        appendSightId(parameters, sql, sightId, needSubSight);
        appendQueryD(parameters, sql, queryD);
        appendDateRange(parameters, sql, dateType, startDate, endDate);
        appendBusinessType(parameters, sql, businessType);
        appendVendorIdList(parameters, sql, vendorIdList);
        sql.append(" and provider_id is not null and provider_id!=0");
        sql.append(" group by vendorName");
        sql.append(" order by ").append(rankColumn).append("asc".equals(rankType)?" asc ":" desc ");//升序还是降序
        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            log.warn("queryVendorQualityTable error", e);
        }
        if(CollectionUtils.isEmpty(result)){
            return new ArrayList<>();
        }

        List<VendorQuality> vendorQualityTableList = new ArrayList<>();

        for(Map<String, Object> map : result) {
            VendorQuality vendorQuality = new VendorQuality();
            vendorQuality.setVendorName((String) map.get("vendorName"));
            vendorQuality.setPaidOrderCount((Integer) map.get("paidOrderCount"));
            vendorQuality.setWeightedDefectCount((Double) map.get("weightedDefectCount"));
            vendorQuality.setWeightedDefectRate((Double) map.get("weightedDefectRate"));
            vendorQuality.setCpo((Double) map.get("cpo"));
            vendorQuality.setEventCount((Double) map.get("eventCount"));
            vendorQuality.setCustomerTelIntakeCount((Integer) map.get("customerTelIntakeCount"));
            vendorQuality.setCustomerIMIntakeCount((Integer) map.get("customerIMIntakeCount"));
            vendorQuality.setComplaintCount((Integer) map.get("complaintCount"));
            vendorQuality.setComplaintRate((Double) map.get("complaintRate"));
            vendorQuality.setServiceValueAmount((Integer) map.get("serviceValueAmount"));
            vendorQuality.setCtripPayoutAmount((Integer) map.get("ctripPayoutAmount"));
            vendorQualityTableList.add(vendorQuality);
//
        }


        return vendorQualityTableList;
    }

    public List<Map<String, Object>> querySightRankListOftouristevaluation(String queryD, List<Long> competitiveSightIdList, String startDate, String endDate, Integer businessType, List<Long> vendorIdList) {
        if (CollectionUtils.isEmpty(competitiveSightIdList)) {
            return new ArrayList<>();
        }
        StringBuilder sql = new StringBuilder("select sub_viewspotid,sub_viewspot_name" +
                ",cast(sum(comment_score)/sum(comment_cnt) as Double) as comment_score" +
                " from adm_sev_ttd_viewspot_file_index_df");
        List<PreparedParameterBean> parameters = new java.util.ArrayList<>();
        appendSightIdOfCompetitiveSight(parameters, sql, competitiveSightIdList);
        appendQueryD(parameters, sql, queryD);
        appendDateRange(parameters, sql, 1, startDate, endDate);
//        appendBusinessType(parameters, sql, businessType);
//        appendVendorIdList(parameters, sql, vendorIdList);
        sql.append(" group by sub_viewspotid,sub_viewspot_name");
        sql.append(" order by comment_score DESC");
        sql.append(" limit 10");
        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            log.warn("querySightRankListOftouristevaluation error", e);
        }
        return result;
    }

    private void appendSightIdOfCompetitiveSight(List<PreparedParameterBean> parameters, StringBuilder sql, List<Long> competitiveSightIdList) {
        if(CollectionUtils.isNotEmpty(competitiveSightIdList)){
            sql.append(" where sub_viewspotid in (");
            for(int i = 0; i < competitiveSightIdList.size(); i++){
                if(i == 0){
                    sql.append("?");
                }else {
                    sql.append(",?");
                }
                parameters.add(new PreparedParameterBean(String.valueOf(competitiveSightIdList.get(i)), Types.BIGINT));
            }
            sql.append(") ");
        }else {
            sql.append(" where sub_viewspotid = 0 ");
        }
    }

    public List<Map<String, Object>> querySightRankListOfcomplaintRate(String queryD,List<Long> competitiveSightIdList, String startDate, String endDate, Integer businessType, List<Long> vendorIdList) {
        if (CollectionUtils.isEmpty(competitiveSightIdList)) {
            return new ArrayList<>();
        }
        StringBuilder sql = new StringBuilder("select sub_viewspotid,sub_viewspot_name" +
                " ,cast(sum(complain_cnt)/sum(odr_cnt) as Double) as complaintRate" +
                " from adm_sev_ttd_viewspot_file_index_df");
        List<PreparedParameterBean> parameters = new java.util.ArrayList<>();
        appendSightIdOfCompetitiveSight(parameters, sql, competitiveSightIdList);
        appendQueryD(parameters, sql, queryD);
        appendDateRange(parameters, sql, 1, startDate, endDate);
//        appendBusinessType(parameters, sql, businessType);
//        appendVendorIdList(parameters, sql, vendorIdList);
        sql.append(" and odr_cnt>0 ");
        sql.append(" group by sub_viewspotid,sub_viewspot_name");
        sql.append(" order by complaintRate ASC");
        sql.append(" limit 10");
        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            log.warn("querySightRankListOfcomplaintRate error", e);
        }
        return result;

    }

    public List<Map<String, Object>> querySightRankListOfperformanceQuality(String queryD, List<Long> competitiveSightIdList, String startDate, String endDate, Integer businessType, List<Long> vendorIdList, Integer dateType) {
        StringBuilder sql = new StringBuilder("select sub_viewspotid,sub_viewspot_name" +
                " ,cast(sum(weight_defect_cnt)/sum(cpo_odr_cnt) as Double) as weightedDefectRate" +
                " from adm_sev_ttd_viewspot_file_index_df");
        List<PreparedParameterBean> parameters = new java.util.ArrayList<>();
        appendSightIdOfCompetitiveSight(parameters, sql, competitiveSightIdList);
        appendQueryD(parameters, sql, queryD);
        appendDateRange(parameters, sql, dateType, startDate, endDate);
        appendBusinessType(parameters, sql, businessType);
        appendVendorIdList(parameters, sql, vendorIdList);
        sql.append(" group by sub_viewspotid,sub_viewspot_name");
        sql.append(" order by weightedDefectRate ASC");
        sql.append(" limit 10");
        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            log.warn("querySightRankListOfperformanceQuality error", e);
        }
        return result;
    }

    public Map<String, Object> queryRadarScore(String queryD,Long sightId, Integer dateType, String startDate, String endDate, List<Long> vendorIdList, Integer businessType, Boolean needSubSight) {
        StringBuilder sql = new StringBuilder("select " +
                "cast(sum(comment_score)/sum(comment_cnt) as Double) as score" +//点评分
                " from adm_sev_ttd_viewspot_file_index_df");
        List<PreparedParameterBean> parameters = new java.util.ArrayList<>();
        appendSightId(parameters, sql, sightId, needSubSight);
        appendQueryD(parameters, sql, queryD);
        appendDateRange(parameters, sql, dateType, startDate, endDate);
        appendBusinessType(parameters, sql, businessType);
        appendVendorIdList(parameters, sql, vendorIdList);
        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            log.warn("queryRadarScore error", e);
        }
        if (CollectionUtils.isEmpty(result)) {
            return new HashMap<>();
        }
        return result.get(0);
    }

    public Map<String, Object> queryRadarweightedDefectRate(String queryD,Long sightId, Integer dateType, String startDate, String endDate, List<Long> vendorIdList, Integer businessType, Boolean needSubSight) {
        StringBuilder sql = new StringBuilder("select " +
                "cast(sum(weight_defect_cnt)/sum(cpo_odr_cnt) as Double) as weightedDefectRate" +//加权缺陷率
                " from adm_sev_ttd_viewspot_file_index_df");
        List<PreparedParameterBean> parameters = new java.util.ArrayList<>();
        appendSightId(parameters, sql, sightId, needSubSight);
        appendQueryD(parameters, sql, queryD);
        appendDateRange(parameters, sql, dateType, startDate, endDate);
        appendBusinessType(parameters, sql, businessType);
        appendVendorIdList(parameters, sql, vendorIdList);
        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            log.warn("queryRadarweightedDefectRate error", e);
        }
        if (CollectionUtils.isEmpty(result)) {
            return new HashMap<>();
        }
        return result.get(0);
    }

    public Map<String, Object> queryRadarweightedDefectRateAverage(String queryD,Long sightId, Integer dateType, String startDate, String endDate, List<Long> vendorIdList, Integer businessType) {
        StringBuilder sql = new StringBuilder("select " +
                "cast(sum(weight_defect_cnt)/sum(cpo_odr_cnt) as Double)/COUNT(DISTINCT sub_viewspotid) as weightedDefectRate" +//加权缺陷率
                " from adm_sev_ttd_viewspot_file_index_df where d = ? and biz_date between ? and ?");
        List<PreparedParameterBean> parameters = new java.util.ArrayList<>();
        parameters.add(new PreparedParameterBean(queryD, Types.VARCHAR));
        parameters.add(new PreparedParameterBean(startDate, Types.VARCHAR));
        parameters.add(new PreparedParameterBean(endDate, Types.VARCHAR));
//        appendSightId(parameters, sql, sightId, false);
//        appendDateRange(parameters, sql, dateType, startDate, endDate);
//        appendBusinessType(parameters, sql, businessType);
//        appendVendorIdList(parameters, sql, vendorIdList);
        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            log.warn("queryRadarweightedDefectRateAverage error", e);
        }
        if (CollectionUtils.isEmpty(result)) {
            return new HashMap<>();
        }
        return result.get(0);
    }
}
