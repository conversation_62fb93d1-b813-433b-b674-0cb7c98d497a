package com.ctrip.tour.business.dashboard.sightArchives.entity.domesticMetricEntity;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;

@Entity
@Getter
@Setter
public class CdmOrdTtdDashboardExamLevelPerfDfBO {
    //业务线
    String buType;
    //使用月份
    String useDate;
    //使用季度
    String useMonth;
    //使用半年
    String useQuarter;
    //使用年份
    String useHalfYear;
    //业务国家名称
    String businessDomainName;
    //业务大区名称
    String businessRegionName;
    //业务子区域名称
    String businessSubRegionName;
    //产品经理
    String prdMeid;
    String prdMeidName;
    String vstName;
    Long vstId;
    //考核层级
    String examineLevel;
    //考核范围
    String examineRange;
    //订单收入
    Double ttdSucIncome;
    //系统内毛利
    Double ttdSysInnerProfit;
    //系统外毛利
    Double ttdSysOuterProfit;
    //订单毛利
    Double ttdSucProfit;
}
