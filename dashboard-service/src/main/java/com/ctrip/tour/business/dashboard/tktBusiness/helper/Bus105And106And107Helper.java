package com.ctrip.tour.business.dashboard.tktBusiness.helper;

import com.ctrip.soa._24922.*;
import com.ctrip.soa._27181.GetRawDataResponseType;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.CdmPrdTktDashboardWeaknessStatisticsBO;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.bean.CdmPrdTktDashboardWeaknessParamBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.*;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.enums.CompetitionStatisTypeEnum;
import com.ctrip.tour.business.dashboard.tktBusiness.enums.CompetitorTypeEnumEnum;
import com.ctrip.tour.business.dashboard.tktBusiness.enums.TrendDrillTypeEnum;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.InputArgumentException;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.ctrip.tour.business.dashboard.utils.MapperUtil;
import com.ctrip.tour.business.dashboard.utils.UserUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;
import java.util.*;

/**
 * 1 邮箱前缀,角色
 * 2 邮箱前缀,角色,子区域,大区 (按商拓下钻)
 * 3 考核对象，角色,大区 (按大区下钻)
 * 4 考核对象,角色,子区域,大区 (按子区域下钻)
 * 5 考核对象,角色,子区域,大区,国家/地区 (按国家/地区下钻)
 */
public class Bus105And106And107Helper {


    private static Map<String, Long> idMap = new HashMap<>();

    private static Map<String, Double> tagretMap = new HashMap<>();


    static {
        idMap.put("tklk", 40L);
        idMap.put("tfly", 41L);
        tagretMap.put("105", 0d);
        tagretMap.put("106", 0d);
        tagretMap.put("107", 0.03d);
    }



    /**
     * 填充首页指标卡所需的下钻信息
     * @param metricDetailInfo 指标卡信息
     * @param subMetric 子指标
     * @param metricInfoBean 海外指标配置
     * @param remoteConfig  qconfig配置
     */
    public static void setMetricCardDrillDownParmater(MetricDetailInfo metricDetailInfo,
                                                      String subMetric,
                                                      OverseaMetricInfoBean metricInfoBean,
                                                      RemoteConfig remoteConfig){
        Bus101102Helper.setMetricCardDrillDownParmater(metricDetailInfo, subMetric, metricInfoBean, remoteConfig);
    }

    /**
     * 填充首页指标卡所需的下钻信息V2
     *
     * @param oveaseaSubMetric 指标卡信息
     * @param subMetric        子指标
     * @param metricInfoBean   海外指标配置
     * @param remoteConfig     qconfig配置
     */
    public static void setMetricCardDrillDownParmaterV2(OveaseaSubMetric oveaseaSubMetric,
                                                        String subMetric,
                                                        OverseaMetricInfoBeanV2 metricInfoBean,
                                                        RemoteConfig remoteConfig,
                                                        TimeFilter timeFilter) {
        Bus101102Helper.setMetricCardDrillDownParmaterV2(oveaseaSubMetric, subMetric, metricInfoBean, remoteConfig, timeFilter);
    }





    /**
     * 获取指标卡请求参数
     * @param timeFilter
     * @param metricInfoBean
     * @param d
     * @param metric
     * @param subMetric
     * @param remoteConfig
     * @param examineConfigBean
     * @return
     */
    public static SqlParamterBean getMetricCardSqlBean(TimeFilter timeFilter,
                                                       OverseaMetricInfoBean metricInfoBean,
                                                       String d,
                                                       String metric,
                                                       String subMetric,
                                                       RemoteConfig remoteConfig,
                                                       ExamineConfigBean examineConfigBean) throws Exception {
        SqlParamterBean bean = new SqlParamterBean();
        Map<String, String> baseMap = OverseaMetricHelper.generateQuarterMetricBaseMap(metric, timeFilter, "current", d, examineConfigBean);
        baseMap.put("statistics_dim_id", "1");
        String destinationLevel = metricInfoBean.getDestinationLevel();
        baseMap.put("examine_level_id", getExamineLevelId(destinationLevel, null, remoteConfig));
        baseMap.put("examinee", metricInfoBean.getDomainName());
        baseMap.put("metric", metric);
        bean.setId(getId(subMetric));
        bean.setAndMap(baseMap);
        return bean;
    }

    /**
     * 构建商品力竞争请求参数
     * @param timeFilter
     * @param request
     * @param domainName
     * @return
     */
    public static OverseasRelatedSearchParamBean generateOverseaInfoSearch(TimeFilter timeFilter,
                                                                           GetOverseaTrendLineDataV2RequestType request,
                                                                           String domainName,
                                                                           String d,
                                                                           String metric){
        String year = timeFilter.getYear();
        String dateType = timeFilter.getDateType();
        // H1获取Q1和Q2，H3获取Q3和Q4，季度维度保持原状
        List<String> quarterList = "quarter".equals(dateType) ? Collections.singletonList(timeFilter.getQuarter()) : DateUtil.getQuarterOfHalf(timeFilter.getHalf());
        OverseasRelatedSearchParamBean overseasRelatedSearchParamBean = new OverseasRelatedSearchParamBean();

        overseasRelatedSearchParamBean.setYear(year);
        overseasRelatedSearchParamBean.setDomainName(domainName);
        overseasRelatedSearchParamBean.setQuarters(quarterList);
        overseasRelatedSearchParamBean.setD(d);
        overseasRelatedSearchParamBean.setExamineType(metric);

        generateSearchParamInfoWithStatisticDim(overseasRelatedSearchParamBean, request);

        return overseasRelatedSearchParamBean;
    }

    /**
     * 覆盖商品力下钻维度构建
     * @param overseasRelatedSearchParamBean
     * @param requestType
     */
    public static void generateSearchParamInfoWithStatisticDim(OverseasRelatedSearchParamBean overseasRelatedSearchParamBean, GetOverseaTrendLineDataV2RequestType requestType) {
        if (requestType == null || !TrendDrillTypeEnum.DRILL_DOWN.getName().equals(requestType.getQueryType())) {
            overseasRelatedSearchParamBean.setStatisticsDimId("1");
            return;
        }
        switch (requestType.getDimName()) {
            case "region":
                overseasRelatedSearchParamBean.setStatisticsDimId(String.valueOf(CompetitionStatisTypeEnum.REGION.getCode()));
                overseasRelatedSearchParamBean.setBusinessRegionName(requestType.getDimValueList());
                overseasRelatedSearchParamBean.setDomainName(null);
                overseasRelatedSearchParamBean.setDimValue(requestType.getDimName());
                break;
            case "province":
                overseasRelatedSearchParamBean.setStatisticsDimId(String.valueOf(CompetitionStatisTypeEnum.PROVINCE.getCode()));
                overseasRelatedSearchParamBean.setBusinessSubRegionNames(requestType.getDimValueList());
                overseasRelatedSearchParamBean.setDomainName(null);
                overseasRelatedSearchParamBean.setDimValue(requestType.getDimName());
                break;
            case "country":
                overseasRelatedSearchParamBean.setStatisticsDimId(String.valueOf(CompetitionStatisTypeEnum.COUNTRY.getCode()));
                overseasRelatedSearchParamBean.setCountryNames(requestType.getDimValueList());
                overseasRelatedSearchParamBean.setDomainName(null);
                overseasRelatedSearchParamBean.setDimValue(requestType.getDimName());
                break;
            case "examinee":
                overseasRelatedSearchParamBean.setStatisticsDimId(String.valueOf(CompetitionStatisTypeEnum.EXAMINEE.getCode()));
                overseasRelatedSearchParamBean.setDimValue(requestType.getDimName());
                overseasRelatedSearchParamBean.setDomainNameList(requestType.getDimValueList());
                break;
            default:
                overseasRelatedSearchParamBean.setStatisticsDimId(String.valueOf(CompetitionStatisTypeEnum.METRIC_CARD.getCode()));
        }
    }


    /**
     * 填充指标卡基础数据
     * @param res
     * @param dimMap
     */
    public static void processMetricCardData(GetRawDataResponseType res,
                                             Map<String, Double> dimMap) {
        ChartHelper.fillOverallDimMap(MapperUtil.str2ListList(res.getResult(), Object.class), res.getMetricList(), dimMap);
    }





    /**
     * 获取下钻趋势线请求参数
     * @param metricInfoBean
     * @param d
     * @param remoteConfig
     * @param examineConfigBean
     * @return
     */
    public static SqlParamterBean getTrendlineDrilldownSqlBean(GetOverseaTrendLineDataRequestType request,
                                                               OverseaMetricInfoBean metricInfoBean,
                                                               String d,
                                                               RemoteConfig remoteConfig,
                                                               ExamineConfigBean examineConfigBean) throws Exception {
        SqlParamterBean bean = new SqlParamterBean();
        String metric = request.getMetric();
        String subMetric = request.getSubMetric();
        Map<String, String> baseMap = OverseaMetricHelper.generateQuarterMetricBaseMap(metric, null, "current", d, examineConfigBean);


        DrillDownFilter drillDownFilter = request.getDrillDownFilter();
        String field = drillDownFilter.getField();
        SubMetricFiledBean configBean = remoteConfig.getSubMetricFiledBean(metric, subMetric, field);
        if (!configBean.getNeedLine()) {
            throw new InputArgumentException("this param is not support drilldown:metric:" + metric + ",subMetric:" + subMetric + ",field:" + field);
        }

        bean.setId(getId(subMetric));
        baseMap.put("metric", metric);
        baseMap.put("statistics_dim_id", getStatisticsDimId(field));
        String destinationLevel = metricInfoBean.getDestinationLevel();
        baseMap.put("examine_level_id", getExamineLevelId(destinationLevel, field, remoteConfig));


        List<String> lineGroupList = configBean.getLineGroupListMap().get("current");
        bean.setGroupList(OverseaMetricHelper.translateGroupList(lineGroupList));

        OverseaMetricHelper.setDestinationRangeValue(baseMap, metricInfoBean, remoteConfig);
        //设置前端传入的条件
        OverseaMetricHelper.setConditionValue(baseMap, configBean, drillDownFilter.getFieldValueList());

        bean.setAndMap(baseMap);
        return bean;
    }



    /**
     * 填充趋势线基础数据(非下钻)
     * @param trendLineDetailInfoList
     * @param periodDataBean
     * @param timeList
     */
    public static void processTrendLineData(List<TrendLineDetailInfo> trendLineDetailInfoList,
                                            PeriodDataBean periodDataBean,
                                            List<String> timeList,
                                            String metric) {
        Map<String, Double> dimMap = new HashMap<>();
        Set<String> drillDownSet = new HashSet<>();
        List<List<Object>> weaknessList = periodDataBean.getWeaknessList();
        List<String> weaknessHeaderList = periodDataBean.getWeaknessHeaderList();
        List<String> weaknessDimList = weaknessHeaderList.subList(1, weaknessHeaderList.size());

        ChartHelper.setMonthlyMetricTrendlineActualReach(dimMap, drillDownSet, weaknessList, Lists.newArrayList("time"), weaknessDimList);

        Map<String, String> typeMap = new HashMap<>();
        typeMap.put("cw_Inferiority_num", "lineChart");
        typeMap.put("cw_Inferiority_rate" , "lineChart");

        ChartHelper.fillLineChartTrendLineData(null, timeList, dimMap,
                trendLineDetailInfoList, typeMap);
        makeUpTrendlineData(trendLineDetailInfoList, metric);
    }


    /**
     * 补齐趋势线数据(非下钻)
     * @param trendLineDetailInfoList
     * @param metric
     */
    private static void makeUpTrendlineData(List<TrendLineDetailInfo> trendLineDetailInfoList,
                                           String metric) {
        List<TrendLineDetailInfo> gapTrendLineDetailInfoList =
                MapperUtil.str2List(MapperUtil.obj2Str(trendLineDetailInfoList), TrendLineDetailInfo.class);
        for (TrendLineDetailInfo info : gapTrendLineDetailInfoList) {
            String dim = info.getDim();
            if (!"cw_Inferiority_rate".equals(dim)) {
                continue;
            }
            String gapDim = dim + "_gap";
            info.setDim(gapDim);
            Double target = tagretMap.get(metric);
            List<TrendLineDataItem> itemList = info.getTrendLineDataItemList();
            for (TrendLineDataItem item : itemList) {
                Double value = item.getValue();
                if (!GeneralUtil.isEmpty(value)) {
                    item.setValue(value - target);
                }
            }
            trendLineDetailInfoList.add(info);
        }
    }


    /**
     * 填充趋势线数据(下钻)
     * @param trendLineDetailInfoList
     * @param periodDataBean
     * @param timeList
     */
    public static void processTrendLineDrillDownData(List<TrendLineDetailInfo> trendLineDetailInfoList,
                                                     PeriodDataBean periodDataBean,
                                                     List<String> timeList) {
        Map<String, Double> dimMap = new HashMap<>();
        Set<String> drillDownSet = new HashSet<>();
        List<List<Object>> weaknessList = periodDataBean.getWeaknessList();
        List<String> weaknessHeaderList = periodDataBean.getWeaknessHeaderList();
        List<String> reachGroupList = weaknessHeaderList.subList(0, 2);
        List<String> reachDimList = weaknessHeaderList.subList(2, weaknessHeaderList.size());

        ChartHelper.setMonthlyMetricTrendlineActualReach(dimMap, drillDownSet, weaknessList, reachGroupList, reachDimList);


        Map<String, String> typeMap = new HashMap<>();
        typeMap.put("cw_Inferiority_rate", "lineChart");

        ChartHelper.fillLineChartTrendLineDataWithDrillDown(null, timeList, dimMap, trendLineDetailInfoList,
                typeMap, drillDownSet, false);


    }




    /**
     * 获取下钻待下钻维度列表
     * @param subMetric
     * @param metricInfoBean
     * @param remoteConfig
     * @return
     */
    public static List<String> getDrillDownFieldList(String subMetric,
                                                     OverseaMetricInfoBean metricInfoBean,
                                                     RemoteConfig remoteConfig) {
        String oversea = remoteConfig.getConfigValue("oversea");
        String region = remoteConfig.getConfigValue("region");
        String subRegion = remoteConfig.getConfigValue("subRegion");
        String destinationLevel = metricInfoBean.getDestinationLevel();
        //海外  大区、子区域、国家/地区、商拓
        //多个大区  大区、子区域、国家/地区、商拓
        //一个大区  子区域、国家/地区、商拓
        //多个子区域 子区域、国家/地区、商拓
        //一个子区域 国家/地区、商拓
        if (oversea.equals(destinationLevel)) {
            return Lists.newArrayList("region", "province", "country", "examinee");
        }
        List<String> destinationRangeList = metricInfoBean.getDestinationRangeList();
        if (GeneralUtil.isNotEmpty(destinationRangeList)) {
            if (region.equals(destinationLevel) && destinationRangeList.size() > 1) {
                return Lists.newArrayList("region", "province", "country", "examinee");
            }

            if (region.equals(destinationLevel) && destinationRangeList.size() == 1) {
                return Lists.newArrayList("province", "country", "examinee");
            }

            if (subRegion.equals(destinationLevel) && destinationRangeList.size() > 1) {
                return Lists.newArrayList("province", "country", "examinee");
            }

            if (subRegion.equals(destinationLevel) && destinationRangeList.size() == 1) {
                return Lists.newArrayList("country", "examinee");
            }
        }
        throw new InputArgumentException("invalid subMetric:" + subMetric);
    }

    public static List<String> getDrillDownFieldListV2(String subMetric,
                                                     OverseaMetricInfoBean metricInfoBean,
                                                     RemoteConfig remoteConfig) {
        String oversea = remoteConfig.getConfigValue("oversea");
        String region = remoteConfig.getConfigValue("region");
        String subRegion = remoteConfig.getConfigValue("subRegion");
        //国家、地区、省份、城市
        String country = remoteConfig.getConfigValue("country");
        //景点经理、景点助理、景点
        String spot = remoteConfig.getConfigValue("spot");
        String destinationLevel = metricInfoBean.getDestinationLevel();
        //海外  大区、子区域、国家/地区、商拓
        //多个大区  大区、子区域、国家/地区、商拓
        //一个大区  子区域、国家/地区、商拓
        //多个子区域 子区域、国家/地区、商拓
        //一个子区域 国家/地区、商拓
        if (oversea.equals(destinationLevel)) {
            return Lists.newArrayList("region", "province", "country", "examinee");
        }
        List<String> destinationRangeList = metricInfoBean.getDestinationRangeList();
        if (GeneralUtil.isNotEmpty(destinationRangeList)) {
            if (region.equals(destinationLevel) && destinationRangeList.size() > 1) {
                return Lists.newArrayList("region", "province", "country", "examinee");
            }

            if (region.equals(destinationLevel) && destinationRangeList.size() == 1) {
                return Lists.newArrayList("province", "country", "examinee");
            }

            if (subRegion.equals(destinationLevel) && destinationRangeList.size() > 1) {
                return Lists.newArrayList("province", "country", "examinee");
            }

            if (subRegion.equals(destinationLevel) && destinationRangeList.size() == 1) {
                return Lists.newArrayList("country", "examinee");
            }
            //考核层级是国家、地区、省份、城市，1个子区域，展示子国家
            if (country.contains(destinationLevel) && destinationRangeList.size() > 1) {
                return Lists.newArrayList("country");
            }
            if (country.contains(destinationLevel) && destinationRangeList.size() == 1) {
                return Lists.newArrayList();
            }
//考核层级是景点经理、景点助理、景点，展示景点
            if (spot.contains(destinationLevel)) {
                return Lists.newArrayList();
            }

        }
        throw new InputArgumentException("invalid subMetric:" + subMetric);
    }

    /**
     * 获取下钻基础信息请求参数
     * @param field
     * @param request
     * @param d
     * @param metricInfoBean
     * @param remoteConfig
     * @return
     * @throws Exception
     */
    public static SqlParamterBean getDrillDownBaseInfoSqlBean(String field,
                                                              GetOverseaDrillDownBaseInfoRequestType request,
                                                              String d,
                                                              OverseaMetricInfoBean metricInfoBean,
                                                              RemoteConfig remoteConfig) throws Exception {
        SqlParamterBean bean = new SqlParamterBean();

        String metric = request.getMetric();
        String subMetric = request.getSubMetric();

        SubMetricFiledBean configBean = remoteConfig.getSubMetricFiledBean(metric, subMetric, field);

        bean.setId(getId(subMetric));



        Map<String, String> baseMap = OverseaMetricHelper.generateQuarterMetricBaseMap(metric, request.getTimeFilter(), "current", d, null);
        OverseaMetricHelper.setDestinationRangeValue(baseMap, metricInfoBean, remoteConfig);

        baseMap.put("statistics_dim_id", getStatisticsDimId(field));
        String destinationLevel = metricInfoBean.getDestinationLevel();
        baseMap.put("examine_level_id", getExamineLevelId(destinationLevel, field, remoteConfig));
        baseMap.put("metric", metric);



        bean.setAndMap(baseMap);

        List<String> groupList = configBean.getBaseInfoGroupList();
        List<String> translateGroupList = OverseaMetricHelper.translateGroupList(groupList);
        bean.setGroupList(translateGroupList);
        bean.setOrderList(Lists.newArrayList(translateGroupList.get(0)));
        bean.setOrderTypeList(Lists.newArrayList("asc"));

        if (request.isNeedSearch()) {
            //likeMap的key:value对格式为：group1|group2|group3:searchWord
            String searchWord = request.getSearchWord();
            Map<String, String> likeMap = new HashMap<>();
            List<Integer> likeIndexList = configBean.getBaseInfoLikeIndexList();
            StringBuilder likeKeySb = new StringBuilder();
            for (Integer index : likeIndexList) {
                likeKeySb.append(translateGroupList.get(index)).append("|");
            }
            likeKeySb.deleteCharAt(likeKeySb.length() - 1);
            likeMap.put(likeKeySb.toString(), searchWord);
            bean.setLikeMap(likeMap);
        }

        return bean;
    }


    /**
     * 获取下钻基础信息请求参数
     * @param field
     * @param request
     * @param d
     * @param metricInfoBean
     * @param remoteConfig
     * @return
     * @throws Exception
     */
    public static SqlParamterBean getDrillDownBaseInfoSqlBeanV2(String field,
                                                              GetOverseaDrillDownBaseInfoV2RequestType request,
                                                              String d,
                                                              OverseaMetricInfoBean metricInfoBean,
                                                              RemoteConfig remoteConfig) throws Exception {
        SqlParamterBean bean = new SqlParamterBean();

        String metric = request.getMetric();
        String subMetric = request.getSubMetric();

        SubMetricFiledBean configBean = remoteConfig.getSubMetricFiledBean(metric, subMetric, field);

        bean.setId(getId(subMetric));



        Map<String, String> baseMap = OverseaMetricHelper.generateQuarterMetricBaseMap(metric, request.getTimeFilter(), "current", d, null);
        OverseaMetricHelper.setDestinationRangeValue(baseMap, metricInfoBean, remoteConfig);

        baseMap.put("statistics_dim_id", getStatisticsDimId(field));
        String destinationLevel = metricInfoBean.getDestinationLevel();
        baseMap.put("examine_level_id", getExamineLevelId(destinationLevel, field, remoteConfig));
        baseMap.put("metric", metric);



        bean.setAndMap(baseMap);

        List<String> groupList = configBean.getBaseInfoGroupList();
        List<String> translateGroupList = OverseaMetricHelper.translateGroupList(groupList);
        bean.setGroupList(translateGroupList);
        bean.setOrderList(Lists.newArrayList(translateGroupList.get(0)));
        bean.setOrderTypeList(Lists.newArrayList("asc"));

        if (StringUtils.isNotEmpty(request.getSearchWord())) {
            //likeMap的key:value对格式为：group1|group2|group3:searchWord
            String searchWord = request.getSearchWord();
            Map<String, String> likeMap = new HashMap<>();
            List<Integer> likeIndexList = configBean.getBaseInfoLikeIndexList();
            StringBuilder likeKeySb = new StringBuilder();
            for (Integer index : likeIndexList) {
                likeKeySb.append(translateGroupList.get(index)).append("|");
            }
            likeKeySb.deleteCharAt(likeKeySb.length() - 1);
            likeMap.put(likeKeySb.toString(), searchWord);
            bean.setLikeMap(likeMap);
        }

        return bean;
    }

    /**
     * 填充下钻基础数据
     *
     * @param request
     * @param field
     * @param response
     * @param fieldDataItem
     * @param remoteConfig
     */
    public static void processDrillDownBaseInfo(GetOverseaDrillDownBaseInfoRequestType request,
                                                String field,
                                                GetRawDataResponseType response,
                                                FieldDataItem fieldDataItem,
                                                RemoteConfig remoteConfig) {
        Bus101102Helper.processDrillDownBaseInfo(request, field, response, fieldDataItem, remoteConfig);
    }




    /**
     * 获取表格数据请求参数
     *
     * @param request
     * @param d
     * @param metricInfoBean
     * @param configBean
     * @param remoteConfig
     * @return
     * @throws Exception
     */

    public static SqlParamterBean getTableDataSqlBean(GetOverseaTableDataRequestType request,
                                                      String d,
                                                      OverseaMetricInfoBean metricInfoBean,
                                                      SubMetricFiledBean configBean,
                                                      RemoteConfig remoteConfig) throws Exception {
        SqlParamterBean bean = new SqlParamterBean();

        String metric = request.getMetric();
        String subMetric = request.getSubMetric();

        List<String> otherGroupList = configBean.getTableGroupListMap().get("other");
        List<String> groupList = configBean.getTableGroupListMap().get("current");
        bean.setGroupList(GeneralUtil.isEmpty(groupList) ? OverseaMetricHelper.translateGroupList(otherGroupList)
                : OverseaMetricHelper.translateGroupList(groupList));


        bean.setId(getId(subMetric));

        Map<String, String> baseMap = OverseaMetricHelper.generateQuarterMetricBaseMap(metric, request.getTimeFilter(), "current", d, null);
        OverseaMetricHelper.setDestinationRangeValue(baseMap, metricInfoBean, remoteConfig);

        DrillDownFilter drillDownFilter = request.getDrillDownFilter();
        //设置前端传入的条件
        OverseaMetricHelper.setConditionValue(baseMap, configBean, drillDownFilter.getFieldValueList());
        String field = drillDownFilter.getField();

        baseMap.put("statistics_dim_id", getStatisticsDimId(field));
        String destinationLevel = metricInfoBean.getDestinationLevel();
        baseMap.put("examine_level_id", getExamineLevelId(destinationLevel, field, remoteConfig));
        baseMap.put("metric", metric);

        OverseaMetricHelper.setDestinationRangeValue(baseMap, metricInfoBean, remoteConfig);


        bean.setPageNo(request.getPageNo());
        bean.setPageSize(request.getPageSize());
        bean.setOrderList(Lists.newArrayList("cw_Inferiority_rate", configBean.getConditionColumn()));
        bean.setOrderTypeList(Lists.newArrayList("desc", "desc"));
        bean.setAndMap(baseMap);

        return bean;
    }

    public static CdmPrdTktDashboardWeaknessParamBean getTableDataSqlBeanV2(GetOverseaTableDataV2RequestType request,
                                                                            String d,
                                                                            OverseaMetricInfoBean metricInfoBean) throws Exception {
        CdmPrdTktDashboardWeaknessParamBean paramBean = new CdmPrdTktDashboardWeaknessParamBean();
        paramBean.setD(d);
        paramBean.setPageIndex(request.getPageNo());
        if (request.getPageSize() != null) {
            paramBean.setPageSize(request.getPageSize() / 3);
        }
        paramBean.setDimName(request.getDimName());
        TimeFilter timeFilter = request.getTimeFilter();
        String metric = request.getMetric();
        String year = timeFilter.getYear();
        String dateType = timeFilter.getDateType();
        // H1获取Q1和Q2，H3获取Q3和Q4，季度维度保持原状
        List<String> quarterList = "quarter".equals(dateType) ? Collections.singletonList(timeFilter.getQuarter()) : DateUtil.getQuarterOfHalf(timeFilter.getHalf());

        paramBean.setExamineYear(year);
        paramBean.setExamineType(NumberUtils.toInt(metric));
        paramBean.setExamineQuarter(quarterList);
        //先在考核范围内查询，若有下钻维度限制，限制下钻维度
        if (metricInfoBean.getDestinationLevel().contains("大区")) {//NOSONAR
            paramBean.setBusinessRegionName(metricInfoBean.getDestinationRangeList());
        }
        if (metricInfoBean.getDestinationLevel().contains("子区域")) {//NOSONAR
            paramBean.setBusinessSubRegionName(metricInfoBean.getDestinationRangeList());
        }
        if (metricInfoBean.getDestinationLevel().contains("国家/地区")) {//NOSONAR
            paramBean.setCountryName(metricInfoBean.getDestinationRangeList());
        }
        if (metricInfoBean.getDestinationLevel().contains("省份")) {//NOSONAR
            paramBean.setCountryName(metricInfoBean.getDestinationRangeList());
        }
        if (metricInfoBean.getDestinationLevel().contains("城市")) {//NOSONAR
            paramBean.setCountryName(metricInfoBean.getDestinationRangeList());
        }
        Boolean useZhCN="zh-CN".equalsIgnoreCase(UserUtil.getVbkLocale());
        switch (request.getDimName()) {
            case "region":
                paramBean.setStatisticsDimId(CompetitionStatisTypeEnum.REGION.getCode());
                if (CollectionUtils.isNotEmpty(request.getDimValueList())) {
                    if(useZhCN){
                        paramBean.setBusinessRegionName(request.getDimValueList());
                    }else{
                        paramBean.setBusinessRegionNameEn(request.getDimValueList());
                    }
                }
                break;
            case "province":
                paramBean.setStatisticsDimId(CompetitionStatisTypeEnum.PROVINCE.getCode());
                if (CollectionUtils.isNotEmpty(request.getDimValueList())) {
                    if(useZhCN){
                        paramBean.setBusinessSubRegionName(request.getDimValueList());
                    }else {
                        paramBean.setBusinessRegionNameEn(request.getDimValueList());
                    }
                }
                break;
            case "country":
                paramBean.setStatisticsDimId(CompetitionStatisTypeEnum.COUNTRY.getCode());
                if (CollectionUtils.isNotEmpty(request.getDimValueList())) {
                    if(useZhCN){
                        paramBean.setCountryName(request.getDimValueList());
                    }else {
                        paramBean.setCountryNameEn(request.getDimValueList());
                    }

                }
                break;
            case "examinee":
                paramBean.setStatisticsDimId(CompetitionStatisTypeEnum.EXAMINEE.getCode());
                if (CollectionUtils.isNotEmpty(request.getDimValueList())) {
                    paramBean.setDomainNames(request.getDimValueList());
                }
                break;
        }
        return paramBean;
    }

    /**
     * 填充表格基础数据
     * @param currentRes
     * @param tableDataItemList
     */
    public static void processTableBaseData(GetRawDataResponseType currentRes,
                                            List<TableDataItem> tableDataItemList) {
        List<List<Object>> currentList = MapperUtil.str2ListList(currentRes.getResult(), Object.class);
        //还原翻译字段
        List<String> currentGroupList = OverseaMetricHelper.revertGroupList(currentRes.getGroupList());
        List<String> currentMetricList = currentRes.getMetricList();

        ChartHelper.fillCommmonTableDataV2(tableDataItemList,
                currentGroupList,
                new ArrayList<>(),
                currentMetricList,
                new ArrayList<>(),
                currentList,
                new ArrayList<>());

    }


    /**
     * 计算表格平均数据
     * @param tableDataItemList
     * @param gapDays
     */
    public static void calTableAverageData(List<TableDataItem> tableDataItemList,
                                           Integer gapDays) {
        for (TableDataItem item : tableDataItemList) {
            Map<String, Double> dimMap = item.getDimMap();
            calMetricCardAverageData(dimMap, gapDays);
        }
    }


    /**
     * 补齐表格数据
     * @param tableDataItemList
     */
    public static void makeUpTableData(List<TableDataItem> tableDataItemList,
                                       String metric) {
        for (TableDataItem item : tableDataItemList) {
            Map<String, Double> dimMap = item.getDimMap();
            makeUpMetricData(dimMap, metric);
        }
    }


    /**
     * 生成下钻表格表头
     * @param configBean
     * @return
     */
    public static List<String> getTableHeaderList(SubMetricFiledBean configBean) {
        List<String> tableHeaderList = new ArrayList<>();
        tableHeaderList.addAll(configBean.getHeaderFieldList());
        tableHeaderList.add("targetValue");
        tableHeaderList.add("completeValue");
        tableHeaderList.add("inferiorNum");
        tableHeaderList.add("gapValue");
        return tableHeaderList;
    }



    /**
     * 获取当前查询条件下应该除以的天数
     * @param timeFilter
     * @param examineConfigBean
     * @param d
     * @return
     * @throws ParseException
     */
    public static Integer getGapDays(TimeFilter timeFilter,
                                     ExamineConfigBean examineConfigBean,
                                     String d) throws ParseException {
        return Bus11Helper.getGapDays(timeFilter, examineConfigBean, d);
    }


    /**
     * 获取查询数据时是否需要扣减额外的天数
     *
     * @param timeFilter
     * @param examineConfigBean
     * @param d
     * @param baseMap
     * @return
     * @throws ParseException
     */
    public static Integer getAddtionalGapDay(TimeFilter timeFilter,
                                             ExamineConfigBean examineConfigBean,
                                             String d,
                                             Map<String, String> baseMap) throws ParseException {
        if (GeneralUtil.isNotEmpty(timeFilter)) {
            return Bus567Helper.getAddtionalGapDay(null, baseMap, timeFilter.getYear(), timeFilter.getDateType(),
                    timeFilter.getMonth(), timeFilter.getQuarter(), timeFilter.getHalf(), d);
        } else {
            return Bus567Helper.getAddtionalGapDay(null, baseMap, examineConfigBean.getYear(), examineConfigBean.getDateType(),
                    examineConfigBean.getMonth(), examineConfigBean.getQuarter(), "", d);
        }

    }





    /**
     * 计算均值
     * @param dimMap
     * @param gapDays
     */
    public static void calMetricCardAverageData(Map<String, Double> dimMap,
                                                Integer gapDays) {
        for (Map.Entry<String, Double> entry : dimMap.entrySet()) {
            String key = entry.getKey();
            Double value = entry.getValue();
            if (!GeneralUtil.isEmpty(value)) {
                dimMap.put(key, value / gapDays);
            }
        }
    }

    /**
     * 计算海外覆盖商品力劣势率和劣势数的均值V2
     * 优化点：提取重复逻辑、增强空安全、优化结构
     */
    public static CompetitiveType calculateTrendLineData(CompetitorTypeEnumEnum klkOrFly,
                                                         OverseasCompetitionResponseBean responseBean,
                                                         RemoteConfig remoteConfig,
                                                         GetOverseaTrendLineDataV2RequestType request) {
        if (responseBean == null) {
            return new CompetitiveType();
        }

        TimeFilter timeFilter = request.getTimeFilter();
        String metric = request.getMetric();

        CompetitiveType competitiveType = new CompetitiveType();
        String timeType = getTimeType(timeFilter);

        // 统一处理三种类型的数据
        competitiveType.setCoreInfo(processCompetitiveData(
                responseBean.getCwRate(), responseBean.getCwNum(),
                remoteConfig, timeFilter, metric, klkOrFly.getName(), "core", timeType
        ));

        competitiveType.setFocusInfo(processCompetitiveData(
                responseBean.getFwRate(), responseBean.getFwNum(),
                remoteConfig, timeFilter, metric, klkOrFly.getName(), "focus", timeType
        ));

        competitiveType.setGeneralInfo(processCompetitiveData(
                responseBean.getTwRate(), responseBean.getTwNum(),
                remoteConfig, timeFilter, metric, klkOrFly.getName(), "general", timeType
        ));

        return competitiveType;
    }

    /**
     * 计算海外覆盖商品力劣势率和劣势数的均值V2
     * 优化点：提取重复逻辑、增强空安全、优化结构
     */
    public static void calculateMetricCardData(CompetitorTypeEnumEnum klkOrFly,
                                                  OverseasCompetitionResponseBean responseBean,
                                                  OveaseaSubMetric oveaseaSubMetric,
                                                  RemoteConfig remoteConfig,
                                                  TimeFilter timeFilter,
                                                  String metric) {
        if (responseBean == null) {
            if (CompetitorTypeEnumEnum.KLK == klkOrFly) {
                oveaseaSubMetric.setKlkCompetitiveData(new CompetitiveType());
            } else {
                oveaseaSubMetric.setFlyCompetitiveData(new CompetitiveType());
            }
            return;
        }

        CompetitiveType competitiveType = new CompetitiveType();
        String timeType = getTimeType(timeFilter);

        // 统一处理三种类型的数据
        competitiveType.setCoreInfo(processCompetitiveData(
                responseBean.getCwRate(), responseBean.getCwNum(),
                remoteConfig, timeFilter, metric, klkOrFly.getName(), "core", timeType
        ));

        competitiveType.setFocusInfo(processCompetitiveData(
                responseBean.getFwRate(), responseBean.getFwNum(),
                remoteConfig, timeFilter, metric, klkOrFly.getName(), "focus", timeType
        ));

        competitiveType.setGeneralInfo(processCompetitiveData(
                responseBean.getTwRate(), responseBean.getTwNum(),
                remoteConfig, timeFilter, metric, klkOrFly.getName(), "general", timeType
        ));

        if (CompetitorTypeEnumEnum.KLK == klkOrFly) {
            oveaseaSubMetric.setKlkCompetitiveData(competitiveType);
        } else {
            oveaseaSubMetric.setFlyCompetitiveData(competitiveType);
        }
    }

    /**
     * 统一处理覆盖商品力数据类型
     */
    private static CompetitiveData processCompetitiveData(Double rateValue, Double numValue,
                                                          RemoteConfig remoteConfig, TimeFilter timeFilter,
                                                          String metric, String brand, String dataType,
                                                          String timeType) {
        CompetitiveData data = new CompetitiveData();

        data.setInferiorRate(rateValue);
        data.setInferiorNum(numValue);

        // 获取并设置目标值
        Double target = getTargetValue(remoteConfig, timeFilter.getYear(), brand, metric, dataType, timeType);
        data.setTargetValue(target);
        if (data.getInferiorRate() != null) {
            data.setGapValue(data.getInferiorRate() - target);
        }

        return data;
    }

    /**
     * 安全获取目标值
     */
    private static Double getTargetValue(RemoteConfig config, String year, String brand,
                                         String metric, String dataType, String timeType) {
        Integer target = config.getCompetitiveTarget(year, brand, metric, dataType, timeType);
        return target != null ? target / 100.0 : 0.00;
    }

    /**
     * 获取时间类型
     */
    public static String getTimeType(TimeFilter timeFilter) {
        return Optional.ofNullable(timeFilter.getQuarter()).orElse(timeFilter.getHalf());
    }


    /**
     * 补齐指标卡数据
     *
     * @param dimMap
     */
    public static void makeUpMetricData(Map<String, Double> dimMap,
                                        String metric) {
        Double target = tagretMap.get(metric);
        dimMap.put("cw_Inferiority_rate_trgt", target);
        Double rate = dimMap.getOrDefault("cw_Inferiority_rate", 0d);
        dimMap.put("cw_Inferiority_rate_gap", rate - target);
    }

    /**
     * 获取数据的id
     * @param subMetric
     * @return
     */
    private static Long getId(String subMetric) {
        return idMap.get(subMetric);
    }


    /*
     * 获取统计维度id
     * 1 邮箱前缀,角色
     * 2 邮箱前缀,角色,子区域,大区 (按商拓下钻)
     * 3 考核对象，角色,大区 (按大区下钻)
     * 4 考核对象,角色,子区域,大区 (按子区域下钻)
     * 5 考核对象,角色,子区域,大区,国家/地区 (按国家/地区下钻)
     *
     * @param field
     */
    private static String getStatisticsDimId(String field) {
        if (GeneralUtil.isNotEmpty(field)) {
            switch (field) {
                case "region":
                    return "3";
                case "province":
                    return "4";
                case "country":
                    return "5";
                case "examinee":
                    return "2";
                case "viewspot":
                    return "6";
            }
        }
        throw new InputArgumentException("invalid field:" + field);
    }


    /**
     * 根据目的地考核层级或者下钻维度获取考核维度id
     * 1 海外
     * 2 大区
     * 3 子区域
     * 4 国家/地区
     * 5 商拓
     * 6 景点
     *
     * @param destinationLevel
     * @param field
     * @param remoteConfig
     */
    private static String getExamineLevelId(String destinationLevel,
                                            String field,
                                            RemoteConfig remoteConfig) {
        //优先级 field > destinationLevel
        if (GeneralUtil.isNotEmpty(field)) {
            switch (field) {
                case "region":
                    return "2";
                case "province":
                    return "3";
                case "country":
                    return "4";
                case "examinee":
                    return "5";
                case "viewspot":
                    return "6";
            }
        }
        String oversea = remoteConfig.getConfigValue("oversea");
        String region = remoteConfig.getConfigValue("region");
        String subRegion = remoteConfig.getConfigValue("subRegion");
        if (oversea.equals(destinationLevel)) {
            return "1";
        }
        if (region.equals(destinationLevel)) {
            return "2";
        }
        if (subRegion.equals(destinationLevel)) {
            return "3";
        }
        throw new InputArgumentException("invalid destinationLevel:" + destinationLevel + ",field:" + field);
    }


}
