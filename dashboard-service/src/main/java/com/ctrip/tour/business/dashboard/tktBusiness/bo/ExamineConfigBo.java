package com.ctrip.tour.business.dashboard.tktBusiness.bo;

import com.ctrip.soa._24922.AvailableSubMetric;
import com.ctrip.soa._24922.BusinessMetricMap;
import com.ctrip.soa._24922.TimeFilter;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ExamineTypeBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.SubMetricPermissonMappingBean;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardExamineeConfigV2;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.InputArgumentException;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/7/25
 */
public class ExamineConfigBo {

    //仪表盘二期提取指标概览接口
    public List<String> getMetricList(List<BusinessDashboardExamineeConfigV2> examineeConfigV2List) {
        Set<String> metricSet = new HashSet<>();
        for (BusinessDashboardExamineeConfigV2 bean : examineeConfigV2List) {
            String[] metricArray = bean.getExamineMetric().split(";");
            for (String metric : metricArray) {
                metricSet.add(metric);
            }
        }
        List<String> metricList = new ArrayList<>();
        metricList.addAll(metricSet);
        return metricList;
    }



    public List<MetricInfoBean> getMetricInfoBeanList(List<BusinessDashboardExamineeConfigV2> examineeConfigV2List,
                                                      RemoteConfig remoteConfig) {

        //国内
        String domestic = remoteConfig.getConfigValue("domestic");
        //三方
        String three = remoteConfig.getConfigValue("three");
        //大区
        String region = remoteConfig.getConfigValue("region");
        //省份
        String province = remoteConfig.getConfigValue("province");
        //景点
        String viewspot = remoteConfig.getConfigValue("viewspot");

        List<MetricInfoBean> metricInfoBeanList = new ArrayList<>();
        for (BusinessDashboardExamineeConfigV2 bean : examineeConfigV2List) {
            String[] metricArray = bean.getExamineMetric().split(";");
            String level = bean.getExamineLevel();
            String needUniversalStudios = bean.getNeedUniversalStudios();
            Integer examineType = bean.getExamineType();
            Integer role = bean.getRole();
            for (String metric : metricArray) {
                MetricInfoBean metricInfoBean = new MetricInfoBean();
                metricInfoBean.setMetric(metric);
                metricInfoBean.setLevel(level);
                metricInfoBean.setNeedUniversalStudios(needUniversalStudios);
                metricInfoBean.setExamineType(examineType);
                metricInfoBean.setRole(role);
                metricInfoBeanList.add(metricInfoBean);

                //国内日游考核配置
                String odtLevel = bean.getOdtExamineLevel();
                String odtRange = bean.getOdtExamineRange();
                metricInfoBean.setOdtLevel(odtLevel);
                if (!domestic.equals(odtLevel) && GeneralUtil.isNotEmpty(odtRange)) {
                    metricInfoBean.setOdtRegionList(Lists.newArrayList(odtRange.split(";")));
                }

                //出境日游考核配置
                String overseaOdtLevel = bean.getOverseaOdtExamineLevel();
                String overseaOdtRange = bean.getOverseaOdtExamineRange();
                metricInfoBean.setOverseaOdtLevel(overseaOdtLevel);
                if (!domestic.equals(overseaOdtLevel) && GeneralUtil.isNotEmpty(overseaOdtRange)) {
                    metricInfoBean.setOverseaOdtRegionList(Lists.newArrayList(overseaOdtRange.split(";")));
                }

                //门票活动国内  三方没有任何配置
                if (domestic.equals(level) || three.equals(level)) {
                    continue;
                } else if (region.equals(level) || province.equals(level)) {
                    List<String> regionList = new ArrayList<>();
                    Collections.addAll(regionList, bean.getExamineRange().split(";"));
                    metricInfoBean.setRegionList(regionList);
                } else if (viewspot.equals(level)) {
                    List<String> bdList = new ArrayList<>();
                    Collections.addAll(bdList, bean.getExamineRange().split(";"));
                    //此时需要考虑活动考核层级
                    //当活动考核层级是省份时  数据已经将活动考核达成数据加到了人里面
                    //当活动考核层级是景点时  需要将活动考核层级数据与门票考核层级数据(景点)相加
                    String actLevel = bean.getActExamineLevel();
                    if (viewspot.equals(actLevel)) {
                        Collections.addAll(bdList, bean.getActExamineRange().split(";"));
                    }
                    bdList = bdList.stream().distinct().collect(Collectors.toList());
                    metricInfoBean.setBdList(bdList);
                }
            }
        }
        return metricInfoBeanList;
    }

    public List<MetricInfoBean> getMetricInfoBeanListV2(List<BusinessDashboardExamineeConfigV2> examineeConfigV2List,
                                                        RemoteConfig remoteConfig) {

        //国内
        String domestic = remoteConfig.getConfigValue("domestic");
        //大区
        String region = remoteConfig.getConfigValue("region");
        //省份
        String province = remoteConfig.getConfigValue("province");
        //景点
        String viewspot = remoteConfig.getConfigValue("viewspot");

        List<MetricInfoBean> metricInfoBeanList = new ArrayList<>();
        for (BusinessDashboardExamineeConfigV2 bean : examineeConfigV2List) {
            String[] metricArray = bean.getExamineMetric().split(";");
            String level = bean.getExamineLevel();
            String needUniversalStudios = bean.getNeedUniversalStudios();
            Integer examineType = bean.getExamineType();
            Integer role = bean.getRole();
            for (String metric : metricArray) {
                MetricInfoBean metricInfoBean = new MetricInfoBean();
                metricInfoBean.setBusinessType(getBusinessTypeWithOneConfig(bean));
                metricInfoBean.setQuarter(bean.getQuarter());
                metricInfoBean.setMetric(metric);
                metricInfoBean.setLevel(level);
                metricInfoBean.setNeedUniversalStudios(needUniversalStudios);
                metricInfoBean.setExamineType(examineType);
                metricInfoBean.setRole(role);
                metricInfoBeanList.add(metricInfoBean);

                //日游考核配置
                String odtLevel = bean.getOdtExamineLevel();
                String odtRange = bean.getOdtExamineRange();
                metricInfoBean.setOdtLevel(odtLevel);
                if (!domestic.equals(odtLevel) && GeneralUtil.isNotEmpty(odtRange)) {
                    metricInfoBean.setOdtRegionList(Lists.newArrayList(odtRange.split(";")));
                }

                //活动考核配置
                String actLevel = bean.getActExamineLevel();
                metricInfoBean.setActLevel(actLevel);
                if (domestic.equals(actLevel)) {
                    continue;
                } else if (region.equals(actLevel) || province.equals(actLevel)) {
                    List<String> regionList = new ArrayList<>();
                    Collections.addAll(regionList, bean.getActExamineRange().split(";"));
                    metricInfoBean.setActRegionList(regionList);
                } else if (viewspot.equals(actLevel)) {
                    List<String> bdList = new ArrayList<>();
                    Collections.addAll(bdList, bean.getActExamineRange().split(";"));
                    metricInfoBean.setBdList(bdList);
                }

                //门票考核配置
                if (domestic.equals(level)) {
                    continue;
                } else if (region.equals(level) || province.equals(level)) {
                    List<String> regionList = new ArrayList<>();
                    Collections.addAll(regionList, bean.getExamineRange().split(";"));
                    metricInfoBean.setRegionList(regionList);
                } else if (viewspot.equals(level)) {
                    List<String> bdList = new ArrayList<>();
                    Collections.addAll(bdList, bean.getExamineRange().split(";"));
                    metricInfoBean.setBdList(bdList);
                }
            }
        }
        return metricInfoBeanList;
    }



    public MetricInfoBean getMetricInfoBean(BusinessDashboardExamineeConfigV2 examineeConfigV2,
                                            RemoteConfig remoteConfig) {
        List<MetricInfoBean> metricInfoBeanList = getMetricInfoBeanList(Lists.newArrayList(examineeConfigV2), remoteConfig);
        return metricInfoBeanList.get(0);
    }


    public MetricInfoBean getMetricInfoBean(BusinessDashboardExamineeConfigV2 examineeConfigV2,
                                            RemoteConfig remoteConfig,
                                            List<Integer> examineTypeList) {
        MetricInfoBean metricInfoBean = getMetricInfoBean(examineeConfigV2, remoteConfig);
        if (examineTypeList.contains(metricInfoBean.getExamineType())) {
            return metricInfoBean;
        }
        return null;
    }

    public MetricInfoBean getSingleMetricInfoBean(List<BusinessDashboardExamineeConfigV2> examineeConfigV2List,
                                                  RemoteConfig remoteConfig,
                                                  String metric){
        List<MetricInfoBean> metricInfoBeanList = getMetricInfoBeanList(examineeConfigV2List,remoteConfig);
        Optional<MetricInfoBean> optionalMetricInfoBean = metricInfoBeanList.stream()
                .filter(i->i.getMetric().equals(metric))
                .findFirst();
        if(optionalMetricInfoBean.isPresent()){
            return optionalMetricInfoBean.get();
        }
        throw new InputArgumentException("输入非法的指标为:"+metric);
    }
    public MetricInfoBean getSingleMetricInfoBeanV2(List<BusinessDashboardExamineeConfigV2> examineeConfigV2List,
                                                  RemoteConfig remoteConfig,
                                                  String metricRequest){
        //国内
        String domestic = remoteConfig.getConfigValue("domestic");
        //三方
        String three = remoteConfig.getConfigValue("three");
        //大区
        String region = remoteConfig.getConfigValue("region");
        //省份
        String province = remoteConfig.getConfigValue("province");
        //景点
        String viewspot = remoteConfig.getConfigValue("viewspot");

        List<MetricInfoBean> metricInfoBeanList = new ArrayList<>();
        for (BusinessDashboardExamineeConfigV2 bean : examineeConfigV2List) {
            String[] metricArray = bean.getExamineMetric().split(";");
            String level = bean.getExamineLevel();
            String needUniversalStudios = bean.getNeedUniversalStudios();
            Integer examineType = bean.getExamineType();
            Integer role = bean.getRole();
            for (String metric : metricArray) {
                MetricInfoBean metricInfoBean = new MetricInfoBean();
                metricInfoBean.setBusinessType(getBusinessTypeWithOneConfig(bean));
                metricInfoBean.setQuarter(bean.getQuarter());
                metricInfoBean.setMetric(metric);
                metricInfoBean.setLevel(level);
                metricInfoBean.setNeedUniversalStudios(needUniversalStudios);
                metricInfoBean.setExamineType(examineType);
                metricInfoBean.setRole(role);
                metricInfoBeanList.add(metricInfoBean);

                //日游考核配置
                String odtLevel = bean.getOdtExamineLevel();
                String odtRange = bean.getOdtExamineRange();
                metricInfoBean.setOdtLevel(odtLevel);
                if (!domestic.equals(odtLevel) && GeneralUtil.isNotEmpty(odtRange)) {
                    metricInfoBean.setOdtRegionList(Lists.newArrayList(odtRange.split(";")));
                }

                //活动考核配置
                String actLevel = bean.getActExamineLevel();
                metricInfoBean.setActLevel(actLevel);
                if (domestic.equals(actLevel)) {
                    continue;
                } else if (region.equals(actLevel) || province.equals(actLevel)) {
                    List<String> regionList = new ArrayList<>();
                    Collections.addAll(regionList, bean.getActExamineRange().split(";"));
                    metricInfoBean.setActRegionList(regionList);
                } else if (viewspot.equals(actLevel)) {
                    List<String> bdList = new ArrayList<>();
                    Collections.addAll(bdList, bean.getActExamineRange().split(";"));
                    metricInfoBean.setBdList(bdList);
                    metricInfoBean.setActRegionList(bdList);
                }

                //门票考核配置
                if (domestic.equals(level)) {
                    continue;
                } else if (region.equals(level) || province.equals(level)) {
                    List<String> regionList = new ArrayList<>();
                    Collections.addAll(regionList, bean.getExamineRange().split(";"));
                    metricInfoBean.setRegionList(regionList);
                } else if (viewspot.equals(level)) {
                    List<String> bdList = new ArrayList<>();
                    Collections.addAll(bdList, bean.getExamineRange().split(";"));
                    metricInfoBean.setBdList(bdList);
                    metricInfoBean.setRegionList(bdList);
                }
            }
        }

        Optional<MetricInfoBean> optionalMetricInfoBean = metricInfoBeanList.stream()
                .filter(i -> i.getMetric().equals(metricRequest))
                .findFirst();
        if (optionalMetricInfoBean.isPresent()) {
            return optionalMetricInfoBean.get();
        }
        throw new InputArgumentException("输入非法的指标为:" + metricRequest);//NOSONAR
    }


    //仪表盘国内收入力基础配置数据(指标卡)
    public Map<String, AvailableSubMetric> getMetricCardConfigMap(List<BusinessDashboardExamineeConfigV2> examineeConfigV2List,
                                                                  TimeFilter timeFilter,
                                                                  RemoteConfig remoteConfig) {
        //获取国内收入力的指标配置
        BusinessDashboardExamineeConfigV2 examineeConfig = examineeConfigV2List
                .stream()
                .filter(i -> "1;2".equals(i.getExamineMetric()))
                .findAny()
                .orElse(null);

        Map<String, AvailableSubMetric> configMap = new HashMap<>();
        //取不到配置直接返回  理论上不会出现
        if (GeneralUtil.isEmpty(examineeConfig)) {
            return configMap;
        }
        AvailableSubMetric availableSubMetric = new AvailableSubMetric();
        List<String> subMetricList = new ArrayList<>();
        availableSubMetric.setSubMetricList(subMetricList);
        configMap.put("1", availableSubMetric);
        configMap.put("2", availableSubMetric);
        //2023年之前只有门票活动的配置
        String year = timeFilter.getYear();
        if (!DateUtil.compareTwoYear(year, "2022")) {
            subMetricList.add("ticketActivity");
            return configMap;
        }
        ExamineTypeBean examineTypeBean = remoteConfig.getExamineTypeBean(examineeConfig.getExamineType());
        List<String> singleSubMetricList = examineTypeBean.getSubMetricList();
        //需要查看超过一个子指标 则需要返回复合场景
        if (singleSubMetricList.size() > 1) {
            subMetricList.add(StringUtils.join(singleSubMetricList, "+"));
        }
        subMetricList.addAll(singleSubMetricList);
        return configMap;
    }


    //仪表盘国内收入力基础配置数据(趋势线)
    public Map<String, AvailableSubMetric> getTrendLineConfigMap(List<BusinessDashboardExamineeConfigV2> examineeConfigV2List,
                                                                 TimeFilter timeFilter,
                                                                 RemoteConfig remoteConfig) {
        //获取国内收入力的指标配置
        BusinessDashboardExamineeConfigV2 examineeConfig = examineeConfigV2List
                .stream()
                .filter(i -> "1;2".equals(i.getExamineMetric()))
                .findAny()
                .orElse(null);

        Map<String, AvailableSubMetric> configMap = new HashMap<>();
        //取不到配置直接返回  理论上不会出现
        if (GeneralUtil.isEmpty(examineeConfig)) {
            return configMap;
        }
        AvailableSubMetric availableSubMetric = new AvailableSubMetric();
        List<String> subMetricList = new ArrayList<>();
        availableSubMetric.setSubMetricList(subMetricList);
        configMap.put("1", availableSubMetric);
        configMap.put("2", availableSubMetric);
        //2023年之前只有门票活动的配置
        String year = timeFilter.getYear();
        if (!DateUtil.compareTwoYear(year, "2022")) {
            subMetricList.add("ticketActivity");
            return configMap;
        }

        ExamineTypeBean examineTypeBean = remoteConfig.getExamineTypeBean(examineeConfig.getExamineType());
        List<String> singleSubMetricList = examineTypeBean.getSubMetricList();

        List<List<String>> mergedSubMetricDataList = remoteConfig.getMergedSubMetricDataList();
        for (List<String> singleMergerdSubMetricList : mergedSubMetricDataList) {
            //如果可以查看的子指标里面包含了可以合并的场景
            if (singleSubMetricList.containsAll(singleMergerdSubMetricList) && checkCanMerged(singleMergerdSubMetricList, examineeConfig, remoteConfig)) {
                subMetricList.add(StringUtils.join(singleMergerdSubMetricList, "+"));
            }
        }
        subMetricList.addAll(singleSubMetricList);
        return configMap;
    }


    /**
     * 判断可以合并的子指标组合 实际是否可以合并
     *
     * @param singleMergerdSubMetricList
     * @param examineeConfig
     * @param remoteConfig
     * @return
     */
    private static Boolean checkCanMerged(List<String> singleMergerdSubMetricList,
                                          BusinessDashboardExamineeConfigV2 examineeConfig,
                                          RemoteConfig remoteConfig) {
        Set<String> levelSet = new HashSet<>();
        Set<String> rangeSet = new HashSet<>();
        String examineLevel = examineeConfig.getExamineLevel();
        String actExamineLevel = examineeConfig.getActExamineLevel();
        String odtExamineLevel = examineeConfig.getOdtExamineLevel();
        String overseaOdtExamineLevel = examineeConfig.getOverseaOdtExamineLevel();
        for (String metric : singleMergerdSubMetricList) {

            if ("ticketActivity".equals(metric)) {
                if (GeneralUtil.isNotEmpty(examineLevel)) {
                    levelSet.add(examineLevel);
                    rangeSet.add(examineeConfig.getExamineRange());
                }
                if (GeneralUtil.isNotEmpty(actExamineLevel)) {
                    levelSet.add(actExamineLevel);
                    rangeSet.add(examineeConfig.getActExamineRange());
                }
            }

            if ("domesticDayTour".equals(metric)) {
                if (GeneralUtil.isNotEmpty(odtExamineLevel)) {
                    levelSet.add(odtExamineLevel);
                    rangeSet.add(examineeConfig.getOdtExamineRange());
                }
            }

            if ("overseaDayTour".equals(metric)) {
                if (GeneralUtil.isNotEmpty(overseaOdtExamineLevel)) {
                    levelSet.add(overseaOdtExamineLevel);
                    rangeSet.add(examineeConfig.getOverseaOdtExamineRange());
                }
            }
        }

        //由于国内日游/出境日游的考核层级最小为省  当门票活动的考核层级是景点时  必然不一致
        //因此只要判断两个set是否都只有一个元素 如果只有一个元素 则考核层级和考核范围均相同
        //特殊情况处理 当考核层级是省份时  不需要考虑环球影城的影响(不判断needUniversalStudios字段)
        //当考核层级是大区时 如果门票活动考核范围是包含环球影城 则需要忽略环球影城的影响
        if (levelSet.size() == 1 && rangeSet.size() == 1) {
            return true;
        }

        String region = remoteConfig.getConfigValue("region");
        levelSet.add(region);
        if (levelSet.size() == 1) {
            String universalStudios = remoteConfig.getConfigValue("universalStudios");
            rangeSet = rangeSet.stream()
                    .filter(i -> !i.contains(universalStudios))
                    .collect(Collectors.toSet());
            if (rangeSet.size() == 1) {
                return true;
            }
        }
        return false;
    }

    /**
     * 对于除了1；2指标的指标卡tab和下钻tab的处理;
     * 目前除了1 2之外，只有3 和 11指标
     *
     * @param examineeConfigV2List    : 传了人员对应的所有指标权限，通过qconfig的配置去筛选，目前只配置3|11.所以返回值也只有3|11的结果
     * @param trendLineConfigMap
     * @param metricCardConfigMap
     * @param remoteConfig
     */
    public void setOtherConfigMap(List<BusinessDashboardExamineeConfigV2> examineeConfigV2List, Map<String, AvailableSubMetric> trendLineConfigMap, Map<String, AvailableSubMetric> metricCardConfigMap, RemoteConfig remoteConfig) {
        if(examineeConfigV2List.size() > 0){

            for (BusinessDashboardExamineeConfigV2 examineeConfig : examineeConfigV2List) {
                Integer examineType = examineeConfig.getExamineType();
                List<SubMetricPermissonMappingBean> subMetricPermissonMappingBeanList = remoteConfig.getSubMetricPermissonMapping(examineeConfig.getExamineMetric());

                if(subMetricPermissonMappingBeanList != null && subMetricPermissonMappingBeanList.size() > 0){
                    Map<String, SubMetricPermissonMappingBean> subMetricPermissonMappingBeanMap = subMetricPermissonMappingBeanList.stream().collect(Collectors.toMap(SubMetricPermissonMappingBean::getExamineMetric, // 分组依据
                            SubMetricPermissonMappingBean -> SubMetricPermissonMappingBean, // 分组后的元素处理
                            (v1, v2) -> v1));
                    SubMetricPermissonMappingBean subMetricBean = subMetricPermissonMappingBeanMap.get(examineType.toString());
                    if(subMetricBean != null){
                        List<String> subMetricList = subMetricBean.getSubmetric();
                        trendLineConfigMap.put(examineeConfig.getExamineMetric(), new AvailableSubMetric(subMetricList));
                        metricCardConfigMap.put(examineeConfig.getExamineMetric(), new AvailableSubMetric(subMetricList));
                    }
                }

            }
        }
    }

    /**
     * 判断当前请求人的考核业务线，0全部，1门票，2玩乐
     * @param configMap
     * @return
     */
    public int getBusinessType(Map<String, List<MetricInfoBean>> configMap) {
        boolean isTic = false;
        boolean isPlay = false;
        for(Map.Entry<String, List<MetricInfoBean>> entry : configMap.entrySet()) {
            List<MetricInfoBean> val = entry.getValue();
            if (val.stream().anyMatch(i -> i.getLevel() != null && !Objects.equals(i.getLevel(), ""))){
                isTic = true;
            }
            if (val.stream().anyMatch(i -> (i.getActLevel() != null && !Objects.equals(i.getActLevel(), "")) || (i.getOdtLevel() != null && !Objects.equals(i.getOdtLevel(), "")))){
                isPlay = true;
            }
        }
        if (isPlay && isTic) {
            return 0;
        }
        if (isTic) {
            return 1;
        }
        if (isPlay) {
            return 2;
        }
        return -1;
    }

    /**
     * 判断当前请求人的考核业务线，0全部，1门票，2玩乐
     * @param config
     * @return
     */
    public int getBusinessTypeWithOneConfig(BusinessDashboardExamineeConfigV2 config) {
        if ("11".equals(config.getExamineMetric()) || "12".equals(config.getExamineMetric())) {
            //日游相关的特殊逻辑直接返回4
            return 4;
        }
        boolean isTic = StringUtils.isNoneBlank(config.getExamineLevel());
        boolean isPlay = StringUtils.isNoneBlank(config.getOdtExamineLevel()) || StringUtils.isNoneBlank(config.getActExamineLevel());
        if (isPlay && isTic) {
            return 0;//全部
        }
        if (isTic) {
            return 1;//门票
        }
        if (isPlay) {
            boolean isAct = StringUtils.isNoneBlank(config.getActExamineLevel());
            boolean isDayTour = StringUtils.isNoneBlank(config.getOdtExamineLevel());
            if (isAct && isDayTour) {
                return 2; //玩乐
            } else if (isAct) {
                return 3; //活动
            } else if (isDayTour) {
                return 4; //日游
            }
        }
        return -1;
    }

    /**
     * 门票指标
     * @param configMaps
     * @return
     */
    public BusinessMetricMap getBusinessMetricMapWithTic(Map<String, List<MetricInfoBean>> configMaps) {
        BusinessMetricMap businessMetricMap = new BusinessMetricMap();
        businessMetricMap.setId(1);
        businessMetricMap.setCode("tic");
        List<BusinessMetricMap> metricList = new ArrayList<>();
        businessMetricMap.setMetricList(metricList);
        for (Map.Entry<String, List<MetricInfoBean>> entry : configMaps.entrySet()) {
            String metric = entry.getKey();
            List<MetricInfoBean> beans = entry.getValue();
            int metricId = Integer.parseInt(metric);
            boolean isTic = beans.stream().anyMatch(i -> i.getLevel() != null && !Objects.equals(i.getLevel(), ""));
            if (metricId != 11 && metricId != 12 && isTic) {
                //只处理1-7,9指标
                metricList.add(getSecondMetricInfo(metricId, 1, beans));
            }
        }
        if (metricList.isEmpty()) {
            return new BusinessMetricMap(); //如果没有指标则返回空的BusinessMetricMap
        }
        return businessMetricMap;
    }

    /**
     * 玩乐指标
     * @param configMaps
     * @return
     */
    public BusinessMetricMap getBusinessMetricMapWithAct(Map<String, List<MetricInfoBean>> configMaps) {
        BusinessMetricMap businessMetricMap = new BusinessMetricMap();
        businessMetricMap.setId(2);
        businessMetricMap.setCode("act");
        List<BusinessMetricMap> metricList = new ArrayList<>();
        businessMetricMap.setMetricList(metricList);
        boolean isPlay = false;
        boolean isDayTour = false;
        for (Map.Entry<String, List<MetricInfoBean>> entry : configMaps.entrySet()) {
            List<MetricInfoBean> beans = entry.getValue();
            if (beans.stream().anyMatch(i -> i.getActLevel() != null && !Objects.equals(i.getActLevel(), ""))) {
                isPlay = true;
            }
            if (beans.stream().anyMatch(i -> i.getOdtLevel() != null && !Objects.equals(i.getOdtLevel(), ""))) {
                isDayTour = true;
            }
        }
        int actType;
        if (isPlay && isDayTour) {
            actType = 2; //玩乐
        }else if(isPlay) {
            actType = 3; //活动
        }else{
            actType = 4; //日游
        }
        for (Map.Entry<String, List<MetricInfoBean>> entry : configMaps.entrySet()) {
            String metric = entry.getKey();
            List<MetricInfoBean> beans = entry.getValue();
            boolean isPlayTem = beans.stream().anyMatch(i -> i.getActLevel() != null && !Objects.equals(i.getActLevel(), ""));
            boolean isDayTourTem = beans.stream().anyMatch(i -> i.getOdtLevel() != null && !Objects.equals(i.getOdtLevel(), ""));
            boolean isSinglePlay = isPlayTem || isDayTourTem;

            int metricId = Integer.parseInt(metric);
            if (metricId != 5 && metricId != 6 && metricId != 7 && isSinglePlay) {
                metricList.add(getSecondMetricInfo(metricId, actType, beans));
            }
        }
        if (metricList.isEmpty()) {
            return new BusinessMetricMap(); //如果没有指标则返回空的BusinessMetricMap
        }
        return businessMetricMap;
    }


    /**
     * 组二级指标
     * @param metricId
     * @param actType
     * @return
     */
    public BusinessMetricMap getSecondMetricInfo(int metricId, int actType,List<MetricInfoBean> bean){
        BusinessMetricMap secondMetricInfo = new BusinessMetricMap();
        secondMetricInfo.setId(metricId);
        switch (metricId) {
            case 1:
                secondMetricInfo.setCode("gmv");
                secondMetricInfo.setMetricList(getThirdMetricInfo(actType,bean));
                break;
            case 2:
                secondMetricInfo.setCode("profit");
                secondMetricInfo.setMetricList(getThirdMetricInfo(actType,bean));
                break;
            case 3:
                secondMetricInfo.setCode("quality");
                break;
            case 4:
                secondMetricInfo.setCode("directSign");
                break;
            case 5:
                if (actType == 1){
                    secondMetricInfo.setCode("sightCover");
                }
                break;
            case 6:
                if (actType == 1){
                    secondMetricInfo.setCode("typeCover");
                }
                break;
            case 7:
                if (actType == 1){
                    secondMetricInfo.setCode("ticketBooking");
                }
                break;
            case 9:
                secondMetricInfo.setCode("categoryCover");
                break;
            case 11:
                if (actType != 1) {
                    secondMetricInfo.setCode("dayTourCover");
                }
                break;
            case 12:
                if (actType != 1) {
                    secondMetricInfo.setCode("dayTourBooking");
                }
                break;
        }
        return secondMetricInfo;
    }

    /**
     * 组三级指标
     * @param actType
     * @return
     */
    public List<BusinessMetricMap> getThirdMetricInfo(int actType, List<MetricInfoBean> beans) {
        List<BusinessMetricMap> metricList = new ArrayList<>();
        boolean isAct = beans.stream().anyMatch(i -> i.getActLevel() != null && !Objects.equals(i.getActLevel(), ""));
        boolean isDayTour = beans.stream().anyMatch(i -> i.getOdtLevel() != null && !Objects.equals(i.getOdtLevel(), ""));
        switch (actType) {
            case 2:
                BusinessMetricMap playMetricInfo = new BusinessMetricMap();
                playMetricInfo.setId(2);
                playMetricInfo.setCode("play");
                if (isAct && isDayTour) {
                    metricList.add(playMetricInfo);
                }
                BusinessMetricMap actMetricInfo = new BusinessMetricMap();
                actMetricInfo.setId(3);
                actMetricInfo.setCode("act");
                if (isAct) {
                    metricList.add(actMetricInfo);
                }
                BusinessMetricMap dayTourMetricInfo = new BusinessMetricMap();
                dayTourMetricInfo.setId(4);
                dayTourMetricInfo.setCode("dayTour");
                if (isDayTour) {
                    metricList.add(dayTourMetricInfo);
                }
                break;
            case 3:
                BusinessMetricMap actMetricInfo1 = new BusinessMetricMap();
                actMetricInfo1.setId(3);
                actMetricInfo1.setCode("act");
                if (isAct) {
                    metricList.add(actMetricInfo1);
                }
                break;
            case 4:
                BusinessMetricMap dayTourMetricInfo1 = new BusinessMetricMap();
                dayTourMetricInfo1.setId(4);
                dayTourMetricInfo1.setCode("dayTour");
                if (isDayTour) {
                    metricList.add(dayTourMetricInfo1);
                }
                break;
            default:
                return null;
        }
        return metricList;
    }
}
