package com.ctrip.tour.business.dashboard.enums;

public enum KeyProjectDashboardFilterColumn {
    REGION(0, "region"),
    PROVINCE(1, "province"),
    VIEWSPOT_MEID(2, "viewspot_meid"),
    ROLE(3, "role");

    private int code;
    private String name;

    KeyProjectDashboardFilterColumn(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
