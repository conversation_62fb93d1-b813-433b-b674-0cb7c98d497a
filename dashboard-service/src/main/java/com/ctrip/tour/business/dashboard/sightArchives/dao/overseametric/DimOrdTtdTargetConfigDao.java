package com.ctrip.tour.business.dashboard.sightArchives.dao.overseametric;

import com.ctrip.soa._27181.PreparedParameterBean;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.DimOrdTtdTargetBO;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoWithMetricBean;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.TktStarRocksDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.Types;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Repository
@Slf4j
public class DimOrdTtdTargetConfigDao {

    @Autowired
    private TktStarRocksDao tktStarRocksDao;

    /**
     * 获取海外考核人员配置信息
     *
     * @return
     */
    public List<OverseaMetricInfoWithMetricBean> queryOverseaPersonInfo(List<String> quarterList,
                                                                        String year,
                                                                        List<String> buTypeList,
                                                                        String metric,
                                                                        String domainName,
                                                                        String levelType,
                                                                        String d) {
        StringBuilder sql = new StringBuilder("select * from dim_ord_ttd_overseas_person_config_df where 1 = 1");
        List<PreparedParameterBean> parameters = new java.util.ArrayList<>();
        appendBuType(parameters, sql, buTypeList);
        appendDomainName(parameters, sql, domainName);
        appendYear(parameters, sql, year);
        appendQuarterList(parameters, sql, quarterList);
        appendMetricType(parameters, sql, metric);
        appendExamineLevel(parameters, sql, levelType);
        appendD(parameters, sql, d);
        List<Map<String, Object>> result = new ArrayList<>();
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            log.warn("queryOverseaPersonInfo error", e);
        }
        List<OverseaMetricInfoWithMetricBean> overseaMetricInfoWithMetricBeans = new ArrayList<>();
        if (!CollectionUtils.isEmpty(result)) {
            overseaMetricInfoWithMetricBeans = result.stream()
                    .map(bean -> {
                        OverseaMetricInfoWithMetricBean metricInfoBean = new OverseaMetricInfoWithMetricBean();
                        metricInfoBean.setMetric((String) bean.get("examine_metric_type"));
                        metricInfoBean.setDomainName((String) bean.get("domain_name"));
                        metricInfoBean.setDestinationRange((String) bean.get("examine_scope"));
                        metricInfoBean.setDestinationLevel((String) bean.get("examine_level"));
                        metricInfoBean.setYear((String) bean.get("examine_year"));
                        metricInfoBean.setQuarter((String) bean.get("examine_quarter"));
                        metricInfoBean.setBuType((String) bean.get("bu_type"));
                        return metricInfoBean;
                    })
                    .collect(Collectors.toList());
        }

        return overseaMetricInfoWithMetricBeans;
    }

    //拼时间
    private void appendD(List<PreparedParameterBean> parameters, StringBuilder sql, String d) {
        if (d != null) {
            sql.append(" and d=?");
            parameters.add(new PreparedParameterBean(d, Types.VARCHAR));
        }
    }

    //拼业务线
    private void appendBuType(List<PreparedParameterBean> parameters, StringBuilder sql, List<String> buTypeNameList) {
        if (CollectionUtils.isNotEmpty(buTypeNameList)) {
            sql.append(" and bu_type in (");
            for (int i = 0; i < buTypeNameList.size(); i++) {
                if (i == 0) {
                    sql.append("?");
                } else {
                    sql.append(",?");
                }
                parameters.add(new PreparedParameterBean(buTypeNameList.get(i), Types.VARCHAR));
            }
            sql.append(")");
        }
    }

    //拼人维度
    private void appendDomainName(List<PreparedParameterBean> parameters, StringBuilder sql, String domainName) {
        if (domainName != null) {
            sql.append(" and domain_name=?");
            parameters.add(new PreparedParameterBean(domainName, Types.VARCHAR));
        }
    }

    //拼年份
    private void appendYear(List<PreparedParameterBean> parameters, StringBuilder sql, String year) {
        if (year != null) {
            sql.append(" and examine_year=?");
            parameters.add(new PreparedParameterBean(year, Types.VARCHAR));
        }
    }

    //拼季度列表
    private void appendQuarterList(List<PreparedParameterBean> parameters, StringBuilder sql, List<String> quarterList) {
        if (CollectionUtils.isNotEmpty(quarterList)) {
            sql.append(" and examine_quarter in ( ");
            for (int i = 0; i < quarterList.size(); i++) {
                if (i == 0) {
                    sql.append("?");
                } else {
                    sql.append(",?");
                }
                parameters.add(new PreparedParameterBean(quarterList.get(i), Types.VARCHAR));
            }
            sql.append(")");
        }
    }

    //拼季度列表
    private void appendExamineLevel(List<PreparedParameterBean> parameters, StringBuilder sql, String levelName) {
        if (levelName != null) {
            if ("other".equals(levelName)) {
                sql.append(" and examine_level != '渠道' and examine_level != '站点'");//NOSONAR
                return;
            }
            sql.append(" and examine_level=?");
            parameters.add(new PreparedParameterBean(levelName, Types.VARCHAR));
        }
    }

    //拼考核指标
    private void appendMetricType(List<PreparedParameterBean> parameters, StringBuilder sql, String metric) {
        if (metric != null) {
            sql.append(" and examine_metric_type=?");
            parameters.add(new PreparedParameterBean(metric, Types.VARCHAR));
        }
    }

    public List<DimOrdTtdTargetBO> queryDimOrdTtdTarget(String d,List<String> buType, String domainName, String year, List<String> qualityList) {
        StringBuilder sql = new StringBuilder("select * from dim_ord_ttd_overseas_person_config_df  " +
                "where d=?  " +
                "and examine_year=? and domain_name=?  ");
        List<PreparedParameterBean> parameters = new java.util.ArrayList<>();
        parameters.add(new PreparedParameterBean(d, Types.VARCHAR));
        parameters.add(new PreparedParameterBean(year, Types.VARCHAR));
        parameters.add(new PreparedParameterBean(domainName, Types.VARCHAR));
        if (CollectionUtils.isNotEmpty(buType)) {
            sql.append(" and bu_type in ( ");
            for (int i = 0; i < buType.size(); i++) {
                if (i == 0) {
                    sql.append("?");
                } else {
                    sql.append(",?");
                }
                parameters.add(new PreparedParameterBean(buType.get(i), Types.VARCHAR));
            }
            sql.append(")");
        }
        if (CollectionUtils.isNotEmpty(qualityList)) {
            sql.append(" and examine_quarter in ( ");
            for (int i = 0; i < qualityList.size(); i++) {
                if (i == 0) {
                    sql.append("?");
                } else {
                    sql.append(",?");
                }
                parameters.add(new PreparedParameterBean(qualityList.get(i), Types.VARCHAR));
            }
            sql.append(")");
        }
        List<Map<String, Object>> result = new ArrayList<>();
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            log.warn("queryOverseaPersonInfo error", e);
        }
        List<DimOrdTtdTargetBO> dimOrdTtdTargetBOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(result)) {
            dimOrdTtdTargetBOList = result.stream()
                    .map(bean -> {
                        DimOrdTtdTargetBO dimOrdTtdTargetBO = new DimOrdTtdTargetBO();
                        dimOrdTtdTargetBO.setExamineMetricType((String)bean.get("examine_metric_type"));
                        dimOrdTtdTargetBO.setDomainName((String) bean.get("domain_name"));
                        dimOrdTtdTargetBO.setExamineLevel((String)bean.get("examine_level"));
                        dimOrdTtdTargetBO.setExamineScope((String)bean.get("examine_scope"));
                        dimOrdTtdTargetBO.setLocale((String)bean.get("locale"));
                        dimOrdTtdTargetBO.setChannel((String)bean.get("channel"));
                        return dimOrdTtdTargetBO;
                    })
                    .collect(Collectors.toList());
        }
        return dimOrdTtdTargetBOList;
    }

}
