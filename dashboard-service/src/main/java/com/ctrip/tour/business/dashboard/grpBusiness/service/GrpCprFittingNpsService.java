//package com.ctrip.tour.business.dashboard.grpBusiness.service;
//
//import java.util.List;
//import java.util.Map;
//
//import org.springframework.stereotype.Service;
//
//import com.ctrip.tour.business.dashboard.utils.SqlBuilder;
//
///**
// * <AUTHOR>
// * @Date 2024/12/9
// */
//@Service
//public class GrpCprFittingNpsService extends IndexCommonQueryAbstractSerice{
//
//
//    @Override
//    protected SqlBuilder.Condition otherCon(Map<String, ?> param, List<String> groupByCols, String timeAggType) {
//        return null;
//    }
//
//    @Override
//    protected SqlBuilder selectColsAssembly(Map<String, ?> param, List<String> groupByCols, String timeAggType) {
//        return null;
//    }
//
//    @Override
//    protected List<Map<String, Object>> queryData(String sql, Map<String, ?> param, List<String> groupByCols, String timeAggType) {
//        return null;
//    }
//}
