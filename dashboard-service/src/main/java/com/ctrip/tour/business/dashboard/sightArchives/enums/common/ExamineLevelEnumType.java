package com.ctrip.tour.business.dashboard.sightArchives.enums.common;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum ExamineLevelEnumType {

    DOMESTIC(1, "is_domestic", "国内",true), //NOSONAR
    DOMESTIC_REGION(2, "business_region_name", "大区",true), //NOSONAR
    DOMESTIC_PROVINCE(3, "province_name", "省份",true), //NOSONAR
    DOMESTIC_SIGHT(4, "ori_viewspot_meid", "景点",true), //NOSONAR

    OVERSEA(1, "is_domestic", "海外",false), //NOSONAR
    OVERSEA_REGION(2, "business_region_name", "大区",false), //NOSONAR
    OVERSEA_SUB_REGION(4, "business_sub_region_name", "子区域",false); //NOSONAR

    private final int id;
    private final String columnName;
    private final String chineseName;
    private final boolean domestic;

    ExamineLevelEnumType(int id, String columnName, String chineseName, boolean domestic) {
        this.id = id;
        this.columnName = columnName;
        this.chineseName = chineseName;
        this.domestic = domestic;
    }

    public int getId() {
        return id;
    }

    public String getColumnName() {
        return columnName;
    }

    public String getChineseName() {
        return chineseName;
    }

    public boolean isDomestic() {
        return domestic;
    }

    public static final List<ExamineLevelEnumType> domesticExamLevelEnumList = Arrays.stream(ExamineLevelEnumType.values())
                .filter(ExamineLevelEnumType::isDomestic)
                .collect(Collectors.toList());
    public static final List<ExamineLevelEnumType> overseaExamLevelEnumList = Arrays.stream(ExamineLevelEnumType.values())
                .filter(examineLevelEnumType -> !examineLevelEnumType.isDomestic())
                .collect(Collectors.toList());


    public static ExamineLevelEnumType getExamineLevelEnumType(String chineseName, boolean domestic) {
        if (domestic) {
            return getDomesticExamineLevelEnumType(chineseName);
        } else {
            return getOverseaExamineLevelEnumType(chineseName);
        }
    }

    public static ExamineLevelEnumType getDomesticExamineLevelEnumType(String chineseName) {
        return domesticExamLevelEnumList.stream().filter(examineLevelEnumType -> examineLevelEnumType.getChineseName().equals(chineseName)).findFirst().orElse(null);
    }


    public static ExamineLevelEnumType getOverseaExamineLevelEnumType(String chineseName) {
        return overseaExamLevelEnumList.stream().filter(examineLevelEnumType -> examineLevelEnumType.getChineseName().equals(chineseName)).findFirst().orElse(null);
    }

}
