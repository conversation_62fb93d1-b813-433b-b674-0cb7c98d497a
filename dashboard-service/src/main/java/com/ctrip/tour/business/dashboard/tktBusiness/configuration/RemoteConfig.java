package com.ctrip.tour.business.dashboard.tktBusiness.configuration;


import com.ctrip.framework.ucs.common.util.StringUtils;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.*;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.InputArgumentException;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.ctrip.tour.business.dashboard.utils.MapperUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;
import qunar.tc.qconfig.client.spring.QMapConfig;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 读取qconfig配置文件
 * <AUTHOR>
 * @date 2022/7/21
 */

@Component
@Slf4j
public class RemoteConfig {

    @QMapConfig("config.properties")
    private Map<String,String> configMap;

    @QMapConfig("config.properties")
    private void onConfigChange(Map<String, String> map) {
        //监听配置变化
        log.info("config.properties的最新配置是:" + MapperUtil.obj2Str(map));
    }

    @QMapConfig("employeeRelation.properties")
    private Map<String,String> employeeRelationMap;

    @QMapConfig("employeeRelation.properties")
    private void onEmployeeRelationChange(Map<String, String> map) {
        //监听配置变化
        log.info("employeeRelation.properties的最新配置是:" + MapperUtil.obj2Str(map));
    }

    @QMapConfig("pkEmployeeRelation.properties")
    private Map<String, String> pkEmployeeRelationMap;

    @QMapConfig("pkEmployeeRelation.properties")
    private void onPkEmployeeRelationChange(Map<String, String> map) {
        //监听配置变化
        log.info("the lastest config of pkEmployeeRelation.properties:" + MapperUtil.obj2Str(map));
    }

    @QMapConfig("externalConfig.properties")
    private Map<String,String> externalConfigMap;

    @QMapConfig("externalConfig.properties")
    private void onExternalConfigChange(Map<String, String> map) {
        //监听配置变化
        log.info("externalConfig.properties的最新配置是:" + MapperUtil.obj2Str(map));
    }

    @QMapConfig("organizationOrder.properties")
    private Map<String,String> organizationOrderMap;

    @QMapConfig("organizationOrder.properties")
    private void onOrganizationOrderChange(Map<String, String> map){
        //监听配置变化
        log.info("organizationOrder.properties的最新配置是:" + MapperUtil.obj2Str(map));
    }

    @QConfig("OverseaDrillDownMetadata.json")
    private OverseaDrillDownMetadataBean overseaDrillDownMetadata;

    @QConfig("OverseaDrillDownMetadataV2.json")
    private OverseaDrillDownMetadataBean overseaDrillDownMetadataV2;

    @QConfig("OverseaDrillDownMetadata.json")
    private void onOverseaDrillDownMetadataChange(OverseaDrillDownMetadataBean overseaDrillDownMetadata){
        //监听配置变化
        log.info("the lastest config of OverseaDrillDownMetadata.json is:" + MapperUtil.obj2Str(overseaDrillDownMetadata));
    }


    @QConfig("DomesticDrillDownMetadata.json")
    private DomesticDrillDownMetadataBean domesticDrillDownMetadata;


    @QConfig("DomesticDrillBaseInfoBean.json")
    private DomesticDrillDownBaseInfoBean domesticDrillBaseInfoBean;

    @QConfig("DomesticDrillDownMetadata.json")
    private void onDomesticDrillDownMetadataChange(DomesticDrillDownMetadataBean domesticDrillDownMetadata){
        //监听配置变化
        log.info("the lastest config of DomesticDrillDownMetadata.json is:" + MapperUtil.obj2Str(domesticDrillDownMetadata));
    }

    @QConfig("DomesticExamineTypeConfigData.json")
    private DomesticExamineTypeConfigDataBean domesticExamineTypeConfigData;

    @QConfig("DomesticExamineTypeConfigData.json")
    private void onDomesticExamineTypeConfigDataChange(DomesticExamineTypeConfigDataBean domesticExamineTypeConfigData){
        //监听配置变化
        log.info("the lastest config of DomesticExamineTypeConfigData.json is:" + MapperUtil.obj2Str(domesticExamineTypeConfigData));
    }

    @QConfig("taskLevelScoreMapping.json")
    private TaskLevelScoreMappingBean taskLevelScoreMappingBean;

    @QConfig("keyProjectDashboardConfig.json")
    private KeyProjectDashboardConfigBean keyProjectDashboardConfigBean;

    @QConfig("CompetitiveTargetConfig.json")
    private CompetitiveTargetConfigBean competitiveTargetConfigBeans;

    public String getConfigValue(String key){
        return configMap.get(key);
    }

    public String getEmployeeRelationValue(String key){
        return employeeRelationMap.get(key);
    }

    public String getPkEmployeeRelationValue(String key) {
        return pkEmployeeRelationMap.get(key);
    }

    public String getOrganizationOrderValue(String key){
        return organizationOrderMap.get(key);
    }

    public String getExternalConfig(String key){return externalConfigMap.get(key);}

    public SubMetricFiledBean getSubMetricFiledBean(String metric,
                                                    String subMetric,
                                                    String field) {
        OverseaDrillDownMetadataBean copyBean = MapperUtil.str2Obj(MapperUtil.obj2Str(overseaDrillDownMetadata), OverseaDrillDownMetadataBean.class);
        for (MetricMetaDataBean metricMetaDataBean : copyBean.getMetricMetadataList()) {
            if (metricMetaDataBean.getMetricList().contains(metric)) {
                List<SubMetricFiledBean> subMetricFieldList = metricMetaDataBean.getSubMetricFieldList();
                for (SubMetricFiledBean subMetricFiledBean : subMetricFieldList) {
                    if (subMetricFiledBean.getSubMetricList().contains(subMetric) && subMetricFiledBean.getField().equals(field)) {
                        return subMetricFiledBean;
                    }
                }
            }
        }
        throw new InputArgumentException("invalid input,the metric is:" + metric + ",the subMetric is:" + subMetric + ",the field is:" + field);
    }
    public SubMetricFiledBean getSubMetricFiledBeanV2(String year,String metric,
                                                    String subMetric,
                                                    String field) {
        OverseaDrillDownMetadataBean copyBean = MapperUtil.str2Obj(MapperUtil.obj2Str(overseaDrillDownMetadataV2), OverseaDrillDownMetadataBean.class);
        for (MetricMetaDataBean metricMetaDataBean : copyBean.getMetricMetadataList()) {
            if (metricMetaDataBean.getMetricList().contains(metric) && year.equals(metricMetaDataBean.getYear())) {
                List<SubMetricFiledBean> subMetricFieldList = metricMetaDataBean.getSubMetricFieldList();
                for (SubMetricFiledBean subMetricFiledBean : subMetricFieldList) {
                    if (subMetricFiledBean.getSubMetricList().contains(subMetric) && subMetricFiledBean.getField().equals(field)) {
                        return subMetricFiledBean;
                    }
                }
            }
        }
        throw new InputArgumentException("invalid input,the metric is:" + metric + ",the subMetric is:" + subMetric + ",the field is:" + field);
    }
    public SubMetricFiledBean getSubMetricFiledBeanV2(String metric,
                                                      String subMetric,
                                                      String field) {
        OverseaDrillDownMetadataBean copyBean = MapperUtil.str2Obj(MapperUtil.obj2Str(overseaDrillDownMetadataV2), OverseaDrillDownMetadataBean.class);
        for (MetricMetaDataBean metricMetaDataBean : copyBean.getMetricMetadataList()) {
            if (metricMetaDataBean.getMetricList().contains(metric)) {
                List<SubMetricFiledBean> subMetricFieldList = metricMetaDataBean.getSubMetricFieldList();
                for (SubMetricFiledBean subMetricFiledBean : subMetricFieldList) {
                    if (subMetricFiledBean.getSubMetricList().contains(subMetric) && subMetricFiledBean.getField().equals(field)) {
                        return subMetricFiledBean;
                    }
                }
            }
        }
        throw new InputArgumentException("invalid input,the metric is:" + metric + ",the subMetric is:" + subMetric + ",the field is:" + field);
    }


    /**
     * 2024年指标口径新增or变化后 需要根据年份进行匹配 不同年份的数据
     * 如果日期是2024，需要匹配年份
     * 如果日期不等于2024，不匹配年份查询
     * @param year
     * @param metric
     * @param subMetric
     * @param field
     * @return
     */
    public SubMetricFiledBean getSubMetricFiledBean(String year,
                                                    String metric,
                                                    String subMetric,
                                                    String field) {
        OverseaDrillDownMetadataBean copyBean = MapperUtil.str2Obj(MapperUtil.obj2Str(overseaDrillDownMetadata), OverseaDrillDownMetadataBean.class);
        for (MetricMetaDataBean metricMetaDataBean : copyBean.getMetricMetadataList()) {
            if (metricMetaDataBean.getMetricList().contains(metric) && year.equals(metricMetaDataBean.getYear())) {
                List<SubMetricFiledBean> subMetricFieldList = metricMetaDataBean.getSubMetricFieldList();
                for (SubMetricFiledBean subMetricFiledBean : subMetricFieldList) {
                    if (subMetricFiledBean.getSubMetricList().contains(subMetric) && subMetricFiledBean.getField().equals(field)) {
                        return subMetricFiledBean;
                    }
                }
            }
        }
        throw new InputArgumentException("invalid input,the metric is:" + metric + ",the subMetric is:" + subMetric + ",the field is:" + field);
    }

    public DomesticSingelDrillBaseInfo getDrillDownFieldBeanV2(String metric,
                                                               Integer businessId, Integer subBusinessId,
                                                               String field) {
        DomesticSingelDrillBaseInfo singelDrillBaseInfo = new DomesticSingelDrillBaseInfo();
        DomesticDrillDownBaseInfoBean copyBean = MapperUtil.str2Obj(MapperUtil.obj2Str(domesticDrillBaseInfoBean), DomesticDrillDownBaseInfoBean.class);
        for (DomesticDrillBaseInfoBean metricMetaDataBean : copyBean.getDomesticDrillBaseInfoBeanList()) {
            for (String metricByConfig : metricMetaDataBean.getMetric()) {
                if (metricByConfig.equalsIgnoreCase(metric)) {
                    for (String fieldByConfig : metricMetaDataBean.getFields()) {
                        if (fieldByConfig.equalsIgnoreCase(field) && metricMetaDataBean.getBusinessId().equals(businessId.toString())
                                && (StringUtils.isEmpty(metricMetaDataBean.getSubBusinessId())
                                || metricMetaDataBean.getSubBusinessId().equals(subBusinessId.toString()))
                        ) {
                            singelDrillBaseInfo = new DomesticSingelDrillBaseInfo();
                            singelDrillBaseInfo.setNeedBubble(metricMetaDataBean.getNeedBubble());
                            singelDrillBaseInfo.setNeedTrend(metricMetaDataBean.getNeedTrend());
                            return singelDrillBaseInfo;
                        }
                    }
                }
            }
        }
        return singelDrillBaseInfo;
    }

    public DrillDownFieldBean getDrillDownFieldBean(String metric,
                                                    String subMetric,
                                                    String field) {
        DomesticDrillDownMetadataBean copyBean = MapperUtil.str2Obj(MapperUtil.obj2Str(domesticDrillDownMetadata), DomesticDrillDownMetadataBean.class);
        for (MetricMetaDataBean metricMetaDataBean : copyBean.getMetricMetadataList()) {
            if (metricMetaDataBean.getMetricList().contains(metric)) {
                List<DrillDownFieldBean> drillDownFieldList = metricMetaDataBean.getDrillDownFieldList();
                for (DrillDownFieldBean drillDownFieldBean : drillDownFieldList) {
                    if (drillDownFieldBean.getField().equals(field)) {
                        if(GeneralUtil.isNotEmpty(subMetric)){
                            //如果需要考虑子指标 就需要判断子指标和配置里的子指标是不是相同
                            if(subMetric.equals(drillDownFieldBean.getSubMetric())){
                                return drillDownFieldBean;
                            }
                        }else{
                            //如果不需要考虑子指标 那么就直接返回
                            return drillDownFieldBean;
                        }
                    }
                }
            }

        }
        throw new InputArgumentException("invalid input,the metric is:" + metric + ",the subMetric is:" + subMetric + ",the field is:" + field);

    }

    /**
     * 2024年指标口径新增or变化后 需要根据年份进行匹配 不同年份的数据
     * 如果日期是2024，需要匹配年份
     * 如果日期不等于2024，不匹配年份查询
     * @param metric
     * @param subMetric
     * @param field
     * @return
     */
    public DrillDownFieldBean getDrillDownFieldBeanNew(String year,
                                                    String metric,
                                                    String subMetric,
                                                    String field) {
        DomesticDrillDownMetadataBean copyBean = MapperUtil.str2Obj(MapperUtil.obj2Str(domesticDrillDownMetadata), DomesticDrillDownMetadataBean.class);
        for (MetricMetaDataBean metricMetaDataBean : copyBean.getMetricMetadataList()) {
            if (metricMetaDataBean.getMetricList().contains(metric) && metricMetaDataBean.getYear().equals(year)) {
                List<DrillDownFieldBean> drillDownFieldList = metricMetaDataBean.getDrillDownFieldList();
                for (DrillDownFieldBean drillDownFieldBean : drillDownFieldList) {
                    if (drillDownFieldBean.getField().equals(field)) {
                        if(GeneralUtil.isNotEmpty(subMetric)){
                            //如果需要考虑子指标 就需要判断子指标和配置里的子指标是不是相同
                            if(subMetric.equals(drillDownFieldBean.getSubMetric())){
                                return drillDownFieldBean;
                            }
                        }else{
                            //如果不需要考虑子指标 那么就直接返回
                            return drillDownFieldBean;
                        }
                    }
                }
            }

        }
        throw new InputArgumentException("invalid input,the metric is:" + metric + ",the subMetric is:" + subMetric + ",the field is:" + field);

    }

    public List<List<String>> getMergedSubMetricDataList() {
        DomesticExamineTypeConfigDataBean copyBean = MapperUtil.str2Obj(MapperUtil.obj2Str(domesticExamineTypeConfigData), DomesticExamineTypeConfigDataBean.class);
        return copyBean.getMergedSubMetricDataList();
    }

    public ExamineTypeBean getExamineTypeBean(Integer examineType) {
        DomesticExamineTypeConfigDataBean copyBean = MapperUtil.str2Obj(MapperUtil.obj2Str(domesticExamineTypeConfigData), DomesticExamineTypeConfigDataBean.class);
        for (ExamineTypeBean examineTypeBean : copyBean.getExamineTypeMetadataList()) {
            List<Integer> examineTypeList = examineTypeBean.getExamineTypeList();
            if (examineTypeList.contains(examineType)) {
                return examineTypeBean;
            }
        }
        throw new InputArgumentException("invalid config,the examineType is:" + examineType);
    }

    /**
     * 从qconfig拿到对应的子指标权限枚举值
     * @param metricCode
     * @return
     */
    public List<SubMetricPermissonMappingBean> getSubMetricPermissonMapping(String metricCode){
        DomesticExamineTypeConfigDataBean copyBean = MapperUtil.str2Obj(MapperUtil.obj2Str(domesticExamineTypeConfigData), DomesticExamineTypeConfigDataBean.class);
        return copyBean.getSubMetricPermissonEnumMap().get(metricCode);
    }

    public TaskLevelScoreMappingBean getTaskLevelScoreMappingBean(){
        return MapperUtil.str2Obj(MapperUtil.obj2Str(taskLevelScoreMappingBean),TaskLevelScoreMappingBean.class);
    }

    public List<KeyProjectDashboardConfigContentBean> getKeyProjectDashboardConfigList(){
        return MapperUtil.str2List(MapperUtil.obj2Str(keyProjectDashboardConfigBean.getConfigList()),KeyProjectDashboardConfigContentBean.class);
    }

    /**
     * @param year       年份
     * @param brand      客路or飞猪
     * @param metric     指标值
     * @param staticType 核心聚焦大盘
     * @param timeType   quarter or half
     * @return
     */
    public Integer getCompetitiveTarget(String year, String brand, String metric, String staticType, String timeType) {
        List<CompetitiveTargetDetailConfigBean> configList = MapperUtil.str2List(MapperUtil.obj2Str(competitiveTargetConfigBeans.getCompetitiveTargetDetailConfigBeans()), CompetitiveTargetDetailConfigBean.class);
        Optional<Integer> result = configList.stream()
                .filter(config -> Objects.equals(config.getYear(), year)
                        && Objects.equals(config.getBrand(), brand)
                        && Objects.equals(config.getMetric(), metric)
                        && Objects.equals(config.getStaticType(), staticType))
                .findFirst()
                .map(config -> {
                    try {
                        // 使用反射动态获取字段值
                        Field field = config.getClass().getDeclaredField(timeType.toLowerCase());
                        field.setAccessible(true);
                        Object value = field.get(config);
                        return value != null && !value.toString().isEmpty()
                                ? Integer.parseInt(value.toString())
                                : 0;
                    } catch (Exception e) {
                        return 0;
                    }
                });

        return result.orElse(0);
    }

}
