package com.ctrip.tour.business.dashboard.grpBusiness.job;

import static com.ctrip.tour.business.dashboard.grpBusiness.common.GrpConstant.MULT_PRICE_GREAT_THAN_SETTING;
import static com.ctrip.tour.business.dashboard.grpBusiness.common.GrpConstant.MULT_PRICE_LESS_THAN_SETTING;
import static com.ctrip.tour.business.dashboard.grpBusiness.common.GrpConstant.SELF_SERVICE_COVERAGE_RATE_SETTING;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.tour.business.dashboard.grpBusiness.bo.OrderEventInfoDTO;
import com.ctrip.tour.business.dashboard.grpBusiness.bo.TrippalDecorateInfoDTO;
import com.ctrip.tour.business.dashboard.grpBusiness.bo.TrippalInfoDTO;
import com.ctrip.tour.business.dashboard.grpBusiness.common.GrpConstant;
import com.ctrip.tour.business.dashboard.grpBusiness.dao.AdmPrdGrpPrdidDepctyPrcedateOnlinePrDfDao;
import com.ctrip.tour.business.dashboard.grpBusiness.dao.starrocks.AdmPrdGrpMultiplePriceWorkPlatformDfDao;
import com.ctrip.tour.business.dashboard.grpBusiness.entity.AdmPrdGrpPrdidDepctyPrcedateOnlinePrDf;
import com.ctrip.tour.business.dashboard.grpBusiness.entity.EdwHrEmpVacation;
import com.ctrip.tour.business.dashboard.grpBusiness.handler.metric.selfSrv.GrpCrpSlfSrvCoverageQueryService;
import com.ctrip.tour.business.dashboard.grpBusiness.service.HrOrgEmpInfoService;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.utils.MapperUtil;
import com.ctrip.tour.group.workbenchsvc.contract.GetAlertThresholdSettingListInfoType;
import com.ctrip.tour.group.workbenchsvc.contract.GetAlertThresholdSettingListRequestType;
import com.ctrip.tour.group.workbenchsvc.contract.GetAlertThresholdSettingListResponseType;
import com.ctrip.tour.group.workbenchsvc.contract.StructuredTableInfoType;
import com.ctrip.tour.group.workbenchsvc.contract.StructuredTableRowInfoType;
import com.ctrip.tour.group.workbenchsvc.contract.TaskPropertyType;
import com.ctrip.tour.group.workbenchsvc.contract.WbCreateTaskRequestType;
import com.ctrip.tour.group.workbenchsvc.contract.WbCreateTaskResponseType;
import com.ctrip.tour.rights.client.GroupWorkbenchServiceClient;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import lombok.extern.slf4j.Slf4j;
import qunar.tc.qschedule.config.QSchedule;

/**
 * <AUTHOR>
 * @Date 2024/12/16
 */
@Service
@Slf4j
public class AlertNotifyService {


    private static final String CREATOR_EID = "CREATOR_EID";
    private static final String EVENT_TYPE = "CREATOR_EID";
    private static final String EVENT_TYPE_XXX_STRUCTURED_TABLE = "CREATOR_EID";
    private static final String TASK_PRICE_MULTIPAL_ALERT = "TASK_PRICE_MULTIPAL_ALERT";
    private static final String TASK_SELF_SERVICE_COVERAGE_ALERT = "TASK_SELF_SERVICE_COVERAGE_ALERT";
    private static final String EVENT_TYPE_STRUCTURED_TABLE = "EVENT_SELF_SERVICE_COVERAGE_ALERT";
    private static final String EVENT_PRICE_MULTIPAL_ALERT = "EVENT_PRICE_MULTIPAL_ALERT";
    private static final String EVENT_PRICE_MULTIPAL_ALERT_STRUCTURED_TABLE = "EVENT_PRICE_MULTIPAL_ALERT_STRUCTURED_TABLE";
    private static final String EVENT_SELF_SERVICE_COVERAGE_ALERT_STRUCTURED_TABLE = "EVENT_SELF_SERVICE_COVERAGE_ALERT_STRUCTURED_TABLE";
    private static final String ALERT_RULE_ID = "ALERT_RULE_ID";
    private static final String REDIRECT_URL = "http://vendor.package.ctripcorp.com/product/input/priceCompetitiveness?from=6381&productid=";

    @Autowired
    private HrOrgEmpInfoService hrOrgEmpInfoService;
    @Autowired
    AdmPrdGrpMultiplePriceWorkPlatformDfDao multiplePriceDfDao;
    @Autowired
    private GrpCrpSlfSrvCoverageQueryService coverageQueryService;
    @Autowired
    private AdmPrdGrpPrdidDepctyPrcedateOnlinePrDfDao prdfDao;
    @Autowired
    private RemoteConfig remoteConfig;

    private static String NOTIFY_EVENT_EMPCODES = "notify.event.empcodes";

   private GroupWorkbenchServiceClient client = GroupWorkbenchServiceClient.getInstance();

    @QSchedule("com.ctrip.tour.business.dashboard.grpBusiness.job.grpLessThanNotify")
    public void grpLessThanNotify() {

        try {
            handleMultPriceGrp(true);
        } catch (Exception e) {
            log.warn("grpNotify error", e);
        }

    }
    @QSchedule("com.ctrip.tour.business.dashboard.grpBusiness.job.grpGreatThanNotify")
    public void grpGreatThanNotify() {

        try {
            handleMultPriceGrp(false);
        } catch (Exception e) {
            log.warn("grpNotify error", e);
        }

    }
    @QSchedule("com.ctrip.tour.business.dashboard.grpBusiness.job.selfCovNotify")
    public void selfCovNotify() {

        try {
            handleSelfSrvCov();
        } catch (Exception e) {
            log.warn("grpNotify error", e);
        }

    }
    @QSchedule("com.ctrip.tour.business.dashboard.grpBusiness.job.prvLessThanNotify")
    public void prvLessThanNotify() {

        try {
            handleMultPricePrv(true);
        } catch (Exception e) {
            log.warn("grpNotify error", e);
        }

    }
    @QSchedule("com.ctrip.tour.business.dashboard.grpBusiness.job.prvGreatThanNotify")
    public void prvGreatThanNotify() {

        try {
            handleMultPricePrv(false);
        } catch (Exception e) {
            log.warn("grpNotify error", e);
        }

    }

    public void notifyEmp(String empCode, StructuredTableInfoType structuredTableInfoType, String taskName,
                          String taskTypeCode, String eventType, String eventStruTable, String content, List<String> tpInfo, String ruleId) {

        WbCreateTaskRequestType requestType = new WbCreateTaskRequestType();
        requestType.setTaskName(taskName);
        requestType.setTaskTypeCode(taskTypeCode);

        TaskPropertyType creatorProp = new TaskPropertyType();
        creatorProp.setKey(CREATOR_EID);
        creatorProp.setValue("100038120");

        TaskPropertyType ruleProp = new TaskPropertyType();
        ruleProp.setKey(ALERT_RULE_ID);
        ruleProp.setValue(ruleId);

        TaskPropertyType eventTypeProp = new TaskPropertyType();
        eventTypeProp.setKey(eventType);
        OrderEventInfoDTO orderEventInfoDTO = generateEventInfo(Lists.newArrayList(empCode), content, tpInfo, taskName);

        eventTypeProp.setValue(MapperUtil.obj2Str(orderEventInfoDTO));

        TaskPropertyType structProp = new TaskPropertyType();
        structProp.setKey(eventStruTable);
        structProp.setValue(MapperUtil.obj2Str(structuredTableInfoType));

        List<TaskPropertyType> taskPropertyTypes = Lists.newArrayList(creatorProp, eventTypeProp, structProp, ruleProp);

        requestType.setTaskPropertyList(taskPropertyTypes);

        try {
            WbCreateTaskResponseType wbCreateTaskResponseType = client.wbCreateTask(requestType);
            log.info("notify success , task id is" + wbCreateTaskResponseType.getTaskId());
        } catch (Exception e) {
            //工作台把自己业务报错也抛到我们系统了
            log.warn("can not create task ", e);
        }

    }

    private OrderEventInfoDTO generateEventInfo(List<String> empCodes, String content, List<String> tpInfo, String taskName) {

        OrderEventInfoDTO orderEventInfoDTO = new OrderEventInfoDTO();
        orderEventInfoDTO.setProcessorEidList(empCodes);
        orderEventInfoDTO.setContent(content);

        TrippalInfoDTO trippalInfoDTO = new TrippalInfoDTO();
        trippalInfoDTO.setTitle(taskName);

        TrippalDecorateInfoDTO trippalDecorateInfoDTO = new TrippalDecorateInfoDTO();
        trippalDecorateInfoDTO.setIdx(0L);
        trippalDecorateInfoDTO.setType(1);
        trippalDecorateInfoDTO.setTag("p");

        TrippalDecorateInfoDTO childrenDecInfo = new TrippalDecorateInfoDTO();
        childrenDecInfo.setIdx(0L);
        childrenDecInfo.setType(0);
        childrenDecInfo.setText(tpInfo.get(0));

        TrippalDecorateInfoDTO childrenDecInfo2 = new TrippalDecorateInfoDTO();
        childrenDecInfo2.setIdx(1L);
        childrenDecInfo2.setType(1);
        childrenDecInfo2.setTag("font");
        childrenDecInfo2.setAttrs(ImmutableMap.of("color", "red"));

        TrippalDecorateInfoDTO childChildrenDecInfo = new TrippalDecorateInfoDTO();
        childChildrenDecInfo.setIdx(0L);
        childChildrenDecInfo.setType(0);
        childChildrenDecInfo.setText(tpInfo.get(1));
        childrenDecInfo2.setChildren(Lists.newArrayList(childChildrenDecInfo));

        TrippalDecorateInfoDTO childrenDecInfo3 = new TrippalDecorateInfoDTO();
        childrenDecInfo3.setIdx(2L);
        childrenDecInfo3.setType(0);
        childrenDecInfo3.setText(tpInfo.get(2));

        trippalDecorateInfoDTO.setChildren(Lists.newArrayList(childrenDecInfo, childrenDecInfo2, childrenDecInfo3));
        trippalInfoDTO.setContent(Lists.newArrayList(trippalDecorateInfoDTO));

        orderEventInfoDTO.setTrippalInfo(trippalInfoDTO);

        return orderEventInfoDTO;
    }


    private void handleMultPriceGrp(boolean isLessThan) throws Exception {

        String whiteEmpCodesStr = remoteConfig.getExternalConfig(NOTIFY_EVENT_EMPCODES);
        List<String> whiteEmpCodes = Lists.newArrayList();
        if (StringUtils.isNotBlank(whiteEmpCodesStr)) {
            whiteEmpCodes = Splitter.on(",").splitToList(whiteEmpCodesStr);
        }

        GetAlertThresholdSettingListRequestType requestType = new GetAlertThresholdSettingListRequestType();
        GetAlertThresholdSettingListResponseType alertThresholdSettingList = client.getAlertThresholdSettingList(requestType);

        Map<String, List<GetAlertThresholdSettingListInfoType>> indexThreMap = alertThresholdSettingList.getAlertThresholdSettingList()
                .stream().collect(Collectors.groupingBy(GetAlertThresholdSettingListInfoType::getIndexCode));

        GetAlertThresholdSettingListInfoType lessThanSetting = null;
        if (isLessThan) {
            List<GetAlertThresholdSettingListInfoType> lessSettings = indexThreMap.get(GrpConstant.MULTIPLE_PRICE_LESS_RATE);
            lessThanSetting = lessSettings.stream().filter(l -> StringUtils.equalsIgnoreCase(MULT_PRICE_LESS_THAN_SETTING, l.getThresholdKey()))
                    .findFirst().orElse(null);
        } else {
            List<GetAlertThresholdSettingListInfoType> lessSettings = indexThreMap.get(GrpConstant.MULTIPLE_PRICE_MORE_RATE);
            lessThanSetting = lessSettings.stream().filter(l -> StringUtils.equalsIgnoreCase(MULT_PRICE_GREAT_THAN_SETTING, l.getThresholdKey()))
                    .findFirst().orElse(null);
        }

        if (Objects.isNull(lessThanSetting)) {
            return;
        }
        double threshold = Double.parseDouble(lessThanSetting.getThresholdVal());

        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");


        List<Map<String, Object>> empList = multiplePriceDfDao.query("select pm_eid,local_pmeid from adm_prd_grp_multiple_price_work_platform_df where view_date="+"'"+ dtf.format(LocalDate.now().minusDays(1))+"' and sub_bu_type='跟团游' and partition_d='"+ dtf.format(LocalDate.now()) +"'  group by pm_eid, local_pmeid", Maps.newHashMap());//NOSONAR

        List<String> finalWhiteEmpCodes = whiteEmpCodes;
        List<String> empCodes = empList.stream()
                .filter(pw -> CollectionUtils.isEmpty(finalWhiteEmpCodes) || (Objects.nonNull(pw) && (finalWhiteEmpCodes.contains((String)pw.get("pm_eid")) ||
                        finalWhiteEmpCodes.contains((String)pw.get("local_pmeid")))))
                .map(pw -> {
            List<String> result = Lists.newArrayList();
            Object pm_eid = pw.get("pm_eid");
            Object local_pm_eid = pw.get("local_pmeid");
            if (Objects.nonNull(pm_eid) && !StringUtils.equalsIgnoreCase((String)pm_eid, "unkwn")) {
                result.add((String) pm_eid);
            }
            if (Objects.nonNull(local_pm_eid) && !StringUtils.equalsIgnoreCase((String)local_pm_eid, "unkwn")) {
                result.add((String)local_pm_eid);
            }
            return result;
        }).flatMap(Collection::stream).distinct().collect(Collectors.toList());

        for (String empCode : empCodes) {
            try {
                StructuredTableInfoType structuredTableInfoType = new StructuredTableInfoType();
                HashMap<String, Object> queryParam = Maps.newHashMap();
                queryParam.put("view_date", dtf.format(LocalDate.now()));
                queryParam.put("local_pm_eid", empCode);
                queryParam.put("avg_multiple_price", threshold);
                queryParam.put("pm_eid", empCode);
                String sql = "select * from adm_prd_grp_multiple_price_work_platform_df where  (pm_eid = '" + empCode + "' or local_pmeid  = '" + empCode + "') and view_date=" + "'"+ dtf.format(LocalDate.now().minusDays(1))+"'" + " and sub_bu_type='跟团游' and partition_d='"+ dtf.format(LocalDate.now()) +"'";//NOSONAR
                List<Map<String, Object>> multiplePriceWorkPlatformDfs = multiplePriceDfDao.query(sql,
                        queryParam);
                if (CollectionUtils.isEmpty(multiplePriceWorkPlatformDfs)) {
                    continue;
                }
                List<Long> prdIds = multiplePriceWorkPlatformDfs.stream().map(m -> (Long)m.get("productid"))
                        .collect(Collectors.toList());
                List<AdmPrdGrpPrdidDepctyPrcedateOnlinePrDf> prcedateOnlinePrDfs = prdfDao.query("prd_id in (?) and is_focus_prd='核心'", new DalHints(), prdIds);//NOSONAR
                if (CollectionUtils.isEmpty(prcedateOnlinePrDfs)) {
                    continue;
                }
                List<Long> prcPrdIds = prcedateOnlinePrDfs.stream().map(AdmPrdGrpPrdidDepctyPrcedateOnlinePrDf::getPrdId)
                        .collect(Collectors.toList());
                List<Map<String, Object>>gtMultiPrices = multiplePriceWorkPlatformDfs.stream().filter(mp -> {
                    double avgMultiplePrice = (double)mp.get("avg_multiple_price");
                    long prdId = (long)mp.get("productid");
                    boolean isMatchFlag = false;
                    if (isLessThan) {
                        isMatchFlag = avgMultiplePrice < threshold;
                    } else {
                        isMatchFlag = avgMultiplePrice > threshold;
                    }
                    return prcPrdIds.contains(prdId) && isMatchFlag;
                }).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(gtMultiPrices)) {
                    continue;
                }
                List<StructuredTableRowInfoType> rowInfoTypes = gtMultiPrices.stream().map(p -> {

                    Long productid = (Long)p.get("productid");
                    Integer vendorId = (Integer)p.get("vendor_id");
                    Double avgMultiplePrice = (Double) p.get("avg_multiple_price");

                    StructuredTableRowInfoType rowInfoType = new StructuredTableRowInfoType();
                    List<String> colList = Lists.newArrayList(String.valueOf(vendorId), buildHref(REDIRECT_URL+ productid, productid), buildMultiPriceFontText(avgMultiplePrice), buildExceptionContent(isLessThan, threshold));
                    rowInfoType.setColList(colList);
                    return rowInfoType;
                }).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(rowInfoTypes)) {
                    return;
                }
                structuredTableInfoType.setRowList(rowInfoTypes);
                structuredTableInfoType.setHeaderList(Lists.newArrayList("供应商ID", "产品ID", "价格倍数", "异常原因"));//NOSONAR
                String notifyContent = buildGrpNotifyContent(isLessThan, threshold);
                List<String> tpInfos = buildGrpNotifyTpInfo(isLessThan, threshold);
                notifyEmp(empCode, structuredTableInfoType, "价格倍数预警通知",//NOSONAR
                        TASK_PRICE_MULTIPAL_ALERT, EVENT_PRICE_MULTIPAL_ALERT, EVENT_PRICE_MULTIPAL_ALERT_STRUCTURED_TABLE, notifyContent, tpInfos, String.valueOf(lessThanSetting.getId()));//NOSONAR
            } catch (Exception e) {
                log.warn("sendMultPriceGrp error", e);
            }
        }


    }

    private void handleMultPricePrv(boolean isLessThan) throws Exception {

        String whiteEmpCodesStr = remoteConfig.getExternalConfig(NOTIFY_EVENT_EMPCODES);
        List<String> whiteEmpCodes = Lists.newArrayList();
        if (StringUtils.isNotBlank(whiteEmpCodesStr)) {
            whiteEmpCodes = Splitter.on(",").splitToList(whiteEmpCodesStr);
        }

        GetAlertThresholdSettingListRequestType requestType = new GetAlertThresholdSettingListRequestType();
        GetAlertThresholdSettingListResponseType alertThresholdSettingList = client.getAlertThresholdSettingList(requestType);

        Map<String, List<GetAlertThresholdSettingListInfoType>> indexThreMap = alertThresholdSettingList.getAlertThresholdSettingList()
                .stream().collect(Collectors.groupingBy(GetAlertThresholdSettingListInfoType::getIndexCode));

        GetAlertThresholdSettingListInfoType lessThanSetting = null;
        if (isLessThan) {
            List<GetAlertThresholdSettingListInfoType> lessSettings = indexThreMap.get(GrpConstant.MULTIPLE_PRICE_LESS_RATE);
            lessThanSetting = lessSettings.stream().filter(l -> StringUtils.equalsIgnoreCase(MULT_PRICE_LESS_THAN_SETTING, l.getThresholdKey()))
                    .findFirst().orElse(null);
        } else {
            List<GetAlertThresholdSettingListInfoType> lessSettings = indexThreMap.get(GrpConstant.MULTIPLE_PRICE_MORE_RATE);
            lessThanSetting = lessSettings.stream().filter(l -> StringUtils.equalsIgnoreCase(MULT_PRICE_GREAT_THAN_SETTING, l.getThresholdKey()))
                    .findFirst().orElse(null);
        }

        if (Objects.isNull(lessThanSetting)) {
            return;
        }
        double threshold = Double.parseDouble(lessThanSetting.getThresholdVal());

        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        HashMap<String, Object> param = Maps.newHashMap();
        param.put("11", dtf.format(LocalDate.now()));
        param.put("22", threshold);

        List<Map<String, Object>> empList = multiplePriceDfDao.query("select pm_eid from adm_prd_grp_multiple_price_work_platform_df where view_date='"+ dtf.format(LocalDate.now().minusDays(1))+"' and sub_bu_type='独立出游' and partition_d='"+ dtf.format(LocalDate.now()) +"' group by pm_eid",param);//NOSONAR

        List<String> finalWhiteEmpCodes = whiteEmpCodes;
        List<String> empCodes = empList.stream()
                .filter(pw -> CollectionUtils.isEmpty(finalWhiteEmpCodes) || (Objects.nonNull(pw) && finalWhiteEmpCodes.contains((String)pw.get("pm_eid"))))
                .map(pw -> {

            Object pm_eid = pw.get("pm_eid");
            if (Objects.nonNull(pm_eid) && !StringUtils.equalsIgnoreCase((String)pm_eid, "unkwn")) {
                return (String)pm_eid;
            }

            return null;
        }).filter(Objects::nonNull).distinct().collect(Collectors.toList());


        for (String empCode : empCodes) {
            try {
                StructuredTableInfoType structuredTableInfoType = new StructuredTableInfoType();
                HashMap<String, Object> queryParam = Maps.newHashMap();
                queryParam.put("view_date", dtf.format(LocalDate.now()));
                queryParam.put("local_pm_eid", empCode);
                queryParam.put("avg_multiple_price", threshold);
                queryParam.put("pm_eid", empCode);
                List<Map<String, Object>> multiplePriceWorkPlatformDfs = multiplePriceDfDao.query("select * from adm_prd_grp_multiple_price_work_platform_df where pm_eid = '"+empCode+"' and view_date='"+dtf.format(LocalDate.now().minusDays(1))+"' and sub_bu_type='独立出游' and partition_d='"+ dtf.format(LocalDate.now()) +"'",//NOSONAR
                        queryParam);
                if (CollectionUtils.isEmpty(multiplePriceWorkPlatformDfs)) {
                    continue;
                }
                List<Long> prdIds = multiplePriceWorkPlatformDfs.stream().map(m -> (Long)m.get("productid"))
                        .collect(Collectors.toList());
                List<AdmPrdGrpPrdidDepctyPrcedateOnlinePrDf> prcedateOnlinePrDfs = prdfDao.query("prd_id in (?) and vendor_flag='核心供应商'", new DalHints(), prdIds);//NOSONAR
                if (CollectionUtils.isEmpty(prcedateOnlinePrDfs)) {
                    continue;
                }
                List<Long> prcPrdIds = prcedateOnlinePrDfs.stream().map(AdmPrdGrpPrdidDepctyPrcedateOnlinePrDf::getPrdId)
                        .collect(Collectors.toList());
                List<Map<String, Object>>gtMultiPrices = multiplePriceWorkPlatformDfs.stream().filter(mp -> {
                    double avgMultiplePrice = (double)mp.get("avg_multiple_price");
                    long prdId = (long)mp.get("productid");
                    boolean isMatchFlag = false;
                    if (isLessThan) {
                        isMatchFlag = avgMultiplePrice < threshold;
                    } else {
                        isMatchFlag = avgMultiplePrice > threshold;
                    }
                    return prcPrdIds.contains(prdId) && isMatchFlag;
                }).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(gtMultiPrices)) {
                    continue;
                }
                List<StructuredTableRowInfoType> rowInfoTypes = gtMultiPrices.stream().map(p -> {

                    Long productid = (Long)p.get("productid");
                    Integer vendorId = (Integer)p.get("vendor_id");
                    Double avgMultiplePrice = (Double) p.get("avg_multiple_price");

                    StructuredTableRowInfoType rowInfoType = new StructuredTableRowInfoType();
                    List<String> colList = Lists.newArrayList(String.valueOf(vendorId), buildHref(REDIRECT_URL+ productid, productid), buildMultiPriceFontText(avgMultiplePrice), buildExceptionContent(isLessThan, threshold));
                    rowInfoType.setColList(colList);
                    return rowInfoType;
                }).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(rowInfoTypes)) {
                    return;
                }
                structuredTableInfoType.setRowList(rowInfoTypes);
                structuredTableInfoType.setHeaderList(Lists.newArrayList("供应商ID", "产品ID", "价格倍数", "异常原因"));//NOSONAR
                String content = buildPrvNotifyContent(isLessThan, threshold);
                List<String> tpInfos = buildPrvNotifyTpInfo(isLessThan, threshold);
                notifyEmp(empCode, structuredTableInfoType, "价格倍数预警通知",//NOSONAR
                        TASK_PRICE_MULTIPAL_ALERT, EVENT_PRICE_MULTIPAL_ALERT, EVENT_PRICE_MULTIPAL_ALERT_STRUCTURED_TABLE, content, tpInfos, String.valueOf(lessThanSetting.getId()));
            } catch (Exception e) {
                log.warn("sendhandleMultPricePrv error", e);
            }
        }


    }

    private void handleSelfSrvCov() throws Exception {

        String whiteEmpCodesStr = remoteConfig.getExternalConfig(NOTIFY_EVENT_EMPCODES);

        List<String> whiteEmpCodes = Lists.newArrayList();
        if (StringUtils.isNotBlank(whiteEmpCodesStr)) {
            whiteEmpCodes = Splitter.on(",").splitToList(whiteEmpCodesStr);
        }

        GetAlertThresholdSettingListRequestType requestType = new GetAlertThresholdSettingListRequestType();
        GetAlertThresholdSettingListResponseType alertThresholdSettingList = client.getAlertThresholdSettingList(requestType);

        Map<String, List<GetAlertThresholdSettingListInfoType>> indexThreMap = alertThresholdSettingList.getAlertThresholdSettingList()
                .stream().collect(Collectors.groupingBy(GetAlertThresholdSettingListInfoType::getIndexCode));

            List<GetAlertThresholdSettingListInfoType> lessSettings = indexThreMap.get(GrpConstant.SELF_SERVICE_COVERAGE_RATE);
        GetAlertThresholdSettingListInfoType  lessThanSetting = lessSettings.stream().filter(l -> StringUtils.equalsIgnoreCase(SELF_SERVICE_COVERAGE_RATE_SETTING, l.getThresholdKey()))
                    .findFirst().orElse(null);


        if (Objects.isNull(lessThanSetting)) {
            return;
        }
        double threshold = Double.parseDouble(lessThanSetting.getThresholdVal());

        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        String sql = "SELECT\n" +
                "    slf_cnt,\n" +
                "    slf_cnt / (slf_cnt + ctg_cnt) AS slf_cov,\n" +
                "    vendor_id,\n" +
                "    pm_eid\n" +
                "FROM (\n" +
                "    SELECT\n" +
                "        COUNT(DISTINCT CASE \n" +
                "            WHEN second_session_type = '商户自服务会话' THEN uid \n" +//NOSONAR
                "        END) AS slf_cnt,\n" +
                "        COUNT(DISTINCT CASE \n" +
                "            WHEN second_session_type = '携程服务会话' THEN uid \n" +//NOSONAR
                "        END) AS ctg_cnt,\n" +
                "        vendor_id,\n" +
                "        pm_eid\n" +
                "    FROM\n" +
                "        cdm_sev_grp_cpr_platform_self_srv_cr_df\n" +
                "    WHERE\n" +
                "        chat_create_date = '"+dtf.format(LocalDate.now().minusDays(1))+"'\n" +
                "        AND sub_bu_type = '独立出游' and pm_eid !='unkwn' and partition_d='" + dtf.format(LocalDate.now()) + "' " +//NOSONAR
                "    GROUP BY\n" +
                "        vendor_id,\n" +
                "        pm_eid\n" +
                ") AS subquery\n" +
                "WHERE\n" +
                "    slf_cnt / (slf_cnt + ctg_cnt) < "+ threshold +";";

        List<Map<String, Object>> resultList = multiplePriceDfDao.query(sql, Maps.newHashMap());
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }

        List<String> finalWhiteEmpCodes = whiteEmpCodes;
        Map<String, List<Map<String, Object>>> resultGroupByPmEid = resultList.stream()
                .filter(pw -> CollectionUtils.isEmpty(finalWhiteEmpCodes) || (Objects.nonNull(pw) && finalWhiteEmpCodes.contains((String)pw.get("pm_eid"))))
                .collect(Collectors.groupingBy(map -> (String) map.get("pm_eid")));

//        List<EdwHrEmpVacation> edwHrEmpVacations = hrOrgEmpInfoService.queryEmpByDomainNames(Lists.newArrayList(resultGroupByPmEid.keySet()));
//        if (CollectionUtils.isEmpty(edwHrEmpVacations)) {
//            log.warn("can not find emp info");
//            return;
//        }
//        Map<String, String> domai2EmpCodeMap = edwHrEmpVacations.stream().collect(Collectors.toMap(EdwHrEmpVacation::getDomainName, EdwHrEmpVacation::getEmpCode));
        resultGroupByPmEid.forEach((k, v) -> {
            try {
//                String eId = domai2EmpCodeMap.get(k);
//                if (StringUtils.isBlank(eId)) {
//                    log.warn("cant find empCode" + k);
//                    return;
//                }
                StructuredTableInfoType structuredTableInfoType = new StructuredTableInfoType();
                List<StructuredTableRowInfoType> rowInfoTypes = v.stream().map(p -> {
                    Long vendorId = (Long)p.get("vendor_id");
                    List<AdmPrdGrpPrdidDepctyPrcedateOnlinePrDf> prcedateOnlinePrDfs = null;
                    try {
                        prcedateOnlinePrDfs = prdfDao.query("vendor_id = ? and vendor_flag='核心供应商'", new DalHints(), vendorId);//NOSONAR
                    } catch (SQLException e) {
                        log.warn("query core vendor error", e);
                    }
                    if (CollectionUtils.isEmpty(prcedateOnlinePrDfs)) {
                        return null;
                    }
                    double slfCov = (double)p.get("slf_cov");

                    StructuredTableRowInfoType rowInfoType = new StructuredTableRowInfoType();
                    List<String> colList = Lists.newArrayList( String.valueOf(vendorId), buildSelfCovFontText(slfCov), "自服务覆盖率低于" + threshold, "");//NOSONAR
                    rowInfoType.setColList(colList);
                    return rowInfoType;
                }).filter(Objects::nonNull).collect(Collectors.toList());

                if (CollectionUtils.isEmpty(rowInfoTypes)) {
                    return;
                }
                structuredTableInfoType.setRowList(rowInfoTypes);
                structuredTableInfoType.setHeaderList(Lists.newArrayList("供应商ID", "自服务覆盖率",  "异常原因"));//NOSONAR

                String content = "核心商户的自服务覆盖率低于"+ threshold*100 +"%的异常预警请关注；";//NOSONAR
                List<String> tpInfos = Lists.newArrayList("核心商户的自服务覆盖率低于", threshold * 100 + "", "%的异常预警请关注；");//NOSONAR
                notifyEmp(k, structuredTableInfoType ,"自服务覆盖预警通知",//NOSONAR
                        TASK_SELF_SERVICE_COVERAGE_ALERT, EVENT_TYPE_STRUCTURED_TABLE, EVENT_SELF_SERVICE_COVERAGE_ALERT_STRUCTURED_TABLE, content,tpInfos, String.valueOf(lessThanSetting.getId()));//NOSONAR
            } catch (Exception e) {
                log.warn("handleSelfSrvCov error", e);
            }

        });

    }


    private String buildExceptionContent(boolean isLessThan, double threshold) {

        if (isLessThan) {
            return "价格倍数小于" + threshold;//NOSONAR
        } else {
            return "价格倍数超过" + threshold;//NOSONAR
        }
    }

    private String buildMultiPriceFontText(double val) {
        return "<font color=\"red\">"+ BigDecimal.valueOf(val).setScale(2, RoundingMode.HALF_UP).doubleValue() +"</font>";
    }

    private String buildSelfCovFontText(double val) {
        return "<font color=\"red\">"+ BigDecimal.valueOf(val).multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP).doubleValue() +"%</font>";
    }

    private String buildHref(String url, Long val) {
        return "<a href=\""+url+"\">"+val+"</a>";
    }

    private String buildGrpNotifyContent(boolean isLessThan, double threshold) {
        String template = "未来45天/90天核心产品，价格倍数?的产品异常预警请关注；";//NOSONAR
        if (isLessThan) {
            return template.replace("?", "<"+threshold);
        } else {
            return template.replace("?", ">"+threshold);
        }
    }

    private List<String> buildGrpNotifyTpInfo(boolean isLessThan, double threshold) {


        String s = "";
        if (isLessThan) {
            s = "价格倍数" + "<"+threshold;//NOSONAR
        } else {
            s =  "价格倍数"+ ">"+threshold;//NOSONAR
        }

       return Lists.newArrayList("未来45天/90天核心产品，", s, "的产品异常预警请关注；");//NOSONAR

    }

    private List<String> buildPrvNotifyTpInfo(boolean isLessThan, double threshold) {


        String s = "";
        if (isLessThan) {
            s =  "价格倍数" + "<"+threshold;//NOSONAR
        } else {
            s =  "价格倍数"+ ">"+threshold;//NOSONAR
        }

        return Lists.newArrayList("核心商户的产品，", s, "的产品异常预警关注；");//NOSONAR

    }

    private String buildPrvNotifyContent(boolean isLessThan, double threshold) {
        String template = "核心商户的产品，价格倍数?的产品异常预警关注；";//NOSONAR
        if (isLessThan) {
            return template.replace("?", "<"+threshold);
        } else {
            return template.replace("?", ">"+threshold);
        }
    }




}

