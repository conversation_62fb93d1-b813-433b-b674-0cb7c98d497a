package com.ctrip.tour.business.dashboard.sightArchives.dao.flowDao;

import com.ctrip.soa._24922.FlowSource;
import com.ctrip.soa._27181.PreparedParameterBean;
import com.ctrip.tour.business.dashboard.sightArchives.enums.sales.SalesMetricEnumType;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.TktStarRocksDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.sql.Types;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class CdmLogTtdViewspotBenchTrafficFromDiDao {
    @Autowired
    private TktStarRocksDao tktStarRocksDao;

    public List<Map<String,Object>> queryFlowSourceList(Long sightId, String startDate, String endDate, Boolean needSubSight, Integer dateType, Integer businessType, List<Long> vendorIdList) {
        List<FlowSource> res = new ArrayList<>();
        //流量来源分布
        StringBuilder sql = new StringBuilder("select channel as channelName," +
                "sum(dtl_uv) as uv from cdm_log_ttd_viewspot_bench_traffic_from_di");
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendSightId(parameters, sql, sightId, needSubSight);
        appendDateRange(parameters, sql, dateType, startDate, endDate);
        sql.append(" group by channel");
        sql.append(" order by uv desc");
        List<Map<String, Object>> piechart = new ArrayList<>();
        try {
            piechart = tktStarRocksDao.getListResultNew(sql.toString(), parameters);//查询条数
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return piechart;
    }

    public Integer queryFlowSourceCount(Long sightId, String startDate, String endDate, Boolean needSubSight, Integer dateType, Integer businessType, List<Long> vendorIdList) {

        //总uv
        StringBuilder totalUvSql = new StringBuilder("select " +
                "cast(sum(dtl_uv) as Integer) as total_uv from cdm_log_ttd_viewspot_bench_traffic_from_di");
        List<PreparedParameterBean> uvparameters = new ArrayList<>();
        appendSightId(uvparameters, totalUvSql, sightId, needSubSight);
        appendDateRange(uvparameters, totalUvSql, dateType, startDate, endDate);
        List<Map<String, Object>> uv = new ArrayList<>();
        try {
            uv = tktStarRocksDao.getListResultNew(totalUvSql.toString(), uvparameters);//查询条数
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        Integer UvSum;
        if (uv.size() == 0) {
            UvSum = 0;
        } else {
            UvSum = (Integer) uv.get(0).get("total_uv");
        }
        return  UvSum;
    }

    public List<Map<String, Object>> queryFlowSourceListWithLastYear(Long sightId, String startDate, String endDate, Boolean needSubSight, Integer dateType, Integer businessType, List<Long> vendorIdList) {
        //去年uv统计
        StringBuilder lysql = new StringBuilder("select channel as channelName," +
                "sum(dtl_uv) as uv from cdm_log_ttd_viewspot_bench_traffic_from_di t1" +
                " inner join v_dim_date t2 on t1.d = t2.date_lastyear");
        List<PreparedParameterBean> lyparameters = new ArrayList<>();
        appendSightId(lyparameters, lysql, sightId, needSubSight);
        appendDateRangeOfLastYear(lyparameters, lysql, dateType, startDate, endDate);
        lysql.append(" group by channel");
        List<Map<String,Object>> lypiechart = new ArrayList<>();
        try {
            lypiechart = tktStarRocksDao.getListResultNew(lysql.toString(), lyparameters);//查询条数
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }

        return  lypiechart;
    }



    private void appendDateRangeOfLastYear(List<PreparedParameterBean> lyparameters, StringBuilder sql, Integer dateType, String startDate, String endDate) {
        sql.append(" and t2.date_solar between ? and ?");
        lyparameters.add(new PreparedParameterBean(startDate, Types.VARCHAR));
        lyparameters.add(new PreparedParameterBean(endDate, Types.VARCHAR));
    }

    private void appendSightId(List<PreparedParameterBean> parameters, StringBuilder sql, Long sightId, Boolean needSubSight){
        if(needSubSight){
            sql.append(" where viewspot_id = ?");
        }else {
            sql.append(" where sub_viewspot_id = ?");
        }
        parameters.add(new PreparedParameterBean(String.valueOf(sightId), Types.BIGINT));
    }

    private void appendDateRange(List<PreparedParameterBean> parameters, StringBuilder sql, Integer dateType, String startDate, String endDate){

        sql.append(" and d between ? and ?");

        parameters.add(new PreparedParameterBean(startDate, Types.VARCHAR));
        parameters.add(new PreparedParameterBean(endDate, Types.VARCHAR));

    }

    public List<Map<String, Object>> querySightRankList(List<Long> competitiveSightIdList, String startDate, String endDate, Integer businessType, List<Long> vendorIdList) {
        if (CollectionUtils.isEmpty(competitiveSightIdList)) {
            return new ArrayList<>();
        }

        List<Map<String, Object>> res = new ArrayList<>();
        //景点排名
        StringBuilder sql = new StringBuilder("select sub_viewspot_id ,sub_viewspot_name," +
                "cast(sum(dtl_uv) as Double) as dtl_uv from cdm_log_ttd_viewspot_bench_traffic_from_di");
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendSightIdOfCompetitiveSight(parameters, sql, competitiveSightIdList);
        appendDateRange(parameters, sql,1 ,startDate, endDate);
        sql.append(" and channel = '大搜'"); //NOSONAR
        sql.append(" group by sub_viewspot_id,sub_viewspot_name");
        sql.append(" order by dtl_uv desc");
        sql.append(" limit 10");
        List<Map<String,Object>> piechart = new ArrayList<>();
        try {
            piechart = tktStarRocksDao.getListResultNew(sql.toString(), parameters);//查询条数
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return piechart;
    }

    private void appendBusinessType(List<PreparedParameterBean> parameters, StringBuilder sql, Integer businessType){
        if(businessType != 1){
            sql.append(" and business_type = ?");
            parameters.add(new PreparedParameterBean(businessType==2?"门票":"活动", Types.VARCHAR));  //NOSONAR
        }

    }
    //拼供应商id列表
    private void appendVendorIdList(List<PreparedParameterBean> parameters, StringBuilder sql, List<Long> vendorIdList){
        if(CollectionUtils.isNotEmpty(vendorIdList)){
            sql.append(" and vendor_id in (");
            for(int i = 0; i < vendorIdList.size(); i++){
                if(i == 0){
                    sql.append("?");
                }else {
                    sql.append(",?");
                }
                parameters.add(new PreparedParameterBean(String.valueOf(vendorIdList.get(i)), Types.BIGINT));
            }
            sql.append(")");
        }
    }

    private void appendSightIdOfCompetitiveSight(List<PreparedParameterBean> parameters, StringBuilder sql, List<Long> competitiveSightIdList) {
        if(CollectionUtils.isNotEmpty(competitiveSightIdList)){
            sql.append(" where sub_viewspot_id in (");
            for(int i = 0; i < competitiveSightIdList.size(); i++){
                if(i == 0){
                    sql.append("?");
                }else {
                    sql.append(",?");
                }
                parameters.add(new PreparedParameterBean(String.valueOf(competitiveSightIdList.get(i)), Types.BIGINT));
            }
            sql.append(")");
        }else {
            sql.append(" where sub_viewspot_id = 0");
        }
    }

    public List<Map<String, Object>> queryUserAttention(List<Long> competitiveSightIdList, String startDate, String endDate, Integer businessType, List<Long> vendorIdList) {
        if (CollectionUtils.isEmpty(competitiveSightIdList)) {
            return new ArrayList<>();
        }
        List<Map<String, Object>> res = new ArrayList<>();
        //景点排名
        StringBuilder sql = new StringBuilder("select sub_viewspot_id,sub_viewspot_name," +
                "cast(sum(dtl_uv) as Double) as dtl_uv from cdm_log_ttd_viewspot_bench_traffic_from_di");
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendSightIdOfCompetitiveSight(parameters, sql, competitiveSightIdList);
        appendDateRange(parameters, sql,1 ,startDate, endDate);
        sql.append(" group by sub_viewspot_id,sub_viewspot_name");
        sql.append(" order by dtl_uv desc");
        sql.append(" limit 10");
        List<Map<String,Object>> UserAttention = new ArrayList<>();
        try {
            UserAttention = tktStarRocksDao.getListResultNew(sql.toString(), parameters);//查询条数
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        return UserAttention;
    }

    public Integer queryFlowSourceCountWithLastYear(Long sightId, String startDate, String endDate, Boolean needSubSight, Integer dateType, Integer businessType, List<Long> vendorIdList) {
        //去年总uv
        StringBuilder totalUvSql = new StringBuilder("select " +
                "cast(sum(dtl_uv) as Integer) as total_uv from cdm_log_ttd_viewspot_bench_traffic_from_di t1 inner join v_dim_date t2 on t1.d = t2.date_lastyear");
        List<PreparedParameterBean> uvparameters = new ArrayList<>();
        appendSightId(uvparameters, totalUvSql, sightId, needSubSight);
        appendDateRangeOfLastYear(uvparameters, totalUvSql, dateType, startDate, endDate);
        List<Map<String, Object>> uv = new ArrayList<>();
        try {
            uv = tktStarRocksDao.getListResultNew(totalUvSql.toString(), uvparameters);//查询条数
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        Integer UvSum;
        if (uv.size() == 0) {
            UvSum = 0;
        } else {
            UvSum = (Integer) uv.get(0).get("total_uv");
        }
        return  UvSum;
    }

    //流量来源分布 数仓侧设计文档：http://conf.ctripcorp.com/pages/viewpage.action?pageId=3682998994
    //dw_ticketdb.cdm_log_ttd_viewspot_bench_traffic_from_di


}
