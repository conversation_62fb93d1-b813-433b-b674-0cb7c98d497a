package com.ctrip.tour.business.dashboard.grpBusiness.metrics.multiplePrice;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import com.ctrip.platform.dal.dao.helper.DalColumnMapRowMapper;
import com.ctrip.tour.business.dashboard.grpBusiness.annotation.IndexAssemblyHandler;
import com.ctrip.tour.business.dashboard.grpBusiness.dao.starrocks.AdmPrdGrpMultiplePriceWorkPlatformDfDao;
import com.ctrip.tour.business.dashboard.grpBusiness.enums.MetricCategoryEnum;
import com.ctrip.tour.business.dashboard.grpBusiness.metrics.IndexCommonQueryAbstractSerice;
import com.ctrip.tour.business.dashboard.utils.SqlBuilder;
import com.google.common.base.Joiner;

import lombok.extern.slf4j.Slf4j;


@Service
@IndexAssemblyHandler(calcDateName = "view_date",
        calcExpression = "sum_pre1d + sum_umcomp",calcFieldName = "sum_pre1d," +
        "sum_umcomp",
        tableName = "adm_prd_grp_avg_multiple_price_work_platform_df")
@Slf4j
public class GrpMpWeightedAnomalyRateService extends IndexCommonQueryAbstractSerice {

    @Autowired
    AdmPrdGrpMultiplePriceWorkPlatformDfDao multiplePriceDfDao;

    public GrpMpWeightedAnomalyRateService(ApplicationContext ac) {
        super(ac);
    }

    @Override
    protected String otherCon(Map<String, ?> param, List<String> groupByCols, String timeAggType) {
        return doOtherCon(param, MetricCategoryEnum.MULTIPLE_PRICE_CATEGORY.getEnglishName());
    }

    @Override
    protected SqlBuilder selectColsAssembly(SqlBuilder sqlBuilder, Map<String, ?> param, List<String> groupByCols, String timeAggType) {

        String sql = " (sum(pre1d_more1_pv) / sum(pre1d_total_pv)) *0.8 as sum_pre1d, " +
                "(sum(pre1d_uncomp_pv) / sum(pre1d_total_pv)) *0.2 as sum_umcomp";

        if (CollectionUtils.isNotEmpty(groupByCols)) {
            sql = String.join(",", sql, Joiner.on(",").skipNulls().join(groupByCols));
        }

        sqlBuilder.select(sql);

        return sqlBuilder;
    }

    @Override
    protected List<Map<String, Object>> queryData(String sql, Map<String, ?> param, List<String> groupByCols, String timeAggType) {
        List<Map<String, Object>> queryData = null;
        try {
            DalColumnMapRowMapper dalColumnMapRowMapper = new DalColumnMapRowMapper();
            queryData = multiplePriceDfDao.query(sql, param);
            return queryData;
        } catch (SQLException e) {
            log.warn("query adm_prd_grp_multiple_price_work_platform_df error", e);

        }
        return null;
    }

    @Override
    protected List<Map<String, Object>> handleResults(IndexCommonQueryAbstractSerice serice, Map<String, ?> param, String timeAggType, boolean needTimeAgg, List<Map<String, Object>> rowData, List<String> groupCols) {
        return doHandleResults(serice, param, timeAggType, needTimeAgg, rowData);
    }

}