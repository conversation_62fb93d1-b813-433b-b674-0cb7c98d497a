package com.ctrip.tour.business.dashboard.tktBusiness.select43556;

import com.ctrip.platform.dal.dao.StatementParameters;
import com.ctrip.soa._27181.PreparedParameterBean;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;


@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode(exclude = {"parameters", "jdbcParameters"})
public class GenerateSqlBean {
    String dbName;
    String engine;
    String sql;
    Boolean needCount = false;
    StatementParameters parameters;
    List<PreparedParameterBean> preparedParameters;
}