include 'BaijiCommonTypes.bjsc'
namespace java 'com.ctrip.soa._24922'

// 自服务质量推送 请求体
class PushSelfQualityOfServiceToBusinessRequestType{
    // 推送类型，分两种：预警 | 关闭。1: 预警；2：关闭
    int pushType;
    // 120s回复率的预警线
    double ttdTimelyResponseRateLine;
    // 服务差评率预警线
    double ttdNegativeReviewsRateLine;
    // 当前日期，颗粒度到天，格式为YYYY-MM-DD；当pushType=2时提供
    string currentDate;
    // 关闭周期，单位是天; 当pushType=2时提供
    int closeDays;
    // 考核开始时间; 考核月份从这里取
    string startDate;
    // 供应商id列表
    list<long> vendorList;
}


// 自服务质量推送 响应体
class PushSelfQualityOfServiceToBusinessResponseType{
    BaijiCommonTypes.ResponseStatusType responseStatus;
}





@serviceName='BIBusinessDashboardService'
@serviceNamespace='http://soa.ctrip.com/_24922'
service BIBusinessDashboardService {
       /* 自服务质量看板一期-通知触达业务 */
    PushSelfQualityOfServiceToBusinessResponseType pushSelfQualityOfServiceToBusiness(PushSelfQualityOfServiceToBusinessRequestType request);
}