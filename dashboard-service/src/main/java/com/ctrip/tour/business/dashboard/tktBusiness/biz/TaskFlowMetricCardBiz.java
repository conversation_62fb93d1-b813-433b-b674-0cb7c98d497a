package com.ctrip.tour.business.dashboard.tktBusiness.biz;

import com.ctrip.soa._24922.DownloadTaskFlowDataResponseType;
import com.ctrip.soa._24922.GetTaskFlowMetricCardDataRequestType;
import com.ctrip.soa._24922.GetTaskFlowMetricCardDataResponseType;
import org.springframework.scheduling.annotation.Async;

import java.io.UnsupportedEncodingException;
import java.sql.SQLException;
import java.text.ParseException;
import java.util.concurrent.Future;

public interface TaskFlowMetricCardBiz {

    //由于获取表格数据中需要用多线程调用指标卡数据  因此拆开
    @Async("taskFlowMetricCardExecutor")
    Future<GetTaskFlowMetricCardDataResponseType> getTaskFlowMetricCardData(GetTaskFlowMetricCardDataRequestType getTaskFlowMetricCardDataRequestType, String empCode, Boolean needLastPeriod) throws Exception;

    @Async("taskFlowMetricCardExecutor")
    Future<GetTaskFlowMetricCardDataResponseType> getTaskFlowMetricCardData(GetTaskFlowMetricCardDataRequestType getTaskFlowMetricCardDataRequestType, String empCode, Boolean needLastPeriod, String d) throws Exception;

    DownloadTaskFlowDataResponseType downloadTaskFlowData(GetTaskFlowMetricCardDataRequestType requestType, String empCode) throws Exception;

}
