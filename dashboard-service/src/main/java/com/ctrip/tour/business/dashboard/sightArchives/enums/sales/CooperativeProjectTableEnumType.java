package com.ctrip.tour.business.dashboard.sightArchives.enums.sales;

import java.util.Arrays;
import java.util.List;

public enum CooperativeProjectTableEnumType {

    ZONG_DAI(1,"zd", "总代"), //NOSONAR
    BAO_PIAO(2,"bp", "包票"), //NOSONAR
    DI_TUI(3,"dt", "地推"), //NOSONAR
    PIAO_JI(4,"pj", "票机"), //NOSONAR
    YOU_XUAN(5,"yx", "优选"); //NOSONAR

    private final int id;
    private final String englishName;
    private final String chineseName;

    CooperativeProjectTableEnumType(int id, String englishName, String chineseName) {
        this.id = id;
        this.englishName = englishName;
        this.chineseName = chineseName;
    }

    public static List<CooperativeProjectTableEnumType> ProjectEnumList = Arrays.asList(ZONG_DAI, BAO_PIAO, DI_TUI, PIAO_JI, YOU_XUAN);

}
