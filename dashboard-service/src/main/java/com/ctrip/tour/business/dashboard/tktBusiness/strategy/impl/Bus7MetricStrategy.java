package com.ctrip.tour.business.dashboard.tktBusiness.strategy.impl;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.MetricCalStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @date 2022/8/2
 */
@Component
public class Bus7MetricStrategy implements MetricCalStrategy {

    @Autowired
    private Bus567MetricStrategy bus567MetricStrategy;

    @Override
    public Future<MetricDetailInfo> getSingleMetricCardData(String domainName,
                                                            TimeFilter timeFilter,
                                                            MetricInfoBean metricInfoBean,
                                                            String d,
                                                            Boolean needRank) throws Exception {
        return new AsyncResult<>(bus567MetricStrategy.getSingleMetricCardData(domainName, timeFilter, metricInfoBean, d, needRank));
    }

    @Override
    public GetTrendLineDataResponseType getSingleTrendlineData(GetTrendLineDataRequestType request,
                                                               MetricInfoBean metricInfoBean,
                                                               String d) throws Exception {
        return bus567MetricStrategy.getSingleTrendlineData(request, metricInfoBean, d, getMetricName());
    }

    @Override
    public GetTableDataResponseType getSingleTableData(GetTableDataRequestType request, MetricInfoBean metricInfoBean, String d) throws Exception {
        return bus567MetricStrategy.getSingleTableData(request, metricInfoBean, d);
    }

    @Override
    public GetDrillDownBaseInfoResponseType getSingleDrillDownBaseInfo(GetDrillDownBaseInfoRequestType request, MetricInfoBean metricInfoBean, String d) throws Exception {
        return bus567MetricStrategy.getSingleDrillDownBaseInfo(request, metricInfoBean, d);
    }

    @Override
    public String getMetricName() {
        return "7";
    }
}
