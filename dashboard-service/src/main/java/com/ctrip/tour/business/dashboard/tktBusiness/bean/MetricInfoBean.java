package com.ctrip.tour.business.dashboard.tktBusiness.bean;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/28
 */
@Getter
@Setter
public class MetricInfoBean {
    //业务线id，0全部，1门票，2玩乐，3活动，4日游
    private Integer businessType;
    //所属季度
    private String quarter;
    //指标
    private String metric;
    //门票考核层级
    private String level;
    //门票考核层级对应景点的配置
    private List<String> bdList;
    //门票考核层级对应非景点的配置
    private List<String> regionList;
    //是否需要查看北京环影数据
    private String needUniversalStudios;
    //活动考核层级(仅门票活动)
    private String actLevel;
    //活动考核层级对应景点的配置(仅门票活动)
    private List<String> actBdList;
    //活动考核层级对应非景点的配置(仅门票活动)
    private List<String> actRegionList;
    //国内日游考核层级
    private String odtLevel;
    //出境日游考核层级对应的大区省份配置
    private List<String> odtRegionList;
    //出境日游考核层级
    private String overseaOdtLevel;
    //出境日游考核范围
    private List<String> overseaOdtRegionList;
    //考核类型
    private Integer examineType;
    //角色
    private Integer role;

}
