package com.ctrip.tour.business.dashboard.sightArchives.service;

import com.ctrip.soa._24922.*;

public interface CommonService {
    //景点档案tab可见权限
    CheckSightArchivesPermissionResponseType checkSightArchivesPermission(CheckSightArchivesPermissionRequestType checkSightArchivesPermissionRequestType);

    //获取数据更新时间
    GetSightArchivesUpdateTimeResponseType getSightArchivesUpdateTime(GetSightArchivesUpdateTimeRequestType getSightArchivesUpdateTimeRequestType);

    //景点概况 - 景点id/名称搜索
    SearchSightListResponseType searchSightList(SearchSightListRequestType searchSightListRequestType);

    //景点概况-景点信息
    GetSightInfoResponseType getSightInfo(GetSightInfoRequestType getSightInfoRequestType);

    //景点概况 - 年入园量编辑
    UpdateAnnualIntakeResponseType updateAnnualIntake(UpdateAnnualIntakeRequestType updateAnnualIntakeRequestType);

    //筛选项 - 供应商id/名称搜索
    SearchVendorListResponseType searchVendorList(SearchVendorListRequestType searchVendorListRequestType);

    //报告总结 - 雷达图数据&文案内数据
    GetSightArchivesReportSummaryResponseType getSightArchivesReportSummary(GetSightArchivesReportSummaryRequestType getSightArchivesReportSummaryRequestType);

    //ai模块小结
    GetAiModuleSummaryResponseType getAiModuleSummary(GetAiModuleSummaryRequestType requestType);

    String getQueryD();

}
