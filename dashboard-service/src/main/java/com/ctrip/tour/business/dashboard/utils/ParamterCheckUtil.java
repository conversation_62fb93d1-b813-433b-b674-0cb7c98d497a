package com.ctrip.tour.business.dashboard.utils;

import com.ctrip.soa._24922.DrillDownFilter;
import com.ctrip.soa._24922.TimeFilter;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/8
 */
public class ParamterCheckUtil {

    private static List<String> dateTypeList = Lists.newArrayList("quarter", "month", "half");
    private static List<String> dateTypeListV2 = Lists.newArrayList("quarter", "month", "half", "year");
    private static List<String> monthList = Lists.newArrayList("01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12");
    private static List<String> quarterList = Lists.newArrayList("Q1", "Q2", "Q3", "Q4");
    private static List<String> halfList = Lists.newArrayList("H1", "H2");
    private static List<String> queryTypeList = Lists.newArrayList("trendline", "drilldown");
    private static List<String> metricList = Lists.newArrayList("1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12");
    private static List<String> fieldList = Lists.newArrayList("大区", "省份", "商拓", "POI");

    public static Boolean checkDomainName(String domainName){
        return !GeneralUtil.isEmpty(domainName);
    }

    public static boolean checkTimeFilterWithoutTimeFrame(TimeFilter timeFilter){
        if(GeneralUtil.isEmpty(timeFilter)){
            return false;
        }
        String dateType = timeFilter.getDateType();
        String year = timeFilter.getYear();
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();
        String half = timeFilter.getHalf();
        if(GeneralUtil.isEmpty(dateType) || !dateTypeList.contains(dateType)){
            return false;
        }
        if("half".equals(dateType)){
            return !GeneralUtil.isEmpty(half) && halfList.contains(half);
        }
        if("quarter".equals(dateType)){
            return !GeneralUtil.isEmpty(quarter) && quarterList.contains(quarter);
        }
        if("month".equals(dateType)){
            return !GeneralUtil.isEmpty(month) && monthList.contains(month);
        }
        if(GeneralUtil.isEmpty(year)){
            return false;
        }
        return true;
    }

    public static Boolean checkTimeFilter(TimeFilter timeFilter){
        if(!checkTimeFilterWithoutTimeFrame(timeFilter)){
            return false;
        }
        Integer timeFrame = timeFilter.getTimeFrame();
        return !GeneralUtil.isEmpty(timeFrame);
    }

    public static Boolean checkQueryType(String queryType) {
        return !GeneralUtil.isEmpty(queryType) && queryTypeList.contains(queryType);
    }

    public static Boolean checkMetric(String metric) {
        return !GeneralUtil.isEmpty(metric) && metricList.contains(metric);
    }

    public static Boolean checkNeedSearch(Boolean needSearch){
        return !GeneralUtil.isEmpty(needSearch);
    }

    public static Boolean checkField(String field){
        return !GeneralUtil.isEmpty(field) && fieldList.contains(field);
    }

    public static Boolean checkDrillDownFilter(DrillDownFilter drillDownFilter){
        if(GeneralUtil.isEmpty(drillDownFilter)){
            return false;
        }
        String field = drillDownFilter.getField();
        return checkField(field);
    }

    public static boolean checkTimeFilterSqlInjection(TimeFilter timeFilter){
        if(timeFilter == null){
            return false;
        }

        String year = timeFilter.getYear();
        String dateType = timeFilter.getDateType();
        String quarter = timeFilter.getQuarter();
        String month = timeFilter.getMonth();
        String half = timeFilter.getHalf();

        //dateType必须是quarter, month, half中的一个
        if(GeneralUtil.isEmpty(dateType) || !dateTypeList.contains(dateType)){
            return false;
        }

        //year必须是数字
        if(GeneralUtil.isNotEmpty(year) && !year.matches("[0-9]+")){
            return false;
        }
        //half必须是H1, H2中的一个
        if("half".equals(dateType)){
            return !GeneralUtil.isEmpty(half) && halfList.contains(half);
        }
        //quarter必须是Q1, Q2, Q3, Q4中的一个
        if("quarter".equals(dateType)){
            return !GeneralUtil.isEmpty(quarter) && quarterList.contains(quarter);
        }
        //month必须是01, 02, 03, 04, 05, 06, 07, 08, 09, 10, 11, 12中的一个
        if("month".equals(dateType)){
            return !GeneralUtil.isEmpty(month) && monthList.contains(month);
        }
        return false;
    }

    public static boolean checkTimeFilterSqlInjectionV2(TimeFilter timeFilter){
        if(timeFilter == null){
            return false;
        }

        String year = timeFilter.getYear();
        String dateType = timeFilter.getDateType();
        String quarter = timeFilter.getQuarter();
        String month = timeFilter.getMonth();
        String half = timeFilter.getHalf();

        //dateType必须是quarter, month, half中的一个
        if(GeneralUtil.isEmpty(dateType) || !dateTypeListV2.contains(dateType)){
            return false;
        }

        //year必须是数字
        if(GeneralUtil.isNotEmpty(year) && !year.matches("[0-9]+")){
            return false;
        }
        //half必须是H1, H2中的一个
        if("half".equals(dateType)){
            return !GeneralUtil.isEmpty(half) && halfList.contains(half);
        }
        //quarter必须是Q1, Q2, Q3, Q4中的一个
        if("quarter".equals(dateType)){
            return !GeneralUtil.isEmpty(quarter) && quarterList.contains(quarter);
        }
        //month必须是01, 02, 03, 04, 05, 06, 07, 08, 09, 10, 11, 12中的一个
        if("month".equals(dateType)){
            return !GeneralUtil.isEmpty(month) && monthList.contains(month);
        }

        if ("year".equals(dateType)) {
            return !GeneralUtil.isEmpty(year) && year.matches("[0-9]+");
        }
        return false;
    }



    public static boolean checkDomainNameSqlInjection(String domainName){
        return !StringUtils.isBlank(domainName) && !domainName.matches(".*\\s+.*");
    }

}
