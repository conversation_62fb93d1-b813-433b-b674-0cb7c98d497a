package com.ctrip.tour.business.dashboard.tktBusiness.strategy.domestic.impl;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.grpBusiness.enums.DomesticMetricEnum;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.domestic.DomesticMetricCalStrategy;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.domestic.specificlogic.Bus1And2CommonStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.Future;

@Component
public class Bus2DomesticMetricStrategy implements DomesticMetricCalStrategy {

    @Autowired
    private Bus1And2CommonStrategy bus1And2CommonStrategy;

    @Override
    public Future<DomesticMetricDetailInfo> getSingleMetricCardData(String domainName, TimeFilter timeFilter, List<MetricInfoBean> metricInfo<PERSON>eanList, String d, <PERSON><PERSON><PERSON> isFirst, Integer businessId) throws Exception {
        return new AsyncResult<>(
                bus1And2CommonStrategy.getMetricCardDataWithDiffBusinessLine(domainName, timeFilter, metricInfoBeanList, d, DomesticMetricEnum.PROFIT.getId(), businessId, isFirst)
        );
    }

    @Override
    public GetDomesticMetricTrendDataResponseType getSingleTrendlineData(GetDomesticMetricTrendDataRequestType request, String d) throws Exception {
        return bus1And2CommonStrategy.getSingleTrendlineData(request, d, getMetricName());
    }

    @Override
    public GetDomesticTableDataResponseType getSingleTableData(GetDomesticTableDataRequestType request, MetricInfoBean metricInfoBean, String d) throws Exception {
        return bus1And2CommonStrategy.getSingleTableData(request, metricInfoBean, d);
    }

    @Override
    public GetDomesticDrillDownBaseInfoResponseType getSingleDrillDownBaseInfo(GetDomesticDrillDownBaseInfoRequestType request, MetricInfoBean metricInfoBean, String d) throws Exception {
        return bus1And2CommonStrategy.getSingleDrillDownBaseInfo(request,metricInfoBean,d);
    }
    @Override
    public GetFirstPageDomesticMetricCardDrillDataResponseType getFirstPageDomesticMetricCardDrillData(
            GetFirstPageDomesticMetricCardDrillDataRequestType request,MetricInfoBean metricInfoBean, String d) throws Exception{
        return bus1And2CommonStrategy.getFirstPageDomesticMetricCardDrillData(request,metricInfoBean,d);
    }
    @Override
    public Integer getMetricName() {
        return 2;
    }
}
