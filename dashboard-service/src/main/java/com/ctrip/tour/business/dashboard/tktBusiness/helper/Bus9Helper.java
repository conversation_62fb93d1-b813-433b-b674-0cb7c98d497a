package com.ctrip.tour.business.dashboard.tktBusiness.helper;


import com.ctrip.soa._24922.TableDataItem;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.ConfigImportException;
import com.google.common.collect.Lists;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/7/29
 */
public class Bus9Helper {



    public static List<String> getTableDimList(){
        return Lists.newArrayList("cover_scenic_cnt","open_scenic_cnt",
                "single_category_complete_rate","treasuremap_scenic_cnt");
    }

//    public static List<String> getDimList(){
//        return Lists.newArrayList("ttd_sale1_stk_scenic_cnt","ttd_trgt_category");
//    }
//
//    public static List<String> getDimListNew(){
//        return Lists.newArrayList("multi_category_complete_rate");
//    }


//    public static void makeUpMetricCardData(Map<String, Double> dimMap) {
//        String extraDim = "ttd_sale1_stk_scenic_cnt|ttd_trgt_category|/";
//        Double fenzi = dimMap.get("ttd_sale1_stk_scenic_cnt");
//        Double fenmu = dimMap.get("ttd_trgt_category");
//        if (GeneralUtil.isValidDivide(fenzi, fenmu)) {
//            dimMap.put(extraDim, fenzi / fenmu);
//        }
//    }

//    public static void makeUpTableData(List<TableDataItem> tableDataItemList) {
//        for (TableDataItem item : tableDataItemList) {
//            makeUpMetricCardData(item.getDimMap());
//        }
//    }

//    public static Map<String,String> getLineChartTrendlineType(){
//        Map<String,String> typeMap = new HashMap<>();
//        typeMap.put("ttd_sale1_stk_scenic_cnt","barChart");
//        typeMap.put("ttd_sale1_stk_scenic_cnt|ttd_trgt_category|/","lineChart");
//        return typeMap;
//    }

    public static Map<String,String> getLineChartTrendlineTypeNew(){
        Map<String,String> typeMap = new HashMap<>();
        typeMap.put("multi_category_complete_rate","lineChart");
        return typeMap;
    }

//    public static Map<String,String> getLineChartTrendlineTypeWithDrillDown(){
//        Map<String,String> typeMap = new HashMap<>();
//        typeMap.put("ttd_sale1_stk_scenic_cnt|ttd_trgt_category|/","lineChart");
//        return typeMap;
//    }


    public static Map<String,String> getStackLineChartTrendLineType(){
        Map<String,String> typeMap = new HashMap<>();
        typeMap.put("cover_scenic_cnt","stackLineChart");
        return typeMap;
    }


    public static List<String> getPagingCondition(List<List<Object>> rawResultList,
                                                  Integer pageNo,
                                                  Integer pageSize){
        List<String> fieldList = new ArrayList<>();
        for (List<Object> rowResult : rawResultList) {
            fieldList.add(String.valueOf(rowResult.get(0)));
        }
        int startIndex = (pageNo - 1) * pageSize;
        int endIndex = pageNo * pageSize;
        int length = fieldList.size();

        if(startIndex >= length){
            return new ArrayList<>();
        }
        //处理最后一页index溢出
        if (endIndex > length) {
            endIndex = length;
        }


        return fieldList.subList(startIndex, endIndex);
    }

    //根据考核层级确定可下钻维度
    public static List<String> getFieldList(String level){
        switch (level){
            case "国内":
            case "三方":
                return Lists.newArrayList("region_name","province_name");
            case "大区":
            case "省份":
                return Lists.newArrayList("province_name");
            default:
                throw new ConfigImportException("品类覆盖配置中导入了错误的层级:"+level);
        }
    }

    //临时补齐缺失品类的数据
    public static List<TableDataItem> getNewTableDataItemList(List<String> categoryEnumList,
                                                              String field,
                                                              List<TableDataItem> tableDataItemList) {
        List<TableDataItem> newTableDataItemList = new ArrayList<>();

        List<String> fieldList = tableDataItemList
                .stream()
                .map(i -> i.getFieldMap().get(field))
                .distinct()
                .collect(Collectors.toList());
        Map<String, String> provinceRegionMap = new HashMap<>();
        if ("province_name".equals(field)) {
            tableDataItemList.forEach(i -> {
                Map<String, String> fieldMap = i.getFieldMap();
                provinceRegionMap.put(fieldMap.get("province_name"), fieldMap.get("region_name"));
            });
        }

        Map<String, TableDataItem> tableDataItemMap = new HashMap<>();
        tableDataItemList.forEach(i -> {
            Map<String, String> fieldMap = i.getFieldMap();
            String key = fieldMap.get("viewspot_type_name") + ":" + fieldMap.get(field);
            tableDataItemMap.put(key, i);
        });
        for (String fieldItem : fieldList) {
            for (String categoryEnum : categoryEnumList) {
                String key = categoryEnum + ":" + fieldItem;
                if (tableDataItemMap.containsKey(key)) {
                    newTableDataItemList.add(tableDataItemMap.get(key));
                } else {
                    TableDataItem tableDataItem = new TableDataItem();
                    Map<String, String> fieldMap = new HashMap<>();
                    fieldMap.put(field, fieldItem);
                    fieldMap.put("viewspot_type_name", categoryEnum);
                    if ("province_name".equals(field)) {
                        fieldMap.put("region_name", provinceRegionMap.get(fieldItem));
                    }
                    tableDataItem.setFieldMap(fieldMap);
                    tableDataItem.setDimMap(new HashMap<>());
                    newTableDataItemList.add(tableDataItem);
                }
            }
        }
        return newTableDataItemList;
    }


    //获取表头信息
    public static List<String> getTableList(){
        return Arrays.asList("categoryExamineType","categoryCrashExperienceNum","categoryOperatingExperienceNum","categoryCoveredExperienceNum","categoryUncoveredExperienceNum","categoryCurrentCoverageRate","categoryTargetCoverageRate","categoryWeight","categoryCompleteRate");
    }

}
