package com.ctrip.tour.business.dashboard.soa;

import com.ctriposs.baiji.rpc.server.HttpRequestWrapper;
import com.ctriposs.baiji.rpc.server.HttpResponseWrapper;
import com.ctriposs.baiji.rpc.server.ServiceHost;
import com.ctriposs.baiji.rpc.server.filter.ResponseFilter;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/7/20
 */
@Component
public class SoaResponseFilter implements ResponseFilter {
    @Override
    public void apply(ServiceHost host, HttpRequestWrapper request, HttpResponseWrapper response, Object responseObj) {
        MDC.clear();
    }
}
