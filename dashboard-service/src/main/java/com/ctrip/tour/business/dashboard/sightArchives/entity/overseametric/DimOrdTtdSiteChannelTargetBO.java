package com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;

@Entity
@Getter
@Setter
public class DimOrdTtdSiteChannelTargetBO {
    //业务线
    private String buType;
    //考核年
    private String examineYear;
    //考核季
    private String examineQuarter;
    //考核类型站点/渠道
    private String examineType;
    //考核类型值HK/TRIP增长
    private String examineTypeValue;
    //考核指标类型
    private String examineMetricType;
    //考核目标
    private Double examineMetric;
    private String q1;
    private String q2;
    private String q3;
    private String q4;
}
