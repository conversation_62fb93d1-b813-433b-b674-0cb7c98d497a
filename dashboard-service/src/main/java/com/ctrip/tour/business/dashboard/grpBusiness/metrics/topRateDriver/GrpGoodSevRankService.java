package com.ctrip.tour.business.dashboard.grpBusiness.metrics.topRateDriver;

import java.sql.SQLException;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAccessor;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.IntStream;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ctrip.tour.business.dashboard.grpBusiness.dao.starrocks.StarRocksCommonDao;
import com.google.common.collect.Maps;

/**
 * <AUTHOR>
 * @Date 2025/4/9
 */
@Service
public class GrpGoodSevRankService {

    @Autowired
    private StarRocksCommonDao starRocksCommonDao;


    public Long[] getPmRank(String endDate, String empCode) throws SQLException {

        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate date = LocalDate.parse(endDate, dtf);

        LocalDate startOfWeek = date.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        LocalDate endOfWeek = date.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));

        String pdSql = "SELECT max(partition_d) as maxD\n" +
                "FROM adm_ord_grp_work_platform_prdt_df\n" +
                "WHERE \n" +
                "    firstday_of_week = '"+dtf.format(startOfWeek)+"'\n" +
                "    AND lastday_of_week = '"+dtf.format(endOfWeek)+"' and pm_eid='"+ empCode+"'";

        List<Map<String, Object>> pdRst = starRocksCommonDao.query(pdSql, Maps.newHashMap());
        if (CollectionUtils.isEmpty(pdRst)) {
            return null;
        }

        Date maxD = (Date) pdRst.get(0).get("maxD");


        String sql = "SELECT distinct good_sev_rank as rank\n" +
                "FROM adm_ord_grp_work_platform_prdt_df\n" +
                "WHERE partition_d = '"+maxD+"'\n" +
                "    AND firstday_of_week = '"+dtf.format(startOfWeek)+"'\n" +
                "    AND lastday_of_week = '"+dtf.format(endOfWeek)+"' and pm_eid='"+ empCode+"'";


        List<Map<String, Object>> rankQeury = starRocksCommonDao.query(sql, Maps.newHashMap());

        if (CollectionUtils.isEmpty(rankQeury)) {
            return null;
        }

        String maxSql = "SELECT max(good_sev_rank) as maxRank\n" +
                "FROM adm_ord_grp_work_platform_prdt_df\n" +
                "WHERE partition_d = '"+maxD+"'\n" +
                "    AND firstday_of_week = '"+dtf.format(startOfWeek)+"'\n" +
                "    AND lastday_of_week = '"+dtf.format(endOfWeek)+"'";


        List<Map<String, Object>> query = starRocksCommonDao.query(maxSql, Maps.newHashMap());
        if (CollectionUtils.isEmpty(query)) {
            return new Long[]{(Long)rankQeury.get(0).get("rank"), null};
        }


        return new Long[]{(Long)rankQeury.get(0).get("rank"), (Long)query.get(0).get("maxRank")};
    }
}
