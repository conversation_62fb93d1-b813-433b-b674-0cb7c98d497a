package com.ctrip.tour.business.dashboard.grpBusiness.job;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ctrip.tour.business.dashboard.grpBusiness.dao.starrocks.StarRocksCommonDao;
import com.ctrip.tour.business.dashboard.grpBusiness.service.HrOrgEmpInfoService;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.group.workbenchsvc.contract.StructuredTableInfoType;
import com.ctrip.tour.group.workbenchsvc.contract.StructuredTableRowInfoType;
import com.ctrip.tour.rights.client.GroupWorkbenchServiceClient;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2025/3/25
 */
@Service
@Slf4j
public class MultiPriceNewAlertNotifyService extends CommonAlertNotifyService {

    private static final String CREATOR_EID = "CREATOR_EID";
    private static final String EVENT_TYPE = "CREATOR_EID";
    private static final String EVENT_TYPE_XXX_STRUCTURED_TABLE = "CREATOR_EID";
    private static final String TASK_MULT_PRICE_NEW_ALERT = "TASK_PRICE_MULTIPAL_EXCEPTION_NOTIFY";
    private static final String EVENT_MULT_PRICE_NEW_ALERT = "EVENT_PRICE_MULTIPAL_EXCEPTION_NOTIFY";
    private static final String EVENT_MULT_PRICE_NEW_ALERT_STRUCTURED_TABLE = "EVENT_PRICE_MULTIPAL_EXCEPTION_NOTIFY_STRUCTURED_TABLE";

    @Autowired
    private StarRocksCommonDao starRocksCommonDao;

    private static String NOTIFY_EVENT_EMPCODES = "notify.event.empcodes";

    private GroupWorkbenchServiceClient client = GroupWorkbenchServiceClient.getInstance();

    private static final String REDIRECT_URL = "http://vendor.package.ctripcorp.com/product/input/priceCompetitiveness?from=6381&productid=";

    @Autowired
    private RemoteConfig remoteConfig;

    private String buildHref(String url, Long val) {
        return "<a href=\""+url+"\">"+val+"</a>";
    }

    public void handleMultipriceNotify(String condition, String exceptionType, String notifyContent, String buType) throws Exception {

        String whiteEmpCodesStr = remoteConfig.getExternalConfig(NOTIFY_EVENT_EMPCODES);

        List<String> whiteEmpCodes = Lists.newArrayList();
        if (StringUtils.isNotBlank(whiteEmpCodesStr)) {
            whiteEmpCodes = Splitter.on(",").splitToList(whiteEmpCodesStr);
        }

        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        String sql = "SELECT " +
                "(avg_multiple_price * COALESCE((pre1d_total_pv - pre1d_uncomp_pv), 0)) / " +
                "NULLIF(COALESCE((pre1d_total_pv - pre1d_uncomp_pv), 0), 0) AS weighted_multiple_price, " +
                "productid,dest_name,vendor_id,vendor_name,pre1d_total_pv,is_self,tor_last30d_resv_suc_ord_cnt,pm_eid " +
                "FROM adm_prd_grp_avg_multiple_price_work_platform_df\n" +
                "WHERE partition_d = '" + LocalDate.now().format(dtf) + "'\n" +
                "    AND view_date = '" + LocalDate.now().minusDays(1).format(dtf) + "' AND sub_bu_type = '"+buType+"'" +
                "    AND (avg_multiple_price * COALESCE((pre1d_total_pv - pre1d_uncomp_pv), 0)) / " +
                "NULLIF(COALESCE((pre1d_total_pv - pre1d_uncomp_pv), 0), 0 ) "+ condition;//NOSONAR

        List<Map<String, Object>> resultList = starRocksCommonDao.query(sql, Maps.newHashMap());
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }

        List<String> finalWhiteEmpCodes = whiteEmpCodes;
        Map<String, List<Map<String, Object>>> emNotifyInfoMap = resultList.stream()
                .filter(pw -> CollectionUtils.isEmpty(finalWhiteEmpCodes) || (Objects.nonNull(pw) && finalWhiteEmpCodes.contains((String) pw.get("pm_eid"))))
                .collect(Collectors.groupingBy(pw -> (String) pw.get("pm_eid")));

        emNotifyInfoMap.forEach((pmEid, pwList) -> {

            List<StructuredTableRowInfoType> rowInfoTypes = pwList.stream().map(pw -> {
                StructuredTableRowInfoType rowInfoType = new StructuredTableRowInfoType();

                Long productid = (Long) pw.get("productid");
                String destProvinceName = (String) pw.get("dest_name");
                Long vendorId = (Long) pw.get("vendor_id");
                String vendorName = (String) pw.get("vendor_name");
                Long pre1dTotalPv = (Long) pw.get("pre1d_total_pv");
                Integer isSelf = (Integer) pw.get("is_self");
                Integer lst30dOrdCnt = (Integer) pw.get("tor_last30d_resv_suc_ord_cnt");
                Double weightedMultiplePrice = (Double) pw.get("weighted_multiple_price");

                List<String> colList = Lists.newArrayList(buildHref(REDIRECT_URL + productid, productid),BigDecimal.valueOf(weightedMultiplePrice).setScale(2, RoundingMode.HALF_UP).toPlainString(),
                        vendorId.toString(), vendorName, destProvinceName,  pre1dTotalPv.toString(),Objects.equals(1, isSelf)?"自营":"非自营",lst30dOrdCnt.toString(),exceptionType);//NOSONAR
                rowInfoType.setColList(colList);
                return rowInfoType;
            }).collect(Collectors.toList());
            StructuredTableInfoType structuredTableInfoType = new StructuredTableInfoType();
            structuredTableInfoType.setRowList(rowInfoTypes);
            structuredTableInfoType.setHeaderList(Lists.newArrayList("产品id","价格倍数数值","供应商id","供应商名称","目的地省份/国家", "当日日历点击PV","是否自营","近 30天订单量","异常原因"));//NOSONAR
            List<String> tpInfos = Lists.newArrayList(notifyContent);//NOSONAR
            notifyEmp(pmEid, structuredTableInfoType, "价格倍数异常通知",//NOSONAR
                    TASK_MULT_PRICE_NEW_ALERT, EVENT_MULT_PRICE_NEW_ALERT, EVENT_MULT_PRICE_NEW_ALERT_STRUCTURED_TABLE, notifyContent, tpInfos, "1");//NOSONAR
        });
    }

}
