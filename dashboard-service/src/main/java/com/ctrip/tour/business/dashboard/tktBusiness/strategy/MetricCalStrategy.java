package com.ctrip.tour.business.dashboard.tktBusiness.strategy;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean;
import org.springframework.scheduling.annotation.Async;

import java.util.concurrent.Future;

/**
 * 指标计算策略
 *
 * <AUTHOR>
 * @date 2022/7/29
 */
public interface MetricCalStrategy {

    //获取单个指标指标卡数据
    @Async("metricCardExecutor")
    Future<MetricDetailInfo> getSingleMetricCardData(String domainName,
                                                     TimeFilter timeFilter,
                                                     MetricInfoBean metricInfoBean,
                                                     String d,
                                                     Boolean needRank) throws Exception;


    //获取单个指标的趋势线数据
    GetTrendLineDataResponseType getSingleTrendlineData(GetTrendLineDataRequestType request,
                                                        MetricInfoBean metricInfoBean,
                                                        String d) throws Exception;

    //获取单个指标表格数据
    GetTableDataResponseType getSingleTableData(GetTableDataRequestType request,
                                                MetricInfoBean metricInfoBean,
                                                String d) throws Exception;


    //获取下钻相关基础信息
    GetDrillDownBaseInfoResponseType getSingleDrillDownBaseInfo(GetDrillDownBaseInfoRequestType request,
                                                                MetricInfoBean metricInfoBean,
                                                                String d) throws Exception;



    //获取指标枚举值
    String getMetricName();


}
