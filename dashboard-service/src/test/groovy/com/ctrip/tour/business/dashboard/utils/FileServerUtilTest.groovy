package com.ctrip.tour.business.dashboard.utils

import spock.lang.Specification

class FileServerUtilTest extends Specification {
    def "token"(){
        expect: "call"
        FileServerUtil.token("ws.uploadfile.fx.fws.qa.nt.ctripcorp.com")
    }




    /*def "upload"(){
        given: "mock"
        def data = MapperUtil.str2List("[80,75,3,4,20,0,8,8,8,0,-19,-123,-66,88,0,0,0,0,0,0,0,0,0,0,0,0,17,0,0,0,100,111,99,80,114,111,112,115,47,99,111,114,101,46,120,109,108,-83,-111,-47,74,-61,48,20,-122,-17,-5,20,33,-9,-19,73,-37,-87,35,-76,29,-94,12,4,-59,-127,27,-118,119,33,61,-74,-59,54,9,73,-76,-13,-19,-51,-70,89,81,-68,-12,-14,-28,-1,-2,-113,-61,73,-79,-38,15,61,121,71,-21,58,-83,74,-102,38,-116,18,84,82,-41,-99,106,74,-70,-37,-82,-29,37,93,85,81,84,72,109,113,99,-75,65,-21,59,116,36,-76,-108,43,105,-21,-67,-31,0,78,-74,56,8,-105,-124,88,-123,-28,69,-37,65,-8,48,-38,6,-116,-112,-81,-94,65,-56,24,59,-121,1,-67,-88,-123,23,112,-80,-59,102,-42,-47,-93,-113,75,-13,-17,-54,90,-50,74,-13,102,-5,73,80,75,-64,30,7,84,-34,65,-102,-92,-16,-51,122,-76,-125,-5,-77,48,37,51,-71,119,-35,76,-115,-29,-104,-116,-7,-60,-123,-115,82,120,-70,-69,125,-104,-106,-113,59,-27,-68,80,18,105,21,17,82,-100,-20,92,90,20,30,107,18,28,-36,127,24,44,-23,87,-14,-104,95,93,111,-41,-76,-54,88,-74,-120,-39,89,-100,-77,45,91,-14,-59,5,-49,-14,-25,2,126,-11,79,-50,-29,-88,109,117,25,-50,-46,34,-39,-36,-33,28,-48,-7,57,42,-32,-25,-41,85,-47,39,80,75,7,8,52,-98,38,-125,5,1,0,0,-12,1,0,0,80,75,3,4,20,0,8,8,8,0,-19,-123,-66,88,0,0,0,0,0,0,0,0,0,0,0,0,11,0,0,0,95,114,101,108,115,47,46,114,101,108,115,-83,-110,-63,78,-61,48,12,-122,-17,125,-118,40,-9,-43,-35,-112,16,66,77,119,65,72,-69,77,104,60,64,72,-36,54,106,19,71,-119,7,-27,-19,9,7,4,67,12,118,-32,24,-25,-9,-25,79,-78,-37,-19,-30,103,-15,-116,41,59,10,74,-82,-21,70,10,12,-122,-84,11,-125,-110,-113,-121,-5,-43,-115,-36,118,85,-43,62,-32,-84,-71,100,-14,-24,98,22,-91,41,100,37,71,-26,120,11,-112,-51,-120,94,-25,-102,34,-122,-14,-45,83,-14,-102,-53,51,13,16,-75,-103,-12,-128,-80,105,-102,107,72,95,25,-78,-85,-124,56,-63,-118,-99,85,50,-19,-20,90,-118,-61,107,-60,75,-16,-44,-9,-50,-32,29,-103,-93,-57,-64,63,76,-7,-106,40,100,-99,6,100,37,-105,25,94,40,77,79,68,83,93,-96,18,-50,-22,108,-2,83,7,23,-58,96,-47,-82,98,42,-3,-119,29,-26,79,39,75,102,95,-54,25,116,-116,127,72,93,93,46,117,126,5,-32,-111,-75,-43,-84,-63,80,-62,-33,-107,-34,19,31,78,45,-100,92,67,87,-67,1,80,75,7,8,87,40,94,35,-29,0,0,0,70,2,0,0,80,75,3,4,20,0,8,8,8,0,-19,-123,-66,88,0,0,0,0,0,0,0,0,0,0,0,0,19,0,0,0,91,67,111,110,116,101,110,116,95,84,121,112,101,115,93,46,120,109,108,-83,-109,-53,78,-61,48,16,69,-9,-7,10,-53,91,20,-69,101,-127,16,74,-46,5,-113,37,84,-94,124,-128,-79,39,-115,85,-65,100,-69,-91,-3,123,38,41,-27,37,90,-118,-24,-54,-78,-26,-34,123,110,70,113,53,89,91,67,86,16,-109,-10,-82,-90,99,54,-94,4,-100,-12,74,-69,121,77,-97,102,119,-27,37,-99,52,69,81,-51,54,1,18,65,-79,75,53,-19,114,14,87,-100,39,-39,-127,21,-119,-7,0,14,39,-83,-113,86,100,-68,-58,57,15,66,46,-60,28,-8,-7,104,116,-63,-91,119,25,92,46,115,-97,65,-101,-126,-112,-22,6,90,-79,52,-103,-36,-82,113,-78,101,71,48,-119,-110,-21,-83,-74,-57,-43,84,-124,96,-76,20,25,-25,124,-27,-44,55,80,-7,6,97,-24,28,52,-87,-45,33,-99,-95,-128,-14,125,-112,126,-72,-97,-15,97,125,-64,-107,68,-83,-128,76,69,-52,-9,-62,-94,-112,43,47,-89,-47,-121,-60,-47,-62,14,7,-3,80,-42,-73,-83,-106,-128,25,75,-117,22,6,125,39,5,-86,12,24,9,49,107,-8,-36,-4,32,94,-6,8,127,-25,-17,-106,-43,-69,-113,-121,-82,13,79,-99,-120,-96,30,115,-60,95,34,-3,-5,-69,83,-120,32,84,-22,0,-78,53,-20,75,-10,17,85,-14,-58,-64,-55,59,12,-95,-65,-61,95,124,92,60,123,-65,56,-7,10,-16,100,86,104,119,92,-123,65,-97,-8,112,-116,79,-36,-27,61,127,87,-91,-30,-61,-69,111,-118,87,80,75,7,8,37,-32,8,-50,56,1,0,0,40,4,0,0,80,75,3,4,20,0,8,8,8,0,-19,-123,-66,88,0,0,0,0,0,0,0,0,0,0,0,0,16,0,0,0,100,111,99,80,114,111,112,115,47,97,112,112,46,120,109,108,77,-114,-63,10,-62,48,16,68,-17,-126,-1,16,114,111,-73,122,16,-111,52,-91,32,-126,39,123,-48,15,8,-23,-42,6,-102,77,72,86,-23,-25,-101,-109,122,-100,25,-26,-15,84,-73,-6,69,-68,49,101,23,-88,-107,-69,-70,-111,2,-55,-122,-47,-47,-77,-107,-113,-5,-91,58,-54,78,111,55,106,72,33,98,98,-121,89,-108,7,-27,86,-50,-52,-15,4,-112,-19,-116,-34,-28,-70,-52,84,-106,41,36,111,-72,-60,-12,-124,48,77,-50,-30,57,-40,-105,71,98,-40,55,-51,1,112,101,-92,17,-57,42,126,-127,82,-85,62,-58,-59,89,-61,69,66,-9,-47,20,-92,24,110,87,5,-1,-67,-126,-97,-125,-2,0,80,75,7,8,54,110,-125,33,-109,0,0,0,-72,0,0,0,80,75,3,4,20,0,8,8,8,0,-19,-123,-66,88,0,0,0,0,0,0,0,0,0,0,0,0,20,0,0,0,120,108,47,115,104,97,114,101,100,83,116,114,105,110,103,115,46,120,109,108,101,-111,95,75,-62,96,20,-58,-17,-125,-66,-61,-40,109,-44,76,43,45,-26,-68,8,-6,4,117,29,98,-85,13,-36,59,-13,125,-115,46,39,4,-10,31,35,-51,52,67,43,75,-94,90,-112,127,-102,90,-7,93,-22,125,-49,-74,111,-47,66,34,88,-105,-25,-9,60,-25,121,-32,28,49,-74,-83,37,-71,45,57,-115,85,29,69,-7,-23,-87,0,-49,-55,40,-95,-81,-87,104,35,-54,-81,44,47,77,70,-8,-104,52,62,38,98,76,-72,-124,-98,65,-60,51,5,121,46,-125,-44,-51,-116,-68,56,2,17,-98,-13,82,16,-114,-14,10,33,-87,5,65,-64,9,69,-42,-30,120,74,79,-55,-56,83,-42,-11,-76,22,39,-34,-104,-34,16,112,42,45,-57,-41,-80,34,-53,68,75,10,-63,64,96,78,-48,-30,42,-30,37,17,-85,-110,72,36,103,120,-23,52,119,-40,113,-115,93,-44,69,-127,72,-94,-16,-61,71,26,-19,55,-39,105,-42,79,-95,106,64,-71,2,-59,54,20,122,-52,60,-124,-35,60,-108,-70,110,-87,-19,-9,-39,-123,-106,-13,-68,-25,-98,126,-64,-15,-19,-65,-28,-31,21,51,-9,-32,44,7,-43,71,-72,49,-96,115,-32,119,76,-49,-52,-49,6,-25,-62,-77,-85,-10,-3,-48,-23,-18,123,21,-10,125,-33,-51,-26,-100,102,-106,-66,-41,63,-115,-126,-109,107,67,-15,-111,-66,87,-31,-30,-123,53,-102,-52,-38,-91,-42,-64,-67,62,119,107,45,-38,-53,-45,-73,-118,63,17,101,-110,73,63,11,5,-25,3,-95,80,56,-78,74,-83,125,-24,-68,78,-64,-45,-99,91,-18,64,63,79,45,3,74,-73,96,89,95,70,94,81,-56,-24,18,-20,-83,-50,-52,-82,93,60,4,99,96,15,106,-84,95,-75,31,14,-40,81,-53,-66,50,29,-77,65,-83,-58,-17,-54,-55,95,-113,-32,61,82,-6,6,80,75,7,8,5,13,30,99,-128,1,0,0,-10,1,0,0,80,75,3,4,20,0,8,8,8,0,-19,-123,-66,88,0,0,0,0,0,0,0,0,0,0,0,0,13,0,0,0,120,108,47,115,116,121,108,101,115,46,120,109,108,-91,-110,-79,110,-61,32,16,-122,-9,74,125,7,-60,-34,-32,100,-88,-94,-54,38,67,37,87,-99,-109,74,93,-119,57,-37,-88,112,88,64,34,-69,79,95,48,78,-109,76,29,58,-35,-35,-49,-3,31,-121,-49,-27,110,52,-102,-100,-63,121,101,-79,-94,-21,85,65,9,96,99,-91,-62,-82,-94,31,-121,-6,105,75,119,-4,-15,-95,-12,97,-46,-80,-17,1,2,-119,14,-12,21,-19,67,24,94,24,-13,77,15,70,-8,-107,29,0,-29,73,107,-99,17,33,-106,-82,99,126,112,32,-92,79,38,-93,-39,-90,40,-98,-103,17,10,41,47,-15,100,106,19,60,105,-20,9,67,69,11,-54,120,-39,90,-68,42,107,-102,5,94,-6,111,114,22,58,42,105,-74,-40,-42,88,109,29,81,40,97,4,89,-47,109,-46,80,24,-56,93,-81,66,-85,-93,83,51,79,24,-91,-89,44,111,-110,48,79,-70,-12,25,-123,-42,37,-111,-27,91,-26,-32,-93,73,105,-3,59,-60,-122,102,-127,-105,-125,8,1,28,-42,-79,32,75,126,-104,6,-88,40,90,-124,-116,-103,-5,-2,-24,-106,-62,125,-67,57,49,-35,56,-26,16,47,62,90,39,-29,22,110,-33,-97,37,94,106,104,67,52,56,-43,-11,41,6,59,-80,116,24,-126,53,49,-111,74,116,22,-123,78,-56,-117,99,73,34,-74,1,-83,-9,105,117,-97,-19,29,123,108,73,-34,-63,-69,76,-97,-97,-92,-25,95,-46,56,-48,-110,102,76,46,18,-1,-106,-106,-39,-1,-58,-110,-79,-67,-25,-49,104,118,-3,-35,-8,15,80,75,7,8,-82,-111,-109,-42,69,1,0,0,-93,2,0,0,80,75,3,4,20,0,8,8,8,0,-19,-123,-66,88,0,0,0,0,0,0,0,0,0,0,0,0,15,0,0,0,120,108,47,119,111,114,107,98,111,111,107,46,120,109,108,-115,-114,-79,78,-61,48,16,-122,119,36,-34,-63,-70,-99,58,1,-124,32,-118,-45,5,33,117,99,40,-20,-82,115,105,-84,-58,118,100,-101,-106,-75,11,47,64,59,49,49,49,-78,-80,-48,1,94,38,-55,115,52,73,21,96,100,58,-3,-70,-17,-66,-5,-29,-15,-93,42,-56,18,-83,-109,70,51,8,71,1,16,-44,-62,-92,82,-49,25,-36,77,111,78,46,97,-100,28,31,-59,43,99,23,51,99,22,-92,-27,-75,99,-112,123,95,70,-108,58,-111,-93,-30,110,100,74,-44,-19,38,51,86,113,-33,70,59,-89,-82,-76,-56,83,-105,35,122,85,-48,-45,32,-72,-96,-118,75,13,7,67,100,-1,-29,48,89,38,5,94,27,-15,-96,80,-5,-125,-60,98,-63,125,-37,-42,-27,-78,116,-112,-4,52,-69,-75,36,-27,30,-61,-85,-32,-100,65,-58,11,-121,64,-109,-72,-37,-36,75,92,-71,95,-80,-117,-124,11,47,-105,56,-27,51,6,65,-57,-47,63,96,-33,121,-104,68,115,-123,12,-86,-81,-105,122,-73,-87,-73,79,-43,-18,-83,126,94,87,-33,-81,-51,-6,-67,-39,126,52,-101,79,32,54,-110,41,3,59,73,-49,-128,-12,71,-109,54,-122,-67,118,112,-47,-31,123,-78,7,80,75,7,8,-21,-63,29,-35,2,1,0,0,115,1,0,0,80,75,3,4,20,0,8,8,8,0,-19,-123,-66,88,0,0,0,0,0,0,0,0,0,0,0,0,26,0,0,0,120,108,47,95,114,101,108,115,47,119,111,114,107,98,111,111,107,46,120,109,108,46,114,101,108,115,-83,-111,-63,78,-61,48,12,64,-17,-3,-118,-56,119,-102,118,-109,16,66,77,119,65,72,-69,-78,-15,1,81,-22,54,-43,-38,36,-78,13,-37,-2,-98,-128,4,-84,18,8,14,59,89,-74,-29,-25,-105,-92,-39,-100,-26,73,-67,34,-15,24,-125,-127,-70,-84,64,97,112,-79,27,-61,96,-32,121,-1,120,115,7,-101,-74,40,-102,39,-100,-84,-28,51,-20,-57,-60,42,15,5,54,-32,69,-46,-67,-42,-20,60,-50,-106,-53,-104,48,-28,78,31,105,-74,-110,83,26,116,-78,-18,96,7,-44,-85,-86,-70,-43,116,-55,-128,-74,80,106,-127,85,-37,-50,0,109,-69,26,-44,-2,-100,-16,63,-8,-40,-9,-93,-61,-121,-24,94,102,12,-14,-61,22,-51,-34,18,118,59,-95,124,33,-50,96,75,3,-118,-127,69,-71,-52,84,-48,-65,-6,-84,-82,-22,35,-25,9,47,69,62,-14,63,12,-42,-41,52,56,70,58,-80,71,-108,111,-119,-81,-46,-5,123,-27,80,127,-6,52,122,-15,-17,109,-15,6,80,75,7,8,-16,-50,88,-122,-44,0,0,0,48,2,0,0,80,75,3,4,20,0,8,8,8,0,-19,-123,-66,88,0,0,0,0,0,0,0,0,0,0,0,0,24,0,0,0,120,108,47,119,111,114,107,115,104,101,101,116,115,47,115,104,101,101,116,49,46,120,109,108,125,-110,75,79,-61,48,12,-128,-17,72,-4,-121,40,119,-106,-82,101,27,66,109,39,96,-102,-32,-128,-124,120,-35,-77,-42,125,104,109,82,37,-34,-54,-49,-57,-19,70,-107,-107,105,55,91,-97,29,127,-111,29,46,127,-22,-118,-19,-63,-40,82,-85,-120,79,39,30,103,-96,18,-99,-106,42,-113,-8,-41,-25,-6,-26,-114,47,-29,-21,-85,-80,-43,102,107,11,0,100,-44,-96,108,-60,11,-60,-26,94,8,-101,20,80,75,59,-47,13,40,34,-103,54,-75,68,74,77,46,108,99,64,-90,125,83,93,9,-33,-13,-26,-94,-106,-91,-30,113,-104,-106,53,-88,110,34,51,-112,69,-4,97,-54,69,28,-10,-123,-33,37,-76,-42,-119,89,55,119,-93,-11,-74,75,94,-46,-120,-109,31,-54,-51,7,84,-112,32,80,-114,102,7,93,-73,-8,-41,-66,-18,85,-34,12,75,33,-109,-69,10,-33,117,-5,12,101,94,32,125,115,70,-1,-4,27,-71,-110,40,-29,-48,-24,-106,25,34,100,-105,116,1,57,49,-86,-76,-108,-17,99,47,20,123,26,-111,28,-39,-93,-53,-90,-89,-20,-55,101,-2,41,91,-71,44,24,-104,-96,-39,-125,-128,63,8,-8,78,-15,-19,72,-64,101,-77,-111,-128,-53,-26,35,-127,-13,-20,68,32,24,4,-126,11,2,46,91,-116,4,-126,11,2,-25,-39,65,64,56,-21,104,100,14,-81,-46,-28,-91,-78,108,-93,17,117,77,-85,-97,44,102,-100,101,90,35,-104,46,-93,-73,10,58,-80,33,-87,32,-61,-66,-118,51,115,-40,115,31,-93,110,-114,-67,-35,-103,12,119,28,-1,2,80,75,7,8,-68,-47,56,-102,76,1,0,0,-5,2,0,0,80,75,1,2,20,0,20,0,8,8,8,0,-19,-123,-66,88,52,-98,38,-125,5,1,0,0,-12,1,0,0,17,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,100,111,99,80,114,111,112,115,47,99,111,114,101,46,120,109,108,80,75,1,2,20,0,20,0,8,8,8,0,-19,-123,-66,88,87,40,94,35,-29,0,0,0,70,2,0,0,11,0,0,0,0,0,0,0,0,0,0,0,0,0,68,1,0,0,95,114,101,108,115,47,46,114,101,108,115,80,75,1,2,20,0,20,0,8,8,8,0,-19,-123,-66,88,37,-32,8,-50,56,1,0,0,40,4,0,0,19,0,0,0,0,0,0,0,0,0,0,0,0,0,96,2,0,0,91,67,111,110,116,101,110,116,95,84,121,112,101,115,93,46,120,109,108,80,75,1,2,20,0,20,0,8,8,8,0,-19,-123,-66,88,54,110,-125,33,-109,0,0,0,-72,0,0,0,16,0,0,0,0,0,0,0,0,0,0,0,0,0,-39,3,0,0,100,111,99,80,114,111,112,115,47,97,112,112,46,120,109,108,80,75,1,2,20,0,20,0,8,8,8,0,-19,-123,-66,88,5,13,30,99,-128,1,0,0,-10,1,0,0,20,0,0,0,0,0,0,0,0,0,0,0,0,0,-86,4,0,0,120,108,47,115,104,97,114,101,100,83,116,114,105,110,103,115,46,120,109,108,80,75,1,2,20,0,20,0,8,8,8,0,-19,-123,-66,88,-82,-111,-109,-42,69,1,0,0,-93,2,0,0,13,0,0,0,0,0,0,0,0,0,0,0,0,0,108,6,0,0,120,108,47,115,116,121,108,101,115,46,120,109,108,80,75,1,2,20,0,20,0,8,8,8,0,-19,-123,-66,88,-21,-63,29,-35,2,1,0,0,115,1,0,0,15,0,0,0,0,0,0,0,0,0,0,0,0,0,-20,7,0,0,120,108,47,119,111,114,107,98,111,111,107,46,120,109,108,80,75,1,2,20,0,20,0,8,8,8,0,-19,-123,-66,88,-16,-50,88,-122,-44,0,0,0,48,2,0,0,26,0,0,0,0,0,0,0,0,0,0,0,0,0,43,9,0,0,120,108,47,95,114,101,108,115,47,119,111,114,107,98,111,111,107,46,120,109,108,46,114,101,108,115,80,75,1,2,20,0,20,0,8,8,8,0,-19,-123,-66,88,-68,-47,56,-102,76,1,0,0,-5,2,0,0,24,0,0,0,0,0,0,0,0,0,0,0,0,0,71,10,0,0,120,108,47,119,111,114,107,115,104,101,101,116,115,47,115,104,101,101,116,49,46,120,109,108,80,75,5,6,0,0,0,0,9,0,9,0,63,2,0,0,-39,11,0,0,0,0]", byte.class)
        def type = MapperUtil.str2Obj("\"XLSX\"", FileTypeEnum.class)
        def channel = "tour_business_dashboard"
        def host = "ws.uploadfile.fx.fws.qa.nt.ctripcorp.com"

        expect: "call"
        FileServerUtil.upload(data, type, channel, host)

    }*/
}
