package com.ctrip.tour.business.dashboard.tktBusiness.validator;

import com.ctrip.soa._24922.GetOverseaTrendLineDataRequestType;
import com.ctrip.soa._24922.GetOverseaTrendLineDataResponseType;
import com.ctrip.soa._24922.TimeFilter;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.InputArgumentException;
import com.ctrip.tour.business.dashboard.utils.MapperUtil;
import com.ctrip.tour.business.dashboard.utils.ParamterCheckUtil;
import com.ctrip.train.tieyouflight.soa.validation.validator.BusinessConstraintValidator;
import org.springframework.stereotype.Component;

@Component
public class GetOverseaTrendLineDataValidator extends BusinessConstraintValidator<GetOverseaTrendLineDataRequestType, GetOverseaTrendLineDataResponseType> {
    @Override
    public GetOverseaTrendLineDataResponseType validateBusiness(GetOverseaTrendLineDataRequestType request) {
        TimeFilter timeFilter = request.getTimeFilter();
        if (!ParamterCheckUtil.checkTimeFilter(timeFilter)) {
            throw new InputArgumentException("input error timeFilter:" + MapperUtil.obj2Str(request));
        }
        return null;
    }
}
