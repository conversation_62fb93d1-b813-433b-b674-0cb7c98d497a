package com.ctrip.tour.business.dashboard.sightArchives.service.Impl;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.sightArchives.bean.ExamineRangeBO;
import com.ctrip.tour.business.dashboard.sightArchives.dao.commonDao.AdmPrdTtdCpdTripDimInfoUnifiedOutputSRDao;
import com.ctrip.tour.business.dashboard.sightArchives.dao.flowDao.CdmLogTtdViewspotBenchTrafficDiDao;
import com.ctrip.tour.business.dashboard.sightArchives.dao.qualityDao.AdmSevTtdViewspotDefectDetailDfDao;
import com.ctrip.tour.business.dashboard.sightArchives.dao.qualityDao.AdmSevTtdViewspotFileIndexDfDao;
import com.ctrip.tour.business.dashboard.sightArchives.dao.salesDao.CdmOrdTtdVstArchiveDfDao;
import com.ctrip.tour.business.dashboard.sightArchives.entity.commonEntity.AdmPrdTtdCpdTripDimInfoUnifiedOutput;
import com.ctrip.tour.business.dashboard.sightArchives.dao.commonDao.BusSightArchivesAnnualIntakeDao;
import com.ctrip.tour.business.dashboard.sightArchives.enums.common.ExamineLevelEnumType;
import com.ctrip.tour.business.dashboard.sightArchives.enums.sales.SalesMetricEnumType;
import com.ctrip.tour.business.dashboard.sightArchives.proxy.CRankingRankingServiceProxy;
import com.ctrip.tour.business.dashboard.sightArchives.proxy.GeoLocationServiceProxy;
import com.ctrip.tour.business.dashboard.sightArchives.service.*;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.AppConfiguration;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.*;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardEmployeeInfo;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardExamineeConfigV2;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardOrganizationInfo;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardOverseaExamineeConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.ScenicLanguageHelper;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.MapperUtil;
import com.ctrip.tour.business.dashboard.utils.UserUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.util.EntityUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tour.auth.soa.model.SessionUserInfo;
import tour.auth.soa.session.SessionContext;

import javax.net.ssl.SSLContext;
import java.security.cert.X509Certificate;
import java.sql.SQLException;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CommonServiceImpl implements CommonService {

    @Autowired
    private RemoteConfig remoteConfig;
    @Autowired
    private CRankingRankingServiceProxy cRankingRankingServiceProxy;
    @Autowired
    private AdmPrdTtdCpdTripDimInfoUnifiedOutputSRDao admPrdTtdCpdTripDimInfoUnifiedOutputSRDao;
    @Autowired
    private BusSightArchivesAnnualIntakeDao busSightArchivesAnnualIntakeDao;
    @Autowired
    private BusinessDashboardOverseaExamineeConfigDao overseaExamineeConfigDao;
    @Autowired
    private BusinessDashboardExamineeConfigV2Dao domesticExamineeConfigV2Dao;
    @Autowired
    private BusinessDashboardEmployeeInfoDao businessDashboardEmployeeInfoDao;
    @Autowired
    private CdmOrdTtdVstArchiveDfDao cdmOrdTtdVstArchiveDfDao;
    @Autowired
    private BusinessDashboardUpdatetimeDao businessDashboardUpdatetimeDao;
    @Autowired
    GeoLocationServiceProxy geoLocationServiceProxy;

    @Autowired
    private CompetitiveService competitiveService;
    @Autowired
    private FlowService flowService;
    @Autowired
    private MarketService marketService;
    @Autowired
    private QualityService qualityService;
    @Autowired
    private SalesService salesService;
    @Autowired
    private UserProfileService userProfileService;

    @Autowired
    private AppConfiguration appConfiguration;
    @Autowired
    private BusinessDashboardOrganizationInfoDao organizationInfoDao;

    //    cdm_log_ttd_viewspot_bench_traffic_di
    @Autowired
    private CdmLogTtdViewspotBenchTrafficDiDao cdmLogTtdViewspotBenchTrafficDiDao;

    //adm_sev_ttd_viewspot_file_index_df
    @Autowired
    private AdmSevTtdViewspotFileIndexDfDao admSevTtdViewspotFileIndexDfDao;

    @Autowired
    private AdmSevTtdViewspotDefectDetailDfDao admSevTtdViewspotDefectDetailDfDao;

    @Override
    public CheckSightArchivesPermissionResponseType checkSightArchivesPermission(CheckSightArchivesPermissionRequestType requestType) {
        //默认为无权限
        CheckSightArchivesPermissionResponseType responseType = new CheckSightArchivesPermissionResponseType();

        String empCode = UserUtil.getMappingEmpCode(remoteConfig);
        //判断该登录员工是否已申请通过该页面权限code
        boolean havePermission = false;

        //测试  都有权限
        String permissionStr = remoteConfig.getConfigValue("sightArchivesPermission");
        if (StringUtils.isNotBlank(permissionStr)) {
            Boolean permission = Boolean.valueOf(permissionStr);
            responseType.setHavePermission(permission);
            return responseType;
        }


        try {
            SessionUserInfo sessionUserInfo = SessionContext.getInstance().getUserInfo();
            List<String> permissionList = sessionUserInfo.getPermissionList();
            havePermission = permissionList.contains("FP39919");  //景点档案权限编码:原FP39894改为FP39919
        } catch (Exception e) {
            log.error("checkOperatePermission error,orgEmoCode:{}", MDC.get("empCode"), e);
        }

        responseType.setHavePermission(havePermission);
        return responseType;
    }

    @Override
    public GetSightArchivesUpdateTimeResponseType getSightArchivesUpdateTime(GetSightArchivesUpdateTimeRequestType requestType) {
        GetSightArchivesUpdateTimeResponseType responseType = new GetSightArchivesUpdateTimeResponseType();

        String queryD = getQueryD();
        String yesterday = "";
        try {
            yesterday = DateUtil.getDayOfInterval(queryD, -1);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        responseType.setUpdateTime(yesterday);
        return responseType;
    }

//    public SearchSightListResponseType searchSightList(SearchSightListRequestType requestType, String empCode) {
//
//        SearchSightListResponseType responseType = new SearchSightListResponseType();
//        List<Sight> sightList = new ArrayList<>();
//        responseType.setSightList(sightList);
//
//        String searchKey = requestType.getSearchKey();
//        String queryD = getQueryD();
//        //老季看全量，李哲看国内全量，刘畅看海外全量
////        String empCode = UserUtil.getMappingEmpCode(remoteConfig);
//        List<ExamineRangeBO> examineRangeBOList = new ArrayList<>();
//        BusinessDashboardEmployeeInfo employeeInfo = queryEmployeeInfo(empCode);
//
//
//        if("S35097".equals(empCode)) {  //门票ceo 老季
//            //查全量 不筛选
//            examineRangeBOList = new ArrayList<>();
//
//        } else if("D00024".equals(empCode)){  //国内负责人 李哲
//            //查国内全量
//            ExamineRangeBO examineRangeBO = new ExamineRangeBO();
//            examineRangeBO.setExamineLevel(ExamineLevelEnumType.DOMESTIC);
//            examineRangeBO.setExamineRange(Collections.singleton("1"));
//            examineRangeBOList.add(examineRangeBO);
//        } else if("S52754".equals(empCode) || isSpecialEmployee(employeeInfo)){
//            //查海外全量：海外负责人"刘畅"、命中特殊逻辑的员工
//            ExamineRangeBO examineRangeBO = new ExamineRangeBO();
//            examineRangeBO.setExamineLevel(ExamineLevelEnumType.OVERSEA);
//            examineRangeBO.setExamineRange(Collections.singleton("0"));
//            examineRangeBOList.add(examineRangeBO);
//        }else {
//            //其它人的搜索范围限定为其考核范围
//            if(employeeInfo==null){
//                return responseType;
//            }
//            //查询员工的考核范围
//            examineRangeBOList = queryExamineResult(employeeInfo, queryD);
//            //如果考核范围为空，直接返回
//            if(CollectionUtils.isEmpty(examineRangeBOList)){
////                System.out.println("没有考核范围,直接返回");
//                return responseType;
//            }
//        }
//
////        System.out.println("考核范围: "+MapperUtil.obj2Str(examineRangeBOList));
//
//        //根据其考核范围在景点信息表中查询其负责的景点
//        //精确搜索
//        List<Sight> preciseSearchResultList = admPrdTtdCpdTripDimInfoUnifiedOutputSRDao.preciseQuerySightList(queryD,examineRangeBOList, searchKey);
//        //模糊搜索
//        List<Sight> fuzzySearchResultList = admPrdTtdCpdTripDimInfoUnifiedOutputSRDao.fuzzyQuerySightList(queryD,examineRangeBOList, searchKey);
//
//        //删除重复的景点
//        Set<Long> sightIdSet = preciseSearchResultList.stream()
//                .map(Sight::getSightId)
//                .collect(Collectors.toSet());
//        fuzzySearchResultList.removeIf(sight -> sightIdSet.contains(sight.getSightId()));
//
//        sightList.addAll(preciseSearchResultList);
//        sightList.addAll(fuzzySearchResultList);
//
//        return responseType;
//    }

    /**
     * 仅返回在其考核范围内的景点
     */
    @Override
    public SearchSightListResponseType searchSightList(SearchSightListRequestType requestType) {

        SearchSightListResponseType responseType = new SearchSightListResponseType();
        List<Sight> sightList = new ArrayList<>();
        responseType.setSightList(sightList);

        String searchKey = requestType.getSearchKey();
        String queryD = getQueryD();
        //老季看全量，李哲看国内全量，刘畅看海外全量
        String empCode = UserUtil.getMappingEmpCode(remoteConfig);
        List<ExamineRangeBO> examineRangeBOList = new ArrayList<>();
        BusinessDashboardEmployeeInfo employeeInfo = queryEmployeeInfo(empCode);


        if ("S35097".equals(empCode)) {  //门票ceo 老季
            //查全量 不筛选
            examineRangeBOList = new ArrayList<>();

        } else if ("D00024".equals(empCode)) {  //国内负责人 李哲
            //查国内全量
            ExamineRangeBO examineRangeBO = new ExamineRangeBO();
            examineRangeBO.setExamineLevel(ExamineLevelEnumType.DOMESTIC);
            examineRangeBO.setExamineRange(Collections.singleton("1"));
            examineRangeBOList.add(examineRangeBO);
        } else if ("S52754".equals(empCode) || isSpecialEmployee(employeeInfo)) {
            //查海外全量：海外负责人"刘畅"、命中特殊逻辑的员工
            ExamineRangeBO examineRangeBO = new ExamineRangeBO();
            examineRangeBO.setExamineLevel(ExamineLevelEnumType.OVERSEA);
            examineRangeBO.setExamineRange(Collections.singleton("0"));
            examineRangeBOList.add(examineRangeBO);
        } else {
            //其它人的搜索范围限定为其考核范围
            if (employeeInfo == null) {
                return responseType;
            }
            //查询员工的考核范围
            examineRangeBOList = queryExamineResult(employeeInfo, queryD);
            //如果考核范围为空，直接返回
            if (CollectionUtils.isEmpty(examineRangeBOList)) {
                return responseType;
            }
        }

        //根据其考核范围在景点信息表中查询其负责的景点
        //精确搜索
        List<Sight> preciseSearchResultList = admPrdTtdCpdTripDimInfoUnifiedOutputSRDao.preciseQuerySightList(queryD, examineRangeBOList, searchKey);
        //模糊搜索
        List<Sight> fuzzySearchResultList = admPrdTtdCpdTripDimInfoUnifiedOutputSRDao.fuzzyQuerySightList(queryD, examineRangeBOList, searchKey);

        //删除重复的景点
        Set<Long> sightIdSet = preciseSearchResultList.stream()
                .map(Sight::getSightId)
                .collect(Collectors.toSet());
        fuzzySearchResultList.removeIf(sight -> sightIdSet.contains(sight.getSightId()));

        sightList.addAll(preciseSearchResultList);
        sightList.addAll(fuzzySearchResultList);

        return responseType;
    }

    private boolean isSpecialEmployee(BusinessDashboardEmployeeInfo employeeInfo) {
        if (employeeInfo == null || StringUtils.isBlank(employeeInfo.getOrgNamePath())) {
            return false;
        }
        if (employeeInfo.getOrgNamePath().contains("国际化业务职能中心")) {//NOSONAR
            return true;
        }

        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("access_token", "4a44f5b98781b36e28de16cd5901c0a7");
        Map<String, String> requestBodyMap = new HashMap<>();
        requestBodyMap.put("emailaddress", "<EMAIL>");
        requestMap.put("request_body", requestBodyMap);
        String requestJson = MapperUtil.obj2Str(requestMap);
        log.info("查询**************************邮件组下员工,请求参数:{}", requestJson); //NOSONAR
        String response = post("https://itosginternal.it.ctripcorp.com/api/get_group_member_by_emailaddress", requestJson);
        log.info("查询**************************邮件组下员工,返回:{}", response);  //NOSONAR
//        System.out.println(response);
        List<Map<String, String>> resultMapList = MapperUtil.str2Obj(response, List.class);
        List<String> empCodeList = resultMapList.stream()
                .map(map -> map.get("emp_code"))
                .collect(Collectors.toList());

        return empCodeList.contains(employeeInfo.getEmpCode());

    }

    private BusinessDashboardEmployeeInfo queryEmployeeInfo(String empCode) {

        BusinessDashboardEmployeeInfo employeeInfo = null;
        try {
            employeeInfo = businessDashboardEmployeeInfoDao.queryByEmpCode(empCode);
            if (employeeInfo == null) {
                log.warn("未查询到该员工信息,工号:{}", empCode); //NOSONAR
            }
        } catch (SQLException e) {
            log.warn("查询员工信息失败", e);  //NOSONAR
        }
        return employeeInfo;
    }

    //查询员工的考核范围
    private List<ExamineRangeBO> queryExamineResult(BusinessDashboardEmployeeInfo employeeInfo, String queryD) {
        List<ExamineRangeBO> examineRangeBOList = new ArrayList<>();
        //获取国内考核配置
        BusinessDashboardExamineeConfigV2 domesticGmvMetricConfig = getDomesticGmvMetricConfig(employeeInfo.getDomainName(), queryD);
        if (domesticGmvMetricConfig != null) {
            //国内-门票
            String tktExamineLevel = domesticGmvMetricConfig.getExamineLevel();
            String tktExamineRange = domesticGmvMetricConfig.getExamineRange();
            Set<String> tktExamineRangeSet = StringUtils.isBlank(tktExamineRange) ? new HashSet<>() : new HashSet<>(Arrays.asList(tktExamineRange.split(";")));
            if (StringUtils.isNotBlank(tktExamineLevel)) {
                ExamineRangeBO examineRangeBO = new ExamineRangeBO();
                examineRangeBO.setExamineLevel(ExamineLevelEnumType.getDomesticExamineLevelEnumType(tktExamineLevel));
                if (ExamineLevelEnumType.DOMESTIC.getChineseName().equals(tktExamineLevel)) {
                    examineRangeBO.setExamineRange(Collections.singleton("1"));
                } else if (ExamineLevelEnumType.DOMESTIC_SIGHT.getChineseName().equals(tktExamineLevel)) {
                    //若考核层级为景点，可见的景点范围扩充至其考核景点对应的省份
                    examineRangeBO.setExamineLevel(ExamineLevelEnumType.DOMESTIC_PROVINCE);
                    List<String> provinceNameList = admPrdTtdCpdTripDimInfoUnifiedOutputSRDao.queryProvinceNameListByOriViewspotMeidList(queryD, new ArrayList<>(tktExamineRangeSet));
                    examineRangeBO.setExamineRange(new HashSet<>(provinceNameList));
                } else {
                    examineRangeBO.setExamineRange(tktExamineRangeSet);
                }
                examineRangeBOList.add(examineRangeBO);
            }
            //国内-活动
            String actExamineLevel = domesticGmvMetricConfig.getActExamineLevel();
            String actExamineRange = domesticGmvMetricConfig.getActExamineRange();
            Set<String> actExamineRangeSet = StringUtils.isBlank(actExamineRange) ? new HashSet<>() : new HashSet<>(Arrays.asList(actExamineRange.split(";")));

            if (StringUtils.isNotBlank(actExamineLevel)) {
                ExamineRangeBO examineRangeBO = new ExamineRangeBO();
                examineRangeBO.setExamineLevel(ExamineLevelEnumType.getDomesticExamineLevelEnumType(actExamineLevel));
                if (ExamineLevelEnumType.DOMESTIC.getChineseName().equals(actExamineLevel)) {
                    examineRangeBO.setExamineRange(Collections.singleton("1"));
                } else if (ExamineLevelEnumType.DOMESTIC_SIGHT.getChineseName().equals(actExamineLevel)) {
                    //若考核层级为景点，可见的景点范围扩充至其考核景点对应的省份
                    examineRangeBO.setExamineLevel(ExamineLevelEnumType.DOMESTIC_PROVINCE);
                    List<String> provinceNameList = admPrdTtdCpdTripDimInfoUnifiedOutputSRDao.queryProvinceNameListByOriViewspotMeidList(queryD, new ArrayList<>(actExamineRangeSet));
                    examineRangeBO.setExamineRange(new HashSet<>(provinceNameList));
                } else {
                    examineRangeBO.setExamineRange(actExamineRangeSet);
                }
                examineRangeBOList.add(examineRangeBO);
            }

            //国内-境内日游
            String odtExamineLevel = domesticGmvMetricConfig.getOdtExamineLevel();
            String odtExamineRange = domesticGmvMetricConfig.getOdtExamineRange();
            Set<String> odtExamineRangeSet = StringUtils.isBlank(odtExamineRange) ? new HashSet<>() : new HashSet<>(Arrays.asList(odtExamineRange.split(";")));

            if (StringUtils.isNotBlank(odtExamineLevel)) {
                ExamineRangeBO examineRangeBO = new ExamineRangeBO();
                examineRangeBO.setExamineLevel(ExamineLevelEnumType.getDomesticExamineLevelEnumType(odtExamineLevel));
                if (ExamineLevelEnumType.DOMESTIC.getChineseName().equals(odtExamineLevel)) {
                    examineRangeBO.setExamineRange(Collections.singleton("1"));
                } else if (ExamineLevelEnumType.DOMESTIC_SIGHT.getChineseName().equals(odtExamineLevel)) {
                    //若考核层级为景点，可见的景点范围扩充至其考核景点对应的省份
                    examineRangeBO.setExamineLevel(ExamineLevelEnumType.DOMESTIC_PROVINCE);
                    List<String> provinceNameList = admPrdTtdCpdTripDimInfoUnifiedOutputSRDao.queryProvinceNameListByOriViewspotMeidList(queryD, new ArrayList<>(odtExamineRangeSet));
                    examineRangeBO.setExamineRange(new HashSet<>(provinceNameList));
                } else {
                    examineRangeBO.setExamineRange(odtExamineRangeSet);
                }
                examineRangeBOList.add(examineRangeBO);
            }

            //国内-出境日游
            String overseaOdtExamineLevel = domesticGmvMetricConfig.getOverseaOdtExamineLevel();
            String overseaOdtExamineRange = domesticGmvMetricConfig.getOverseaOdtExamineRange();
            Set<String> overseaOdtExamineRangeSet = StringUtils.isBlank(overseaOdtExamineRange) ? new HashSet<>() : new HashSet<>(Arrays.asList(overseaOdtExamineRange.split(";")));
            if (StringUtils.isNotBlank(overseaOdtExamineLevel)) {
                ExamineRangeBO examineRangeBO = new ExamineRangeBO();
                examineRangeBO.setExamineLevel(ExamineLevelEnumType.getDomesticExamineLevelEnumType(overseaOdtExamineLevel));
                if (ExamineLevelEnumType.DOMESTIC.getChineseName().equals(overseaOdtExamineLevel)) {
                    examineRangeBO.setExamineRange(Collections.singleton("1"));
                } else if (ExamineLevelEnumType.DOMESTIC_SIGHT.getChineseName().equals(overseaOdtExamineLevel)) {
                    //若考核层级为景点，可见的景点范围扩充至其考核景点对应的省份
                    examineRangeBO.setExamineLevel(ExamineLevelEnumType.DOMESTIC_PROVINCE);
                    List<String> provinceNameList = admPrdTtdCpdTripDimInfoUnifiedOutputSRDao.queryProvinceNameListByOriViewspotMeidList(queryD, new ArrayList<>(overseaOdtExamineRangeSet));
                    examineRangeBO.setExamineRange(new HashSet<>(provinceNameList));
                } else {
                    examineRangeBO.setExamineRange(overseaOdtExamineRangeSet);
                }
                examineRangeBOList.add(examineRangeBO);
            }
        }


        //获取海外人员大区: 取"国际化业务中心"下第二个层级的名称
        if (employeeInfo.getOrgNamePath().contains("国际化业务部") && !employeeInfo.getOrgNamePath().contains("国际化业务职能中心")) { //NOSONAR
            String orgNamePath = employeeInfo.getOrgNamePath();
            String[] orgNamePathArray = orgNamePath.split(">");
            if (orgNamePathArray.length >= 10) {
                String regionName = orgNamePathArray[9];   //该层级为产品想要的大区
                regionName = regionName.replaceAll("\\s+", ""); //去除空格

                String mappingValue = remoteConfig.getConfigValue("overseaRegionNameMapping_" + regionName); //NOSONAR
                if (StringUtils.isNotBlank(mappingValue)) {
                    ExamineRangeBO examineRangeBO = new ExamineRangeBO();
                    examineRangeBO.setExamineLevel(ExamineLevelEnumType.OVERSEA_REGION);
                    examineRangeBO.setExamineRange(Collections.singleton(mappingValue));
                    examineRangeBOList.add(examineRangeBO);
                }
            } else if (orgNamePathArray.length == 9 && "国际化业务中心".equals(orgNamePathArray[7])) { //NOSONAR
                //如果只有9层 说明他在国际化业务中心的下一层级，则取出组织架构中该节点下的所有直接子节点
                String orgIdPath = employeeInfo.getOrgIdPath();
                String[] orgIdPathArray = orgIdPath.split(" ");
                String orgNodeId = orgIdPathArray[8];
                List<BusinessDashboardOrganizationInfo> organizationInfoList = new ArrayList<>();
                try {
                    organizationInfoList = organizationInfoDao.queryByParentOrgId(Collections.singletonList(orgNodeId));
                } catch (SQLException e) {
                    log.error("查询组织信息失败", e); //NOSONAR
                }
                ExamineRangeBO examineRangeBO = new ExamineRangeBO();
                examineRangeBO.setExamineLevel(ExamineLevelEnumType.OVERSEA_REGION);
                Set<String> examineRangeSet = new HashSet<>();
                examineRangeBO.setExamineRange(examineRangeSet);
                for (BusinessDashboardOrganizationInfo businessDashboardOrganizationInfo : organizationInfoList) {
                    String nodeOrgName = businessDashboardOrganizationInfo.getNodeOrgName();
                    nodeOrgName = nodeOrgName.replaceAll("\\s+", ""); //去除空格
                    String mappingValue = remoteConfig.getConfigValue("overseaRegionNameMapping_" + nodeOrgName); //NOSONAR
                    examineRangeSet.add(mappingValue);
                }
                examineRangeBOList.add(examineRangeBO);
            } else if (orgNamePathArray.length == 7 && "国际化业务部".equals(orgNamePathArray[6])) {  //NOSONAR
                //这个层级需单独做特殊逻辑：陈星昊(TR014728)看欧洲大区、张潇日(S32115)看海长大区
                if ("TR014728".equals(employeeInfo.getEmpCode())) {
                    //陈星昊
                    ExamineRangeBO examineRangeBO = new ExamineRangeBO();
                    examineRangeBO.setExamineLevel(ExamineLevelEnumType.OVERSEA_REGION);
                    examineRangeBO.setExamineRange(Collections.singleton("欧洲"));  //NOSONAR
                    examineRangeBOList.add(examineRangeBO);
                } else if ("S32115".equals(employeeInfo.getEmpCode())) {
                    //张潇日
                    ExamineRangeBO examineRangeBO = new ExamineRangeBO();
                    examineRangeBO.setExamineLevel(ExamineLevelEnumType.OVERSEA_REGION);
                    examineRangeBO.setExamineRange(Collections.singleton("海长大区"));  //NOSONAR
                    examineRangeBOList.add(examineRangeBO);
                }
            }
        }
//        BusinessDashboardOverseaExamineeConfig overseaGmvMetricConfig = getOverseaGmvMetricConfig(employeeInfo.getDomainName(), queryD);
//        if(overseaGmvMetricConfig!=null){
//            //海外
//            String destinationExamineLevel = overseaGmvMetricConfig.getDestinationExamineLevel();
//            String destinationExamineRange = overseaGmvMetricConfig.getDestinationExamineRange();
//            Set<String> destinationExamineRangeSet = StringUtils.isBlank(destinationExamineRange) ? new HashSet<>(): new HashSet<>(Arrays.asList(destinationExamineRange.split("[;，]")));
//            if(StringUtils.isNotBlank(destinationExamineLevel)){
//                ExamineRangeBO examineRangeBO = new ExamineRangeBO();
//                examineRangeBO.setExamineLevel(ExamineLevelEnumType.getOverseaExamineLevelEnumType(destinationExamineLevel));
//                if (ExamineLevelEnumType.OVERSEA.getChineseName().equals(destinationExamineLevel)) {
//                    examineRangeBO.setExamineRange(Collections.singleton("0"));
//                }else {
//                    examineRangeBO.setExamineRange(destinationExamineRangeSet);
//                }
//                examineRangeBOList.add(examineRangeBO);
//            }
//        }


//        //合并相同考核层级下的考核范围
//        for(int i=0; i<examineRangeBOList.size();i++){
//            for (int j=i+1;j<examineRangeBOList.size();j++){
//                if(examineRangeBOList.get(i).getExamineLevel().equals(examineRangeBOList.get(j).getExamineLevel())){
//                    examineRangeBOList.get(i).getExamineRange().addAll(examineRangeBOList.get(j).getExamineRange());
//                    examineRangeBOList.remove(j);
//                    j--;
//                }
//            }
//        }

        return examineRangeBOList;
    }


    //国内员工GMV指标考核配置
    public BusinessDashboardExamineeConfigV2 getDomesticGmvMetricConfig(String domainName, String queryD) {

        String year = null;
        String quarter = null;
        try {
            year = DateUtil.getActualYearOfD(queryD);
            quarter = DateUtil.getActualQuarterOfD(queryD);
        } catch (ParseException e) {
            log.error("解析日期失败", e);  //NOSONAR
            return null;
        }

        //考核配置表中gmv指标code
        String metricCode = "1;2";
        //获取考核配置   按大区/省份/景点考核
        List<BusinessDashboardExamineeConfigV2> metricConfigList = null;
        try {
            metricConfigList = domesticExamineeConfigV2Dao.queryMetricConfig(domainName, queryD, year, quarter, metricCode);
        } catch (SQLException e) {
            log.warn("查询国内员工GMV指标考核配置失败", e); //NOSONAR
        }

        return CollectionUtils.isEmpty(metricConfigList) ? null : metricConfigList.get(0);
    }

    //海外员工gmv考核配置
    private BusinessDashboardOverseaExamineeConfig getOverseaGmvMetricConfig(String domainName, String queryD) {

        String year = null;
        String quarter = null;
        try {
            year = DateUtil.getActualYearOfD(queryD);
            quarter = DateUtil.getActualQuarterOfD(queryD);
        } catch (ParseException e) {
            log.error("解析日期失败", e); //NOSONAR
            return null;
        }

        //考核配置表中gmv指标code
        String metricCode = "101;102";
        //获取考核配置   按大区/省份/景点考核
        List<BusinessDashboardOverseaExamineeConfig> metricConfigList = null;
        try {
            metricConfigList = overseaExamineeConfigDao.queryMetricConfig(domainName, queryD, year, quarter, metricCode);
        } catch (SQLException e) {
            log.warn("查询海外员工GMV指标考核配置失败", e);  //NOSONAR
        }

        return CollectionUtils.isEmpty(metricConfigList) ? null : metricConfigList.get(0);
    }

    @Override
    public GetSightInfoResponseType getSightInfo(GetSightInfoRequestType requestType) {
        Long sightId = requestType.getSightId();
        if (sightId == null) {
            return new GetSightInfoResponseType();
        }
        String language = UserUtil.getVbkLocaleForScenic();

        String queryD = getQueryD();

        AdmPrdTtdCpdTripDimInfoUnifiedOutput realOutPut = admPrdTtdCpdTripDimInfoUnifiedOutputSRDao.querySightInfo(sightId, queryD);
        if (realOutPut == null) {
            return new GetSightInfoResponseType();
        }
        //景点经理
        List<String> mEmpNameList = new ArrayList<>();
        if (StringUtils.isNotBlank(realOutPut.getOriViewspotMeid())) {
            mEmpNameList = Arrays.asList(realOutPut.getOriViewspotMeid().split(","));
        }
        //景点助理
        List<String> aEmpNameList = new ArrayList<>();
        if (StringUtils.isNotBlank(realOutPut.getOriViewspotAeid())) {
            aEmpNameList = Arrays.asList(realOutPut.getOriViewspotAeid().split(","));
        }
        //封装景点信息
        SightInfo sightInfo = new SightInfo();
        sightInfo.setSightId(realOutPut.getViewspotId());
        if ("en-US".equalsIgnoreCase(language) && realOutPut.getPoiEnName() != null && !"null".equals(realOutPut.getPoiEnName())) {
            sightInfo.setSightName(realOutPut.getPoiEnName());
        } else {
            sightInfo.setSightName(realOutPut.getViewspotName());
        }
        sightInfo.setDomestic(realOutPut.getIsDomestic() == null ? null : (realOutPut.getIsDomestic() == 1));
        sightInfo.setHaveSubSight(realOutPut.getIsHasSubViewspot() == null ? null : (realOutPut.getIsHasSubViewspot() == 1));
        sightInfo.setValid(realOutPut.getIsActive() == null ? null : (realOutPut.getIsActive() == 1));
        sightInfo.setSightHierarchy(realOutPut.getViewspotLevel());
        sightInfo.setSightLevel(ScenicLanguageHelper.getMultiLanguage(realOutPut.getViewspotClass(), language));// todo 完成，已测试
        StringBuilder businessSegments = new StringBuilder();
        if (realOutPut.getIsDomestic() != null) {
            if ("en-US".equalsIgnoreCase(language)) {
                businessSegments.append(realOutPut.getIsDomestic() == 1 ? "domestic" : "oversea").append("-");//todo 完成，已测试
            } else {
                businessSegments.append(realOutPut.getIsDomestic() == 1 ? "国内" : "海外").append("-");  //NOSONAR
            }
        }
        businessSegments.append(ScenicLanguageHelper.getMultiLanguage(realOutPut.getBusinessRegionName(), language)).append("-");//todo 完成，已测试
        Map<Long, String> cityTranslateResultMap = geoLocationServiceProxy.getLocationTranslateResult(Collections.singletonList(realOutPut.getCityId()), 3);
        if (cityTranslateResultMap.containsKey(realOutPut.getCityId())) {
            businessSegments.append(cityTranslateResultMap.get(realOutPut.getCityId()));//todo 完成，待测试
        }

        sightInfo.setBusinessSegments(businessSegments.toString());
        sightInfo.setMEmpName(mEmpNameList);
        sightInfo.setAEmpName(aEmpNameList);
        //年入园量
        Double annualIntakeValue = busSightArchivesAnnualIntakeDao.queryAnnualIntakeBySightId(sightId);
        sightInfo.setAnnualIntakeValue(annualIntakeValue);

        //判断该员工是否为该景点的景点经理or景点助理，是则有编辑权限
        BusinessDashboardEmployeeInfo employeeInfo = queryEmployeeInfo(UserUtil.getMappingEmpCode(remoteConfig));
        boolean haveEditPermission = false;
        if (employeeInfo != null) {
            haveEditPermission = mEmpNameList.contains(employeeInfo.getDomainName()) || aEmpNameList.contains(employeeInfo.getDomainName());
        }
        sightInfo.setHaveEditPermission(haveEditPermission);

        //c端榜单接口
        List<String> rankDescList = cRankingRankingServiceProxy.getRankingItemRank(realOutPut.getPoiId());
        sightInfo.setRankDescList(rankDescList);

        GetSightInfoResponseType responseType = new GetSightInfoResponseType();
        responseType.setSightInfo(sightInfo);
        return responseType;
    }

    @Override
    public UpdateAnnualIntakeResponseType updateAnnualIntake(UpdateAnnualIntakeRequestType requestType) {
        Long sightId = requestType.getSightId();
        Double annualIntakeValue = requestType.getAnnualIntakeValue();
        UpdateAnnualIntakeResponseType responseType = new UpdateAnnualIntakeResponseType();
        responseType.setSuccess(false);
        //条件缺失, 无法更新
        if (sightId == null || annualIntakeValue == null) {
            return responseType;
        }

        //更新该景点年入园量，更新成功返回true
        boolean success = busSightArchivesAnnualIntakeDao.insertAnnualIntake(sightId, annualIntakeValue);
        if (success) {
            responseType.setNewAnnualIntakeValue(annualIntakeValue);
            responseType.setSuccess(true);
        }

        return responseType;
    }

    @Override
    public SearchVendorListResponseType searchVendorList(SearchVendorListRequestType requestType) {
        String searchKey = requestType.getSearchKey();
        Long sightId = requestType.getSightId();
        if (StringUtils.isBlank(searchKey)) {
            searchKey = "";
        }
        String queryD = getQueryD();

        //根据searchKey查询产量表，注：需限制范围为该景点下的供应商, 精确匹配供应商id,模糊匹配供应商名称,最多返回20个
        List<Vendor> vendorList = cdmOrdTtdVstArchiveDfDao.queryVendorList(sightId, searchKey, queryD);

        SearchVendorListResponseType responseType = new SearchVendorListResponseType();
        responseType.setVendorList(vendorList);
        return responseType;
    }

    @Override
    public GetSightArchivesReportSummaryResponseType getSightArchivesReportSummary(GetSightArchivesReportSummaryRequestType getSightArchivesReportSummaryRequestType) {
        GetSightArchivesReportSummaryResponseType sightArchivesReportSummaryResponseType = new GetSightArchivesReportSummaryResponseType();
        SightArchivesCommonFilter commonFilter = getSightArchivesReportSummaryRequestType.getCommonFilter();
        Long sightId = commonFilter.getSightId();
        Integer dateType = commonFilter.getDateType();
        String startDate = commonFilter.getStartDate();
        String endDate = commonFilter.getEndDate();
        List<Long> vendorIdList = commonFilter.getVendorIdList();
        Integer businessType = commonFilter.getBusinessType();
        Boolean needSubSight = commonFilter.isNeedSubSight();

        List<RadarMetricItem> radarMetricItemList = new ArrayList<>();
        String queryD = getQueryD();

        //是否国内
        AdmPrdTtdCpdTripDimInfoUnifiedOutput realOutPut = admPrdTtdCpdTripDimInfoUnifiedOutputSRDao.querySightInfo(sightId, queryD);
        if (realOutPut == null) {
            return sightArchivesReportSummaryResponseType;
        }

        Long isDomestic = realOutPut.getIsDomestic();//1国内 0 海外

        //GMV
        Map<String,Object> gmv = cdmOrdTtdVstArchiveDfDao.queryRadargmv(queryD,sightId, dateType, startDate, endDate, vendorIdList, businessType,needSubSight);
        Map<String,Object> gmvpop = cdmOrdTtdVstArchiveDfDao.queryRadargmvPop(queryD,sightId, dateType, startDate, endDate, vendorIdList, businessType,needSubSight);
        //GMV均值
        Map<String,Object> gmvaverage = cdmOrdTtdVstArchiveDfDao.queryRadargmvAverage(queryD,sightId, dateType, startDate, endDate, vendorIdList, businessType, isDomestic);
        Map<String,Object> gmvaveragepop = cdmOrdTtdVstArchiveDfDao.queryRadargmvAveragePop(queryD,sightId, dateType, startDate, endDate, vendorIdList, businessType, isDomestic);
        Double average = 0.0;
        if (gmvaveragepop.get("uv_average") != null && !gmvaveragepop.get("uv_average").equals(0.0) && gmvaverage.get("uv_average") != null) {
            average = ((Double) gmvaverage.get("gmv_average") - (Double) gmvaveragepop.get("gmv_average"))/(Double) gmvaveragepop.get("gmv_average");
        }

        RadarMetricItem radarMetricItemgmvpop = new RadarMetricItem();
        radarMetricItemgmvpop.setMetricName("gmv");
        if (gmvpop.get("gmv") != null && !gmvpop.get("gmv").equals(0.0) && gmv.get("gmv") != null) {
            radarMetricItemgmvpop.setValue(((Integer) gmv.get("gmv") - (Double) gmvpop.get("gmv")) / (Double) gmvpop.get("gmv"));
        }

        if (isDomestic == 1) {
            radarMetricItemgmvpop.setLowerLimit(-0.5 + average);
            radarMetricItemgmvpop.setUpperLimit(0.5 + average);
        } else {
            radarMetricItemgmvpop.setLowerLimit(0.0);
            radarMetricItemgmvpop.setUpperLimit(3 * average);
        }
        radarMetricItemList.add(radarMetricItemgmvpop);

        //UV客流人气 cdm_log_ttd_viewspot_bench_traffic_di
        RadarMetricItem radarMetricItemuv = new RadarMetricItem();
        Map<String, Object> uv = cdmLogTtdViewspotBenchTrafficDiDao.queryRadarUV(sightId, dateType, startDate, endDate, vendorIdList, businessType, needSubSight);
        Map<String, Object> uvpop = cdmLogTtdViewspotBenchTrafficDiDao.queryRadarUVPop(sightId, dateType, startDate, endDate, vendorIdList, businessType, needSubSight);
        //uv均值
        Map<String, Object> uvaverage = cdmLogTtdViewspotBenchTrafficDiDao.queryRadarUVAverage(sightId, dateType, startDate, endDate, vendorIdList, businessType, needSubSight, isDomestic);
        Map<String, Object> uvaveragepop = cdmLogTtdViewspotBenchTrafficDiDao.queryRadarUVAveragePop(sightId, dateType, startDate, endDate, vendorIdList, businessType, needSubSight, isDomestic);
        Double averageuv = 0.0;
        if (uvaveragepop.get("uv_average") != null && !uvaveragepop.get("uv_average").equals(0.0) && uvaverage.get("uv_average") != null) {
            averageuv = ((Double) uvaverage.get("uv_average") - (Double) uvaveragepop.get("uv_average")) / (Double) uvaveragepop.get("uv_average");
        }

        radarMetricItemuv.setMetricName("uv");
        if (uvpop.get("uv") != null && !uvpop.get("uv").equals(0.0) && uv.get("uv") != null) {
            radarMetricItemuv.setValue(((Integer) uv.get("uv") - (Double) uvpop.get("uv")) / (Double) uvpop.get("uv"));
        }
        if (isDomestic == 1) {
            radarMetricItemuv.setLowerLimit(-0.65 + averageuv);
            radarMetricItemuv.setUpperLimit(0.65 + averageuv);
        } else {
            radarMetricItemuv.setLowerLimit(-1.5 + averageuv);
            radarMetricItemuv.setUpperLimit(1.5 + averageuv);
        }
        radarMetricItemList.add(radarMetricItemuv);

        //商品力
        GetSightCompetitiveResponseType sightCompetitiveResponseType = competitiveService.getSightCompetitive(new GetSightCompetitiveRequestType(commonFilter, 1));
        RadarMetricItem radarMetricItemCompetitive = new RadarMetricItem();
        radarMetricItemCompetitive.setLowerLimit(0.0);
        radarMetricItemCompetitive.setUpperLimit(11.0);
        radarMetricItemCompetitive.setMetricName("competitive");
        int value = 0;
        for (SightCompetitiveItem item : sightCompetitiveResponseType.getCompetiveItemList()) {
            if (item.isSelfPerformance() != null && item.isSelfPerformance()) {
                value++;
            }
        }
        for (SightCompetitiveItem item : sightCompetitiveResponseType.getCoveredUserGroupList()) {
            if (item.isSelfPerformance() != null && item.isSelfPerformance()) {
                value++;
            }
        }
        radarMetricItemCompetitive.setValue((double) value);
        radarMetricItemList.add(radarMetricItemCompetitive);

        //履约质量 加权缺陷率

        RadarMetricItem radarMetricItemQuality = new RadarMetricItem();
        Map<String, Object> weightedDefectRate = admSevTtdViewspotDefectDetailDfDao.queryRadarweightedDefectRate(queryD, sightId, dateType, startDate, endDate, vendorIdList, businessType, needSubSight);
        Map<String, Object> weightedDefectRateAverage = admSevTtdViewspotDefectDetailDfDao.queryRadarweightedDefectRateAverage(queryD, sightId, dateType, startDate, endDate, vendorIdList, businessType);
        radarMetricItemQuality.setMetricName("weightedDefectRate");
        radarMetricItemQuality.setValue((Double) weightedDefectRate.get("weightedDefectRate"));
        radarMetricItemQuality.setUpperLimit(0.0);
        radarMetricItemQuality.setLowerLimit(2 * (Double) weightedDefectRateAverage.get("weightedDefectRate"));
        radarMetricItemList.add(radarMetricItemQuality);

        //游客体验  点评分 adm_sev_ttd_viewspot_file_index_df
        RadarMetricItem radarMetricItemScore = new RadarMetricItem();
        Map<String, Object> score = admSevTtdViewspotFileIndexDfDao.queryRadarScore(queryD, sightId, dateType, startDate, endDate, vendorIdList, businessType, needSubSight);
        radarMetricItemScore.setMetricName("commentScore");
        radarMetricItemScore.setValue((Double) score.get("score"));
        if (isDomestic == 1) {
            radarMetricItemScore.setLowerLimit(3.4);
            radarMetricItemScore.setUpperLimit(5.0);
        } else {
            radarMetricItemScore.setLowerLimit(4.0);
            radarMetricItemScore.setUpperLimit(5.0);
        }
        radarMetricItemList.add(radarMetricItemScore);

        SightArchivesSummaryTextData textData = new SightArchivesSummaryTextData();
        textData.setGmv((Integer) gmv.get("gmv"));

        textData.setGmvYoy(radarMetricItemgmvpop.getValue());
        textData.setUvYoy(radarMetricItemuv.getValue());

        GetSightCompetitiveResponseType competitiveResponseType = competitiveService.getSightCompetitive(new GetSightCompetitiveRequestType(commonFilter, 1));
        List<String> unCoveredTicketTypeList = new ArrayList<>();
        for (SightCompetitiveItem competitiveItem : competitiveResponseType.getCoveredUserGroupList()) {
            if (competitiveItem.isSelfPerformance() != null && !competitiveItem.isSelfPerformance()) {
                unCoveredTicketTypeList.add(competitiveItem.getName());
            }
        }
        textData.setUncoveredTicketTypeList(unCoveredTicketTypeList);

        List<String> competitivenessDisadvantageList = new ArrayList<>();
        for (SightCompetitiveItem competitiveItem : competitiveResponseType.getCompetiveItemList()) {
            if (competitiveItem.isSelfPerformance() != null && !competitiveItem.isSelfPerformance()) {
                competitivenessDisadvantageList.add(competitiveItem.getName());
            }
        }

        textData.setCompetitivenessDisadvantageList(competitivenessDisadvantageList);
        sightArchivesReportSummaryResponseType.setSummaryTextData(textData);


        sightArchivesReportSummaryResponseType.setRadarMetricList(radarMetricItemList);
        return sightArchivesReportSummaryResponseType;

    }

    @Override
    public GetAiModuleSummaryResponseType getAiModuleSummary(GetAiModuleSummaryRequestType requestType) {
        Integer module = requestType.getModule();
        String messageId = requestType.getMessageId();
        String sessionId = requestType.getSessionId();
        Integer competitorType = requestType.getCompetitorType();
        if (competitorType == null) {
            competitorType = 1;
        }


        if ("off".equals(remoteConfig.getConfigValue("aiSummary"))) {
            GetAiModuleSummaryResponseType aiModuleSummaryResponseType = new GetAiModuleSummaryResponseType();
            aiModuleSummaryResponseType.setStatus(500);
            return aiModuleSummaryResponseType;
        }

        GetAiModuleSummaryResponseType responseType = new GetAiModuleSummaryResponseType();
        if (StringUtils.isBlank(messageId) || StringUtils.isBlank(sessionId)) {
            //首次请求，没有messageId，需要查询各指标数据
            SightArchivesCommonFilter commonFilter = requestType.getCommonFilter();
            String dataJson = "";
            long startTime = System.currentTimeMillis();
            switch (module) {
                case 1:
                    dataJson = getModule1ResponseJson(commonFilter);
                    break;
                case 2:
                    dataJson = getModule2ResponseJson(commonFilter);
                    break;
                case 3:
                    dataJson = getModule3ResponseJson(commonFilter);
                    break;
                case 4:
                    dataJson = getModule4ResponseJson(commonFilter);
                    break;
                case 5:
                    dataJson = getModule5ResponseJson(commonFilter, competitorType);
                    break;
                case 6:
                    dataJson = getModule6ResponseJson(commonFilter);
                    break;
                default:
                    break;
            }
            log.info("ai总结,模块号:{}, 指标数据查询耗时: {} ms", module, System.currentTimeMillis() - startTime);  //NOSONAR

            String queryD = getQueryD();
            AdmPrdTtdCpdTripDimInfoUnifiedOutput sightInfo = admPrdTtdCpdTripDimInfoUnifiedOutputSRDao.querySightInfo(requestType.getCommonFilter().getSightId(), queryD);

            Map<String, Object> map = new HashMap<>();
            map.put("viewspotId", sightInfo.getViewspotId());
            map.put("viewspotName", sightInfo.getViewspotName());
            map.put("metricModuleId", module);
            map.put("locale", UserUtil.getVbkLocale());
            map.put("metricDatas", dataJson);
            String requestJson = MapperUtil.obj2Str(map);

            startTime = System.currentTimeMillis();
            log.info("ai总结触发接口请求参数: {}", requestJson);  //NOSONAR
            String response = post(remoteConfig.getConfigValue("aiSummaryTriggerUrl"), requestJson);
            log.info("ai总结触发接口耗时: {} ms, 生成结果: {}", System.currentTimeMillis() - startTime, response);  //NOSONAR

            Map<String, Object> resultMap = MapperUtil.str2Obj(response, Map.class);
            String newSessionId = String.valueOf(resultMap.get("sessionId"));
            String newMessageId = String.valueOf(resultMap.get("messageId"));
            Integer status = Integer.valueOf(String.valueOf(resultMap.get("status")));

            responseType.setSessionId(newSessionId);
            responseType.setMessageId(newMessageId);
            responseType.setStatus(status);

            return responseType;

        } else {
            //非首次请求，有messageId, 无需查指标数据
            Map<String, Object> map = new HashMap<>();
            map.put("messageId", messageId);
            map.put("sessionId", sessionId);
            long startTime = System.currentTimeMillis();
            log.info("ai总结文案生成接口请求参数: {}", MapperUtil.obj2Str(map));  //NOSONAR
            String response = post(remoteConfig.getConfigValue("aiSummaryStreamUrl"), MapperUtil.obj2Str(map));
            log.info("ai总结文案生成接口耗时: {} ms, 生成结果: {}", System.currentTimeMillis() - startTime, response);  //NOSONAR
            Map<String, Object> resultMap = MapperUtil.str2Obj(response, Map.class);
            Integer genStatus = Integer.valueOf(String.valueOf(resultMap.get("genStatus")));
            Integer status = Integer.valueOf(String.valueOf(resultMap.get("status")));

            List<ContentItem> contentItemList = MapperUtil.str2List(MapperUtil.obj2Str(resultMap.getOrDefault("tagContentItemList", "")), ContentItem.class);
            String completedMessage = String.valueOf(resultMap.getOrDefault("completedMessage", ""));
            responseType.setGenStatus(genStatus);
            responseType.setStatus(status);
            responseType.setTagContentItemList(contentItemList);
            responseType.setCompletedMessage(completedMessage);
            return responseType;
        }

    }

    /**
     * post json请求
     *
     * @param data：请求json
     * @return
     */
    public static String post(String url, String data) {

        HttpPost request = new HttpPost(url);
        // 设置请求参数
        StringEntity entity = new StringEntity(data, "UTF-8");
        request.addHeader("Content-Type", "application/json");
        request.setEntity(entity);

        try {
//            CloseableHttpClient client = HttpClients.createDefault();
            SSLContext sslContext = SSLContexts.custom()
                    .loadTrustMaterial(null, (X509Certificate[] chain, String authType) -> true)
                    .build();
            CloseableHttpClient client = HttpClients.custom()
                    .setSSLContext(sslContext)
                    .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
                    .build();
            CloseableHttpResponse response = client.execute(request);
            if (HttpStatus.SC_OK != response.getStatusLine().getStatusCode()) {
                log.warn(String.format("post %s failed, statuscode=%s", url, response.getStatusLine().getStatusCode()));
                return StringUtils.EMPTY;
            }
            return EntityUtils.toString(response.getEntity(), "utf-8");
        } catch (Exception ex) {
            log.warn(String.format("post %s failed", url), ex);
            return StringUtils.EMPTY;
        }
    }

    private String getModule1ResponseJson(SightArchivesCommonFilter commonFilter) {
//        //指标卡
//        GetSalesMetricCardResponseType metricCardResponseType = salesService.getSalesMetricCard(new GetSalesMetricCardRequestType(commonFilter));
//        //GMV指标，趋势线
//        GetSalesMetricTrendLineResponseType trendLineResponseType = salesService.getSalesMetricTrendLine(new GetSalesMetricTrendLineRequestType(commonFilter, SalesMetricEnumType.GMV.getEnglishName()));
//        //GMV指标，饼图
//        GetSalesMetricPieChartResponseType pieChartResponseType = salesService.getSalesMetricPieChart(new GetSalesMetricPieChartRequestType(commonFilter, SalesMetricEnumType.GMV.getEnglishName()));
//        //GMV指标，票种、供应商维度表格
//        GetSalesMetricRankTableResponseType table1ResponseType = salesService.getSalesMetricRankTable(new GetSalesMetricRankTableRequestType(commonFilter, SalesMetricEnumType.GMV.getEnglishName(),1,1,10));
//        GetSalesMetricRankTableResponseType table2ResponseType = salesService.getSalesMetricRankTable(new GetSalesMetricRankTableRequestType(commonFilter, SalesMetricEnumType.GMV.getEnglishName(),2,1,10));


        ThreadPoolExecutor threadPoolExecutor = appConfiguration.getSightArchivesExecutor();

        // 使用 CompletableFuture 包裝每個異步調用
        CompletableFuture<GetSalesMetricCardResponseType> metricCardFuture = CompletableFuture.supplyAsync(
                () -> salesService.getSalesMetricCard(new GetSalesMetricCardRequestType(commonFilter), true), threadPoolExecutor
        ).exceptionally(ex -> {
            log.error("getSalesMetricCard error", ex);
            return new GetSalesMetricCardResponseType();
        });
        CompletableFuture<GetSalesMetricTrendLineResponseType> trendLineFuture = CompletableFuture.supplyAsync(
                () -> salesService.getSalesMetricTrendLine(new GetSalesMetricTrendLineRequestType(commonFilter, SalesMetricEnumType.GMV.getEnglishName())), threadPoolExecutor
        ).exceptionally(ex -> {
            log.error("getSalesMetricTrendLine error", ex);
            return new GetSalesMetricTrendLineResponseType();

        });
        CompletableFuture<GetSalesMetricPieChartResponseType> pieChartFuture = CompletableFuture.supplyAsync(
                () -> salesService.getSalesMetricPieChart(new GetSalesMetricPieChartRequestType(commonFilter, SalesMetricEnumType.GMV.getEnglishName())), threadPoolExecutor
        ).exceptionally(ex -> {
            log.error("getSalesMetricPieChart error", ex);
            return new GetSalesMetricPieChartResponseType();
        });
        CompletableFuture<GetSalesMetricRankTableResponseType> table1Future = CompletableFuture.supplyAsync(
                () -> salesService.getSalesMetricRankTable(new GetSalesMetricRankTableRequestType(commonFilter, SalesMetricEnumType.GMV.getEnglishName(), 1, 1, 10)), threadPoolExecutor
        ).exceptionally(ex -> {
            log.error("getSalesMetricRankTable error", ex);
            return new GetSalesMetricRankTableResponseType();
        });
        CompletableFuture<GetSalesMetricRankTableResponseType> table2Future = CompletableFuture.supplyAsync(
                () -> salesService.getSalesMetricRankTable(new GetSalesMetricRankTableRequestType(commonFilter, SalesMetricEnumType.GMV.getEnglishName(), 2, 1, 10)), threadPoolExecutor
        ).exceptionally(ex -> {
            log.error("getSalesMetricRankTable error", ex);
            return new GetSalesMetricRankTableResponseType();
        });

        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                metricCardFuture,
                trendLineFuture,
                pieChartFuture,
                table1Future,
                table2Future
        );

        allFutures.join(); // 阻塞直到所有任務完成

        GetSalesMetricCardResponseType metricCardResponseType = metricCardFuture.join();
        GetSalesMetricTrendLineResponseType trendLineResponseType = trendLineFuture.join();
        GetSalesMetricPieChartResponseType pieChartResponseType = pieChartFuture.join();
        GetSalesMetricRankTableResponseType table1ResponseType = table1Future.join();
        GetSalesMetricRankTableResponseType table2ResponseType = table2Future.join();


        Map<String, Object> map = new HashMap<>();
        map.put("metricList", metricCardResponseType.getMetricList());
        map.put("dateList", trendLineResponseType.getDateList());
        map.put("pieChartList", pieChartResponseType.getPieChartList());
        map.put("ticketTable", table1ResponseType.getTableRowList());
        map.put("vendorTable", table2ResponseType.getTableRowList());

        return MapperUtil.obj2Str(map);

    }

    private String getModule2ResponseJson(SightArchivesCommonFilter commonFilter) {

//        //饼图&漏斗
//        GetFlowMetricResponseType metricResponseType = flowService.getFlowMetric(new GetFlowMetricRequestType(commonFilter));
//        //趋势线
//        GetFlowMetricTrendLineResponseType trendLineResponseType = flowService.getFlowMetricTrendLine(new GetFlowMetricTrendLineRequestType(commonFilter));

        ThreadPoolExecutor threadPoolExecutor = appConfiguration.getSightArchivesExecutor();

        CompletableFuture<GetFlowMetricResponseType> metricFuture = CompletableFuture.supplyAsync(
                () -> flowService.getFlowMetric(new GetFlowMetricRequestType(commonFilter)), threadPoolExecutor
        ).exceptionally(ex -> {
                    log.error("getFlowMetric error", ex);
                    return new GetFlowMetricResponseType();
                }
        );
        CompletableFuture<GetFlowMetricTrendLineResponseType> trendLineFuture = CompletableFuture.supplyAsync(
                () -> flowService.getFlowMetricTrendLine(new GetFlowMetricTrendLineRequestType(commonFilter)), threadPoolExecutor
        ).exceptionally(ex -> {
                    log.error("getFlowMetricTrendLine error", ex);
                    return new GetFlowMetricTrendLineResponseType();
                }
        );

        CompletableFuture<Void> allFlowFutures = CompletableFuture.allOf(metricFuture, trendLineFuture);
        allFlowFutures.join();

        GetFlowMetricResponseType metricResponseType = metricFuture.join();
        GetFlowMetricTrendLineResponseType trendLineResponseType = trendLineFuture.join();


        FlowConversionFunnel flowConversionFunnel = metricResponseType.getFlowConversionFunnel();
        List<FlowSource> flowSourceList = metricResponseType.getFlowSourceList();
        List<SightArchivesFlowMetricTrendLineItem> dateList = trendLineResponseType.getDateList();
        Map<String, Object> map = new HashMap<>();
        map.put("flowConversionFunnel", flowConversionFunnel);
        map.put("flowSourceList", flowSourceList);
        map.put("dateList", dateList);


        return MapperUtil.obj2Str(map);
    }


    private String getModule3ResponseJson(SightArchivesCommonFilter commonFilter) {
//        //提前预订天数&用户年龄分布
//        GetUserProfileHistogramResponseType histogramResponseType = userProfileService.getUserProfileHistogram(new GetUserProfileHistogramRequestType(commonFilter));
//        //客户等级&用户类型&本异地&境内外用户分布（饼图）：
//        GetUserProfilePieChartResponseType pieChartResponseType = userProfileService.getUserProfilePieChart(new GetUserProfilePieChartRequestType(commonFilter));
//        //用户画像 - 客源城市分布（地图&表格）
//        GetUserResidenceDistributionResponseType distributionResponseType = userProfileService.getUserResidenceDistribution(new GetUserResidenceDistributionRequestType(commonFilter));
//        //用户搜索偏好
//        GetUserSearchPreferenceResponseType searchPreferenceResponseType = userProfileService.getUserSearchPreference(new GetUserSearchPreferenceRequestType(commonFilter));

        ThreadPoolExecutor threadPoolExecutor = appConfiguration.getSightArchivesExecutor();

        CompletableFuture<GetUserProfileHistogramResponseType> histogramFuture = CompletableFuture.supplyAsync(
                () -> userProfileService.getUserProfileHistogram(new GetUserProfileHistogramRequestType(commonFilter)), threadPoolExecutor
        ).exceptionally(ex -> {
                    log.error("getUserProfileHistogram error", ex);
                    return new GetUserProfileHistogramResponseType();
                }
        );
        CompletableFuture<GetUserProfilePieChartResponseType> pieChartFuture = CompletableFuture.supplyAsync(
                () -> userProfileService.getUserProfilePieChart(new GetUserProfilePieChartRequestType(commonFilter)), threadPoolExecutor
        ).exceptionally(ex -> {
                    log.error("getUserProfilePieChart error", ex);
                    return new GetUserProfilePieChartResponseType();
                }
        );
        CompletableFuture<GetUserResidenceDistributionResponseType> distributionFuture = CompletableFuture.supplyAsync(
                () -> userProfileService.getUserResidenceDistribution(new GetUserResidenceDistributionRequestType(commonFilter)), threadPoolExecutor
        ).exceptionally(ex -> {
                    log.error("getUserResidenceDistribution error", ex);
                    return new GetUserResidenceDistributionResponseType();
                }
        );
        CompletableFuture<GetUserSearchPreferenceResponseType> searchPreferenceFuture = CompletableFuture.supplyAsync(
                () -> userProfileService.getUserSearchPreference(new GetUserSearchPreferenceRequestType(commonFilter)), threadPoolExecutor
        ).exceptionally(ex -> {
                    log.error("getUserSearchPreference error", ex);
                    return new GetUserSearchPreferenceResponseType();
                }
        );
        CompletableFuture<Void> allUserProfileFutures = CompletableFuture.allOf(
                histogramFuture,
                pieChartFuture,
                distributionFuture,
                searchPreferenceFuture
        );
        allUserProfileFutures.join();
        GetUserProfileHistogramResponseType histogramResponseType = histogramFuture.join();
        GetUserProfilePieChartResponseType pieChartResponseType = pieChartFuture.join();
        GetUserResidenceDistributionResponseType distributionResponseType = distributionFuture.join();
        GetUserSearchPreferenceResponseType searchPreferenceResponseType = searchPreferenceFuture.join();


        Map<String, Object> map = new HashMap<>();
        map.put("advanceBookingDayList", histogramResponseType.getAdvanceBookingDayList());
        map.put("userAgeGroupList", histogramResponseType.getUserAgeGroupList());
        map.put("pieChartList", pieChartResponseType.getPieChartList());
        map.put("cityList", distributionResponseType.getCityList());
        map.put("provinceList", distributionResponseType.getProvinceList());
        map.put("countryList", distributionResponseType.getCountryList());
        map.put("searchKeyList", searchPreferenceResponseType.getSearchKeyList());

        return MapperUtil.obj2Str(map);
    }

    private String getModule4ResponseJson(SightArchivesCommonFilter commonFilter) {

//        GetFulfillmentQualityMetricResponseType qualityMetricResponseType = qualityService.getFulfillmentQualityMetric(new GetFulfillmentQualityMetricRequestType(commonFilter));
//        String defectName = CollectionUtils.isNotEmpty(qualityMetricResponseType.getPieChart())?qualityMetricResponseType.getPieChart().get(0).getName():"";
//        GetFulfillmentQualityTableResponseType qualityTableResponseType = qualityService.getFulfillmentQualityTable(new GetFulfillmentQualityTableRequestType(commonFilter,defectName,1,10));
//
//        GetCommentMetricResponseType commentMetricResponseType = qualityService.getCommentMetric(new GetCommentMetricRequestType(commonFilter));
//        GetServiceMetricResponseType serviceMetricResponseType = qualityService.getServiceMetric(new GetServiceMetricRequestType(commonFilter));
//
//        GetComplaintMetricResponseType complaintMetricResponseType = qualityService.getComplaintMetric(new GetComplaintMetricRequestType(commonFilter));
//        GetVendorQualityTableResponseType vendorQualityTableResponseType = qualityService.getVendorQualityTable(new GetVendorQualityTableRequestType(commonFilter));

        ThreadPoolExecutor threadPoolExecutor = appConfiguration.getSightArchivesExecutor();

        CompletableFuture<GetFulfillmentQualityMetricResponseType> qualityMetricFuture = CompletableFuture.supplyAsync(
                () -> qualityService.getFulfillmentQualityMetric(new GetFulfillmentQualityMetricRequestType(commonFilter)), threadPoolExecutor
        ).exceptionally(ex -> {
                    log.error("getFulfillmentQualityMetric error", ex);
                    return new GetFulfillmentQualityMetricResponseType();
                }
        );
        CompletableFuture<GetFulfillmentQualityTableResponseType> qualityTableFuture = CompletableFuture.supplyAsync(
                () -> qualityService.getFulfillmentQualityTable(new GetFulfillmentQualityTableRequestType(commonFilter, "", 1, 10)), threadPoolExecutor
        ).exceptionally(ex -> {
                    log.error("getFulfillmentQualityTable error", ex);
                    return new GetFulfillmentQualityTableResponseType();
                }
        );
        CompletableFuture<GetCommentMetricResponseType> commentMetricFuture = CompletableFuture.supplyAsync(
                () -> qualityService.getCommentMetric(new GetCommentMetricRequestType(commonFilter)), threadPoolExecutor
        ).exceptionally(ex -> {
                    log.error("getCommentMetric error", ex);
                    return new GetCommentMetricResponseType();
                }
        );
        CompletableFuture<GetServiceMetricResponseType> serviceMetricFuture = CompletableFuture.supplyAsync(
                () -> qualityService.getServiceMetric(new GetServiceMetricRequestType(commonFilter)), threadPoolExecutor
        ).exceptionally(ex -> {
                    log.error("getServiceMetric error", ex);
                    return new GetServiceMetricResponseType();
                }
        );
        CompletableFuture<GetComplaintMetricResponseType> complaintMetricFuture = CompletableFuture.supplyAsync(
                () -> qualityService.getComplaintMetric(new GetComplaintMetricRequestType(commonFilter)), threadPoolExecutor
        ).exceptionally(ex -> {
                    log.error("getComplaintMetric error", ex);
                    return new GetComplaintMetricResponseType();
                }
        );
        CompletableFuture<GetVendorQualityTableResponseType> vendorQualityTableFuture = CompletableFuture.supplyAsync(
                () -> qualityService.getVendorQualityTable(new GetVendorQualityTableRequestType(commonFilter, null, null)), threadPoolExecutor
        ).exceptionally(ex -> {
                    log.error("getVendorQualityTable error", ex);
                    return new GetVendorQualityTableResponseType();
                }
        );
        CompletableFuture<Void> allQualityFutures = CompletableFuture.allOf(
                qualityMetricFuture,
                qualityTableFuture,
                commentMetricFuture,
                serviceMetricFuture,
                complaintMetricFuture,
                vendorQualityTableFuture
        );
        allQualityFutures.join();
        GetFulfillmentQualityMetricResponseType qualityMetricResponseType = qualityMetricFuture.join();
        GetFulfillmentQualityTableResponseType qualityTableResponseType = qualityTableFuture.join();
        GetCommentMetricResponseType commentMetricResponseType = commentMetricFuture.join();
        GetServiceMetricResponseType serviceMetricResponseType = serviceMetricFuture.join();
        GetComplaintMetricResponseType complaintMetricResponseType = complaintMetricFuture.join();
        GetVendorQualityTableResponseType vendorQualityTableResponseType = vendorQualityTableFuture.join();


        Map<String, Object> map = new HashMap<>();
        Map<String, Object> defectiveMetric = new HashMap<>();
        defectiveMetric.put("weightedDefectCount", qualityMetricResponseType.getWeightedDefectCount());
        defectiveMetric.put("weightedDefectRate", qualityMetricResponseType.getWeightedDefectRate());
        defectiveMetric.put("weightedDefectRatePop", qualityMetricResponseType.getWeightedDefectRatePop());
        defectiveMetric.put("weightedDefectRateYoy", qualityMetricResponseType.getWeightedDefectRateYoy());
        map.put("defectiveMetric", defectiveMetric);
        map.put("pieChart", qualityMetricResponseType.getPieChart());
        map.put("defectiveProductList", qualityTableResponseType.getDefectiveProductList());

        map.put("currentCommentScore", commentMetricResponseType.getCurrentCommentScore());
        map.put("commentTrendLine", commentMetricResponseType.getCommentTrendLine());

        map.put("positiveCommentList", commentMetricResponseType.getPositiveCommentList());
        map.put("negativeCommentList", commentMetricResponseType.getNegativeCommentList());

        Map<String, Object> serviceMetric = new HashMap<>();
        serviceMetric.put("cpoValue", serviceMetricResponseType.getCpoValue());
        serviceMetric.put("cpoPop", serviceMetricResponseType.getCpoPop());
        serviceMetric.put("cpoYoy", serviceMetricResponseType.getCpoYoy());
        serviceMetric.put("telServiceMinutes", serviceMetricResponseType.getTelServiceMinutes());
        serviceMetric.put("totalConsultCount", serviceMetricResponseType.getTotalConsultCount());
        serviceMetric.put("preSaleConsultCount", serviceMetricResponseType.getPreSaleConsultCount());
        serviceMetric.put("postSaleConsultCount", serviceMetricResponseType.getPostSaleConsultCount());
        serviceMetric.put("serviceValueAmount", serviceMetricResponseType.getServiceValueAmount());
        map.put("serviceMetric", serviceMetric);
        map.put("consultQuestionList", serviceMetricResponseType.getConsultQuestionList());

        Map<String, Object> complaintMetric = new HashMap<>();
        complaintMetric.put("complaintCount", complaintMetricResponseType.getComplaintCount());
        complaintMetric.put("complaintRate", complaintMetricResponseType.getComplaintRate());
        complaintMetric.put("complaintRatePop", complaintMetricResponseType.getComplaintRatePop());
        complaintMetric.put("complaintRateYoy", complaintMetricResponseType.getComplaintRateYoy());
        map.put("complaintMetric", complaintMetric);
        map.put("complaintQuestionList", complaintMetricResponseType.getComplaintQuestionList());

        map.put("vendorList", vendorQualityTableResponseType.getVendorList());


        return MapperUtil.obj2Str(map);
    }

    private String getModule5ResponseJson(SightArchivesCommonFilter commonFilter, Integer competitorType) {


//        GetSightComparisonResponseType sightComparisonResponseType = competitiveService.getSightComparison(new GetSightComparisonRequestType(commonFilter,1));
//        GetSightCompetitiveResponseType sightCompetitiveResponseType = competitiveService.getSightCompetitive(new GetSightCompetitiveRequestType(commonFilter,1));
//        GetUncoveredTicketTypeResponseType uncoveredTicketTypeResponseType = competitiveService.getUncoveredTicketType(new GetUncoveredTicketTypeRequestType(commonFilter,1,1,10));
//        GetCoreTicketTypeCompetitiveResponseType coreTicketTypeCompetitiveResponseType = competitiveService.getCoreTicketTypeCompetitive(new GetCoreTicketTypeCompetitiveRequestType(commonFilter,1,1,10));

        ThreadPoolExecutor threadPoolExecutor = appConfiguration.getSightArchivesExecutor();

        CompletableFuture<GetSightComparisonResponseType> sightComparisonFuture = CompletableFuture.supplyAsync(
                () -> competitiveService.getSightComparison(new GetSightComparisonRequestType(commonFilter, competitorType)), threadPoolExecutor
        ).exceptionally(ex -> {
                    log.error("getSightComparison error", ex);
                    return new GetSightComparisonResponseType();
                }
        );
        CompletableFuture<GetSightCompetitiveResponseType> sightCompetitiveFuture = CompletableFuture.supplyAsync(
                () -> competitiveService.getSightCompetitive(new GetSightCompetitiveRequestType(commonFilter, competitorType)), threadPoolExecutor
        ).exceptionally(ex -> {
                    log.error("getSightCompetitive error", ex);
                    return new GetSightCompetitiveResponseType();
                }
        );
        CompletableFuture<GetUncoveredTicketTypeResponseType> uncoveredTicketTypeFuture = CompletableFuture.supplyAsync(
                () -> competitiveService.getUncoveredTicketType(new GetUncoveredTicketTypeRequestType(commonFilter, competitorType, 1, 10)), threadPoolExecutor
        ).exceptionally(ex -> {
                    log.error("getUncoveredTicketType error", ex);
                    return new GetUncoveredTicketTypeResponseType();
                }
        );
        CompletableFuture<GetCoreTicketTypeCompetitiveResponseType> coreTicketTypeCompetitiveFuture = CompletableFuture.supplyAsync(
                () -> competitiveService.getCoreTicketTypeCompetitive(new GetCoreTicketTypeCompetitiveRequestType(commonFilter, 1, 1, 10)), threadPoolExecutor
        ).exceptionally(ex -> {
                    log.error("getCoreTicketTypeCompetitive error", ex);
                    return new GetCoreTicketTypeCompetitiveResponseType();
                }
        );
        CompletableFuture<Void> allCompetitiveFutures = CompletableFuture.allOf(
                sightComparisonFuture,
                sightCompetitiveFuture,
                uncoveredTicketTypeFuture,
                coreTicketTypeCompetitiveFuture
        );
        allCompetitiveFutures.join();
        GetSightComparisonResponseType sightComparisonResponseType = sightComparisonFuture.join();
        GetSightCompetitiveResponseType sightCompetitiveResponseType = sightCompetitiveFuture.join();
        GetUncoveredTicketTypeResponseType uncoveredTicketTypeResponseType = uncoveredTicketTypeFuture.join();
        GetCoreTicketTypeCompetitiveResponseType coreTicketTypeCompetitiveResponseType = coreTicketTypeCompetitiveFuture.join();


        Map<String, Object> map = new HashMap<>();
        map.put("rankList", sightComparisonResponseType.getRankList());
        map.put("coveredUserGroupList", sightCompetitiveResponseType.getCoveredUserGroupList());
        map.put("competiveItemList", sightCompetitiveResponseType.getCompetiveItemList());
        map.put("unCoveredTicketTypeList", uncoveredTicketTypeResponseType.getUnCoveredTicketTypeList());
        map.put("coreTicketTypeCompetitiveList", coreTicketTypeCompetitiveResponseType.getCoreTicketTypeCompetitiveList());

        return MapperUtil.obj2Str(map);
    }

    private String getModule6ResponseJson(SightArchivesCommonFilter commonFilter) {

        GetLocationHeatForecastResponseType locationHeatForecastResponseType = marketService.getLocationHeatForecast(new GetLocationHeatForecastRequestType(commonFilter));

        Map<String, Object> map = new HashMap<>();
        map.put("heatForecastTrendLine", locationHeatForecastResponseType.getHeatForecastTrendLine());

        return MapperUtil.obj2Str(map);
    }

    public String getQueryD() {
        String queryD = "";
        try {
            queryD = businessDashboardUpdatetimeDao.getSightArchivesUpdateTime();
        } catch (SQLException e) {
            log.error("getSightArchivesUpdateTime error", e);
        }
        return queryD;
    }

}
