package com.ctrip.tour.business.dashboard.schedule

import com.ctrip.tour.business.dashboard.tktBusiness.bean.ContractEventAggTimeBean
import com.ctrip.tour.business.dashboard.tktBusiness.biz.PushMessageToTaskBiz
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardNoticeInfoDao
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardNoticeInfo
import com.ctrip.tour.business.dashboard.tktBusiness.schedule.ContractEventSchedule
import com.ctrip.tour.business.dashboard.utils.MapperUtil
import spock.lang.Specification

import java.time.format.DateTimeFormatter

class ContractEventScheduleTest extends Specification {
    // mock
    def noticeInfoDao = Mock(BusinessDashboardNoticeInfoDao)
    def remoteConfig = Mock(RemoteConfig)
    def messageToTaskBiz = Mock(PushMessageToTaskBiz)
    def formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
    def schedule = new ContractEventSchedule(noticeInfoDao: noticeInfoDao, remoteConfig: remoteConfig, messageToTaskBiz: messageToTaskBiz, formatter: formatter)


    // call
//    def noticeInfoDao = new BusinessDashboardNoticeInfoDao()
//    def remoteConfig = Mock(RemoteConfig)
//    def messageToTaskBiz = new PushMessageToTaskBizImpl(remoteConfig: remoteConfig, pushClient: TourRightsServiceClient.getInstance())
//    def formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
//    def schedule = new ContractEventSchedule(noticeInfoDao: noticeInfoDao, remoteConfig: remoteConfig, messageToTaskBiz: messageToTaskBiz, formatter: formatter)


    def "test contractEventJob"() {
        given: "mock"
        remoteConfig.getConfigValue("isPushSceneMessage") >> true
        remoteConfig.getConfigValue("hostName") >> "ws.uploadfile.fx.fws.qa.nt.ctripcorp.com"
        remoteConfig.getConfigValue("fileChannel") >> "tour_business_dashboard"
        remoteConfig.getConfigValue("scheduleConfig") >> "[{\"sceneType\":\"PRODUCT_INFO_RECTIFICATION\",\"isSplitScene\":true,\"className\":\"com.ctrip.tour.business.dashboard.bean.ProductInfoRecificationBean\",\"splitType\":1,\"splitSceneMap\":{\"supplier\":{\"sceneType\":\"PRODUCT_INFO_RECTIFICATION_SUPPLIER_DELIVERY\",\"className\":\"com.ctrip.tour.business.dashboard.bean.ProductInfoRecificationBean\",\"messageType\":1,\"title\":\"供应商产品信息整改\",\"taskType\":\"TASK_SUPPLIER_REWARD_PUNISHMENT_01\",\"taskName\":\"商品质量信息整改\",\"eventType\":\"EVENT_SUPPLIER_REWARD_PUNISHMENT_01\",\"contractEventAggTimeBean\":{\"endTime\":9,\"interval\":1,\"endType\":1},\"serviceNumInfo\":{\"contentType\":1,\"length\":4000,\"templateStr\":\"您有%s家供应商产品信息需整改，可点击查看详情\",\"isplace\":true,\"templateList\":[\"count\"]},\"detailInfo\":{\"contentType\":2,\"length\":4000,\"templateStr\":\"您负责的产品涉嫌违规将被处理，请尽快整改，如有异议，请在最晚整改完成时间前联系供应商及时申诉，逾期不予受理，明细如下：\",\"isplace\":false,\"headers\":{\"供应商\":[\"vendorId\",\"vendorName\"],\"违规原因\":[\"problemType\"],\"产品\":[\"productId\",\"productName\",\"productUrl\"],\"问题描述\":[\"problemDesc\"],\"最晚整改完成时间\":[\"rectificationDeadline\"],\"申诉链接\":[\"problemUrl\"]},\"hyperLinks\":[{\"type\":1,\"apparentIndex\":2,\"head\":\"产品\"},{\"type\":2,\"custom\":\"点击申诉\",\"head\":\"申诉链接\"}]}},\"business\":{\"messageType\":1,\"sceneType\":\"PRODUCT_INFO_RECTIFICATION_BUSINESS_DELIVERY\",\"className\":\"com.ctrip.tour.business.dashboard.bean.ProductInfoRecificationBean\",\"title\":\"产品信息整改\",\"taskType\":\"TASK_SUPPLIER_REWARD_PUNISHMENT_01\",\"taskName\":\"商品质量信息整改\",\"eventType\":\"EVENT_SUPPLIER_REWARD_PUNISHMENT_04\",\"contractEventAggTimeBean\":{\"endTime\":9,\"interval\":1,\"endType\":1},\"serviceNumInfo\":{\"contentType\":1,\"length\":4000,\"templateStr\":\"您有%s家供应商产品信息需整改，可点击查看详情\",\"isplace\":true,\"templateList\":[\"count\"]},\"detailInfo\":{\"contentType\":2,\"length\":4000,\"templateStr\":\"您负责的产品涉嫌违规将被处理，请尽快整改，如有异议，请在最晚整改完成时间前联系供应商及时申诉，逾期不予受理，明细如下：\",\"isplace\":false,\"headers\":{\"供应商\":[\"vendorId\",\"vendorName\"],\"违规原因\":[\"problemType\"],\"产品\":[\"productId\",\"productName\",\"productUrl\"],\"问题描述\":[\"problemDesc\"],\"最晚整改完成时间\":[\"rectificationDeadline\"],\"申诉链接\":[\"problemUrl\"]},\"hyperLinks\":[{\"type\":1,\"apparentIndex\":2,\"head\":\"产品\"},{\"type\":2,\"custom\":\"点击申诉\",\"head\":\"申诉链接\"}]}}}},{\"sceneType\":\"PRODUCT_INFO_APPEAL_PASSED\",\"isSplitScene\":true,\"className\":\"com.ctrip.tour.business.dashboard.bean.ProductInfoAppealPassedBean\",\"splitType\":1,\"splitSceneMap\":{\"supplier\":{\"sceneType\":\"PRODUCT_INFO_APPEAL_PASSED_SUPPLIER_DELIVERY\",\"className\":\"com.ctrip.tour.business.dashboard.bean.ProductInfoAppealPassedBean\",\"messageType\":1,\"title\":\"供应商产品信息申诉通过\",\"taskType\":\"TASK_SUPPLIER_REWARD_PUNISHMENT_02\",\"taskName\":\"商品质量信息申诉\",\"eventType\":\"EVENT_SUPPLIER_REWARD_PUNISHMENT_02\",\"contractEventAggTimeBean\":{\"endTime\":9,\"interval\":1,\"endType\":1},\"serviceNumInfo\":{\"contentType\":1,\"length\":4000,\"templateStr\":\"您有%s家供应商产品信息整改申诉通过，可点击查看详情\",\"isplace\":true,\"templateList\":[\"count\"]},\"detailInfo\":{\"contentType\":2,\"length\":4000,\"templateStr\":\"您名下供应商产品信息申诉已通过，如供应商重新编辑产品，该产品将从白名单中剔除，重新触发数据监控\",\"isplace\":false,\"headers\":{\"供应商\":[\"vendorId\",\"vendorName\"],\"产品\":[\"productId\",\"productName\"]}}},\"business\":{\"messageType\":1,\"sceneType\":\"PRODUCT_INFO_APPEAL_PASSED_BUSINESS_DELIVERY\",\"className\":\"com.ctrip.tour.business.dashboard.bean.ProductInfoAppealPassedBean\",\"title\":\"产品信息申诉通过\",\"taskType\":\"TASK_SUPPLIER_REWARD_PUNISHMENT_02\",\"taskName\":\"商品质量信息申诉\",\"eventType\":\"EVENT_SUPPLIER_REWARD_PUNISHMENT_05\",\"contractEventAggTimeBean\":{\"endTime\":9,\"interval\":1,\"endType\":1},\"serviceNumInfo\":{\"contentType\":1,\"length\":4000,\"templateStr\":\"您有%s家供应商产品信息整改申诉通过，可点击查看详情\",\"isplace\":true,\"templateList\":[\"count\"]},\"detailInfo\":{\"contentType\":2,\"length\":4000,\"templateStr\":\"您名下产品信息申诉已通过，如果您重新编辑产品，该产品将从白名单中剔除，重新触发数据监控\",\"isplace\":false,\"headers\":{\"供应商\":[\"vendorId\",\"vendorName\"],\"产品\":[\"productId\",\"productName\"]}}}}},{\"sceneType\":\"PRODUCT_INFO_APPEAL_DENIED\",\"isSplitScene\":true,\"className\":\"com.ctrip.tour.business.dashboard.bean.ProductInfoAppealDeniedBean\",\"splitType\":1,\"splitSceneMap\":{\"supplier\":{\"sceneType\":\"PRODUCT_INFO_APPEAL_DENIED_SUPPLIER_DELIVERY\",\"className\":\"com.ctrip.tour.business.dashboard.bean.ProductInfoAppealDeniedBean\",\"messageType\":1,\"title\":\"供应商产品信息申诉驳回\",\"taskType\":\"TASK_SUPPLIER_REWARD_PUNISHMENT_02\",\"taskName\":\"商品质量信息申诉\",\"eventType\":\"EVENT_SUPPLIER_REWARD_PUNISHMENT_03\",\"contractEventAggTimeBean\":{\"endTime\":9,\"interval\":1,\"endType\":1},\"serviceNumInfo\":{\"contentType\":1,\"length\":4000,\"templateStr\":\"您有%s家供应商产品信息整改申诉驳回，可点击查看详情\",\"isplace\":true,\"templateList\":[\"count\"]},\"detailInfo\":{\"contentType\":2,\"length\":4000,\"templateStr\":\"您名下供应商产品信息申诉已驳回，请提醒供应商在改正期限内修改完成\",\"isplace\":false,\"headers\":{\"供应商\":[\"vendorId\",\"vendorName\"],\"产品\":[\"productId\",\"productName\"],\"驳回理由\":[\"rejectReason\"],\"改正期限\":[\"rectificationDeadline\"]}}},\"business\":{\"messageType\":2,\"sceneType\":\"PRODUCT_INFO_APPEAL_DENIED_BUSINESS_DELIVERY\",\"className\":\"com.ctrip.tour.business.dashboard.bean.ProductInfoAppealDeniedBean\",\"title\":\"产品信息申诉驳回\",\"taskType\":\"TASK_SUPPLIER_REWARD_PUNISHMENT_02\",\"taskName\":\"商品质量信息申诉\",\"eventType\":\"EVENT_SUPPLIER_REWARD_PUNISHMENT_06\",\"serviceNumInfo\":{\"contentType\":1,\"length\":4000,\"templateStr\":\"产品：%s<br/><br/>改正期限：%s<br/><br/>驳回原因：%s\",\"isplace\":true,\"templateList\":[\"product[ID+Name]\",\"changeTimeLimit\",\"rejectReason\"]},\"detailInfo\":{\"contentType\":1,\"length\":4000,\"templateStr\":\"您名下产品信息申诉已驳回，请提醒供应商在改正期限内修改完成<br/><br/>产品：%s<br/><br/>改正期限：%s<br/><br/>驳回原因：%s\",\"isplace\":true,\"templateList\":[\"product[ID+Name]\",\"changeTimeLimit\",\"rejectReason\"]}}}}]"
        noticeInfoDao.getSceneDataList(_,_,_) >> MapperUtil.str2List("[{\"message\":\"[{\\\"vendorId\\\":\\\"2\\\",\\\"vendorName\\\":\\\"供应商名字\\\",\\\"productId\\\":1,\\\"productName\\\":\\\"八音盒真品陈列馆门票成人票\\\"}]\",\"receiver\":\"S20736\"},{\"message\":\"[{\\\"vendorId\\\":\\\"2\\\",\\\"vendorName\\\":\\\"供应商名字\\\",\\\"productId\\\":1,\\\"productName\\\":\\\"八音盒真品陈列馆门票成人票\\\"}]\",\"receiver\":\"TR024594\"},{\"message\":\"[{\\\"vendorId\\\":\\\"2\\\",\\\"vendorName\\\":\\\"供应商名字\\\",\\\"productId\\\":1,\\\"productName\\\":\\\"八音盒真品陈列馆门票成人票\\\"}]\",\"receiver\":\"S47352\"},{\"message\":\"[{\\\"vendorId\\\":\\\"2\\\",\\\"vendorName\\\":\\\"供应商名字\\\",\\\"productId\\\":1,\\\"productName\\\":\\\"八音盒真品陈列馆门票成人票\\\"}]\",\"receiver\":\"TR040277\"},{\"message\":\"[{\\\"vendorId\\\":\\\"2\\\",\\\"vendorName\\\":\\\"供应商名字\\\",\\\"productId\\\":1,\\\"productName\\\":\\\"八音盒真品陈列馆门票成人票\\\"}]\",\"receiver\":\"S58020\"},{\"message\":\"[{\\\"vendorId\\\":\\\"2\\\",\\\"vendorName\\\":\\\"供应商名字\\\",\\\"productId\\\":1,\\\"productName\\\":\\\"八音盒真品陈列馆门票成人票\\\"}]\",\"receiver\":\"S00155\"}]", BusinessDashboardNoticeInfo.class)

        expect: "call & verify"
        schedule.contractEventJob()
    }


    def "getAggTimeRange"(){
        given: "mock param"
        def contractEventAggTimeBean = MapperUtil.str2Obj("{\"startTime\":9,\"interval\":24}", ContractEventAggTimeBean.class)

        when: "call"
        def resp = schedule.getAggTimeRange(contractEventAggTimeBean)

        then: "verify"
        with(resp){
            resp.get("start") != null
            resp.get("end") != null
        }
    }

    def "getSceneDataMap"(){
        given: "mock param"
        def messageType = 1
        def SceneType = "PRODUCT_INFO_RECTIFICATION_SUPPLIER_DELIVERY"
        def contractEventAggTimeBean = MapperUtil.str2Obj("{\"startTime\":9,\"interval\":24}", ContractEventAggTimeBean.class)

        and: "mock dal"
        noticeInfoDao.getSceneDataList(_,_,_) >> MapperUtil.str2List("[{\"message\":\"[{\\\"productId\\\":67980372,\\\"productName\\\":\\\"天津野生动物园门票优待票(儿童)\\\",\\\"problemType\\\":\\\"轮播二图非纯图\\\",\\\"rectificationDeadline\\\":\\\"2024-07-15\\\",\\\"problemUrl\\\":\\\"http://vendor.package.ctripcorp.com/vbk/detector/problemList?type=ttd&problemId=2543353\\\"}]\",\"receiver\":\"TR038578\"}]", BusinessDashboardNoticeInfo.class)

        when: "call"
        def resp = schedule.getSceneDataMap(messageType, SceneType, contractEventAggTimeBean)

        then: "verify"
        with(resp){
            resp.size() == 1
        }
    }
}
