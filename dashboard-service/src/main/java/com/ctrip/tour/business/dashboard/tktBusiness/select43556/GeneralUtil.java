package com.ctrip.tour.business.dashboard.tktBusiness.select43556;

import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/29
 */
public class GeneralUtil {

    //判断一个double型是否为0
    public static Boolean isZero(Double x) {
        return Math.abs(x) < 1e-8;
    }

    //判断一次除法操作是否合法
    public static Boolean isValidDivide(Double fenzi,
                                        Double fenmu){
        return fenzi != null && fenmu != null && !GeneralUtil.isZero(fenmu);
    }

    //判断一次减法是否合法
    public static Boolean isValidMinus(Double leftnum,
                                        Double rightnum) {
        return leftnum != null && rightnum != null;
    }

    //输出复合计算结果
    public static Double getComplexResult(Double leftnum, Double rightnum, String operator) {
        Double result = null;
        switch (operator) {
            case "/":
                if (isValidDivide(leftnum, rightnum)) {
                    result = leftnum / rightnum;
                }
                break;
            case "-":
                if (isValidMinus(leftnum, rightnum)) {
                    result = leftnum - rightnum;
                }
                break;
            default:
                result = null;
        }
        return result;
    }

    //输出复合计算的结果(需要判断是否需要默认值)
    public static Double getComplexResult(Double leftnum,
                                          Double rightnum,
                                          String operator,
                                          Boolean needDefaultValue) {
        Double result = null;
        switch (operator) {
            case "/":
                if (isValidDivide(leftnum, rightnum)) {
                    result = leftnum / rightnum;
                }
                break;
            case "-":
                if (isValidMinus(leftnum, rightnum)) {
                    result = leftnum - rightnum;
                }
                break;
            default:
                result = null;
        }
        if (needDefaultValue && GeneralUtil.isEmpty(result)) {
            result = 0d;
        }
        return result;
    }


    //判断几种对象类型是否为空
    public static Boolean isEmpty(Object o) {
        if (o == null) {
            return true;
        }
        if (o instanceof String) {
            return ((String) o).length() == 0;//如果是string,则其如果长度为0，则为空
        }
        if (o instanceof Collection) {
            return ((Collection) o).size() == 0;//如果为集合(list)，则如果其size为0，则为空
        }
        if (o instanceof Map) {
            return ((Map) o).size() == 0;//如果为map,则如果其size为0，则为空
        }
        return false;
    }


    //判断几种对象类型是否非空
    public static Boolean isNotEmpty(Object o) {
        return !isEmpty(o);
    }
}
