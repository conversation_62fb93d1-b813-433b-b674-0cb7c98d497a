package com.ctrip.tour.business.dashboard;

import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.boot.web.support.SpringBootServletInitializer;
import org.springframework.scheduling.annotation.EnableAsync;


/**
 * 默认只会Component Scan com.ctrip.tour.dashboard以及其子package
 * 如果需要Scan更多的package可以使用@SpringBootApplication(scanBasePackages = {"com.ctrip.tour.business.*", "other package"})
 *
 * <AUTHOR>
 * @date 2022/7/8
 */

//exclude排除数据源的自动导入
@SpringBootApplication(scanBasePackages = {"com.ctrip.tour.business.*"}, exclude = {DataSourceAutoConfiguration.class})
@ServletComponentScan
@EnableAsync
public class ServiceInitializer extends SpringBootServletInitializer {

    /**
     * Configure your application when it’s launched by the servlet container
     */
    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(ServiceInitializer.class);
    }
}
