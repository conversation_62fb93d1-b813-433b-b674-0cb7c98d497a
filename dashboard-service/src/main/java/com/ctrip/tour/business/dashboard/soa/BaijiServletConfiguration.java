package com.ctrip.tour.business.dashboard.soa;

import com.ctriposs.baiji.rpc.extensions.springboot.BaijiRegistrationBean;
import org.springframework.context.annotation.Configuration;

/**
 * 调整访问路由的根路径
 *
 * <AUTHOR>
 * @date 2022/7/8
 */
@Configuration
public class BaijiServletConfiguration extends BaijiRegistrationBean {


    public BaijiServletConfiguration(SoaBaijiListener soaBaijiListener) {
        // baiji的servlet url mapping 和实现类
        super("/api/*", soaBaijiListener);
    }

}
