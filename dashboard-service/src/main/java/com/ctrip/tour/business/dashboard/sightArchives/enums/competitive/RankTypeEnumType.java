package com.ctrip.tour.business.dashboard.sightArchives.enums.competitive;

import java.util.Arrays;
import java.util.List;

public enum RankTypeEnumType {

    //搜索热度
    //用户关注度
    //用户吸引力
    //景区客流
    //客单价
    //游客评价
    //履约质量
    //退订率
    //投诉率
    SEARCH_HEAT(1, "searchHeat", "搜索热度","",false), //NOSONAR
    USER_ATTENTION(2, "userAttention", "用户关注度","",false), //NOSONAR
    USER_ATTRACTION(3, "userAttraction", "用户吸引力","",false), //NOSONAR
    SCENIC_SPOT_FLOW(4, "scenicSpotFlow", "景区客流","",false), //NOSONAR
    AVERAGE_ORDER_VALUE(5, "averageOrderValue", "客单价","",false), //NOSONAR
    TOURIST_EVALUATION(6, "touristEvaluation", "游客评价","",false), //NOSONAR
    PERFORMANCE_QUALITY(7, "performanceQuality", "履约质量","",true), //NOSONAR
    REFUND_RATE(8, "refundRate", "退订率","",true), //NOSONAR
    COMPLAINT_RATE(9, "complaintRate", "投诉率","",true); //NOSONAR

    private final int id;
    private final String rankEnglishName;
    private final String rankChineseName;
    private final String orderMetric;
    private final boolean asc;

    RankTypeEnumType(int id, String rankEnglishName, String rankChineseName, String orderMetric, boolean asc) {
        this.id = id;
        this.rankEnglishName = rankEnglishName;
        this.rankChineseName = rankChineseName;
        this.orderMetric = orderMetric;
        this.asc = asc;
    }

    public int getId() {
        return id;
    }

    public String getRankEnglishName() {
        return rankEnglishName;
    }

    public String getRankChineseName() {
        return rankChineseName;
    }

    public String getOrderMetric() {
        return orderMetric;
    }

    public boolean isAsc() {
        return asc;
    }

    public static final List<RankTypeEnumType> ALL_RANK_TYPE_ENUM_LIST = Arrays.asList(RankTypeEnumType.values());
}
