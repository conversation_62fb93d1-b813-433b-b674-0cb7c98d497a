package com.ctrip.tour.business.dashboard.grpBusiness.job;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ctrip.tour.business.dashboard.grpBusiness.dao.starrocks.StarRocksCommonDao;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.group.workbenchsvc.contract.StructuredTableInfoType;
import com.ctrip.tour.group.workbenchsvc.contract.StructuredTableRowInfoType;
import com.ctrip.tour.rights.client.GroupWorkbenchServiceClient;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2025/3/25
 */
@Service
@Slf4j
public class FittingNpsV2AlertNotifyService extends CommonAlertNotifyService {

    private static final String TASK_NPS_V2_ALERT = "TASK_NPS_ACHIEVEMENT_ALERT";
    private static final String EVENT_NPS_V2_ALERT = "EVENT_NPS_ACHIEVEMENT_ALERT";
    private static final String EVENT_NPS_V2_ALERT_STRUCTURED_TABLE = "EVENT_NPS_ACHIEVEMENT_ALERT_STRUCTURED_TABLE";

    @Autowired
    private StarRocksCommonDao starRocksCommonDao;

    private static String NOTIFY_EVENT_EMPCODES = "notify.event.empcodes";

    private GroupWorkbenchServiceClient client = GroupWorkbenchServiceClient.getInstance();

    @Autowired
    private RemoteConfig remoteConfig;

    public void handleMultipriceNotify() throws Exception {

        String whiteEmpCodesStr = remoteConfig.getExternalConfig(NOTIFY_EVENT_EMPCODES);

        List<String> whiteEmpCodes = Lists.newArrayList();
        if (StringUtils.isNotBlank(whiteEmpCodesStr)) {
            whiteEmpCodes = Splitter.on(",").splitToList(whiteEmpCodesStr);
        }

        // 获取当前日期
        LocalDate today = LocalDate.now();

        // 获取两周前的开始日期（即两周前的周一）
        LocalDate endDate = today.minusDays(7);

        // 获取两周前的结束日期（即两周前的周日）
        LocalDate startDate = endDate.minusDays(7);

        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");


        String sql1 = "WITH current_week_data AS (\n" +
                "    select\n" +
                "    sum(recommend_weight - slander_weight)/sum(order_weight) as fittingNps,\n" +
                " pm_eid\n" +
                "from\n" +
                "    cdm_sev_grp_cpr_platform_fitting_nps_df \n" +
                "where\n" +
                "    return_date>='"+startDate.format(dtf)+"' \n" +
                "    and return_date<='"+endDate.format(dtf)+"' and sub_bu_type='跟团游' \n" +//NOSONAR
                "    and partition_d='"+LocalDate.now().format(dtf)+"' \n" +
                "group by\n" +
                "    pm_eid\n" +
                "),\n" +
                "last_week_data AS (\n" +
                "    SELECT\n" +
                "        pm_eid,\n" +
                "        sum(recommend_weight - slander_weight)/sum(order_weight) as last_week_fittingNps\n" +
                "    FROM\n" +
                "        cdm_sev_grp_cpr_platform_fitting_nps_df\n" +
                "    WHERE\n" +
                "        return_date >= DATE_SUB('"+startDate.format(dtf)+"', INTERVAL 1 WEEK) \n" +
                "        AND return_date <= DATE_SUB('"+endDate.format(dtf)+"', INTERVAL 1 WEEK) and sub_bu_type='跟团游'\n" +//NOSONAR
                "        AND partition_d='"+LocalDate.now().format(dtf)+"' \n" +
                "    GROUP BY\n" +
                "        pm_eid\n" +
                ")\n" +
                "SELECT\n" +
                "    c.pm_eid,\n" +
                "    (c.fittingNps - l.last_week_fittingNps) / l.last_week_fittingNps AS week_over_week_ratio\n" +
                "FROM\n" +
                "    current_week_data c\n" +
                "LEFT JOIN\n" +
                "    last_week_data l\n" +
                "ON\n" +
                "    c.pm_eid = l.pm_eid\n" +
                "where ((c.fittingNps - l.last_week_fittingNps) / l.last_week_fittingNps) <= -0.05;\n" +
                "\n";

        List<Map<String, Object>> notifyEids = starRocksCommonDao.query(sql1, Maps.newHashMap());
        if (CollectionUtils.isEmpty(notifyEids)) {
            return;
        }
        List<String> pmEids = notifyEids.stream()
                .map(pw -> {
                    return (String) pw.get("pm_eid");
                }).collect(Collectors.toList());

        for (String pmEid : pmEids) {
            String sql = "WITH current_week_data AS (\n" +
                    "    select\n" +
                    "    sum(recommend_weight - slander_weight)/sum(order_weight) as fittingNps,\n" +
                    "sum(recommend_weight)/ sum(order_weight) as recommend,\n" +
                    "sum(slander_weight) / sum(order_weight) as slanderWCov,\n" +
                    "  sum(feed_back_ord_cnt) / sum(total_ord_cnt) as coverage,\n" +
                    "sum(order_weight) as order_wei,\n" +
                    " vendor_id, vendor_name\n" +
                    "from\n" +
                    "    cdm_sev_grp_cpr_platform_fitting_nps_df \n" +
                    "where\n" +
                    "    return_date>='"+startDate.format(dtf)+"' \n" +
                    "    and return_date<='"+endDate.format(dtf)+"' and sub_bu_type='跟团游' \n" +//NOSONAR
                    "    and partition_d='"+LocalDate.now().format(dtf)+"' \n" +
                    "    and pm_eid='"+pmEid+"' \n" +
                    "group by\n" +
                    "    vendor_id, vendor_name\n" +
                    "),\n" +
                    "last_week_data AS (\n" +
                    "    SELECT\n" +
                    "        vendor_id,vendor_name,\n" +
                    "        sum(recommend_weight - slander_weight)/sum(order_weight) as last_week_fittingNps\n" +
                    "    FROM\n" +
                    "        cdm_sev_grp_cpr_platform_fitting_nps_df\n" +
                    "    WHERE\n" +
                    "        return_date >= DATE_SUB('"+startDate.format(dtf)+"', INTERVAL 1 WEEK) \n" +
                    "        AND return_date <= DATE_SUB('"+endDate.format(dtf)+"', INTERVAL 1 WEEK) and sub_bu_type='跟团游'\n" +//NOSONAR
                    "        AND partition_d='"+LocalDate.now().format(dtf)+"' \n" +
                            "    and pm_eid='"+pmEid+"' \n" +
                    "    GROUP BY\n" +
                    "        vendor_id, vendor_name\n" +
                    ")\n" +
                    "SELECT\n" +
                    "    c.vendor_id,c. vendor_name,\n" +
                    "    c.fittingNps,\n" +
                    "    c.recommend,\n" +
                    "\tc.slanderWCov,\n" +
                    "c.coverage,\n" +
                    "c.order_wei,\n" +
                    "    (c.fittingNps - l.last_week_fittingNps) / l.last_week_fittingNps AS week_over_week_ratio\n" +
                    "FROM\n" +
                    "    current_week_data c\n" +
                    "LEFT JOIN\n" +
                    "    last_week_data l\n" +
                    "ON\n" +
                    "    c.vendor_id = l.vendor_id\n" +
                    "where ((c.fittingNps - l.last_week_fittingNps) / l.last_week_fittingNps) <= -0.05;\n" +
                    "\n";

            List<Map<String, Object>> resultList = starRocksCommonDao.query(sql, Maps.newHashMap());
            if (CollectionUtils.isEmpty(resultList)) {
                return;
            }

            List<StructuredTableRowInfoType> rowInfoTypes = resultList.stream().map(pw -> {
                StructuredTableRowInfoType rowInfoType = new StructuredTableRowInfoType();

                Double fittingNps = (Double) pw.get("fittingNps");
                Long vendorId = (Long) pw.get("vendor_id");
                String vendorName = (String) pw.get("vendor_name");
                Double recommend = (Double) pw.get("recommend");
                Double slanderWCov = (Double) pw.get("slanderWCov");
                Double coverage = (Double) pw.get("coverage");
                Double order_wei = (Double) pw.get("order_wei");
                Double week_over_week_ratio = (Double) pw.get("week_over_week_ratio");

                List<String> colList = Lists.newArrayList( vendorId.toString(), vendorName,getDataRatioStr(fittingNps) , getDataRatioStr(week_over_week_ratio) , getDataRatioStr(recommend), getDataRatioStr(slanderWCov), getDataRatioStr(coverage) , getPrettyDataStr(order_wei));
                rowInfoType.setColList(colList);
                return rowInfoType;
            }).collect(Collectors.toList());
            StructuredTableInfoType structuredTableInfoType = new StructuredTableInfoType();
            structuredTableInfoType.setRowList(rowInfoTypes);
            structuredTableInfoType.setHeaderList(Lists.newArrayList("供应商id","供应商名称","拟合NPS", "周环比", "拟合推荐率", "拟合诋毁率", "覆盖率", "拟合nps分母"));//NOSONAR
            String content = "跟团拟合NPS周环比降幅≥5%预警，数据范围：返程上上周四-上周三（环比往前推一周），请及时联系商家改进。";//NOSONAR
            List<String> tpInfos = Lists.newArrayList("跟团拟合NPS周环比降幅≥5%预警，数据范围：返程上上周四-上周三（环比往前推一周），请及时联系商家改进。");//NOSONAR
            notifyEmp(pmEid, structuredTableInfoType, "跟团拟合NPS通知",//NOSONAR
                    TASK_NPS_V2_ALERT, EVENT_NPS_V2_ALERT, EVENT_NPS_V2_ALERT_STRUCTURED_TABLE, content, tpInfos, "1");//NOSONAR
        }



//        List<String> finalWhiteEmpCodes = whiteEmpCodes;
//        Map<String, List<Map<String, Object>>> emNotifyInfoMap = resultList.stream()
//                .filter(pw -> CollectionUtils.isEmpty(finalWhiteEmpCodes) || (Objects.nonNull(pw) && finalWhiteEmpCodes.contains((String) pw.get("pm_eid"))))
//                .collect(Collectors.groupingBy(pw -> (String) pw.get("pm_eid")));
//
//        emNotifyInfoMap.forEach((pmEid, pwList) -> {
//
//
//        });


    }

}
