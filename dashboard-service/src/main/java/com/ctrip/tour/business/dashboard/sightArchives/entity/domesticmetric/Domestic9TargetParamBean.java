package com.ctrip.tour.business.dashboard.sightArchives.entity.domesticmetric;

import lombok.Data;

import java.util.List;

@Data
public class Domestic9TargetParamBean {
    // 查询时间
    String d;
    // 年份
    String year;
    // 大区范围
    List<String> regionNames;
    // 省份范围
    List<String> provinceNames;
    // 时间类型
    String dateType;
    // 季度列表
    List<String> quarters;
    // 类型特色品类0，重点品类1
    Integer mode;
    // 目标类型0省份目标，1大区目标，2大盘目标
    Integer dataType;
}
