package com.ctrip.tour.business.dashboard.grpBusiness.domain.process.dsl.common;

import com.ctrip.soa._24922.DSLRequestType;
import com.ctrip.soa._24922.Limit;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.AbstractPreDSLProcess;

public class DSLProcessLimitSet extends AbstractPreDSLProcess {
    private Integer page;
    private Integer size;

    public static AbstractPreDSLProcess getInstance(Integer page, Integer size) {
        DSLProcessLimitSet dslProcessLimitSet = new DSLProcessLimitSet();
        dslProcessLimitSet.page = page;
        dslProcessLimitSet.size = size;
        return dslProcessLimitSet;
    }

    @Override
    public DSLRequestType process(DSLRequestType dslRequestType, EarlyReturn earlyReturn) {
        if (page != null) {
            int size = this.size;
            int start = (this.page - 1) * size;
            dslRequestType.setLimit(new Limit(start, size));
        } else {
            int size = 0;
            int start = 100;
            dslRequestType.setLimit(new Limit(start, size));
        }
        return dslRequestType;
    }
}
