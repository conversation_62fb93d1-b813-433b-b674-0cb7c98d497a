package com.ctrip.tour.business.dashboard.tktBusiness.dao;

import com.ctrip.platform.dal.dao.*;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-03-14
 * @API DOC: http://pages.release.ctripcorp.com/framework/dal-client-trip/#/3/3.2/3.2
 */
@Repository
public class BusinessDashboardVendorSalesDao {
    @Autowired
    private GeneralDao dao;


    /**
     * 获取供应商对应的总销量
     * @param vendorId
     * @return
     * @throws SQLException
     */
    public Object getSalesByVendor(Long vendorId) throws SQLException {
        // DalHints hints = DalHints.createIfAbsent(null);
        String sql = "select sum(sales) from business_dashboard_vendor_sales where vendorId=?";
        StatementParameters parameters = new StatementParameters();
        parameters.set(parameters.nextIndex(), vendorId);
        List<List<Object>> numList = dao.getListResult(sql, parameters);
        return numList.get(0).get(0);
    }
}
