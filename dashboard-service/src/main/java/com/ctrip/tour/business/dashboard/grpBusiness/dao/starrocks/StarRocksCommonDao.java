package com.ctrip.tour.business.dashboard.grpBusiness.dao.starrocks;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ctrip.soa._27181.PreparedParameterBean;

/**
 * <AUTHOR>
 * @Date 2025/3/20
 */
@Component
public class StarRocksCommonDao {

    @Autowired
    StarRocksDao starRocksDao;

    public List<Map<String, Object>> query(String sql, Map<String, ?> map) throws SQLException {

        List<PreparedParameterBean> parameters = new ArrayList<>();  //todo


        return starRocksDao.getListResult(sql, parameters);
    }

}
