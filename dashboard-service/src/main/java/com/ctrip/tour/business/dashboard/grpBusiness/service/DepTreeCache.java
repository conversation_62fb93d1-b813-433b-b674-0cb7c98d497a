package com.ctrip.tour.business.dashboard.grpBusiness.service;

import com.ctrip.tour.business.dashboard.grpBusiness.dao.mysql.DimOrgTreeVacationDao;
import com.ctrip.tour.business.dashboard.grpBusiness.dao.mysql.EdwHrEmpVacationDao;
import com.ctrip.tour.business.dashboard.grpBusiness.entity.DimOrgTreeVacation;
import com.ctrip.tour.business.dashboard.grpBusiness.entity.EdwHrEmpVacation;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class DepTreeCache implements Runnable {
    @Autowired
    DimOrgTreeVacationDao dimOrgTreeVacationDao;
    @Autowired
    EdwHrEmpVacationDao edwHrEmpVacationDao;

    final static String RootDeptID = "SO003419";

    static Map<String, DimOrgTreeVacation> depMap = new HashMap<>();
    static Map<String, EdwHrEmpVacation> empMap = new HashMap<>();
    static boolean inited;
    // 周期任务
    static ScheduledThreadPoolExecutor executor = new ScheduledThreadPoolExecutor(2); // 指定线程池大小为5

    @Override
    public void run() {
        Map<String, DimOrgTreeVacation> depMapCp = new HashMap<>();
        Map<String, EdwHrEmpVacation> empMapCP = new HashMap<>();
        // 查询所有人
        List<EdwHrEmpVacation> empList;
        try {
            empList = edwHrEmpVacationDao.getAllSubordinateEmpV2(Collections.singletonList("BG-旅游事业群"));//NOSONAR
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        empList.forEach(v -> {
            empMapCP.put(v.getEmpCode(), v);
        });
        // 部门架构
        List<DimOrgTreeVacation> deptList;
        try {
            deptList = dimOrgTreeVacationDao.queryByDeptID(RootDeptID);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        // 构建部门架构树
        deptList.forEach(v -> {
            depMapCp.put(v.getNodeOrgId(), v);
            // 部门leader，挂在部门下
            EdwHrEmpVacation emp = empMapCP.get(v.getLeaderEmpCode());
            if (emp == null) {
                return;
            }
            v.Leader = emp;
            if (v.directEmp == null) {
                v.directEmp = new ArrayList<>();
            }
            v.directEmp.add(emp);
            // 管理的部门
            if (emp.managedDept == null) {
                emp.managedDept = new ArrayList<>();
            }
            emp.managedDept.add(v);
        });
        // 父部门添加子部门
        deptList.forEach(v -> {
            // 子部门
            DimOrgTreeVacation parentDep = depMapCp.get(v.getParentOrgId());
            if (parentDep == null) {
                return;
            }
            if (parentDep.subDept == null) {
                parentDep.subDept = new ArrayList<>();
            }
            parentDep.subDept.add(v);
        });
        // 部门下的emp
        empList.forEach(v -> {
            // 非leader挂在部门下
            if (v.managedDept != null && !v.managedDept.isEmpty()) {
                return;
            }
            DimOrgTreeVacation curDep = depMapCp.get(v.getTeamId());
            if (curDep == null) {
                return;
            }
            if (curDep.directEmp == null) {
                curDep.directEmp = new ArrayList<>();
            }
            v.dept = curDep;
            curDep.directEmp.add(v);
        });
        depMap = depMapCp;
        empMap = empMapCP;
        inited = true;
    }

    public void initDepTreeSync() {
        if (!inited) {
            run();
            executor.scheduleWithFixedDelay(this, 1, 1, TimeUnit.MINUTES); // 立即开始，每次执行结束后等待1分钟后再次执行
        }
    }


    List<String> getEmpIDByDepID() {
        initDepTreeSync();
        ArrayList<String> empIDs = new ArrayList<>();
        return empIDs;
    }

    // 获取管理的部门
    public List<String> getManagedDepByEmpCode(String empCodes) {
        initDepTreeSync();
        EdwHrEmpVacation edwHrEmpVacation = empMap.get(empCodes);
        List<String> ret = new ArrayList<>();
        if (edwHrEmpVacation == null) {
            return ret;
        }
        if (edwHrEmpVacation.managedDept != null) {
            edwHrEmpVacation.managedDept.forEach(v -> {
                ret.add(v.getNodeOrgId());
            });
        }
        return ret;
    }

    // 获取所在的部门
    public List<String> getDeptByEmpCode(String empCodes) {
        initDepTreeSync();
        EdwHrEmpVacation edwHrEmpVacation = empMap.get(empCodes);
        Set<String> ret = new HashSet<>();
        if (edwHrEmpVacation == null) {
            return new ArrayList<>(ret);
        }
        // 如果是部门管理者
        if (edwHrEmpVacation.managedDept != null && !edwHrEmpVacation.managedDept.isEmpty()) {
            edwHrEmpVacation.managedDept.forEach(v -> ret.add(v.getNodeOrgId()));
        }
        // 是下属
         if (edwHrEmpVacation.dept != null) {
             ret.add(edwHrEmpVacation.dept.getNodeOrgId());
         }
        return new ArrayList<>(ret);
    }

    public List<String> getManagedDeptByEmpCode(String empCodes) {
        initDepTreeSync();
        EdwHrEmpVacation edwHrEmpVacation = empMap.get(empCodes);
        Set<String> ret = new HashSet<>();
        if (edwHrEmpVacation == null) {
            return new ArrayList<>(ret);
        }
        // 如果是部门管理者
        if (edwHrEmpVacation.managedDept != null && !edwHrEmpVacation.managedDept.isEmpty()) {
            edwHrEmpVacation.managedDept.forEach(v -> ret.add(v.getNodeOrgId()));
        }
        // 是下属
        // if (edwHrEmpVacation.dept != null) {
        //     ret.add(edwHrEmpVacation.dept.getNodeOrgId());
        // }
        return new ArrayList<>(ret);
    }

    // 部门是否属于子部门
    public boolean isSubDept(String depCode, String subDeptCode) {
        initDepTreeSync();
        DimOrgTreeVacation dep = depMap.get(depCode);
        if (dep == null) {
            return false;
        }
        DimOrgTreeVacation subDep = depMap.get(subDeptCode);
        if (subDep == null) {
            return false;
        }
        return subDep.getOrgIdPath().contains(dep.getOrgIdPath()) || dep.getOrgIdPath().contains(subDep.getOrgIdPath());
    }

    // 获取所有子部门code
    public List<DimOrgTreeVacation> getAllDepByDepListImpl(DimOrgTreeVacation dep) {
        initDepTreeSync();
        List<DimOrgTreeVacation> deps = new ArrayList<>(Collections.singletonList(dep));
        if (dep.subDept == null || dep.subDept.isEmpty()) {
            return deps;
        }
        for (int i = 0; i < dep.subDept.size(); i++) {
            deps.addAll(getAllDepByDepListImpl(dep.subDept.get(i)));
        }
        return deps;
    }

    public List<DimOrgTreeVacation> getAllDepByDepList(DimOrgTreeVacation dep) {
        initDepTreeSync();
        return getAllDepByDepListImpl(dep);
    }

    public List<DimOrgTreeVacation> getAllDepByDepListWithLimit(DimOrgTreeVacation dep, List<String> limits) {
        initDepTreeSync();
        List<DimOrgTreeVacation> allDepByDepListImpl = getAllDepByDepListImpl(dep);
        List<String> containsList;
        if (limits != null && !limits.isEmpty()) {
            containsList = limits.stream()
                    .map(v -> depMap.get(v))
                    .filter(Objects::nonNull)
                    .map(DimOrgTreeVacation::getOrgIdPath)
                    .collect(Collectors.toList());
        } else {
            containsList = null;
        }
        return allDepByDepListImpl.stream().filter(v -> {
            if (limits == null || limits.isEmpty()) {
                return true;
            }
            for (String depPath : containsList) {
                if (v.getOrgIdPath().contains(depPath)) {
                    return true;
                }
            }
            return false;
        }).collect(Collectors.toList());
    }


    // 获取当前部门下所有下属
    public List<String> getAllEmpCodeByDepIDWithLimit(List<String> depCodes, List<String> limit) {
        initDepTreeSync();
        return depCodes.stream()
                .flatMap(v -> {
                    DimOrgTreeVacation dep = depMap.get(v);
                    if (dep == null) {
                        return Stream.empty();
                    }
                    List<DimOrgTreeVacation> allDepByDepListWithLimit = getAllDepByDepListWithLimit(dep, limit);
                    if (allDepByDepListWithLimit == null) {
                        return Stream.empty();
                    }
                    return allDepByDepListWithLimit.stream();
                })
                .flatMap(v -> {
                    if (v.directEmp == null || v.directEmp.isEmpty()) {
                        return Stream.empty();
                    }
                    return v.directEmp.stream();
                })
                .map(EdwHrEmpVacation::getEmpCode)
                .distinct()
                .collect(Collectors.toList());
    }


    public EdwHrEmpVacation getEmpByEmpID(String empID) {
        initDepTreeSync();
        return empMap.get(empID);
    }
}
