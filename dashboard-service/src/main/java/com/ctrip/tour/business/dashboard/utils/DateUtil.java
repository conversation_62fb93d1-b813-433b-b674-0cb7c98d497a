package com.ctrip.tour.business.dashboard.utils;

import com.ctrip.soa._24922.TimeFilter;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.LastQOrHTimeInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.InputArgumentException;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Month;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class  DateUtil {

    private static List<String> monthList = Lists.newArrayList("01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12");

    private static List<String> quarterList = Lists.newArrayList("Q1","Q2","Q3","Q4");

    private static Map<String, Integer> DaysMap = new HashMap<>();

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 锁对象
     */
    private static final Object lockObj = new Object();

    /**
     * 存放不同的日期模板格式的sdf的Map
     */
    private static Map<String, ThreadLocal<SimpleDateFormat>> sdfMap = new HashMap<>();

    /**
     * 返回一个ThreadLocal的sdf,每个线程只会new一次sdf
     *
     * @param pattern
     * @return
     */
    private static SimpleDateFormat getSdf(final String pattern) {
        ThreadLocal<SimpleDateFormat> tl = sdfMap.get(pattern);

        // 此处的双重判断和同步是为了防止sdfMap这个单例被多次put重复的sdf
        if (tl == null) {
            synchronized (lockObj) {
                tl = sdfMap.get(pattern);
                if (tl == null) {
                    // 只有Map中还没有这个pattern的sdf才会生成新的sdf并放入map

                    // 这里是关键,使用ThreadLocal<SimpleDateFormat>替代原来直接new SimpleDateFormat
                    tl = ThreadLocal.withInitial(() -> new SimpleDateFormat(pattern));
                    sdfMap.put(pattern, tl);
                }
            }
        }

        return tl.get();
    }

    static {
        DaysMap.put("01", 31);
        DaysMap.put("02", 59);
        DaysMap.put("03", 90);
        DaysMap.put("Q1", 90);
        DaysMap.put("04", 30);
        DaysMap.put("05", 61);
        DaysMap.put("06", 91);
        DaysMap.put("Q2", 91);
        DaysMap.put("07", 31);
        DaysMap.put("08", 62);
        DaysMap.put("09", 92);
        DaysMap.put("Q3", 92);
        DaysMap.put("10", 31);
        DaysMap.put("11", 61);
        DaysMap.put("12", 92);
        DaysMap.put("Q4", 92);
    }

    public static List<String> getMonthList(String month,
                                            Boolean needFromFirst) {
        int index = monthList.indexOf(month);
        int start = index - index % 3;
        if(needFromFirst){
            start = 0;
        }
        int end = index + 1;
        return monthList.subList(start, end);
    }



    public static List<String> getTimeList(String dateType,
                                           String month,
                                           String quarter,String half) {
        if ("month".equals(dateType)) {
            return getMonthList(month, false);
        } else if ("half".equals(dateType)) {
            return getMonthListOfHalf(half);
        } else {
            return Lists.newArrayList(quarter);
        }
    }

    //季度考核指标获取时
    //根据传入的时间获取实际对应的时间List
    public static List<String> getTimeList(String dateType,
                                           String month,
                                           String quarter) {
        if ("month".equals(dateType)) {
            return getMonthList(month, false);
        } else {
            return Lists.newArrayList(quarter);
        }
    }

    //将传入的月份转化为对应的季
    public static String getQuarterOfMonth(String month) {
        int monthIndex = monthList.indexOf(month);
        int quarterIndex = (monthIndex - monthIndex % 3) / 3;
        return quarterList.get(quarterIndex);
    }

    //将传入的季度转化为对应的月
    public static List<String> getMonthListOfQuarter(String quarter) {
        int quarterIndex = quarterList.indexOf(quarter);
        int startIndex = quarterIndex * 3;
        int endIndex = (quarterIndex + 1) * 3;
        return monthList.subList(startIndex,endIndex);
    }

    //将传入的半年转化为对应的月
    public static List<String> getMonthListOfHalf(String half) {
        if ("H1".equals(half)) {
            return monthList.subList(0, 6);
        }
        return monthList.subList(6, 12);
    }

    /**
     * 获取季度中不超过指定日期的月份列表
     *
     * @param year 年份（如2025）
     * @param quarter 季度（如"Q3"）
     * @param endDateStr 截止日期（格式yyyy-MM-dd）
     * @return 符合条件的月份列表（如["07"]）
     */
    public static List<String> getMonthsBeforeDate(int year, String quarter, String endDateStr) {
        // 1. 解析截止日期
        LocalDate endDate = LocalDate.parse(endDateStr, DATE_FORMATTER);

        // 2. 获取季度对应的所有月份
        List<String> quarterMonths = getQuarterMonths(quarter);

        // 3. 遍历季度中的月份
        return new ArrayList<>(quarterMonths);
    }

    /**
     * 获取季度对应的所有月份
     *
     * @param quarter 季度（如"Q3"）
     * @return 该季度的所有月份（如["07","08","09"]）
     */
    private static List<String> getQuarterMonths(String quarter) {
        int quarterIndex = quarterList.indexOf(quarter);
        if (quarterIndex == -1) {
            throw new IllegalArgumentException("无效的季度: " + quarter);//NOSONAR
        }

        int startIndex = quarterIndex * 3;
        int endIndex = startIndex + 3;
        return new ArrayList<>(monthList.subList(startIndex, endIndex));
    }



    //季度指标计算时,获取实际目标对应的季度
    public static String getQuarter(String dateType,
                                    String month,
                                    String quarter) {
        if ("month".equals(dateType)) {
            return getQuarterOfMonth(month);
        } else {
            return quarter;
        }
    }

    //获取实际时间参数对应的季度
    public static String getQuarter(String dateType,
                                    String month,
                                    String quarter,
                                    String half) {
        if ("month".equals(dateType)) {
            return getQuarterOfMonth(month);
        } else if ("half".equals(dateType)) {
            return getFirstQuarterOfHalf(half);
        } else {
            return quarter;
        }
    }

    //获取实际时间参数对应的季度
    public static String getQuarterV2(String dateType,
                                    String month,
                                    String quarter,
                                    String half) {
        if ("month".equals(dateType)) {
            return getQuarterOfMonth(month);
        } else if ("half".equals(dateType)) {
            return getFirstQuarterOfHalf(half);
        } else if ("year".equals(dateType)){
            return "Q1";
        }else{
            return quarter;
        }
    }


    //获取实际应该传入的时间
    public static String getInputTime(String dateType,
                                      String month,
                                      String quarter,
                                      String half){
        switch (dateType){
            case "month":
                return month;
            case "quarter":
                return quarter;
            case "half":
                return half;
            default:
                throw new InputArgumentException("invalid dateType is:"+dateType);
        }
    }

    /**
     * 获取季度指标实际应该传入的时间
     * @param dateType
     * @param month
     * @param quarter
     * @param half
     * @return
     */
    public static String getQuarterMetricInputTime(String dateType,
                                                   String month,
                                                   String quarter,
                                                   String half) {
        switch (dateType) {
            case "month":
                return StringUtils.join(getMonthList(month, false), "|");
            case "quarter":
                return quarter;
            case "half":
                return half;
            default:
                throw new InputArgumentException("invalid dateType is:" + dateType);
        }
    }


    /**
     * 获取季度指标要取的目标实际对应的时间(仅针对海外质量成本)
     * @param dateType
     * @param month
     * @param quarter
     * @param half
     * @return
     */
    public static String getQuarterMetricTargetTime(String dateType,
                                                    String month,
                                                    String quarter,
                                                    String half) {
        switch (dateType) {
            case "month":
                return getQuarterOfMonth(month);
            case "quarter":
                return quarter;
            case "half":
                return half;
            default:
                throw new InputArgumentException("invalid dateType is:" + dateType);
        }
    }


    //获取传入半年的第一个季度
    public static String getFirstQuarterOfHalf(String half){
        switch (half){
            case "H1":
                return "Q1";
            case "H2":
                return "Q3";
            default:
                throw new InputArgumentException("input invalid time parameter:" + half);
        }
    }

    public static List<String> getMonthByDateType(String dateType,String month){
        List<String> result = new ArrayList<>();
        switch (dateType){
            case "half":

                    if("H1".equals(month)){
                        result.add("1");
                        result.add("2");
                        result.add("3");
                        result.add("4");
                        result.add("5");
                        result.add("6");
                    }else if("H2".equals(month)){
                        result.add("7");
                        result.add("8");
                        result.add("9");
                        result.add("10");
                        result.add("11");
                        result.add("12");

                }
                break;
            case "quarter":

                    if("Q1".equals(month)){
                        result.add("1");
                        result.add("2");
                        result.add("3");
                    }else if("Q2".equals(month)){
                        result.add("4");
                        result.add("5");
                        result.add("6");
                    }else if("Q3".equals(month)){
                        result.add("7");
                        result.add("8");
                        result.add("9");
                    }else if("Q4".equals(month)){
                        result.add("10");
                        result.add("11");
                        result.add("12");
                    }

                break;
        }
        return result;
    }

    //获取half匹配的季度数据
    public static List<String> getQuarterOfHalf(String half){
        List<String> quaterList = new ArrayList<>();
        switch (half){
            case "H1":
                quaterList.add("Q1");
                quaterList.add("Q2");
                return quaterList;
            case "H2":
                quaterList.add("Q3");
                quaterList.add("Q4");
                return quaterList;
            default:
                throw new InputArgumentException("input invalid time parameter:" + half);
        }
    }

    //获取传入半年对应的第一个月
    public static String getFirstMonthOfHalf(String half) {
        switch (half) {
            case "H1":
                return "01";
            case "H2":
                return "07";
            default:
                throw new InputArgumentException("input invalid time parameter:" + half);
        }
    }

    //获取传入半年对应的最后一个月
    public static String getLastMonthOfHalf(String half) {
        switch (half) {
            case "H1":
                return "06";
            case "H2":
                return "12";
            default:
                throw new InputArgumentException("input invalid time parameter:" + half);
        }
    }


    //获取传入半年对应的季度
    public static List<String> getQuarterListOfHalf(String half){
        switch (half) {
            case "H1":
                return Lists.newArrayList("Q1","Q2");
            case "H2":
                return Lists.newArrayList("Q3","Q4");
            default:
                throw new InputArgumentException("input invalid time parameter:" + half);
        }
    }

    //获取传入半年对应的季度
    public static List<String> getQuarterListOfYear(String year, String d) {
        if (year.equals(d.substring(0, 4))) {
            List<String> passedQuarters = new ArrayList<>();
            // 解析日期字符串
            LocalDate date = LocalDate.parse(d);
            int month = date.getMonthValue();

            // 根据月份确定当前是第几季度
            int currentQuarter = getCurrentQuarter(month);

            // 返回从Q1到当前季度的所有季度
            for (int i = 1; i <= currentQuarter; i++) {
                passedQuarters.add("Q" + i);
            }
            return passedQuarters;
        } else {
            return Arrays.asList("Q1", "Q2", "Q3", "Q4");
        }
    }

    //获取传入季对应的最后一个月
    public static String getLastMonthOfQuarter(String quarter) {
        switch (quarter) {
            case "Q1":
                return "03";
            case "Q2":
                return "06";
            case "Q3":
                return "09";
            case "Q4":
                return "12";
            default:
                throw new InputArgumentException("输入了非法的时间参数:" + quarter);
        }
    }


    //获取传入季对应的第一个月
    public static String getFirstMonthOfQuarter(String quarter) {
        switch (quarter) {
            case "Q1":
                return "01";
            case "Q2":
                return "04";
            case "Q3":
                return "07";
            case "Q4":
                return "10";
            default:
                throw new InputArgumentException("输入了非法的时间参数:" + quarter);
        }
    }

    //判断一年是否为闰年
    public static Boolean isLeapYear(String year) {
        Integer value = Integer.valueOf(year);
        return (value % 4 == 0 && value % 100 != 0) || (value % 400 == 0);
    }

    //判断两个年份的大小关系
    public static Boolean compareTwoYear(String year1,
                                         String year2) {
        return Integer.parseInt(year1) - Integer.parseInt(year2) > 0;
    }

    public static Map<String, Integer> getTrendLineDaysOfMonthOrQuarter(String year,
                                                                        String dateType,
                                                                        String d) throws ParseException {
        Map<String, Integer> resultMap = new HashMap<>();
        //如果当前需要获取的年份比真正的年份要小
        if (compareTwoYear(d.substring(0, 4), year)) {
            d = year + "-12-32";
        }
        String currentMonth = getActualMonthOfD(d);
        //趋势线情况下把今年  当季或者当月以及之前所有的数据全部返回
        if ("quarter".equals(dateType)) {
            String currentQuarter = getActualQuarterOfD(d);
            int index = quarterList.indexOf(currentQuarter);
            for (int i = 0; i <= index - 1; i++) {
                resultMap.put(quarterList.get(i), DaysMap.get(quarterList.get(i)));
            }
            //String currentMonth = getActualMonthOfD(d);
            Integer gapDays = getActualDaysOfD(d);
            List<String> tempMonthList = getMonthList(currentMonth, false);
            int size = tempMonthList.size();
            if (size <= 1) {
                resultMap.put(currentQuarter, gapDays);
            } else {
                resultMap.put(currentQuarter, gapDays + DaysMap.get(tempMonthList.get(size - 2)));
            }
        } else {
            //String currentMonth = getActualMonthOfD(d);
            int index = monthList.indexOf(currentMonth);
            for (int i = 0; i <= index - 1; i++) {
                resultMap.put(monthList.get(i), DaysMap.get(monthList.get(i)));
            }
            List<String> tempMonthList = getMonthList(currentMonth, false);
            Integer gapDays = getActualDaysOfD(d);
            int size = tempMonthList.size();
            if (size <= 1) {
                resultMap.put(currentMonth, gapDays);
            } else {
                resultMap.put(currentMonth, gapDays + DaysMap.get(tempMonthList.get(size - 2)));
            }
        }
        List<String> specialList = Lists.newArrayList("02", "03", "Q1");
        if (isLeapYear(year)) {
            int index = monthList.indexOf(currentMonth);
            if (index > 1) {
                for (Map.Entry<String, Integer> entry : resultMap.entrySet()) {
                    String key = entry.getKey();
                    Integer value = entry.getValue();
                    if (specialList.contains(key)) {
                        resultMap.put(key, value + 1);
                    }
                }
            }
        }
        return resultMap;
    }


    /**
     * 获取日/月/季/半年的窗口天数
     *
     * @param year
     * @param dateType
     * @param month
     * @param quarter
     * @param half
     * @param d
     * @return
     * @throws ParseException
     */
    public static Integer getOverallDaysOfMonthOrQuarterOrHalf(String year,
                                                               String dateType,
                                                               String month,
                                                               String quarter,
                                                               String half,
                                                               String d) throws ParseException {
        if ("half".equals(dateType)) {
            List<String> quarterList = getQuarterListOfHalf(half);
            Integer firstDays = getOverallDaysOfMonthOrQuarter(year, "quarter", "", quarterList.get(0), d);
            Integer secondDays = getOverallDaysOfMonthOrQuarter(year, "quarter", "", quarterList.get(1), d);
            return firstDays + Optional.ofNullable(secondDays).orElse(0);
        } else {
            return getOverallDaysOfMonthOrQuarter(year, dateType, month, quarter, d);
        }
    }




    public static Integer getOverallDaysOfMonthOrQuarter(String year,
                                                         String dateType,
                                                         String month,
                                                         String quarter,
                                                         String d) throws ParseException {
        Map<String,Integer> resultMap = getTrendLineDaysOfMonthOrQuarter(year, dateType, d);
        if("quarter".equals(dateType)){
            return resultMap.get(quarter);
        }else{
            return resultMap.get(month);
        }
    }

    //获取更新时间对应的月
    //由于数据T+1更新  因此需要-1
    public static String getActualMonthOfD(String d) throws ParseException {
        SimpleDateFormat sdf = getSdf("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(sdf.parse(d));
        calendar.add(Calendar.DATE, -1);
        return sdf.format(calendar.getTime()).substring(5, 7);
    }

    //根据更新时间 推算当前最新的数据所在的季
    public static String getActualQuarterOfD(String d) throws ParseException {
        String month = getActualMonthOfD(d);
        return getQuarterOfMonth(month);
    }

    //根据更新时间  推算当前最新的数据所在的年
    public static String getActualYearOfD(String d) throws ParseException{
        SimpleDateFormat sdf = getSdf("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(sdf.parse(d));
        calendar.add(Calendar.DATE, -1);
        return sdf.format(calendar.getTime()).substring(0, 4);
    }

    //获取更新时间对应的时间与当月第一天之间的时间间隔
    private static Integer getActualDaysOfD(String d) throws ParseException {
        SimpleDateFormat sdf = getSdf("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(sdf.parse(d));
        calendar.add(Calendar.DATE, -1);
        return calendar.get(Calendar.DAY_OF_MONTH);
    }

    //根据今年获取去年
    public static String getLastYear(String year) throws ParseException {
        SimpleDateFormat sdf = getSdf("yyyy");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(sdf.parse(year));
        calendar.add(Calendar.YEAR, -1);
        return sdf.format(calendar.getTime());
    }

    //判断选中月份是否是当前月
    public static Boolean isCurrentMonth(String d,
                                         String month) {
        return month.equals(d.substring(5, 7));
    }

    //判断选中月份是否是当前月  v2版
    public static Boolean isCurrentMonthV2(String lastDay,
                                           String year,
                                           String month) {
        return month.equals(lastDay.substring(5, 7)) && year.equals(lastDay.substring(0, 4));
    }


    //判断选中季度是否为当前季
    public static Boolean isCurrentQuarter(String d,
                                           String quarter) {
        String month = d.substring(5, 7);
        List<String> monthList = getMonthListOfQuarter(quarter);
        return monthList.contains(month);
    }


    //判断选中季度是否为当前季 V2版
    public static Boolean isCurrentQuarterV2(String lastDay,
                                             String year,
                                             String quarter) {
        List<String> monthList = getMonthListOfQuarter(quarter);
        return monthList.contains(lastDay.substring(5, 7)) && year.equals(lastDay.substring(0, 4));
    }

    //判断选中半年是不是当前半年 V2
    public static Boolean isCurrentHalfV2(String lastDay,
                                          String year,
                                          String half){
        List<String> monthList = getMonthListOfHalf(half);
        return monthList.contains(lastDay.substring(5, 7)) && year.equals(lastDay.substring(0, 4));
    }

    //判断选中年是不是当前年
    public static Boolean isCurrentYear(String lastDay,
                                        String year) {
        return year.equals(lastDay.substring(0, 4));
    }


    //判断选中的时间是不是最新的时间周期
    public static Boolean isLastestTime(String lastDay,
                                        TimeFilter timeFilter) {
        String dateType = timeFilter.getDateType();
        String year = timeFilter.getYear();
        if ("month".equals(dateType)) {
            return isCurrentMonthV2(lastDay, year, timeFilter.getMonth());
        } else if ("quarter".equals(dateType)) {
            return isCurrentQuarterV2(lastDay, year, timeFilter.getQuarter());
        }
        return isCurrentHalfV2(lastDay, year, timeFilter.getHalf());
    }

    //根据时间间隔和传入时间获取对应时间
    public static String getDayOfInterval(String d,
                                          int interval) throws ParseException {
        SimpleDateFormat sdf = getSdf("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(sdf.parse(d));
        calendar.add(Calendar.DATE, interval);
        return sdf.format(calendar.getTime());
    }

    //根据传入的月份和季度
    // 以及日期类型
    //获取上个月或者上个季度对应的 年和月(季)
    //会出现跨年的情况
    public static List<String> getLastTimeInfo(String dateType,
                                               String year,
                                               String quarter,
                                               String month) throws ParseException {
        if ("month".equals(dateType)) {
            if ("01".equals(month)) {
                String lastYear = getLastYear(year);
                String lastMonth = "12";
                return Lists.newArrayList(lastYear, lastMonth);
            } else {
                int index = monthList.indexOf(month);
                String lastMonth = monthList.get(index - 1);
                return Lists.newArrayList(year, lastMonth);
            }
        }
        if ("quarter".equals(dateType)) {
            if ("Q1".equals(quarter)) {
                String lastYear = getLastYear(year);
                String lastMonth = "Q4";
                return Lists.newArrayList(lastYear, lastMonth);
            } else {
                int index = quarterList.indexOf(quarter);
                String lastQuarter = quarterList.get(index - 1);
                return Lists.newArrayList(year, lastQuarter);
            }
        }
        return new ArrayList<>();
    }

    //根据传入的月份和季度
    // 以及日期类型
    //获取上个月或者上个季度对应的 年和月(季)
    //会出现跨年的情况
    public static List<String> getLastTimeInfoV2(String dateType,
                                               String year,
                                               String quarter,
                                               String month) throws ParseException {
        if ("month".equals(dateType)) {
            if ("01".equals(month)) {
                String lastYear = getLastYear(year);
                String lastMonth = "12";
                return Lists.newArrayList(lastYear, lastMonth);
            } else {
                int index = monthList.indexOf(month);
                String lastMonth = monthList.get(index - 1);
                return Lists.newArrayList(year, lastMonth);
            }
        }else{
            if ("Q1".equals(quarter)) {
                String lastYear = getLastYear(year);
                String lastMonth = "Q4";
                return Lists.newArrayList(lastYear, lastMonth);
            } else {
                int index = quarterList.indexOf(quarter);
                String lastQuarter = quarterList.get(index - 1);
                return Lists.newArrayList(year, lastQuarter);
            }
        }
    }


    //根据传入的月份和季度和半年
    // 以及日期类型
    //获取上个月或者上个季度对应的 年和月/季/半年
    //会出现跨年的情况
    public static List<String> getLastTimeInfoV2(TimeFilter timeFilter) throws ParseException {
        String dateType = timeFilter.getDateType();
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();
        String half = timeFilter.getHalf();
        String year = timeFilter.getYear();
        if ("month".equals(dateType)) {
            if ("01".equals(month)) {
                String lastYear = getLastYear(year);
                String lastMonth = "12";
                return Lists.newArrayList(lastYear, lastMonth);
            } else {
                int index = monthList.indexOf(month);
                String lastMonth = monthList.get(index - 1);
                return Lists.newArrayList(year, lastMonth);
            }
        }
        if ("quarter".equals(dateType)) {
            if ("Q1".equals(quarter)) {
                String lastYear = getLastYear(year);
                String lastMonth = "Q4";
                return Lists.newArrayList(lastYear, lastMonth);
            } else {
                int index = quarterList.indexOf(quarter);
                String lastQuarter = quarterList.get(index - 1);
                return Lists.newArrayList(year, lastQuarter);
            }
        }
        if ("half".equals(dateType)) {
            if ("H1".equals(half)) {
                return Lists.newArrayList(getLastYear(year), "H2");
            } else {
                return Lists.newArrayList(year, "H1");
            }
        }
        return new ArrayList<>();
    }


    /**
     * 获取上个季度或上半年的年、季度
     * @param dateType
     * @param year
     * @param quarter
     * @return
     * @throws ParseException
     */
    public static LastQOrHTimeInfoBean getLastQOrHTimeInfo(String dateType,String year,String quarter) throws ParseException {
        LastQOrHTimeInfoBean lastQOrHTimeInfoBean = new LastQOrHTimeInfoBean();
        if ("quarter".equals(dateType)) {
            if ("Q1".equals(quarter)) {
                lastQOrHTimeInfoBean.setYear(getLastYear(year));
                lastQOrHTimeInfoBean.setQuarter("Q4");
            } else {
                int index = quarterList.indexOf(quarter);
                String lastQuarter = quarterList.get(index - 1);
                lastQOrHTimeInfoBean.setYear(year);
                lastQOrHTimeInfoBean.setQuarter(lastQuarter);
            }
        }
        if ("half".equals(dateType)) {
            if ("Q1".equals(quarter)) {
                lastQOrHTimeInfoBean.setYear(getLastYear(year));
                lastQOrHTimeInfoBean.setQuarter("Q3");
            } else if ("Q2".equals(quarter)) {
                lastQOrHTimeInfoBean.setYear(getLastYear(year));
                lastQOrHTimeInfoBean.setQuarter("Q4");
            }else if ("Q3".equals(quarter)) {
                lastQOrHTimeInfoBean.setYear(year);
                lastQOrHTimeInfoBean.setQuarter("Q1");
            }else {
                lastQOrHTimeInfoBean.setYear(year);
                lastQOrHTimeInfoBean.setQuarter("Q2");
            }
        }
        return lastQOrHTimeInfoBean;
    }


    //获取当前所在月份
    public static String getCurrentMonth(String d) {
        return d.substring(5, 7);
    }


    //根据传入的季度 获取季度对应的月份
    //特别注意当传入的季度是当季的时候需要特殊处理
    public static String getMappingMonthByQuarter(String d,
                                                  String year,
                                                  String quarter) throws ParseException {
        String lastDay = getDayOfInterval(d, -1);
        if (isCurrentQuarterV2(lastDay, year, quarter)) {
            return getCurrentMonth(d);
        } else {
            return getLastMonthOfQuarter(quarter);
        }
    }

    //根据传入的参数获取同比时间的开始时间和结束时间
    public static List<String> getPopTimeRange(String dateType,
                                               String d,
                                               String month,
                                               String quarter,
                                               String popYear,
                                               Boolean needFromFirst) {
        List<String> resultList = new ArrayList<>();
        String startDate;
        String endDate;
        if (needFromFirst) {
            startDate = popYear + "-01-01";
        } else {
            if ("month".equals(dateType)) {
                startDate = popYear + "-" + month + "-" + "01";
            } else {
                startDate = popYear + "-" + getFirstMonthOfQuarter(quarter) + "-" + "01";
            }
        }
        endDate = popYear + d.substring(4);
        resultList.add(startDate);
        resultList.add(endDate);
        return resultList;
    }

    //根据传入的时间获取同比时间的开始时间和结束时间 v2
    public static String getPopTimeRangeV2(TimeFilter timeFilter,
                                           String d,
                                           String popYear,
                                           Boolean needFromFirst) {
        String startDate;
        String endDate;
        String dateType = timeFilter.getDateType();
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();
        String half = timeFilter.getHalf();
        if (needFromFirst) {
            startDate = popYear + "-01-01";
        } else {
            if ("month".equals(dateType)) {
                startDate = popYear + "-" + month + "-" + "01";
            } else if ("quarter".equals(dateType)) {
                startDate = popYear + "-" + getFirstMonthOfQuarter(quarter) + "-" + "01";
            } else {
                startDate = popYear + "-" + getFirstMonthOfHalf(half) + "-" + "01";
            }
        }
        endDate = popYear + d.substring(4);
        return startDate + "|" + endDate;
    }



    /**
     * 在趋势线计算中  根据传入的时间参数 返回所有需要展示的时间
     *
     * @param timeFilter 时间参数
     * @return
     */
    public static List<String> getAllSelectedTime(TimeFilter timeFilter,
                                                  String type) throws ParseException {
        String pattern = "yyyy-MM";
        SimpleDateFormat sdf = getSdf(pattern);
        String dateType = timeFilter.getDateType();
        Integer timeFrame = timeFilter.getTimeFrame();
        String year = timeFilter.getYear();
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();

        if ("month".equals(dateType)) {
            //传入月的情况
            Calendar e = convertStringToCalendar(year + "-" + month, sdf);
            return getTrendlineMonthTimeList(e, sdf, timeFrame, type);
        } else {
            //传入季的情况
            //先把季转化成月 按月的情况处理 再转化成季 再去重
            String mapppingMonth = getLastMonthOfQuarter(quarter);
            Calendar e = convertStringToCalendar(year + "-" + mapppingMonth, sdf);
            //季转化成月*3
            List<String> trendlineMonthList = getTrendlineMonthTimeList(e, sdf, timeFrame * 3, type);
            return trendlineMonthList.stream()
                    .map(item -> {
                        String[] array = item.split("-");
                        String itemYear = array[0];
                        String itemMonth = array[1];
                        String itemQuarter = getQuarterOfMonth(itemMonth);
                        return itemYear + "-" + itemQuarter;
                    })
                    .distinct()
                    .collect(Collectors.toList());
        }
    }

    /**
     * 在趋势线计算中 获取传入时间序列的 环比xxxx时间 xxxx为一个固定写死的时间
     *
     * @param dateType
     * @param originTimeList
     * @param popYear
     * @return
     */
//    public static List<String> getPopTime(String dateType,
//                                          List<String> originTimeList,
//                                          String popYear) throws ParseException {
//        String pattern = "yyyy-MM";
//        SimpleDateFormat sdf = getSdf(pattern);
//        List<String> resultList = new ArrayList<>();
//        if (GeneralUtil.isEmpty(originTimeList)) {
//            return resultList;
//        }
//        String firstTime = originTimeList.get(0);
//        int length = originTimeList.size();
//        if ("month".equals(dateType)) {
//            String firstMonth = firstTime.split("-")[1];
//            String popFirstTime = popYear + "-" + firstMonth;
//            Calendar s = Calendar.getInstance();
//            s.setTime(sdf.parse(popFirstTime));
//            while (resultList.size() < length) {
//                resultList.add(sdf.format(s.getTime()));
//                s.add(Calendar.MONTH, 1);
//            }
//        } else {
//            String firstQuarter = firstTime.split("-")[1];
//            String firstMonth = getFirstMonthOfQuarter(firstQuarter);
//            String popFirstTime = popYear + "-" + firstMonth;
//            Calendar s = Calendar.getInstance();
//            s.setTime(sdf.parse(popFirstTime));
//            while (resultList.size() < length) {
//                String currentPopTime = sdf.format(s.getTime());
//                String currentPopYear = currentPopTime.split("-")[0];
//                String currentPopQuarter = getQuarterOfMonth(currentPopTime.split("-")[1]);
//                resultList.add(currentPopYear + "-" + currentPopQuarter);
//                s.add(Calendar.MONTH, 3);
//            }
//        }
//        return resultList;
//    }


    /**
     * 将string转化为calendar
     *
     * @param time
     * @param sdf
     * @return
     * @throws ParseException
     */
    public static Calendar convertStringToCalendar(String time,
                                                   SimpleDateFormat sdf) throws ParseException {
        Date date = sdf.parse(time);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar;
    }


    /**
     * 将calendar转化为string
     *
     * @param calendar
     * @param sdf
     * @return
     */
    public static String convertCalendarToString(Calendar calendar,
                                                 SimpleDateFormat sdf) {
        return sdf.format(calendar.getTime());
    }


    /**
     * 获取系统当天时间
     */
    public static String getCurrentDate() {
        SimpleDateFormat sdf = getSdf("yyyy-MM-dd");
        return sdf.format(new Date());
    }

    /**
     *  自定义format获取当前时间
     */
    public static String getCurrentTime(String format) {
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat sdf = getSdf(format);
        return sdf.format(calendar.getTime());
    }


    /**
     * 根据传入参数推算所有需要 年-月
     * 当遇到2022年之前的数据时 自动干掉
     *
     * @param e
     * @param timeFrame
     * @return
     */
    public static List<String> getTrendlineMonthTimeList(Calendar e,
                                                         SimpleDateFormat sdf,
                                                         int timeFrame,
                                                         String type) {
        Calendar s = Calendar.getInstance();
        s.setTime(e.getTime());
        //因为需要当前月 所以实际的时间间隔-1
        s.add(Calendar.MONTH, -1 * (timeFrame - 1));
        List<String> resultList = new ArrayList<>();
        //海外从2023年开始看 国内从2022年开始看
        int skipYear = 2022;
        int skipMonth = 0;
        if ("oversea".equals(type)) {
            skipYear = 2023;
        }
        if("overseaDayTour".equals(type)){
            skipYear = 2023;
            skipMonth = 9;
        }
        //纯数字形式  skipYear直接等于传入的年份
        if(GeneralUtil.isNumber(type)){
            skipYear = Integer.parseInt(type);
        }
        while (s.getTimeInMillis() - e.getTimeInMillis() <= 0) {
            //如果是包含出境日游的数据  那么数据从2023年10月开始统计
            if("overseaDayTour".equals(type)){
                if(s.get(Calendar.YEAR) < skipYear || (s.get(Calendar.YEAR) == skipYear && s.get(Calendar.MONTH) < skipMonth)){
                    s.add(Calendar.MONTH, 1);
                    continue;
                }
            }
            //剔除2022年之前的时间  因为仪表盘不需要展示
            if (s.get(Calendar.YEAR) < skipYear) {
                s.add(Calendar.MONTH, 1);
                continue;
            }
            resultList.add(convertCalendarToString(s, sdf));
            s.add(Calendar.MONTH, 1);
        }
        return resultList;
    }

    /**
     * 判断当前输入的参数得到的时间是不是最新季的数据
     */
    public static Boolean isLastestQuarter(String year,
                                           String month,
                                           String quarter,
                                           String dateType,
                                           String lastDay) {
        if ("month".equals(dateType)) {
            String quarterOfMonth = getQuarterOfMonth(month);
            return isCurrentQuarterV2(lastDay, year, quarterOfMonth);
        }
        return isCurrentQuarterV2(lastDay, year, quarter);
    }


    /**
     * 判断当前输入的参数得到的时间是不是最新季的数据
     */
    public static Boolean isLastestQuarter(TimeFilter timeFilter,
                                           String d) throws ParseException {
        String lastDay = DateUtil.getDayOfInterval(d, -1);
        return isLastestQuarter(timeFilter.getYear(), timeFilter.getMonth(), timeFilter.getQuarter(), timeFilter.getDateType(), lastDay);
    }

    /**
     * 获取包含开始时间和结束时间的时间范围  拼接格式为 开始时间|结束时间
     * @param dateType
     * @param d
     * @param isPopTime
     * @param inputStartDate
     * @param inputEndDate
     * @return
     */
    public static String getTimeRange(String dateType,
                                      String d,
                                      Boolean isPopTime,
                                      String inputStartDate,
                                      String inputEndDate) throws ParseException {
        String lastDay = DateUtil.getDayOfInterval(d, -1);

        if ("last_7day".equals(dateType)) {
            String startDate = DateUtil.getDayOfInterval(lastDay, -6);
            if (isPopTime) {
                String momEndDate = DateUtil.getDayOfInterval(startDate, -1);
                String momStartDate = DateUtil.getDayOfInterval(momEndDate, -6);
                return momStartDate + "|" + momEndDate;
            } else {
                return startDate + "|" + lastDay;
            }
        } else if ("last_30day".equals(dateType)) {
            String startDate = DateUtil.getDayOfInterval(lastDay, -29);
            if (isPopTime) {
                String momEndDate = DateUtil.getDayOfInterval(startDate, -1);
                String momStartDate = DateUtil.getDayOfInterval(momEndDate, -29);
                return momStartDate + "|" + momEndDate;
            } else {
                return startDate + "|" + lastDay;
            }
        } else if ("current_month".equals(dateType)) {
            String startDate = DateUtil.getFirstDayOfMonth(lastDay);
            if (isPopTime) {
                String momEndDate = DateUtil.getPopTime(lastDay, 1);
                String momStartDate = DateUtil.getPopTime(startDate, 1);
                return momStartDate + "|" + momEndDate;
            } else {
                return startDate + "|" + lastDay;
            }
        } else if ("current_quarter".equals(dateType)) {
            String startDate = DateUtil.getFirstDayOfQuarter(lastDay);
            if (isPopTime) {
                String momEndDate = DateUtil.getPopTime(lastDay, 3);
                String momStartDate = DateUtil.getPopTime(startDate, 3);
                return momStartDate + "|" + momEndDate;
            } else {
                return startDate + "|" + lastDay;
            }
        } else if ("yesterday".equals(dateType)) {
            if (isPopTime) {
                String momDate = DateUtil.getDayOfInterval(lastDay, -1);
                return momDate + "|" + momDate;
            } else {
                return lastDay + "|" + lastDay;
            }
        } else if ("customTimeRange".equals(dateType)) {
            if (isPopTime) {
                Integer dateGap = getDateGap(inputStartDate, inputEndDate);
                String momEndDate = DateUtil.getDayOfInterval(inputStartDate, -1);
                String momStartDate = DateUtil.getDayOfInterval(momEndDate, -dateGap);
                return momStartDate + "|" + momEndDate;
            } else {
                return inputStartDate + "|" + inputEndDate;
            }
        } else {
            throw new InputArgumentException("invalid dateType is:" + dateType);
        }
    }


    /**
     * 获取当月第一天
     * @param d
     * @return
     * @throws ParseException
     */
    private static String getFirstDayOfMonth(String d) throws ParseException {
        SimpleDateFormat sdf = getSdf("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(sdf.parse(d));
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        return sdf.format(calendar.getTime());
    }


    /**
     * 获取当前季度第一天
     * @param d
     * @return
     */
    private static String getFirstDayOfQuarter(String d) throws ParseException {

        SimpleDateFormat sdf = getSdf("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(sdf.parse(d));
        // 获取当前月份
        int currentMonth = calendar.get(Calendar.MONTH);
        // 获取当前季度的第一个月份
        int firstMonthOfQuarter = (currentMonth / 3) * 3;
        // 设置Calendar对象的月份和日期
        calendar.set(Calendar.MONTH, firstMonthOfQuarter);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        return sdf.format(calendar.getTime());
    }

    /**
     * 根据输入时间获取环比时间
     * @return
     */
    public static String getPopTime(String d,
                                     int timeDiff) throws ParseException {
        SimpleDateFormat sdf = getSdf("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(sdf.parse(d));
        calendar.add(Calendar.MONTH, -1 * timeDiff);
        return sdf.format(calendar.getTime());
    }

    /**
     * 根据输入时间获取环比时间
     * @return
     */
    public static String getYoyTime(String d,
                                    int timeDiff) throws ParseException {
        SimpleDateFormat sdf = getSdf("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(sdf.parse(d));
        calendar.add(Calendar.YEAR, -1 * timeDiff);
        return sdf.format(calendar.getTime());
    }

    /**
     * 指定格式时间字符串 转化成Calendar格式
     * @param dateStr
     * @param format：如yyyy-MM-dd
     * @return
     */
    public static Calendar StringToCalendar(String dateStr, String format) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        Date date = sdf.parse(dateStr);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar;
    }

    /**
     * 对月份做减法
     * @param month：月份
     * @param subNum：减去的数额
     * @return
     */
    public static int getSubMonth(int month, int subNum){
        int m = month - subNum;
        if(m > 0){
            return m;
        }else{
            return 12 - m;
        }
    }

    /**
     * 获取传入日期的最后一天的日期
     * @param d
     * @return
     */
    public static String getLastDate(String d){
        // 获取当前日期
        LocalDate today = LocalDate.parse(d);
        // 获取当前月份的最后一天
        LocalDate lastDayOfMonth = today.with(TemporalAdjusters.lastDayOfMonth());
        return lastDayOfMonth.toString();
    }

    /**
     * 获取两个时间之间的差值
     * @param startDate
     * @param endDate
     * @return
     */
    public static Integer getDateGap(String startDate,
                                     String endDate) {
        LocalDate localDate1 = LocalDate.parse(startDate);
        LocalDate localDate2 = LocalDate.parse(endDate);

        long daysDiff = ChronoUnit.DAYS.between(localDate1, localDate2);
        return (int) daysDiff;
    }


    /**
     * 获取两个时间之间的所有值
     * @param startDate
     * @param endDate
     * @return
     */
    public static List<String> generateDateRange(String startDate, String endDate) {
        List<String> dateList = new ArrayList<>();

        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 将字符串转换为 LocalDate
        LocalDate start = LocalDate.parse(startDate, formatter);
        LocalDate end = LocalDate.parse(endDate, formatter);

        // 迭代日期范围
        while (!start.isAfter(end)) {
            dateList.add(start.format(formatter));
            start = start.plusDays(1);
        }

        return dateList;
    }


    /**
     * 返回匹配时间范围的年和季度
     * @param startDateString
     * @param endDateString
     * @return
     */
    public static List<String> generateYearAndQuarter(String startDateString,String endDateString) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate startDate = LocalDate.parse(startDateString, formatter);
        LocalDate endDate = LocalDate.parse(endDateString, formatter);

        // 检查日期范围有效性
        if (startDate.isAfter(endDate)) {
            return new ArrayList<>();
        }

        // 计算开始季度和结束季度
        int startYear = startDate.getYear();
        int startQuarter = (startDate.getMonthValue() - 1) / 3 + 1;

        int endYear = endDate.getYear();
        int endQuarter = (endDate.getMonthValue() - 1) / 3 + 1;

        List<String> quarterList = new ArrayList<>();
        int currentYear = startYear;
        int currentQuarter = startQuarter;

        // 生成季度列表
        while (currentYear < endYear || (currentYear == endYear && currentQuarter <= endQuarter)) {
            quarterList.add(String.format("%d-Q%d", currentYear, currentQuarter));

            // 移动到下个季度
            currentQuarter++;
            if (currentQuarter > 4) {
                currentQuarter = 1;
                currentYear++;
            }
        }
        return quarterList;
    }

    /**
     * 下发是否可以下钻字段的时间筛选
     * @param timeFilter
     * @return
     */
    public static String selectQuarterWithDiffTimeType(TimeFilter timeFilter) {
        if ("quarter".equalsIgnoreCase(timeFilter.getDateType())){
            return timeFilter.getQuarter();
        }
        String half = timeFilter.getHalf();
        if ("H1".equalsIgnoreCase(half)) {
            return "Q2";
        }else if ("H2".equalsIgnoreCase(half)) {
            return "Q4";
        }
        return "Q1";
    }

    /**
     * 获取季度列表
     * @param timeFilter
     * @return
     */
    public static List<String> getQuarterListWithDiffDateType(TimeFilter timeFilter,String d) throws ParseException {
        d = DateUtil.getDayOfInterval(d, -1);
        String dateType = timeFilter.getDateType();
        if ("quarter".equalsIgnoreCase(dateType) || "month".equalsIgnoreCase(dateType)) {
            return Collections.singletonList("quarter".equals(dateType) ? timeFilter.getQuarter() : DateUtil.getQuarterOfMonth(timeFilter.getMonth()));
        } else if ("half".equalsIgnoreCase(dateType)) {
            String half = timeFilter.getHalf();
            if ("H1".equalsIgnoreCase(half)) {
                if ("Q1".equals(getQuarterWithBelongs(d))){
                    return Lists.newArrayList("Q1");
                }else{
                    return Lists.newArrayList("Q1", "Q2");
                }
            } else if ("H2".equalsIgnoreCase(half)) {
                if ("Q3".equals(getQuarterWithBelongs(d))){
                    return Lists.newArrayList("Q3");
                }else{
                    return Lists.newArrayList("Q3", "Q4");
                }
            }
        }else{
            return getCompletedQuarters(Integer.parseInt(timeFilter.getYear()),d);
        }
        return new ArrayList<>();
    }

    public static List<String> getCompletedQuarters(int year, String dateStr) {
        // 解析输入日期
        LocalDate inputDate = LocalDate.parse(dateStr);

        // 验证年份一致性
        if (inputDate.getYear() != year) {
            return Arrays.asList("Q1","Q2", "Q3", "Q4");
        }

        // 计算当前日期所在季度
        int currentQuarter = getQuarterFromDate(inputDate);

        // 生成已过季度列表
        List<String> quarters = new ArrayList<>();
        for (int q = 1; q <= currentQuarter; q++) {
            quarters.add("Q" + q);
        }

        return quarters;
    }

    private static int getQuarterFromDate(LocalDate date) {
        Month month = date.getMonth();
        return (month.getValue() - 1) / 3 + 1;
    }
    //获取日期所属Q
    public static String getQuarterWithBelongs(String date) {
        // 提取月份部分（索引5到6，例如"2025-06-21"中的"06"）
        String monthStr = date.substring(5, 7);
        int month = Integer.parseInt(monthStr);

        // 计算季度：(month - 1) / 3 + 1 使用整数除法
        int quarter = (month - 1) / 3 + 1;

        // 返回季度字符串
        return "Q" + quarter;
    }
    public static String getQuarterByMonth(String month) {
        // 提取月份部分（索引5到6，例如"2025-06-21"中的"06"）
        switch (month){
            case "01":
            case "02":
            case "03":
            case "1":
            case "2":
            case "3":
                return "Q1";
            case "04":
            case "05":
            case "06":
            case "4":
            case "5":
            case "6":
                return "Q2";
            case "07":
            case "08":
            case "09":
            case "7":
            case "8":
            case "9":
                return "Q3";
            case "10":
            case "11":
            case "12":
                return "Q4";
        }
        return "";
    }

    //找到季列表中的最近一个季并判断是否在当前季
    public static Boolean findLatestJudgeCurrentQuarter(String lastDay,
                                                        String year,
                                                        List<String> quarters) {
        // 获取最大季度值
        String maxQuarter = quarters.stream()
                .max(Comparator.comparingInt(DateUtil::extractQuarterNumber)).orElse(quarters.get(quarters.size() - 1));
        return isCurrentQuarterV2(lastDay, year, maxQuarter);
    }

    // 辅助方法：从季度字符串提取数字
    private static int extractQuarterNumber(String quarterStr) {
        return Integer.parseInt(quarterStr.substring(1));
    }

    //获取最大季度
    public static String getMaxQuarters(List<String> quarterList){
        return quarterList.stream()
                .max(Comparator.comparingInt(DateUtil::extractQuarterNumber)).orElse(quarterList.get(quarterList.size() - 1));
    }

    //处于今年，获取匹配d的最大季，非今年，获取最大季Q4
    public static TimeFilter getMaxQuarterTimeFilter(TimeFilter timeFilter, String d) throws ParseException {
        TimeFilter result = new TimeFilter();
        BeanUtils.copyProperties(timeFilter, result);
        String lastDay = DateUtil.getDayOfInterval(d, -1);
        String currentYear = d.substring(0, 4);
        boolean check = currentYear.equals(result.getYear());
        if ("half".equalsIgnoreCase(result.getDateType())) {
            if (check) {
                String belongQuarter = getQuarterWithBelongs(lastDay);
                if ("H1".equals(result.getHalf())) {
                    if ("Q1".equals(belongQuarter)) {
                        result.setDateType("quarter");
                        result.setQuarter("Q1");
                        result.setHalf(null);
                    }else{
                        result.setDateType("quarter");
                        result.setQuarter("Q2");
                        result.setHalf(null);
                    }
                } else {
                    if ("Q3".equals(belongQuarter)) {
                        result.setDateType("quarter");
                        result.setQuarter("Q3");
                        result.setHalf(null);
                    }else{
                        result.setDateType("quarter");
                        result.setQuarter("Q4");
                        result.setHalf(null);
                    }
                }
            } else {
                if ("H1".equals(result.getHalf())) {
                    result.setDateType("quarter");
                    result.setQuarter("Q2");
                    result.setHalf(null);
                } else {
                    result.setDateType("quarter");
                    result.setQuarter("Q4");
                    result.setHalf(null);
                }
            }
        }
        if ("year".equalsIgnoreCase(result.getDateType())) {
            result.setDateType("quarter");
            if (check) {
                result.setQuarter(getActualQuarterOfD(lastDay));
            }else{
                result.setQuarter("Q4");
            }
        }
        return result;
    }

    // 获取上一年的结束日期
    public static String getLastYearEndDate(String d) {
        Integer year = Integer.parseInt(d.substring(0, 4)) - 1;
        return year + d.substring(4);
    }

    //过滤H维度下跨到下个季度的情况
    public static TimeFilter getRealTimeFilter(TimeFilter timeFilter, String d) throws ParseException {
        String lastDay = DateUtil.getDayOfInterval(d, -1);
        if (!timeFilter.getYear().equals(d.substring(0,4))){
            return timeFilter;
        }
        if ("half".equals(timeFilter.getDateType())){
            String belongQuarter = getQuarterWithBelongs(lastDay);
            if ("H1".equals(timeFilter.getHalf())) {
                if ("Q1".equals(belongQuarter)) {
                    timeFilter.setDateType("quarter");
                    timeFilter.setQuarter("Q1");
                    timeFilter.setHalf(null);
                }
            } else {
                if ("Q3".equals(belongQuarter)) {
                    timeFilter.setDateType("quarter");
                    timeFilter.setQuarter("Q3");
                    timeFilter.setHalf(null);
                }
            }
        }
        return timeFilter;
    }

    public static List<String> getMonthByMonth(String month) {
        List<String> result = new ArrayList<>();
        if(StringUtils.isEmpty(month)){
            return result;
        }
        Integer monthNum= NumberUtils.toInt(month);
        result.add(monthNum.toString());
        return result;
    }

    /**
     * 根据月份获取对应的季度
     * @param month 月份 (1-12)
     * @return 季度 (1-4)
     */
    private static int getCurrentQuarter(int month) {
        if (month >= 1 && month <= 3) {
            return 1; // Q1
        } else if (month >= 4 && month <= 6) {
            return 2; // Q2
        } else if (month >= 7 && month <= 9) {
            return 3; // Q3
        } else if (month >= 10 && month <= 12) {
            return 4; // Q4
        } else {
            throw new IllegalArgumentException("无效的月份: " + month);//NOSONAR
        }
    }
    public static List<String> getDateRange(String period, LocalDate currentDate) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate startDate;
        LocalDate endDate;

        switch (period) {
            case "H1":
                startDate = LocalDate.of(currentDate.getYear(), 1, 1);
                endDate = currentDate.minusDays(1).isBefore(LocalDate.of(currentDate.getYear(), 6, 30)) ? currentDate.minusDays(1) : LocalDate.of(currentDate.getYear(), 6, 30);
                break;

            case "H2":
                startDate = LocalDate.of(currentDate.getYear(), 7, 1);
                endDate = currentDate.minusDays(1).isBefore(LocalDate.of(currentDate.getYear(), 12, 31)) ? currentDate.minusDays(1) : LocalDate.of(currentDate.getYear(), 12, 31);
                break;

            case "Q1":
                startDate = LocalDate.of(currentDate.getYear(), 1, 1);
                endDate = currentDate.minusDays(1).isBefore(LocalDate.of(currentDate.getYear(), 3, 31)) ? currentDate.minusDays(1) : LocalDate.of(currentDate.getYear(), 3, 31);
                break;

            case "Q2":
                startDate = LocalDate.of(currentDate.getYear(), 4, 1);
                endDate = currentDate.minusDays(1).isBefore(LocalDate.of(currentDate.getYear(), 6, 30)) ? currentDate.minusDays(1) : LocalDate.of(currentDate.getYear(), 6, 30);
                break;

            case "Q3":
                startDate = LocalDate.of(currentDate.getYear(), 7, 1);
                endDate = currentDate.minusDays(1).isBefore(LocalDate.of(currentDate.getYear(), 9, 30)) ? currentDate.minusDays(1) : LocalDate.of(currentDate.getYear(), 9, 30);
                break;

            case "Q4":
                startDate = LocalDate.of(currentDate.getYear(), 10, 1);
                endDate = currentDate.minusDays(1).isBefore(LocalDate.of(currentDate.getYear(), 12, 31)) ? currentDate.minusDays(1) : LocalDate.of(currentDate.getYear(), 12, 31);
                break;

            default:
                int month = Integer.parseInt(period);
                startDate = LocalDate.of(currentDate.getYear(), month, 1);
                endDate = currentDate.minusDays(1).isBefore(startDate.withDayOfMonth(startDate.lengthOfMonth())) ? currentDate.minusDays(1) : startDate.withDayOfMonth(startDate.lengthOfMonth());
                break;
        }

        return getDateList(startDate, endDate, formatter);
    }

    private static List<String> getDateList(LocalDate startDate, LocalDate endDate, DateTimeFormatter formatter) {
        List<String> dateList = new ArrayList<>();
        LocalDate date = startDate;
        while (!date.isAfter(endDate)) {
            dateList.add(date.format(formatter));
            date = date.plusDays(1);
        }
        return dateList;
    }

    public static List<String> generateDateList(LocalDate inputDate) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        int year = inputDate.getYear();

        List<String> dateList = new ArrayList<>();
        LocalDate startDate = LocalDate.of(year, 1, 1);
        LocalDate endDate = inputDate.minusDays(1);

        while (!startDate.isAfter(endDate)) {
            dateList.add(startDate.format(formatter));
            startDate = startDate.plusDays(1);
        }

        return dateList;
    }

    public static boolean isCurrentPeriod(String period ) {
        LocalDate now = LocalDate.now();
        int currentYear = now.getYear();

        switch (period) {
            case "H1":
                return now.isAfter(LocalDate.of(currentYear, 1, 1).minusDays(1)) && now.isBefore(LocalDate.of(currentYear, 7, 1));

            case "H2":
                return now.isAfter(LocalDate.of(currentYear, 7, 1).minusDays(1)) && now.isBefore(LocalDate.of(currentYear + 1, 1, 1));

            case "Q1":
                return now.isAfter(LocalDate.of(currentYear, 1, 1).minusDays(1)) && now.isBefore(LocalDate.of(currentYear, 4, 1));

            case "Q2":
                return now.isAfter(LocalDate.of(currentYear, 4, 1).minusDays(1)) && now.isBefore(LocalDate.of(currentYear, 7, 1));

            case "Q3":
                return now.isAfter(LocalDate.of(currentYear, 7, 1).minusDays(1)) && now.isBefore(LocalDate.of(currentYear, 10, 1));

            case "Q4":
                return now.isAfter(LocalDate.of(currentYear, 10, 1).minusDays(1)) && now.isBefore(LocalDate.of(currentYear + 1, 1, 1));

            default:
                int month = Integer.parseInt(period);
                return now.getMonth() == Month.of(month);
        }
    }

}
