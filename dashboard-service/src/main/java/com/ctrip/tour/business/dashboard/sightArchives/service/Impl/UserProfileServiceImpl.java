package com.ctrip.tour.business.dashboard.sightArchives.service.Impl;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.sightArchives.dao.userProfileDao.AdmUsrTtdUserProfileSummaryDfDao;
import com.ctrip.tour.business.dashboard.sightArchives.dao.userProfileDao.CdmLogTtdViewspotBenchSearchTrafficDiDao;
import com.ctrip.tour.business.dashboard.sightArchives.proxy.GargleTranslateServiceProxy;
import com.ctrip.tour.business.dashboard.sightArchives.proxy.GeoLocationServiceProxy;
import com.ctrip.tour.business.dashboard.sightArchives.service.CommonService;
import com.ctrip.tour.business.dashboard.sightArchives.service.UserProfileService;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.ScenicLanguageHelper;
import com.ctrip.tour.business.dashboard.utils.UserUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class UserProfileServiceImpl implements UserProfileService {

    @Autowired
    private AdmUsrTtdUserProfileSummaryDfDao cdmUsrTtdUserProfileSummaryDao;
    @Autowired
    private CdmLogTtdViewspotBenchSearchTrafficDiDao cdmLogTtdViewspotBenchSearchTrafficDiDao;
    @Autowired
    private CommonService commonService;
    @Autowired
    private RemoteConfig config;
    @Autowired
    GeoLocationServiceProxy geoLocationServiceProxy;
    @Autowired
    GargleTranslateServiceProxy gargleTranslateServiceProxy;

    //数仓侧设计文档: http://conf.ctripcorp.com/pages/viewpage.action?pageId=3668691112
    //表: adm_usr_ttd_user_profile_summary_df
    @Override
    public GetUserProfileHistogramResponseType getUserProfileHistogram(GetUserProfileHistogramRequestType requestType) {
        SightArchivesCommonFilter commonFilter = requestType.getCommonFilter();
        String startDate = commonFilter.getStartDate();
        String endDate = commonFilter.getEndDate();
        Long sightId = commonFilter.getSightId();
        Boolean needSubSight = commonFilter.isNeedSubSight();
        Integer dateType = commonFilter.getDateType();

        String queryD = commonService.getQueryD();

        //提前预订天数-订单量柱状图     有同比
        //数据源表: adm_usr_ttd_user_profile_summary_df
        ArrayList<UserProfileHistogramItem> AdvanceBookingDayList = new ArrayList<>();
        List<Map<String, Object>> groupbyres = cdmUsrTtdUserProfileSummaryDao.queryAdvanceBookingDayList(queryD, startDate,endDate,sightId,needSubSight,dateType);

        ///////////总数  今年
        Integer totalNums = cdmUsrTtdUserProfileSummaryDao.getTotalttdOrdSucCnt(queryD, startDate, endDate, sightId, needSubSight,dateType,null);

        ///////////总数  去年
        Integer totalNumsOflastYear = cdmUsrTtdUserProfileSummaryDao.getTotalttdOrdSucCntLastYear(queryD, startDate, endDate, sightId, needSubSight,dateType);

        //去年
        List<Map<String, Object>> lygroupbyres = cdmUsrTtdUserProfileSummaryDao.queryAdvanceBookingDayListOfLastYear(queryD, startDate, endDate, sightId, needSubSight,dateType);

        List<Map<String, Object>> maps = mergeLists(groupbyres, lygroupbyres);

        for(Map<String,Object> map : maps){
            UserProfileHistogramItem userProfileHistogramItem = new UserProfileHistogramItem();
            userProfileHistogramItem.setSegmentName(ScenicLanguageHelper.getMultiLanguage((String) map.get("advance_day_tag"), UserUtil.getVbkLocaleForScenic()));
            if(totalNums == 0){
                userProfileHistogramItem.setOrderCount(0.0);
            }else{
                map.putIfAbsent("ttd_ord_suc_cnt", 0);
                userProfileHistogramItem.setOrderCount((Integer)map.get("ttd_ord_suc_cnt")*1.0/totalNums);
                userProfileHistogramItem.setOrderNum(((Integer) map.get("ttd_ord_suc_cnt")).longValue());
            }
            if(totalNumsOflastYear==null || totalNumsOflastYear == 0){
                userProfileHistogramItem.setLastYearOrderCount(0.0);
            }else{
                map.putIfAbsent("ttd_ord_suc_cnt_last_year", 0);
                userProfileHistogramItem.setLastYearOrderCount((Integer)map.get("ttd_ord_suc_cnt_last_year")*1.0/totalNumsOflastYear);//done 去年占比
            }

            AdvanceBookingDayList.add(userProfileHistogramItem);
        }



        //用户年龄段-订单量柱状图      无同比
        //数据源表: adm_usr_ttd_user_profile_summary_df
        ArrayList<UserProfileHistogramItem> UserAgeGroupList = new ArrayList<>();
        List<Map<String, Object>> UserAgeGroup = cdmUsrTtdUserProfileSummaryDao.queryUserAgeGroupList(queryD, startDate,endDate,sightId,needSubSight,dateType);

        ///////////总数
        Integer totalNumsofUserAgeGroup = cdmUsrTtdUserProfileSummaryDao.getTotalttdOrdSucCnt(queryD, startDate, endDate, sightId, needSubSight,dateType,"user_age_tag");

        for(Map<String,Object> map : UserAgeGroup){
            UserProfileHistogramItem userProfileHistogramItem = new UserProfileHistogramItem();
            userProfileHistogramItem.setSegmentName(ScenicLanguageHelper.getMultiLanguage((String) map.get("user_age_tag"), UserUtil.getVbkLocaleForScenic()));
            userProfileHistogramItem.setOrderCount((Integer)map.get("ttd_ord_suc_cnt")*1.0/totalNumsofUserAgeGroup);
            userProfileHistogramItem.setOrderNum(((Integer)map.get("ttd_ord_suc_cnt")).longValue());
            userProfileHistogramItem.setLastYearOrderCount(0.0);//done 无同比需求
            UserAgeGroupList.add(userProfileHistogramItem);
        }

        GetUserProfileHistogramResponseType getUserProfileHistogramResponseType = new GetUserProfileHistogramResponseType();
        getUserProfileHistogramResponseType.setUserAgeGroupList(UserAgeGroupList);//提前预订天数-订单量柱状
        getUserProfileHistogramResponseType.setAdvanceBookingDayList(AdvanceBookingDayList);//用户年龄段-订单量柱状图
        return getUserProfileHistogramResponseType;
    }

    @Override
    public GetUserProfilePieChartResponseType getUserProfilePieChart(GetUserProfilePieChartRequestType requestType) {
        //done 去年
        SightArchivesCommonFilter commonFilter = requestType.getCommonFilter();
        String startDate = commonFilter.getStartDate();
        String endDate = commonFilter.getEndDate();
        Long sightId = commonFilter.getSightId();
        Boolean needSubSight = commonFilter.isNeedSubSight();
        Integer dateType = commonFilter.getDateType();


        String queryD = commonService.getQueryD();

        GetUserProfilePieChartResponseType userProfilePieChartResponseType = new GetUserProfilePieChartResponseType();
        List<UserProfilePieChart> userProfilePieChart = new ArrayList<>();
        //客户等级/用户类型分布/本异地分析/境内外用户占比
        UserProfilePieChart customer_level = new UserProfilePieChart();
        UserProfilePieChart user_type = new UserProfilePieChart();
        UserProfilePieChart local_analysis = new UserProfilePieChart();
        UserProfilePieChart domestic_foreign = new UserProfilePieChart();

        List<UserProfilePieChartSegment> customer_level_segment = cdmUsrTtdUserProfileSummaryDao.querycustomerLevelSegment(queryD, startDate,endDate,sightId,needSubSight,dateType);
//        List<UserProfilePieChartSegment> customer_lastyear_level_segment = new ArrayList<>();//无去年
        customer_level.setChartName("userLevel");
        customer_level.setSegmentList(customer_level_segment);
//        customer_level.setLastYearSegmentList(customer_lastyear_level_segment);

        List<UserProfilePieChartSegment> user_type_segment = cdmUsrTtdUserProfileSummaryDao.queryUserTypeSegment(queryD,startDate,endDate,sightId,needSubSight,dateType);
//        List<UserProfilePieChartSegment> user_type_lastyear_segment = new ArrayList<>();////无去年
        user_type.setChartName("userType");
        user_type.setSegmentList(user_type_segment);
//        user_type.setLastYearSegmentList(user_type_lastyear_segment);


        List<UserProfilePieChartSegment> local_analysis_segment = cdmUsrTtdUserProfileSummaryDao.queryLocalAnalysisSegment(queryD,startDate,endDate,sightId,needSubSight,dateType);
        List<UserProfilePieChartSegment> local_analysis_lastyear_segment = cdmUsrTtdUserProfileSummaryDao.queryLocalAnalysisSegmentOfLastYear(queryD,startDate,endDate,sightId,needSubSight,dateType);
        local_analysis.setChartName("localAndForeign");
        local_analysis.setSegmentList(local_analysis_segment);
        local_analysis.setLastYearSegmentList(local_analysis_lastyear_segment);


        List<UserProfilePieChartSegment> domestic_foreign_segment = cdmUsrTtdUserProfileSummaryDao.queryDomesticForeignSegment(queryD,startDate,endDate,sightId,needSubSight,dateType);
        List<UserProfilePieChartSegment> domestic_foreign_lastyear_segment = cdmUsrTtdUserProfileSummaryDao.queryDomesticForeignSegmentOfLastYear(queryD,startDate,endDate,sightId,needSubSight,dateType);
        domestic_foreign.setChartName("domesticAndOversea");
        domestic_foreign.setSegmentList(domestic_foreign_segment);
        domestic_foreign.setLastYearSegmentList(domestic_foreign_lastyear_segment);

        for(UserProfilePieChartSegment userProfilePieChartSegment : domestic_foreign_segment){
            if("国内".equals(userProfilePieChartSegment.getName())){ //NOSONAR
                userProfilePieChartSegment.setName("境内"); //NOSONAR
            }else if ("海外".equals(userProfilePieChartSegment.getName())) { //NOSONAR
                userProfilePieChartSegment.setName("境外"); //NOSONAR
            }
        }
        for(UserProfilePieChartSegment userProfilePieChartSegment : domestic_foreign_lastyear_segment) {
            if ("国内".equals(userProfilePieChartSegment.getName())) { //NOSONAR
                userProfilePieChartSegment.setName("境内"); //NOSONAR
            } else if ("海外".equals(userProfilePieChartSegment.getName())) { //NOSONAR
                userProfilePieChartSegment.setName("境外"); //NOSONAR
            }
        }

        userProfilePieChart.add(customer_level);
        userProfilePieChart.add(user_type);
        userProfilePieChart.add(local_analysis);
        userProfilePieChart.add(domestic_foreign);

        userProfilePieChartResponseType.setPieChartList(userProfilePieChart);


        if ("T".equals(config.getConfigValue("languageSwitch"))) {
            setMultiLanguage(userProfilePieChartResponseType);
        }
        return userProfilePieChartResponseType;
    }

    private void setMultiLanguage(GetUserProfilePieChartResponseType userProfilePieChartResponseType) {
        List<UserProfilePieChart> userProfilePieChartList = userProfilePieChartResponseType.getPieChartList();
        if (CollectionUtils.isNotEmpty(userProfilePieChartList)) {
            for (UserProfilePieChart userProfilePieChart : userProfilePieChartList) {
                List<UserProfilePieChartSegment> userProfilePieChartSegmentList = userProfilePieChart.getSegmentList();
                if (CollectionUtils.isNotEmpty(userProfilePieChartSegmentList)) {
                    for (UserProfilePieChartSegment userProfilePieChartSegment : userProfilePieChartSegmentList) {
                        if (StringUtils.isNotBlank(userProfilePieChartSegment.getName())) {
                            userProfilePieChartSegment.setName(ScenicLanguageHelper.getMultiLanguage(userProfilePieChartSegment.getName(), UserUtil.getVbkLocaleForScenic()));
                        }
                    }
                }
            }
        }
    }

    @Override
    public GetUserResidenceDistributionResponseType getUserResidenceDistribution(GetUserResidenceDistributionRequestType requestType) {
        SightArchivesCommonFilter commonFilter = requestType.getCommonFilter();
        String startDate = commonFilter.getStartDate();
        String endDate = commonFilter.getEndDate();
        Long sightId = commonFilter.getSightId();
        Boolean needSubSight = commonFilter.isNeedSubSight();
        Integer dateType = commonFilter.getDateType();
        String queryD = commonService.getQueryD();

        GetUserResidenceDistributionResponseType userResidenceDistributionResponseType = new GetUserResidenceDistributionResponseType();
        List<UserResidenceItem> cityList = cdmUsrTtdUserProfileSummaryDao.queryCityList(queryD,startDate,endDate,sightId,needSubSight,dateType);
        List<UserResidenceItem> provinceList = cdmUsrTtdUserProfileSummaryDao.queryProvinceList(queryD,startDate,endDate,sightId,needSubSight,dateType);
        List<UserResidenceItem> countryList = cdmUsrTtdUserProfileSummaryDao.queryCountryList(queryD,startDate,endDate,sightId,needSubSight,dateType);

        //翻译
        if ("T".equals(config.getConfigValue("languageSwitch")) && "en-US".equals(UserUtil.getVbkLocale())){
            List<Long> cityIdList = cityList.stream().map(UserResidenceItem::getCityId).collect(Collectors.toList());
            List<Long> cityProvinceIdList = cityList.stream().map(UserResidenceItem::getProvinceId).collect(Collectors.toList());
            List<Long> provinceIdList = provinceList.stream().map(UserResidenceItem::getProvinceId).collect(Collectors.toList());
            List<Long> countryIdList = countryList.stream().map(UserResidenceItem::getCountryId).collect(Collectors.toList());
            Map<Long,String> cityTranslateResultMap = geoLocationServiceProxy.getLocationTranslateResult(cityIdList, 3);
            Map<Long,String> cityProvinceTranslateResultMap = geoLocationServiceProxy.getLocationTranslateResult(cityProvinceIdList, 2);
            Map<Long,String> provinceTranslateResultMap = geoLocationServiceProxy.getLocationTranslateResult(provinceIdList, 2);
            Map<Long,String> countryTranslateResultMap = geoLocationServiceProxy.getLocationTranslateResult(countryIdList, 1);

            for (UserResidenceItem userResidenceItem : cityList) {
                Long cityId = userResidenceItem.getCityId();
                if (cityTranslateResultMap.containsKey(cityId)) {
                    userResidenceItem.setTranslateCityName(cityTranslateResultMap.get(cityId));
                }
                Long provinceId = userResidenceItem.getProvinceId();
                if (cityProvinceTranslateResultMap.containsKey(provinceId)) {
                    userResidenceItem.setTranslateProvinceName(cityProvinceTranslateResultMap.get(provinceId));
                }
            }
            for (UserResidenceItem userResidenceItem : provinceList) {
                Long provinceId = userResidenceItem.getProvinceId();
                if (provinceTranslateResultMap.containsKey(provinceId)) {
                    userResidenceItem.setTranslateProvinceName(provinceTranslateResultMap.get(provinceId));
                }
            }
            for (UserResidenceItem userResidenceItem : countryList) {
                Long countryId = userResidenceItem.getCountryId();
                if (countryTranslateResultMap.containsKey(countryId)) {
                    userResidenceItem.setTranslateCountryName(countryTranslateResultMap.get(countryId));
                }
            }
        }
        userResidenceDistributionResponseType.setCityList(cityList);
        userResidenceDistributionResponseType.setProvinceList(provinceList);
        userResidenceDistributionResponseType.setCountryList(countryList);
        return userResidenceDistributionResponseType;
    }

    @Override
    public GetUserSearchPreferenceResponseType getUserSearchPreference(GetUserSearchPreferenceRequestType requestType) {
        SightArchivesCommonFilter commonFilter = requestType.getCommonFilter();
        String startDate = commonFilter.getStartDate();
        String endDate = commonFilter.getEndDate();
        Long sightId = commonFilter.getSightId();
        Boolean needSubSight = commonFilter.isNeedSubSight();

        GetUserSearchPreferenceResponseType userSearchPreferenceResponseType = new GetUserSearchPreferenceResponseType();
        List<UserSearchPreferenceSearchKey> searchKeyList = cdmLogTtdViewspotBenchSearchTrafficDiDao.querySearchKeyList(startDate,endDate,sightId,needSubSight);

        //翻译搜索词云
        if("T".equals(config.getConfigValue("languageSwitch")) && "en-US".equals(UserUtil.getVbkLocale()) ) {

            List<String> textList = new ArrayList<>();
            for (UserSearchPreferenceSearchKey userSearchPreferenceSearchKey : searchKeyList) {
                if (StringUtils.isNotBlank(userSearchPreferenceSearchKey.getSearchKey())) {
                    textList.add(userSearchPreferenceSearchKey.getSearchKey());
                }
            }
            Map<String, String> translateResultMap = gargleTranslateServiceProxy.googleBatchTranslate(textList);
            for (UserSearchPreferenceSearchKey searchKey : searchKeyList) {
                String text = searchKey.getSearchKey();
                if (StringUtils.isNotBlank(text) && translateResultMap.containsKey(text)) {
                    searchKey.setSearchKey(translateResultMap.get(text));
                }
            }
        }

        userSearchPreferenceResponseType.setSearchKeyList(searchKeyList);
        return userSearchPreferenceResponseType;
    }

    public static List<Map<String, Object>> mergeLists(List<Map<String, Object>> currentYearList, List<Map<String, Object>> lastYearList) {
        List<Map<String, Object>> mergedList = new ArrayList<>();

        for (Map<String, Object> currentYearMap : currentYearList) {
            String currentName = (String) currentYearMap.get("advance_day_tag");
            boolean matched = false;

            for (Map<String, Object> lastYearMap : lastYearList) {
                String lastYearName = (String) lastYearMap.get("advance_day_tag");

                if (currentName.equals(lastYearName)) {
                    Map<String, Object> mergedMap = new HashMap<>();
                    mergedMap.put("advance_day_tag", lastYearName);
                    // 复制当前年份的指标
                    mergedMap.put("ttd_ord_suc_cnt", currentYearMap.get("ttd_ord_suc_cnt"));
                    mergedMap.put("ttd_ord_suc_cnt_last_year", lastYearMap.get("ttd_ord_suc_cnt"));
                    mergedList.add(mergedMap);
                    matched = true;
                    break;
                }
            }

            if (!matched) {
                // 如果没有匹配到，把当前年份的记录直接加入到结果列表中
                Map<String, Object> mergedMap = new HashMap<>(currentYearMap);
                mergedMap.put("ttd_ord_suc_cnt_last_year", 0); // 或者其他默认值
                mergedList.add(mergedMap);
            }
        }

        return mergedList;
    }

}
