package com.ctrip.tour.business.dashboard.tktBusiness.dao;

import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.StatementParameters;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.MetricHelper;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.ctrip.tour.business.dashboard.utils.SqlUtil;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/8/8
 */
@Repository
public class Bus2Dao {

    @Autowired
    private GeneralDao dao;


    public void getMetricCardDataAsync(String level,
                                       Map<String, List<String>> inMap,
                                       Map<String, List<String>> notInMap,
                                       DalHints dalHints) throws SQLException {

        String tableName = getReachTableName(level, null, "metricCard",null);
        StringBuilder sb = new StringBuilder();
        sb.append("select sum(ttd_cps_tot_profit) as ttd_cps_tot_profit,sum(ttd_trgt_profit) as ttd_trgt_profit ");
        sb.append(" from ").append(tableName).append(" ");
        StatementParameters parameters = new StatementParameters();
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        if (!GeneralUtil.isEmpty(notInMap)) {
            SqlUtil.jointNotInCondition(sb, parameters, notInMap);
        }
        dao.getListResultAsync(sb.toString(), parameters, dalHints);
    }

    public void getMetricCardDetailProfitAsync(String level,
                                               Map<String, List<String>> inMap,
                                               Map<String, List<String>> notInMap,
                                               DalHints dalHints) throws SQLException {
        String tableName = getReachTableName(level, null, "metricCard",null);
        StringBuilder sb = new StringBuilder();
        sb.append("select sum(ttd_suc_subsidy_rebate_profit) as ttd_sys_inner_profit,sum(ttd_suc_finext_profit) as ttd_sys_outer_profit ");
        sb.append(" from ").append(tableName).append(" ");
        StatementParameters parameters = new StatementParameters();
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        if (!GeneralUtil.isEmpty(notInMap)) {
            SqlUtil.jointNotInCondition(sb, parameters, notInMap);
        }
        dao.getListResultAsync(sb.toString(), parameters, dalHints);
    }

    public void getMetricCardMomDataAsync(String level,
                                          Map<String, List<String>> inMap,
                                          Map<String, List<String>> notInMap,
                                          Map<String, List<String>> betweenMap,
                                          DalHints dalHints) throws SQLException {
        String tableName = getReachTableName(level, null, "momRate",null);
        StringBuilder sb = new StringBuilder();
        sb.append("select sum(ttd_cps_tot_profit) as ttd_cps_tot_profit ");
        sb.append(" from ").append(tableName).append(" ");
        StatementParameters parameters = new StatementParameters();
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        if (!GeneralUtil.isEmpty(notInMap)) {
            SqlUtil.jointNotInCondition(sb, parameters, notInMap);
        }
        if (!GeneralUtil.isEmpty(betweenMap)) {
            SqlUtil.jointBetweenCondition(sb, parameters, betweenMap);
        }
        dao.getListResultAsync(sb.toString(), parameters, dalHints);
    }




    //获取某月或者某季数据明细
    //按照下钻维度排序
    public void getTableReachDataAsync(String field,
                                       Map<String, List<String>> inMap,
                                       Map<String, List<String>> notInMap,
                                       List<String> tagList,
                                       Integer pageNo,
                                       Integer pageSize,
                                       Map<String, List<String>> betweenMap,
                                       DalHints dalHints,
                                       String type,
                                       String dateType,
                                       Map<String, List<String>> likeMap,
                                       Map<String,List<String>> notLikeMap) throws SQLException {
        String tableName = getReachTableName(null, field, type, dateType);
        StringBuilder sb = new StringBuilder();
        StatementParameters parameters = new StatementParameters();
        sb.append("select ");
        SqlUtil.jointGroupCondition(sb, false, tagList);
        sb.append(",sum(ttd_cps_tot_profit) as ttd_cps_tot_profit ");
        sb.append(" from ").append(tableName).append(" ");
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        if (!GeneralUtil.isEmpty(notInMap)) {
            SqlUtil.jointNotInCondition(sb, parameters, notInMap);
        }
        if (!GeneralUtil.isEmpty(betweenMap)) {
            SqlUtil.jointBetweenCondition(sb, parameters, betweenMap);
        }
        if (!GeneralUtil.isEmpty(likeMap)) {
            SqlUtil.jointLikeCondition(sb, likeMap, parameters);
        }
        if (!GeneralUtil.isEmpty(notLikeMap)) {
            SqlUtil.jointNotLikeCondition(sb, notLikeMap, parameters);
        }
        SqlUtil.jointGroupCondition(sb, true, tagList);
        List<String> orderList = Lists.newArrayList("sum(ttd_cps_tot_profit)");
        if ("masterdata".equals(type)) {
            orderList.add(MetricHelper.getDrillDownColumnName(field));
        }
        SqlUtil.jointOrderCondition(sb, orderList, "desc");
        if (pageNo != null && pageSize != null) {
            SqlUtil.jointPagingCondition(sb, parameters, pageNo, pageSize);
        }
        dao.getListResultAsync(sb.toString(), parameters, dalHints);
    }


    //获取表格数据总条数
    public void getTableDataCountAsync(String field,
                                       Map<String, List<String>> inMap,
                                       Map<String, List<String>> notInMap,
                                       List<String> tagList,
                                       DalHints dalHints,
                                       String dateType,
                                       Map<String, List<String>> likeMap,
                                       Map<String,List<String>> notLikeMap) throws SQLException {
        String tableName = getReachTableName(null, field, "masterdata", dateType);
        StringBuilder sb = new StringBuilder();
        StatementParameters parameters = new StatementParameters();
        sb.append("select count(*) from ( select ");
        SqlUtil.jointGroupCondition(sb, false, tagList);
        sb.append(",sum(ttd_cps_tot_profit) as ttd_cps_tot_profit ");
        sb.append(" from ").append(tableName).append(" ");
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        if (!GeneralUtil.isEmpty(notInMap)) {
            SqlUtil.jointNotInCondition(sb, parameters, notInMap);
        }
        if (!GeneralUtil.isEmpty(likeMap)) {
            SqlUtil.jointLikeCondition(sb, likeMap, parameters);
        }
        if (!GeneralUtil.isEmpty(notLikeMap)) {
            SqlUtil.jointNotLikeCondition(sb, notLikeMap, parameters);
        }
        SqlUtil.jointGroupCondition(sb, true, tagList);
        SqlUtil.jointOrderCondition(sb, tagList, "asc");
        sb.append(" ) aa");
        dao.getListResultAsync(sb.toString(), parameters, dalHints);
    }


    //获取某月或者某季系统内外毛利
    //按照下钻维度排序
    public void getTableProfitDetailDataAsync(String field,
                                              Map<String, List<String>> inMap,
                                              Map<String, List<String>> notInMap,
                                              List<String> tagList,
                                              DalHints dalHints,
                                              String dateType,
                                              Map<String, List<String>> likeMap,
                                              Map<String,List<String>> notLikeMap) throws SQLException {
        String tableName = getReachTableName(null, field, "masterdata", dateType);
        StringBuilder sb = new StringBuilder();
        StatementParameters parameters = new StatementParameters();
        sb.append("select ");
        SqlUtil.jointGroupCondition(sb, false, tagList);
        sb.append(",sum(ttd_suc_subsidy_rebate_profit) as ttd_sys_inner_profit,sum(ttd_suc_finext_profit) as ttd_sys_outer_profit ");
        sb.append(" from ").append(tableName).append(" ");
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        if (!GeneralUtil.isEmpty(notInMap)) {
            SqlUtil.jointNotInCondition(sb, parameters, notInMap);
        }
        if (!GeneralUtil.isEmpty(likeMap)) {
            SqlUtil.jointLikeCondition(sb, likeMap, parameters);
        }
        if (!GeneralUtil.isEmpty(notLikeMap)) {
            SqlUtil.jointNotLikeCondition(sb, notLikeMap, parameters);
        }
        SqlUtil.jointGroupCondition(sb, true, tagList);
        SqlUtil.jointOrderCondition(sb, tagList, "asc");
        dao.getListResultAsync(sb.toString(), parameters, dalHints);
    }






    //按月或者季拆分 返回当年目标
    public void getSpilitTargetDataAsync(String field,
                                         Map<String, List<String>> inMap,
                                         Map<String, List<String>> notInMap,
                                         List<String> tagList,
                                         DalHints dalHints,
                                         String dateType) throws SQLException {
        String tableName = getTargetTableName(field, dateType);
        StringBuilder sb = new StringBuilder();
        StatementParameters parameters = new StatementParameters();
        sb.append("select ");
        SqlUtil.jointGroupCondition(sb, false, tagList);
        sb.append(",sum(ttd_trgt_profit) as ttd_trgt_profit ");
        sb.append(" from ").append(tableName).append(" ");
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        if (!GeneralUtil.isEmpty(notInMap)) {
            SqlUtil.jointNotInCondition(sb, parameters, notInMap);
        }
        SqlUtil.jointGroupCondition(sb, true, tagList);
        dao.getListResultAsync(sb.toString(), parameters, dalHints);
    }




    private String getReachTableName(String level,
                                     String field,
                                     String type,
                                     String dateType){
        String tableName = "";
        if ("metricCard".equals(type) || "trendline".equals(type)) {
            //大区 省份 国内
            tableName = "bus_1_2_region_finish_t_new";
            if ("三方".equals(level)) {
                tableName = "bus_1_2_three_finish_t_new";
            } else if ("景点".equals(level)) {
                tableName = "bus_1_2_examinee_viewlevel_finish_t";
            }
            return tableName;
        }
        if ("momRate".equals(type) || "popRate".equals(type)) {
            //大区 省份 国内
            tableName = "bus_1_2_region_perfo_t_new";
            if ("景点".equals(level)) {
                //天粒度新表
                tableName = "bus_1_2_tkt_bd_day_perfo_t";
            } else if ("三方".equals(level)) {
                tableName = "bus_1_2_three_perfo_t_new";
            }
            return tableName;
        }
        if("drilldown".equals(type)){
            //大区 省份
            tableName = "bus_1_2_region_perfo_t_new";
            if("商拓".equals(field)){
                //商拓(天粒度)
                tableName = "bus_1_2_tkt_bd_day_perfo_t";
            }else if("POI".equals(field)){
                //POI
                tableName = "bus_1_2_view_day_t";
            }
            return tableName;
        }
        if("masterdata".equals(type)){
            //大区 省份
            tableName = "bus_1_2_region_finish_t_new";
            if("商拓".equals(field)){
                if("month".equals(dateType)){
                    //商拓(月粒度)
                    tableName = "bus_1_2_tkt_bd_month_finish_t";
                }else{
                    //商拓(季粒度)
                    tableName = "bus_1_2_tkt_bd_quarter_finish_t";
                }
            }else if("POI".equals(field)){
                //POI
                tableName = "bus_1_2_view_summary_t";
            }
            return tableName;
        }
        return tableName;
    }


    private String getTargetTableName(String field,
                                      String dateType) {
        //大区 省份
        String tableName = "bus_1_2_region_config";
        if ("商拓".equals(field)) {
            if("month".equals(dateType)){
                //商拓(月粒度)
                tableName = "bus_1_2_tkt_bd_month_finish_t";
            }else{
                //商拓(季粒度)
                tableName = "bus_1_2_tkt_bd_quarter_finish_t";
            }
        }
        return tableName;
    }


    public void getFieldListAsync(String field,
                                  Map<String, List<String>> inMap,
                                  Map<String, List<String>> likeMap,
                                  List<String> tagList,
                                  Integer pageNo,
                                  Integer pageSize,
                                  DalHints dalHints,
                                  String dateType) throws SQLException {
        String tableName = getReachTableName(null, field, "masterdata", dateType);
        StringBuilder sb = new StringBuilder();
        sb.append("select ");
        SqlUtil.jointGroupCondition(sb, false, tagList);
        sb.append(" from ").append(tableName).append(" ");
        StatementParameters parameters = new StatementParameters();
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        if (!GeneralUtil.isEmpty(likeMap)) {
            SqlUtil.jointLikeCondition(sb,likeMap,parameters);
        }
        SqlUtil.jointGroupCondition(sb, true, tagList);
        SqlUtil.jointOrderCondition(sb, tagList, "asc");
        if (pageNo != null && pageSize != null) {
            SqlUtil.jointPagingCondition(sb, parameters, pageNo, pageSize);
        }
        dao.getListResultAsync(sb.toString(), parameters, dalHints);
    }


//    public void getRankAsync(Map<String, List<String>> inMap,
//                             DalHints dalHints) throws SQLException {
//        StringBuilder sb = new StringBuilder();
//        sb.append("select ranking_profit as rank from bus_1_2_rank_t_new ");
//        StatementParameters parameters = new StatementParameters();
//        SqlUtil.jointWhereCondition(sb, parameters, inMap);
//        dao.getListResultAsync(sb.toString(), parameters, dalHints);
//    }


    private String getOverallTargetTableName(String originLevel){
        //国内 大区  省份
        String tableName = "bus_1_2_region_config";
        if("三方".equals(originLevel)){
            tableName = "bus_1_2_three_result_config";
        }else if("景点".equals(originLevel)){
            tableName = "bus_1_2_examinee_config_new";
        }
        return tableName;
    }


    //1.获取某个月或者季的目标
    //2.获取某年所有月或者季的目标
    public void getOveralltargetDataAsync(String originLevel,
                                          Map<String, List<String>> inMap,
                                          Map<String, List<String>> notInMap,
                                          List<String> tagList,
                                          DalHints dalHints) throws SQLException {
        String tableName = getOverallTargetTableName(originLevel);
        StringBuilder sb = new StringBuilder();
        StatementParameters parameters = new StatementParameters();
        sb.append("select ");
        if (!GeneralUtil.isEmpty(tagList)){
            SqlUtil.jointGroupCondition(sb, false, tagList);
            sb.append(",");
        }
        sb.append("sum(ttd_trgt_profit) as ttd_trgt_profit ");
        sb.append(" from ").append(tableName).append(" ");
        SqlUtil.jointWhereCondition(sb, parameters, inMap);
        if (!GeneralUtil.isEmpty(notInMap)) {
            SqlUtil.jointNotInCondition(sb, parameters, notInMap);
        }
        if(!GeneralUtil.isEmpty(tagList)){
            SqlUtil.jointGroupCondition(sb, true, tagList);
        }
        dao.getListResultAsync(sb.toString(), parameters, dalHints);
    }
}
