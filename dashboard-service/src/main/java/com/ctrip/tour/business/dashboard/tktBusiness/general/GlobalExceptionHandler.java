package com.ctrip.tour.business.dashboard.tktBusiness.general;

import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.utils.ClickhouseLogUtil;
import com.ctrip.tour.business.dashboard.utils.HickWallLogUtil;
import com.ctrip.tour.business.dashboard.utils.MapperUtil;
import com.ctrip.train.tieyouflight.soa.exception.annotation.SOAExceptionHandler;
import com.ctriposs.baiji.rpc.server.HttpRequestWrapper;
import com.ctriposs.baiji.rpc.server.HttpResponseWrapper;
import com.dianping.cat.Cat;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.ExceptionHandler;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/8/23
 */
@SOAExceptionHandler
@Slf4j
public class GlobalExceptionHandler {

    @Autowired
    private RemoteConfig remoteConfig;

    @Autowired
    private LogInfo logInfo;

//    @ExceptionHandler({ConfigImportException.class, InputArgumentException.class, StrategyException.class})
//    public void handleCustomException(RuntimeException e) {
//        log.error("服务抛出了未处理异常", e);
//    }


    @ExceptionHandler(Exception.class)
    public void handleException(Exception e, HttpRequestWrapper request, HttpResponseWrapper response) {

        if(Cat.isOriginatedFromInfoSec()){   //忽略信安扫描
            log.info("isOriginatedFromInfoSec");
            return;
        }

        String stringRequest = MapperUtil.obj2Str(request.requestObject());
        String exceptionName = e.getClass().getSimpleName();
        Map<String, String> cTagMap = logInfo.createClickhouseTagMap(stringRequest, "", exceptionName, -1L);
        Map<String, String> hTapMap = logInfo.createHickWallTagMap(exceptionName);
        String scenario = remoteConfig.getConfigValue("scenario");
        ClickhouseLogUtil.logSendWithTag(cTagMap, scenario);
        //空指针异常需要特别关注
        if("NullPointerException".equals(exceptionName)){
            HickWallLogUtil.logSendWithTag("tour.bi.business.dashboard.nullpointer.count", hTapMap);
        }
        HickWallLogUtil.logSendWithTag("tour.bi.business.dashboard.error.count", hTapMap);
        log.error("服务抛出了未处理异常", e);
    }
}
