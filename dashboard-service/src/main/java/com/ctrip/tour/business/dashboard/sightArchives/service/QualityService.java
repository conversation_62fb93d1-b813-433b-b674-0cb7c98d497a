package com.ctrip.tour.business.dashboard.sightArchives.service;

import com.ctrip.soa._24922.*;

//服务质量
public interface QualityService {

    //履约质量 - 指标卡&缺陷原因占比饼图
    GetFulfillmentQualityMetricResponseType getFulfillmentQualityMetric(GetFulfillmentQualityMetricRequestType requestType);

    //履约质量 - 按缺陷原因下钻表格
    GetFulfillmentQualityTableResponseType getFulfillmentQualityTable(GetFulfillmentQualityTableRequestType requestType);

    //点评 - 当前点评分&点评趋势&好评关键词&差评关键词
    GetCommentMetricResponseType getCommentMetric(GetCommentMetricRequestType requestType);

    //服务 - 指标卡&高频问题Top5
    GetServiceMetricResponseType getServiceMetric(GetServiceMetricRequestType requestType);

    //投诉 - 指标卡&高频问题Top5
    GetComplaintMetricResponseType getComplaintMetric(GetComplaintMetricRequestType requestType);

    //景区各供应商质量情况
    GetVendorQualityTableResponseType getVendorQualityTable(GetVendorQualityTableRequestType requestType);



}
