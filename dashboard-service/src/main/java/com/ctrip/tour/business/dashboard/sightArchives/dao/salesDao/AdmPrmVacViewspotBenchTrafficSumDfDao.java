package com.ctrip.tour.business.dashboard.sightArchives.dao.salesDao;

import com.ctrip.soa._24922.DeliveryPage;
import com.ctrip.soa._27181.PreparedParameterBean;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.TktStarRocksDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.sql.Types;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class AdmPrmVacViewspotBenchTrafficSumDfDao {

    //广告投放情况  数仓侧设计文档：http://conf.ctripcorp.com/pages/viewpage.action?pageId=3669341013
    //数据源表: dw_diydb.adm_prm_vac_viewspot_bench_traffic_sum_df

    @Autowired
    private TktStarRocksDao tktStarRocksDao;

    public List<DeliveryPage> queryDeliveryPageList(String queryD, Long sightId, Boolean needSubSight, String startDate, String endDate, Integer pageNo, Integer pageSize) {
        //省份分布
        StringBuilder sql = new StringBuilder("select " +
                "imp_name" +
                ",begin_date" +
                ",end_date" +
                ",sum(exposurepv) as exposurepv" +  //曝光次数
                ",sum(exposureuv) as exposureuv" +  //曝光人数
                ",sum(clickpv) as clickpv" +  //点击次数
                ",sum(clickpv)/sum(exposurepv) as clk_rate" + //点击率
                ",sum(clickuv) as clickuv" +  //	点击用户量
                " from adm_prm_vac_viewspot_bench_traffic_sum_df");
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendSightId(parameters, sql, sightId, needSubSight);
        appendQueryD(parameters, sql, queryD);
        appendDateRange(parameters, sql, startDate, endDate);
        sql.append(" group by imp_name,begin_date,end_date");
        sql.append(" order by begin_date desc");
        appendLimit(parameters, sql, pageNo, pageSize);

        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(),parameters);
        } catch (SQLException e) {
            log.warn("queryDeliveryPageList error", e);
        }
        if(CollectionUtils.isEmpty(result)) {
            return new ArrayList<>();
        }else {
            List<DeliveryPage> deliveryPageList = new ArrayList<>();
            for(Map<String,Object> map : result){
                DeliveryPage deliveryPage = new DeliveryPage();
                deliveryPage.setDeliveryPageName((String) map.getOrDefault("imp_name", ""));
                deliveryPage.setDateRange((String) map.getOrDefault("begin_date", "") + " - " + (String) map.getOrDefault("end_date", ""));
                deliveryPage.setExposureCount(Math.toIntExact((Long) map.getOrDefault("exposurepv", 0L)));
                deliveryPage.setVisitorCount(Math.toIntExact((Long) map.getOrDefault("exposureuv", 0L)));
                deliveryPage.setClickCount(Math.toIntExact((Long) map.getOrDefault("clickpv", 0L)));
                deliveryPage.setClickRate((Double) map.getOrDefault("clk_rate", 0.0));
                deliveryPage.setClickedUserCount(Math.toIntExact((Long) map.getOrDefault("clickuv", 0L)));
                deliveryPageList.add(deliveryPage);
            }
            return deliveryPageList;
        }
    }

    public Integer queryDeliveryPageListTotalNum(String queryD, Long sightId, Boolean needSubSight, String startDate, String endDate) {
        //省份分布
        StringBuilder sql = new StringBuilder(
                "select count(1) as totalNum from " +
                    " (select " +
                    " imp_name" +
                    " ,begin_date" +
                    " ,end_date" +
                    " ,sum(exposurepv) as exposurepv" +  //曝光次数
                    " ,sum(exposureuv) as exposureuv" +  //曝光人数
                    " ,sum(clickpv) as clickpv" +  //点击次数
                    " ,sum(clickpv)/sum(exposurepv) as clk_rate" + //点击率
                    " ,sum(clickuv) as clickuv" +  //	点击用户量
                    " from adm_prm_vac_viewspot_bench_traffic_sum_df");
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendSightId(parameters, sql, sightId, needSubSight);
        appendQueryD(parameters, sql, queryD);
        appendDateRange(parameters, sql, startDate, endDate);
        sql.append(" group by imp_name,begin_date,end_date");
        sql.append(" order by begin_date desc");
        sql.append(") a");

        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(),parameters);
        } catch (SQLException e) {
            log.warn("queryDeliveryPageListTotalNum error", e);
        }
        if(CollectionUtils.isEmpty(result)) {
            return 0;
        }else {
            return Math.toIntExact((Long) result.get(0).getOrDefault("totalNum", 0L));
        }
    }

    private void appendSightId(List<PreparedParameterBean> parameters, StringBuilder sql, Long sightId, Boolean needSubSight){
        if(needSubSight){
            sql.append(" where parent_viewspotid = ?");
        }else {
            sql.append(" where viewspot_id = ?");
        }
        parameters.add(new PreparedParameterBean(String.valueOf(sightId), Types.BIGINT));
    }

    private void appendQueryD(List<PreparedParameterBean> parameters, StringBuilder sql, String queryD){
        sql.append(" and d = ?");
        parameters.add(new PreparedParameterBean(queryD, Types.VARCHAR));
    }

    private void appendDateRange(List<PreparedParameterBean> parameters, StringBuilder sql, String startDate, String endDate){
        sql.append(" and (begin_date <= ? and end_date >= ?) ");
        parameters.add(new PreparedParameterBean(endDate, Types.VARCHAR));
        parameters.add(new PreparedParameterBean(startDate, Types.VARCHAR));
    }

    private void appendLimit(List<PreparedParameterBean> parameters, StringBuilder sql, Integer pageNo, Integer pageSize){
        sql.append(" limit ?,?");
        parameters.add(new PreparedParameterBean(String.valueOf((pageNo-1)*pageSize), Types.INTEGER));
        parameters.add(new PreparedParameterBean(String.valueOf(pageSize), Types.INTEGER));
    }
}
