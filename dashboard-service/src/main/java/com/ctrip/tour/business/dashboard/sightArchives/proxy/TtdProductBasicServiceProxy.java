package com.ctrip.tour.business.dashboard.sightArchives.proxy;


import com.ctrip.tour.business.dashboard.sightArchives.bean.SaleUnitInfo;
import com.ctrip.tour.business.dashboard.utils.UserUtil;
import com.ctrip.tour.ttd.product.soa.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@Slf4j
public class TtdProductBasicServiceProxy {

    static TtdProductBasicServiceClient client = TtdProductBasicServiceClient.getInstance();

    public String getPropertyValueName(SaleUnitInfo saleUnitInfo) {

        GetFirstLevelSaleUnitInfoRequestType requestType = new GetFirstLevelSaleUnitInfoRequestType();
        requestType.setFirstLevelSaleUnitIds(Collections.singletonList(saleUnitInfo.getFirstSaleUnitId()));
        requestType.setLocale(UserUtil.getVbkLocale());
        requestType.setNoCache(false);
        FirstLevelSaleUnitInfoReturnOptionType returnOptionType = new FirstLevelSaleUnitInfoReturnOptionType();
        returnOptionType.setNeedFirstLevelSaleUnitProperties(true);
        returnOptionType.setNeedFirstLevelSaleUnitRelations(true);
        returnOptionType.setNeedSaleUnit(true);
        requestType.setReturnOption(returnOptionType);

        GetFirstLevelSaleUnitInfoResponseType responseType = null;
        try {
            log.info("getFirstLevelSaleUnitInfo requestType: {}", requestType);
            responseType = client.getFirstLevelSaleUnitInfo(requestType);
            log.info("getFirstLevelSaleUnitInfo responseType: {}", responseType);
        } catch (Exception e) {
            log.warn("getFirstLevelSaleUnitInfo failed, requestType: {}", requestType, e);
        }
        if(responseType == null || CollectionUtils.isEmpty(responseType.getFirstLevelSaleUnitInfoList())) {
            log.warn("getFirstLevelSaleUnitInfo responseType is null or empty, requestType: {}", requestType);
            return null;
        }

        List<FirstLevelSaleUnitInfoType> firstLevelSaleUnitInfoList = responseType.getFirstLevelSaleUnitInfoList();
        for(FirstLevelSaleUnitInfoType firstLevelSaleUnitInfo : firstLevelSaleUnitInfoList) {
            if (firstLevelSaleUnitInfo.getFirstLevelSaleUnitId() != null
                    && CollectionUtils.isNotEmpty(firstLevelSaleUnitInfo.getProperties())
                    && CollectionUtils.isNotEmpty(firstLevelSaleUnitInfo.getSaleUnitRelations())) {
                Long proId = firstLevelSaleUnitInfo.getSaleUnitRelations().stream()
                        .filter(saleUnitRelationType -> Objects.equals(saleUnitRelationType.getSaleUnitId(), saleUnitInfo.getSecondSaleUnitId()))
                        .map(saleUnitRelationType -> saleUnitRelationType.getSaleUnit().getPropertyId())
                        .findFirst().orElse(null);
                return firstLevelSaleUnitInfo.getProperties().stream()
                        .filter(propertyType -> Objects.equals(propertyType.getPropertyId(), proId))
                        .map(FirstLevelSaleUnitPropertyType::getPropertyValueName)
                        .findFirst().orElse(null);
            }
        }

        return null;
    }

    public Map<Long,String> getFirstLevelSaleUnitInfo(List<Long> firstLevelSaleUnitIds) {

        GetFirstLevelSaleUnitInfoRequestType requestType = new GetFirstLevelSaleUnitInfoRequestType();
        requestType.setFirstLevelSaleUnitIds(firstLevelSaleUnitIds);
        requestType.setLocale(UserUtil.getVbkLocale());
//        requestType.setDisableTranslate(false);
        requestType.setNoCache(true);
        FirstLevelSaleUnitInfoReturnOptionType returnOptionType = new FirstLevelSaleUnitInfoReturnOptionType();
        returnOptionType.setNeedFirstLevelSaleUnit(true);
        requestType.setReturnOption(returnOptionType);

        GetFirstLevelSaleUnitInfoResponseType responseType = null;
        try {
            log.info("getFirstLevelSaleUnitInfo requestType: {}", requestType);
            responseType = client.getFirstLevelSaleUnitInfo(requestType);
            log.info("getFirstLevelSaleUnitInfo responseType: {}", responseType);
        } catch (Exception e) {
            log.warn("getFirstLevelSaleUnitInfo failed, requestType: {}", requestType, e);
        }
        if(responseType == null || CollectionUtils.isEmpty(responseType.getFirstLevelSaleUnitInfoList())) {
            log.warn("getFirstLevelSaleUnitInfo responseType is null or empty, requestType: {}", requestType);
            return new HashMap<>();
        }

        Map<Long,String> map = new HashMap<>();
        List<FirstLevelSaleUnitInfoType> firstLevelSaleUnitInfoList = responseType.getFirstLevelSaleUnitInfoList();
        for(FirstLevelSaleUnitInfoType firstLevelSaleUnitInfo : firstLevelSaleUnitInfoList) {
            if (firstLevelSaleUnitInfo.getFirstLevelSaleUnitId() != null && StringUtils.isNotBlank(firstLevelSaleUnitInfo.getSaleUnit().getName())) {
                map.put(firstLevelSaleUnitInfo.getFirstLevelSaleUnitId(), firstLevelSaleUnitInfo.getSaleUnit().getName());
            }
        }

        return map;
    }

    /**
     * 获取一二级票种相关翻译数据
     * @param saleUnitIds
     * @return
     */
    public Map<Long,String> getSaleUnitInfo(List<Long> saleUnitIds){
        GetSaleUnitInfoRequestType requestType = new GetSaleUnitInfoRequestType();
        requestType.setLocale(UserUtil.getVbkLocale());
        requestType.setNoCache(true);
        requestType.setSaleUnitIds(saleUnitIds);
        SaleUnitInfoReturnOptionType returnOptionType = new SaleUnitInfoReturnOptionType();
        returnOptionType.setNeedSaleUnit(true);
        returnOptionType.setNeedSaleUnitResourceRelations(true);
        requestType.setReturnOption(returnOptionType);

        GetSaleUnitInfoResponseType responseType;

        try {
            responseType = client.getSaleUnitInfo(requestType);
        } catch (Exception e) {
            log.warn("getSaleUnitInfo failed, requestType: {}", requestType, e);
            return new HashMap<>();
        }
        if (responseType == null || CollectionUtils.isEmpty(responseType.getSaleUnitInfoList())) {
            log.warn("getSaleUnitInfo responseType is null or empty, requestType: {}", requestType);
            return new HashMap<>();
        }

        List<SaleUnitInfoType> saleUnitInfoList = responseType.getSaleUnitInfoList();
        Map<Long, String> map = new HashMap<>();

        Map<Long, Long> saleIdProductIdMap = new HashMap<>();
        List<Long> productIds = new ArrayList<>();
        for (SaleUnitInfoType saleUnitInfoType : saleUnitInfoList) {
            if (saleUnitInfoType.getSaleUnitId() != null &&
                    StringUtils.isNotBlank(saleUnitInfoType.getSaleUnit().getName()) &&
                    CollectionUtils.isNotEmpty(saleUnitInfoType.getResourceRelations())) {

                Long productId = Optional.ofNullable(saleUnitInfoType.getResourceRelations())
                        .orElseGet(Collections::emptyList)
                        .stream()
                        .filter(resourceRelation -> resourceRelation.getProductId() != null)
                        .findFirst()
                        .map(SaleUnitRelationType::getProductId).orElse(0L);

                saleIdProductIdMap.put(saleUnitInfoType.getSaleUnitId(), productId);
                productIds.add(productId);
            }
        }
        Map<Long, String> productTranslateInfoMap = getProductInfo(productIds);

        for (SaleUnitInfoType saleUnitInfoType : saleUnitInfoList) {
            if (saleUnitInfoType.getSaleUnitId() != null &&
                    StringUtils.isNotBlank(saleUnitInfoType.getSaleUnit().getName()) &&
                    CollectionUtils.isNotEmpty(saleUnitInfoType.getResourceRelations())) {
                Long productId = saleIdProductIdMap.get(saleUnitInfoType.getSaleUnitId());
                if (productTranslateInfoMap.containsKey(productId)) {
                    map.put(saleUnitInfoType.getSaleUnitId(), productTranslateInfoMap.getOrDefault(productId, saleUnitInfoType.getSaleUnit().getName()));
                }
            }
        }
        return map;

    }

    public Map<Long,String> getProductInfo(List<Long> productIds){
        if(CollectionUtils.isEmpty(productIds)){
            return new HashMap<>();
        }
        GetProductInfoRequestType requestType = new GetProductInfoRequestType();
        requestType.setProductIds(productIds);
        requestType.setLocale(UserUtil.getVbkLocale());
        requestType.setNoCache(true);
        ProductReturnOptionType productReturnOptionType = new ProductReturnOptionType();
        productReturnOptionType.setNeedBasicInfo(true);
        requestType.setReturnOption(productReturnOptionType);

        GetProductInfoResponseType responseType = null;
        try {
            log.info("getProductInfo requestType: {}", requestType);
            responseType = client.getProductInfo(requestType);
            log.info("getProductInfo responseType: {}", responseType);
        } catch (Exception e) {
            log.warn("getProductInfo failed, requestType: {}", requestType, e);
            return new HashMap<>();
        }
        if (responseType == null || CollectionUtils.isEmpty(responseType.getProductInfoList())) {
            log.warn("getProductInfo responseType is null or empty, requestType: {}", requestType);
            return new HashMap<>();
        }
        List<ProductInfoType> productInfoList = responseType.getProductInfoList();

        Map<Long,String> map = new HashMap<>();
        for (ProductInfoType productInfo : productInfoList) {
            if(productInfo.getProductId()!=null && StringUtils.isNotBlank(productInfo.getBasicInfo().getName())){
                map.put(productInfo.getProductId(), productInfo.getBasicInfo().getName());
            }
        }

         return map;

    }


//    public Map<Long,String> getScenicSpotInfo(List<Long> scenicSpotIds){
//
//        GetScenicSpotInfoRequestType requestType = new GetScenicSpotInfoRequestType();
//        requestType.setLocale(UserUtil.getVbkLocale());
//        requestType.setScenicSpotIds(scenicSpotIds);
//        ScenicSpotReturnOptionType returnOptionType = new ScenicSpotReturnOptionType();
//        returnOptionType.setNeedBasicInfo(true);
//        requestType.setReturnOption(returnOptionType);
//        GetScenicSpotInfoResponseType responseType;
//        try {
//            responseType = client.getScenicSpotInfo(requestType);
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
//        List<ScenicSpotInfoType> scenicSpotInfos = responseType.getScenicSpotInfos();
//
//        Map<Long,String> map = new HashMap<>();
//        for(ScenicSpotInfoType scenicSpotInfo : scenicSpotInfos){
//            if (scenicSpotInfo.getScenicSpotId() != null) {
//                map.put(scenicSpotInfo.getScenicSpotId(), scenicSpotInfo.getScenicSpotBasicInfo().get);
//            } else {
//                log.warn("Scenic Spot ID is null for scenic spot: {}", scenicSpotInfo);
//            }
//        }
//    }


}
