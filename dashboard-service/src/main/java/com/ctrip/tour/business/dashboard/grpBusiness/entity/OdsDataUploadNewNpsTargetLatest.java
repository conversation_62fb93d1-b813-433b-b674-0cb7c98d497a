package com.ctrip.tour.business.dashboard.grpBusiness.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Sensitive;
import com.ctrip.platform.dal.dao.annotation.Type;
import java.sql.Types;
import java.math.BigDecimal;
import java.sql.Timestamp;

import com.ctrip.platform.dal.dao.DalPojo;

/**
 * <AUTHOR>
 * @date 2025-04-08
 */
@Entity
@Database(name = "ttdreportdb_dalcluster")
@Table(name = "ods_data_upload_new_nps_target_latest")
public class OdsDataUploadNewNpsTargetLatest implements DalPojo {

    /**
     * 主键
     */
    @Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long id;

    /**
     * 区域类型名称
     */
	@Column(name = "regiontype_name")
	@Type(value = Types.VARCHAR)
	private String regiontypeName;

    /**
     * 区域名称
     */
	@Column(name = "domain")
	@Type(value = Types.VARCHAR)
	private String domain;

    /**
     * 业务大区
     */
	@Column(name = "business_region_name")
	@Type(value = Types.VARCHAR)
	private String businessRegionName;

    /**
     * 评级区域
     */
	@Column(name = "grade_region")
	@Type(value = Types.VARCHAR)
	private String gradeRegion;

    /**
     * 月份
     */
	@Column(name = "month")
	@Type(value = Types.VARCHAR)
	private String month;

    /**
     * 目标值
     */
	@Column(name = "target_val")
	@Type(value = Types.DECIMAL)
	private BigDecimal targetVal;

    /**
     * 更新时间
     */
	@Column(name = "datachange_lasttime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getRegiontypeName() {
		return regiontypeName;
	}

	public void setRegiontypeName(String regiontypeName) {
		this.regiontypeName = regiontypeName;
	}

	public String getDomain() {
		return domain;
	}

	public void setDomain(String domain) {
		this.domain = domain;
	}

	public String getBusinessRegionName() {
		return businessRegionName;
	}

	public void setBusinessRegionName(String businessRegionName) {
		this.businessRegionName = businessRegionName;
	}

	public String getGradeRegion() {
		return gradeRegion;
	}

	public void setGradeRegion(String gradeRegion) {
		this.gradeRegion = gradeRegion;
	}

	public String getMonth() {
		return month;
	}

	public void setMonth(String month) {
		this.month = month;
	}

	public BigDecimal getTargetVal() {
		return targetVal;
	}

	public void setTargetVal(BigDecimal targetVal) {
		this.targetVal = targetVal;
	}

	public Timestamp getDatachangeLasttime() {
		return datachangeLasttime;
	}

	public void setDatachangeLasttime(Timestamp datachangeLasttime) {
		this.datachangeLasttime = datachangeLasttime;
	}

}