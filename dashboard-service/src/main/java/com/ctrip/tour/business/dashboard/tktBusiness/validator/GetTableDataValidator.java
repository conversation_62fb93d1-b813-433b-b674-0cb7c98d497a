package com.ctrip.tour.business.dashboard.tktBusiness.validator;

import com.ctrip.soa._24922.DrillDownFilter;
import com.ctrip.soa._24922.GetTableDataRequestType;
import com.ctrip.soa._24922.GetTableDataResponseType;
import com.ctrip.soa._24922.TimeFilter;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.InputArgumentException;
import com.ctrip.tour.business.dashboard.utils.MapperUtil;
import com.ctrip.tour.business.dashboard.utils.ParamterCheckUtil;
import com.ctrip.train.tieyouflight.soa.validation.validator.BusinessConstraintValidator;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/9/8
 */
@Component
public class GetTableDataValidator extends BusinessConstraintValidator<GetTableDataRequestType, GetTableDataResponseType> {
    @Override
    public GetTableDataResponseType validateBusiness(GetTableDataRequestType request) {
        String domainName = request.getDomainName();
        if (!ParamterCheckUtil.checkDomainName(domainName)) {
            throw new InputArgumentException("输入了非法的domainName:" + MapperUtil.obj2Str(request));
        }
        TimeFilter timeFilter = request.getTimeFilter();
        if (!ParamterCheckUtil.checkTimeFilterWithoutTimeFrame(timeFilter)) {
            throw new InputArgumentException("输入了非法的timeFilter:" + MapperUtil.obj2Str(request));
        }
        String queryType = request.getQueryType();
        if(!"overall".equals(queryType)){
            DrillDownFilter drillDownFilter = request.getDrillDownFilter();
            if(!ParamterCheckUtil.checkDrillDownFilter(drillDownFilter)){
                throw new InputArgumentException("输入了非法的drillDownFilter:" + MapperUtil.obj2Str(request));
            }
        }
        return null;
    }
}
