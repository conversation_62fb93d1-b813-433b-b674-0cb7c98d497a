package com.ctrip.tour.business.dashboard.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;

/**
 * 数值格式化工具类
 *
 * <AUTHOR>
 */
public class NumberFormatUtil {

    /**
     * 将小数转换为百分比格式，保留两位小数，四舍五入
     * 例如: 2.4256244995200795 -> "242.56%"
     *
     * @param value 原始数值（小数形式）
     * @return 格式化后的百分比字符串
     */
    public static String formatPercentage(double value) {
        if (Double.isNaN(value) || Double.isInfinite(value)) {
            return "0.00";
        }

        // 使用BigDecimal避免精度问题
        BigDecimal bd = new BigDecimal(String.valueOf(value));

        // 乘以100转换为百分比
        BigDecimal percentage = bd.multiply(new BigDecimal("100"));

        // 四舍五入保留两位小数
        BigDecimal rounded = percentage.setScale(2, RoundingMode.HALF_UP);

        return rounded.toString();
    }

    /**
     * 将小数转换为百分比格式，使用DecimalFormat
     * 例如: 2.4256244995200795 -> "242.56%"
     *
     * @param value 原始数值（小数形式）
     * @return 格式化后的百分比字符串
     */
    public static String formatPercentageWithDecimalFormat(double value) {
        if (Double.isNaN(value) || Double.isInfinite(value)) {
            return "0.00%";
        }

        DecimalFormat percentFormat = new DecimalFormat("0.00%");
        return percentFormat.format(value);
    }


    public static void main(String[] args) {
        System.out.println(formatPercentage(-0.07349298100743187));
    }

}
