package com.ctrip.tour.business.dashboard.tktBusiness.bean;

import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardExamineeConfigV2;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardOverseaExamineeConfig;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Map;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ExamineConfigBean {

    //当前年
    String year;
    //当前月 dateType为month时传入
    String month;
    //当前季 dateType为quarter时传入
    String quarter;
    //存储时间的map 仅部分指标需要
    //currentTime 当前时间
    //lastyearTime 同比去年时间
    //2019Time 同比2019年时间
    Map<String, String> timeMap;
    //是不是最新的考核周期(对月而言 传入的是不是当前月 对季而言 传入的是不是当前季)
    Boolean isLastestPeriod;
    //是最新考核周期的情况下(计算同比需要裁剪的时间 仅部分指标需要)
    Map<String, String> limitTimeMap;
    //当前考核周期配置
    BusinessDashboardExamineeConfigV2 businessDashboardExamineeConfigV2;
    //当前考核周期海外的考核配置
    BusinessDashboardOverseaExamineeConfig businessDashboardOverseaExamineeConfig;
    //日期类型
    String dateType;
}
