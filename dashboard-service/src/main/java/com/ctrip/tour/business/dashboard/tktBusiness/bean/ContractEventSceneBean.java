package com.ctrip.tour.business.dashboard.tktBusiness.bean;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.util.Map;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ContractEventSceneBean {
    public String sceneType;    // 场景类型的编号
    public boolean isSplitScene; // true表示需要根据上游的场景进行子场景的切分
    public String className;    // 当前场景数据传输的类名，因为要通用，所以这里需要反射调用
    public int splitType;       // 场景切分方式，1：表示按照productid=4|11=供应商上货，其他=业务上货
    public int messageType;     // 消息类型，1：通知[需要下游聚合]；2：任务[不聚合直接发送]; 3: 通知[上游以聚合好，不需要下游聚合]
    public String title;        // 发送内容的标题
    public String taskType;     // 发送消息需要
    public String taskName;     // 发送消息需要
    public String eventType;    // 发送消息需要
    public ContractEventAggTimeBean contractEventAggTimeBean;     // 发送内容的聚合时间范围；当messageType=1时需要该值；因为messageType=2和3时是即时发送
    public ContractEventContentSceneBean serviceNumInfo; // 服务号展示内容
    public ContractEventContentSceneBean detailInfo;     // 详情页展示内容
    public Map<String, ContractEventSceneBean> splitSceneMap; // 如果切分场景，子场景依然是这个格式; String:场景的分类tag，根据这个值对拆分后的结果进行映射，supplier：供应商上货；business：业务上货

}
