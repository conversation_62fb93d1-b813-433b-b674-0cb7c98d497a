package com.ctrip.tour.business.dashboard.soa;

import com.ctriposs.baiji.rpc.server.BaijiListener;
import com.ctriposs.baiji.rpc.server.HostConfig;
import com.ctriposs.baiji.rpc.server.filter.RequestFilter;
import com.ctriposs.baiji.rpc.server.filter.ResponseFilter;
import com.ctriposs.baiji.rpc.server.filter.ServiceExecutionFilter;
import org.springframework.stereotype.Component;
import tour.auth.soa.filter.AuthTourAccountFilter;

/**
 * <AUTHOR>
 * @date 2022/7/20
 */
@Component
public class SoaBaijiListener extends BaijiListener {

    private final RequestFilter requestFilter;

    private final ResponseFilter responseFilter;

    private final ServiceExecutionFilter serviceExecutionFilter;

    public SoaBaijiListener(RequestFilter requestFilter,
                            ResponseFilter responseFilter,
                            ServiceExecutionFilter serviceExecutionFilter) {
        super(BIBusinessDashboardServiceImpl.class);
        this.requestFilter = requestFilter;
        this.responseFilter = responseFilter;
        this.serviceExecutionFilter = serviceExecutionFilter;
    }

    @Override
    protected void configure(HostConfig hostConfig){

        super.configure(hostConfig);

        // 设置缓存请求流，如果要在 PreRequestFilter 或 RequestFilter 中获取请求流，那么需要设置为 true
        // 因为原生的流只能读一次，需要先缓存起来，否则 Filter 读取了请求流，会导致真正做反序列化时获取不到数据。
        hostConfig.setCacheRequestStream(true);

        AuthTourAccountFilter.registerFilter(hostConfig);

        // 此 Filter 将在请求对象反序列化之后，服务实现代码执行之前执行
        hostConfig.addRequestFilter(requestFilter);

        // 此 Filter 将在服务实现代码执行之后，在响应对象序列化之前执行
        hostConfig.addResponseFilter(responseFilter);

        //业务逻辑wrapper  用来粗粒度监控方法耗时
        hostConfig.addServiceExecutionFilter(serviceExecutionFilter);
    }
}
