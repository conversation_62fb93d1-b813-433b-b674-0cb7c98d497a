package com.ctrip.tour.business.dashboard.tktBusiness.strategy.domestic.impl;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.grpBusiness.enums.DomesticMetricEnum;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.domestic.DomesticMetricCalStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

@Component
public class Bus14DomesticMetricStrategy implements DomesticMetricCalStrategy {

    @Autowired
    @Qualifier("bus11DomesticMetricStrategy")
    private DomesticMetricCalStrategy bus11DomesticMetricStrategy;

    @Autowired
    @Qualifier("bus12DomesticMetricStrategy")
    private  DomesticMetricCalStrategy bus12DomesticMetricStrategy;

    @Override
    public Future<DomesticMetricDetailInfo> getSingleMetricCardData(String domainName, TimeFilter timeFilter, List<MetricInfoBean> metricInfoBean, String d, Boolean isFirst, Integer businessId) throws Exception {
        DomesticMetricDetailInfo metricDetailInfo = new DomesticMetricDetailInfo();
        metricDetailInfo.setMetricCode(DomesticMetricEnum.getCodeById(getMetricName()));
        List<DomesticMetricDetailInfo> subList = new ArrayList<>();
        subList.add(bus11DomesticMetricStrategy.getSingleMetricCardData(domainName, timeFilter, metricInfoBean.stream().filter(e -> "11".equals(e.getMetric())).collect(Collectors.toList()), d, isFirst, businessId).get());
        subList.add(bus12DomesticMetricStrategy.getSingleMetricCardData(domainName, timeFilter, metricInfoBean.stream().filter(e -> "12".equals(e.getMetric())).collect(Collectors.toList()), d, isFirst, businessId).get());
        metricDetailInfo.setSubMetricDetailInfoList(subList);
        return new AsyncResult<>(metricDetailInfo);
    }

    @Override
    public GetDomesticMetricTrendDataResponseType getSingleTrendlineData(GetDomesticMetricTrendDataRequestType request, String d) throws Exception {
        return null;
    }

    @Override
    public GetDomesticTableDataResponseType getSingleTableData(GetDomesticTableDataRequestType request, MetricInfoBean metricInfoBean, String d) throws Exception {
        return null;
    }

    @Override
    public GetDomesticDrillDownBaseInfoResponseType getSingleDrillDownBaseInfo(GetDomesticDrillDownBaseInfoRequestType request, MetricInfoBean metricInfoBean, String d) throws Exception {
        return null;
    }
    @Override
    public GetFirstPageDomesticMetricCardDrillDataResponseType getFirstPageDomesticMetricCardDrillData(
            GetFirstPageDomesticMetricCardDrillDataRequestType request,MetricInfoBean metricInfoBean, String d) throws Exception{
        return null;
    }
    @Override
    public Integer getMetricName() {
        return 14;
    }
}
