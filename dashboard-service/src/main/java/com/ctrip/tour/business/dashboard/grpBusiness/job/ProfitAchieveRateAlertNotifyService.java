package com.ctrip.tour.business.dashboard.grpBusiness.job;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.tour.business.dashboard.grpBusiness.dao.mysql.DimOrdGrpAchv2025PersonTrgtDao;
import com.ctrip.tour.business.dashboard.grpBusiness.dao.starrocks.StarRocksCommonDao;
import com.ctrip.tour.business.dashboard.grpBusiness.entity.DimOrdGrpAchv2025PersonTrgt;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.group.workbenchsvc.contract.StructuredTableInfoType;
import com.ctrip.tour.group.workbenchsvc.contract.StructuredTableRowInfoType;
import com.ctrip.tour.rights.client.GroupWorkbenchServiceClient;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2025/3/25
 */
@Service
@Slf4j
public class ProfitAchieveRateAlertNotifyService extends CommonAlertNotifyService {

    private static final String TASK_GRP_PROFIT_ALERT = "TASK_BENEFIT_ACHIEVEMENT_ALERT";
    private static final String EVENT_GRP_PROFIT_ALERT = "EVENT_BENEFIT_ACHIEVEMENT_ALERT";
    private static final String EVENT_GRP_PROFIT_ALERT_STRUCTURED_TABLE = "EVENT_BENEFIT_ACHIEVEMENT_ALERT_STRUCTURED_TABLE";

    @Autowired
    private StarRocksCommonDao starRocksCommonDao;

    @Autowired
    private DimOrdGrpAchv2025PersonTrgtDao achv2025PersonTrgtDao;

    private static String NOTIFY_EVENT_EMPCODES = "notify.event.empcodes";

    private GroupWorkbenchServiceClient client = GroupWorkbenchServiceClient.getInstance();

    @Autowired
    private RemoteConfig remoteConfig;

    public void handleMultipriceNotify() throws Exception {

        String whiteEmpCodesStr = remoteConfig.getExternalConfig(NOTIFY_EVENT_EMPCODES);

        List<String> whiteEmpCodes = Lists.newArrayList();
        if (StringUtils.isNotBlank(whiteEmpCodesStr)) {
            whiteEmpCodes = Splitter.on(",").splitToList(whiteEmpCodesStr);
        }

        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        Map<String, BigDecimal> allPmEidTarget = getAllPmEidTarget();

        String empcodeQueryStr = Joiner.on(",").join(allPmEidTarget.keySet().stream().map(empcode -> "'" + empcode + "'").collect(Collectors.toList()));

        int month = YearMonth.now().getMonth().getValue();

        String format = String.format("%02d", month);

        String sql = "SELECT sum(suc_profit) as sumProfit, pm_eid as pmEid\n" +
                "FROM adm_ord_grp_work_platform_prdt_df\n" +
                "WHERE partition_d = '" + LocalDate.now().format(dtf) + "'\n" +
                "    AND dep_month = '" + format + "'\n" +
                "    AND dep_year = '2025'\n" +
                "    AND sub_bu_type = '跟团游'\n" +//NOSONAR
                "    and pm_eid in (" + empcodeQueryStr + ")\n" +
                "GROUP BY pm_eid";

        List<Map<String, Object>> resultList = starRocksCommonDao.query(sql, Maps.newHashMap());
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }

        List<String> finalWhiteEmpCodes = whiteEmpCodes;
         resultList.stream()
                .filter(pw -> CollectionUtils.isEmpty(finalWhiteEmpCodes) || (Objects.nonNull(pw) && finalWhiteEmpCodes.contains((String) pw.get("pm_eid"))))
                .forEach( pw -> {

            StructuredTableRowInfoType rowInfoType = new StructuredTableRowInfoType();

            Double sumProfit = (Double) pw.get("sumProfit");
            String pmEid = (String) pw.get("pmEid");

            BigDecimal targetVal = allPmEidTarget.get(pmEid);

            Double achRate = BigDecimal.valueOf(sumProfit).divide(targetVal, RoundingMode.CEILING).doubleValue();

            List<String> colList = Lists.newArrayList( getPrettyDataStr(sumProfit), getDataRatioStr(achRate));
            rowInfoType.setColList(colList);
            StructuredTableInfoType structuredTableInfoType = new StructuredTableInfoType();
            structuredTableInfoType.setRowList(Lists.newArrayList(rowInfoType));
            structuredTableInfoType.setHeaderList(Lists.newArrayList("达成毛利", "毛利达成率"));//NOSONAR
            String content = "毛利达成率指标通知，请关注达成情况。";//NOSONAR
            List<String> tpInfos = Lists.newArrayList("毛利达成率指标通知，请关注达成情况。");//NOSONAR
            notifyEmp(pmEid, structuredTableInfoType, "毛利达成率通知",//NOSONAR
                    TASK_GRP_PROFIT_ALERT, EVENT_GRP_PROFIT_ALERT, EVENT_GRP_PROFIT_ALERT_STRUCTURED_TABLE, content, tpInfos, "1");//NOSONAR
        });


    }

    private Map<String, BigDecimal> getAllPmEidTarget() throws SQLException {
        int month = YearMonth.now().getMonth().getValue();
        String format = String.format("%02d", month);
        List<DimOrdGrpAchv2025PersonTrgt> trgts = achv2025PersonTrgtDao.query("dep_month=?", new DalHints(), format + "");
        return trgts.stream().collect(Collectors.toMap(DimOrdGrpAchv2025PersonTrgt::getPmLvl3No
                , DimOrdGrpAchv2025PersonTrgt::getTargetProfit, BigDecimal::add));
    }

}
