package com.ctrip.tour.business.dashboard.tktBusiness.bean;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class OverseaMetricInfoWithMetricBean {

    //年份
    private String year;
    //季度
    private String quarter;
    //邮箱前缀
    private String domainName;
    //考核类型
    private String buType;
    //指标
    private String metric;
    //目的地考核层级
    private String destinationLevel;
    //目的地考核范围
    private String destinationRange;
}
