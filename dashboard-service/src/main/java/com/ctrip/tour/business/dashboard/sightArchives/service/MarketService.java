package com.ctrip.tour.business.dashboard.sightArchives.service;

import com.ctrip.soa._24922.GetLocationHeatForecastRequestType;
import com.ctrip.soa._24922.GetLocationHeatForecastResponseType;
import com.ctrip.soa._24922.GetPopularSightsRequestType;
import com.ctrip.soa._24922.GetPopularSightsResponseType;

//市场趋势
public interface MarketService {

    //所在地热度预测
    GetLocationHeatForecastResponseType getLocationHeatForecast(GetLocationHeatForecastRequestType requestType);

    //行业动态 - 新开业景点top5、热门景点top5
    GetPopularSightsResponseType getPopularSights(GetPopularSightsRequestType requestType);



}
