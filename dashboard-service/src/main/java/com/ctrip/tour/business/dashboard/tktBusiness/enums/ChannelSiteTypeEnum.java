package com.ctrip.tour.business.dashboard.tktBusiness.enums;

public enum ChannelSiteTypeEnum {
    CHANNEL("渠道"),//NOSONAR
    SITE("站点");//NOSONAR

    private String name;

    ChannelSiteTypeEnum(String name) {
        this.name = name;
    }


    public String getName() {
        return name;
    }

    public static ChannelSiteTypeEnum getByCode(String code) {
        for (ChannelSiteTypeEnum buType : ChannelSiteTypeEnum.values()) {
            if (buType.getName().equals(code)) {
                return buType;
            }
        }
        return null;
    }
}
