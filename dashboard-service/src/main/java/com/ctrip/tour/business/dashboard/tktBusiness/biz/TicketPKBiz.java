package com.ctrip.tour.business.dashboard.tktBusiness.biz;

import com.ctrip.ottd.product.background.openapi.external.GetMixProductPreviewStatusRequestType;
import com.ctrip.ottd.product.background.openapi.external.GetMixProductPreviewStatusResponseType;
import com.ctrip.soa._24922.GetPreviewInfoRequestType;
import com.ctrip.soa._24922.GetPreviewInfoResponseType;
import com.ctrip.ttd.vendor.soa.GetTicketPKBestStatusRequestType;
import com.ctrip.ttd.vendor.soa.GetTicketPKBestStatusResponseType;
import com.ctrip.ttd.vendor.soa.GetTicketPKDefectOrderIdRequestType;
import com.ctrip.ttd.vendor.soa.GetTicketPKDefectOrderIdResponseType;
import com.ctrip.ttd.vendor.soa.GetTicketPKDefectOverallDataRequestType;
import com.ctrip.ttd.vendor.soa.GetTicketPKDefectOverallDataResponseType;
import com.ctrip.ttd.vendor.soa.GetTicketPKDefectTableDataRequestType;
import com.ctrip.ttd.vendor.soa.GetTicketPKDefectTableDataResponseType;
import com.ctrip.ttd.vendor.soa.GetTicketPKEnumDataRequestType;
import com.ctrip.ttd.vendor.soa.GetTicketPKEnumDataResponseType;
import com.ctrip.ttd.vendor.soa.GetTicketPKQualificationRequestType;
import com.ctrip.ttd.vendor.soa.GetTicketPKQualificationResponseType;
import com.ctrip.ttd.vendor.soa.GetTicketPKResourceRankingRequestType;
import com.ctrip.ttd.vendor.soa.GetTicketPKResourceRankingResponseType;
import com.ctrip.ttd.vendor.soa.GetTicketPKSaleunitRankingRequestType;
import com.ctrip.ttd.vendor.soa.GetTicketPKSaleunitRankingResponseType;
import com.ctrip.ttd.vendor.soa.GetTicketPKScheduleCalendarRequestType;
import com.ctrip.ttd.vendor.soa.GetTicketPKScheduleCalendarResponseType;
import com.ctrip.ttd.vendor.soa.GetTicketPKTableRequestType;
import com.ctrip.ttd.vendor.soa.GetTicketPKTableResponseType;

public interface TicketPKBiz {
    GetTicketPKEnumDataResponseType getTicketPKEnumData(GetTicketPKEnumDataRequestType getTicketPKEnumDataRequestType) throws Exception ;

    GetTicketPKSaleunitRankingResponseType getTicketPKSaleunitRanking(GetTicketPKSaleunitRankingRequestType getTicketPKSaleunitRankingRequestType) throws Exception ;

    GetTicketPKResourceRankingResponseType getTicketPKResourceRanking(GetTicketPKResourceRankingRequestType getTicketPKResourceRankingRequestType) throws Exception ;

    GetTicketPKScheduleCalendarResponseType getTicketPKScheduleCalendar(GetTicketPKScheduleCalendarRequestType getTicketPKScheduleCalendarRequestType) throws Exception ;

    GetTicketPKQualificationResponseType getTicketPKQualification(GetTicketPKQualificationRequestType getTicketPKQualificationRequestType) throws Exception ;

    GetTicketPKTableResponseType getTicketPKTable(GetTicketPKTableRequestType getTicketPKTableRequestType) throws Exception ;

    GetTicketPKBestStatusResponseType getTicketPKBestStatus(GetTicketPKBestStatusRequestType getTicketPKBestStatusRequestType) throws Exception ;

    GetTicketPKDefectOverallDataResponseType getTicketPKDefectOverallData(GetTicketPKDefectOverallDataRequestType getTicketPKDefectOverallDataRequestType) throws Exception ;

    GetTicketPKDefectTableDataResponseType getTicketPKDefectTableData(GetTicketPKDefectTableDataRequestType getTicketPKDefectTableDataRequestType) throws Exception ;

    GetTicketPKDefectOrderIdResponseType getTicketPKDefectOrderId(GetTicketPKDefectOrderIdRequestType getTicketPKDefectOrderIdRequestType) throws Exception ;

    GetPreviewInfoResponseType getPreviewInfo(GetPreviewInfoRequestType request) throws Exception;

    GetMixProductPreviewStatusResponseType getMixProductPreviewStatus(GetMixProductPreviewStatusRequestType request) throws Exception;
}
