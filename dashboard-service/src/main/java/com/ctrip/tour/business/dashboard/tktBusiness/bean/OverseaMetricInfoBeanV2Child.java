package com.ctrip.tour.business.dashboard.tktBusiness.bean;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
public class OverseaMetricInfoBeanV2Child {

    //季度
    private String quarters;
    //目的地考核层级
    private List<String> destinationLevelList;
    //目的地考核范围
    private List<String> destinationRangeList;
    //目的地匹配的考核范围Map，key为考核层级，value考核层级对应的考核范围
    private Map<String, List<String>> destinationLevelMap;
}
