package com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.bean;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class DimOrdTtdPersonTargetParamBean {
    String d;
    //业务线
    private List<String> buType;
    //邮箱前缀
    private List<String> domainName;
    //考核年
    private String examineYear;
    //考核季
    private List<String> examineQuater;
    //C/T站
    private List<String> CT;
    //考核指标类型
    private String examineMetricType;
}


