package com.ctrip.tour.business.dashboard.grpBusiness.config;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
public class GrpConfigEntity {
    private DimDrillDownConfig dimDrillDownConfig;

    @Getter
    @Setter
    public static class DimDrillDownConfig {
        private List<DimDrillDownItem> dimDrillDown;
        private List<DimDrillDownEnum> dimDrillDownEnum;
    }

    @Getter
    @Setter
    public static class DimDrillDownItem {
        private String indicatorName;
        private String indicatorNameCn;
        private Boolean canSelect;
    }

    /**
     * 下钻维度枚举配置
     */
    @Getter
    @Setter
    public static class DimDrillDownEnum {
        private List<String> businessLine;
        private Map<String, String> mapping;
    }
}
