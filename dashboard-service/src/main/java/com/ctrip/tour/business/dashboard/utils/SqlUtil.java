package com.ctrip.tour.business.dashboard.utils;

import com.ctrip.platform.dal.dao.StatementParameters;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/30
 */
public class SqlUtil {

    //拼接where条件  只有inMap
    public static void jointWhereCondition(StringBuilder sb,
                                           StatementParameters parameters,
                                           Map<String, List<String>> inMap) {
        int index = parameters.nextIndex();
        sb.append(" where ");
        for (Map.Entry<String, List<String>> entry : inMap.entrySet()) {
            String key = entry.getKey();
            List<String> valueList = entry.getValue();
            sb.append(key).append(" in (?) and ");
            parameters.setInParameter(index++, valueList);
        }
        int curLength = sb.length();
        //删除多余的end
        sb.delete(curLength - 4, curLength);
    }

    //拼接not in条件
    public static void jointNotInCondition(StringBuilder sb,
                                           StatementParameters parameters,
                                           Map<String, List<String>> notInMap) {
        int index = parameters.nextIndex();
        for (Map.Entry<String, List<String>> entry : notInMap.entrySet()) {
            String key = entry.getKey();
            List<String> valueList = entry.getValue();
            sb.append(" and ").append(key).append(" not in (?) ");
            parameters.setInParameter(index++, valueList);
        }
    }

    //拼接group条件
    public static void jointGroupCondition(StringBuilder sb,
                                           boolean needSuffix,
                                           List<String> groupTagList) {
        String groupCondition = StringUtils.join(groupTagList,",");
        if(needSuffix){
            sb.append(" group by ").append(groupCondition);
        }else{
            sb.append(groupCondition);
        }
    }

    //拼接order条件
    public static void jointOrderCondition(StringBuilder sb,
                                           List<String> orderTagList,
                                           String orderTag) {
        sb.append(" order by ");
        for (String orderColumn : orderTagList) {
            sb.append(orderColumn).append(" ").append(orderTag).append(",");
        }
        sb.deleteCharAt(sb.length() - 1);
//        String orderCondition = StringUtils.join(orderTagList,",");
//        sb.append(" order by ").append(orderCondition).append(" ").append(orderTag).append(" ");
    }

    //拼接分页条件
    public static void jointPagingCondition(StringBuilder sb,
                                            StatementParameters parameters,
                                            Integer pageNo,
                                            Integer pageSize){
        sb.append(" limit ?,? ");
        int index = parameters.nextIndex();
        parameters.set(index++,(pageNo-1)*pageSize);
        parameters.set(index++,pageSize);
    }

    //拼接like条件
    public static void jointLikeCondition(StringBuilder sb,
                                          StatementParameters parameters,
                                          Map<String, String> likeMap,
                                          Boolean isAndCondition) {
        String jointParam = "or";
        if (isAndCondition) {
            jointParam = "and";
        }
        int index = parameters.nextIndex();
        sb.append(" and (");
        for (Map.Entry<String, String> entry : likeMap.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            sb.append(key).append(" like ? ").append("  ").append(jointParam).append(" ");
            parameters.set(index++, "%" + value + "%");
        }
        int curLength = sb.length();
        //删除多余的end
        sb.delete(curLength - 4, curLength);
        sb.append(") ");

    }

    //拼接like条件(新版)
    public static void jointLikeCondition(StringBuilder sb,
                                          Map<String, List<String>> likeMap,
                                          StatementParameters parameters) {
        int index = parameters.nextIndex();
        if (GeneralUtil.isNotEmpty(likeMap)) {
            sb.append(" and ( ");
            for (Map.Entry<String, List<String>> entry : likeMap.entrySet()) {
                String originKey = entry.getKey();
                String[] keyArr = originKey.split("\\|");
                List<String> valueList = entry.getValue();
                sb.append("(");
                for (String key : keyArr) {
                    for (String item : valueList) {
                        sb.append(key).append(" like ? ");
                        parameters.set(index++, "%" + item + "%");
                        sb.append(" or ");
                    }
                }
                int curLength = sb.length();
                //删除多余的or
                sb.delete(curLength - 3, curLength);
                sb.append(")");
                sb.append(" and ");
            }
            int curLength = sb.length();
            //删除多余的and
            sb.delete(curLength - 4, curLength);
            sb.append(" ) ");
        }

    }


    //拼接not like条件
    public static void jointNotLikeCondition(StringBuilder sb,
                                             Map<String, List<String>> notLikeMap,
                                             StatementParameters parameters) {
        int index = parameters.nextIndex();
        if (GeneralUtil.isNotEmpty(notLikeMap)) {
            sb.append(" and ( ");
            for (Map.Entry<String, List<String>> entry : notLikeMap.entrySet()) {
                String key = entry.getKey();
                List<String> valueList = entry.getValue();
                sb.append("(");
                for (String item : valueList) {
                    sb.append(key).append(" not like ? ");
                    parameters.set(index++, "%" + item + "%");
                    sb.append(" or ");
                }
                int curLength = sb.length();
                //删除多余的or
                sb.delete(curLength - 3, curLength);
                sb.append(")");
                sb.append(" and ");
            }
            int curLength = sb.length();
            //删除多余的and
            sb.delete(curLength - 4, curLength);
            sb.append(" ) ");
        }

    }

    //拼接between条件
    public static void jointBetweenCondition(StringBuilder sb,
                                             StatementParameters parameters,
                                             Map<String, List<String>> betweenMap) {
        int index = parameters.nextIndex();
        for (Map.Entry<String, List<String>> entry : betweenMap.entrySet()) {
            String key = entry.getKey();
            List<String> valueList = entry.getValue();
            String firstValue = valueList.get(0);
            String lastValue = valueList.get(1);
            sb.append(" and (").append(key).append(" between ? and ?) ");
            parameters.set(index++, firstValue);
            parameters.set(index++, lastValue);
        }
    }
}
