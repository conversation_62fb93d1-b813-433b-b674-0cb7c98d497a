package com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.bean;

import lombok.Data;

import java.util.List;

//下钻维度表格查询类
@Data
public class OverseasPerformanceInfoParamBean {
    String d;
    //登录态语言
    String vbkLocale;
    //下钻维度
    String field;
    Integer PageIndex;
    Integer PageSize;
    String metric;
    // 邮箱前缀
    List<String> domainNames;
    //考核层级
    String examineLevel;
    /** 日期，计算30日环比时查询- **/
    String useDate;
    //按季度查询时搜索
    String quarter;
    //按半年查询时搜索
    String halfYear;
    //指定年份
    String year;
    //开始时间
    String startDate;
    //结束时间
    String endDate;
    //业务线
    List<String> buTypeName;
    //业务大区
    List<String> buRegionNames;
    List<String> buRegionNameEns;
    //下钻维度业务大区
    List<String> dimValueBuRegionNames;
    List<String> dimValueBuRegionNameEns;
    //业务子区域
    List<String> buSubRegionNames;
    List<String> buSubRegionNameEns;
    //下钻维度业务子区域
    List<String> dimValueBuSubRegionNames;
    List<String> dimValueBuSubRegionNameEns;
    //CT站
    List<String> ct;
    //站点
    List<String> sites;
    List<String> locale;
    List<String> dimValueSites;
    List<String> dimValueLocale;
    List<String> disChannelName;
    List<String> dimValueDisChannelName;
//国家ID
    List<String> ctryNames;
    List<String> provNames;
    List<String> cityNames;
    List<String> ctryNameEns;
    List<String> provNameEns;
    List<String> cityNameEns;
    //景点经理
    List<String> vstMeids;
    //景点助理
    List<String> vstAeids;
    //景点景点ID
    List<String> vstNames;
    List<String> dimVstNames;
    List<String> vstNameEns;
    List<String> dimVstNameEns;
}
