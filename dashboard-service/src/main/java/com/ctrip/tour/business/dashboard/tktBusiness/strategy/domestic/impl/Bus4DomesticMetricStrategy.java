package com.ctrip.tour.business.dashboard.tktBusiness.strategy.domestic.impl;

import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.grpBusiness.enums.DomesticMetricEnum;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.DomesticSingelDrillBaseInfo;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ExamineConfigBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.SinglePeriodDataBean;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.SinglePeriodTrendLineBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.bo.ExamineConfigBo;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.Bus4Dao;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardExamineeConfigV2Dao;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardExamineeConfigV2;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.*;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.domestic.DomesticMetricCalStrategy;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

@Component
public class Bus4DomesticMetricStrategy implements DomesticMetricCalStrategy {

    @Autowired
    private Bus4Dao bus4Dao;

    @Autowired
    private RemoteConfig remoteConfig;

    @Autowired
    private BusinessDashboardExamineeConfigV2Dao examineeConfigV2Dao;
    @Autowired
    private SinglePeriodTrendLineBiz singlePeriodTrendLineBiz;

    @Override
    public Future<DomesticMetricDetailInfo> getSingleMetricCardData(String domainName, TimeFilter timeFilter, List<MetricInfoBean> metricInfoBeanList, String d, Boolean isFirst, Integer businessId) throws Exception {
        DomesticMetricDetailInfo metricDetailInfo = new DomesticMetricDetailInfo();
        metricDetailInfo.setMetricCode(DomesticMetricEnum.getCodeById(getMetricName()));

        if (DomesticMetricHelper.existBusinessLevel(businessId, metricInfoBeanList)) {
            return new AsyncResult<>(new DomesticMetricDetailInfo());
        }

        Map<String,MetricInfoBean> metricQuarterMap = metricInfoBeanList.stream().collect(Collectors.toMap(MetricInfoBean::getQuarter, metricInfoBean -> metricInfoBean));
        if (metricQuarterMap.isEmpty()) {
            new AsyncResult<>(new DomesticMetricDetailInfo());
        }
        //遍历每个季度累加数据重新计算
        DomesticNoDrillTableData dirCore = new DomesticNoDrillTableData();
        DomesticNoDrillTableData dirFocus = new DomesticNoDrillTableData();
        DomesticNoDrillTableData dirTailValue = new DomesticNoDrillTableData();
        DomesticNoDrillTableData dirTailYield = new DomesticNoDrillTableData();
        DomesticMetricHelper.initDirectBean(dirCore,"core");
        DomesticMetricHelper.initDirectBean(dirFocus,"focus");
        DomesticMetricHelper.initDirectBean(dirTailValue,"tailHighValue");
        DomesticMetricHelper.initDirectBean(dirTailYield,"tailHighYield");
        int targetScore = 0;
        int completeScore = 0;
        boolean isTargetNull = true;
        String maxQuarter = DateUtil.getMaxQuarters(new ArrayList<>(metricQuarterMap.keySet()));
        for (Map.Entry<String, MetricInfoBean> entry : metricQuarterMap.entrySet()) {
            String quarterOrMonth;
            if(!"month".equals(timeFilter.getDateType())) {
                quarterOrMonth = entry.getKey();
                timeFilter.setQuarter(quarterOrMonth);
                if (!maxQuarter.equals(quarterOrMonth)) {
                    continue;
                }
            }

            MetricInfoBean metricInfoBean = entry.getValue();
            List<DomesticNoDrillTableData> directQuarterBeans = getDirectCardInfoWithQuarterOrMonth(timeFilter, metricInfoBean, d);
            for (DomesticNoDrillTableData bean : directQuarterBeans) {
                isTargetNull = false;
                String sightRange = bean.getDirectSightRange();
                switch (sightRange) {
                    case "core":
                        dirCore.setDirectSightRange("core");
                        dirCore.setDirectCompleteScore(bean.getDirectCompleteScore());
                        dirCore.setDirectTargetScore(bean.getDirectTargetScore());
                        dirCore.setDirectCrashSightNum(dirCore.getDirectCrashSightNum() + bean.getDirectCrashSightNum());
                        dirCore.setDirectRealSightNum(dirCore.getDirectRealSightNum() + bean.getDirectRealSightNum());
                        dirCore.setDirectTargetSightNum(dirCore.getDirectTargetSightNum() + bean.getDirectTargetSightNum());
                        dirCore.setDirectWeight(0.05);
                        break;
                    case "focus":
                        dirFocus.setDirectSightRange("focus");
                        dirFocus.setDirectCompleteScore(bean.getDirectCompleteScore());
                        dirFocus.setDirectTargetScore(bean.getDirectTargetScore());
                        dirFocus.setDirectCrashSightNum(dirFocus.getDirectCrashSightNum() + bean.getDirectCrashSightNum());
                        dirFocus.setDirectRealSightNum(dirFocus.getDirectRealSightNum() + bean.getDirectRealSightNum());
                        dirFocus.setDirectTargetSightNum(dirFocus.getDirectTargetSightNum() + bean.getDirectTargetSightNum());
                        dirFocus.setDirectWeight(0.03);
                        break;
                    case "tailHighValue":
                        dirTailValue.setDirectSightRange("tailHighValue");
                        dirTailValue.setDirectCompleteScore(bean.getDirectCompleteScore());
                        dirTailValue.setDirectTargetScore(bean.getDirectTargetScore());
                        dirTailValue.setDirectCrashSightNum(dirTailValue.getDirectCrashSightNum() + bean.getDirectCrashSightNum());
                        dirTailValue.setDirectRealSightNum(dirTailValue.getDirectRealSightNum() + bean.getDirectRealSightNum());
                        dirTailValue.setDirectTargetSightNum(dirTailValue.getDirectTargetSightNum() + bean.getDirectTargetSightNum());
                        dirTailValue.setDirectWeight(0.03);
                        break;
                    case "tailHighYield":
                        dirTailYield.setDirectSightRange("tailHighYield");
                        dirTailYield.setDirectCompleteScore(bean.getDirectCompleteScore());
                        dirTailYield.setDirectTargetScore(bean.getDirectTargetScore());
                        dirTailYield.setDirectCrashSightNum(dirTailYield.getDirectCrashSightNum() + bean.getDirectCrashSightNum());
                        dirTailYield.setDirectRealSightNum(dirTailYield.getDirectRealSightNum() + bean.getDirectRealSightNum());
                        dirTailYield.setDirectTargetSightNum(dirTailYield.getDirectTargetSightNum() + bean.getDirectTargetSightNum());
                        dirTailYield.setDirectWeight(0.01);
                        break;

                }
                targetScore += bean.getDirectTargetScore();
                completeScore += bean.getDirectCompleteScore();
            }
        }
        SetDirectCommonFiled(dirCore);
        SetDirectCommonFiled(dirFocus);
        SetDirectCommonFiled(dirTailValue);
        SetDirectCommonFiled(dirTailYield);

        dirCore.setDirectTargetSightNum(null);
        dirFocus.setDirectTargetSightNum(null);
        dirTailValue.setDirectTargetSightNum(null);
        dirTailYield.setDirectTargetSightNum(null);

        if (isFirst) {
            metricDetailInfo.setTableHeaderList(Bus4Helper.getFirstTableList());
        } else {
            metricDetailInfo.setTableHeaderList(Bus4Helper.getDetailTableList());
            dirCore.setDirectSightGap(null);
            dirFocus.setDirectSightGap(null);
            dirTailValue.setDirectSightGap(null);
            dirTailYield.setDirectSightGap(null);
        }

        if (isTargetNull) {
            dirCore.setDirectTargetScore(-1);
            dirFocus.setDirectTargetScore(-1);
            dirTailValue.setDirectTargetScore(-1);
            dirTailYield.setDirectTargetScore(-1);
        }

        metricDetailInfo.setCompleteRate(completeScore == 0 ? 0.0 : (double) completeScore / targetScore);

        List<DomesticNoDrillTableData> tableInfo = new ArrayList<>();
        tableInfo.add(dirCore);
        tableInfo.add(dirFocus);
        tableInfo.add(dirTailValue);
        tableInfo.add(dirTailYield);
        metricDetailInfo.setTableDataItemList(tableInfo);

        MetricHelper.setMetricCardDrillDownInfV2(metricQuarterMap.get(maxQuarter), metricDetailInfo);

        //获取排名数据
        DalHints rankDalHints = new DalHints().asyncExecution();
        getRankDataAsync(timeFilter.getDateType(), timeFilter.getYear(), maxQuarter, timeFilter.getMonth(), d, domainName, String.valueOf(DomesticMetricEnum.getIdByCode(metricDetailInfo.getMetricCode())), rankDalHints);
        ChartHelper.fillRankDataV2(metricDetailInfo, rankDalHints.getListResult());
        return new AsyncResult<>(metricDetailInfo);

    }

    private void getRankDataAsync(String dateType,
                                  String year,
                                  String quarter,
                                  String month,
                                  String d,
                                  String domainName,
                                  String metric,
                                  DalHints dalHints) throws Exception {
        Map<String, List<String>> inMap = new LinkedHashMap<>();
        inMap.put("query_d", Lists.newArrayList(d));
        inMap.put("examinee", Lists.newArrayList(domainName));
        inMap.put("year", Lists.newArrayList(year));
        if ("month".equals(dateType)) {
            inMap.put("month", Lists.newArrayList(month));
        } else {
            String mappingMonth = DateUtil.getMappingMonthByQuarter(d, year, quarter);
            inMap.put("month", Lists.newArrayList(mappingMonth));
        }
        inMap.put("metric", Lists.newArrayList(metric));
        bus4Dao.getRankAsync(inMap, dalHints);
    }

    /**
     * 设置直签公共字段
     * @param dirInfo
     */
    private void SetDirectCommonFiled(DomesticNoDrillTableData dirInfo) {
        if (dirInfo.getDirectTargetSightNum() != 0 && dirInfo.getDirectRealSightNum() != 0) {
            dirInfo.setDirectSightGap(Math.abs(dirInfo.getDirectTargetSightNum() - dirInfo.getDirectRealSightNum()));
        }
        dirInfo.setDirectRate(dirInfo.getDirectCrashSightNum() == 0 ? 0.0 : (double) dirInfo.getDirectRealSightNum() / dirInfo.getDirectCrashSightNum());
    }

    /**
     * 处理单个季度的直签数据
     * @param timeFilter
     * @param metricInfoBean
     * @param d
     * @return
     * @throws Exception
     */
    private List<DomesticNoDrillTableData> getDirectCardInfoWithQuarterOrMonth(TimeFilter timeFilter,MetricInfoBean metricInfoBean, String d) throws Exception {
        String year = timeFilter.getYear();
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();
        //当是季度指标 但该指标数仓直接给了累计值 所以可以像正常月度指标一样取数据
        String dateType = timeFilter.getDateType();

        String level = MetricHelper.getLevelColumnName(metricInfoBean.getLevel());
        List<String> regionList = metricInfoBean.getRegionList();

        Map<String, List<String>> inMap = new HashMap<>();
        inMap.put("query_d", Lists.newArrayList(d));
        inMap.put("year", Lists.newArrayList(year));
        inMap.put("date_type", Lists.newArrayList(dateType));
        if ("month".equals(dateType)) {
            inMap.put("month", Lists.newArrayList(month));
        } else {
            inMap.put("quarter", Lists.newArrayList(quarter));
        }
        if (!level.isEmpty()) {
            inMap.put(level, regionList);
        }
        List<String> groupTagList = Lists.newArrayList("scenic_class_name");
        List<String> orderTagList = Lists.newArrayList("scenic_class_id");

        //单条数据时dimMap直接使用，dimReachList包含所有参数
        Map<String, Double> dimMap = new HashMap<>();
        List<String> dimReachList = Bus4Helper.getDimList();
        List<List<Object>> reachList = bus4Dao.getScenicSignData(inMap, new ArrayList<>());
        ChartHelper.fillOverallDimMap(reachList, dimReachList, dimMap);

        //group by的时候有多条要分开，dimStackList包含除第一列的数据，groupTagList第一列数据
        List<TableDataItem> tableDataItemList = new ArrayList<>();
        List<String> dimStackList = Bus4Helper.getLevelList();
        List<List<Object>> stackList = bus4Dao.getScenicMetricData(inMap, groupTagList, orderTagList);
        ChartHelper.fillCommmonTableData(tableDataItemList, groupTagList, dimStackList, new ArrayList<>(), stackList, new ArrayList<>());

        if (tableDataItemList.isEmpty() || reachList.isEmpty()) {
            return Collections.emptyList();
        }

        List<DomesticNoDrillTableData> response = new ArrayList<>();
        for (TableDataItem info : tableDataItemList) {
            DomesticNoDrillTableData bean = new DomesticNoDrillTableData();
            String lineName = info.getFieldMap().get("scenic_class_name");
            switch (lineName) {
                case "核心"://NOSONAR
                    bean.setDirectSightRange("core");
                    break;
                case "聚焦"://NOSONAR
                    bean.setDirectSightRange("focus");
                    break;
                case "长尾高价值"://NOSONAR
                    bean.setDirectSightRange("tailHighValue");
                    break;
                case "长尾高产"://NOSONAR
                    bean.setDirectSightRange("tailHighYield");
                    break;
                default:
                    continue;
            }
            //共同数据
            bean.setDirectCompleteScore(dimMap.getOrDefault("complete_score",0.00).intValue());
            bean.setDirectTargetScore(dimMap.getOrDefault("target_score",-1.00).intValue());

            //私有数据"actual_sign_scenic_cnt","target_sign_scenic_cnt", "gap_sign_scenic_cnt","treasuremap_scenic_cnt"
            Map<String, Double> tableDimMap = info.getDimMap();
            bean.setDirectCrashSightNum(tableDimMap.getOrDefault("treasuremap_scenic_cnt",0.00).intValue());
            bean.setDirectTargetSightNum(tableDimMap.getOrDefault("target_sign_scenic_cnt",0.00).intValue());
            bean.setDirectRealSightNum(tableDimMap.getOrDefault("actual_sign_scenic_cnt",0.00).intValue());

            response.add(bean);
        }

        return response;
    }



    @Override
    public GetDomesticMetricTrendDataResponseType getSingleTrendlineData(GetDomesticMetricTrendDataRequestType request, String d) throws Exception {
        GetDomesticMetricTrendDataResponseType response = new GetDomesticMetricTrendDataResponseType();
        List<TrendLineDetailInfo> trendLineDetailInfoList = new ArrayList<>();
        response.setTrendLineDetailInfoList(trendLineDetailInfoList);
        TimeFilter timeFilter = request.getTimeFilter();
        String domainName = request.getDomainName();

        TimeFilter timeFilterWithDomain = DateUtil.getMaxQuarterTimeFilter(timeFilter, d);
        List<ExamineConfigBean> examineConfigBeanList = MultiPeriodMappingHelper.getMultiPeriodConfigList(timeFilterWithDomain, d, examineeConfigV2Dao.queryMetricAllConfig(domainName, d, String.valueOf(getMetricName())), null);

        TrendLineDetailInfo lineChart = new TrendLineDetailInfo();
        TrendLineDetailInfo stackLineChart = new TrendLineDetailInfo();

        lineChart.setType("lineChart");
        lineChart.setDim(request.getMetricCode());

        stackLineChart.setType("stackLineChart");
        stackLineChart.setDim(request.getMetricCode());

        List<TrendLineDataItem> lineDataItems = new ArrayList<>();
        List<TrendLineDataItem> stackLineDataItems = new ArrayList<>();

        lineChart.setTrendLineDataItemList(lineDataItems);
        stackLineChart.setTrendLineDataItemList(stackLineDataItems);
        for (ExamineConfigBean examineConfigBean : examineConfigBeanList) {
            //只下发25年的
            if (Integer.parseInt(examineConfigBean.getYear()) <= 2024) {
                continue;
            }
            //获取单季或单月的数据
            List<TrendLineDataItem> singleLineDataItems = getSingleTrendLineDataWithQuarterOrMonth(request,examineConfigBean, d);
            for (TrendLineDataItem item : singleLineDataItems) {
                switch (item.getName()) {
                    case "core":
                    case "focus":
                    case "tailHighValue":
                    case "tailHighYield":
                        stackLineDataItems.add(item);
                        break;
                    case "directCompleteRate":
                        lineDataItems.add(item);
                        break;
                }
            }
        }
        trendLineDetailInfoList.add(lineChart);
        trendLineDetailInfoList.add(stackLineChart);

        return response;
    }


    public List<TrendLineDataItem> getSingleTrendLineDataWithQuarterOrMonth(GetDomesticMetricTrendDataRequestType request,
                                                                            ExamineConfigBean examineConfigBean,
                                                                            String d) throws Exception {
        List<TrendLineDataItem> trendLineDataItems = new ArrayList<>();
        String timeString = DomesticMetricHelper.getTimeFormat(examineConfigBean);

        ExamineConfigBo bo = new ExamineConfigBo();
        BusinessDashboardExamineeConfigV2 examineeConfigV2 = examineConfigBean.getBusinessDashboardExamineeConfigV2();
        List<MetricInfoBean> metricInfoBeanList = bo.getMetricInfoBeanListV2(Collections.singletonList(examineeConfigV2), remoteConfig);
        if (metricInfoBeanList.size() != 1) {
            return trendLineDataItems;
        }

        TimeFilter timeFilter = DomesticMetricHelper.getTimeFilter(examineConfigBean);
        List<DomesticNoDrillTableData> directQuarterBeans = getDirectCardInfoWithQuarterOrMonth(timeFilter, metricInfoBeanList.get(0), d);
        DomesticNoDrillTableData dirCore = new DomesticNoDrillTableData();
        DomesticNoDrillTableData dirFocus = new DomesticNoDrillTableData();
        DomesticNoDrillTableData dirTailValue = new DomesticNoDrillTableData();
        DomesticNoDrillTableData dirTailYield = new DomesticNoDrillTableData();
        DomesticMetricHelper.initDirectBean(dirCore,"core");
        DomesticMetricHelper.initDirectBean(dirFocus,"focus");
        DomesticMetricHelper.initDirectBean(dirTailValue,"tailHighValue");
        DomesticMetricHelper.initDirectBean(dirTailYield,"tailHighYield");
        int targetScore = 0;
        int completeScore = 0;
        for (DomesticNoDrillTableData bean : directQuarterBeans) {
            String sightRange = bean.getDirectSightRange();
            switch (sightRange) {
                case "core":
                    dirCore.setDirectSightRange("core");
                    dirCore.setDirectRealSightNum(dirCore.getDirectRealSightNum() + bean.getDirectRealSightNum());
                    dirCore.setDirectTargetSightNum(dirCore.getDirectTargetSightNum() + bean.getDirectTargetSightNum());
                    break;
                case "focus":
                    dirFocus.setDirectSightRange("focus");
                    dirFocus.setDirectRealSightNum(dirFocus.getDirectRealSightNum() + bean.getDirectRealSightNum());
                    dirFocus.setDirectTargetSightNum(dirFocus.getDirectTargetSightNum() + bean.getDirectTargetSightNum());
                    break;
                case "tailHighValue":
                    dirTailValue.setDirectSightRange("tailHighValue");
                    dirTailValue.setDirectRealSightNum(dirTailValue.getDirectRealSightNum() + bean.getDirectRealSightNum());
                    dirTailValue.setDirectTargetSightNum(dirTailValue.getDirectTargetSightNum() + bean.getDirectTargetSightNum());
                    break;
                case "tailHighYield":
                    dirTailYield.setDirectSightRange("tailHighYield");
                    dirTailYield.setDirectRealSightNum(dirTailYield.getDirectRealSightNum() + bean.getDirectRealSightNum());
                    dirTailYield.setDirectTargetSightNum(dirTailYield.getDirectTargetSightNum() + bean.getDirectTargetSightNum());
                    break;

            }
            targetScore += bean.getDirectTargetScore();
            completeScore += bean.getDirectCompleteScore();
        }
        SetDirectCommonFiled(dirCore);
        SetDirectCommonFiled(dirFocus);
        SetDirectCommonFiled(dirTailValue);
        SetDirectCommonFiled(dirTailYield);
        double completeRate = completeScore == 0 ? 0.0 : (double) completeScore / targetScore;

        TrendLineDataItem coreItem = new TrendLineDataItem();
        TrendLineDataItem focusItem = new TrendLineDataItem();
        TrendLineDataItem tailHighValueItem = new TrendLineDataItem();
        TrendLineDataItem tailHighYieldItem = new TrendLineDataItem();
        TrendLineDataItem completeRateItem = new TrendLineDataItem();

        coreItem.setTime(timeString);
        focusItem.setTime(timeString);
        tailHighValueItem.setTime(timeString);
        tailHighYieldItem.setTime(timeString);
        completeRateItem.setTime(timeString);

        coreItem.setName("core");
        focusItem.setName("focus");
        tailHighValueItem.setName("tailHighValue");
        tailHighYieldItem.setName("tailHighYield");
        completeRateItem.setName("directCompleteRate");

        coreItem.setValue(dirCore.getDirectRealSightNum().doubleValue());
        focusItem.setValue(dirFocus.getDirectRealSightNum().doubleValue());
        tailHighValueItem.setValue(dirTailValue.getDirectRealSightNum().doubleValue());
        tailHighYieldItem.setValue(dirTailYield.getDirectRealSightNum().doubleValue());
        completeRateItem.setValue(completeRate);

        trendLineDataItems.add(coreItem);
        trendLineDataItems.add(focusItem);
        trendLineDataItems.add(tailHighValueItem);
        trendLineDataItems.add(tailHighYieldItem);
        trendLineDataItems.add(completeRateItem);

        return trendLineDataItems;
    }


    @Override
    public GetDomesticTableDataResponseType getSingleTableData(GetDomesticTableDataRequestType request, MetricInfoBean metricInfoBean, String d) throws Exception {
        GetDomesticTableDataResponseType response = new GetDomesticTableDataResponseType();
        List<DomesticTableData> tableDataItemList = new ArrayList<>();
        response.setTableDataItemList(tableDataItemList);
        TimeFilter timeFilter = request.getTimeFilter();
        String year = timeFilter.getYear();
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();
        String dateType = MetricHelper.getDateTypeColumnName(timeFilter.getDateType());

        String level = MetricHelper.getLevelColumnName(metricInfoBean.getLevel());
        List<String> regionList = metricInfoBean.getRegionList();
        Integer pageNo = request.getPageNo() == null ? 1 : request.getPageSize();
        Integer pageSize = request.getPageSize();
        if (pageSize == null) {
            pageSize = 18;
        }
        if ("trend".equalsIgnoreCase(request.getQueryType())) {
            List<TrendLineDetailInfo> trendLineDetailInfoList = getDrillThrendLineDetailInfoList(request, metricInfoBean, d);
            response.setTrendLineDetailInfoList(trendLineDetailInfoList);
            return response;
        }

        DrillDownFilter drillDownFilter = request.getDrillDownFilter();
        String field = MetricHelper.getDrillDownColumnName(drillDownFilter.getField());
        List<String> fieldValueList = drillDownFilter.getFieldValueList();

        List<String> subjectTagList = MetricHelper.getTableDrillDownGroupList(field);
        subjectTagList.add("scenic_class_name");
        //获取分层进展  目标   差值   直签率
        List<String> orderTagList = Lists.newArrayList(field, "scenic_class_id");
        Map<String, List<String>> inMap = new LinkedHashMap<>();
        inMap.put("query_d", Lists.newArrayList(d));
        inMap.put("year", Lists.newArrayList(year));
        inMap.put("date_type", Lists.newArrayList(dateType));
        if ("month".equals(dateType)) {
            inMap.put("month", Lists.newArrayList(month));
        } else {
            inMap.put("quarter", Lists.newArrayList(quarter));
        }
        if (!"".equals(level)) {
            inMap.put(level, regionList);
        }
        if (!GeneralUtil.isEmpty(fieldValueList)) {
            inMap.put(field, fieldValueList);
        }
        List<List<Object>> scenicTableList = bus4Dao.getLevelScenicTableData(inMap, subjectTagList, orderTagList, pageNo, pageSize);
        List<TableDataItem> oldTableDataItemList = new ArrayList<>();
        //计算数据总条数
        response.setTotalNum(bus4Dao.getTableReachDataCount(inMap, subjectTagList));
        List<String> tableDimList = Bus4Helper.getTableDimList();
        ChartHelper.fillCommmonTableData(oldTableDataItemList, subjectTagList,
                tableDimList, new ArrayList<>(), scenicTableList, new ArrayList<>());

        //收集下钻字段的值
        List<String> actualFieldList = oldTableDataItemList.stream()
                .map(i -> i.getFieldMap().get(field))
                .collect(Collectors.toList());
        if (!GeneralUtil.isEmpty(actualFieldList)) {
            inMap.put(field, actualFieldList);
            List<List<Object>> scenicSignDataList = bus4Dao.getScenicSignData(inMap, Lists.newArrayList(field));
            ChartHelper.fillCommmonTableDataV2(oldTableDataItemList, new ArrayList<>(),
                    Lists.newArrayList(field), new ArrayList<>(), Bus4Helper.getDimList(), new ArrayList<>(), scenicSignDataList);
        }
        for (TableDataItem item : oldTableDataItemList) {
            DomesticTableData newItem = new DomesticTableData();
            Map<String, String> fieldMap = item.getFieldMap();
            Map<String, Double> dimMap = item.getDimMap();
            newItem.setRegionName(fieldMap.get("region_name"));
            newItem.setProvinceName(fieldMap.getOrDefault("province_name",""));
            newItem.setRatioLevel(fieldMap.get("scenic_class_name"));
            newItem.setTreasuremapScenicCnt(dimMap.getOrDefault("treasuremap_scenic_cnt",0.00));
            newItem.setTargetSignScenicCnt(dimMap.getOrDefault("target_sign_scenic_cnt",0.00));
            newItem.setActualSignScenicCnt(dimMap.getOrDefault("actual_sign_scenic_cnt",0.00));
            newItem.setGapValue(dimMap.getOrDefault("gap_sign_scenic_cnt",0.00));
            newItem.setTtdCpsSignRate(dimMap.getOrDefault("ttd_cps_sign_rate",0.00));
            newItem.setCompleteValue(dimMap.getOrDefault("complete_score",0.00));
            newItem.setTargetValue(dimMap.getOrDefault("target_score",0.00));
            newItem.setCompleteRate(dimMap.getOrDefault("delta_complete_rate",0.00));
            tableDataItemList.add(newItem);
        }


        return response;
    }

    private List<TrendLineDetailInfo> getDrillThrendLineDetailInfoList(GetDomesticTableDataRequestType request,
                                                                       MetricInfoBean metricInfoBean,
                                                                       String d) throws Exception {

        List<TrendLineDetailInfo> trendLineDetailInfoList = new ArrayList<>();


        TimeFilter timeFilter = request.getTimeFilter();

        DrillDownFilter drillDownFilter = request.getDrillDownFilter();
        String field = MetricHelper.getDrillDownColumnName(drillDownFilter.getField());
        Integer metricId = DomesticMetricEnum.getIdByCode(request.getMetricCode());
        String domainName = request.getDomainName();
        List<ExamineConfigBean> examineConfigBeanList = MultiPeriodMappingHelper.getMultiPeriodConfigList(timeFilter, d, examineeConfigV2Dao.queryMetricAllConfig(domainName, d, metricId.toString()), null);
        List<Future<SinglePeriodDataBean>> futureList = new ArrayList<>();
        for (ExamineConfigBean examineConfigBean : examineConfigBeanList) {
            GetTrendLineDataRequestType requestOld=new GetTrendLineDataRequestType();
            requestOld.setTimeFilter(request.getTimeFilter());
            requestOld.setMetric(DomesticMetricEnum.getMetricByCode(request.getMetricCode()));
            requestOld.setSubMetric("domestic");
            requestOld.setQueryType("drilldown");
            requestOld.setDrillDownFilter(request.getDrillDownFilter());
            requestOld.setDomainName(request.getDomainName());
            futureList.add(singlePeriodTrendLineBiz.getBus4SinglePeriodTrendLineData(requestOld, examineConfigBean, d));
        }
        List<List<Object>> reachList = new ArrayList<>();
        for (Future<SinglePeriodDataBean> futureResult : futureList) {
            SinglePeriodDataBean singlePeriodDataBean = futureResult.get();
            List<List<Object>> periodReachList = singlePeriodDataBean.getPeriodReachList();
            if (!GeneralUtil.isEmpty(periodReachList)) {
                reachList.addAll(periodReachList);
            }
        }

        Map<String, Double> dimMap = new HashMap<>();
        Set<String> drillDownSet = new HashSet<>();
        ChartHelper.setMonthlyMetricTrendlineActualReach(dimMap, drillDownSet, reachList, Lists.newArrayList("time", field), Bus4Helper.getDimList());

        //有效时间范围
        List<String> timeList = DateUtil.getAllSelectedTime(timeFilter, "domestic");
        ChartHelper.fillLineChartTrendLineDataWithDrillDown(null, timeList, dimMap,
                trendLineDetailInfoList, Bus4Helper.getLineChartTrendlineTypeWithDrillDown(), drillDownSet, false);
        return trendLineDetailInfoList;
    }

    @Override
    public GetDomesticDrillDownBaseInfoResponseType getSingleDrillDownBaseInfo(GetDomesticDrillDownBaseInfoRequestType request, MetricInfoBean metricInfoBean, String d) throws Exception {
        GetDomesticDrillDownBaseInfoResponseType response = new GetDomesticDrillDownBaseInfoResponseType();
        List<FieldDataItem> fieldDataItemList = new ArrayList<>();
        response.setFieldDataItemList(fieldDataItemList);

        TimeFilter timeFilter = request.getTimeFilter();
        String year = timeFilter.getYear();
        String dateType = timeFilter.getDateType();
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();

        String metric = metricInfoBean.getMetric();
        List<String> regionList = metricInfoBean.getRegionList();
        String level = metricInfoBean.getLevel();//国内  三方  大区  省份
        String levelColumnName = MetricHelper.getLevelColumnName(level);

        boolean needSearch = StringUtils.isNotEmpty(request.getSearchWord());
        String searchWord = request.getSearchWord();
        List<String> fieldList = Bus4Helper.getFieldList(level);

        if(needSearch){
            String searchField = request.getSearchField();//大区 省份

            if (searchField != null) {
                String searchFieldColumnName = MetricHelper.getDrillDownColumnName(searchField);
                fieldList = Lists.newArrayList(searchFieldColumnName);
            }
        }else{
            response.setDefaultChosenField(MetricHelper.getDefaultChosenField(level, metric));
        }
        Map<String, List<String>> inMap = new LinkedHashMap<>();
        inMap.put("query_d", Lists.newArrayList(d));
        inMap.put("year", Lists.newArrayList(year));
        if ("month".equals(dateType)) {
            inMap.put("month", Lists.newArrayList(month));
        } else {
            inMap.put("quarter", Lists.newArrayList(quarter));
        }
        if (!"".equals(levelColumnName)) {
            inMap.put(levelColumnName, regionList);
        }
        for (String field : fieldList) {
            List<String> tagList = Lists.newArrayList(field);
            Map<String,String> likeMap = new HashMap<>();
            if(needSearch){
                likeMap.put(field,searchWord);
            }
            List<List<Object>> rawObjectList = bus4Dao.getFieldList(inMap, likeMap, tagList);
            ChartHelper.fillFieldDataItemList(field, rawObjectList, fieldDataItemList);
        }

        for(FieldDataItem item: fieldDataItemList ){
            item.setNeedBubble(false);
            item.setNeedLine(false);
            DomesticSingelDrillBaseInfo domesticSingelDrillBaseInfo = remoteConfig.getDrillDownFieldBeanV2(request.getMetricCode(), request.getBusinessId(), request.getSubBusinessId(), item.getField());
            if (domesticSingelDrillBaseInfo != null) {
                item.setNeedBubble(domesticSingelDrillBaseInfo.getNeedBubble());
                item.setNeedLine(domesticSingelDrillBaseInfo.getNeedTrend());
            }
        }
        return response;
    }
    @Override
    public GetFirstPageDomesticMetricCardDrillDataResponseType getFirstPageDomesticMetricCardDrillData(
            GetFirstPageDomesticMetricCardDrillDataRequestType request,MetricInfoBean metricInfoBean, String d) throws Exception{
        GetFirstPageDomesticMetricCardDrillDataResponseType response = new GetFirstPageDomesticMetricCardDrillDataResponseType();
        List<FirstPageDomesticTableData> tableDataItemList = new ArrayList<>();
        response.setTableDataItemList(tableDataItemList);
        TimeFilter timeFilter = request.getTimeFilter();
        String year = timeFilter.getYear();
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();
        String dateType = MetricHelper.getDateTypeColumnName(timeFilter.getDateType());

        String level = MetricHelper.getLevelColumnName(metricInfoBean.getLevel());
        List<String> regionList = metricInfoBean.getRegionList();
        Integer pageNo = 1;
        Integer pageSize = 18;


        DrillDownFilter drillDownFilter = new DrillDownFilter();
        drillDownFilter.setField(request.getDefaultField());
        String field = MetricHelper.getDrillDownColumnName(drillDownFilter.getField());
        List<String> fieldValueList = drillDownFilter.getFieldValueList();

        List<String> subjectTagList = MetricHelper.getTableDrillDownGroupList(field);
        subjectTagList.add("scenic_class_name");
        //获取分层进展  目标   差值   直签率
        List<String> orderTagList = Lists.newArrayList(field, "scenic_class_id");
        Map<String, List<String>> inMap = new LinkedHashMap<>();
        inMap.put("query_d", Lists.newArrayList(d));
        inMap.put("year", Lists.newArrayList(year));
        inMap.put("date_type", Lists.newArrayList(dateType));
        if ("month".equals(dateType)) {
            inMap.put("month", Lists.newArrayList(month));
        } else {
            inMap.put("quarter", Lists.newArrayList(quarter));
        }
        if (!"".equals(level)) {
            inMap.put(level, regionList);
        }
        if (!GeneralUtil.isEmpty(fieldValueList)) {
            inMap.put(field, fieldValueList);
        }
        List<List<Object>> scenicTableList = bus4Dao.getLevelScenicTableData(inMap, subjectTagList, orderTagList, pageNo, pageSize);
        List<TableDataItem> oldTableDataItemList = new ArrayList<>();

        List<String> tableDimList = Bus4Helper.getTableDimList();
        ChartHelper.fillCommmonTableData(oldTableDataItemList, subjectTagList,
                tableDimList, new ArrayList<>(), scenicTableList, new ArrayList<>());

        //收集下钻字段的值
        List<String> actualFieldList = oldTableDataItemList.stream()
                .map(i -> i.getFieldMap().get(field))
                .collect(Collectors.toList());
        if (!GeneralUtil.isEmpty(actualFieldList)) {
            inMap.put(field, actualFieldList);
            List<List<Object>> scenicSignDataList = bus4Dao.getScenicSignData(inMap, Lists.newArrayList(field));
            ChartHelper.fillCommmonTableDataV2(oldTableDataItemList, new ArrayList<>(),
                    Lists.newArrayList(field), new ArrayList<>(), Bus4Helper.getDimList(), new ArrayList<>(), scenicSignDataList);
        }
        Map<String,FirstPageDomesticTableData> firstPageDomesticTableDataMap=new HashMap<>();
        for (TableDataItem item : oldTableDataItemList) {
            FirstPageDomesticTableData newItem = null;
            Map<String, String> fieldMap = item.getFieldMap();
            Map<String, Double> dimMap = item.getDimMap();
            String key = String.format("%s:%s", fieldMap.get("region_name"), fieldMap.getOrDefault("province_name", ""));
            if (firstPageDomesticTableDataMap.containsKey(key)) {
                newItem = firstPageDomesticTableDataMap.get(key);
            } else {
                newItem = new FirstPageDomesticTableData();
                tableDataItemList.add(newItem);
            }
            newItem.setRegionName(fieldMap.get("region_name"));
            newItem.setProvinceName(fieldMap.getOrDefault("province_name",""));
            String ratioLevel=fieldMap.get("scenic_class_name");
            switch (ratioLevel){
                case "核心"://NOSONAR
                    newItem.setCoreDirectRate(dimMap.getOrDefault("ttd_cps_sign_rate",0.00));
                    newItem.setCoreTargetRate(dimMap.getOrDefault("target_sign_scenic_cnt",0.00));
                    break;
                case "聚焦"://NOSONAR
                    newItem.setFocusDirectRate(dimMap.getOrDefault("ttd_cps_sign_rate",0.00));
                    newItem.setFocusTargetRate(dimMap.getOrDefault("target_sign_scenic_cnt",0.00));
                    break;
                case "长尾高价值"://NOSONAR
                    newItem.setTailHighValueDirectRate(dimMap.getOrDefault("ttd_cps_sign_rate",0.00));
                    newItem.setTailHighValueTargetRate(dimMap.getOrDefault("target_sign_scenic_cnt",0.00));
                    break;
                case "长尾高产"://NOSONAR
                    newItem.setTailHighYieldDirectRate(dimMap.getOrDefault("ttd_cps_sign_rate",0.00));
                    newItem.setTailHighYieldTargetRate(dimMap.getOrDefault("target_sign_scenic_cnt",0.00));
                    break;
            }
        }
        return response;
    }
    @Override
    public Integer getMetricName() {
        return 4;
    }
}
