package com.ctrip.tour.business.dashboard.grpBusiness.job;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

import com.ctrip.tour.business.dashboard.grpBusiness.bo.OrderEventInfoDTO;
import com.ctrip.tour.business.dashboard.grpBusiness.bo.TrippalDecorateInfoDTO;
import com.ctrip.tour.business.dashboard.grpBusiness.bo.TrippalInfoDTO;
import com.ctrip.tour.business.dashboard.utils.MapperUtil;
import com.ctrip.tour.group.workbenchsvc.contract.StructuredTableInfoType;
import com.ctrip.tour.group.workbenchsvc.contract.TaskPropertyType;
import com.ctrip.tour.group.workbenchsvc.contract.WbCreateTaskRequestType;
import com.ctrip.tour.group.workbenchsvc.contract.WbCreateTaskResponseType;
import com.ctrip.tour.rights.client.GroupWorkbenchServiceClient;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2025/3/25
 */
@Slf4j
public class CommonAlertNotifyService {

    private static final String CREATOR_EID = "CREATOR_EID";
    private static final String EVENT_TYPE = "CREATOR_EID";
    private static final String ALERT_RULE_ID = "ALERT_RULE_ID";

    private GroupWorkbenchServiceClient client = GroupWorkbenchServiceClient.getInstance();
    public void notifyEmp(String empCode, StructuredTableInfoType structuredTableInfoType, String taskName,
                          String taskTypeCode, String eventType, String eventStruTable, String content, List<String> tpInfo, String ruleId) {

        WbCreateTaskRequestType requestType = new WbCreateTaskRequestType();
        requestType.setTaskName(taskName);
        requestType.setTaskTypeCode(taskTypeCode);

        TaskPropertyType creatorProp = new TaskPropertyType();
        creatorProp.setKey(CREATOR_EID);
        creatorProp.setValue("100038120");

        TaskPropertyType ruleProp = new TaskPropertyType();
        ruleProp.setKey(ALERT_RULE_ID);
        ruleProp.setValue(ruleId);

        TaskPropertyType eventTypeProp = new TaskPropertyType();
        eventTypeProp.setKey(eventType);
        OrderEventInfoDTO orderEventInfoDTO = generateEventInfo(Lists.newArrayList(empCode), content, tpInfo, taskName);

        eventTypeProp.setValue(MapperUtil.obj2Str(orderEventInfoDTO));

        TaskPropertyType structProp = new TaskPropertyType();
        structProp.setKey(eventStruTable);
        structProp.setValue(MapperUtil.obj2Str(structuredTableInfoType));

        List<TaskPropertyType> taskPropertyTypes = Lists.newArrayList(creatorProp, eventTypeProp, structProp, ruleProp);

        requestType.setTaskPropertyList(taskPropertyTypes);

        try {
            WbCreateTaskResponseType wbCreateTaskResponseType = client.wbCreateTask(requestType);
            log.info("notify success , task id is" + wbCreateTaskResponseType.getTaskId());
        } catch (Exception e) {
            //工作台把自己业务报错也抛到我们系统了
            log.warn("can not create task ", e);
        }

    }

    private OrderEventInfoDTO generateEventInfo(List<String> empCodes, String content, List<String> tpInfo, String taskName) {

        OrderEventInfoDTO orderEventInfoDTO = new OrderEventInfoDTO();
        orderEventInfoDTO.setProcessorEidList(empCodes);
        orderEventInfoDTO.setContent(content);

        TrippalInfoDTO trippalInfoDTO = new TrippalInfoDTO();
        trippalInfoDTO.setTitle(taskName);

        TrippalDecorateInfoDTO trippalDecorateInfoDTO = new TrippalDecorateInfoDTO();
        trippalDecorateInfoDTO.setIdx(0L);
        trippalDecorateInfoDTO.setType(1);
        trippalDecorateInfoDTO.setTag("p");

        TrippalDecorateInfoDTO childrenDecInfo = new TrippalDecorateInfoDTO();
        childrenDecInfo.setIdx(0L);
        childrenDecInfo.setType(0);
        childrenDecInfo.setText(tpInfo.get(0));
        TrippalDecorateInfoDTO childrenDecInfo2 = null;
        TrippalDecorateInfoDTO childrenDecInfo3 = new TrippalDecorateInfoDTO();
        if (tpInfo.size() > 1) {
            childrenDecInfo2 = new TrippalDecorateInfoDTO();
            childrenDecInfo2.setIdx(1L);
            childrenDecInfo2.setType(1);
            childrenDecInfo2.setTag("font");
            childrenDecInfo2.setAttrs(ImmutableMap.of("color", "red"));
            if (tpInfo.size() > 2) {
                TrippalDecorateInfoDTO childChildrenDecInfo = new TrippalDecorateInfoDTO();
                childChildrenDecInfo.setIdx(0L);
                childChildrenDecInfo.setType(0);
                childChildrenDecInfo.setText(tpInfo.get(1));
                childrenDecInfo2.setChildren(Lists.newArrayList(childChildrenDecInfo));

                childrenDecInfo3.setIdx(2L);
                childrenDecInfo3.setType(0);
                childrenDecInfo3.setText(tpInfo.get(2));
            }
        }



        trippalDecorateInfoDTO.setChildren(Lists.newArrayList(childrenDecInfo, childrenDecInfo2, childrenDecInfo3));
        trippalInfoDTO.setContent(Lists.newArrayList(trippalDecorateInfoDTO));

        orderEventInfoDTO.setTrippalInfo(trippalInfoDTO);

        return orderEventInfoDTO;
    }

    protected String getPrettyDataStr(double data) {
        return BigDecimal.valueOf(data).setScale(2, RoundingMode.HALF_UP).toPlainString();
    }

    protected String getDataRatioStr(double data) {
        return getPrettyDataStr(data * 100) + "%";
    }
}
