package com.ctrip.tour.business.dashboard.utils.time;

import lombok.AllArgsConstructor;
import lombok.Data;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 月份计算器类，用于处理年月相关的日期计算、转换及比较
 */
@Data
@AllArgsConstructor
public class MonthCalculator implements Cloneable, Comparable<MonthCalculator> {
    private Integer year;
    private Integer month;

    /**
     * 默认构造方法，初始化当前年月
     */
    public MonthCalculator() {
        DateTime now = DateTime.now();
        year = now.getYear();
        month = now.getMonthOfYear();
    }

    /**
     * 根据年月字符串创建MonthCalculator实例
     *
     * @param monthString 年月字符串，格式：yyyy-MM
     * @return MonthCalculator实例
     */
    public static MonthCalculator getByMonthString(String monthString) {
        MonthCalculator monthCalculator = new MonthCalculator();
        monthCalculator.setMonthString(monthString);
        return monthCalculator;
    }

    /**
     * 获取两个月份之间的所有月份列表
     *
     * @param start        起始月份
     * @param end          结束月份
     * @param includeStart 是否包含起始月份
     * @param includeEnd   是否包含结束月份
     * @return 月份列表（按时间升序排列）
     */
    public static List<MonthCalculator> getBetween(MonthCalculator start, MonthCalculator end, boolean includeStart, boolean includeEnd) {
        List<MonthCalculator> result = new ArrayList<>();

        while (end.compareTo(start) >= 0) {
            result.add(end);
            end = end.getLastMonth();
        }

        Collections.reverse(result);

        if (!includeStart) {
            result.remove(0);
        }
        if (!includeEnd && result.size() > 0) {
            result.remove(result.size() - 1);
        }
        return result;
    }

    /**
     * 根据日期字符串创建MonthCalculator实例（截取年月部分）
     *
     * @param date 日期字符串，格式：yyyy-MM-dd
     * @return MonthCalculator实例
     */
    public static MonthCalculator getByDate(String date) {
        return MonthCalculator.getByMonthString(date.substring(0, 7));
    }

    /**
     * 获取当前月份的MonthCalculator实例
     *
     * @return 当前月份实例
     */
    public static MonthCalculator getCurrentMonth() {
        String date = DateTime.now().toString("yyyy-MM-dd");
        return getByDate(date);
    }

    /**
     * 获取年月字符串（格式：yyyy-MM）
     *
     * @return 年月字符串，若年或月为null则返回null
     */
    public String getMonthString() {
        if (year != null && month != null) {
            return new DateTime(year, month, 1, 0, 0).toString("yyyy-MM");
        } else {
            return null;
        }
    }

    /**
     * 设置年月字符串（支持格式：yyyy-MM）
     *
     * @param monthString 年月字符串
     */
    public void setMonthString(String monthString) {
        DateTimeFormatter formatter = DateTimeFormat.forPattern("yyyy-MM");
        DateTime dateTime = formatter.parseDateTime(monthString);
        this.year = dateTime.getYear();
        this.month = dateTime.getMonthOfYear();
    }

    /**
     * 获取当月第一天（格式：yyyy-MM-dd）
     *
     * @return 当月第一天字符串
     */
    public String getDateStart() {
        return new DateTime(year, month, 1, 0, 0).toString("yyyy-MM-dd");
    }

    /**
     * 获取当月最后一天（格式：yyyy-MM-dd）
     *
     * @return 当月最后一天字符串
     */
    public String getDateEnd() {
        DateTime dateTime;
        try {
            dateTime = new DateTime(year, month, 1, 0, 0).dayOfMonth().withMaximumValue();
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid month parameter!"); // 不合法的月份参数！ → Invalid month parameter!
        }
        return dateTime.toString("yyyy-MM-dd");
    }

    /**
     * 获取当月天数
     *
     * @return 当月总天数
     */
    public Integer getDateCount() {
        return Days.daysBetween(
                DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(getDateStart()),
                DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(getDateEnd())
        ).getDays() + 1;
    }

    /**
     * 获取当月最后一天（若当月未结束则返回今天）
     *
     * @return 调整后的当月最后一天字符串
     */
    public String getDateEndWithoutFuture() {
        String dateEnd = getDateEnd();
        DateTime now = DateTime.now();
        if (DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(dateEnd).isAfter(now)) {
            return now.toString("yyyy-MM-dd");
        } else {
            return dateEnd;
        }
    }

    /**
     * 获取当月最后一天（若当月未结束则返回昨天）
     *
     * @return 调整后的当月最后一天字符串
     */
    public String getDateEndByYesterday() {
        String dateEnd = getDateEnd();
        DateTime yesterday = DateTime.now().minusDays(1);
        if (DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(dateEnd).isAfter(yesterday)) {
            return yesterday.toString("yyyy-MM-dd");
        } else {
            return dateEnd;
        }
    }

    /**
     * 根据对比月份的周期长度获取当前月份的对应结束日期
     *
     * @param needCompareMonth 对比月份
     * @return 计算后的结束日期字符串
     */
    public String getDateEndWithSamePeriod(MonthCalculator needCompareMonth) {
        DateTime now = DateTime.now();
        String dateEnd = needCompareMonth.getDateEnd();
        if (DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(dateEnd).isBefore(now)) {
            String dateStart = needCompareMonth.getDateStart();
            int days = Days.daysBetween(
                    DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(dateStart),
                    DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(dateEnd)
            ).getDays();
            return DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(getDateStart()).plusDays(days).toString("yyyy-MM-dd");
        } else {
            return getDateEnd();
        }
    }

    /**
     * 根据日期获取上月的结束日期
     *
     * @param date 日期字符串，格式：yyyy-MM-dd
     * @return 上月结束日期字符串
     */
    public static String getDateEndOfLastMonthByDate(String date) {
        DateTimeFormatter df = DateTimeFormat.forPattern("yyyy-MM-dd");
        DateTime dateTime = DateTime.parse(date, df);
        return date.equals(dateTime.dayOfMonth().withMaximumValue().toString(df))
                ? dateTime.minusMonths(1).dayOfMonth().withMaximumValue().toString(df)
                : dateTime.minusMonths(1).toString(df);
    }

    /**
     * 根据日期获取上一年的结束日期
     *
     * @param date 日期字符串，格式：yyyy-MM-dd
     * @return 上一年结束日期字符串
     */
    public static String getDateEndOfLastYearByDate(String date) {
        DateTimeFormatter df = DateTimeFormat.forPattern("yyyy-MM-dd");
        DateTime dateTime = DateTime.parse(date, df);
        return date.equals(dateTime.dayOfMonth().withMaximumValue().toString(df))
                ? dateTime.minusYears(1).dayOfMonth().withMaximumValue().toString(df)
                : dateTime.minusYears(1).toString(df);
    }

    /**
     * 获取当前月份所属的季度
     *
     * @return 季度信息对象
     */
    public QuarterCalculator getQuarter() {
        return QuarterCalculator.getByDate(getDateStart());
    }

    /**
     * 获取包括当前月在内的前N个月列表
     *
     * @param lastMonthCount 月份数量
     * @return 月份列表（按时间升序排列）
     */
    public List<MonthCalculator> getLastMonthByCount(int lastMonthCount) {
        List<MonthCalculator> monthCalculators = new ArrayList<>(lastMonthCount);
        monthCalculators.add(this);
        MonthCalculator monthCalculator = this;
        for (int i = 0; i < lastMonthCount - 1; i++) {
            monthCalculator = monthCalculator.getLastMonth();
            monthCalculators.add(monthCalculator);
        }
        Collections.reverse(monthCalculators);
        return monthCalculators;
    }

    /**
     * 获取上一个月的MonthCalculator实例
     *
     * @return 上一个月实例
     */
    public MonthCalculator getLastMonth() {
        MonthCalculator clone = this.clone();
        DateTime dateTime = new DateTime(clone.getYear(), clone.getMonth(), 1, 0, 0).minusMonths(1);
        clone.setYear(dateTime.getYear());
        clone.setMonth(dateTime.getMonthOfYear());
        return clone;
    }

    /**
     * 获取下一个月的MonthCalculator实例
     *
     * @return 下一个月实例
     */
    public MonthCalculator getNextMonth() {
        MonthCalculator clone = this.clone();
        DateTime dateTime = new DateTime(clone.getYear(), clone.getMonth(), 1, 0, 0).plusMonths(1);
        clone.setYear(dateTime.getYear());
        clone.setMonth(dateTime.getMonthOfYear());
        return clone;
    }

    /**
     * 获取去年同期月份的MonthCalculator实例
     *
     * @return 去年同期月份实例
     */
    public MonthCalculator getLastYearSameMonth() {
        MonthCalculator clone = this.clone();
        DateTime dateTime = new DateTime(clone.getYear(), clone.getMonth(), 1, 0, 0).minusYears(1);
        clone.setYear(dateTime.getYear());
        clone.setMonth(dateTime.getMonthOfYear());
        return clone;
    }

    /**
     * 克隆当前实例
     *
     * @return 克隆后的MonthCalculator实例
     */
    @Override
    protected MonthCalculator clone() {
        return new MonthCalculator(year, month);
    }

    /**
     * 比较两个MonthCalculator实例（按年升序，年相同则按月升序）
     *
     * @param o 待比较的实例
     * @return 比较结果（-1：当前实例小，0：相等，1：当前实例大）
     */
    @Override
    public int compareTo(MonthCalculator o) {
        int yearCompare = this.year - o.getYear();
        if (yearCompare == 0) {
            return this.month - o.getMonth();
        } else {
            return yearCompare;
        }
    }

    /**
     * 获取当月已过天数
     *
     * @return 若为当前月则返回今天日期，若为过去的月则返回当月总天数，若为未来的月则返回0
     */
    public Integer getHaveGoneDays() {
        MonthCalculator currentMonth = new MonthCalculator();
        int compare = currentMonth.getMonthString().compareTo(getMonthString());
        if (compare == 0) {
            return DateTime.now().getDayOfMonth();
        } else if (compare > 0) {
            return new DateTime(year, month, 1, 0, 0).dayOfMonth().withMaximumValue().getDayOfMonth();
        } else {
            return 0;
        }
    }


    /**
     * 获取同比日期范围（不考虑时间窗口概念）
     * @return 同比日期范围
     */
    public DateRange getYoYDateRange() {
        MonthCalculator lastYearSameMonth = getLastYearSameMonth();
        return new DateRange(lastYearSameMonth.getDateStart(), lastYearSameMonth.getDateEnd());
    }

    /**
     * 获取同比日期范围（考虑时间窗口概念）
     * @return 对齐时间窗口的同比日期范围
     */
    public DateRange getYoYDateRangeWithWindow(String windowDate) {
        MonthCalculator lastYearSameMonth = getLastYearSameMonth();
        DateTime now = DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(windowDate);
        DateTime currentMonthEnd = new DateTime(year, month, 1, 0, 0).dayOfMonth().withMaximumValue();

        // 如果当前月份已结束，返回完整月份
        if (now.isAfter(currentMonthEnd)) {
            return new DateRange(lastYearSameMonth.getDateStart(), lastYearSameMonth.getDateEnd());
        }

        // 计算当前月份已过去的天数
        DateTime currentMonthStart = new DateTime(year, month, 1, 0, 0);
        int daysPassed = Days.daysBetween(currentMonthStart, now).getDays() + 1;

        // 计算同比月份对应的结束日期
        DateTime yoyStartDate = new DateTime(lastYearSameMonth.getYear(), lastYearSameMonth.getMonth(), 1, 0, 0);
        DateTime yoyEndDate = yoyStartDate.plusDays(daysPassed - 1);

        // 确保不超过目标月份的最后一天
        DateTime targetMonthEnd = yoyStartDate.dayOfMonth().withMaximumValue();
        if (yoyEndDate.isAfter(targetMonthEnd)) {
            yoyEndDate = targetMonthEnd;
        }

        return new DateRange(yoyStartDate.toString("yyyy-MM-dd"), yoyEndDate.toString("yyyy-MM-dd"));
    }

    /**
     * 获取环比日期范围（不考虑时间窗口概念）
     * @return 环比日期范围
     */
    public DateRange getMoMDateRange() {
        MonthCalculator lastMonth = getLastMonth();
        return new DateRange(lastMonth.getDateStart(), lastMonth.getDateEnd());
    }

    /**
     * 获取环比日期范围（考虑时间窗口概念）
     * @return 对齐时间窗口的环比日期范围
     */
    public DateRange getMoMDateRangeWithWindow(String windowDate) {
        MonthCalculator lastMonth = getLastMonth();
        DateTime now = DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(windowDate);
        DateTime currentMonthEnd = new DateTime(year, month, 1, 0, 0).dayOfMonth().withMaximumValue();

        // 如果当前月份已结束，返回完整月份
        if (now.isAfter(currentMonthEnd)) {
            return new DateRange(lastMonth.getDateStart(), lastMonth.getDateEnd());
        }

        // 计算当前月份已过去的天数
        DateTime currentMonthStart = new DateTime(year, month, 1, 0, 0);
        int daysPassed = Days.daysBetween(currentMonthStart, now).getDays() + 1;

        // 计算环比月份对应的结束日期
        DateTime qoqStartDate = new DateTime(lastMonth.getYear(), lastMonth.getMonth(), 1, 0, 0);
        DateTime qoqEndDate = qoqStartDate.plusDays(daysPassed - 1);

        // 确保不超过目标月份的最后一天
        DateTime targetMonthEnd = qoqStartDate.dayOfMonth().withMaximumValue();
        if (qoqEndDate.isAfter(targetMonthEnd)) {
            qoqEndDate = targetMonthEnd;
        }

        return new DateRange(qoqStartDate.toString("yyyy-MM-dd"), qoqEndDate.toString("yyyy-MM-dd"));
    }


    /**
     * 获取上一季度同期月份（当前月对应上一季度的同月）
     * 例：2023-04（Q2）的上一季度同期为 2023-01（Q1）
     *
     * @return 上一季度同期月份实例
     */
    public MonthCalculator getLastQuarterSameMonth() {
        MonthCalculator clone = this.clone();
        DateTime dateTime = new DateTime(clone.getYear(), clone.getMonth(), 1, 0, 0).minusMonths(3);
        clone.setYear(dateTime.getYear());
        clone.setMonth(dateTime.getMonthOfYear());
        return clone;
    }

    /**
     * 获取上一半年同期月份（当前月对应上一半年的同月）
     * 例：2023-07（H2）的上一半年同期为 2023-01（H1）
     *
     * @return 上一半年同期月份实例
     */
    public MonthCalculator getLastHalfYearSameMonth() {
        MonthCalculator clone = this.clone();
        DateTime dateTime = new DateTime(clone.getYear(), clone.getMonth(), 1, 0, 0).minusMonths(6);
        clone.setYear(dateTime.getYear());
        clone.setMonth(dateTime.getMonthOfYear());
        return clone;
    }

    /**
     * 获取上一季度同期日期范围（不考虑时间窗口）
     *
     * @return 上一季度同期月份的完整日期范围
     */
    public DateRange getLastQuarterDateRange() {
        MonthCalculator lastQuarterSameMonth = getLastQuarterSameMonth();
        return new DateRange(lastQuarterSameMonth.getDateStart(), lastQuarterSameMonth.getDateEnd());
    }

    /**
     * 获取上一季度同期日期范围（考虑时间窗口）
     *
     * @param windowDate 窗口日期，格式：yyyy-MM-dd
     * @return 对齐时间窗口的上一季度同期日期范围
     */
    public DateRange getLastQuarterDateRangeWithWindow(String windowDate) {
        MonthCalculator lastQuarterSameMonth = getLastQuarterSameMonth();
        DateTime windowDateTime = DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(windowDate);
        DateTime currentMonthEnd = DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(getDateEnd());

        // 窗口日期在当前月结束后，返回完整月份范围
        if (windowDateTime.isAfter(currentMonthEnd)) {
            return new DateRange(lastQuarterSameMonth.getDateStart(), lastQuarterSameMonth.getDateEnd());
        }

        // 计算当前月已过天数
        DateTime currentMonthStart = DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(getDateStart());
        int daysPassed = Days.daysBetween(currentMonthStart, windowDateTime).getDays() + 1;

        // 计算上一季度同期月份的对应日期范围
        DateTime compareStartDate = DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(lastQuarterSameMonth.getDateStart());
        DateTime compareEndDate = compareStartDate.plusDays(daysPassed - 1);

        // 处理天数溢出：不超过目标月份的最后一天
        DateTime targetMonthEnd = DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(lastQuarterSameMonth.getDateEnd());
        if (compareEndDate.isAfter(targetMonthEnd)) {
            compareEndDate = targetMonthEnd;
        }

        return new DateRange(lastQuarterSameMonth.getDateStart(), compareEndDate.toString("yyyy-MM-dd"));
    }

    /**
     * 获取上一半年同期日期范围（不考虑时间窗口）
     *
     * @return 上一半年同期月份的完整日期范围
     */
    public DateRange getLastHalfYearDateRange() {
        MonthCalculator lastHalfYearSameMonth = getLastHalfYearSameMonth();
        return new DateRange(lastHalfYearSameMonth.getDateStart(), lastHalfYearSameMonth.getDateEnd());
    }

    /**
     * 获取上一半年同期日期范围（考虑时间窗口）
     *
     * @param windowDate 窗口日期，格式：yyyy-MM-dd
     * @return 对齐时间窗口的上一半年同期日期范围
     */
    public DateRange getLastHalfYearDateRangeWithWindow(String windowDate) {
        MonthCalculator lastHalfYearSameMonth = getLastHalfYearSameMonth();
        DateTime windowDateTime = DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(windowDate);
        DateTime currentMonthEnd = DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(getDateEnd());

        // 窗口日期在当前月结束后，返回完整月份范围
        if (windowDateTime.isAfter(currentMonthEnd)) {
            return new DateRange(lastHalfYearSameMonth.getDateStart(), lastHalfYearSameMonth.getDateEnd());
        }

        // 计算当前月已过天数
        DateTime currentMonthStart = DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(getDateStart());
        int daysPassed = Days.daysBetween(currentMonthStart, windowDateTime).getDays() + 1;

        // 计算上一半年同期月份的对应日期范围
        DateTime compareStartDate = DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(lastHalfYearSameMonth.getDateStart());
        DateTime compareEndDate = compareStartDate.plusDays(daysPassed - 1);

        // 处理天数溢出：不超过目标月份的最后一天
        DateTime targetMonthEnd = DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(lastHalfYearSameMonth.getDateEnd());
        if (compareEndDate.isAfter(targetMonthEnd)) {
            compareEndDate = targetMonthEnd;
        }

        return new DateRange(lastHalfYearSameMonth.getDateStart(), compareEndDate.toString("yyyy-MM-dd"));
    }

    /**
     * 判断两个MonthCalculator实例是否相等（基于年和月）
     *
     * @param o 待比较对象
     * @return 是否相等
     */
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof MonthCalculator)) return false;

        MonthCalculator that = (MonthCalculator) o;

        if (getYear() != null ? !getYear().equals(that.getYear()) : that.getYear() != null) return false;
        return getMonth() != null ? getMonth().equals(that.getMonth()) : that.getMonth() == null;
    }

    /**
     * 计算哈希码（基于年和月）
     *
     * @return 哈希码
     */
    @Override
    public int hashCode() {
        int result = getYear() != null ? getYear().hashCode() : 0;
        result = 31 * result + (getMonth() != null ? getMonth().hashCode() : 0);
        return result;
    }

    /**
     * 转换为字符串（返回年月字符串）
     *
     * @return 年月字符串（格式：yyyy-MM）
     */
    @Override
    public String toString() {
        return getMonthString();
    }
}
