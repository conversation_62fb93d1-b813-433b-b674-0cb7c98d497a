package com.ctrip.tour.business.dashboard.tktBusiness.select43556;

import com.ctrip.soa._27181.GetRawDataRequestType;
import com.ctrip.soa._27181.PreparedParameterBean;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.TaskFlowHelper;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class JdbcUtil {
    /**
     * 拼接select子句
     *
     * @param sb
     * @param request
     * @param basicConfig
     * @param whiteListMap
     */
    public static void jointSelectClause(StringBuilder sb,
                                         GetRawDataRequestType request,
                                         ReportQueryBasicConfig basicConfig,
                                         Map<String, ReportQueryWhitelist> whiteListMap,
                                         List<PreparedParameterBean> parameters,
                                         String drillDownMetric) {
        List<String> groupList = request.getGroupList();
        Map<String,String> andMap = request.getAndMap();
        String script = basicConfig.getScript();
//        script = "COUNT(DISTINCT CASE WHEN status_id = '5' AND the_date >= '{startDate}' AND the_date <= '{endDate}' THEN event_id END) / COUNT(DISTINCT CASE WHEN the_date >= '{startDate}' AND the_date <= '{endDate}' THEN event_id END) AS complete_rate , \n" +
//                "COUNT(DISTINCT CASE WHEN is_ontime = 1 AND the_date >= '{startDate}' AND the_date <= '{endDate}' THEN event_id END) / COUNT(DISTINCT CASE WHEN the_date >= '{startDate}' AND the_date <= '{endDate}' THEN event_id END) AS ontime_complete_rate , \n" +
//                "COUNT(DISTINCT CASE WHEN is_overtime = 1 AND the_date >= '{startDate}' AND the_date <= '{endDate}' THEN event_id END) AS overtime_event_cnt , \n" +
//                "SUM(DISTINCT CASE WHEN the_date >= '{startDate}' AND the_date <= '{endDate}' THEN complete_duration + power(0.01 / log10(CAST(event_id AS BIGINT)), 2) END) / COUNT(DISTINCT CASE WHEN status_id = '5' AND the_date >= '{startDate}' AND the_date <= '{endDate}' THEN event_id END) AS average_process_time , \n" +
//                "COUNT(DISTINCT CASE WHEN status_id = '5' AND the_date >= '{startDate}' AND the_date <= '{endDate}' THEN event_id END) AS complete_task_cnt , \n" +
//                "COUNT(DISTINCT CASE WHEN is_ontime = 1 AND the_date >= '{startDate}' AND the_date <= '{endDate}' THEN event_id END) AS ontime_complete_task_cnt , \n" +
//                "COUNT(DISTINCT CASE WHEN the_date >= '{startDate}' AND the_date <= '{endDate}' THEN event_id END) AS recieve_task_cnt , \n" +
//                "COUNT(DISTINCT CASE WHEN TO_DATE(send_time) >= '{startDate}' AND TO_DATE(send_time) <= '{endDate}' THEN event_id END) AS new_add_task_cnt";


        String tableName = basicConfig.getTablename();

        sb.append("select ");
        if (GeneralUtil.isNotEmpty(groupList)) {
            for (String groupTag : groupList) {
                if (!whiteListMap.containsKey(groupTag)) {
                    throw new InputArgumentException(10003);
                }
                sb.append(groupTag).append(",");
            }
        }
        Boolean needScript = request.isNeedScript();
        if (needScript) {

            String theDate = andMap.get("the_date");
            if(StringUtils.isNotBlank(theDate)){
                List<String> valueList = Lists.newArrayList(theDate.split("\\|"));
                String startDate = valueList.get(0);
                String endDate = valueList.get(1);
//            for(int i = 0; i < 1; i++){
//                parameters.add(getParameterBean(1, startDate));
//                parameters.add(getParameterBean(1, endDate));
//            }
                script = script.replace("{startDate}", startDate);
                script = script.replace("{endDate}", endDate);
                sb.append(script);
            }

        } else {
            sb.deleteCharAt(sb.length() - 1);
        }
        sb.append(" from ").append(tableName).append(" where 1=1 ");

        String dateRange = request.getAndMap().get("the_date");

        if (GeneralUtil.isNotEmpty(groupList)) {
            for (String groupTag : groupList) {    //根据the_date和send_date分组时，均为趋势线场景
                if ("the_date".equals(groupTag)) {      //趋势线 非new_task_cnt指标
                    request.getAndMap().remove("send_date");
                } else if ("send_date".equals(groupTag)) {    //趋势线  new_task_cnt指标
                    request.getAndMap().remove("the_date");
                } else {   //其它需分组的场景
                    request.getAndMap().remove("the_date");
                }
            }
        } else {   //不分组的场景
            request.getAndMap().remove("the_date");
        }

        if(StringUtils.isNotBlank(drillDownMetric)){
            request.getAndMap().put(TaskFlowHelper.getMetricTimeColumn(drillDownMetric),dateRange);
        }

    }


    /**
     * 拼接where子句
     *
     * @param sb
     * @param request
     * @param whiteListMap
     * @param parameters
     */
    public static void jointWhereClause(StringBuilder sb,
                                        GetRawDataRequestType request,
                                        Map<String, ReportQueryWhitelist> whiteListMap,
                                        List<PreparedParameterBean> parameters) {
        Map<String, String> andMap = request.getAndMap();
        Map<String, String> notInMap = request.getNotInMap();
        Map<String, String> likeMap = request.getLikeMap();
        Map<String, String> notLikeMap = request.getNotLikeMap();
        Map<String, String> orMap = request.getOrMap();
        jointAndMap(sb, andMap, whiteListMap, parameters);
        jointNotInMap(sb, notInMap, whiteListMap, parameters);
        jointLikeMap(sb, likeMap, whiteListMap, parameters);
        jointNotLikeMap(sb, notLikeMap, whiteListMap, parameters);
        jointOrMap(sb, orMap, whiteListMap, parameters);
    }

    /**
     * 拼接group子句
     * @param sb
     * @param request
     * @param whiteListMap
     */
    public static void jointGroupClause(StringBuilder sb,
                                        GetRawDataRequestType request,
                                        Map<String, ReportQueryWhitelist> whiteListMap) {
        List<String> groupList = request.getGroupList();
        if (GeneralUtil.isNotEmpty(groupList)) {
            sb.append(" group by ");
            for (String groupTag : groupList) {
                if (!whiteListMap.containsKey(groupTag)) {
                    throw new InputArgumentException(10003);
                }
                sb.append(groupTag).append(",");

            }
            sb.deleteCharAt(sb.length() - 1);
        }
    }

    /**
     * 拼接order子句
     *
     * @param sb
     * @param request
     * @param whiteListMap
     */
    public static void jointOrderClause(StringBuilder sb,
                                        GetRawDataRequestType request,
                                        Map<String, ReportQueryWhitelist> whiteListMap) {
        List<String> orderList = request.getOrderList();
        List<String> orderTypeList = request.getOrderTypeList();
        if (GeneralUtil.isNotEmpty(orderList)) {
            sb.append(" order by ");
            for (int i = 0; i < orderList.size(); i++) {
                String orderTag = orderList.get(i);
                if (!whiteListMap.containsKey(orderTag)) {
                    throw new InputArgumentException(10003);
                }
                ReportQueryWhitelist whitelist = whiteListMap.get(orderTag);
                String calculateType = whitelist.getCalculateType();
                if (GeneralUtil.isNotEmpty(calculateType)) {
                    orderTag = calculateType;
                }
                sb.append(orderTag).append(" ").append(orderTypeList.get(i)).append(",");
            }
            sb.deleteCharAt(sb.length() - 1);
        }
    }

    /**
     * 生成count(*)子句
     * @param sb
     * @param request
     * @param parameters
     * @param sqlBeanList
     */
    public static void setCountQuery(StringBuilder sb,
                                     GetRawDataRequestType request,
                                     List<PreparedParameterBean> parameters,
                                     String dbName,
                                     List<GenerateSqlBean> sqlBeanList){
        Integer pageNo = request.getPageNo();
        Integer pageSize = request.getPageSize();
        if(GeneralUtil.isNotEmpty(pageNo) && GeneralUtil.isNotEmpty(pageSize)){
            GenerateSqlBean sqlBean = new GenerateSqlBean();
            List<PreparedParameterBean> countParam = new ArrayList<>(parameters);
            sqlBean.setSql("select count(*) from (" + sb + ") aa");
            sqlBean.setDbName(dbName);
            sqlBean.setNeedCount(true);
            sqlBean.setPreparedParameters(countParam);
            sqlBean.setEngine("starrocks");
            sqlBeanList.add(sqlBean);
        }
    }

    /**
     * 拼接limit子句
     * @param sb
     * @param request
     * @param parameters
     */
    public static void jointLimitClause(StringBuilder sb,
                                        GetRawDataRequestType request,
                                        List<PreparedParameterBean> parameters){
        Integer pageNo = request.getPageNo();
        Integer pageSize = request.getPageSize();
        if(GeneralUtil.isNotEmpty(pageNo) && GeneralUtil.isNotEmpty(pageSize)){
            sb.append(" limit ?,? ");
//            int index = parameters.nextIndex();
//            parameters.set(index++,(pageNo-1)*pageSize);
//            parameters.set(index++,pageSize);
            parameters.add(getParameterBean(2, String.valueOf((pageNo-1)*pageSize)));
            parameters.add(getParameterBean(2, String.valueOf(pageSize)));
        }
    }

    private static void jointOrMap(StringBuilder sb,
                                   Map<String, String> orMap,
                                   Map<String, ReportQueryWhitelist> whiteListMap,
                                   List<PreparedParameterBean> parameters) {
        if (GeneralUtil.isNotEmpty(orMap)) {
            sb.append(" and ( ");
            for (Map.Entry<String, String> entry : orMap.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();
                if (!whiteListMap.containsKey(key)) {
                    throw new InputArgumentException(10003);
                }
                List<String> valueList = Lists.newArrayList(value.split("\\|"));
                ReportQueryWhitelist whitelist = whiteListMap.get(key);
                jointAndCondition(sb, whitelist, key, valueList, parameters);
                sb.append(" or ");
            }
            int curLength = sb.length();
            //删除多余的or
            sb.delete(curLength - 3, curLength);
            sb.append(" ) ");
        }
    }

    /**
     * 拼接likeMap条件
     *
     * @param sb
     * @param notLikeMap
     * @param whiteListMap
     * @param parameters
     */
    private static void jointNotLikeMap(StringBuilder sb,
                                        Map<String, String> notLikeMap,
                                        Map<String, ReportQueryWhitelist> whiteListMap,
                                        List<PreparedParameterBean> parameters) {
        if (GeneralUtil.isNotEmpty(notLikeMap)) {
            sb.append(" and ( ");
            for (Map.Entry<String, String> entry : notLikeMap.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();
                String[] valueArr = value.split("\\|");
                if (!whiteListMap.containsKey(key)) {
                    throw new InputArgumentException(10003);
                }
                sb.append("(");
                for (String item : valueArr) {
                    sb.append(key).append(" not like ? ");
                    parameters.add(getParameterBean(3, "%" + item + "%"));
                    sb.append(" or ");
                }
                int curLength = sb.length();
                //删除多余的or
                sb.delete(curLength - 3, curLength);
                sb.append(")");
                sb.append(" and ");
            }
            int curLength = sb.length();
            //删除多余的and
            sb.delete(curLength - 4, curLength);
            sb.append(" ) ");
        }

    }

    /**
     * 拼接likeMap条件
     *
     * @param sb
     * @param likeMap
     * @param whiteListMap
     * @param parameters
     */
    private static void jointLikeMap(StringBuilder sb,
                                     Map<String, String> likeMap,
                                     Map<String, ReportQueryWhitelist> whiteListMap,
                                     List<PreparedParameterBean> parameters) {
        if (GeneralUtil.isNotEmpty(likeMap)) {
            sb.append(" and ( ");
            for (Map.Entry<String, String> entry : likeMap.entrySet()) {
                String originKey = entry.getKey();
                String[] keyArr = originKey.split("\\|");
                String value = entry.getValue();
                String[] valueArr = value.split("\\|");
                sb.append("(");
                for (String key : keyArr) {
                    if (!whiteListMap.containsKey(key)) {
                        throw new InputArgumentException(10003);
                    }
                    for (String item : valueArr) {
                        sb.append(key).append(" like ? ");
                        parameters.add(getParameterBean(3, "%" + item + "%"));
                        sb.append(" or ");
                    }
                }
                int curLength = sb.length();
                //删除多余的or
                sb.delete(curLength - 3, curLength);
                sb.append(")");
                sb.append(" and ");
            }
            int curLength = sb.length();
            //删除多余的and
            sb.delete(curLength - 4, curLength);
            sb.append(" ) ");
        }

    }


    /**
     * 拼接notInMap
     *
     * @param sb
     * @param notInMap
     * @param whiteListMap
     * @param parameters
     */
    private static void jointNotInMap(StringBuilder sb,
                                      Map<String, String> notInMap,
                                      Map<String, ReportQueryWhitelist> whiteListMap,
                                      List<PreparedParameterBean> parameters) {
        if (GeneralUtil.isNotEmpty(notInMap)) {
            sb.append(" and ");
            for (Map.Entry<String, String> entry : notInMap.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();
                if (!whiteListMap.containsKey(key)) {
                    throw new InputArgumentException(10003);
                }
                ReportQueryWhitelist whitelist = whiteListMap.get(key);
                List<String> valueList = Lists.newArrayList(value.split("\\|"));
                String n = addInKeyValue(parameters, valueList, whitelist.getColumnType());
                sb.append(key).append(String.format(" not in (%s) ", n));
//                jointAndCondition(sb, whitelist, key, valueList, parameters);
                sb.append(" and ");
            }
            int curLength = sb.length();
            //删除多余的and
            sb.delete(curLength - 4, curLength);
        }
    }

    public static String addInKeyValue(List<PreparedParameterBean> parameters, List<String> valueList, int columnType){
        List<String> n = new ArrayList<>();
        for (String v : valueList) {
            n.add("?");
            parameters.add(getParameterBean(columnType, v));
        }
        return String.join(",", n);
    }


    /**
     * 拼接andMap
     *
     * @param sb
     * @param andMap
     * @param whiteListMap
     * @param parameters
     */
    private static void jointAndMap(StringBuilder sb,
                                    Map<String, String> andMap,
                                    Map<String, ReportQueryWhitelist> whiteListMap,
                                    List<PreparedParameterBean> parameters) {
        if (GeneralUtil.isNotEmpty(andMap)) {
            sb.append(" and ");
            for (Map.Entry<String, String> entry : andMap.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();
                if (!whiteListMap.containsKey(key)) {
                    throw new InputArgumentException(10003);
                }
                if(StringUtils.isBlank(value)){
                    continue;
                }
                List<String> valueList = Lists.newArrayList(value.split("\\|"));
                ReportQueryWhitelist whitelist = whiteListMap.get(key);
                jointAndCondition(sb, whitelist, key, valueList, parameters);
                sb.append(" and ");
            }
            int curLength = sb.length();
            //删除多余的and
            sb.delete(curLength - 4, curLength);
        }
    }


    /**
     * 拼接实际的and条件类型
     *
     * @param sb
     * @param whitelist
     * @param key
     * @param valueList
     * @param parameters
     */
    private static void jointAndCondition(StringBuilder sb,
                                          ReportQueryWhitelist whitelist,
                                          String key,
                                          List<String> valueList,
                                          List<PreparedParameterBean> parameters) {
        int columnType = whitelist.getColumnType();
        int columnCondition = whitelist.getColumnCondition();
        switch (columnCondition) {
            case 1:
                sb.append(key).append(String.format(" in (%s) ", addInKeyValue(parameters, valueList, whitelist.getColumnType())));
                // parameters.add(getParameterBean(columnType, valueList));
                break;
            case 2:
                sb.append("( ").append(key).append(" between ? and ?) ");
                parameters.add(getParameterBean(columnType, valueList.get(0)));
                parameters.add(getParameterBean(columnType, valueList.get(1)));
                break;
            case 3:
                sb.append(key).append(String.format(" not in (%s) ", addInKeyValue(parameters, valueList, whitelist.getColumnType())));
                // parameters.add(getParameterBean(4, valueList));
                break;
            default:
                throw new ConfigException(10004);
        }

    }

    public static PreparedParameterBean getParameterBean(int columnType, String value) {
        PreparedParameterBean bean = new PreparedParameterBean();
        bean.setType(columnType);
        bean.setValue(value);
        return bean;
    }


}
