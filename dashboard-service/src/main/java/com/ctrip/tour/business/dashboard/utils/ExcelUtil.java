package com.ctrip.tour.business.dashboard.utils;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.ss.usermodel.Sheet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class ExcelUtil {
    private static final Logger logger = LoggerFactory.getLogger(ExcelUtil.class);

    /**
     * 把数据写入excel
     *
     * @param sheetName
     * @param heads
     * @param resultList
     * @return: 文件路径
     */
    /*public static String writeExcel(String base, String sheetName, List<String> heads, List<List<String>> resultList) {
        File basePath = new File(base);
        XSSFWorkbook workbook = new XSSFWorkbook(); // 创建Excel文件
        Sheet sheet = workbook.createSheet(sheetName); // 创建工作表

        // 添加表头
        Row headerRow = sheet.createRow(0);
        for (int i = 0; i < heads.size(); i++) {
            Cell headerCell = headerRow.createCell(i);
            headerCell.setCellValue(heads.get(i));
        }
        // 添加数据
        for (int i = 0; i < resultList.size(); i++) {
            Row dataRow = sheet.createRow(i + 1);
            List<String> result = resultList.get(i);
            for (int j = 0; j < result.size(); j++) {
                Cell dataCell = dataRow.createCell(j);
                dataCell.setCellValue(result.get(j));
            }
        }
        // 保存Excel文件
        // File file = new File(String.format("%s.xlsx", sheetName));
        File file = new File(basePath.getAbsoluteFile() + String.format("/%s.xlsx", sheetName));
        try (FileOutputStream outputStream = new FileOutputStream(file)) {
            workbook.write(outputStream);
            logger.info("excel success");
        } catch (IOException e) {
            logger.warn("excel fail; reason:" + e.getMessage());
            return null;
        }
        return file.getAbsolutePath();
    }*/

    /**
     * 把数据写入excel 生成输出字节数组
     * @param sheetName
     * @param heads
     * @param resultList
     * @return
     * @throws IOException
     */
    public static byte[] getExcelStream(String sheetName, List<String> heads, List<List<String>> resultList) throws IOException {
        XSSFWorkbook workbook = new XSSFWorkbook(); // 创建Excel文件
        Sheet sheet = workbook.createSheet(sheetName); // 创建工作表
        // 添加表头
        Row headerRow = sheet.createRow(0);
        for (int i = 0; i < heads.size(); i++) {
            Cell headerCell = headerRow.createCell(i);
            headerCell.setCellValue(heads.get(i));
        }
        // 添加数据
        for (int i = 0; i < resultList.size(); i++) {
            Row dataRow = sheet.createRow(i + 1);
            List<String> result = resultList.get(i);
            for (int j = 0; j < result.size(); j++) {
                Cell dataCell = dataRow.createCell(j);
                dataCell.setCellValue(result.get(j));
            }
        }
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        try {
            workbook.write(bos);
            logger.info("get excel stream success");
        } catch (Exception e){
            logger.warn("get excel stream fail; reason:" + e.getMessage());
            return null;
        }finally {
            bos.close();
        }
        return bos.toByteArray();
    }


    /**
     * 读取excel 获取字节流
     *
     * @param fileName
     * @return
     * @throws IOException
     */
    public static byte[] readExcel(String fileName) throws IOException {
        InputStream inputStream = new BufferedInputStream(Files.newInputStream(Paths.get(fileName))); // 创建BufferedInputStream对象
        byte[] byteArray = new byte[inputStream.available()]; // 创建byte数组
        inputStream.read(byteArray); // 读取byte数组
        inputStream.close(); // 关闭流
        return byteArray;
    }

//    public static void main(String[] args) throws IOException {
//        List<String> heads = Arrays.asList("a", "b");
//        List<List<String>> resultList = Arrays.asList(
//                Arrays.asList("c", "d"),
//                Arrays.asList("e", "ggg")
//        );
//        byte[] bs = getExcelStream("sheet1", heads, resultList);
//        System.out.println(bs);
//    }


    public static byte[] outputExcel(List<ExcelSheetData> sheetDataList) {
        try (SXSSFWorkbook workbook = new SXSSFWorkbook();
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            workbook.setCompressTempFiles(true);

            for (ExcelSheetData sheetData : sheetDataList) {
                SXSSFSheet sheet = workbook.createSheet(sheetData.getSheetName());

                Row headerRow = sheet.createRow(0);
                List<String> heads = sheetData.getHeads();
                for (int i = 0; i < heads.size(); i++) {
                    Cell headerCell = headerRow.createCell(i);
                    headerCell.setCellValue(heads.get(i));
                }

                List<List<String>> resultList = sheetData.getResultList();
                for (int i = 0; i < resultList.size(); i++) {
                    Row dataRow = sheet.createRow(i + 1);
                    List<String> result = resultList.get(i);
                    for (int j = 0; j < result.size(); j++) {
                        Cell dataCell = dataRow.createCell(j);
                        dataCell.setCellValue(result.get(j));
                    }
                }
                sheet.flushRows();
            }

            workbook.write(outputStream);
            return outputStream.toByteArray();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

}
