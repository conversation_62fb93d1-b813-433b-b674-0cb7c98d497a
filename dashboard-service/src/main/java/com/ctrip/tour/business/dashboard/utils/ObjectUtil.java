package com.ctrip.tour.business.dashboard.utils;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;

public class ObjectUtil {

    public static <T> T deepJsonCopy(Object object, Class<T> clazz) {
        return JSONObject.parseObject(JSONObject.toJSONString(object), clazz);
    }

    public static <T> T deepJsonCopy(Object object, TypeReference<T> typeReference) {
        return JSONObject.parseObject(JSONObject.toJSONString(object), typeReference);
    }
}
