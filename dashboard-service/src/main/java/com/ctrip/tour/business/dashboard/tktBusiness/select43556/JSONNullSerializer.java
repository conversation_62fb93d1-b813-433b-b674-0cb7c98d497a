package com.ctrip.tour.business.dashboard.tktBusiness.select43556;

import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonToken;
import com.google.gson.stream.JsonWriter;
import net.sf.json.JSONNull;

import java.io.IOException;

public class JSONNullSerializer extends TypeAdapter<JSONNull> {
    @Override
    public void write(<PERSON>son<PERSON>rite<PERSON> out, JSONNull value) throws IOException {
        out.nullValue();
    }

    @Override
    public JSONNull read(JsonReader in) throws IOException {
        if(in.peek()== JsonToken.NULL){
            in.nextNull();
            return JSONNull.getInstance();
        }
        return null;
    }
}
