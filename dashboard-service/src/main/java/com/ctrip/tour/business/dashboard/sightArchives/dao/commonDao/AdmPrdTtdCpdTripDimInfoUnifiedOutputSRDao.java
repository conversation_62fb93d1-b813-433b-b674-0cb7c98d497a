package com.ctrip.tour.business.dashboard.sightArchives.dao.commonDao;


import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.base.SQLResult;
import com.ctrip.soa._24922.Sight;
import com.ctrip.soa._27181.PreparedParameterBean;
import com.ctrip.tour.business.dashboard.sightArchives.bean.ExamineRangeBO;
import com.ctrip.tour.business.dashboard.sightArchives.entity.commonEntity.AdmPrdTtdCpdTripDimInfoUnifiedOutput;
import com.ctrip.tour.business.dashboard.sightArchives.enums.common.ExamineLevelEnumType;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ExamineTypeBean;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.TktStarRocksDao;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.ScenicLanguageHelper;
import com.ctrip.tour.business.dashboard.utils.UserUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.sql.Types;
import java.util.*;
import java.util.stream.Collectors;

@Repository
@Slf4j
public class AdmPrdTtdCpdTripDimInfoUnifiedOutputSRDao {

    @Autowired
    TktStarRocksDao tktStarRocksDao;


    public AdmPrdTtdCpdTripDimInfoUnifiedOutput querySightInfo(Long sightId,String queryD) {
        StringBuilder sql = new StringBuilder("select * from adm_prd_ttd_cpd_trip_dim_info_unified_output where d = ? and viewspot_id = ?");

        AdmPrdTtdCpdTripDimInfoUnifiedOutput admPrdTtdCpdTripDimInfoUnifiedOutput = new AdmPrdTtdCpdTripDimInfoUnifiedOutput();

        List<PreparedParameterBean> parameters = new ArrayList<>();
        parameters.add(new PreparedParameterBean(queryD, Types.VARCHAR));
        parameters.add(new PreparedParameterBean(String.valueOf(sightId), Types.BIGINT));
        List<Map<String, Object>> resultMapList = null;
        try {
            resultMapList = tktStarRocksDao.getListResultNew(sql.toString(),parameters);
        } catch (SQLException e) {
            log.warn("queryCompetitiveSightIdList error", e);
        }

        if (CollectionUtils.isEmpty(resultMapList)) {
            return null;
        }

        for(Map<String, Object> resultMap : resultMapList){
            admPrdTtdCpdTripDimInfoUnifiedOutput.setViewspotId((Long) resultMap.get("viewspot_id"));
            admPrdTtdCpdTripDimInfoUnifiedOutput.setViewspotName((String) resultMap.get("viewspot_name"));
            admPrdTtdCpdTripDimInfoUnifiedOutput.setCityName((String) resultMap.get("city_name"));
            admPrdTtdCpdTripDimInfoUnifiedOutput.setCountryName((String) resultMap.get("country_name"));
            admPrdTtdCpdTripDimInfoUnifiedOutput.setIsDomestic((Long) resultMap.get("is_domestic"));
            admPrdTtdCpdTripDimInfoUnifiedOutput.setCityId((Long) resultMap.get("city_id"));
            admPrdTtdCpdTripDimInfoUnifiedOutput.setCountryId((Long) resultMap.get("country_id"));
            admPrdTtdCpdTripDimInfoUnifiedOutput.setParentViewspotId((Long) resultMap.get("parent_viewspot_id"));
            admPrdTtdCpdTripDimInfoUnifiedOutput.setParentViewspotName((String) resultMap.get("parent_viewspot_name"));
            admPrdTtdCpdTripDimInfoUnifiedOutput.setBusinessRegionName((String) resultMap.get("business_region_name"));
            admPrdTtdCpdTripDimInfoUnifiedOutput.setIsActive((Long) resultMap.get("is_active"));
            admPrdTtdCpdTripDimInfoUnifiedOutput.setIsHasSubViewspot((Long) resultMap.get("is_has_sub_viewspot"));
            admPrdTtdCpdTripDimInfoUnifiedOutput.setOriViewspotAeid((String) resultMap.get("ori_viewspot_aeid"));
            admPrdTtdCpdTripDimInfoUnifiedOutput.setOriViewspotMeid((String) resultMap.get("ori_viewspot_meid"));
            admPrdTtdCpdTripDimInfoUnifiedOutput.setProvinceId((Long) resultMap.get("province_id"));
            admPrdTtdCpdTripDimInfoUnifiedOutput.setProvinceName((String) resultMap.get("province_name"));
            admPrdTtdCpdTripDimInfoUnifiedOutput.setViewspotClass((String) resultMap.get("viewspot_class"));
            admPrdTtdCpdTripDimInfoUnifiedOutput.setViewspotLevel(String.valueOf(resultMap.get("viewspot_level")));
            admPrdTtdCpdTripDimInfoUnifiedOutput.setId((Long) resultMap.get("id"));
            admPrdTtdCpdTripDimInfoUnifiedOutput.setPoiEnName(String.valueOf(resultMap.get("poi_en_name")));
            try {
                admPrdTtdCpdTripDimInfoUnifiedOutput.setPoiId(Long.valueOf((String)resultMap.get("poi_id")));
            }catch (Exception e){
                admPrdTtdCpdTripDimInfoUnifiedOutput.setPoiId(0L);
            }
        }


        return admPrdTtdCpdTripDimInfoUnifiedOutput;

    }



    public List<Sight> fuzzyQuerySightList(String queryD, List<ExamineRangeBO> examineRangeBOList, String searchKey) {

        String language = UserUtil.getVbkLocaleForScenic();

        StringBuilder sql = new StringBuilder("select viewspot_id,viewspot_name,poi_en_name from adm_prd_ttd_cpd_trip_dim_info_unified_output where d= ?");
        List<PreparedParameterBean> parameters = new ArrayList<>();
        parameters.add(new PreparedParameterBean(queryD, Types.VARCHAR));

        //限制查询出的景点在此范围内
        for(int j = 0; j<examineRangeBOList.size(); j++){
            ExamineRangeBO examineRangeBO = examineRangeBOList.get(j);
            String columnName = examineRangeBO.getExamineLevel().getColumnName();
            Set<String> examineRange = examineRangeBO.getExamineRange();
            List<String> examineRangeList = new ArrayList<>(examineRange);
            if (j==0){
                sql.append(" and ( ");//第一个加左括号
            }else {
                sql.append(" or ");
            }
            if(ExamineLevelEnumType.DOMESTIC_SIGHT.equals(examineRangeBO.getExamineLevel())){
                //如果是考核国内的景点层级，需要like匹配景点经理，因为表里该字段下会把多个景点经理拼在一起
                //遍历examineRangeList
                sql.append(" ( ");
                for (int i=0; i<examineRangeList.size(); i++) {
                    //除第一个以外，后面的都加or
                    if(i!=0){
                        sql.append(" or ");
                    }
                    sql.append(columnName).append(" like \"%").append(examineRangeList.get(i)).append("%\""); //NOSONAR
                }
                sql.append(" ) ");
            }else {
                //如果是考核其他的层级，直接in
                sql.append(columnName).append(" in (\"").append(String.join("\",\"", examineRange)).append("\")");
            }
            if(j == examineRangeBOList.size() - 1){
                sql.append(" ) ");//最后一个加右括号
            }

        }
        if(StringUtils.isNotBlank(searchKey)){
            //如果searchkey不是数字，不匹配景点id，只匹配景点名称
                if(NumberUtils.isNumber(searchKey) ){
                    if ("en-US".equalsIgnoreCase(language)) {
                        sql.append(" and (viewspot_id = ").append(searchKey).append(" or poi_en_name like \"%").append(searchKey).append("%\")");
                    } else {
                        sql.append(" and (viewspot_id = ").append(searchKey).append(" or viewspot_name like \"%").append(searchKey).append("%\")");
                    }
                }else {
                    if ("en-US".equalsIgnoreCase(language)) {
                        sql.append(" and poi_en_name like '%").append(searchKey).append("%'");
                    } else {
                        sql.append(" and viewspot_name like '%").append(searchKey).append("%'");
                    }

                }
        }
        //按景点分层排序: 核心>聚焦>长尾
        sql.append(" order by case when viewspot_class = '核心' then 1 when viewspot_class = '聚焦' then 2 when viewspot_class = '长尾' then 3 else 4 end asc "); //NOSONAR

        //限制返回的景点<=50个
        sql.append(" limit 50");

        List<Map<String, Object>> resultMapList = null;
        try {
            resultMapList = tktStarRocksDao.getListResultNew(sql.toString(),parameters);
        } catch (SQLException e) {
            log.warn("queryCompetitiveSightIdList error", e);
        }

        if (CollectionUtils.isEmpty(resultMapList)) {
            return new ArrayList<>();
        }

        List<Sight> sightList = new ArrayList<>();
        for(Map<String, Object> resultMap : resultMapList){
            Sight sight = new Sight();
            sight.setSightId((Long) resultMap.get("viewspot_id"));
            if ("en-US".equalsIgnoreCase(language) && resultMap.get("poi_en_name") != null) {
                sight.setSightName((String) resultMap.get("poi_en_name"));
            } else {
                sight.setSightName((String) resultMap.get("viewspot_name"));
            }
            sightList.add(sight);
        }

        return sightList;
    }


    public List<Sight> preciseQuerySightList(String queryD, List<ExamineRangeBO> examineRangeBOList, String searchKey) {

        String language = UserUtil.getVbkLocaleForScenic();

        if(StringUtils.isBlank(searchKey)){
            return new ArrayList<>();
        }
        StringBuilder sql = new StringBuilder("select viewspot_id,viewspot_name,poi_en_name from adm_prd_ttd_cpd_trip_dim_info_unified_output where d= ?");
        List<PreparedParameterBean> parameters = new ArrayList<>();
        parameters.add(new PreparedParameterBean(queryD, Types.VARCHAR));

        //限制查询出的景点在此范围内
        for(ExamineRangeBO examineRangeBO : examineRangeBOList){
            String columnName = examineRangeBO.getExamineLevel().getColumnName();
            Set<String> examineRange = examineRangeBO.getExamineRange();
            sql.append(" and ").append(columnName).append(" in (\"").append(String.join("\",\"", examineRange)).append("\")");
        }
        if(StringUtils.isNotBlank(searchKey)){
            //如果searchkey是纯数字，则只匹配景点id，否则只匹配景点名称
            if(NumberUtils.isNumber(searchKey)){
                sql.append(" and viewspot_id = ?  ");
            }else {
                if ("en-US".equalsIgnoreCase(language)) {
                    //如果是英文环境，匹配poi_en_name
                    sql.append(" and poi_en_name = ? ");
                } else {
                    //如果是中文环境，匹配viewspot_name
                    sql.append(" and viewspot_name = ? ");
                }
            }
            parameters.add(new PreparedParameterBean(searchKey, Types.VARCHAR));
        }
        //按景点分层排序: 核心>聚焦>长尾
        sql.append(" order by case when viewspot_class = '核心' then 1 when viewspot_class = '聚焦' then 2 when viewspot_class = '长尾' then 3 else 4 end asc "); //NOSONAR

        //限制返回的景点<=50个
        sql.append(" limit 50");

        List<Map<String, Object>> resultMapList = null;
        try {
            resultMapList = tktStarRocksDao.getListResultNew(sql.toString(),parameters);
        } catch (SQLException e) {
            log.warn("queryCompetitiveSightIdList error", e);
        }

        if (CollectionUtils.isEmpty(resultMapList)) {
            return new ArrayList<>();
        }

        List<Sight> sightList = new ArrayList<>();
        for(Map<String, Object> resultMap : resultMapList){
            Sight sight = new Sight();
            sight.setSightId((Long) resultMap.get("viewspot_id"));
            if ("en-US".equalsIgnoreCase(language) && resultMap.get("poi_en_name") != null) {
                sight.setSightName((String) resultMap.get("poi_en_name"));
            } else {
                sight.setSightName((String) resultMap.get("viewspot_name"));
            }

            sightList.add(sight);
        }


        return sightList;
    }

    public List<String> queryProvinceNameListByOriViewspotMeidList(String queryD, List<String> oriViewspotMeidList) {
        if(CollectionUtils.isEmpty(oriViewspotMeidList)){
            return new ArrayList<>();
        }
        StringBuilder sql = new StringBuilder("select distinct province_name from adm_prd_ttd_cpd_trip_dim_info_unified_output where d= ? and is_domestic=1 and (");
        List<PreparedParameterBean> parameters = new ArrayList<>();
        parameters.add(new PreparedParameterBean(queryD, Types.VARCHAR));

        for(int i=0 ; i<oriViewspotMeidList.size(); i++){
            String oriViewspotMeid = oriViewspotMeidList.get(i);
            //除第一个以外，后面的都加or
            if(i!=0){
                sql.append(" or ");
            }
            sql.append(" ori_viewspot_meid like ? ");
            parameters.add(new PreparedParameterBean("%" + oriViewspotMeid + "%", Types.VARCHAR));
        }
        sql.append(" ) ");
        List<Map<String, Object>> resultMapList = null;
        try {
            resultMapList = tktStarRocksDao.getListResultNew(sql.toString(),parameters);
        } catch (SQLException e) {
            log.warn("queryCompetitiveSightIdList error", e);
        }
        if(CollectionUtils.isEmpty(resultMapList)){
            return new ArrayList<>();
        }
        return resultMapList.stream().map(resultMap -> (String) resultMap.get("province_name")).collect(Collectors.toList());

    }

}
