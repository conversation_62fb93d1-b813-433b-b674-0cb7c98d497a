package com.ctrip.tour.business.dashboard.tktBusiness.validator;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.InputArgumentException;
import com.ctrip.tour.business.dashboard.utils.MapperUtil;
import com.ctrip.tour.business.dashboard.utils.ParamterCheckUtil;
import com.ctrip.train.tieyouflight.soa.validation.validator.BusinessConstraintValidator;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/9/8
 */
@Component
public class GetTrendLineDataValidator extends BusinessConstraintValidator<GetTrendLineDataRequestType, GetTrendLineDataResponseType> {
    @Override
    public GetTrendLineDataResponseType validateBusiness(GetTrendLineDataRequestType request) {

        String domainName = request.getDomainName();
        if (!ParamterCheckUtil.checkDomainName(domainName)) {
            throw new InputArgumentException("输入了非法的domainName:" + MapperUtil.obj2Str(request));
        }
        TimeFilter timeFilter = request.getTimeFilter();
        if (!ParamterCheckUtil.checkTimeFilter(timeFilter)) {
            throw new InputArgumentException("输入了非法的timeFilter:" + MapperUtil.obj2Str(request));
        }
        String queryType = request.getQueryType();
        if (!ParamterCheckUtil.checkQueryType(queryType)) {
            throw new InputArgumentException("输入了非法的queryType:" + MapperUtil.obj2Str(request));
        }
        String metric = request.getMetric();
        if (!ParamterCheckUtil.checkMetric(metric)) {
            throw new InputArgumentException("输入了非法的metric:" + MapperUtil.obj2Str(request));
        }
        return null;
    }
}
