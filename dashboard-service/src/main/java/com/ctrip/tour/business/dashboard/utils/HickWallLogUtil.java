package com.ctrip.tour.business.dashboard.utils;

import com.ctrip.tour.vadata.utils.hickwall.HickWallUtil;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Collections;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/8/31
 */
@Slf4j
public class HickWallLogUtil {

    /**
     * 发送错误log，并计数1
     *
     * @param keyName
     * @param name
     */

    public static void logSend(String keyName, String name) {
        try {
            StackTraceElement stackTraceElement = Thread.currentThread().getStackTrace()[1];
            // key为自定义的tag标签名称，value为标签具体的值。可添加多个tag
            Map<String, String> errorMap = Collections.singletonMap(keyName,
                    String.format("%s.%s", stackTraceElement.getClassName(), stackTraceElement.getMethodName()));
            HickWallUtil.getInstance().recordCounterToMetricsWithTags(name, errorMap, 1L);
        } catch (Exception e) {
            log.warn(String.format("method:%s %n freud.profile.err:%s", SystemUtil.getMethodName(), formatError(e)));
        }
    }

    /**
     * 发送错误log，并计数1
     * 指定tag
     *
     * @param name
     * @param tag
     */
    public static void logSendWithTag(String name, Map<String, String> tag) {
        logSendWithTag(name, tag, 1L);
    }

    /**
     * 发送每次qmq消息的长度
     * 指定tag
     *
     * @param name
     * @param tag
     */
    public static void logSendLengthWithTag(int size, String name, Map<String, String> tag) {
        logSendWithTag(name, tag, (long)size);
    }

    /**
     * 发送错误log，并计数1
     * 指定tag
     *
     * @param name
     * @param tag
     */
    public static void logSendWithTag(String name, Map<String, String> tag, Long count) {
        try {
            HickWallUtil.getInstance().recordCounterToMetricsWithTags(name, tag, count);
        } catch (Exception e) {
            log.warn(String.format("method:%s %n user.profile.err:%s", SystemUtil.getMethodName(), formatError(e)));
        }
    }

    /**
     * 发送计数 不指定tag
     *
     * @param name
     * @param count
     */
    private static void logSend(String name, Long count) {
        try {
            HickWallUtil.getInstance().recordCounterToMetrics(name, count);
        } catch (Exception e) {
            log.warn(String.format("method:%s %n user.profile.err:%s", SystemUtil.getMethodName(), formatError(e)));
        }
    }

    /**
     * 发送计数1  并不指定tag
     *
     * @param name
     */
    public static void logSend(String name) {
        logSend(name, 1L);
    }

    /**
     * 记录时间
     *
     * @param cost
     * @param name
     */
    public static void logSendTime(Long cost, String name) {
        try {
            HickWallUtil.getInstance().recordTimerToMetrics(cost, name);
        } catch (Exception e) {
            log.warn(String.format("method:%s %n user.profile.err:%s", SystemUtil.getMethodName(), formatError(e)));
        }
    }

    /**
     * 记录时间
     *
     * @param cost
     * @param name
     */
    public static void logSendTimeWithTag(Long cost, String name, Map<String, String> tag) {
        try {
            HickWallUtil.getInstance().recordTimerToMetrics(cost, name, tag);
        } catch (Exception e) {
            log.warn(String.format("method:%s %n user.profile.err:%s", SystemUtil.getMethodName(), formatError(e)));
        }
    }

    public static String formatError(Throwable e) {
        try {
            StringWriter writer = new StringWriter();
            e.printStackTrace(new PrintWriter(writer, true));
            return writer.toString();
        } catch (Exception e1) {
            log.warn("ExceptionUtil format error", "ExceptionUtil:" + e1);
            return e.toString();
        }
    }
}
