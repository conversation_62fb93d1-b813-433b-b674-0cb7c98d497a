package com.ctrip.tour.business.dashboard;

import org.springframework.boot.SpringApplication;

import java.awt.*;
import java.net.URI;

/**
 * 本地调试启动类
 *
 * <AUTHOR>
 * @date 2021/9/26
 */
public class ServiceStarter {

    public static void main(String[] args) throws Exception {
        System.setProperty("java.awt.headless", "false");
        // 本地模式请参考 http://conf.ctripcorp.com/pages/viewpage.action?pageId=192829007
        System.setProperty("artemis.client.local.enabled", "true");

        SpringApplication.run(ServiceInitializer.class);

        // port 8080 is configured in src/test/resources/application.properties(key: server.port)
        Desktop.getDesktop().browse(new URI("http://localhost:8080/api"));
    }
}
