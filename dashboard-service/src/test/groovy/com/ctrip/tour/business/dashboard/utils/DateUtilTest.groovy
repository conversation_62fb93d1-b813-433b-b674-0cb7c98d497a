package com.ctrip.tour.business.dashboard.utils

import spock.lang.Specification

class DateUtilTest extends Specification {
    def "StringToCalendar"() {
        expect:
        DateUtil.StringToCalendar(datestr, format)

        where: "多条件验证"
        datestr     |       format
        "2024-02-14"    |   "yyyy-MM-dd"
    }


    def "getLastDate"() {
        expect: "call & verify"
        result == DateUtil.getLastDate(d)

        where: "多条件验证"
        result  |   d
        "2024-01-31"    |   "2024-01-03"
    }
}
