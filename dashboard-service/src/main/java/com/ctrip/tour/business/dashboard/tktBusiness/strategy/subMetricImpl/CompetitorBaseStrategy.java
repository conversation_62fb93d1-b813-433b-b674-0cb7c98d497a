package com.ctrip.tour.business.dashboard.tktBusiness.strategy.subMetricImpl;

import com.ctrip.soa._24922.*;
import com.ctrip.soa._27181.BIBaseReportQueryServiceClient;
import com.ctrip.soa._27181.GetRawDataRequestType;
import com.ctrip.soa._27181.GetRawDataResponseType;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.*;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.OverseaSinglePeriodTrendLineBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.bo.OverSeaExamineConfigBO;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.Bus105And106And107Helper;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.SwitchNewTableHelper;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

/**
 * 海外竞品基础策略
 */
@Component
public class CompetitorBaseStrategy {

    @Autowired
    private RemoteConfig remoteConfig;

    @Autowired
    private BIBaseReportQueryServiceClient baseReportQueryServiceClient;

    @Autowired
    private OverseaSinglePeriodTrendLineBiz overseaSinglePeriodTrendLineBiz;

    @Autowired
    private SwitchNewTableHelper switchNewTableHelper;


    public Future<MetricDetailInfo> getBus105106107SubMetricCardData(TimeFilter timeFilter,
                                                                     OverseaMetricInfoBean metricInfoBean,
                                                                     String d,
                                                                     String metric,
                                                                     String subMetric) throws Exception {
        MetricDetailInfo metricDetailInfo = new MetricDetailInfo();
        metricDetailInfo.setMetric(metric);
        metricDetailInfo.setSubMetric(subMetric);
        Bus105And106And107Helper.setMetricCardDrillDownParmater(metricDetailInfo, subMetric, metricInfoBean, remoteConfig);
        Map<String, Double> dimMap = new HashMap<>();
        metricDetailInfo.setDimData(dimMap);

        SqlParamterBean bean = Bus105And106And107Helper.getMetricCardSqlBean(timeFilter, metricInfoBean, d, metric, subMetric, remoteConfig,null);
        Integer addtionalGapDay = Bus105And106And107Helper.getAddtionalGapDay(timeFilter, null, d, bean.getAndMap());
        GetRawDataRequestType metricCardReq = bean.convertBeanToRequest(true);
        GetRawDataResponseType metricCardRes = switchNewTableHelper.switchRemoteDatabase(metricCardReq);

        Bus105And106And107Helper.processMetricCardData(metricCardRes, dimMap);

        Integer gapDays = Bus105And106And107Helper.getGapDays(timeFilter, null, d);
        Bus105And106And107Helper.calMetricCardAverageData(dimMap, gapDays - addtionalGapDay);
        Bus105And106And107Helper.makeUpMetricData(dimMap, metric);

        return new AsyncResult<>(metricDetailInfo);
    }


    public GetOverseaTrendLineDataResponseType getBus105106107SubTrendLineData(GetOverseaTrendLineDataRequestType request,
                                                                               String d,
                                                                               List<ExamineConfigBean> examineConfigBeanList) throws Exception {


        GetOverseaTrendLineDataResponseType response = new GetOverseaTrendLineDataResponseType();
        List<TrendLineDetailInfo> trendLineDetailInfoList = new ArrayList<>();
        response.setTrendLineDetailInfoList(trendLineDetailInfoList);

        String queryType = request.getQueryType();
        String metric = request.getMetric();
        String subMetric = request.getSubMetric();
        List<Future<SinglePeriodDataBean>> futureList = new ArrayList<>();

        for (ExamineConfigBean bean : examineConfigBeanList) {

            OverSeaExamineConfigBO bo = new OverSeaExamineConfigBO();
            OverseaMetricInfoBean metricInfoBean = bo.getMetricInfoBean(bean.getBusinessDashboardOverseaExamineeConfig(), remoteConfig);
            SqlParamterBean currentBean;
            if ("trendline".equals(queryType)) {
                currentBean = Bus105And106And107Helper.getMetricCardSqlBean(null, metricInfoBean, d, metric, subMetric, remoteConfig, bean);
            } else {
                currentBean = Bus105And106And107Helper.getTrendlineDrilldownSqlBean(request, metricInfoBean, d, remoteConfig, bean);
            }
            Integer addtionalGapDay = Bus105And106And107Helper.getAddtionalGapDay(null, bean, d, currentBean.getAndMap());
            Integer gapDays = Bus105And106And107Helper.getGapDays(null, bean, d);
            GetRawDataRequestType currentReq = currentBean.convertBeanToRequest(true);
            futureList.add(overseaSinglePeriodTrendLineBiz.getBus105106107SubSinglePeriodTrendLineData(request, metricInfoBean, currentReq, bean, gapDays - addtionalGapDay));

        }

        //转化汇总返回数据
        PeriodDataBean periodDataBean = new PeriodDataBean();
        periodDataBean.setBeanData(futureList);

        //有效时间范围
        List<String> timeList = DateUtil.getAllSelectedTime(request.getTimeFilter(), "oversea");

        if ("trendline".equals(queryType)) {
            Bus105And106And107Helper.processTrendLineData(trendLineDetailInfoList, periodDataBean, timeList, metric);
        } else {
            Bus105And106And107Helper.processTrendLineDrillDownData(trendLineDetailInfoList, periodDataBean, timeList);
        }

        return response;
    }


    public GetOverseaDrillDownBaseInfoResponseType getBus105106107SubDrillDownBaseInfo(GetOverseaDrillDownBaseInfoRequestType request,
                                                                                       String d,
                                                                                       OverseaMetricInfoBean metricInfoBean) throws Exception {

        GetOverseaDrillDownBaseInfoResponseType response = new GetOverseaDrillDownBaseInfoResponseType();

        Boolean needSearch = request.isNeedSearch();
        if (GeneralUtil.isEmpty(needSearch)) {
            request.setNeedSearch(false);
            needSearch = false;
        }
        String subMetric = request.getSubMetric();
        List<String> fieldList = new ArrayList<>();

        if (needSearch) {
            fieldList.add(request.getSearchField());
        } else {
            fieldList.addAll(Bus105And106And107Helper.getDrillDownFieldList(subMetric, metricInfoBean, remoteConfig));
            response.setDefaultChosenField(fieldList.get(0));
        }

        Map<String, Future<GetRawDataResponseType>> fieldMap = new HashMap<>();
        for (String field : fieldList) {
            SqlParamterBean sqlParamterBean = Bus105And106And107Helper.getDrillDownBaseInfoSqlBean(field, request, d, metricInfoBean, remoteConfig);
            GetRawDataRequestType fieldRequest = sqlParamterBean.convertBeanToRequest(false);
            fieldMap.put(field, switchNewTableHelper.switchRemoteDatabaseAsync(fieldRequest));
        }
        List<FieldDataItem> fieldDataItemList = new ArrayList<>();
        response.setFieldDataItemList(fieldDataItemList);
        for (String field : fieldList) {
            FieldDataItem item = new FieldDataItem();
            fieldDataItemList.add(item);
            Bus105And106And107Helper.processDrillDownBaseInfo(request, field, fieldMap.get(field).get(), item, remoteConfig);
        }



        return response;
    }


    public GetOverseaTableDataResponseType getBus105106107SubTableData(GetOverseaTableDataRequestType request,
                                                                       String d,
                                                                       OverseaMetricInfoBean metricInfoBean) throws Exception {

        GetOverseaTableDataResponseType response = new GetOverseaTableDataResponseType();
        List<TableDataItem> tableDataItemList = new ArrayList<>();
        response.setTableDataItemList(tableDataItemList);

        String metric = request.getMetric();
        String subMetric = request.getSubMetric();
        DrillDownFilter drillDownFilter = request.getDrillDownFilter();
        String field = drillDownFilter.getField();
        SubMetricFiledBean configBean = remoteConfig.getSubMetricFiledBean(metric, subMetric, field);

        TimeFilter timeFilter = request.getTimeFilter();
        //获取当前数据
        SqlParamterBean currentBean = Bus105And106And107Helper.getTableDataSqlBean(request, d, metricInfoBean, configBean, remoteConfig);
        Integer addtionalGapDay = Bus105And106And107Helper.getAddtionalGapDay(timeFilter, null, d, currentBean.getAndMap());
        GetRawDataRequestType currentReq = currentBean.convertBeanToRequest(true);
        GetRawDataResponseType currentRes = switchNewTableHelper.switchRemoteDatabase(currentReq);

        //计算平均数据
        Integer gapDays = Bus105And106And107Helper.getGapDays(timeFilter, null, d);
        Bus105And106And107Helper.processTableBaseData(currentRes, tableDataItemList);
        Bus105And106And107Helper.calTableAverageData(tableDataItemList, gapDays - addtionalGapDay);
        Bus105And106And107Helper.makeUpTableData(tableDataItemList, metric);

        response.setTotalNum(currentRes.getTotalNum());
        response.setTableHeaderList(Bus105And106And107Helper.getTableHeaderList(configBean));
        response.setShowField(configBean.getShowField());
        response.setShowFieldId(configBean.getShowFieldId());
        return response;
    }



}
