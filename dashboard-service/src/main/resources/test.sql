# todo： 数仓加的字段
#      数仓增加子业务线
#      虚假点评量
#      点评日期
#     历史点评总数
#     历史点评均分
#
#
#     累计到当月不达标次数 --
#     虚假点评订单数量/当月总点评订单数量≥5% -- 待定
#
#     当月不达标次数 -- 待定

domain_info:
  # 模块ID 1001: 对应 "指标卡" 查询逻辑
  - db_name:
    id: 1001
    table_name: ads_vacdb.vbk_commentanalysis_summary
    source_type: starrocks
    indicators:
      # --- 指标 ---
      - indicator_name: avg_score
        indicator_name_cn: 点评均分
        indicator_type: double
        indicator_format: double
        indicator_key_name: avgScore
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: review_rate
        indicator_name_cn: 点评率
        indicator_type: double
        indicator_format: percent
        indicator_key_name: reviewRate
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: fake_review_rate
        indicator_name_cn: 虚假点评率
        indicator_type: double
        indicator_format: percent
        indicator_key_name: fakeReviewRate
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: fake_review_count
        indicator_name_cn: 虚假点评量
        indicator_type: long
        indicator_format: long
        indicator_key_name: fakeReviewCount
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: non_compliance_count
        indicator_name_cn: 不达标次数
        indicator_type: long
        indicator_format: long
        indicator_key_name: nonComplianceCount
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      # --- 维度 ---
      - indicator_name: date
        indicator_name_cn: 点评日期
        indicator_type: string
        indicator_is_dimension: true
      - indicator_name: vendor_id
        indicator_name_cn: 供应商ID
        indicator_type: string
        indicator_is_dimension: true
      - indicator_name: sale_mode_name
        indicator_name_cn: 销售模式
        indicator_type: string
        indicator_is_dimension: true
      - indicator_name: dest_area
        indicator_name_cn: 目的地区域
        indicator_type: string
        indicator_is_dimension: true
      - indicator_name: start_city
        indicator_name_cn: 出发城市
        indicator_type: string
        indicator_is_dimension: true
      - indicator_name: line_name
        indicator_name_cn: 产品线
        indicator_type: string
        indicator_is_dimension: true
    template: |
      select
              1 as default_group
              <#if indicators?seq_contains("avg_score")> ,COALESCE(sum(agrade_sum),0) / COALESCE(sum(agrade_num),0) as avg_score </#if>
              <#if indicators?seq_contains("review_rate")> ,COALESCE(sum(agradeorder_num),0) / COALESCE(sum(order_num),0) as review_rate </#if>
              <#if indicators?seq_contains("fake_review_rate")> ,sum(虚假点评量) / COALESCE(sum(agrade_num),0) as fake_review_rate </#if>
              <#if indicators?seq_contains("fake_review_count")> ,sum(虚假点评量) as fake_review_count </#if>
              <#if indicators?seq_contains("non_compliance_count")> ,max(不达标次数) as non_compliance_count </#if>

              <#if group_by??>
                  <#list group_by as value>
                      ,${value}
                  </#list>
              </#if>
      from
              ads_vacdb.vbk_commentanalysis_summary
      where
              1=1
              <#if filter_flat.date??> AND returndate between '${filter_flat.date.filterValues[0]}' AND '${filter_flat.date.filterValues[1]}' </#if>
              <#if filter_flat.vendor_id??> AND providerid IN ('${filter_flat.vendor_id.filterValues?join("', '")}') </#if>
              <#if filter_flat.sale_mode_name??> AND salemode IN ('${filter_flat.sale_mode_name.filterValues?join("', '")}') </#if>
              <#if filter_flat.dest_area??> AND poi IN ('${filter_flat.dest_area.filterValues?join("', '")}') </#if>
              <#if filter_flat.start_city??> AND startcity IN ('${filter_flat.start_city.filterValues?join("', '")}') </#if>
              <#if filter_flat.line_name??> AND linename IN ('${filter_flat.line_name.filterValues?join("', '")}') </#if>

              <#if group_by??> group by default_group <#list group_by as value> ,${value} </#list></#if>

  # 模块ID 1002: 对应 "点评分布" 查询逻辑
  - db_name:
    id: 1002
    table_name: ads_vacdb.vbk_commentanalysis_summary
    source_type: starrocks
    indicators:
      - indicator_name: review_count
        indicator_name_cn: 点评数
        indicator_type: long
        indicator_is_dimension: false
      - indicator_name: score_0_3_count
        indicator_name_cn: 0-3分点评数
        indicator_type: long
        indicator_is_dimension: false
      - indicator_name: score_0_3_reply_rate
        indicator_name_cn: 0-3分点评回复率
        indicator_type: double
        indicator_format: percent
        indicator_is_dimension: false
      - indicator_name: score_3_4_count
        indicator_name_cn: 3-4分点评数
        indicator_type: long
        indicator_is_dimension: false
      - indicator_name: score_3_4_reply_rate
        indicator_name_cn: 3-4分点评回复率
        indicator_type: double
        indicator_format: percent
        indicator_is_dimension: false
      - indicator_name: score_4_5_count
        indicator_name_cn: 4-5分点评数
        indicator_type: long
        indicator_is_dimension: false
      - indicator_name: score_4_5_reply_rate
        indicator_name_cn: 4-5分点评回复率
        indicator_type: double
        indicator_format: percent
        indicator_is_dimension: false
    template: |
      select
              1 as default_group
              <#if indicators?seq_contains("review_count")> ,COALESCE(sum(agrade_num),0) as review_count </#if>
              <#if indicators?seq_contains("score_0_3_count")> ,COALESCE(sum(agrade0_3_num),0) as score_0_3_count </#if>
              <#if indicators?seq_contains("score_0_3_reply_rate")> ,COALESCE(sum(replyagrade0_3_num),0) / COALESCE(sum(agrade0_3_num),0) as score_0_3_reply_rate </#if>
              <#if indicators?seq_contains("score_3_4_count")> ,COALESCE(sum(agrade4_num),0) as score_3_4_count </#if>
              <#if indicators?seq_contains("score_3_4_reply_rate")> ,COALESCE(sum(replyagrade4_num),0) / COALESCE(sum(agrade4_num),0) as score_3_4_reply_rate </#if>
              <#if indicators?seq_contains("score_4_5_count")> ,COALESCE(sum(agrade5_num),0) as score_4_5_count </#if>
              <#if indicators?seq_contains("score_4_5_reply_rate")> ,COALESCE(sum(replyagrade5_num),0) / COALESCE(sum(agrade5_num),0) as score_4_5_reply_rate </#if>
      from
              ads_vacdb.vbk_commentanalysis_summary
      where
              1=1
              <#if filter_flat.date??> AND returndate between '${filter_flat.date.filterValues[0]}' AND '${filter_flat.date.filterValues[1]}' </#if>
              <#if filter_flat.vendor_id??> AND providerid IN ('${filter_flat.vendor_id.filterValues?join("', '")}') </#if>
              <#if filter_flat.sale_mode_name??> AND salemode IN ('${filter_flat.sale_mode_name.filterValues?join("', '")}') </#if>
              <#if filter_flat.dest_area??> AND poi IN ('${filter_flat.dest_area.filterValues?join("', '")}') </#if>
              <#if filter_flat.start_city??> AND startcity IN ('${filter_flat.start_city.filterValues?join("', '")}') </#if>

  # 模块ID 1003: 对应 "子点评分布" 查询逻辑
  - db_name:
    id: 1003
    table_name: ads_vacdb.vbk_commentanalysis_summary
    source_type: starrocks
    indicators:
      - indicator_name: itinerary_avg_score
        indicator_name_cn: 行程安排点评均分
        indicator_type: double
        indicator_is_dimension: false
      - indicator_name: hotel_avg_score
        indicator_name_cn: 酒店住宿点评均分
        indicator_type: double
        indicator_is_dimension: false
      - indicator_name: guide_avg_score
        indicator_name_cn: 导游服务点评均分
        indicator_type: double
        indicator_is_dimension: false
    template: |
      select
              1 as default_group
              <#if indicators?seq_contains("itinerary_avg_score")> ,COALESCE(sum(xcap_subgrade_sum),0) / COALESCE(sum(xcap_subgrade_num),0) as itinerary_avg_score </#if>
              <#if indicators?seq_contains("hotel_avg_score")> ,COALESCE(sum(jdty_subgrade_sum),0) / COALESCE(sum(jdty_subgrade_num),0) as hotel_avg_score </#if>
              <#if indicators?seq_contains("guide_avg_score")> ,COALESCE(sum(dyjj_subgrade_sum),0) / COALESCE(sum(dyjj_subgrade_num),0) as guide_avg_score </#if>
      from
              ads_vacdb.vbk_commentanalysis_summary
      where
              1=1
              <#if filter_flat.date??> AND returndate between '${filter_flat.date.filterValues[0]}' AND '${filter_flat.date.filterValues[1]}' </#if>
              <#if filter_flat.vendor_id??> AND providerid IN ('${filter_flat.vendor_id.filterValues?join("', '")}') </#if>
              <#if filter_flat.sale_mode_name??> AND salemode IN ('${filter_flat.sale_mode_name.filterValues?join("', '")}') </#if>
              <#if filter_flat.dest_area??> AND poi IN ('${filter_flat.dest_area.filterValues?join("', '")}') </#if>

  # 模块ID 1004: 对应 "产品维度明细" 查询逻辑
  - db_name:
    id: 1004
    table_name: ads_vacdb.vbk_commentanalysis_summary
    source_type: starrocks
    indicators:
      - indicator_name: avg_score
        indicator_name_cn: 点评均分
        indicator_type: double
        indicator_is_dimension: false
      - indicator_name: review_count
        indicator_name_cn: 点评数
        indicator_type: long
        indicator_is_dimension: false
      - indicator_name: product_id
        indicator_name_cn: 产品ID
        indicator_type: string
        indicator_is_dimension: true
      - indicator_name: product_name
        indicator_name_cn: 产品名称
        indicator_type: string
        indicator_is_dimension: true
      - indicator_name: dest_area
        indicator_name_cn: 目的地区域
        indicator_type: string
        indicator_is_dimension: true
    template: |
      select
              <#if indicators?seq_contains("avg_score")> COALESCE(sum(agrade_sum),0) / COALESCE(sum(agrade_num),0) as avg_score, </#if>
              <#if indicators?seq_contains("review_count")> COALESCE(sum(agrade_num),0) as review_count, </#if>
              poi as dest_area,
              pkg as product_id,
              pkgname as product_name
      from
              ads_vacdb.vbk_commentanalysis_summary
      where
              1=1
              <#if filter_flat.date??> AND returndate between '${filter_flat.date.filterValues[0]}' AND '${filter_flat.date.filterValues[1]}' </#if>
              <#if filter_flat.vendor_id??> AND providerid IN ('${filter_flat.vendor_id.filterValues?join("', '")}') </#if>
              <#if filter_flat.sale_mode_name??> AND salemode IN ('${filter_flat.sale_mode_name.filterValues?join("', '")}') </#if>
              <#if filter_flat.dest_area??> AND poi IN ('${filter_flat.dest_area.filterValues?join("', '")}') </#if>
      group by
              poi, pkg, pkgname