package com.ctrip.tour.business.dashboard.grpBusiness.domain.process.dsl.diy;

import com.ctrip.soa._24922.DSLRequestType;
import com.ctrip.soa._24922.EnumOperators;
import com.ctrip.soa._24922.WhereCondition;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.AbstractPreDSLProcess;

import java.util.ArrayList;
import java.util.List;


/**
 * 产品形态
 */
public class DSLProcessProductPatternPares extends AbstractPreDSLProcess {
    private List<String> productPatternPares;

    public static AbstractPreDSLProcess getInstance(List<String> productPatternPares) {
        DSLProcessProductPatternPares dslProcessLimitSet = new DSLProcessProductPatternPares();
        dslProcessLimitSet.productPatternPares = productPatternPares;
        return dslProcessLimitSet;
    }

    @Override
    public DSLRequestType process(DSLRequestType dsl, EarlyReturn earlyReturn) {
        if (productPatternPares == null || productPatternPares.isEmpty()) {
            return dsl;
        }
        // 初始化
        if (dsl.getWhereCondition() == null) {
            dsl.setWhereCondition(new WhereCondition());
        }
        if (dsl.getWhereCondition().getSubWhereConditions() == null) {
            dsl.getWhereCondition().setSubWhereConditions(new ArrayList<>());
        }

        // 产品形态
        WhereCondition provNamesCondition = new WhereCondition();
        dsl.getWhereCondition().getSubWhereConditions().add(provNamesCondition);

        provNamesCondition.setFilterName("prd_pattern_name");
        provNamesCondition.setOperators(EnumOperators.IN);
        provNamesCondition.setFilterValues(productPatternPares);
        return dsl;
    }
}
