package com.ctrip.tour.business.dashboard.sightArchives.dao.overseametric;

import com.ctrip.soa._27181.PreparedParameterBean;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.CdmOrdTtdOverseasPerformanceIndexBO;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.DimOrdTtdSiteChannelTargetBO;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.bean.DimOrdTtdSiteChannelTargetParamBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseasRelatedSearchParamBean;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.TktStarRocksDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.sql.Types;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Repository
@Slf4j
public class DimOrdTtdSiteChannelTargetConfigDao {

    @Autowired
    private TktStarRocksDao tktStarRocksDao;

    /**
     * 获取海外站点渠道目标数据
     *
     * @return
     */
    public Double queryOverseaSiteTargetInfo(OverseasRelatedSearchParamBean searchParamBean) {
        StringBuilder sql = new StringBuilder("SELECT");
        switch (searchParamBean.getQuarter()) {
            case "Q1":
                sql.append("  SUM(COALESCE(CAST(q1 AS DOUBLE), 0)) as siteChannelTarget");
                break;
            case "Q2":
                sql.append("  SUM(COALESCE(CAST(q2 AS DOUBLE), 0)) as siteChannelTarget");
                break;
            case "Q3":
                sql.append("  SUM(COALESCE(CAST(q3 AS DOUBLE), 0)) as siteChannelTarget");
                break;
            case "Q4":
                sql.append("  SUM(COALESCE(CAST(q4 AS DOUBLE), 0)) as siteChannelTarget");
                break;
            default:
                return 0.00;
        }
        sql.append(" FROM ods_data_upload_new_performance_site_latest WHERE 1=1");
        List<PreparedParameterBean> parameters = new java.util.ArrayList<>();
        appendYear(parameters, sql, searchParamBean.getYear());
        appendExamineType(parameters, sql, searchParamBean.getExamineType());
        appendExamineTypeValue(parameters, sql, searchParamBean.getExamineTypeValue());
        appendExamineMetricType(parameters, sql, searchParamBean.getExamineMetricType());
        appendD(parameters, sql, searchParamBean.getD());
        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            log.warn("queryOverseaSiteTargetInfo error", e);
        }
        if (!CollectionUtils.isEmpty(result)) {
            return result.stream()
                    .map(bean -> bean.get("siteChannelTarget"))
                    .filter(Objects::nonNull)
                    .map(value -> Double.parseDouble(value.toString()))
                    .findFirst()
                    .orElse(0.00);
        }
        return 0.00;
    }

    //拼季度
    public void appendQuarter(List<PreparedParameterBean> parameters, StringBuilder sql, String quarter) {
        if (quarter != null) {
            sql.append(" and use_quarter=?");
            parameters.add(new PreparedParameterBean(quarter, Types.VARCHAR));
        }
    }

    //拼时间
    private void appendD(List<PreparedParameterBean> parameters, StringBuilder sql, String d) {
        if (d != null) {
            sql.append(" and d=?");
            parameters.add(new PreparedParameterBean(d, Types.VARCHAR));
        }
    }

    //拼年份
    private void appendYear(List<PreparedParameterBean> parameters, StringBuilder sql, String year) {
        if (year != null) {
            sql.append(" and examine_year = ?");
            parameters.add(new PreparedParameterBean(year, Types.VARCHAR));
        }
    }

    //拼考核类型
    private void appendExamineType(List<PreparedParameterBean> parameters, StringBuilder sql, String examineType) {
        if (examineType != null) {
            sql.append(" and examine_type = ?");
            parameters.add(new PreparedParameterBean(examineType, Types.VARCHAR));
        }
    }

    //拼考核类型值
    private void appendExamineTypeValue(List<PreparedParameterBean> parameters, StringBuilder sql, List<String> examineTypeValue) {
        if (CollectionUtils.isNotEmpty(examineTypeValue)) {
            sql.append(" and examine_type_value in (");
            for (int i = 0; i < examineTypeValue.size(); i++) {
                if (i == 0) {
                    sql.append("?");
                } else {
                    sql.append(",?");
                }
                parameters.add(new PreparedParameterBean(examineTypeValue.get(i), Types.VARCHAR));
            }
            sql.append(")");
        }
    }

    //拼考核指标类型
    private void appendExamineMetricType(List<PreparedParameterBean> parameters, StringBuilder sql, String metric) {
        if (metric != null) {
            sql.append(" and examine_metric_type = ?");
            parameters.add(new PreparedParameterBean(metric, Types.VARCHAR));
        }
    }


    public List<DimOrdTtdSiteChannelTargetBO> getDimOrdTtdSiteChannelTargetBO(DimOrdTtdSiteChannelTargetParamBean param) {
        StringBuilder sql = new StringBuilder("select * from ods_data_upload_new_performance_site_latest " +
                "where d=? and  examine_year=? and examine_type=? and examine_metric_type=?  " );

        List<PreparedParameterBean> parameters = new java.util.ArrayList<>();
        parameters.add(new PreparedParameterBean(param.getD(), Types.VARCHAR));
        parameters.add(new PreparedParameterBean(param.getExamineYear(), Types.VARCHAR));
        parameters.add(new PreparedParameterBean(param.getExamineType(), Types.VARCHAR));
        parameters.add(new PreparedParameterBean(param.getExamineMetricType(), Types.VARCHAR));

        List<Map<String, Object>> result = new ArrayList<>();
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
        } catch (Exception e) {
            log.warn("queryOverseaPersonInfo error", e);
        }
        List<DimOrdTtdSiteChannelTargetBO> siteChannelList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(result)) {
            siteChannelList = result.stream()
                    .map(bean -> {
                        DimOrdTtdSiteChannelTargetBO siteChannelBO = new DimOrdTtdSiteChannelTargetBO();
                        siteChannelBO.setExamineType((String) bean.get("examine_type"));
                        siteChannelBO.setExamineTypeValue((String) bean.get("examine_type_value"));
                        siteChannelBO.setQ1(((String)bean.get("q1")).replace(",",""));
                        siteChannelBO.setQ2(((String)bean.get("q2")).replace(",",""));
                        siteChannelBO.setQ3(((String)bean.get("q3")).replace(",",""));
                        siteChannelBO.setQ4(((String)bean.get("q4")).replace(",",""));
                        return siteChannelBO;
                    })
                    .collect(Collectors.toList());
        }
        return siteChannelList;
    }
}
