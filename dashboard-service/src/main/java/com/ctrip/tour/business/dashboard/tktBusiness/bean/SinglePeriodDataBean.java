package com.ctrip.tour.business.dashboard.tktBusiness.bean;

import com.ctrip.soa._27181.GetRawDataResponseType;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.ctrip.tour.business.dashboard.utils.MapperUtil;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

public class SinglePeriodDataBean {
    //基础的数据单元
    //达成数据
    List<List<Object>> periodReachList;
    //目标数据
    List<List<Object>> periodTargetList;
    //堆叠柱状图数据
    List<List<Object>> periodStackList;
    //劣势数据
    List<List<Object>> periodWeaknessList;
    //去年同比数据
    List<List<Object>> periodLastyearList;
    //2019年同比数据
    List<List<Object>> period2019List;

    //数据表头
    List<String> reachHeaderList;
    List<String> targetHeaderList;
    List<String> lastyearHeaderList;
    List<String> _2019HeaderList;
    List<String> weaknessHeaderList;


    public void setPeriodReachList(GetRawDataResponseType response,
                                   String time) {

        List<List<Object>> originList = MapperUtil.str2ListList(response.getResult(), Object.class);
        setPeriodReachList(originList, time);
    }



    public void setPeriodReachList(List<List<Object>> originList,
                                   String time) {
        periodReachList = new ArrayList<>();
        originList.forEach(i -> {
            List<Object> rowList = new ArrayList<>();
            rowList.add(time);
            rowList.addAll(i);
            periodReachList.add(rowList);
        });

    }

    public void setPeriodTargetList(GetRawDataResponseType response,
                                    String time) {
        List<List<Object>> originList = MapperUtil.str2ListList(response.getResult(), Object.class);
        setPeriodTargetList(originList, time);
    }


    public void setPeriodTargetList(List<List<Object>> originList,
                                    String time) {
        periodTargetList = new ArrayList<>();
        originList.forEach(i -> {
            List<Object> rowList = new ArrayList<>();
            rowList.add(time);
            rowList.addAll(i);
            periodTargetList.add(rowList);
        });
    }

    public void setPeriodStackList(List<List<Object>> originList,
                                   String time) {
        periodStackList = new ArrayList<>();
        originList.forEach(i -> {
            List<Object> rowList = new ArrayList<>();
            rowList.add(time);
            rowList.addAll(i);
            periodStackList.add(rowList);
        });
    }


    public void setPeriodWeaknessList(GetRawDataResponseType response,
                                      Integer gapDay,
                                      String time,
                                      List<String> groupTagList) {
        List<List<Object>> originList = MapperUtil.str2ListList(response.getResult(), Object.class);
        setPeriodWeaknessList(originList, gapDay, time, groupTagList);
    }


    public void setPeriodWeaknessList(List<List<Object>> originList,
                                      Integer gapDay,
                                      String time,
                                      List<String> groupTagList) {
        periodWeaknessList = new ArrayList<>();
        int size = groupTagList.size();
        originList.forEach(item -> {
            List<Object> rowList = new ArrayList<>();
            rowList.add(time);
            for (int i = 0; i < item.size(); i++) {
                Object o = item.get(i);
                if (i < size) {
                    rowList.add(o);
                } else {
                    if (GeneralUtil.isNotEmpty(o)) {
                        Double result = Double.valueOf(String.valueOf(o));
                        rowList.add(result / gapDay);
                    }
                }
            }
            periodWeaknessList.add(rowList);
        });
    }


    public void setPeriodWeaknessList(GetRawDataResponseType response,
                                      Integer gapDays,
                                      Integer addtionalGapDays,
                                      String time,
                                      List<String> groupTagList) {
        List<List<Object>> originList = MapperUtil.str2ListList(response.getResult(), Object.class);
        setPeriodWeaknessList(originList, gapDays, addtionalGapDays, time, groupTagList);
    }


    public void setPeriodWeaknessList(List<List<Object>> originList,
                                      Integer gapDays,
                                      Integer addtionalGapDays,
                                      String time,
                                      List<String> groupTagList) {
        periodWeaknessList = new ArrayList<>();
        int size = groupTagList.size();
        originList.forEach(item -> {
            List<Object> rowList = new ArrayList<>();
            rowList.add(time);
            for (int i = 0; i < item.size(); i++) {
                Object o = item.get(i);
                if (i < size) {
                    rowList.add(o);
                } else {
                    if (GeneralUtil.isNotEmpty(o) && GeneralUtil.isNotEmpty(gapDays)) {
                        Double result = Double.valueOf(String.valueOf(o));
                        rowList.add(result / (gapDays- addtionalGapDays));
                    }
                }
            }
            periodWeaknessList.add(rowList);
        });
    }



    public void setPeriodLastyearList(GetRawDataResponseType response,
                                      GetRawDataResponseType popResponse,
                                      String time) {
        List<List<Object>> originList = MapperUtil.str2ListList(response.getResult(), Object.class);
        List<List<Object>> popList = MapperUtil.str2ListList(popResponse.getResult(), Object.class);
        List<String> dimList = response.getMetricList();
        setPeriodLastyearList(originList, popList, dimList, time);
    }

    /**
     * originList与popList的长度均为1 且数据一一对应
     * @param originList
     * @param popList
     * @param dimList
     * @param time
     */
    public void setPeriodLastyearList(List<List<Object>> originList,
                                      List<List<Object>> popList,
                                      List<String> dimList,
                                      String time) {
        periodLastyearList = new ArrayList<>();
        List<Object> rowOriginList = originList.get(0);
        List<Object> rowPopList = popList.get(0);

        List<Object> rowList = new ArrayList<>();
        rowList.add(time);
        for (int i = 0; i < dimList.size(); i++) {
            Object o = rowOriginList.get(i);
            Object r = rowPopList.get(i);
            Double result = null;
            Double fenzi = null;
            Double fenmu = null;
            if (GeneralUtil.isNotEmpty(o) && GeneralUtil.isNotEmpty(r)) {
                fenzi = Double.valueOf(String.valueOf(o));
                fenmu = Double.valueOf(String.valueOf(r));
                if (GeneralUtil.isValidDivide(fenzi, fenmu)) {
                    result = fenzi / fenmu - 1;
                }
            }
            rowList.add(result);
            rowList.add(fenmu);
        }
        periodLastyearList.add(rowList);
    }

    public void setPeriod2019List(GetRawDataResponseType response,
                                  GetRawDataResponseType popResponse,
                                  String time) {
        List<List<Object>> originList = MapperUtil.str2ListList(response.getResult(), Object.class);
        List<List<Object>> popList = MapperUtil.str2ListList(popResponse.getResult(), Object.class);
        List<String> dimList = response.getMetricList();
        setPeriod2019List(originList, popList, dimList, time);
    }

    public void setPeriod2019List(List<List<Object>> originList,
                                  List<List<Object>> popList,
                                  List<String> dimList,
                                  String time) {
        period2019List = new ArrayList<>();
        List<Object> rowOriginList = originList.get(0);
        List<Object> rowPopList = popList.get(0);

        List<Object> rowList = new ArrayList<>();
        rowList.add(time);
        for (int i = 0; i < dimList.size(); i++) {
            Object o = rowOriginList.get(i);
            Object r = rowPopList.get(i);
            Double result = null;
            Double fenzi = null;
            Double fenmu = null;
            if (GeneralUtil.isNotEmpty(o) && GeneralUtil.isNotEmpty(r)) {
                fenzi = Double.valueOf(String.valueOf(o));
                fenmu = Double.valueOf(String.valueOf(r));
                if (GeneralUtil.isValidDivide(fenzi, fenmu)) {
                    result = fenzi / fenmu - 1;
                }
            }
            rowList.add(result);
            rowList.add(fenmu);
        }
        period2019List.add(rowList);
    }

    public void setReachHeaderList(List<String> dimList) {
        reachHeaderList = new ArrayList<>();
        reachHeaderList.add("time");
        reachHeaderList.addAll(dimList);
    }

    public void setReachHeaderList(List<String> dimList,
                                   List<String> groupList){
        reachHeaderList = new ArrayList<>();
        reachHeaderList.add("time");
        if(GeneralUtil.isNotEmpty(groupList)){
            reachHeaderList.addAll(groupList);
        }
        reachHeaderList.addAll(dimList);
    }


    public void setTargetHeaderList(List<String> dimList) {
        targetHeaderList = new ArrayList<>();
        targetHeaderList.add("time");
        targetHeaderList.addAll(dimList);
    }

    public void setLastyearHeaderList(List<String> dimList) {
        lastyearHeaderList = new ArrayList<>();
        lastyearHeaderList.add("time");
        for (String dim : dimList) {
            lastyearHeaderList.add(dim);
            lastyearHeaderList.add(dim + "_value");
        }

    }

    public void set2019HeaderList(List<String> dimList) {
        _2019HeaderList = new ArrayList<>();
        _2019HeaderList.add("time");
        for (String dim : dimList) {
            _2019HeaderList.add(dim);
            _2019HeaderList.add(dim + "_value");
        }

    }


    public void setWeaknessHeaderList(List<String> dimList,
                                      List<String> groupList){
        weaknessHeaderList = new ArrayList<>();
        weaknessHeaderList.add("time");
        if(GeneralUtil.isNotEmpty(groupList)){
            weaknessHeaderList.addAll(groupList);
        }
        weaknessHeaderList.addAll(dimList);
    }

    public List<List<Object>> getPeriodStackList() {
        return periodStackList;
    }

    public List<List<Object>> getPeriodReachList() {
        return periodReachList;
    }

    public List<List<Object>> getPeriodTargetList() {
        return periodTargetList;
    }

    public List<List<Object>> getPeriodWeaknessList() {
        return periodWeaknessList;
    }

    public List<List<Object>> getPeriodLastyearList() {
        return periodLastyearList;
    }

    public List<List<Object>> getPeriod2019List() {
        return period2019List;
    }

    public List<String> getReachHeaderList() {
        return reachHeaderList;
    }


    public List<String> getTargetHeaderList() {
        return targetHeaderList;
    }

    public List<String> getLastyearHeaderList() {
        return lastyearHeaderList;
    }


    public List<String> get2019HeaderList() {
        return _2019HeaderList;
    }


    public List<String> getWeaknessHeaderList() {
        return weaknessHeaderList;
    }
}
