package com.ctrip.tour.business.dashboard.grpBusiness.metrics.profitPrice;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.base.SQLResult;
import com.ctrip.platform.dal.dao.helper.DalColumnMapRowMapper;
import com.ctrip.tour.business.dashboard.grpBusiness.annotation.IndexAssemblyHandler;
import com.ctrip.tour.business.dashboard.grpBusiness.dao.mysql.DimOrdGrpAchv2025PersonTrgtDao;
import com.ctrip.tour.business.dashboard.grpBusiness.dao.starrocks.AdmOrdGrpEorkPlatformPrdtDfDao;
import com.ctrip.tour.business.dashboard.grpBusiness.entity.DimOrdGrpAchv2025PersonTrgt;
import com.ctrip.tour.business.dashboard.grpBusiness.enums.MetricCategoryEnum;
import com.ctrip.tour.business.dashboard.grpBusiness.handler.dim.DimconvertHandler;
import com.ctrip.tour.business.dashboard.grpBusiness.metrics.IndexCommonQueryAbstractSerice;
import com.ctrip.tour.business.dashboard.utils.SqlBuilder;
import com.google.common.base.Joiner;
import com.google.common.collect.ImmutableMap;

import lombok.extern.slf4j.Slf4j;


@Service
@IndexAssemblyHandler(calcDateName = "dep_date",
        calcExpression = "sum_suc_profit/target_profit", calcFieldName = "sum_suc_profit,target_profit",
        tableName = "adm_ord_grp_work_platform_prdt_df")
@Slf4j
public class GrpCprProfitPriceAchieveRateService extends IndexCommonQueryAbstractSerice {
    @Autowired
    AdmOrdGrpEorkPlatformPrdtDfDao orderDfDao;
    @Autowired
    DimOrdGrpAchv2025PersonTrgtDao trgtDao;

    private static final ImmutableMap<String, String> TARGET_COL_MAP = ImmutableMap.of("sale_channel_name", "sale_channel",
            "sale_mode_name", "sale_mode",
            "dest_first_region", "prd_region",
            "dest_domain", "dest_domain",
            "pm_eid", "pm_lvl3_no"
    );

    public GrpCprProfitPriceAchieveRateService(ApplicationContext ac) {
        super(ac);
    }

    @Override
    protected String otherCon(Map<String, ?> param, List<String> groupByCols, String timeAggType) {
        return doOtherCon(param, MetricCategoryEnum.PROFIT_PRICE_CATEGORY.getEnglishName());
    }

    @Override
    protected SqlBuilder selectColsAssembly(SqlBuilder sqlBuilder, Map<String, ?> param, List<String> groupByCols, String timeAggType) {

        String sql = " sum(suc_profit) as sum_suc_profit";

        if (CollectionUtils.isNotEmpty(groupByCols)) {
            sql = String.join(",", sql, Joiner.on(",").skipNulls().join(groupByCols));
        }

        sqlBuilder.select(sql);

        return sqlBuilder;
    }

    @Override
    protected List<Map<String, Object>> queryData(String sql, Map<String, ?> param, List<String> groupByCols, String timeAggType) {
        List<Map<String, Object>> queryData = null;
        try {
            DalColumnMapRowMapper dalColumnMapRowMapper = new DalColumnMapRowMapper();
            queryData = orderDfDao.query(sql, param);
            return queryData;
        } catch (SQLException e) {
            log.warn("query adm_ord_grp_work_platform_prdt_df error", e);

        }
        return null;
    }

    @Override
    protected List<Map<String, Object>> handleResults(IndexCommonQueryAbstractSerice serice, Map<String, ?> param, String timeAggType, boolean needTimeAgg, List<Map<String, Object>> rowData, List<String> groupCols) {

        List<String> empCodes = (List<String>)param.get("empCodes");

        try {

            if (CollectionUtils.isNotEmpty(groupCols)) {
                String groupCol = groupCols.get(0);
                String profitTblName = DimconvertHandler.convertDimEnumName(groupCol, MetricCategoryEnum.PROFIT_PRICE_CATEGORY.getEnglishName());
                String relationCol = TARGET_COL_MAP.get(profitTblName);
                String endDate = ((String[])param.get("dep_date"))[1];
                String monthStr = endDate.split("-")[1];
                List<DimOrdGrpAchv2025PersonTrgt> query = Lists.newArrayList();
                if (empCodes.contains("S40160")) {
                    query = trgtDao.query("select sum(target_profit) as target_profit, " + relationCol + " as groupColName from dim_ord_grp_achv2025_person_trgt" +
                            " where dep_month=?  group by " + relationCol, new DalHints().freeSql(), monthStr);
                } else {
                    query = trgtDao.query("select sum(target_profit) as target_profit, " + relationCol + " as groupColName from dim_ord_grp_achv2025_person_trgt" +
                            " where dep_month=?  and pm_lvl3_no in (?)  group by " + relationCol, new DalHints().freeSql(), monthStr, empCodes);
                }



                for (DimOrdGrpAchv2025PersonTrgt dimOrdGrpAchv2025PersonTrgt : query) {
                    String groupColName = dimOrdGrpAchv2025PersonTrgt.getGroupColName();
                    rowData.forEach(row -> {
                        if (StringUtils.equals(groupColName, (String) row.get(profitTblName))) {
                            row.put("target_profit", dimOrdGrpAchv2025PersonTrgt.getTargetProfit().doubleValue());
                        }
                    });
                }


            } else if (StringUtils.isNotBlank(timeAggType) && StringUtils.equalsIgnoreCase("month", timeAggType)) {
                String[] dateRange = (String[])param.get("dep_date");
                String endMonthStr = dateRange[1].split("-")[1];
                String startMonthStr = dateRange[0].split("-")[1];
                List<DimOrdGrpAchv2025PersonTrgt> targets = Lists.newArrayList();
                if (empCodes.contains("S40160")) {
                    targets = trgtDao.query("select sum(target_profit) as target_profit, concat('2025', '-', dep_month) as dep_month from dim_ord_grp_achv2025_person_trgt" +
                            " where dep_month>=? and dep_month <= ? group by dep_month", new DalHints().freeSql(), startMonthStr, endMonthStr);
                }  else {
                    targets = trgtDao.query("select sum(target_profit) as target_profit, concat('2025', '-', dep_month) as dep_month from dim_ord_grp_achv2025_person_trgt" +
                            " where dep_month>=? and dep_month <= ? and pm_lvl3_no in (?) group by dep_month", new DalHints().freeSql(), startMonthStr, endMonthStr, empCodes);
                }
                Map<String, BigDecimal> targetMap = targets.stream().collect(Collectors.toMap(DimOrdGrpAchv2025PersonTrgt::getDepMonth,
                        DimOrdGrpAchv2025PersonTrgt::getTargetProfit, (v1, v2) -> v1));
                for (Map<String, Object> rowDatum : rowData) {
                    String depDate = (String)rowDatum.get("dep_date");
                    BigDecimal tarVal = targetMap.getOrDefault(depDate, BigDecimal.ZERO);
                    rowDatum.put("target_profit", tarVal.doubleValue());
                }

            }  else {
                String endDate = ((String[])param.get("dep_date"))[1];
                String monthStr = endDate.split("-")[1];
                BigDecimal targetByEmpCode = trgtDao.getTargetProfitByEmpCode(empCodes, monthStr);
                rowData.forEach(row -> {
                    row.put("target_profit", targetByEmpCode.doubleValue());
                });
            }
        } catch (Exception e) {
            log.warn("query trgt error", e);
        }

        return doHandleResults(serice, param, timeAggType, needTimeAgg, rowData);
    }
}
