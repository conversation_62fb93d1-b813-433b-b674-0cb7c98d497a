package com.ctrip.tour.business.dashboard.grpBusiness.job;//package com.ctrip.tour.business.dashboard.grpBusiness.job;

import static com.ctrip.tour.business.dashboard.grpBusiness.common.GrpConstant.DIY_PROFIT_ACHIEVE_RATE_SETTING;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.SQLType;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.base.SQLResult;
import com.ctrip.platform.dal.dao.base.SQLResultSpec;
import com.ctrip.tour.business.dashboard.grpBusiness.common.GrpConstant;
import com.ctrip.tour.business.dashboard.grpBusiness.dao.mysql.OdsDataUploadNewNpsTargetLatestDao;
import com.ctrip.tour.business.dashboard.grpBusiness.dao.starrocks.StarRocksCommonDao;
import com.ctrip.tour.business.dashboard.grpBusiness.entity.OdsDataUploadNewNpsTargetLatest;
import com.ctrip.tour.business.dashboard.grpBusiness.service.CustEmpOrgInfoService;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.group.workbenchsvc.contract.GetAlertThresholdSettingListInfoType;
import com.ctrip.tour.group.workbenchsvc.contract.GetAlertThresholdSettingListRequestType;
import com.ctrip.tour.group.workbenchsvc.contract.GetAlertThresholdSettingListResponseType;
import com.ctrip.tour.group.workbenchsvc.contract.StructuredTableInfoType;
import com.ctrip.tour.group.workbenchsvc.contract.StructuredTableRowInfoType;
import com.ctrip.tour.rights.client.GroupWorkbenchServiceClient;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2025/3/25
 */
@Service
@Slf4j
public class DiyNpsAlertNotifyService extends CommonAlertNotifyService {

    private static final String TASK_DIY_PROFIT_ACH_RATE_ALERT = "TASK_NPS_ACHIEVEMENT_ALERT";
    private static final String EVENT_DIY_PROFIT_ACH_RATE_ALERT = "EVENT_NPS_ACHIEVEMENT_ALERT";
    private static final String EVENT_DIY_PROFIT_ACH_RATE_STRUCTURED_TABLE = "EVENT_NPS_ACHIEVEMENT_ALERT_STRUCTURED_TABLE";
    @Autowired
    private StarRocksCommonDao starRocksCommonDao;
    @Autowired
    private CustEmpOrgInfoService custEmpOrgInfoService;
    @Autowired
    private OdsDataUploadNewNpsTargetLatestDao npsTargetLatestDao;

    private static String NOTIFY_EVENT_EMPCODES = "notify.event.empcodes";

    private GroupWorkbenchServiceClient client = GroupWorkbenchServiceClient.getInstance();

    @Autowired
    private RemoteConfig remoteConfig;

    public void handleSelfSrvCov() throws Exception {

        String whiteEmpCodesStr = remoteConfig.getExternalConfig(NOTIFY_EVENT_EMPCODES);

        List<String> whiteEmpCodes = Lists.newArrayList();
        if (StringUtils.isNotBlank(whiteEmpCodesStr)) {
            whiteEmpCodes = Splitter.on(",").splitToList(whiteEmpCodesStr);
        }

        // 获取当前日期
        LocalDate today = LocalDate.now();

        // 获取两周前的开始日期（即两周前的周一）
        LocalDate startDate = today.minusWeeks(2)
                .with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));

        // 获取两周前的结束日期（即两周前的周日）
        LocalDate endDate = today.minusWeeks(2)
                .with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));

        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");


        String sql = "\n" +
                "WITH current_week_data AS (\n" +
                "    select\n" +
                "    sum(nps_supplier_recommend_v5 - nps_supplier_detract_v5)/sum(nps_denominator_v5) as fittingNps,\n" +
                "sum(nps_supplier_recommend_v5)/ sum(nps_denominator_v5) as recommend,\n" +
                "sum(nps_supplier_detract_v5) / sum(nps_denominator_v5) as slanderWCov,\n" +
                "  \n" +
                " grade_region_name\n" +
                "from\n" +
                "    adm_sev_cus_metrics_tag_to_workbench_df \n" +
                "where\n" +
                "    tour_enddate>='"+startDate.format(dtf)+"' \n" +
                "    and tour_enddate<='"+endDate.format(dtf)+"' \n" +
                "    and partition_d='"+LocalDate.now().format(dtf)+"' \n" +
                "group by\n" +
                "    grade_region_name\n" +
                "),\n" +
                "last_week_data AS (\n" +
                "    SELECT\n" +
                "        grade_region_name,\n" +
                "        SUM(nps_supplier_recommend_v5 - nps_supplier_detract_v5) / SUM(nps_denominator_v5) AS last_week_fittingNps\n" +
                "    FROM\n" +
                "        adm_sev_cus_metrics_tag_to_workbench_df\n" +
                "    WHERE\n" +
                "        tour_enddate >= DATE_SUB('"+startDate.format(dtf)+"', INTERVAL 1 WEEK) \n" +
                "        AND tour_enddate <= DATE_SUB('"+endDate.format(dtf)+"', INTERVAL 1 WEEK) \n" +
                "        AND partition_d='"+LocalDate.now().format(dtf)+"' \n" +
                "    GROUP BY\n" +
                "        grade_region_name\n" +
                ")\n" +
                "SELECT\n" +
                "    c.grade_region_name,\n" +
                "    c.fittingNps,\n" +
                "    c.recommend,\n" +
                "\tc.slanderWCov,\n" +
                "    (c.fittingNps - l.last_week_fittingNps) / l.last_week_fittingNps AS week_over_week_ratio\n" +
                "FROM\n" +
                "    current_week_data c\n" +
                "LEFT JOIN\n" +
                "    last_week_data l\n" +
                "ON\n" +
                "    c.grade_region_name = l.grade_region_name;";


        List<Map<String, Object>> resultList = starRocksCommonDao.query(sql, Maps.newHashMap());
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }

        Map<String, List<Map<String, Object>>> resultGroupByPmEid = resultList.stream()
                .collect(Collectors.groupingBy(map -> (String) map.get("grade_region_name")));

        Map<String, List<String>> empNoByAreaNames = custEmpOrgInfoService.getEmpNoByAreaNames(new ArrayList<>(resultGroupByPmEid.keySet()), false);
        StructuredTableInfoType structuredTableInfoType = new StructuredTableInfoType();
        empNoByAreaNames
                .forEach((k, v) -> {
                    try {
                        List<StructuredTableRowInfoType> rowInfoTypes = v.stream().map(areaName -> {
                            List<Map<String, Object>> maps = resultGroupByPmEid.get(areaName);
                            if (CollectionUtils.isEmpty(maps)) {
                                return null;
                            }
                            Map<String, Object> infoMap = maps.get(0);
                            Double fittingNps = (Double) infoMap.get("fittingNps");
                            Double week_over_week_ratio = (Double) infoMap.get("week_over_week_ratio");
                            Double recommend = (Double) infoMap.get("recommend");
                            Double slanderWCov = (Double) infoMap.get("slanderWCov");

                            Double npsTarget = getNpsTarget(areaName);
                            if (Objects.isNull(npsTarget)) {
                                return null;
                            }

                            if (fittingNps < npsTarget) {
                                StructuredTableRowInfoType rowInfoType = new StructuredTableRowInfoType();
                                List<String> colList = Lists.newArrayList(getDataRatioStr(fittingNps), getDataRatioStr(week_over_week_ratio), getDataRatioStr(recommend), getDataRatioStr(slanderWCov));//NOSONAR
                                rowInfoType.setColList(colList);
                                return rowInfoType;
                            }
                            return null;

                        }).filter(Objects::nonNull).collect(Collectors.toList());
                        structuredTableInfoType.setRowList(rowInfoTypes);
                        structuredTableInfoType.setHeaderList(Lists.newArrayList("NPS", "周环比", "推荐率", "诋毁率"));//NOSONAR

                        String content = "拟合NPS、推荐率、诋毁率未达成目标预警，请关注达成情况。";//NOSONAR
                        List<String> tpInfos = Lists.newArrayList("拟合NPS、推荐率、诋毁率未达成目标预警，请关注达成情况。");//NOSONAR
                        notifyEmp(k, structuredTableInfoType, "拟合NPS达成通知",//NOSONAR
                                TASK_DIY_PROFIT_ACH_RATE_ALERT, EVENT_DIY_PROFIT_ACH_RATE_ALERT, EVENT_DIY_PROFIT_ACH_RATE_STRUCTURED_TABLE, content, tpInfos, "1");//NOSONAR
                    } catch (Exception e) {
                        log.warn("handleSelfSrvCov error", e);
                    }


                });

//        List<EdwHrEmpVacation> edwHrEmpVacations = hrOrgEmpInfoService.queryEmpByDomainNames(Lists.newArrayList(resultGroupByPmEid.keySet()));
//        if (CollectionUtils.isEmpty(edwHrEmpVacations)) {
//            log.warn("can not find emp info");
//            return;
//        }
//        Map<String, String> domai2EmpCodeMap = edwHrEmpVacations.stream().collect(Collectors.toMap(EdwHrEmpVacation::getDomainName, EdwHrEmpVacation::getEmpCode));

    }

    private Double getNpsTarget(String domainName) {
        try {
            OdsDataUploadNewNpsTargetLatest npsTargetLatest = npsTargetLatestDao.queryFirst("select * from ods_data_upload_new_nps_target_latest " +
                    "where grade_region = ? and month=?", new DalHints(), SQLResult.type(OdsDataUploadNewNpsTargetLatest.class), domainName, LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM")));
            if (npsTargetLatest != null && npsTargetLatest.getTargetVal() != null) {
                return npsTargetLatest.getTargetVal().doubleValue();
            }
        } catch (Exception e) {
            log.error("getNpsTarget error", e);
        }
        return null;
    }


}