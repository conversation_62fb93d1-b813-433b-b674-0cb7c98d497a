package com.ctrip.tour.business.dashboard.tktExternal;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.utils.ObjectUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ctrip.tour.business.dashboard.sightArchives.service.UserProfileService;

/**
 * <AUTHOR>
 * @Date 2025/4/24
 */
@Service
public class UserDimAssembleService {

    @Autowired
    private UserProfileService userProfileService;


    public GetUserDimDetailInfoResponseType getUserDimDetailInfo(GetUserDimDetailInfoRequestType requestType) throws ExecutionException, InterruptedException {
        GetUserDimDetailInfoResponseType responseType = new GetUserDimDetailInfoResponseType();
        SightArchivesCommonFilter commonFilter = new SightArchivesCommonFilter();
        if (requestType.getBusinessType() != null) {
            commonFilter.setBusinessType(requestType.getBusinessType());
        } else {
            commonFilter.setBusinessType(1);
        }

        if (requestType.isNeedSubSight() != null) {
            commonFilter.setNeedSubSight(requestType.isNeedSubSight());
        } else {
            commonFilter.setNeedSubSight(false);
        }

        if (requestType.getDateType() != null) {
            commonFilter.setDateType(requestType.getDateType());
        } else {
            commonFilter.setDateType(1);
        }

        if (CollectionUtils.isNotEmpty(requestType.getVendorIdList())){
            commonFilter.setVendorIdList(requestType.getVendorIdList());
        }else {
            commonFilter.setVendorIdList(Lists.newArrayList());
        }
        
        commonFilter.setStartDate(requestType.getStartDate());
        commonFilter.setEndDate(requestType.getEndDate());
        commonFilter.setSightId(requestType.getSightId());

        CompletableFuture<GetUserProfileHistogramResponseType> histogramResponseTypeCompletableFuture = CompletableFuture.supplyAsync(() -> {
            GetUserProfileHistogramRequestType getUserProfileHistogramRequestType = new GetUserProfileHistogramRequestType();
            getUserProfileHistogramRequestType.setCommonFilter(commonFilter);
            return userProfileService.getUserProfileHistogram(getUserProfileHistogramRequestType);
        });
        CompletableFuture<GetUserProfilePieChartResponseType> pieChartResponseTypeCompletableFuture = CompletableFuture.supplyAsync(() -> {
            GetUserProfilePieChartRequestType getUserProfileHistogramRequestType = new GetUserProfilePieChartRequestType();
            getUserProfileHistogramRequestType.setCommonFilter(commonFilter);
            return userProfileService.getUserProfilePieChart(getUserProfileHistogramRequestType);
        });
        CompletableFuture<GetUserResidenceDistributionResponseType> distributionResponseTypeCompletableFuture = CompletableFuture.supplyAsync(() -> {
            GetUserResidenceDistributionRequestType getUserResidenceDistributionRequestType = new GetUserResidenceDistributionRequestType();
            getUserResidenceDistributionRequestType.setCommonFilter(commonFilter);
            return userProfileService.getUserResidenceDistribution(getUserResidenceDistributionRequestType);
        });

        CompletableFuture<GetUserSearchPreferenceResponseType> searchPreferenceFuture = CompletableFuture.supplyAsync(
                () -> userProfileService.getUserSearchPreference(new GetUserSearchPreferenceRequestType(commonFilter)));

        CompletableFuture.allOf(histogramResponseTypeCompletableFuture,
                pieChartResponseTypeCompletableFuture,
                distributionResponseTypeCompletableFuture,
                searchPreferenceFuture).join();

        GetUserProfileHistogramResponseType getUserProfileHistogramResponseType = histogramResponseTypeCompletableFuture.get();
        GetUserProfilePieChartResponseType getUserProfilePieChartResponseType = pieChartResponseTypeCompletableFuture.get();
        GetUserResidenceDistributionResponseType getUserResidenceDistributionResponseType = distributionResponseTypeCompletableFuture.get();
        GetUserSearchPreferenceResponseType getUserSearchPreferenceResponseType = searchPreferenceFuture.get();

        if (Objects.nonNull(getUserProfileHistogramResponseType)) {
            List<UserProfileHistogramItem> userAgeGroupList =
                    getUserProfileHistogramResponseType.getUserAgeGroupList();

            List<UserProfileHistogramItem> advanceBookingDayList =
                    getUserProfileHistogramResponseType.getAdvanceBookingDayList();
            List<BookingAdvanceDayInfo> bookingAdvanceDayInfos = Optional.ofNullable(advanceBookingDayList).orElse(Lists.newArrayList()).stream()
                    .map(abd -> {
                        BookingAdvanceDayInfo dayInfo = new BookingAdvanceDayInfo();
                        if (abd.getOrderNum() != null) {
                            dayInfo.setValue(abd.getOrderNum());
                        }
                        dayInfo.setBookingDayName(abd.getSegmentName());
                        return dayInfo;
                    }).collect(Collectors.toList());

            List<UserAgeRangeInfo> userAgeRangeInfos = Optional.ofNullable(userAgeGroupList).orElse(Lists.newArrayList()).stream()
                    .map(uag -> {
                        UserAgeRangeInfo userAgeRangeInfo = new UserAgeRangeInfo();
                        userAgeRangeInfo.setAgeRangeName(uag.getSegmentName());
                        if (uag.getOrderNum() != null) {
                            userAgeRangeInfo.setValue(uag.getOrderNum());
                        }
                        return userAgeRangeInfo;
                    }).collect(Collectors.toList());
            responseType.setUserAgeRangeList(userAgeRangeInfos);
            responseType.setBookingAdvanceDayList(bookingAdvanceDayInfos);
        }

        if (Objects.nonNull(getUserProfilePieChartResponseType)) {
            List<UserProfilePieChart> pieChartList = getUserProfilePieChartResponseType.getPieChartList();
            Map<String, UserProfilePieChart> profilePieChartMap = pieChartList.stream()
                    .collect(Collectors.toMap(UserProfilePieChart::getChartName, Function.identity(), (a, b) -> a));
            UserProfilePieChart userLevel = profilePieChartMap.get("userLevel");
            UserProfilePieChart userType = profilePieChartMap.get("userType");
            UserProfilePieChart domesticAndOversea = profilePieChartMap.get("domesticAndOversea");

            if (Objects.nonNull(userLevel)) {
                List<UserLevelInfo> userLevelInfos = Optional.ofNullable(userLevel.getSegmentList()).orElse(Lists.newArrayList()).stream()
                        .map(segment -> {
                            UserLevelInfo userLevelInfo = new UserLevelInfo();
                            userLevelInfo.setLevelName(segment.getName());
                            userLevelInfo.setValue(segment.getCount());
                            return userLevelInfo;
                        }).collect(Collectors.toList());
                responseType.setUserLevelList(userLevelInfos);
            }

            if (Objects.nonNull(userType)) {
                List<UserCategoryInfo> userCategoryInfos = Optional.ofNullable(userType.getSegmentList()).orElse(Lists.newArrayList()).stream()
                        .map(segment -> {
                            UserCategoryInfo userLevelInfo = new UserCategoryInfo();
                            userLevelInfo.setCategoryName(segment.getName());
                            userLevelInfo.setValue(segment.getCount());
                            return userLevelInfo;
                        }).collect(Collectors.toList());
                responseType.setUserCategoryList(userCategoryInfos);
            }

            if (Objects.nonNull(domesticAndOversea)) {
                List<ForeignAnddomesticUserInfo> foreignAnddomesticUserInfos = Optional.ofNullable(domesticAndOversea.getSegmentList()).orElse(Lists.newArrayList())
                        .stream()
                        .map(segment -> {
                            ForeignAnddomesticUserInfo foreignAnddomesticUserInfo = new ForeignAnddomesticUserInfo();
                            foreignAnddomesticUserInfo.setPermanentDomain(segment.getName());
                            foreignAnddomesticUserInfo.setValue(segment.getCount());
                            return foreignAnddomesticUserInfo;
                        }).collect(Collectors.toList());
                responseType.setForeignAnddomesticUserInfoList(foreignAnddomesticUserInfos);
            }


        }

        if (Objects.nonNull(getUserResidenceDistributionResponseType)) {
            List<UserCityInfo> userCityInfos = Optional.ofNullable(getUserResidenceDistributionResponseType.getCityList()).orElse(Lists.newArrayList()).stream()
                    .map(city -> {
                        UserCityInfo userCityInfo = new UserCityInfo();
                        userCityInfo.setCityName(city.getCityName());
                        if (city.getOrderCountPercentage() != null) {
                            userCityInfo.setValue(city.getOrderCountPercentage());
                        }
                        return userCityInfo;
                    }).collect(Collectors.toList());

            responseType.setUserCityList(userCityInfos);
        }

        if (Objects.nonNull(getUserProfileHistogramResponseType)) {
            List<UserSearchPreferenceSearchKeyForUserDimDetailInfo> userSearchPreferenceList = Optional.ofNullable(getUserSearchPreferenceResponseType.getSearchKeyList()).orElse(Lists.newArrayList()).stream()
                    .map(x -> ObjectUtil.deepJsonCopy(x, UserSearchPreferenceSearchKeyForUserDimDetailInfo.class))
                    .collect(Collectors.toList());
            responseType.setSearchKeyList(userSearchPreferenceList);
        }

        return responseType;

    }
}
