domain_info:
  - db_name:
    id: 1
    table_name: adm_ord_grp_work_platform_prdt_df
    source_type: starrocks
    indicators:
      - indicator_name: suc_income
        indicator_name_cn: 销售额
        indicator_type: double
        indicator_format: double
        indicator_key: v.page.workbench.salesVolume.tips
        indicator_key_name: income
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: suc_income_V2
        indicator_name_cn: 销售额
        indicator_type: double
        indicator_format: double
        indicator_key: v.page.workbench.salesVolume.tips
        indicator_key_name: income
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: self_suc_income
        indicator_name_cn: 自营销售额
        indicator_type: double
        indicator_format: double
        indicator_key: v.page.workbench.selfOprIncome.tips
        indicator_key_name: selfOprIncome
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: self_suc_income_proportion
        indicator_name_cn: 自营销售额占比
        indicator_type: double
        indicator_format: percent
        indicator_key: v.page.workbench.selfOprIncomeRatio.tips
        indicator_key_name: selfOprIncomeRatio
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: suc_profit
        indicator_name_cn: 毛利
        indicator_type: double
        indicator_format: double
        indicator_key: v.page.workbench.grossProfit.tips
        indicator_key_name: profitPrice
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: self_suc_profit
        indicator_name_cn: 自营毛利
        indicator_type: double
        indicator_format: double
        indicator_key: v.page.workbench.selfOprProfit.tips
        indicator_key_name: selfOprProfit
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: self_suc_profit_proportion
        indicator_name_cn: 自营毛利占比
        indicator_type: double
        indicator_format: percent
        indicator_key: v.page.workbench.selfOprProfitRatio.tips
        indicator_key_name: selfOprProfitRatio
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: self_suc_profit_rate
        indicator_name_cn: 自营毛利占比没有用的指标
        indicator_type: double
        indicator_format: percent
        indicator_key: v.page.workbench.selfOprProfitRate.tips
        indicator_key_name: selfOprProfitRate
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: good_guide_sev_ord_cnt_rate
        indicator_name_cn: 金牌导游真实派遣率
        indicator_type: double
        indicator_format: percent
        indicator_key: v.page.workbench.grpGuiderAct.tips
        indicator_key_name: grpGuiderActualDispacthRate
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: send_good_guide_ord_cnt_rate
        indicator_name_cn: 金牌导游派遣率
        indicator_type: double
        indicator_format: percent
        indicator_key: v.page.workbench.grpGuiderDisp.tips
        indicator_key_name: grpGuiderDispatchRate
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: good_guide_sev_ord_check_cnt_rate
        indicator_name_cn: 金牌导游打卡率
        indicator_type: double
        indicator_format: percent
        indicator_key: v.page.workbench.grpGuiderCheckIn.tips
        indicator_key_name: grpGuiderCheckInRate
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: good_driver_sev_ord_cnt_rate
        indicator_name_cn: 金牌司机打卡率
        indicator_type: double
        indicator_format: percent
        indicator_key: v.page.workbench.grpDirverCheckIn.tips
        indicator_key_name: grpDriverCheckInRate
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: good_driver_sev_ord_sop_cnt_rate
        indicator_name_cn: 金牌司机SOP执行率
        indicator_type: double
        indicator_format: percent
        indicator_key: v.page.workbench.grpDiriverExe.tips
        indicator_key_name: grpDriverExecutionRate
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: send_good_driver_ord_cnt_rate
        indicator_name_cn: 金牌司机派遣率
        indicator_type: double
        indicator_format: percent
        indicator_key: v.page.workbench.grpDirverDisp.tips
        indicator_key_name: grpDriverDispatchRate
        indicator_is_dimension: false
        indicator_is_support_sorting: true

      - indicator_name: date
        indicator_name_cn: 日期
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: date_week
        indicator_name_cn: 日期
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: date_month
        indicator_name_cn: 日期
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false

      - indicator_name: sub_bu_type
        indicator_name_cn: 产线
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: prd_pattern_name
        indicator_name_cn: 产品形态
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: dest_domain
        indicator_name_cn: 区域
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: sale_channel_name
        indicator_name_cn: 销售渠道
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: sale_channel_name
        indicator_name_cn: 销售模式
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: tour_region_type
        indicator_name_cn: 客源地/目的地
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: prd_region_name
        indicator_name_cn: 产品大区
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: pm_eid
        indicator_name_cn: 业务经理
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: pm_name
        indicator_name_cn: 业务经理名称
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: dest_first_region
        indicator_name_cn: 运营大区
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: dest_city_name
        indicator_name_cn: 目的地
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: vendor_id
        indicator_name_cn: 供应商ID
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: vendor_name
        indicator_name_cn: 供应商名称
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: is_sec_kill
        indicator_name_cn: 是否参与秒杀
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: local_pm_eid
        indicator_name_cn: 驻地业务经理
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: local_pm_name
        indicator_name_cn: 驻地业务经理名称
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: plc_sale_mode
        indicator_name_cn: 下单时销售模式
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: l1_route_name
        indicator_name_cn: 线路玩法（L1）
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: prd_level_name
        indicator_name_cn: 钻级
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: pk_tour_line
        indicator_name_cn: 线路玩法·钻级·天数·是否拼小团
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: cpr_dest_area
        indicator_name_cn: 竞争圈
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false

    template: |
      select 
              * 
      from (
              SELECT  1 as default_group
                      <#if indicators?seq_contains("suc_income")> ,sum(suc_income) as suc_income </#if> -- 销售额，月环比，年同比
                      <#if indicators?seq_contains("suc_income_V2")> ,sum(suc_income) as suc_income_V2 </#if> -- 销售额，月环比，年同比

                      <#if indicators?seq_contains("self_suc_income")> ,sum(case when bu_sale_mode IN ('合同自营', '双好自营') then suc_income else 0 end) as self_suc_income </#if> -- 自营销售额
                      <#if indicators?seq_contains("self_suc_income_proportion")> ,sum(case when bu_sale_mode IN ('合同自营', '双好自营') then suc_income else 0 end) / sum(suc_income) as self_suc_income_proportion </#if> -- 自营销售额占比
                      <#if indicators?seq_contains("suc_profit")> ,sum(suc_profit) as suc_profit </#if> -- 毛利，月环比，年同比
                      <#if indicators?seq_contains("self_suc_profit")> ,sum(case when bu_sale_mode IN ('合同自营', '双好自营') then suc_profit else 0 end) as self_suc_profit </#if> -- 自营毛利
                      <#if indicators?seq_contains("self_suc_profit_proportion")> ,sum(case when bu_sale_mode IN ('合同自营', '双好自营') then suc_profit else 0 end) / sum(suc_profit) as self_suc_profit_proportion </#if> -- 自营毛利占比,
                      
                      <#if indicators?seq_contains("good_guide_sev_ord_cnt_rate")> ,sum(good_guide_sev_ord_cnt) / sum(should_send_guide_ord_cnt) as good_guide_sev_ord_cnt_rate </#if> -- 金牌导游真是派遣率
                      <#if indicators?seq_contains("send_good_guide_ord_cnt_rate")> ,sum(send_good_guide_ord_cnt) / sum(should_send_guide_ord_cnt) as send_good_guide_ord_cnt_rate </#if> -- 金牌导游派遣率
                      <#if indicators?seq_contains("good_guide_sev_ord_check_cnt_rate")> ,sum(good_guide_sev_ord_cnt) / sum(send_good_guide_ord_cnt) as good_guide_sev_ord_check_cnt_rate </#if> -- 金牌导游打卡率
                      
                      <#if indicators?seq_contains("good_driver_sev_ord_cnt_rate")> ,sum(good_driver_sev_ord_cnt) / sum(should_send_driver_ord_cnt) as good_driver_sev_ord_cnt_rate </#if> -- 金牌司机打卡率
                      <#if indicators?seq_contains("good_driver_sev_ord_sop_cnt_rate")> ,sum(good_driver_sev_ord_cnt) / sum(send_good_guide_ord_cnt) as good_driver_sev_ord_sop_cnt_rate </#if> -- 金牌司机SOP执行率
                      <#if indicators?seq_contains("send_good_driver_ord_cnt_rate")> ,sum(send_good_driver_ord_cnt) / sum(should_send_driver_ord_cnt) as send_good_driver_ord_cnt_rate </#if> -- 金牌司机派遣率
              
                      <#if indicators?seq_contains("ava_order_cap")> ,0 as ava_order_cap </#if>
                      <#if indicators?seq_contains("vendor_ord_accept_rate")> ,0 as vendor_ord_accept_rate </#if>
                      <#if indicators?seq_contains("completed_orders")> ,0 as completed_orders </#if>
                      <#if indicators?seq_contains("completed_orders_rate")> ,0 as completed_orders_rate </#if>
                      <#if indicators?seq_contains("ord_avg_price")> ,0 as ord_avg_price </#if>
                      <#if indicators?seq_contains("cpr_completed_orders_rate")> ,0 as cpr_completed_orders_rate </#if>
              
                      <#if group_by??>
#                          <#list group_by as value>
                              <#if value = "date"> ,date_format(to_date(dep_date), '%Y-%m-%d') as date
                              <#elseif value = "date_week"> ,date_format(date_sub(to_date(dep_date), interval (dayofweek(to_date(dep_date)) + 5) % 7  day), '%Y-%m-%d') as date_week
                              <#elseif value = "date_month"> ,date_format(date_sub(to_date(dep_date), interval dayofmonth(to_date(dep_date)) -1  day), '%Y-%m-%d') as date_month
                              <#elseif value = "local_pm_eid"> ,local_pmeid as local_pm_eid
                              <#elseif value = "local_pm_name"> ,"" as local_pm_name
                              <#elseif value = "pm_name"> ,"" as pm_name
                              <#else> ,${value}
                              </#if>
                          </#list>
                      </#if>
              FROM
                      adm_ord_grp_work_platform_prdt_df
              WHERE   partition_d = (select max(partition_d) from adm_ord_grp_work_platform_prdt_df)
              <#if filter_flat.partition_d??>   AND     partition_d = '${filter_flat.partition_d.filterValues[0]}' </#if>
              <#if filter_flat.date??>          AND     dep_date between '${filter_flat.date.filterValues[0]}' AND '${filter_flat.date.filterValues[1]}' </#if>
        
              <#if filter_flat.emp_codes??>     AND     (pm_eid IN ('${filter_flat.emp_codes.filterValues?join("', '")}') OR local_pmeid IN ('${filter_flat.emp_codes.filterValues?join("', '")}') ) </#if>
              <#if filter_flat.dest_area??>     AND     dest_area  IN ('${filter_flat.dest_area.filterValues?join("', '")}') </#if>
              
              <#if filter_flat.prd_pattern_name??>   AND     prd_pattern_name IN ('${filter_flat.prd_pattern_name.filterValues?join("', '")}') </#if> -- 跟团、 私家团
              <#if filter_flat.business_line??>
                  <#if filter_flat.business_line.filterValues[0] = "100">
                      AND (sub_bu_type IN ("跟团游") and is_small_group = 0)  -- AND sub_bu_type IN ("跟团游")
                  <#elseif filter_flat.business_line.filterValues[0] = "200">
                      and (sub_bu_type IN ("独立出游") or is_small_group = 1)  -- AND sub_bu_type IN ("独立出游")
                  <#elseif filter_flat.business_line.filterValues[0] = "220">
                  </#if>
              </#if>

              <#if filter_flat.cur_filter_str??> AND ${filter_flat.cur_filter_str.filterValues[0]} </#if>
              <#if group_by??>group by default_group <#list group_by as value> ,${value} </#list></#if>
      ) as t
      <#if order_by??>
          order by <#list order_by as item>${item.order_field} ${item.order_type}, </#list> 1
      </#if>
      <#if limit??>
          limit ${limit.limit_start},${limit.limit_size}
      </#if>
      

  - db_name:
    id: 111
    table_name: adm_ord_grp_work_platform_prdt_df
    source_type: starrocks
    indicators:
      - indicator_name: suc_income_rate
        indicator_name_cn: 销售额达成率
        indicator_type: double
        indicator_format: percent
        indicator_key: v.page.workbench.grpIncomeAch.tips
        indicator_key_name: grpIncomeAchieveRate
        indicator_is_dimension: false
        indicator_is_support_sorting: false
      - indicator_name: suc_income_target
        indicator_name_cn: 销售额目标
        indicator_type: double
        indicator_format: double
        indicator_key: v.page.workbench.grpIncomeTarget.tips
        indicator_key_name: grpIncomeTarget
        indicator_is_dimension: false
        indicator_is_support_sorting: false
      - indicator_name: suc_profit_rate
        indicator_name_cn: 毛利达成率
        indicator_type: double
        indicator_format: percent
        indicator_key: v.page.workbench.grpProfitAcg.tips
        indicator_key_name: grpProfitAchieveRate
        indicator_is_dimension: false
        indicator_is_support_sorting: false
      - indicator_name: suc_profit_target
        indicator_name_cn: 毛利目标
        indicator_type: double
        indicator_format: double
        indicator_key: v.page.workbench.grpProfitTarget.tips
        indicator_key_name: grpProfitTarget
        indicator_is_dimension: false
        indicator_is_support_sorting: false

    template: |
      select 
              * 
      from (
              select
                      sum(suc_income_target) as suc_income_target,
                      sum(suc_profit_target) as suc_profit_target,
                      sum(suc_income) /  sum(suc_income_target) as suc_income_rate,
                      sum(suc_profit) /  sum(suc_profit_target) as suc_profit_rate
              from (
                      SELECT  
                              sum(suc_income) as suc_income, sum(suc_profit) as suc_profit, 0 as suc_income_target, 0 as suc_profit_target
                      FROM 
                              adm_ord_grp_work_platform_prdt_df
                      WHERE   partition_d = (select max(partition_d) from adm_ord_grp_work_platform_prdt_df)
                      AND     (pm_eid IN ('${filter_flat.emp_codes.filterValues?join("', '")}') OR local_pmeid IN ('${filter_flat.emp_codes.filterValues?join("', '")}') )
                      AND     dep_date between concat(substr('${filter_flat.date.filterValues[1]}', 1, 8), '01') AND '${filter_flat.date.filterValues[1]}'
                      <#if filter_flat.business_line??>
                          <#if filter_flat.business_line.filterValues[0] = "100">
                              AND (sub_bu_type IN ("跟团游") and is_small_group = 0)  -- AND sub_bu_type IN ("跟团游")
                          <#elseif filter_flat.business_line.filterValues[0] = "200">
                              and (sub_bu_type IN ("独立出游") or is_small_group = 1)  -- AND sub_bu_type IN ("独立出游")
                          <#elseif filter_flat.business_line.filterValues[0] = "220">
                          </#if>
                      </#if>
                      
                      union all
                      
                      select 
                              0 as result_value, 
                              0 as suc_profit, 
                              sum(target_income) as suc_income_target, 
                              sum(target_profit) as suc_profit_target 
                      from 
                              dim_ord_grp_achv2025_person_trgt
                      where   partition_d = (select max(partition_d) from dim_ord_grp_achv2025_person_trgt)
                      and     dep_month = substr('${filter_flat.date.filterValues[1]}', 6, 2)
                      and     pm_lvl3_no in ('${filter_flat.emp_codes.filterValues?join("', '")}')
                ) as t
      ) as t
      <#if order_by??>
          -- order by <#list order_by as item>${item.order_field} ${item.order_type}, </#list> 1
      </#if>
      <#if limit??>
          limit ${limit.limit_start},${limit.limit_size}
      </#if>

  - db_name:
    id: 2
    table_name: edw_log_grp_cpr_platform_flow_cr_di
    source_type: starrocks
    indicators:
      - indicator_name: detail_page_uv
        indicator_name_cn: 详情页uv
        indicator_type: long
        indicator_format: long
        indicator_key: v.page.workbench.detailPage.UV.tips
        indicator_key_name: uv
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: detail_page_cvr
        indicator_name_cn: 详情页转化率
        indicator_type: double
        indicator_format: percent
        indicator_key: v.page.workbench.detailPage.conversion.tips
        indicator_key_name: conversionRate
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: self_detail_page_cvr
        indicator_name_cn: 自营详情页转化率
        indicator_type: double
        indicator_format: percent
        indicator_key: v.page.workbench.selfOprConversionRate.conversion.tips
        indicator_key_name: selfOprConversionRate
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: agent_detail_page_cvr
        indicator_name_cn: 代理详情页转化率
        indicator_type: double
        indicator_format: percent
        indicator_key: v.page.workbench.agentConversionRate.conversion.tips
        indicator_key_name: agentConversionRate
        indicator_is_dimension: false
        indicator_is_support_sorting: true

      - indicator_name: date
        indicator_name_cn: 日期
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: date_week
        indicator_name_cn: 日期
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: date_month
        indicator_name_cn: 日期
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false

      - indicator_name: sub_bu_type
        indicator_name_cn: 产线
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: prd_pattern_name
        indicator_name_cn: 产品形态
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: dest_domain
        indicator_name_cn: 区域
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: sale_channel_name
        indicator_name_cn: 销售渠道
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: sale_mode_name
        indicator_name_cn: 销售模式
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: tour_region_type
        indicator_name_cn: 客源地/目的地
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: prd_region_name
        indicator_name_cn: 产品大区
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: pm_eid
        indicator_name_cn: 业务经理
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: pm_name
        indicator_name_cn: 业务经理名称
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: dest_first_region
        indicator_name_cn: 运营大区
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: dest_city_name
        indicator_name_cn: 目的地
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: vendor_id
        indicator_name_cn: 供应商ID
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: vendor_name
        indicator_name_cn: 供应商名称
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: is_sec_kill
        indicator_name_cn: 是否参与秒杀
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false


    template: |
      select 
              * 
      from (
              SELECT  1 as default_group
                      <#if indicators?seq_contains("detail_page_uv")> ,count(distinct concat(vid,partition_d)) as detail_page_uv </#if> -- 详情页uv，月环比，年同比
                      <#if indicators?seq_contains("detail_page_cvr")> ,count(distinct order_id) / count(distinct concat(vid,partition_d)) as detail_page_cvr </#if> -- 详情页转化率，月环比，年同比
                      <#if indicators?seq_contains("self_detail_page_cvr")> ,count(distinct case when bu_sale_mode  IN ('双好自营', '合同自营') then order_id else null end) / count(distinct case when bu_sale_mode  IN ('双好自营', '合同自营') then concat(vid,partition_d) else null end) as self_detail_page_cvr </#if> -- 自营详情页转化率，月环比，年同比
                      <#if indicators?seq_contains("agent_detail_page_cvr")> ,count(distinct case when bu_sale_mode  IN ('代理') then order_id else null end) / count(distinct case when bu_sale_mode  IN ('代理') then concat(vid,partition_d) else null end) as agent_detail_page_cvr </#if> -- 代理详情页转化率，月环比，年同比
                      <#if group_by??>
                          <#list group_by as value>
                              <#if value = "date"> ,date_format(to_date(partition_d), '%Y-%m-%d') as date
                              <#elseif value = "date_week"> ,date_format(date_sub(to_date(partition_d), interval (dayofweek(to_date(partition_d)) + 5) % 7  day), '%Y-%m-%d') as date_week
                              <#elseif value = "date_month"> ,date_format(date_sub(to_date(partition_d), interval dayofmonth(to_date(partition_d)) -1  day), '%Y-%m-%d') as date_month
                              <#elseif value = "local_pm_name"> ,"" as local_pm_name
                              <#elseif value = "pm_name"> ,"" as pm_name
                              <#else> ,${value}
                              </#if>
                          </#list>
                      </#if>
              FROM
                      edw_log_grp_cpr_platform_flow_cr_di
              WHERE   1 = 1
              <#if filter_flat.date??>          AND     partition_d between '${filter_flat.date.filterValues[0]}' and '${filter_flat.date.filterValues[1]}' </#if>
        
              <#if filter_flat.emp_codes??>     AND     (pm_eid IN ('${filter_flat.emp_codes.filterValues?join("', '")}') OR local_pm_eid IN ('${filter_flat.emp_codes.filterValues?join("', '")}') ) </#if>
              <#if filter_flat.dest_area??>     AND     dest_area  IN ('${filter_flat.dest_area.filterValues?join("', '")}') </#if>
              <#if filter_flat.prd_pattern_name??>   AND     prd_pattern_name IN ('${filter_flat.prd_pattern_name.filterValues?join("', '")}') </#if>
              <#if filter_flat.business_line??>
                  <#if filter_flat.business_line.filterValues[0] = "100">
                      AND (sub_bu_type IN ("跟团游") and is_small_group = 0)  -- AND sub_bu_type IN ("跟团游")
                  <#elseif filter_flat.business_line.filterValues[0] = "200">
                      and (sub_bu_type IN ("独立出游") or is_small_group = 1)  -- AND sub_bu_type IN ("独立出游")
                  <#elseif filter_flat.business_line.filterValues[0] = "220">
                  </#if>
              </#if>

              <#if filter_flat.cur_filter_str??> AND ${filter_flat.cur_filter_str.filterValues[0]} </#if>

              <#if group_by??>group by default_group <#list group_by as value> ,${value} </#list></#if>
      ) as t
      <#if order_by??>
          order by <#list order_by as item>${item.order_field} ${item.order_type}, </#list> 1
      </#if>
      <#if limit??>
          limit ${limit.limit_start},${limit.limit_size}
      </#if>

  - db_name:
    id: 3
    table_name: cdm_sev_grp_cpr_platform_fitting_nps_df
    source_type: starrocks
    indicators:
      - indicator_name: fitting_nps
        indicator_name_cn: 当月拟合NPS
        indicator_type: double
        indicator_format: double
        indicator_key: v.page.workbench.fitting.nps.tips
        indicator_key_name: fittingNps
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: recommend_rate
        indicator_name_cn: 推荐率
        indicator_type: double
        indicator_format: percent
        indicator_key: v.page.workbench.recommendation.rate.tips
        indicator_key_name: recommendationRate
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: slander_weight_rate
        indicator_name_cn: 诋毁率
        indicator_type: double
        indicator_format: percent
        indicator_key: v.page.workbench.defamation.rate.tips
        indicator_key_name: slanderRate
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: feed_back_ord_cnt_rate
        indicator_name_cn: 订单覆盖率
        indicator_type: double
        indicator_format: percent
        indicator_key: v.page.workbench.order.coverage.rate.tips
        indicator_key_name: orderCoverageRate
        indicator_is_dimension: false
        indicator_is_support_sorting: true

      - indicator_name: date
        indicator_name_cn: 日期
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: date_week
        indicator_name_cn: 日期
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: date_month
        indicator_name_cn: 日期
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false

      - indicator_name: sub_bu_type
        indicator_name_cn: 产线
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: prd_pattern_name
        indicator_name_cn: 产品形态
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: dest_domain
        indicator_name_cn: 区域
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: sale_channel_name
        indicator_name_cn: 销售渠道
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: sale_mode_name
        indicator_name_cn: 销售模式
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: tour_region_type
        indicator_name_cn: 客源地/目的地
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: prd_region_name
        indicator_name_cn: 产品大区
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: pm_eid
        indicator_name_cn: 业务经理
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: pm_name
        indicator_name_cn: 业务经理名称
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: dest_first_region
        indicator_name_cn: 运营大区
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: dest_city_name
        indicator_name_cn: 目的地
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: vendor_id
        indicator_name_cn: 供应商ID
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: vendor_name
        indicator_name_cn: 供应商名称
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
    template: |
      select *
      from (
              SELECT  1 as default_group
                      <#if indicators?seq_contains("fitting_nps")> ,sum(recommend_weight - slander_weight) / sum(order_weight) as fitting_nps </#if>  -- 当月拟合NPS
                      <#if indicators?seq_contains("recommend_rate")> ,sum(recommend_weight) / sum(order_weight) as recommend_rate </#if>  -- 推荐率
                      <#if indicators?seq_contains("slander_weight_rate")> ,sum(slander_weight) / sum(order_weight) as slander_weight_rate </#if>  -- 诋毁率
                      <#if indicators?seq_contains("feed_back_ord_cnt_rate")> ,sum(feed_back_ord_cnt) /  sum(total_ord_cnt) as feed_back_ord_cnt_rate </#if>  -- 订单覆盖率
              
                      <#if group_by??>
                          <#list group_by as value>
                              <#if value = "date"> ,date_format(to_date(return_date), '%Y-%m-%d') as date
                              <#elseif value = "date_week"> ,date_format(date_sub(to_date(return_date), interval (dayofweek(to_date(return_date)) + 5) % 7  day), '%Y-%m-%d') as date_week
                              <#elseif value = "date_month"> ,date_format(date_sub(to_date(return_date), interval dayofmonth(to_date(return_date)) -1  day), '%Y-%m-%d') as date_month
                              <#elseif value = "local_pm_name"> ,"" as local_pm_name
                              <#elseif value = "pm_name"> ,"" as pm_name
                              <#else> ,${value}
                              </#if>
                          </#list>
                      </#if>
              FROM
                      cdm_sev_grp_cpr_platform_fitting_nps_df
              WHERE   partition_d = (select max(partition_d) from cdm_sev_grp_cpr_platform_fitting_nps_df)
              <#if filter_flat.partition_d??>   AND     partition_d = '${filter_flat.partition_d.filterValues[1]}' </#if>
              <#if filter_flat.date??>          AND     return_date between '${filter_flat.date.filterValues[0]}' and '${filter_flat.date.filterValues[1]}' </#if>
        
              <#if filter_flat.emp_codes??>     AND     (pm_eid IN ('${filter_flat.emp_codes.filterValues?join("', '")}') OR local_pm_eid IN ('${filter_flat.emp_codes.filterValues?join("', '")}') ) </#if>
              <#if filter_flat.dest_area??>     AND     dest_area  IN ('${filter_flat.dest_area.filterValues?join("', '")}') </#if>
              <#if filter_flat.prd_pattern_name??>   AND     prd_pattern_name IN ('${filter_flat.prd_pattern_name.filterValues?join("', '")}') </#if>
              
              <#if filter_flat.business_line??>
                  <#if filter_flat.business_line.filterValues[0] = "100">
                      AND (sub_bu_type IN ("跟团游") and is_small_group = 0)  -- AND sub_bu_type IN ("跟团游")
                  <#elseif filter_flat.business_line.filterValues[0] = "200">
                      and (sub_bu_type IN ("独立出游") or is_small_group = 1)  -- AND sub_bu_type IN ("独立出游")
                  <#elseif filter_flat.business_line.filterValues[0] = "220">
                  </#if>
              </#if>
      
              <#if filter_flat.cur_filter_str??> AND ${filter_flat.cur_filter_str.filterValues[0]} </#if>
      
              <#if group_by??>group by default_group <#list group_by as value> ,${value} </#list></#if>
      ) as t
      <#if order_by??>
          order by <#list order_by as item>${item.order_field} ${item.order_type}, </#list> 1
      </#if>
      <#if limit??>
          limit ${limit.limit_start},${limit.limit_size}
      </#if>
  - db_name:
    id: 4
    table_name: adm_prd_grp_avg_multiple_price_work_platform_df
    source_type: starrocks
    indicators:
      - indicator_name: price_rate
        indicator_name_cn: 价格倍数
        indicator_type: double
        indicator_format: double
        indicator_key: v.page.workbench.price.multiple.tips
        indicator_key_name: multiplePrice
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: price_rate_more_than_100_ctr_rate
        indicator_name_cn: 价格倍数>1.0点击PV比率
        indicator_type: double
        indicator_format: percent
        indicator_key: v.page.workbench.priceMultiple.greaterThan.tips
        indicator_key_name: multiplePriceMoreRate
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: price_rate_less_than_60_ctr_rate
        indicator_name_cn: 价格倍数<0.6点击PV比率
        indicator_type: double
        indicator_format: percent
        indicator_key: v.page.workbench.priceMultiple.lessThan.tips
        indicator_key_name: multiplePriceLessRate
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: pre1d_uncomp_pv_rate
        indicator_name_cn: 不可比价比率
        indicator_type: double
        indicator_format: percent
        indicator_key: v.page.workbench.umCompareRate.tips
        indicator_key_name: unCompareRate
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: weighted_error_rate
        indicator_name_cn: 加权异常率
        indicator_type: double
        indicator_format: percent
        indicator_key: v.page.workbench.weiAnoamlRate.tips
        indicator_key_name: weightedAnomalyRate
        indicator_is_dimension: false
        indicator_is_support_sorting: true

      - indicator_name: date
        indicator_name_cn: 日期
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: date_week
        indicator_name_cn: 日期
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: date_month
        indicator_name_cn: 日期
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false

      - indicator_name: sub_bu_type
        indicator_name_cn: 产线
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: prd_pattern_name
        indicator_name_cn: 产品形态
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
#      - indicator_name: sale_channel_name
#        indicator_name_cn: 销售渠道
#        indicator_type: string
#        indicator_format: string
#        indicator_key:
#        indicator_key_name:
#        indicator_is_dimension: true
      - indicator_name: sale_mode_name
        indicator_name_cn: 销售模式
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: vendor_id
        indicator_name_cn: 供应商ID
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: vendor_name
        indicator_name_cn: 供应商名称
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: prd_region_name
        indicator_name_cn: 产品大区
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: pm_eid
        indicator_name_cn: 业务经理
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: pm_name
        indicator_name_cn: 业务经理名称
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: dest_first_region
        indicator_name_cn: 运营大区
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: dest_city_name
        indicator_name_cn: 目的地
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: is_sec_kill
        indicator_name_cn: 是否参与秒杀
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
    template: |
      select *
      from (
              SELECT  1 as default_group
                      <#if indicators?seq_contains("price_rate")> ,sum(avg_multiple_price * (ifnull(pre1d_total_pv,0)- ifnull(pre1d_uncomp_pv,0))) / sum(ifnull(pre1d_total_pv,0)-ifnull(pre1d_uncomp_pv,0)) as price_rate </#if> -- 价格倍数
                      <#if indicators?seq_contains("price_rate_more_than_100_ctr_rate")> ,sum(pre1d_more1_pv) / sum(pre1d_total_pv) as price_rate_more_than_100_ctr_rate </#if>  -- 价格倍数>1.0点击PV比率
                      <#if indicators?seq_contains("price_rate_less_than_60_ctr_rate")> ,sum(pre1d_less06_pv) / sum(pre1d_total_pv) as price_rate_less_than_60_ctr_rate </#if>  -- 价格倍数<0.6点击PV比率
                      <#if indicators?seq_contains("pre1d_uncomp_pv_rate")> ,sum(pre1d_uncomp_pv) / sum(pre1d_total_pv) as pre1d_uncomp_pv_rate </#if> -- 不可比价比率
                      <#if indicators?seq_contains("weighted_error_rate")> ,(sum(pre1d_uncomp_pv) / sum(pre1d_total_pv)) *0.2 + (sum(pre1d_more1_pv) / sum(pre1d_total_pv)) *0.8 as weighted_error_rate </#if> -- 加权异常率
        
                      <#if group_by??>
                          <#list group_by as value>
                              <#if value = "date"> ,date_format(to_date(view_date), '%Y-%m-%d') as date
                              <#elseif value = "date_week"> ,date_format(date_sub(to_date(view_date), interval (dayofweek(to_date(view_date)) + 5) % 7  day), '%Y-%m-%d') as date_week
                              <#elseif value = "date_month"> ,date_format(date_sub(to_date(view_date), interval dayofmonth(to_date(view_date)) -1  day), '%Y-%m-%d') as date_month
                              <#elseif value = "local_pm_name"> ,"" as local_pm_name
                              <#elseif value = "pm_name"> ,"" as pm_name
                              <#else>,${value}
                              </#if>
                          </#list>
                      </#if>
              FROM
                      adm_prd_grp_avg_multiple_price_work_platform_df
              WHERE   partition_d = (select max(partition_d) from adm_prd_grp_avg_multiple_price_work_platform_df)
              <#if filter_flat.partition_d??>   AND     partition_d = '${filter_flat.partition_d.filterValues[1]}' </#if>
              <#if filter_flat.date??>          AND     view_date between '${filter_flat.date.filterValues[0]}' and '${filter_flat.date.filterValues[1]}' </#if>
        
              <#if filter_flat.emp_codes??>     AND     (pm_eid IN ('${filter_flat.emp_codes.filterValues?join("', '")}') OR local_pm_eid IN ('${filter_flat.emp_codes.filterValues?join("', '")}') ) </#if>
              <#if filter_flat.dest_area??>     AND     dest_area  IN ('${filter_flat.dest_area.filterValues?join("', '")}') </#if>
              <#if filter_flat.prd_pattern_name??>   AND     prd_pattern_name IN ('${filter_flat.prd_pattern_name.filterValues?join("', '")}') </#if>
              
              <#if filter_flat.business_line??>
                  <#if filter_flat.business_line.filterValues[0] = "100">
                      AND (sub_bu_type IN ("跟团游") and is_small_group = 0)  -- AND sub_bu_type IN ("跟团游")
                  <#elseif filter_flat.business_line.filterValues[0] = "200">
                      and (sub_bu_type IN ("独立出游") or is_small_group = 1)  -- AND sub_bu_type IN ("独立出游")
                  <#elseif filter_flat.business_line.filterValues[0] = "220">
                  </#if>
              </#if>
      
              <#if filter_flat.cur_filter_str??> AND ${filter_flat.cur_filter_str.filterValues[0]} </#if>
      
              <#if group_by??>group by default_group <#list group_by as value> ,${value} </#list></#if>
       ) as t
      <#if order_by??>
          order by <#list order_by as item>${item.order_field} ${item.order_type}, </#list> 1
      </#if>
      <#if limit??>
          limit ${limit.limit_start},${limit.limit_size}
      </#if>

  - db_name:
    id: 5
    table_name: adm_prd_grp_product_score_df
    source_type: starrocks
    indicators:
      - indicator_name: weighted_platform_score
        indicator_name_cn: 加权平台信息分
        indicator_type: double
        indicator_format: double
        indicator_key: v.page.workbench.weighted.platform.information.tip
        indicator_key_name: weightedPlatformInfoScore
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: weighted_product_score
        indicator_name_cn: 加权商品信息分
        indicator_type: double
        indicator_format: double
        indicator_key: v.page.workbench.weighted.product.information.division.tip
        indicator_key_name: weightedPrdInfoScore
        indicator_is_dimension: false
        indicator_is_support_sorting: true

      - indicator_name: date
        indicator_name_cn: 日期
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: date_week
        indicator_name_cn: 日期
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: date_month
        indicator_name_cn: 日期
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false

      - indicator_name: sub_bu_type
        indicator_name_cn: 产线
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: prd_pattern_name
        indicator_name_cn: 产品形态
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: sale_channel_name
        indicator_name_cn: 销售渠道
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: sale_mode_name
        indicator_name_cn: 销售模式
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: vendor_id
        indicator_name_cn: 供应商ID
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: vendor_name
        indicator_name_cn: 供应商名称
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: prd_region_name
        indicator_name_cn: 产品大区
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: pm_eid
        indicator_name_cn: 业务经理
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: pm_name
        indicator_name_cn: 业务经理名称
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: dest_first_region
        indicator_name_cn: 运营大区
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: dest_city_name
        indicator_name_cn: 目的地
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: is_sec_kill
        indicator_name_cn: 是否参与秒杀
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
    template: |
      select *
      from (
              SELECT  1 as default_group
                      <#if indicators?seq_contains("weighted_platform_score")> ,SUM(dtl_uv_1y*platform_score) / sum(dtl_uv_1y) as weighted_platform_score </#if> -- 加权平台信息分
                      <#if indicators?seq_contains("weighted_product_score")> ,SUM(dtl_uv_1y*product_score) / sum(dtl_uv_1y) as weighted_product_score </#if> -- 加权商品信息分
                      <#if group_by??>
                          <#list group_by as value>
                              <#if value = "date"> ,date_format(to_date(etl_date), '%Y-%m-%d') as date
                              <#elseif value = "date_week"> ,date_format(date_sub(to_date(etl_date), interval (dayofweek(to_date(etl_date)) + 5) % 7  day), '%Y-%m-%d') as date_week
                              <#elseif value = "date_month"> ,date_format(date_sub(to_date(etl_date), interval dayofmonth(to_date(etl_date)) -1  day), '%Y-%m-%d') as date_month
                              <#elseif value = "local_pm_name"> ,"" as local_pm_name
                              <#elseif value = "pm_name"> ,"" as pm_name
                              <#else> ,${value}
                              </#if>
                          </#list>
                      </#if>
              FROM
                      adm_prd_grp_product_score_df
              WHERE   partition_d = (select max(partition_d) from adm_prd_grp_product_score_df)
              <#if filter_flat.partition_d??>   AND     partition_d = '${filter_flat.partition_d.filterValues[1]}' </#if>
              <#if filter_flat.date??>          AND     etl_date between '${filter_flat.date.filterValues[0]}' and '${filter_flat.date.filterValues[1]}' </#if>
              
              <#if filter_flat.emp_codes??>     AND     (pm_eid IN ('${filter_flat.emp_codes.filterValues?join("', '")}') OR local_pmeid IN ('${filter_flat.emp_codes.filterValues?join("', '")}') ) </#if>
              <#if filter_flat.dest_area??>     AND     dest_area  IN ('${filter_flat.dest_area.filterValues?join("', '")}') </#if>
              <#if filter_flat.prd_pattern_name??>   AND     prd_pattern_name IN ('${filter_flat.prd_pattern_name.filterValues?join("', '")}') </#if>
              
              <#if filter_flat.business_line??>
                  <#if filter_flat.business_line.filterValues[0] = "100">
                      AND (sub_bu_type IN ("跟团游") and is_small_group = 0)  -- AND sub_bu_type IN ("跟团游")
                  <#elseif filter_flat.business_line.filterValues[0] = "200">
                      and (sub_bu_type IN ("独立出游") or is_small_group = 1)  -- AND sub_bu_type IN ("独立出游")
                  <#elseif filter_flat.business_line.filterValues[0] = "220">
                  </#if>
              </#if>
      
              <#if filter_flat.cur_filter_str??> AND ${filter_flat.cur_filter_str.filterValues[0]} </#if>
      
              <#if group_by??>group by default_group <#list group_by as value> ,${value} </#list></#if>
      ) as t
      <#if order_by??>
          order by <#list order_by as item>${item.order_field} ${item.order_type}, </#list> 1
      </#if>
      <#if limit??>
          limit ${limit.limit_start},${limit.limit_size}
      </#if>

  - db_name:
    id: 6
    table_name: cdm_sev_grp_platform_self_srv_summary_df
    source_type: starrocks
    indicators:
      - indicator_name: self_sev_rate
        indicator_name_cn: 自服务覆盖率
        indicator_type: double
        indicator_format: percent
        indicator_key: v.page.workbench.service.coverage.tips
        indicator_key_name: selfServiceCoverageRate
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: self_sev_cvr
        indicator_name_cn: 自服务转化率
        indicator_type: double
        indicator_format: percent
        indicator_key: v.page.workbench.service.conversion.tips
        indicator_key_name: selfServiceConversionRate
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: reply_120s_rate
        indicator_name_cn: 120s及时回复率
        indicator_type: double
        indicator_format: percent
        indicator_key: v.page.workbench.timelyRespRate.tips
        indicator_key_name: timelyResponseRate
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: tor_fail_distr_cnsl_rate
        indicator_name_cn: 商家会话分配失败率
        indicator_type: double
        indicator_format: percent
        indicator_key: v.page.workbench.dispFail.tips
        indicator_key_name: dispFailRate
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: op_avg_score_per_session
        indicator_name_cn: 商家会话点评均分
        indicator_type: double
        indicator_format: double
        indicator_key: v.page.workbench.avgTranRevScore.tips
        indicator_key_name: avgTranslationReviewScore
        indicator_is_dimension: false
        indicator_is_support_sorting: true


      - indicator_name: date
        indicator_name_cn: 日期
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: date_week
        indicator_name_cn: 日期
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: date_month
        indicator_name_cn: 日期
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false

      - indicator_name: sub_bu_type
        indicator_name_cn: 产线
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: prd_pattern_name
        indicator_name_cn: 产品形态
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: sale_channel_name
        indicator_name_cn: 销售渠道
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: sale_mode_name
        indicator_name_cn: 销售模式
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: vendor_id
        indicator_name_cn: 供应商ID
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: vendor_name
        indicator_name_cn: 供应商名称
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: prd_region_name
        indicator_name_cn: 产品大区
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: pm_eid
        indicator_name_cn: 业务经理
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: pm_name
        indicator_name_cn: 业务经理名称
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: dest_first_region
        indicator_name_cn: 运营大区
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: dest_city_name
        indicator_name_cn: 目的地
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: is_sec_kill
        indicator_name_cn: 是否参与秒杀
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false

    template: |
      select * 
      from (
              SELECT  1 as default_group
                      <#if indicators?seq_contains("self_sev_rate")> ,count(distinct self_sev_uid) / count(distinct case when second_session_type in( '商户自服务会话' ,'携程服务会话') then sev_uid end) as self_sev_rate </#if> -- 自服务覆盖率
                      <#if indicators?seq_contains("self_sev_cvr")> ,count(distinct suc_ord_uid) / count(distinct uid) as self_sev_cvr </#if> -- 自服务转化率
                      <#if indicators?seq_contains("reply_120s_rate")> ,sum( reply_120s_cnt) / sum(total_message_cnt) as reply_120s_rate </#if> -- 120s及时回复率
                      <#if indicators?seq_contains("tor_fail_distr_cnsl_rate")> ,count(distinct tor_fail_distr_cnsl_sid) / count(distinct session_id) as tor_fail_distr_cnsl_rate </#if> -- 商家会话分配失败率
                      <#if indicators?seq_contains("op_avg_score_per_session")> ,sum(op_avg_score) / count(distinct session_evaluate_id) as op_avg_score_per_session </#if> -- 商家会话点评均分
        
                      <#if group_by??>
                          <#list group_by as value>
                              <#if value = "date"> ,date_format(to_date(chat_create_date), '%Y-%m-%d') as date 
                              <#elseif value = "date_week"> ,date_format(date_sub(to_date(chat_create_date), interval (dayofweek(to_date(chat_create_date)) + 5) % 7  day), '%Y-%m-%d') as date_week
                              <#elseif value = "date_month"> ,date_format(date_sub(to_date(chat_create_date), interval dayofmonth(to_date(chat_create_date)) -1  day), '%Y-%m-%d') as date_month
                              <#elseif value = "local_pm_name"> ,"" as local_pm_name
                              <#elseif value = "pm_name"> ,"" as pm_name
                              <#else> ,${value}
                              </#if>
                          </#list>
                      </#if>
              FROM
                      cdm_sev_grp_platform_self_srv_summary_df
              WHERE   partition_d = (select max(partition_d) from cdm_sev_grp_platform_self_srv_summary_df)
              <#if filter_flat.partition_d??>       AND     partition_d = '${filter_flat.partition_d.filterValues[1]}' </#if>
              <#if filter_flat.date??>              AND     chat_create_date between '${filter_flat.date.filterValues[0]}' and '${filter_flat.date.filterValues[1]}' </#if>
        
              <#if filter_flat.emp_codes??>         AND     (pm_eid IN ('${filter_flat.emp_codes.filterValues?join("', '")}') OR local_pm_eid IN ('${filter_flat.emp_codes.filterValues?join("', '")}') ) </#if>
              <#if filter_flat.dest_area??>         AND     dest_area  IN ('${filter_flat.dest_area.filterValues?join("', '")}') </#if>
              <#if filter_flat.prd_pattern_name??>       AND     prd_pattern_name IN ('${filter_flat.prd_pattern_name.filterValues?join("', '")}') </#if>
              
              <#if filter_flat.business_line??>
                  <#if filter_flat.business_line.filterValues[0] = "100">
                      AND (sub_bu_type IN ("跟团游") and is_small_group = 0)  -- AND sub_bu_type IN ("跟团游")
                  <#elseif filter_flat.business_line.filterValues[0] = "200">
                      and (sub_bu_type IN ("独立出游") or is_small_group = 1)  -- AND sub_bu_type IN ("独立出游")
                  <#elseif filter_flat.business_line.filterValues[0] = "220">
                  </#if>
              </#if>
      
              <#if filter_flat.cur_filter_str??> AND ${filter_flat.cur_filter_str.filterValues[0]} </#if>
              
              <#if group_by??>group by default_group <#list group_by as value> ,${value} </#list></#if>
      
      ) as t
      <#if order_by??>
          order by <#list order_by as item>${item.order_field} ${item.order_type}, </#list> 1
      </#if>
      <#if limit??>
          limit ${limit.limit_start},${limit.limit_size}
      </#if>

  - db_name:
    id: 7
    table_name: adm_ord_grp_work_platform_prdt_df,edw_log_grp_cpr_platform_flow_cr_di
    source_type: starrocks
    indicators:
      - indicator_name: self_suc_income_per_uv
        indicator_name_cn: 自营商品
        indicator_type: long
        indicator_format: long
        indicator_key: v.page.workbench.weighted.selfOprSingleUVValPrd.tip
        indicator_key_name: selfOprSingleUVValPrd
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: agent_suc_income_per_uv
        indicator_name_cn: 代理商品
        indicator_type: long
        indicator_format: long
        indicator_key: v.page.workbench.weighted.product.agentSingleUVValPrd.tip
        indicator_key_name: agentSingleUVValPrd
        indicator_is_dimension: false
        indicator_is_support_sorting: true

      - indicator_name: date
        indicator_name_cn: 日期
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: date_week
        indicator_name_cn: 日期
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: date_month
        indicator_name_cn: 日期
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false

      - indicator_name: sub_bu_type
        indicator_name_cn: 产线
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: prd_pattern_name
        indicator_name_cn: 产品形态
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: dest_domain
        indicator_name_cn: 区域
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: sale_mode_name
        indicator_name_cn: 销售模式
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: tour_region_type
        indicator_name_cn: 客源地/目的地
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: prd_region_name
        indicator_name_cn: 产品大区
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: pm_eid
        indicator_name_cn: 业务经理
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: pm_name
        indicator_name_cn: 业务经理名称
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: dest_first_region
        indicator_name_cn: 运营大区
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: dest_city_name
        indicator_name_cn: 目的地
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: vendor_id
        indicator_name_cn: 供应商ID
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: vendor_name
        indicator_name_cn: 供应商名称
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: is_sec_kill
        indicator_name_cn: 是否参与秒杀
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: local_pm_eid
        indicator_name_cn: 驻地业务经理
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: local_pm_name
        indicator_name_cn: 驻地业务经理名称
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: l1_route_name
        indicator_name_cn: 线路玩法（L1）
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: prd_level_name
        indicator_name_cn: 钻级
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: pk_tour_line
        indicator_name_cn: 线路玩法·钻级·天数·是否拼小团
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: cpr_dest_area
        indicator_name_cn: 竞争圈
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false

    template: |
      select  
              *
      from (
              select  1 as default_group_t
                      <#if indicators?seq_contains("self_suc_income_per_uv")> ,sum(self_suc_income) / sum(self_uv) as self_suc_income_per_uv </#if> -- 自营商品
                      <#if indicators?seq_contains("agent_suc_income_per_uv")> ,sum(agent_suc_income) / sum(agent_uv) as agent_suc_income_per_uv </#if> -- 代理商品
              
                      <#if group_by??><#list group_by as value> ,${value} </#list></#if>
              from (
                      SELECT
                              1 as default_group,
                              sum(case when bu_sale_mode in ('代理') then suc_income else null end) as self_suc_income,
                              sum(case when bu_sale_mode in ('合同自营', '双好自营') then suc_income else null end) as agent_suc_income,
                              0 as self_uv,
                              0 as agent_uv
                              <#if group_by??>
                                  <#list group_by as value>
                                      <#if value = "date"> ,date_format(to_date(dep_date), '%Y-%m-%d') as date 
                                      <#elseif value = "date_week"> ,date_format(date_sub(to_date(dep_date), interval (dayofweek(to_date(dep_date)) + 5) % 7  day), '%Y-%m-%d') as date_week
                                      <#elseif value = "date_month"> ,date_format(date_sub(to_date(dep_date), interval dayofmonth(to_date(dep_date)) -1  day), '%Y-%m-%d') as date_month
                                      <#elseif value = "local_pm_name"> ,"" as local_pm_name
                                      <#elseif value = "pm_name"> ,"" as pm_name
                                      <#else> ,${value}
                                      </#if>
                                  </#list>
                              </#if>
                      FROM
                              adm_ord_grp_work_platform_prdt_df
                      WHERE   partition_d = (select max(partition_d) from adm_ord_grp_work_platform_prdt_df)
                      <#if filter_flat.partition_d??>   AND     partition_d = '${filter_flat.partition_d.filterValues[1]}' </#if>
                      <#if filter_flat.date??>          AND     dep_date between '${filter_flat.date.filterValues[0]}' AND '${filter_flat.date.filterValues[1]}' </#if>
              
                      <#if filter_flat.emp_codes??>     AND     (pm_eid IN ('${filter_flat.emp_codes.filterValues?join("', '")}') OR local_pmeid IN ('${filter_flat.emp_codes.filterValues?join("', '")}') ) </#if>
                      <#if filter_flat.dest_area??>     AND     dest_area  IN ('${filter_flat.dest_area.filterValues?join("', '")}') </#if>
                      <#if filter_flat.prd_pattern_name??>   AND     prd_pattern_name IN ('${filter_flat.prd_pattern_name.filterValues?join("', '")}') </#if>
              
                      <#if filter_flat.business_line??>
                          <#if filter_flat.business_line.filterValues[0] = "100">
                              AND (sub_bu_type IN ("跟团游") and is_small_group = 0)  -- AND sub_bu_type IN ("跟团游")
                          <#elseif filter_flat.business_line.filterValues[0] = "200">
                              and (sub_bu_type IN ("独立出游") or is_small_group = 1)  -- AND sub_bu_type IN ("独立出游")
                          <#elseif filter_flat.business_line.filterValues[0] = "220">
                          </#if>
                      </#if>
      
                      <#if filter_flat.cur_filter_str??> AND ${filter_flat.cur_filter_str.filterValues[0]} </#if>
      
                      <#if group_by??>group by default_group <#list group_by as value> ,${value} </#list></#if>
              
                      union all
              
                      SELECT
                              1 as default_group,
                              0 as self_suc_income,
                              0 as agent_suc_income,
                              count(distinct case when BU_SALE_MODE in ('合同自营', '双好自营') then  concat(vid,partition_d) else null end) as self_uv,
                              count(distinct case when BU_SALE_MODE in ('代理') then  concat(vid,partition_d) else null end) as agent_uv
                              <#if group_by??>
                                  <#list group_by as value>
                                      <#if value = "date"> ,date_format(to_date(partition_d), '%Y-%m-%d') as date
                                      <#elseif value = "date_week"> ,date_format(date_sub(to_date(partition_d), interval (dayofweek(to_date(partition_d)) + 5) % 7  day), '%Y-%m-%d') as date_week
                                      <#elseif value = "date_month"> ,date_format(date_sub(to_date(partition_d), interval dayofmonth(to_date(partition_d)) -1  day), '%Y-%m-%d') as date_month
                                      <#elseif value = "local_pm_name"> ,"" as local_pm_name
                                      <#elseif value = "pm_name"> ,"" as pm_name
                                      <#else> ,${value}
                                      </#if>
                                  </#list>
                              </#if>
                      FROM    edw_log_grp_cpr_platform_flow_cr_di
                      WHERE   1 = 1
                      <#if filter_flat.date??>          AND     partition_d between '${filter_flat.date.filterValues[0]}' and '${filter_flat.date.filterValues[1]}' </#if>
              
                      <#if filter_flat.emp_codes??>     AND     (pm_eid IN ('${filter_flat.emp_codes.filterValues?join("', '")}') OR local_pm_eid IN ('${filter_flat.emp_codes.filterValues?join("', '")}') ) </#if>
                      <#if filter_flat.dest_area??>     AND     dest_area  IN ('${filter_flat.dest_area.filterValues?join("', '")}') </#if>
                      <#if filter_flat.prd_pattern_name??>   AND     prd_pattern_name IN ('${filter_flat.prd_pattern_name.filterValues?join("', '")}') </#if>
              
                      <#if filter_flat.business_line??>
                          <#if filter_flat.business_line.filterValues[0] = "100">
                              AND (sub_bu_type IN ("跟团游") and is_small_group = 0)  -- AND sub_bu_type IN ("跟团游")
                          <#elseif filter_flat.business_line.filterValues[0] = "200">
                              and (sub_bu_type IN ("独立出游") or is_small_group = 1)  -- AND sub_bu_type IN ("独立出游")
                          <#elseif filter_flat.business_line.filterValues[0] = "220">
                          </#if>
                      </#if>
      
                      <#if filter_flat.cur_filter_str??> AND ${filter_flat.cur_filter_str.filterValues[0]} </#if>
      
                      <#if group_by??>group by default_group <#list group_by as value> ,${value} </#list></#if>
              ) as t
              <#if group_by??>group by default_group <#list group_by as value> ,${value} </#list></#if>
      
      ) as t
      <#if order_by??>
          order by <#list order_by as item>${item.order_field} ${item.order_type}, </#list> 1
      </#if>
      <#if limit??>
          limit ${limit.limit_start},${limit.limit_size}
      </#if>

  - db_name:
    id: 8
    table_name: adm_prd_grp_product_total_work_platform_df
    source_type: starrocks
    indicators:
      - indicator_name: self_parent_prd_cnt
        indicator_name_cn: 自营母商品数量
        indicator_type: long
        indicator_format: long
        indicator_key: v.page.workbench.weighted.selfOprParentPrdCount.tip
        indicator_key_name: selfOprParentPrdCount
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: oln_self_parent_prd_cnt
        indicator_name_cn: 有效在线自营母商品数量
        indicator_type: long
        indicator_format: long
        indicator_key: v.page.workbench.weighted.product.activeSelfOprParentPrdCount.tip
        indicator_key_name: activeSelfOprParentPrdCount
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: oln_self_parent_prd_rate
        indicator_name_cn: 有效在线上线自营母商品比率
        indicator_type: double
        indicator_format: percent
        indicator_key: v.page.workbench.weighted.activeSelfOprParentPrdRate.tip
        indicator_key_name: activeSelfOprParentPrdRate
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: pre30d_tdate_density_avg
        indicator_name_cn: 近30天的班期密度
        indicator_type: double
        indicator_format: double
        indicator_key: v.page.workbench.weighted.product.srvFrequencyInLst30d.tip
        indicator_key_name: srvFrequencyInLst30d
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: self_cpr_ved_area_cnt
        indicator_name_cn: 有自营商品的好商家数量
        indicator_type: long
        indicator_format: long
        indicator_key: v.page.workbench.weighted.platform.highQuaVendorCountWithSelfOprPrd.tip
        indicator_key_name: highQuaVendorCountWithSelfOprPrd
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: self_no_stard_ved_area_cnt
        indicator_name_cn: 有自营商品但好商家考核不达标的好商家数量
        indicator_type: long
        indicator_format: long
        indicator_key: v.page.workbench.weighted.product.highQuaVendorCountWithSelfOprPrdFailToMeet.division.tip
        indicator_key_name: highQuaVendorCountWithSelfOprPrdFailToMeet
        indicator_is_dimension: false
        indicator_is_support_sorting: true

      - indicator_name: date
        indicator_name_cn: 日期
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: date_week
        indicator_name_cn: 日期
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: date_month
        indicator_name_cn: 日期
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false

      - indicator_name: sub_bu_type
        indicator_name_cn: 产线
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: prd_pattern_name
        indicator_name_cn: 产品形态
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: sale_mode_name
        indicator_name_cn: 销售模式
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: vendor_id
        indicator_name_cn: 供应商ID
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: vendor_name
        indicator_name_cn: 供应商名称
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: prd_region_name
        indicator_name_cn: 产品大区
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: pm_eid
        indicator_name_cn: 业务经理
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: pm_name
        indicator_name_cn: 业务经理名称
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: dest_first_region
        indicator_name_cn: 运营大区
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: dest_city_name
        indicator_name_cn: 目的地
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: is_sec_kill
        indicator_name_cn: 是否参与秒杀
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: l1_route_name
        indicator_name_cn: 线路玩法（L1）
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: prd_level_name
        indicator_name_cn: 钻级
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: pk_tour_line
        indicator_name_cn: 线路玩法·钻级·天数·是否拼小团
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: cpr_dest_area
        indicator_name_cn: 竞争圈
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: dest_domain
        indicator_name_cn: 区域
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false
      - indicator_name: tour_region_type
        indicator_name_cn: 客源地/目的地
        indicator_type: string
        indicator_format: string
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: true
        indicator_is_support_sorting: false


    template: |
      select 
              *
      from (
              SELECT  1 as default_group
                      <#if indicators?seq_contains("self_parent_prd_cnt")> ,count(distinct self_parent_prd_id) as self_parent_prd_cnt </#if> -- 自营母商品数量
                      <#if indicators?seq_contains("oln_self_parent_prd_cnt")> ,count(distinct oln_self_parent_prd_id) as oln_self_parent_prd_cnt </#if> -- 有效在线自营母商品数量
                      <#if indicators?seq_contains("oln_self_parent_prd_rate")> ,count(distinct oln_self_parent_prd_id) / count(distinct self_parent_prd_id) as oln_self_parent_prd_rate </#if> -- 有效在线上线自营母商品比率
                      <#if indicators?seq_contains("pre30d_tdate_density_avg")> ,avg(pre30d_tdate_density) as pre30d_tdate_density_avg </#if> -- 近30天的班期密度
                      <#if indicators?seq_contains("self_cpr_ved_area_cnt")> ,count(distinct self_cpr_ved_area) as self_cpr_ved_area_cnt </#if> -- 有自营商品的好商家数量
                      <#if indicators?seq_contains("self_no_stard_ved_area_cnt")> ,count(distinct self_no_stard_ved_area) as self_no_stard_ved_area_cnt </#if>  -- 有自营商品但好商家考核不达标的好商家数量
                      <#if group_by??>
                          <#list group_by as value>
                              <#if value = "date"> ,date_format(to_date(etl_date), '%Y-%m-%d') as date
                              <#elseif value = "date_week"> ,date_format(date_sub(to_date(etl_date), interval (dayofweek(to_date(etl_date)) + 5) % 7  day), '%Y-%m-%d') as date_week
                              <#elseif value = "date_month"> ,date_format(date_sub(to_date(etl_date), interval dayofmonth(to_date(etl_date)) -1  day), '%Y-%m-%d') as date_month
                              <#elseif value = "local_pm_name"> , "" as local_pm_name
                              <#elseif value = "pm_name"> ,"" as pm_name
                              <#else> ,${value}
                              </#if>
                          </#list>
                      </#if>
              FROM    
                      adm_prd_grp_product_total_work_platform_df
              WHERE   1 = 1
              <#if filter_flat.partition_d??>   AND     partition_d = '${filter_flat.partition_d.filterValues[1]}' </#if>
              <#if filter_flat.date??>          AND     partition_d between '${filter_flat.date.filterValues[0]}' and '${filter_flat.date.filterValues[1]}' </#if>
              
              <#if filter_flat.emp_codes??>     AND     (pm_eid IN ('${filter_flat.emp_codes.filterValues?join("', '")}') OR local_pmeid IN ('${filter_flat.emp_codes.filterValues?join("', '")}') ) </#if>
              <#if filter_flat.dest_area??>     AND     dest_area  IN ('${filter_flat.dest_area.filterValues?join("', '")}') </#if>
              <#if filter_flat.prd_pattern_name??>   AND     prd_pattern_name IN ('${filter_flat.prd_pattern_name.filterValues?join("', '")}') </#if>
              
              <#if filter_flat.business_line??>
                  <#if filter_flat.business_line.filterValues[0] = "100">
                      AND (sub_bu_type IN ("跟团游") and is_small_group = 0)  -- AND sub_bu_type IN ("跟团游")
                  <#elseif filter_flat.business_line.filterValues[0] = "200">
                      and (sub_bu_type IN ("独立出游") or is_small_group = 1)  -- AND sub_bu_type IN ("独立出游")
                  <#elseif filter_flat.business_line.filterValues[0] = "220">
                  </#if>
              </#if>
      
              <#if filter_flat.cur_filter_str??> AND ${filter_flat.cur_filter_str.filterValues[0]} </#if>
      
              <#if group_by??>group by default_group <#list group_by as value> ,${value} </#list></#if>
      
      ) as t
      <#if order_by??>
          order by <#list order_by as item>${item.order_field} ${item.order_type}, </#list> 1
      </#if>
      <#if limit??>
          limit ${limit.limit_start},${limit.limit_size}
      </#if>

  - db_name:
    id: 11
    table_name: adm_ord_grp_work_platform_prdt_df
    source_type: starrocks
    indicators:
      - indicator_name: good_guide_sev_ord_cnt_rate_rank
        indicator_name_cn: 金牌导游真实派遣率排名
        indicator_type: long
        indicator_format: long
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: false
        indicator_is_support_sorting: false
      - indicator_name: good_guide_sev_ord_cnt_rate_rank_max
        indicator_name_cn: 金牌导游真实派遣率排名最大值
        indicator_type: long
        indicator_format: long
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: false
        indicator_is_support_sorting: false
      - indicator_name: good_guide_sev_ord_cnt_rate_rank_percentile
        indicator_name_cn: 金牌导游真实派遣率排名百分位
        indicator_type: double
        indicator_format: percent
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: false
        indicator_is_support_sorting: false

    template: |-
      select 
              *,
              good_guide_sev_ord_cnt_rate_rank / good_guide_sev_ord_cnt_rate_rank_max as good_guide_sev_ord_cnt_rate_rank_percentile
      from (
              SELECT 
                      max(case when pm_eid='<#if filter_flat.org_emp_code??>${filter_flat.org_emp_code.filterValues[0]}</#if>' then good_sev_rank else null end) as good_guide_sev_ord_cnt_rate_rank,
                      max(good_sev_rank) as good_guide_sev_ord_cnt_rate_rank_max
              FROM 
                      adm_ord_grp_work_platform_prdt_df
              WHERE 
                      partition_d = (select max(partition_d) from adm_ord_grp_work_platform_prdt_df) 
                      AND firstday_of_week = date_sub(to_date('${filter_flat.date.filterValues[1]}'), INTERVAL ((dayofweek(to_date('${filter_flat.date.filterValues[1]}'))) + 5) % 7 DAY) 
                      AND lastday_of_week = date_add(date_sub(to_date('${filter_flat.date.filterValues[1]}'), INTERVAL ((dayofweek(to_date('${filter_flat.date.filterValues[1]}'))) + 5) % 7 DAY), interval 6 DAY)
      ) as t
      

  - db_name:
    id: 12
    table_name: adm_sev_grp_platform_nps_rank_df
    source_type: starrocks
    indicators:
      - indicator_name: fitting_nps_foreign_rank
        indicator_name_cn: 当月拟合NPS国外排名
        indicator_type: long
        indicator_format: long
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: false
        indicator_is_support_sorting: false
      - indicator_name: fitting_nps_foreign_rank_max
        indicator_name_cn: 当月拟合NPS国外排名
        indicator_type: long
        indicator_format: long
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: false
        indicator_is_support_sorting: false
      - indicator_name: fitting_nps_foreign_rank_percentile
        indicator_name_cn: 当月拟合NPS国外排名百分位
        indicator_type: double
        indicator_format: percent
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: false
        indicator_is_support_sorting: false
      - indicator_name: fitting_nps_domestic_rank
        indicator_name_cn: 当月拟合NPS国内排名
        indicator_type: long
        indicator_format: long
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: false
        indicator_is_support_sorting: false
      - indicator_name: fitting_nps_domestic_rank_max
        indicator_name_cn: 当月拟合NPS国内排名最大值
        indicator_type: long
        indicator_format: long
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: false
        indicator_is_support_sorting: false
      - indicator_name: fitting_nps_domestic_rank_percentile
        indicator_name_cn: 当月拟合NPS国内排名百分位
        indicator_type: double
        indicator_format: percent
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: false
        indicator_is_support_sorting: false

    template: |-
      select 
              * ,
              fitting_nps_foreign_rank / fitting_nps_foreign_rank_max as fitting_nps_foreign_rank_percentile,
              fitting_nps_domestic_rank / fitting_nps_domestic_rank_max as fitting_nps_domestic_rank_percentile
      from
      (
              SELECT
                      max(case when area = '国内' and business_team = '<#if filter_flat.org_emp_code??>${filter_flat.org_emp_code.filterValues[0]}</#if>' then  fitting_nps_rank else null end) as fitting_nps_foreign_rank,
                      max(case when area = '国内' then fitting_nps_rank else null end) as fitting_nps_foreign_rank_max,
                      max(case when area = '海外' and business_team = '<#if filter_flat.org_emp_code??>${filter_flat.org_emp_code.filterValues[0]}</#if>' then  fitting_nps_rank else null end) as fitting_nps_domestic_rank,
                      max(case when area = '海外' then fitting_nps_rank else null end) as fitting_nps_domestic_rank_max
              FROM 
                      adm_sev_grp_platform_nps_rank_df
              WHERE 
                      partition_d = (select max(partition_d) from adm_sev_grp_platform_nps_rank_df)
      
                      AND stat_quarter = 
                            case 
                                when substr('${filter_flat.date.filterValues[1]}', 6, 2) in ('01', '02', '03') then 'Q1'
                                when substr('${filter_flat.date.filterValues[1]}', 6, 2) in ('04', '05', '06') then 'Q2'
                                when substr('${filter_flat.date.filterValues[1]}', 6, 2) in ('07', '08', '09') then 'Q3'
                                when substr('${filter_flat.date.filterValues[1]}', 6, 2) in ('10', '11', '12') then 'Q4'
                            end
      
                      <#if filter_flat.business_line??>
                          <#if filter_flat.business_line.filterValues[0] = "100">
                              AND (sub_bu_type IN ("跟团游") and is_small_group = 0)  -- AND sub_bu_type IN ("跟团游")
                          <#elseif filter_flat.business_line.filterValues[0] = "200">
                              and (sub_bu_type IN ("独立出游") or is_small_group = 1)  -- AND sub_bu_type IN ("独立出游")
                          <#elseif filter_flat.business_line.filterValues[0] = "220">
                          </#if>
                      </#if>
      ) as t


  - db_name:
    id: 13
    table_name: adm_sev_grp_platform_self_srv_rank_df
    source_type: starrocks
    indicators:
      - indicator_name: self_sev_rate_domestic_rank
        indicator_name_cn: 自服务覆盖率国内排名
        indicator_type: long
        indicator_format: long
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: false
        indicator_is_support_sorting: false
      - indicator_name: self_sev_rate_domestic_rank_max
        indicator_name_cn: 自服务覆盖率国内排名最大值
        indicator_type: long
        indicator_format: long
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: false
        indicator_is_support_sorting: false
      - indicator_name: self_sev_rate_domestic_rank_percentile
        indicator_name_cn: 自服务覆盖率国内排名百分位
        indicator_type: long
        indicator_format: long
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: false
        indicator_is_support_sorting: false
      - indicator_name: self_sev_rate_foreign_rank
        indicator_name_cn: 自服务覆盖率国外排名
        indicator_type: long
        indicator_format: long
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: false
        indicator_is_support_sorting: false
      - indicator_name: self_sev_rate_foreign_rank_max
        indicator_name_cn: 自服务覆盖率国外排名最大值
        indicator_type: long
        indicator_format: long
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: false
        indicator_is_support_sorting: false
      - indicator_name: self_sev_rate_foreign_rank_percentile
        indicator_name_cn: 自服务覆盖率国外排名百分位
        indicator_type: long
        indicator_format: long
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: false
        indicator_is_support_sorting: false

    template: |-
      select 
              *,
              self_sev_rate_domestic_rank / self_sev_rate_domestic_rank_max as self_sev_rate_domestic_rank_percentile,
              self_sev_rate_foreign_rank / self_sev_rate_foreign_rank_max as self_sev_rate_foreign_rank_percentile
      from (
              SELECT
                      max(case when area = '国内' and business_team = '<#if filter_flat.org_emp_code??>${filter_flat.org_emp_code.filterValues[0]}</#if>' then self_srv_rank else null end ) as self_sev_rate_domestic_rank,
                      max(case when area = '国内' then self_srv_rank else null end ) as self_sev_rate_domestic_rank_max,
                      max(case when area = '海外' and business_team = '<#if filter_flat.org_emp_code??>${filter_flat.org_emp_code.filterValues[0]}</#if>' then self_srv_rank else null end ) as self_sev_rate_foreign_rank,
                      max(case when area = '海外' then self_srv_rank else null end ) as self_sev_rate_foreign_rank_max
              FROM 
                      adm_sev_grp_platform_self_srv_rank_df
              WHERE 
                      partition_d = (select max(partition_d) from adm_sev_grp_platform_self_srv_rank_df) 
                      AND stat_quarter =                             
                            case 
                                when substr('${filter_flat.date.filterValues[1]}', 6, 2) in ('01', '02', '03') then 'Q1'
                                when substr('${filter_flat.date.filterValues[1]}', 6, 2) in ('04', '05', '06') then 'Q2'
                                when substr('${filter_flat.date.filterValues[1]}', 6, 2) in ('07', '08', '09') then 'Q3'
                                when substr('${filter_flat.date.filterValues[1]}', 6, 2) in ('10', '11', '12') then 'Q4'
                            end
      ) as t


  - db_name:
    id: 14
    table_name: adm_prd_grp_avg_multiple_price_work_platform_df
    source_type: starrocks
    indicators:
      - indicator_name: price_rate_rank
        indicator_name_cn: 价格倍数排名
        indicator_type: long
        indicator_format: long
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: price_rate_rank_max
        indicator_name_cn: 价格倍数排名最大值
        indicator_type: long
        indicator_format: long
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: false
        indicator_is_support_sorting: true
      - indicator_name: price_rate_rank_percentile
        indicator_name_cn: 价格倍数排名百分位
        indicator_type: double
        indicator_format: double
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: false
        indicator_is_support_sorting: true

    template: |
      select
              max(case when pm_eid = '<#if filter_flat.org_emp_code??>${filter_flat.org_emp_code.filterValues[0]}</#if>' then t.rnk else null end) as price_rate_rank,
              max(t.rnk) as price_rate_rank_max,
              max(case when pm_eid = '<#if filter_flat.org_emp_code??>${filter_flat.org_emp_code.filterValues[0]}</#if>' then t.rnk else null end) / max(t.rnk) as price_rate_rank_percentile
      from (
              select                 
                      pm_eid,
                      rank() over(order by price_rate desc) as rnk
              from (
                      SELECT  1 as default_group
                              ,pm_eid
                              ,sum(avg_multiple_price * (ifnull(pre1d_total_pv,0)- ifnull(pre1d_uncomp_pv,0))) / sum(ifnull(pre1d_total_pv,0)-ifnull(pre1d_uncomp_pv,0)) as price_rate -- 价格倍数
        
                      FROM
                              adm_prd_grp_avg_multiple_price_work_platform_df
                      WHERE   partition_d = (select max(partition_d) from adm_prd_grp_avg_multiple_price_work_platform_df)
                              <#if filter_flat.date??>          AND     view_date between '${filter_flat.date.filterValues[0]}' and '${filter_flat.date.filterValues[1]}' </#if>
                      
                              <#if filter_flat.business_line??>
                                  <#if filter_flat.business_line.filterValues[0] = "100">
                                      AND (sub_bu_type IN ("跟团游") and is_small_group = 0)  -- AND sub_bu_type IN ("跟团游")
                                  <#elseif filter_flat.business_line.filterValues[0] = "200">
                                      and (sub_bu_type IN ("独立出游") or is_small_group = 1)  -- AND sub_bu_type IN ("独立出游")
                                  <#elseif filter_flat.business_line.filterValues[0] = "220">
                                  </#if>
                              </#if>
                      group by
                              pm_eid
               ) as t
      ) as t
      


  - db_name:
    id: 15
    table_name: adm_prd_grp_product_score_df
    source_type: starrocks
    indicators:
      - indicator_name: weighted_platform_score_rank
        indicator_name_cn: 加权平台信息分排名
        indicator_type: long
        indicator_format: long
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: false
        indicator_is_support_sorting: false
      - indicator_name: weighted_platform_score_rank_max
        indicator_name_cn: 加权平台信息分排名最大值
        indicator_type: long
        indicator_format: long
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: false
        indicator_is_support_sorting: false
      - indicator_name: weighted_platform_score_rank_percentile
        indicator_name_cn: 加权平台信息分排名百分位
        indicator_type: long
        indicator_format: long
        indicator_key:
        indicator_key_name:
        indicator_is_dimension: false
        indicator_is_support_sorting: false

    template: |
      select
              max(case when pm_eid = '<#if filter_flat.org_emp_code??>${filter_flat.org_emp_code.filterValues[0]}</#if>' then rnk else null end) as weighted_platform_score_rank,
              max(rnk) as weighted_platform_score_rank_max,
              max(case when pm_eid = '<#if filter_flat.org_emp_code??>${filter_flat.org_emp_code.filterValues[0]}</#if>' then rnk else null end) / max(rnk) as weighted_platform_score_rank_percentile
      from (
              select 
                      pm_eid,
                      rank() over(order by weighted_platform_score desc) as rnk
              from (
                      SELECT  1 as default_group,
                              pm_eid,
                              SUM(dtl_uv_1y*platform_score) / sum(dtl_uv_1y) as weighted_platform_score  -- 加权平台信息分
                      FROM
                              adm_prd_grp_product_score_df
                      WHERE   partition_d = (select max(partition_d) from adm_prd_grp_product_score_df)
                              <#if filter_flat.partition_d??>   AND     partition_d = '${filter_flat.partition_d.filterValues[1]}' </#if>
                              <#if filter_flat.date??>          AND     etl_date between '${filter_flat.date.filterValues[0]}' and '${filter_flat.date.filterValues[1]}' </#if>
      
                              <#if filter_flat.business_line??>
                                  <#if filter_flat.business_line.filterValues[0] = "100">
                                      AND (sub_bu_type IN ("跟团游") and is_small_group = 0)  -- AND sub_bu_type IN ("跟团游")
                                  <#elseif filter_flat.business_line.filterValues[0] = "200">
                                      and (sub_bu_type IN ("独立出游") or is_small_group = 1)  -- AND sub_bu_type IN ("独立出游")
                                  <#elseif filter_flat.business_line.filterValues[0] = "220">
                                  </#if>
                              </#if>
      
                      group by 
                              pm_eid
              ) as t
      ) as t