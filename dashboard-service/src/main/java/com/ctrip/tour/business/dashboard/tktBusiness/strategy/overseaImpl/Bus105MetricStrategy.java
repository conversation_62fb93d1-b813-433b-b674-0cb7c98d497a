package com.ctrip.tour.business.dashboard.tktBusiness.strategy.overseaImpl;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.OverseaMetricCalStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.Future;

@Component
public class Bus105MetricStrategy implements OverseaMetricCalStrategy {

    @Autowired
    private Bus105And106And107MetricStrategy bus105And106And107MetricStrategy;


    @Override
    public Future<List<MetricDetailInfo>> getOverseaSingleMetricCardData(TimeFilter timeFilter,
                                                                         OverseaMetricInfoBean metricInfoBean,
                                                                         String d,
                                                                         AvailableSubMetric availableSubMetric) throws Exception {
        return bus105And106And107MetricStrategy.getOverseaSingleMetricCardData(timeFilter, metricInfoBean, d, availableSubMetric, "105");
    }

    @Override
    public GetOverseaTrendLineDataResponseType getOverseaSingleTrendlineData(GetOverseaTrendLineDataRequestType request,
                                                                             String d) throws Exception {
        return bus105And106And107MetricStrategy.getOverseaSingleTrendlineData(request, d);
    }

    @Override
    public GetOverseaDrillDownBaseInfoResponseType getOverseaSingleDrillDownBaseInfo(GetOverseaDrillDownBaseInfoRequestType request,
                                                                                     String d,
                                                                                     OverseaMetricInfoBean metricInfoBean) throws Exception {
        return bus105And106And107MetricStrategy.getOverseaSingleDrillDownBaseInfo(request, d, metricInfoBean);
    }

    @Override
    public GetOverseaTableDataResponseType getOverseaSingleTableData(GetOverseaTableDataRequestType request,
                                                                     String d,
                                                                     OverseaMetricInfoBean metricInfoBean) throws Exception {
        return bus105And106And107MetricStrategy.getOverseaSingleTableData(request, d, metricInfoBean);
    }

    @Override
    public String getMetricName() {
        return "105";
    }
}
