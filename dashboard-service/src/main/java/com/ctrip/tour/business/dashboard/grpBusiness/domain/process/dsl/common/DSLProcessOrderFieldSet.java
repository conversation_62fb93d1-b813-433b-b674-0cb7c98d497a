package com.ctrip.tour.business.dashboard.grpBusiness.domain.process.dsl.common;

import com.ctrip.soa._24922.DSLRequestType;
import com.ctrip.soa._24922.OrderBy;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.AbstractPreDSLProcess;

import java.util.List;
import java.util.Objects;

public class DSLProcessOrderFieldSet extends AbstractPreDSLProcess {
    private List<OrderBy> orderBys;

    public static AbstractPreDSLProcess getInstance(List<OrderBy> orderBys) {
        DSLProcessOrderFieldSet dslProcessLimitSet = new DSLProcessOrderFieldSet();
        dslProcessLimitSet.orderBys = orderBys;
        return dslProcessLimitSet;
    }

    @Override
    public DSLRequestType process(DSLRequestType dslRequestType, EarlyReturn earlyReturn) {
        orderBys.forEach(v -> {
            if (v != null && !Objects.equals(v.orderFiled, "")) {
                dslRequestType.setOrderBy(orderBys);
            }
        });
        return dslRequestType;
    }
}
