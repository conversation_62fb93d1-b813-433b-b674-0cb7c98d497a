package com.ctrip.tour.business.dashboard.grpBusiness.domain.process.dsl.common;

import com.ctrip.soa._24922.CompareConfig;
import com.ctrip.soa._24922.DSLRequestType;
import com.ctrip.soa._24922.WhereCondition;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.AbstractPreDSLProcess;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.dsl.CompareConfigUtils;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.dsl.DSLUtils;

import java.util.*;

public class DSLProcessCompareSet extends AbstractPreDSLProcess {
    private List<String> compareConfig;
    private static final Set<String> defaultCompareSet = new HashSet<>(Arrays.asList("date", "date_week", "date_month"));

    public static AbstractPreDSLProcess getInstance(List<String> compareConfig) {
        DSLProcessCompareSet dslProcessLimitSet = new DSLProcessCompareSet();
        dslProcessLimitSet.compareConfig = compareConfig;
        return dslProcessLimitSet;
    }


    private String getCompareIndicatorName(List<String> groupBys) {
        if (groupBys == null) {
            return "date";
        }
        for (String groupBy : groupBys) {
            if (defaultCompareSet.contains(groupBy)) {
                return groupBy;
            }
        }
        return "date";
    }

    @Override
    public DSLRequestType process(DSLRequestType dsl, EarlyReturn earlyReturn) {
        if (compareConfig == null || compareConfig.isEmpty()) {
            return dsl;
        }
        if (dsl.getCompareConfig() == null) {
            dsl.setCompareConfig(new ArrayList<>());
        }
        // 获取日期
        WhereCondition dateWhereCondition = DSLUtils.getWhereConditionByFilterName(dsl, "date");
        if (dateWhereCondition == null || dateWhereCondition.getFilterValues().size() < 2) {
            return dsl;
        }
        String startDate = dateWhereCondition.getFilterValues().get(0);
        String endDate = dateWhereCondition.getFilterValues().get(1);
        // 初始化
        compareConfig.forEach(v -> {
            switch (v) {
                case "year_tb":
                    CompareConfig compareConfigYearTB = CompareConfigUtils.getYearTB(startDate, endDate, getCompareIndicatorName(dsl.getGroupBy()));
                    dsl.getCompareConfig().add(compareConfigYearTB);
                    break;
                case "month_tb":
                    CompareConfig compareConfigMonthTB = CompareConfigUtils.getMonthTB(startDate, endDate, getCompareIndicatorName(dsl.getGroupBy()));
                    dsl.getCompareConfig().add(compareConfigMonthTB);
                    break;
                case "month_hb":
                    CompareConfig compareConfigMonthHb = CompareConfigUtils.getHBWithPrefix(startDate, endDate, getCompareIndicatorName(dsl.getGroupBy()), "month_hb");
                    dsl.getCompareConfig().add(compareConfigMonthHb);
                    break;
                case "week_hb":
                    CompareConfig compareConfigWeekHb = CompareConfigUtils.getHBWithPrefix(startDate, endDate, getCompareIndicatorName(dsl.getGroupBy()), "week_hb");
                    dsl.getCompareConfig().add(compareConfigWeekHb);
                    break;
                case "hb":
                    CompareConfig compareConfigHb = CompareConfigUtils.getHBWithPrefix(startDate, endDate, getCompareIndicatorName(dsl.getGroupBy()), "hb");
                    dsl.getCompareConfig().add(compareConfigHb);
                    break;
            }
        });
        return dsl;
    }
}
