package com.ctrip.tour.business.dashboard.sightArchives.bean;

import com.ctrip.tour.business.dashboard.sightArchives.enums.common.ExamineLevelEnumType;

import java.util.List;
import java.util.Set;

public class ExamineRangeBO {

    //0-国内 1-海外
    private Integer examineType;
    private ExamineLevelEnumType examineLevel;
    private Set<String> examineRange;

    public Integer getExamineType() {
        return examineType;
    }

    public void setExamineType(Integer examineType) {
        this.examineType = examineType;
    }

    public ExamineLevelEnumType getExamineLevel() {
        return examineLevel;
    }

    public void setExamineLevel(ExamineLevelEnumType examineLevel) {
        this.examineLevel = examineLevel;
    }

    public Set<String> getExamineRange() {
        return examineRange;
    }

    public void setExamineRange(Set<String> examineRange) {
        this.examineRange = examineRange;
    }
}
