package com.ctrip.tour.business.dashboard.tktBusiness.strategy.domestic.impl;

import com.ctrip.soa._24922.*;
import com.ctrip.soa._27181.GetRawDataRequestType;
import com.ctrip.soa._27181.GetRawDataResponseType;
import com.ctrip.tour.business.dashboard.grpBusiness.enums.DomesticMetricEnum;
import com.ctrip.tour.business.dashboard.sightArchives.dao.domesticmetric.CdmPrdOdtCpdDashboardTmtWeaknessStatisticsDfDao;
import com.ctrip.tour.business.dashboard.sightArchives.dao.domesticmetric.Domestic11And12WeaknessDao;
import com.ctrip.tour.business.dashboard.sightArchives.entity.domesticMetricEntity.bean.Domestic11And12WeaknessParam;
import com.ctrip.tour.business.dashboard.sightArchives.entity.domesticmetric.Domestic1112ParamBean;
import com.ctrip.tour.business.dashboard.sightArchives.entity.domesticmetric.Domestic567ParamBean;
import com.ctrip.tour.business.dashboard.sightArchives.entity.domesticmetric.Domestic567SearchResult;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.domestic.DomesticMetricCalStrategy;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.domestic.specificlogic.Bus11And12CommonStrategy;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.Future;

@Component
public class Bus11DomesticMetricStrategy implements DomesticMetricCalStrategy {

    @Autowired
    private Bus11And12CommonStrategy bus11And12CommonStrategy;

    @Autowired
    Domestic11And12WeaknessDao domestic11And12WeaknessDao;

    @Override
    public Future<DomesticMetricDetailInfo> getSingleMetricCardData(String domainName, TimeFilter timeFilter, List<MetricInfoBean> metricInfoBeanList, String d, Boolean isFirst, Integer businessId) throws Exception {
        return new AsyncResult<>(bus11And12CommonStrategy.getSingleMetricCardData(
                domainName, timeFilter, metricInfoBeanList, d, isFirst, getMetricName()));
    }
    @Override
    public GetFirstPageDomesticMetricCardDrillDataResponseType getFirstPageDomesticMetricCardDrillData(
            GetFirstPageDomesticMetricCardDrillDataRequestType request,MetricInfoBean metricInfoBean, String d) throws Exception{
        return bus11And12CommonStrategy.getFirstPageDomesticMetricCardDrillData(request, metricInfoBean, d);
    }
    @Override
    public GetDomesticMetricTrendDataResponseType getSingleTrendlineData(GetDomesticMetricTrendDataRequestType request, String d) throws Exception {
        return bus11And12CommonStrategy.getTrendLineData(request, d, getMetricName());
    }

    @Override
    public GetDomesticTableDataResponseType getSingleTableData(GetDomesticTableDataRequestType request, MetricInfoBean metricInfoBean, String d) throws Exception {
        return bus11And12CommonStrategy.getSingleTableData(request, metricInfoBean, d);
    }

    @Override
    public GetDomesticDrillDownBaseInfoResponseType getSingleDrillDownBaseInfo(GetDomesticDrillDownBaseInfoRequestType request, MetricInfoBean metricInfoBean, String d) throws Exception {
       return bus11And12CommonStrategy.getSingleDrillDownBaseInfo(request, metricInfoBean, d);
    }

    @Override
    public Integer getMetricName() {
        return 11;
    }
}
