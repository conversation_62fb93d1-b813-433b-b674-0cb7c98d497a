include 'BaijiCommonTypes.bjsc'
include 'getMetricData.bjsc'
namespace java 'com.ctrip.soa._24922'

class TimeFilter{
    //时间类型 枚举值 month或者quarter或者half或者year
    string dateType;
    //所属年 2021 2022
    string year;
    //时间类型是month时传入 所属月 01 02 03 ... 012
    string month;
    //时间类型是quarter时传入 所属季 Q1 Q2 Q3 Q4
    string quarter;
    //时间类型是half时传入  枚举值为H1 H2
    string half;
    //趋势线或者下钻时展示的时间范围 选择月时枚举值3 6 12 选择季时枚举值4
    int timeFrame;
}

class GetFirstPageDomesticMetricCardDrillDataRequestType{
    //指标code，（gmv,profit,quality,directSign,sightCover,typeCover,ticketBooking,categoryCover,dayTourCover,dayTourBooking）1gmv，2毛利，3质量，4直签，5景点覆盖，6票种覆盖，7门票预订政策，9品类覆盖，11日游覆盖，12日游预订政策
    string metricCode;
    //默认下钻维度
    string defaultField;
    //邮箱前缀
    string domainName;
    //时间选择器
    TimeFilter timeFilter;
    //业务线(1门票，2玩乐)
    int businessId;
}
class GetFirstPageDomesticMetricCardDrillDataResponseType{
    //表头数据(按照产品要求定义)
    list<string> tableHeaderList
    //表格数据
    list<FirstPageDomesticTableData> tableDataItemList
    //返回类型
    BaijiCommonTypes.ResponseStatusType ResponseStatus;
}
class FirstPageDomesticTableData{
   //大区
    string regionName;
   //省份
    string provinceName;
   //商拓
    string examinee;
   //景点id
    int viewspotId;
   //景点名称
   string viewspot;
   //完成率-GMV、毛利、景点覆盖、票种、门票、日游覆盖、
   double completeRate;
   //目标值
   double targetRate;
   //同比去年-GMV、毛利、质量
   double yoyValue;
   //劣势率-日游覆盖
    double disadvantageRate;
   //劣势率-日游覆盖目标值
    double disadvantageTargetRate;
   //加权缺陷率-质量
   double weightedDefectRate;
   //直签-核心直签率
    double coreDirectRate;
   //直签-核心目标率
    double coreTargetRate;
   //直签-聚焦直签率
    double focusDirectRate;
   //直签-聚焦目标率
    double focusTargetRate;
   //直签-长尾高价值直签率
    double tailHighValueDirectRate;
   //直签-长尾高价值目标率
    double tailHighValueTargetRate;
   //直签-长尾高产直签率
    double tailHighYieldDirectRate;
   //直签-长尾高产目标率
    double tailHighYieldTargetRate;
   //景点覆盖、票种、门票-核心劣势率
   double  coreDisadvantageRate;
   //景点覆盖、票种、门票-核心目标劣势率
   double  coreDisadvantageTargetRate;
   //景点覆盖、票种、门票-聚焦劣势率
   double  focusDisadvantageRate;
   //景点覆盖、票种、门票-聚焦目标劣势率
   double  focusDisadvantageTargetRate;
   //景点覆盖、票种、门票-长尾高价值劣势率
   double  tailHighValueDisadvantageRate;
   //景点覆盖、票种、门票-长尾高价值目标劣势率
   double  tailHighValueDisadvantageTargetRate;
   //景点覆盖、票种、门票-长尾其他劣势率
   double  tailHighOtherDisadvantageRate;
   //景点覆盖、票种、门票-长尾其他目标劣势率
   double  tailHighOtherDisadvantageTargetRate;

}
class DomesticTableData{
   //大区
    string regionName;
   //省份
    string provinceName;
   //商拓
    string examinee;
   //商拓
    string examineeName;
   //景点id
    int viewspotId;
   //景点名称
   string viewspot;
   //景点分层
   string ratioLevel;
   //目标值 、目标分
   double targetValue;
   //目标劣势率、
   double targetRate;
   //完成值 、达成分
   double completeValue;
   //系统内毛利
   double innerProfitValue;
   //系统外毛利
   double outerProfitValue;
   //完成率、实际劣势率
   double completeRate;
   //同比去年-GMV、毛利、质量
   double yoyValue;
   //指标值与目标值的差值-景点覆盖、票种、门票、直签景区数差额
   double gapValue;     
   //藏宝图景区数
   double treasuremapScenicCnt;
   //目标直签景区数
   double targetSignScenicCnt;
   //实际直签景区数
   double actualSignScenicCnt; 
   //直签率
   double ttdCpsSignRate;
   //活动省份
   string actProvinceName;
   //活动目标值
   double actTargetValue;
   //活动完成值
   double actCompleteValue;
   //系统内毛利
   double actInnerProfitValue;
   //系统外毛利
   double actOuterProfitValue;
   //活动完成率-GMV、
   double actCompleteRate;
   //活动同比去年-GMV、毛利、质量
   double actYoyValue;
   //日游省份
   string  odfProvinceName;
   //日游目标值
   double odfTargetValue;
   //日游完成值
   double odfCompleteValue;
   //系统内毛利
   double odfInnerProfitValue;
   //系统外毛利
   double odfOuterProfitValue;
   //日游完成率-GMV、
   double odfCompleteRate;
   //日游同比去年-GMV、毛利、质量
   double odfYoyValue;
   //加权缺陷率
   double weightedDefectRate;
   //支付订单量
   double payOrdCnt;
   //加权缺陷数
   double weightedDefectCnt;  
   //劣势率-日游覆盖
    double disadvantageRate;
   //品类覆盖-品类类型
   string categoryType;
    //品类覆盖-品类权重
    double categoryWeight
    //品类覆盖-藏宝图特色体验数
    int treasuremapExpCnt;
    //品类覆盖-营业中特色体验数
    int openExpCnt;
    //品类覆盖-已覆盖特色体验数
    int coverExpCnt;
    //品类覆盖-未覆盖特色体验数
   int uncoverExpCnt;
    //品类-现状覆盖率
    double categoryCurrentCoverageRate;
}

class DomesticNoDrillTableData {
    //直签-景点范围,枚举（核心-core，聚焦-focus，长尾高价值-tailHighValue，长尾高产tailHighYield）
    string directSightRange;
    //直签-藏宝图景区数
    int directCrashSightNum;
    //直签-目标直签景区数
    int directTargetSightNum;
    //直签-实际直签景区数
    int directRealSightNum;
    //直签-直签景区数差额
    int directSightGap;
    //直签-直签率
    double directRate;
    //直签-权重
    double directWeight;
    //直签-达成分
    int directCompleteScore;
    //直签-目标分
    int directTargetScore;
    //直签-完成率
    double directCompleteRate;

    //景点、票种、门票预订-景点范围，枚举（核心-core，大盘-general，聚焦-focus，长尾高价值-tailHighValue，长尾其他tailOther）
    string sightRange
    //景点、票种、门票预订-劣势率
    double sightDisadvantageRate;
    //景点、票种、门票预订-目标
    double sightTargetValue;
    //景点、票种、门票预订-差值
    double sightGapValue;
    //景点、票种、门票预订-分层完成率
    double sightLevelCompleteRate;
    //景点、票种、门票预订-权重
    double sightWeight;
    //景点、票种、门票预订-完成率
    double sightCompleteRate;

    //品类-考核类型，枚举（keyCat-重点品类,feaCat-特色品类）
    string categoryExamineType
    //品类-藏宝图特色体验数
    int categoryCrashExperienceNum;
    //品类-营业中特色体验数
    int categoryOperatingExperienceNum;
    //品类-已覆盖特色体验数
    int categoryCoveredExperienceNum;
    //品类-未覆盖特色体验数
    int categoryUncoveredExperienceNum;
    //品类-现状覆盖率
    double categoryCurrentCoverageRate;
    //品类-目标覆盖率
    double categoryTargetCoverageRate;
    //品类-权重
    double categoryWeight;
    //品类-完成率
    double categoryCompleteRate;
}

class GetDomesticMetricSummaryDataRequestType{
    //邮箱前缀
    string domainName
    //时间选择器
    TimeFilter timeFilter
}
class GetDomesticMetricSummaryDataResponseType{
    //指标列表数据
    list<DomesticMetricDetailInfo> metricDetailInfoList
    //返回类型
    BaijiCommonTypes.ResponseStatusType ResponseStatus;
}
class DomesticMetricDetailInfo{
    //指标code，（gmv,profit,quality,directSign,sightCover,typeCover,ticketBooking,categoryCover,dayTourCover,dayTourBooking,ticGoods,actGoods）1gmv，2毛利，3质量，4直签，5景点覆盖，6票种覆盖，7门票预订政策，9品类覆盖，11日游覆盖，12日游预订政策,13门票商品力，14日游商品力，二级指标（首页需要）（tic-门票，play-玩乐，act-活动，dayTour日游）
    string metricCode
    //指标名称
    string metricName
    //完成值（gmv、毛利、日游商品力）、加权缺陷数（质量）
    double completeValue
    //系统外完成值（毛利）
    double outSystemCompleteValue
    //系统内完成值（毛利）
    double inSystemCompleteValue
    //目标值（gmv、毛利、日游商品力、质量）
    double targetValue
    //完成率（gmv、毛利）
    double completeRate
    //劣势率
    double disadvantageRate
    //加权缺陷率（质量）
    double weightedDefectRate
    //差值（日游商品力相关）
    double gapValue
    //年同比（gmv、毛利）
    double yoyValue
    //环比
    double momValue
    //支付订单量（质量）
    double payOrderCount
    //个人排名（gmv）
    int rank
    //参与排名人数（gmv）
    int totalRank
    //业务线(1门票，2玩乐)
    string businessName
    //是否需要展示下钻标识
    bool needDrillDown
    //默认下钻维度 (对于国内收入力部分) 可能需要用|分割
    string defaultField
    //环比类型 枚举值 7days、lastmonth、30days、lastquarter gmv和毛利指标需要该字段,且在详情页需要展示
    string momType
    //表头数据(按照产品要求定义)
    list<string> tableHeaderList
    //表格数据
    list<DomesticNoDrillTableData> tableDataItemList
    //子指标信息,包含大类指标下的相关子指标
    list<DomesticMetricDetailInfo> subMetricDetailInfoList
}


class GetDomesticMetricCardDataRequestType{
    //邮箱前缀
    string domainName
    //时间选择器
    TimeFilter timeFilter
    //业务线(1门票，2玩乐)
    int businessId
}
class GetDomesticMetricCardDataResponseType{
    //指标列表具体数据
    list<DomesticMetricDetailInfo> metricDetailInfoList
    //返回类型
    BaijiCommonTypes.ResponseStatusType ResponseStatus;
}


class GetDomesticMetricTrendDataRequestType{
    //邮箱前缀
    string domainName
    //时间选择器
    TimeFilter timeFilter
    //指标code，（gmv,profit,quality,directSign,sightCover,typeCover,ticketBooking,categoryCover,dayTourCover,dayTourBooking）1gmv，2毛利，3质量，4直签，5景点覆盖，6票种覆盖，7门票预订政策，9品类覆盖，11日游覆盖，12日游预订政策
    string metricCode
    //业务线(1门票，2玩乐)
    int businessId
    //子业务线（3活动，4日游，5玩乐总计）
    int subBusinessId
}
class GetDomesticMetricTrendDataResponseType{
    //趋势线数据
    list<TrendLineDetailInfo> trendLineDetailInfoList
    //返回数据
    BaijiCommonTypes.ResponseStatusType ResponseStatus;
}
class TrendLineDetailInfo{
    //趋势线类型  lineChart折线图 barChart柱状图  stackBarChart堆叠柱状图
    string type
    //维度名称 标识当前趋势线所属的是哪个具体指标
    string dim
    //实际数据
    list<TrendLineDataItem> trendLineDataItemList
}
class TrendLineDataItem{
    //字段名，gmv毛利:completeValue、yoyValue、completeRate，质量：weightedDefectRate、targetValue，直签：directCompleteRate、core、focus、tailHighValue、tailHighYield，门票商品力：core、coreGap、focus、focusGap、general、generalGap、tailHighValue、tailHighValueGap、tailOther、tailOtherGap，品类覆盖：keyCat、featCat、completeRate，日游商品力：disadvantageRate、disadvantageRateGap
    string name
    //对应时间
    string time
    //对应值
    double value
}

class GetDomesticDrillDownBaseInfoRequestType{
    //时间选择器
    TimeFilter timeFilter
    //指标code，（gmv,profit,quality,directSign,sightCover,typeCover,ticketBooking,categoryCover,dayTourCover,dayTourBooking,ticGoods,actGoods）1gmv，2毛利，3质量，4直签，5景点覆盖，6票种覆盖，7门票预订政策，9品类覆盖，11日游覆盖，12日游预订政策,13门票商品力，14日游商品力
    string metricCode
    //邮箱前缀
    string domainName
    //搜索的field
    string searchField
    //搜索词
    string searchWord
    //业务线(1门票，2玩乐)
    int businessId
    //子业务线（3活动，4日游）
    int subBusinessId
}
class GetDomesticDrillDownBaseInfoResponseType{
    //当前选择用户应该默认选中的下钻字段
    string defaultChosenField
    //所有可能的下钻字段以及其可下钻的值
    list<FieldDataItem> fieldDataItemList
    //返回数据
    BaijiCommonTypes.ResponseStatusType ResponseStatus;
}
class FieldDataItem{
    //当前字段
    string field
    //是否需要气泡图
    bool needBubble
    //是否需要趋势线
    bool needLine
    //当前字段的所有可能值(大区 省份 商拓 POI) poi需要value和relationValue
    list<FieldValueItem> fieldValueItemList
}
class FieldValueItem{
    //值
    string value
    //关联值
    string relationValue
}


class GetDomesticTableDataRequestType{
    //时间选择器
    TimeFilter timeFilter
    //指标code，（gmv,profit,quality,directSign,sightCover,typeCover,ticketBooking,categoryCover,dayTourCover,dayTourBooking）1gmv，2毛利，3质量，4直签，5景点覆盖，6票种覆盖，7门票预订政策，9品类覆盖，11日游覆盖，12日游预订政策
    string metricCode
    //邮箱前缀
    string domainName
    //下钻选择器
    DrillDownFilter drillDownFilter
    //当前页数
    int pageNo
    //分页条数  对于首页以及详情页的气泡图可以传999
    int pageSize
    //bubble 气泡图   table真正的表格 trend趋势图
    string queryType
    //业务线(1门票，2玩乐)
    int businessId
    //子业务线（3活动，4日游）
    int subBusinessId
}
class GetDomesticTableDataResponseType{
    //总数据条数
    int totalNum
    //表格数据
    list<DomesticTableData> tableDataItemList
    //表头数据(按照产品要求定义)
    list<string> tableHeaderList
    //趋势线数据
    list<TrendLineDetailInfo> trendLineDetailInfoList
    //返回数据
    BaijiCommonTypes.ResponseStatusType ResponseStatus;
}
class DrillDownFilter{
    //下钻字段
    string field
    //下钻字段选中的值 未选中需传[]
    list<string> fieldValueList
}

class GetFirstPageDomesticMetricCardDataRequestType{
    //邮箱前缀
    string domainName
    //时间选择器
    TimeFilter timeFilter
}
class GetFirstPageDomesticMetricCardDataResponseType{
    //指标列表具体数据
    list<DomesticMetricDetailInfo> metricDetailInfoList
    //当月/季/半年/年 枚举
    string dateType
    //返回格式
    BaijiCommonTypes.ResponseStatusType ResponseStatus;
}

@serviceName='BIBusinessDashboardService'
@serviceNamespace='http://soa.ctrip.com/_24922'
service BIBusinessDashboardService {
    //首页(国内业绩)-指标卡
    GetFirstPageDomesticMetricCardDataResponseType getFirstPageDomesticMetricCardData(GetFirstPageDomesticMetricCardDataRequestType request);
    //首页(国内业绩)-指标卡的下钻
    GetFirstPageDomesticMetricCardDrillDataResponseType getFirstPageDomesticMetricCardDrillData(GetFirstPageDomesticMetricCardDrillDataRequestType request);
    //经营业绩看板(国内业绩)-大盘概览
    GetDomesticMetricSummaryDataResponseType getDomesticMetricSummaryData(GetDomesticMetricSummaryDataRequestType request);
    //经营业绩看板(国内业绩)-指标卡
    GetDomesticMetricCardDataResponseType getDomesticMetricCardData(GetDomesticMetricCardDataRequestType request);
    //经营业绩看板(国内业绩)-趋势图
    GetDomesticMetricTrendDataResponseType getDomesticMetricTrendData(GetDomesticMetricTrendDataRequestType request);
    //获取国内可下钻字段以及其值
    GetDomesticDrillDownBaseInfoResponseType getDomesticDrillDownBaseInfo(GetDomesticDrillDownBaseInfoRequestType request);
    //获取国内表格数据
    GetDomesticTableDataResponseType getDomesticTableData(GetDomesticTableDataRequestType request);
}