package com.ctrip.tour.business.dashboard.tktBusiness.strategy.subMetricImplV2;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ExamineConfigBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBeanV2;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.DataUpdateBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.bo.OverSeaExamineConfigBO;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardOverseaExamineeConfigDao;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardOverseaExamineeConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.enums.TrendLineNameEnum;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBeanV3;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.StrategyException;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.Bus103Helper;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.*;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;

@Service
@Slf4j
public class OverseaSubMetricCalStategyBizImplV2 implements ApplicationContextAware {

    private ApplicationContext applicationContext;
    @Autowired
    private BusinessDashboardOverseaExamineeConfigDao overseaConfigDao;

    @Autowired
    private RemoteConfig remoteConfig;

    @Autowired
    private DataUpdateBiz dataUpdateBiz;

    private ConcurrentHashMap<String, IncomeSubMetricCalStrategyV2> incomeCalStrategyMap = new ConcurrentHashMap<>();

    private ConcurrentHashMap<String, TicketQuantityCalStrategyV2> ticketQuantityCalStrategyMap = new ConcurrentHashMap<>();

    private ConcurrentHashMap<String, QuantitySubMetricCalStrategyV2> quantityCalStrategyMap = new ConcurrentHashMap<>();

    private ConcurrentHashMap<String, QuantitySubMetricCalStrategy> quantityCalStrategyMapV1 = new ConcurrentHashMap<>();

    private ConcurrentHashMap<String, CompetitorSubMetricCalStrategyV2> competitorCalStrategyMap = new ConcurrentHashMap<>();


    public Future<OveaseaSubMetric> getBus101102SubMetricCardData(TimeFilter timeFilter,
                                                                  OverseaMetricInfoBeanV2 metricInfoBean,
                                                                  String d,
                                                                  String subMetric,
                                                                  GetOverseaMetricCardDataV2RequestType request) throws Exception {

        IncomeSubMetricCalStrategyV2 metricCalStrategy = incomeCalStrategyMap.get(subMetric);
        if (metricCalStrategy == null) {
            throw new StrategyException("strategy is not found!");
        }
        return metricCalStrategy.getBus101102SubMetricCardData(timeFilter, metricInfoBean, d, subMetric, request);
    }


    public GetOverseaTrendLineDataV2ResponseType getBus101102SubTrendlineData(GetOverseaTrendLineDataV2RequestType request,
                                                                              String d,
                                                                              List<String> timeList) throws Exception {
        String subMetric = request.getSubMetric();
        IncomeSubMetricCalStrategyV2 metricCalStrategy = incomeCalStrategyMap.get(subMetric);
        if (metricCalStrategy == null) {
            throw new StrategyException("strategy is not found!");
        }
        return metricCalStrategy.getBus101102SubTrendLineData(request, d, timeList);
    }


    public GetOverseaDrillDownBaseInfoV2ResponseType getBus101102SubDrillDownBaseInfo(GetOverseaDrillDownBaseInfoV2RequestType request,
                                                                                    String d,
                                                                                      OverseaMetricInfoBean metricInfoBean) throws Exception {
        String subMetric = request.getSubMetric();
        IncomeSubMetricCalStrategyV2 metricCalStrategy = incomeCalStrategyMap.get(subMetric);
        if (metricCalStrategy == null) {
            throw new StrategyException("strategy is not found!");
        }
        return metricCalStrategy.getBus101102SubDrillDownBaseInfo(request, d, metricInfoBean);
    }

    public GetOverseaTableDataV2ResponseType getBus101102SubTableData(GetOverseaTableDataV2RequestType request,
                                                                    String d,
                                                                      OverseaMetricInfoBean metricInfoBean) throws Exception {
        String subMetric = request.getSubMetric();
        IncomeSubMetricCalStrategyV2 metricCalStrategy = incomeCalStrategyMap.get(subMetric);
        if (metricCalStrategy == null) {
            throw new StrategyException("strategy is not found!");
        }
        return metricCalStrategy.getBus101102SubTableData(request, d, metricInfoBean);
    }


    public Future<OveaseaSubMetric> getBus103SubMetricCardData(TimeFilter timeFilter,
                                                               OverseaMetricInfoBeanV2 metricInfoBean,
                                                               String d,
                                                               String metric,
                                                               String subMetric) throws Exception {
        subMetric = Bus103Helper.transformSubMetricName(subMetric);
        QuantitySubMetricCalStrategy metricCalStrategy = quantityCalStrategyMapV1.get(subMetric);
        if (metricCalStrategy == null) {
            throw new StrategyException("strategy is not found!");
        }
        d = dataUpdateBiz.getUpdateTime();
        //对于按半年考核的人  他们两个季度配置的考核数据是一样的  所以任取其一就行了
        String quarter = "quarter".equals(timeFilter.getDateType()) ? timeFilter.getQuarter() : DateUtil.getFirstQuarterOfHalf(timeFilter.getHalf());

        //获取应该展示的指标列表
        List<BusinessDashboardOverseaExamineeConfig> examineeConfigList = overseaConfigDao.querySpecificPeriodAllMetricConfig(metricInfoBean.getDomainName(), d, timeFilter.getYear(), quarter);
        OverSeaExamineConfigBO bo = new OverSeaExamineConfigBO();
        List<OverseaMetricInfoBean> metricInfoBeanList = bo.getMetricInfoBeanList(examineeConfigList, remoteConfig);
        OverseaMetricInfoBean oldMetricInfoBean = metricInfoBeanList.stream().filter(bean -> bean.getMetric().equals(metric)).findFirst().orElse(null);

        Future<MetricDetailInfo> metricDetailInfoFuture =  metricCalStrategy.getBus103SubMetricCardData(timeFilter, oldMetricInfoBean, d, metric, subMetric);
        OveaseaSubMetric oveaseaSubMetric = transformMetricInfo(metricDetailInfoFuture.get());
        return new AsyncResult<>(oveaseaSubMetric);
    }

    public GetOverseaTrendLineDataV2ResponseType getBus103SubTrendlineData(GetOverseaTrendLineDataV2RequestType request,
                                                                         String d,
                                                                         List<ExamineConfigBean> examineConfigBeanList) throws Exception {
        String subMetric = Bus103Helper.transformSubMetricName(request.getSubMetric());
        request.setSubMetric(subMetric);
        QuantitySubMetricCalStrategy metricCalStrategy = quantityCalStrategyMapV1.get(subMetric);
        if (metricCalStrategy == null) {
            throw new StrategyException("strategy is not found!");
        }
        d = dataUpdateBiz.getUpdateTime();
        GetOverseaTrendLineDataRequestType oldRequest = transform103TrendLineRequestNewToOld(request);
        GetOverseaTrendLineDataResponseType oldResponse = metricCalStrategy.getBus103SubTrendLineData(oldRequest, d, examineConfigBeanList);
        return transform103TrendLineResponseOldToNew(oldResponse);
    }

    /**
     * 质量趋势线接口V2响应转V1
     * @param oldResponse
     * @return
     */
    protected GetOverseaTrendLineDataV2ResponseType transform103TrendLineResponseOldToNew(GetOverseaTrendLineDataResponseType oldResponse) {
        GetOverseaTrendLineDataV2ResponseType newResponse = new GetOverseaTrendLineDataV2ResponseType();
        newResponse.setResponseStatus(oldResponse.getResponseStatus());
        List<OverseaTrendLine> trendLines = new ArrayList<>();
        oldResponse.getTrendLineDetailInfoList().forEach(
                oldTrendLineDetailInfo -> {
                    OverseaTrendLine newTrendLine = new OverseaTrendLine();
                    newTrendLine.setTrendLineType(oldTrendLineDetailInfo.getType());
                    if ("ttd_weighted_defect_rate".equals(oldTrendLineDetailInfo.getDim())) {
                        newTrendLine.setTrendLineName(TrendLineNameEnum.WEIGHT_DEFECT_RATE.getName());
                    } else {
                        newTrendLine.setTrendLineName(TrendLineNameEnum.TARGET_VALUE.getName());
                    }
                    List<OverseaTrendLineDataItem> trendLineDataItems = new ArrayList<>();
                    newTrendLine.setDataList(trendLineDataItems);
                    oldTrendLineDetailInfo.getTrendLineDataItemList().forEach(
                            oldTrendLineDataItem -> {
                                OverseaTrendLineDataItem newTrendLineDataItem = new OverseaTrendLineDataItem();
                                newTrendLineDataItem.setName(oldTrendLineDataItem.getName());
                                newTrendLineDataItem.setDate(oldTrendLineDataItem.getTime());
                                newTrendLineDataItem.setValue(oldTrendLineDataItem.getValue());
                                trendLineDataItems.add(newTrendLineDataItem);
                            }
                    );
                    trendLines.add(newTrendLine);
                }
        );
        newResponse.setTrendlines(trendLines);
        return newResponse;
    }


    /**
     * 质量趋势线接口V2请求转V1
     * @param newRequest
     * @return
     */
    protected GetOverseaTrendLineDataRequestType transform103TrendLineRequestNewToOld(GetOverseaTrendLineDataV2RequestType newRequest) {
        GetOverseaTrendLineDataRequestType oldRequest = new GetOverseaTrendLineDataRequestType();
        oldRequest.setSubMetric(newRequest.getSubMetric());
        oldRequest.setTimeFilter(newRequest.getTimeFilter());
        oldRequest.setMetric(newRequest.getMetric());
        oldRequest.setDomainName(newRequest.getDomainName());
        oldRequest.setQueryType(newRequest.getQueryType());
        DrillDownFilter drillDownFilter = new DrillDownFilter();
        drillDownFilter.setField(newRequest.getDimName());
        drillDownFilter.setFieldValueList(newRequest.getDimValueList());
        oldRequest.setDrillDownFilter(drillDownFilter);
        return oldRequest;
    }
    /**
     * 将MetricDetailInfo转换为OveaseaSubMetric
     * @param metricDetailInfo
     * @return
     */
    protected OveaseaSubMetric transformMetricInfo(MetricDetailInfo metricDetailInfo) {
        OveaseaSubMetric oveaseaSubMetric = new OveaseaSubMetric();
        oveaseaSubMetric.setSubMetric(Bus103Helper.transformSubMetricNameReverse(metricDetailInfo.getSubMetric()));
        oveaseaSubMetric.setNeedDrillDown(metricDetailInfo.isNeedDrillDown());
        oveaseaSubMetric.setDefaultField(metricDetailInfo.getDefaultField());
        oveaseaSubMetric.setMomType(metricDetailInfo.getMomType());
        if (!MapUtils.isEmpty(metricDetailInfo.getDimData())){
            for (String dimKey : metricDetailInfo.getDimData().keySet()) {
                switch (dimKey){
                    case "ttd_weighted_defect_cnt":
                        oveaseaSubMetric.setWeightedDefectCnt(metricDetailInfo.getDimData().get(dimKey));
                        break;
                    case "ttd_weighted_defect_rate_lastyear":
                        oveaseaSubMetric.setYoyValue(metricDetailInfo.getDimData().get(dimKey));
                        break;
                    case "ttd_weighted_defect_achieved_rate":
                        oveaseaSubMetric.setWeightedDefectAchievedRata(metricDetailInfo.getDimData().get(dimKey));
                        break;
                    case "ttd_pay_odr_cnt":
                        oveaseaSubMetric.setPayOrdCnt(metricDetailInfo.getDimData().get(dimKey));
                        break;
                    case "ttd_weighted_defect_rate":
                        oveaseaSubMetric.setWeightedDefectRate(metricDetailInfo.getDimData().get(dimKey));
                        break;
                    case "ttd_weighted_defect_target":
                        oveaseaSubMetric.setTargetValue(metricDetailInfo.getDimData().get(dimKey));
                        break;
                }
            }
        }
        return oveaseaSubMetric;
    }


    public GetOverseaDrillDownBaseInfoV2ResponseType getBus103SubDrillDownBaseInfo(GetOverseaDrillDownBaseInfoV2RequestType request,
                                                                                 String d,
                                                                                   OverseaMetricInfoBean metricInfoBean) throws Exception {
        String subMetric = request.getSubMetric();
        QuantitySubMetricCalStrategyV2 metricCalStrategy = quantityCalStrategyMap.get(subMetric);
        if (metricCalStrategy == null) {
            throw new StrategyException("strategy is not found!");
        }
        return metricCalStrategy.getBus103SubDrillDownBaseInfo(request, d, metricInfoBean);
    }


    public GetOverseaTableDataV2ResponseType getBus103SubTableData(GetOverseaTableDataV2RequestType request,
                                                                 String d,
                                                                   OverseaMetricInfoBean metricInfoBean) throws Exception {
        String subMetric = request.getSubMetric();
        QuantitySubMetricCalStrategyV2 metricCalStrategy = quantityCalStrategyMap.get(subMetric);
        if (metricCalStrategy == null) {
            throw new StrategyException("strategy is not found!");
        }
        return metricCalStrategy.getBus103SubTableData(request, d, metricInfoBean);
    }


    public Future<OveaseaSubMetric> getBus105106107SubMetricCardData(TimeFilter timeFilter,
                                                                     OverseaMetricInfoBeanV2 metricInfoBean,
                                                                     String d,
                                                                     String metric,
                                                                     String subMetric) throws Exception {

        CompetitorSubMetricCalStrategyV2 metricCalStrategy = competitorCalStrategyMap.get(subMetric);
        if (metricCalStrategy == null) {
            throw new StrategyException("strategy is not found!");
        }
        return metricCalStrategy.getBus105106107SubMetricCardData(timeFilter, metricInfoBean, d, metric, subMetric);
    }


    public GetOverseaTrendLineDataV2ResponseType getBus105106107SubTrendLineData(GetOverseaTrendLineDataV2RequestType request,
                                                                                 String d,
                                                                                 List<String> timeList) throws Exception {

        String subMetric = request.getSubMetric();
        CompetitorSubMetricCalStrategyV2 metricCalStrategy = competitorCalStrategyMap.get(subMetric);
        if (metricCalStrategy == null) {
            throw new StrategyException("strategy is not found!");
        }
        return metricCalStrategy.getBus105106107SubTrendLineData(request, d, timeList);
    }


    public GetOverseaDrillDownBaseInfoV2ResponseType getBus105106107SubDrillDownBaseInfo(GetOverseaDrillDownBaseInfoV2RequestType request,
                                                                                       String d,
                                                                                         OverseaMetricInfoBean metricInfoBean) throws Exception {

        String subMetric = request.getSubMetric();
        CompetitorSubMetricCalStrategyV2 metricCalStrategy = competitorCalStrategyMap.get(subMetric);
        if (metricCalStrategy == null) {
            throw new StrategyException("strategy is not found!");
        }
        return metricCalStrategy.getBus105106107SubDrillDownBaseInfo(request, d, metricInfoBean);
    }


    public GetOverseaTableDataV2ResponseType getBus105106107SubTableData(GetOverseaTableDataV2RequestType request,
                                                                       String d,
                                                                         OverseaMetricInfoBean metricInfoBean) throws Exception {

        String subMetric = request.getSubMetric();
        CompetitorSubMetricCalStrategyV2 metricCalStrategy = competitorCalStrategyMap.get(subMetric);
        if (metricCalStrategy == null) {
            throw new StrategyException("strategy is not found!");
        }
        return metricCalStrategy.getBus105106107SubTableData(request, d, metricInfoBean);
    }


    public Future<OveaseaSubMetric> getBus110SubMetricCardData(TimeFilter timeFilter,
                                                               OverseaMetricInfoBeanV2 metricInfoBean,
                                                               String d,
                                                               String subMetric,
                                                               GetOverseaMetricCardDataV2RequestType request) throws Exception {
        TicketQuantityCalStrategyV2 ticketQuantityCalStrategy = ticketQuantityCalStrategyMap.get(subMetric);
        if (ticketQuantityCalStrategy == null) {
            throw new StrategyException("strategy is not found!");
        }
        return ticketQuantityCalStrategy.getBus110SubMetricCardData(timeFilter, metricInfoBean, d, subMetric, request);
    }


    public GetOverseaTrendLineDataV2ResponseType getBus110SubTrendlineData(GetOverseaTrendLineDataV2RequestType request,
                                                                            String d,
                                                                           List<String> timeList) throws Exception {
        String subMetric = request.getSubMetric();
        TicketQuantityCalStrategyV2 ticketQuantityCalStrategy = ticketQuantityCalStrategyMap.get(subMetric);
        if (ticketQuantityCalStrategy == null) {
            throw new StrategyException("strategy is not found!");
        }
        return ticketQuantityCalStrategy.getBus110SubTrendLineData(request, d, timeList);
    }


    public GetOverseaDrillDownBaseInfoV2ResponseType getBus110SubDrillDownBaseInfo(GetOverseaDrillDownBaseInfoV2RequestType request,
                                                                                    String d,
                                                                                   OverseaMetricInfoBean metricInfoBean) throws Exception {
        String subMetric = request.getSubMetric();
        TicketQuantityCalStrategyV2 ticketQuantityCalStrategy = ticketQuantityCalStrategyMap.get(subMetric);
        if (ticketQuantityCalStrategy == null) {
            throw new StrategyException("strategy is not found!");
        }
        return ticketQuantityCalStrategy.getBus110SubDrillDownBaseInfo(request, d, metricInfoBean);
    }


    public GetOverseaTableDataV2ResponseType getBus110SubTableData(GetOverseaTableDataV2RequestType request,
                                                                    String d,
                                                                 OverseaMetricInfoBean metricInfoBean) throws Exception {
        String subMetric = request.getSubMetric();
        TicketQuantityCalStrategyV2 ticketQuantityCalStrategy = ticketQuantityCalStrategyMap.get(subMetric);
        if (ticketQuantityCalStrategy == null) {
            throw new StrategyException("strategy is not found!");
        }
        return ticketQuantityCalStrategy.getBus110SubTableData(request, d, metricInfoBean);
    }


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
        Map<String, IncomeSubMetricCalStrategyV2> incomeStrategyMap = applicationContext.getBeansOfType(IncomeSubMetricCalStrategyV2.class);
        incomeStrategyMap.values().forEach(e -> incomeCalStrategyMap.put(e.getSubMetricName(), e));
        Map<String, QuantitySubMetricCalStrategyV2> quantityStrategyMap = applicationContext.getBeansOfType(QuantitySubMetricCalStrategyV2.class);
        quantityStrategyMap.values().forEach(e->quantityCalStrategyMap.put(e.getSubMetricName(),e));
        Map<String, QuantitySubMetricCalStrategy> quantityStrategyMapV1 = applicationContext.getBeansOfType(QuantitySubMetricCalStrategy.class);
        quantityStrategyMapV1.values().forEach(e->quantityCalStrategyMapV1.put(e.getSubMetricName(),e));
        Map<String, CompetitorSubMetricCalStrategyV2> competitorStrategyMap = applicationContext.getBeansOfType(CompetitorSubMetricCalStrategyV2.class);
        competitorStrategyMap.values().forEach(e->competitorCalStrategyMap.put(e.getSubMetricName(),e));
        Map<String, TicketQuantityCalStrategyV2> ticketQuantityStrategyMap = applicationContext.getBeansOfType(TicketQuantityCalStrategyV2.class);
        ticketQuantityStrategyMap.values().forEach(e->ticketQuantityCalStrategyMap.put(e.getSubMetricName(),e));
    }

    /**
     * 修改目的地CT的取值逻辑（C为空返回T，T为空返回C）
     * @param seaSubMetrics
     */
    public void setNewSubMetricWithCT(List<OveaseaSubMetric> seaSubMetrics) {
        OveaseaSubMetric subMetricCT = new OveaseaSubMetric();
        OveaseaSubMetric subMetricC = new OveaseaSubMetric();
        OveaseaSubMetric subMetricT = new OveaseaSubMetric();
        seaSubMetrics.forEach(subMetric -> {
            if ("destinationC".equalsIgnoreCase(subMetric.getSubMetric())) {
                BeanUtils.copyProperties(subMetric, subMetricC);
                subMetricC.setNeedDrillDown(subMetric.isNeedDrillDown());
            }
            if ("destinationT".equalsIgnoreCase(subMetric.getSubMetric())) {
                BeanUtils.copyProperties(subMetric, subMetricT);
                subMetricT.setNeedDrillDown(subMetric.isNeedDrillDown());
            }
            if ("destinationCT".equalsIgnoreCase(subMetric.getSubMetric())) {
                BeanUtils.copyProperties(subMetric, subMetricCT);
                subMetricCT.setNeedDrillDown(subMetric.isNeedDrillDown());
            }
        });
        if (subMetricC.getTargetValue() != null && subMetricC.getTargetValue() == 0.0) {
            BeanUtils.copyProperties(subMetricT, subMetricCT);
            subMetricCT.setSubMetric("destinationCT");
            subMetricCT.setNeedDrillDown(subMetricT.isNeedDrillDown());
        }
        if (subMetricT.getTargetValue() != null && subMetricT.getTargetValue() == 0.0) {
            BeanUtils.copyProperties(subMetricC, subMetricCT);
            subMetricCT.setSubMetric("destinationCT");
            subMetricCT.setNeedDrillDown(subMetricC.isNeedDrillDown());
        }
        seaSubMetrics.removeIf(subMetric -> "destinationCT".equalsIgnoreCase(subMetric.getSubMetric()));
        seaSubMetrics.add(subMetricCT);
    }
}
