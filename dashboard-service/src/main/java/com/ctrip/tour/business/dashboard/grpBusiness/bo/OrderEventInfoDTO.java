package com.ctrip.tour.business.dashboard.grpBusiness.bo;

import java.util.List;

/**
 * description
 *
 * <AUTHOR>
 */
public class OrderEventInfoDTO {
    /**
     * 供应商类型事件对应的供应商
     */
    private Long providerId;

    /**
     * 事件处理人内部员工
     */
    private List<String> processorEidList;

    /**
     * 事件内容
     */
    private String content;

    /**
     * trippal信息
     */
    private TrippalInfoDTO trippalInfo;

    /**
     * 事件文件内容，仅支持文件路径方式，不支持文件流
     */
    private List<OrderEventFileInfoDTO> listFiles;


    public Long getProviderId() {
        return providerId;
    }

    public void setProviderId(Long providerId) {
        this.providerId = providerId;
    }


    public List<String> getProcessorEidList() {
        return processorEidList;
    }

    public void setProcessorEidList(List<String> processorEidList) {
        this.processorEidList = processorEidList;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public TrippalInfoDTO getTrippalInfo() {
        return trippalInfo;
    }

    public void setTrippalInfo(TrippalInfoDTO trippalInfo) {
        this.trippalInfo = trippalInfo;
    }

    public List<OrderEventFileInfoDTO> getListFiles() {
        return listFiles;
    }

    public void setListFiles(List<OrderEventFileInfoDTO> listFiles) {
        this.listFiles = listFiles;
    }
}
