package com.ctrip.tour.business.dashboard.grpBusiness.job;

import static com.ctrip.tour.business.dashboard.grpBusiness.common.GrpConstant.SELF_SERVICE_COVERAGE_RATE_SETTING;
import static com.ctrip.tour.business.dashboard.grpBusiness.common.GrpConstant.SELF_SERVICE_RATE_SETTING;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.tour.business.dashboard.grpBusiness.common.GrpConstant;
import com.ctrip.tour.business.dashboard.grpBusiness.dao.starrocks.StarRocksCommonDao;
import com.ctrip.tour.business.dashboard.grpBusiness.entity.AdmPrdGrpPrdidDepctyPrcedateOnlinePrDf;
import com.ctrip.tour.business.dashboard.grpBusiness.service.HrOrgEmpInfoService;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.group.workbenchsvc.contract.GetAlertThresholdSettingListInfoType;
import com.ctrip.tour.group.workbenchsvc.contract.GetAlertThresholdSettingListRequestType;
import com.ctrip.tour.group.workbenchsvc.contract.GetAlertThresholdSettingListResponseType;
import com.ctrip.tour.group.workbenchsvc.contract.StructuredTableInfoType;
import com.ctrip.tour.group.workbenchsvc.contract.StructuredTableRowInfoType;
import com.ctrip.tour.rights.client.GroupWorkbenchServiceClient;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2025/3/25
 */
@Service
@Slf4j
public class SlfsrvAlertNotifyService extends CommonAlertNotifyService {

    private static final String TASK_SLF_SRV_ALERT = "TASK_PROVIDER_SELF_SERVICE_NOTIFY";
    private static final String EVENT_SLF_SRV_ALERT = "EVENT_PROVIDER_SELF_SERVICE_NOTIFY";
    private static final String EVENT_SLF_SRV_ALERT_STRUCTURED_TABLE = "EVENT_PROVIDER_SELF_SERVICE_NOTIFY_STRUCTURED_TABLE";

    private static final String EVENT_SLF_SRV_ALERT_table = "EVENT_SLF_SRV_ALERT";
    private static final String ALERT_RULE_ID = "ALERT_RULE_ID";
    private static final String REDIRECT_URL = "http://vendor.package.ctripcorp.com/product/input/priceCompetitiveness?from=6381&productid=";
    @Autowired
    private HrOrgEmpInfoService hrOrgEmpInfoService;
    @Autowired
    private StarRocksCommonDao starRocksCommonDao;

    private static String NOTIFY_EVENT_EMPCODES = "notify.event.empcodes";

    private GroupWorkbenchServiceClient client = GroupWorkbenchServiceClient.getInstance();

    @Autowired
    private RemoteConfig remoteConfig;

    public void handlePmSelfSrv() throws Exception {

        String whiteEmpCodesStr = remoteConfig.getExternalConfig(NOTIFY_EVENT_EMPCODES);

        List<String> whiteEmpCodes = Lists.newArrayList();
        if (StringUtils.isNotBlank(whiteEmpCodesStr)) {
            whiteEmpCodes = Splitter.on(",").splitToList(whiteEmpCodesStr);
        }

        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        String sql = "SELECT pm_eid, count(DISTINCT self_sev_uid)\n" +
                "FROM cdm_sev_grp_platform_self_srv_summary_df\n" +
                "WHERE partition_d = '" + LocalDate.now().format(dtf) + "'\n" +
                "    AND sub_bu_type = '跟团游'\n" +//NOSONAR
                "    AND self_sev_uid != 'null'\n" +
                "    AND chat_create_date = '" + LocalDate.now().minusDays(1).format(dtf) + "'\n" +
                "GROUP BY pm_eid\n" +
                "HAVING count(DISTINCT self_sev_uid) > 0;";

        List<Map<String, Object>> resultList = starRocksCommonDao.query(sql, Maps.newHashMap());
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }

        List<String> finalWhiteEmpCodes = whiteEmpCodes;
        List<String> pmEids = resultList.stream()
                .filter(pw -> CollectionUtils.isEmpty(finalWhiteEmpCodes) || (Objects.nonNull(pw) && finalWhiteEmpCodes.contains((String) pw.get("pm_eid"))))
                .map(pw -> (String) pw.get("pm_eid"))
                .collect(Collectors.toList());

        for (String pmEid : pmEids) {

            String mixSql = "SELECT vendor_id,vendor_name, dest_city_name, count(distinct tor_fail_distr_cnsl_sid) / count(distinct session_id) as fail_rate,\n" +
                    "sum( reply_120s_cnt)/sum(total_message_cnt) as timely_resp_rate,\n" +
                    "count(distinct suc_ord_uid)/count(distinct uid) as coversion_rate,\n" +
                    "count(distinct uid)/count(distinct case when second_session_type in( '商户自服务会话' ,'携程服务会话')then sev_uid else 0 end)  as coverage_rate\n" +//NOSONAR
                    "FROM cdm_sev_grp_platform_self_srv_summary_df\n" +
                    "WHERE partition_d = '" + LocalDate.now().format(dtf) + "'\n" +
                    "    AND sub_bu_type = '跟团游'\n" +//NOSONAR
                    "    AND pm_eid = '" + pmEid + "'\n" +
                    "    AND self_sev_uid != 'null'\n" +
                    "    AND chat_create_date = '" + LocalDate.now().minusDays(1).format(dtf) + "' group by vendor_id,vendor_name, dest_city_name";
            List<Map<String, Object>> query = starRocksCommonDao.query(mixSql, Maps.newHashMap());

            String tranReviewScoreSql = "SELECT vendor_id,vendor_name,dest_city_name,  avg(avg_score) as tran_review_score\n" +
                    "FROM (\n" +
                    "    SELECT vendor_id, vendor_name,dest_city_name, sum(op_avg_score) / count(distinct session_evaluate_id) as avg_score\n" +
                    "    FROM cdm_sev_grp_platform_self_srv_summary_df\n" +
                    "    WHERE partition_d = '" + LocalDate.now().format(dtf) + "'\n" +
                    "        AND sub_bu_type = '跟团游'\n" +//NOSONAR
                    "        AND pm_eid = '" + pmEid + "'\n" +
                    "        AND op_avg_score IS NOT NULL\n" +
                    "        AND self_sev_uid IS NOT NULL\n" +
                    "        AND chat_create_date >= '" + LocalDate.now().minusDays(5).format(dtf) + "'\n" +
                    "        AND chat_create_date <= '" + LocalDate.now().minusDays(1).format(dtf) + "'\n" +
                    "    GROUP BY session_evaluate_id, vendor_id,vendor_name, dest_city_name\n" +
                    ") t GROUP BY vendor_id,vendor_name, dest_city_name ;";

            List<Map<String, Object>> tranReviewScoreResult = starRocksCommonDao.query(tranReviewScoreSql, Maps.newHashMap());

            Double failRate = null;
            Double timelyRespRate = null;
            Double coversionRate = null;
            Double coverageRate = null;
            Double tranReviewScore = null;
            Long vendorId = null;
            String cityName = null;
            String vendorName = null;

            if (CollectionUtils.isNotEmpty(query)) {
                failRate = (Double) query.get(0).get("fail_rate");
                timelyRespRate = (Double) query.get(0).get("timely_resp_rate");
                coversionRate = (Double) query.get(0).get("coversion_rate");
                coverageRate = (Double) query.get(0).get("coverage_rate");
                vendorId = (Long) query.get(0).get("vendor_id");
                cityName = (String) query.get(0).get("dest_city_name");
                vendorName = (String) query.get(0).get("vendor_name");

            }
            if (CollectionUtils.isNotEmpty(tranReviewScoreResult)) {

                Long finalVendorId = vendorId;
                String finalCityName = cityName;
                Map<String, Object> scoreMap = tranReviewScoreResult.stream().filter(trs -> {
                    return Objects.equals(finalVendorId, (Long) trs.get("vendor_id"))
                            && StringUtils.equalsIgnoreCase(finalCityName, (String) trs.get("dest_city_name"));
                }).findFirst().orElse(null);

                if (MapUtils.isNotEmpty(scoreMap)) {
                    tranReviewScore = (Double) scoreMap.get("tran_review_score");
                }

            }

            StructuredTableInfoType structuredTableInfoType = new StructuredTableInfoType();
            StructuredTableRowInfoType rowInfoType = new StructuredTableRowInfoType();
            List<String> colList = Lists.newArrayList(
                    vendorId + "",
                    vendorName,
                    cityName,
                    Objects.isNull(coverageRate)? "":getDataRatioStr(coverageRate),
                    Objects.isNull(coversionRate)? "":getDataRatioStr(coversionRate),
                    Objects.isNull(failRate)? "":getDataRatioStr(failRate),
                    Objects.isNull(timelyRespRate)? "":getDataRatioStr(timelyRespRate),
                    Objects.isNull(tranReviewScore)? "":getPrettyDataStr(tranReviewScore));
            rowInfoType.setColList(colList);
            ArrayList<StructuredTableRowInfoType> rowInfoTypes = Lists.newArrayList(rowInfoType);
            structuredTableInfoType.setRowList(rowInfoTypes);
            structuredTableInfoType.setHeaderList(Lists.newArrayList("供应商id","供应商名称", "咨询目的地","覆盖率","用户转化率","咨询流失率", "回复及时率",   "近5日点评均分"));//NOSONAR
            String content = "客人浏览商品，有效咨询 uv>0 的产品范围;";//NOSONAR
            List<String> tpInfos = Lists.newArrayList("客人浏览商品，有效咨询 uv>0 的产品范围;");//NOSONAR
            notifyEmp(pmEid, structuredTableInfoType, "商家自服务通知",//NOSONAR
                    TASK_SLF_SRV_ALERT, EVENT_SLF_SRV_ALERT, EVENT_SLF_SRV_ALERT_STRUCTURED_TABLE, content, tpInfos, "1");//NOSONAR
        }

    }

    private void handleLocalPmSelfSrv() throws Exception {

        String whiteEmpCodesStr = remoteConfig.getExternalConfig(NOTIFY_EVENT_EMPCODES);

        List<String> whiteEmpCodes = Lists.newArrayList();
        if (StringUtils.isNotBlank(whiteEmpCodesStr)) {
            whiteEmpCodes = Splitter.on(",").splitToList(whiteEmpCodesStr);
        }

        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        String sql = "SELECT local_pm_eid, count(DISTINCT self_sev_uid)\n" +
                "FROM dw_diydb.cdm_sev_grp_platform_self_srv_summary_df\n" +
                "WHERE d = '" + LocalDate.now().format(dtf) + "'\n" +
                "    AND sub_bu_type = '独立出游'\n" +//NOSONAR
                "    AND self_sev_uid != 'null'\n" +
                "    AND local_pm_eid != 'unkwn'\n" +
                "    AND chat_create_date = '" + LocalDate.now().minusDays(1).format(dtf) + "'\n" +
                "GROUP BY local_pm_eid\n" +
                "HAVING count(DISTINCT self_sev_uid) > 0;";

        List<Map<String, Object>> resultList = starRocksCommonDao.query(sql, Maps.newHashMap());
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }

        List<String> finalWhiteEmpCodes = whiteEmpCodes;
        List<String> pmEids = resultList.stream()
                .filter(pw -> CollectionUtils.isEmpty(finalWhiteEmpCodes) || (Objects.nonNull(pw) && finalWhiteEmpCodes.contains((String) pw.get("pm_eid"))))
                .map(pw -> (String) pw.get("pm_eid"))
                .collect(Collectors.toList());

        for (String pmEid : pmEids) {

            String mixSql = "SELECT count(distinct tor_fail_distr_cnsl_sid) / count(distinct session_id) as fail_rate,\n" +
                    "sum( reply_120s_cnt)/sum(total_message_cnt) as timely_resp_rate,\n" +
                    "sum(op_avg_score) / count(distinct session_evaluate_id) as avg_score,\n" +
                    "count(distinct suc_ord_uid)/count(distinct uid) as coversion_rate,\n" +
                    "count(distinct uid)/count(distinct case when second_session_type in( '商户自服务会话' ,'携程服务会话')then sev_uid else 0 end)  as coverage_rate\n" +//NOSONAR
                    "FROM dw_diydb.cdm_sev_grp_platform_self_srv_summary_df\n" +
                    "WHERE d = '" + LocalDate.now().format(dtf) + "'\n" +
                    "    AND sub_bu_type = '跟团游'\n" +//NOSONAR
                    "    AND local_pm_eid = '" + pmEid + "'\n" +
                    "    AND self_sev_uid != 'null'\n" +
                    "    AND chat_create_date = '" + LocalDate.now().minusDays(1).format(dtf) + "'";
            List<Map<String, Object>> query = starRocksCommonDao.query(mixSql, Maps.newHashMap());


            Double failRate = null;
            Double timelyRespRate = null;
            Double coversionRate = null;
            Double coverageRate = null;
            Double tranReviewScore = null;

            if (CollectionUtils.isNotEmpty(query)) {
                failRate = (Double) query.get(0).get("fail_rate");
                timelyRespRate = (Double) query.get(0).get("timely_resp_rate");
                coversionRate = (Double) query.get(0).get("coversion_rate");
                coverageRate = (Double) query.get(0).get("coverage_rate");
                tranReviewScore = (Double) query.get(0).get("avg_score");

            }
            StructuredTableInfoType structuredTableInfoType = new StructuredTableInfoType();
            StructuredTableRowInfoType rowInfoType = new StructuredTableRowInfoType();
            List<String> colList = Lists.newArrayList(
                    getDataRatioStr(failRate),
                    getDataRatioStr(timelyRespRate),
                    getDataRatioStr(coversionRate),
                    getDataRatioStr(coverageRate),
                    getPrettyDataStr(tranReviewScore));
            rowInfoType.setColList(colList);
            ArrayList<StructuredTableRowInfoType> rowInfoTypes = Lists.newArrayList(rowInfoType);
            structuredTableInfoType.setRowList(rowInfoTypes);
            structuredTableInfoType.setHeaderList(Lists.newArrayList("流失率", "响应率", "转化率", "覆盖率", "近5日点评均分"));//NOSONAR
            String content = "自服务指标，请关注";//NOSONAR
            List<String> tpInfos = Lists.newArrayList("自服务指标，请关注");//NOSONAR
            notifyEmp(pmEid, structuredTableInfoType, "自服务通知",//NOSONAR
                    TASK_SLF_SRV_ALERT, EVENT_SLF_SRV_ALERT, EVENT_SLF_SRV_ALERT_STRUCTURED_TABLE, content, tpInfos, "1");//NOSONAR
        }

    }


}
