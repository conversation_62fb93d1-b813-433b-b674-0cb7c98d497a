package com.ctrip.tour.business.dashboard.tktBusiness.strategy.impl;

import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ExamineConfigBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.SinglePeriodDataBean;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.SinglePeriodTrendLineBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.Bus8Dao;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardExamineeConfigV2Dao;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.Bus8Helper;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.ChartHelper;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.MetricHelper;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.MultiPeriodMappingHelper;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.MetricCalStrategy;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @date 2022/7/29
 */
@Component
public class Bus8MetricStrategy implements MetricCalStrategy {

    @Autowired
    private Bus8Dao bus8Dao;

    @Autowired
    private BusinessDashboardExamineeConfigV2Dao examineeConfigV2Dao;

    @Autowired
    private SinglePeriodTrendLineBiz singlePeriodTrendLineBiz;


    @Override
    public Future<MetricDetailInfo> getSingleMetricCardData(String domainName,
                                                            TimeFilter timeFilter,
                                                            MetricInfoBean metricInfoBean,
                                                            String d,
                                                            Boolean needRank) throws Exception {
        MetricDetailInfo metricDetailInfo = new MetricDetailInfo();
        String metric = metricInfoBean.getMetric();
        Map<String, Double> dimMap = new HashMap<>();
        metricDetailInfo.setDimData(dimMap);
        metricDetailInfo.setMetric(metric);

        String year = timeFilter.getYear();
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();
        String dateType = MetricHelper.getDateTypeColumnName(timeFilter.getDateType());

        String level = MetricHelper.getLevelColumnName(metricInfoBean.getLevel());

        List<String> regionList = metricInfoBean.getRegionList();

        DalHints rankDalHints = new DalHints().asyncExecution();
        if (needRank) {
            getRankDataAsync(dateType, year, quarter, month, d, domainName, metric, rankDalHints);
        }

        //由于mysql需要设置索引 因此需要保持顺序
        Map<String, List<String>> inMap = new LinkedHashMap<>();
        inMap.put("query_d", Lists.newArrayList(d));
        inMap.put("year", Lists.newArrayList(year));
        inMap.put("date_type",Lists.newArrayList(dateType));
        if("month".equals(dateType)){
            inMap.put("month",Lists.newArrayList(month));
        }else{
            inMap.put("quarter",Lists.newArrayList(quarter));
        }
        if (!"".equals(level)) {
            inMap.put(level, regionList);
        }
        List<List<Object>> reachList = bus8Dao.getMetricCardData(inMap);
        List<String> dimList = Bus8Helper.getDimList();
        ChartHelper.fillOverallDimMap(reachList, dimList, dimMap);


        Bus8Helper.makeUpMetricCardData(dimMap);

        MetricHelper.setMetricCardDrillDownInfo(metricInfoBean, metricDetailInfo);

        //获取排名数据
        if (needRank) {
            ChartHelper.fillRankData(metricDetailInfo, rankDalHints.getListResult());
        }

        return new AsyncResult<>(metricDetailInfo);
    }

    @Override
    public GetTrendLineDataResponseType getSingleTrendlineData(GetTrendLineDataRequestType request,
                                                               MetricInfoBean metricInfoBean,
                                                               String d) throws Exception {

        GetTrendLineDataResponseType response = new GetTrendLineDataResponseType();
        List<TrendLineDetailInfo> trendLineDetailInfoList = new ArrayList<>();
        response.setTrendLineDetailInfoList(trendLineDetailInfoList);

        String queryType = request.getQueryType();
        TimeFilter timeFilter = request.getTimeFilter();
        DrillDownFilter drillDownFilter = request.getDrillDownFilter();



        String domainName = request.getDomainName();
        List<ExamineConfigBean> examineConfigBeanList = MultiPeriodMappingHelper.getMultiPeriodConfigList(timeFilter, d, examineeConfigV2Dao.queryMetricAllConfig(domainName, d, getMetricName()), null);
        List<Future<SinglePeriodDataBean>> futureList = new ArrayList<>();
        for (ExamineConfigBean examineConfigBean : examineConfigBeanList) {
            futureList.add(singlePeriodTrendLineBiz.getBus8SinglePeriodTrendLineData(request, examineConfigBean, d));
        }
        List<List<Object>> reachList = new ArrayList<>();
        for (Future<SinglePeriodDataBean> futureResult : futureList) {
            SinglePeriodDataBean singlePeriodDataBean = futureResult.get();
            List<List<Object>> periodReachList = singlePeriodDataBean.getPeriodReachList();
            if (!GeneralUtil.isEmpty(periodReachList)) {
                reachList.addAll(periodReachList);
            }
        }


        Map<String, Double> dimMap = new HashMap<>();
        Set<String> drillDownSet = new HashSet<>();
        List<String> dimList = Bus8Helper.getDimList();
        List<String> groupTagList = Lists.newArrayList("time");
        if ("drilldown".equals(queryType)) {
            String field = MetricHelper.getDrillDownColumnName(drillDownFilter.getField());
            groupTagList.add(field);
        }

        ChartHelper.setMonthlyMetricTrendlineActualReach(dimMap, drillDownSet, reachList, groupTagList, dimList);
        //有效时间范围
        List<String> timeList = DateUtil.getAllSelectedTime(timeFilter, "domestic");

        if ("drilldown".equals(queryType)) {
            ChartHelper.fillLineChartTrendLineDataWithDrillDown(null, timeList, dimMap,
                    trendLineDetailInfoList, Bus8Helper.getLineChartTrendlineTypeWithDrillDown(), drillDownSet, false);
        } else {
            ChartHelper.fillLineChartTrendLineData(null, timeList, dimMap,
                    trendLineDetailInfoList, Bus8Helper.getLineChartTrendlineType());
        }
        return response;
    }

    @Override
    public GetTableDataResponseType getSingleTableData(GetTableDataRequestType request,
                                                       MetricInfoBean metricInfoBean,
                                                       String d) throws Exception {
        GetTableDataResponseType response = new GetTableDataResponseType();
        List<TableDataItem> tableDataItemList = new ArrayList<>();
        response.setTableDataItemList(tableDataItemList);

        TimeFilter timeFilter = request.getTimeFilter();
        String year = timeFilter.getYear();
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();
        String dateType = MetricHelper.getDateTypeColumnName(timeFilter.getDateType());
        String level = MetricHelper.getLevelColumnName(metricInfoBean.getLevel());
        List<String> regionList = metricInfoBean.getRegionList();
        Integer pageNo = request.getPageNo();
        Integer pageSize = request.getPageSize();
        if (pageSize == null) {
            pageSize = 20;
        }

        DrillDownFilter drillDownFilter = request.getDrillDownFilter();
        String field = MetricHelper.getDrillDownColumnName(drillDownFilter.getField());
        List<String> fieldValueList = drillDownFilter.getFieldValueList();

        List<String> groupTagList = MetricHelper.getTableDrillDownGroupList(field);
        Map<String, List<String>> inMap = new LinkedHashMap<>();
        inMap.put("query_d", Lists.newArrayList(d));
        inMap.put("year", Lists.newArrayList(year));
        inMap.put("date_type", Lists.newArrayList(dateType));
        if ("month".equals(dateType)) {
            inMap.put("month", Lists.newArrayList(month));
        } else {
            inMap.put("quarter", Lists.newArrayList(quarter));
        }
        if (!"".equals(level)) {
            inMap.put(level, regionList);
        }
        if (!GeneralUtil.isEmpty(fieldValueList)) {
            inMap.put(field, fieldValueList);
        }
        List<List<Object>> reachList = bus8Dao.getTableData(inMap, groupTagList, pageNo, pageSize);
        Integer totalNum = bus8Dao.getTableDataCount(inMap, groupTagList);
        response.setTotalNum(totalNum);

        List<String> dimList = Bus8Helper.getDimList();
        ChartHelper.fillCommmonTableData(tableDataItemList, groupTagList, dimList, new ArrayList<>(), reachList, new ArrayList<>());
        Bus8Helper.makeUpTableData(tableDataItemList);
        return response;
    }

    @Override
    public GetDrillDownBaseInfoResponseType getSingleDrillDownBaseInfo(GetDrillDownBaseInfoRequestType request,
                                                                       MetricInfoBean metricInfoBean,
                                                                       String d) throws Exception {
        GetDrillDownBaseInfoResponseType response = new GetDrillDownBaseInfoResponseType();
        List<FieldDataItem> fieldDataItemList = new ArrayList<>();
        response.setFieldDataItemList(fieldDataItemList);

        TimeFilter timeFilter = request.getTimeFilter();
        String year = timeFilter.getYear();
        String dateType = timeFilter.getDateType();
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();

        String metric = metricInfoBean.getMetric();
        List<String> regionList = metricInfoBean.getRegionList();
        String level = metricInfoBean.getLevel();//国内  三方  大区  省份
        String levelColumnName = MetricHelper.getLevelColumnName(level);

        boolean needSearch = request.isNeedSearch();
        String searchWord = request.getSearchWord();
        List<String> fieldList = Bus8Helper.getFieldList(level);

        if(needSearch){
            String searchField = request.getSearchField();//大区 省份

            if (searchField != null) {
                String searchFieldColumnName = MetricHelper.getDrillDownColumnName(searchField);
                fieldList = Lists.newArrayList(searchFieldColumnName);
            }

        }else{
            response.setDefaultChosenField(MetricHelper.getDefaultChosenField(level, metric));
        }
        Map<String, List<String>> inMap = new LinkedHashMap<>();
        inMap.put("query_d", Lists.newArrayList(d));
        inMap.put("year", Lists.newArrayList(year));
        if ("month".equals(dateType)) {
            inMap.put("month", Lists.newArrayList(month));
        } else {
            inMap.put("quarter", Lists.newArrayList(quarter));
        }
        if (!"".equals(levelColumnName)) {
            inMap.put(levelColumnName, regionList);
        }
        for (String field : fieldList) {
            List<String> tagList = Lists.newArrayList(field);
            Map<String,String> likeMap = new HashMap<>();
            if(needSearch){
                likeMap.put(field,searchWord);
            }
            List<List<Object>> rawObjectList = bus8Dao.getFieldList(inMap, likeMap, tagList);
            ChartHelper.fillFieldDataItemList(field, rawObjectList, fieldDataItemList);
        }
        return response;
    }

    private void getRankDataAsync(String dateType,
                                  String year,
                                  String quarter,
                                  String month,
                                  String d,
                                  String domainName,
                                  String metric,
                                  DalHints dalHints) throws Exception {
        Map<String, List<String>> inMap = new LinkedHashMap<>();
        inMap.put("query_d", Lists.newArrayList(d));
        inMap.put("examinee", Lists.newArrayList(domainName));
        inMap.put("year", Lists.newArrayList(year));
        if ("month".equals(dateType)) {
            inMap.put("month", Lists.newArrayList(month));
        } else {
            String mappingMonth = DateUtil.getMappingMonthByQuarter(d, year, quarter);
            inMap.put("month", Lists.newArrayList(mappingMonth));
        }
        inMap.put("metric", Lists.newArrayList(metric));
        bus8Dao.getRankAsync(inMap, dalHints);
    }




    @Override
    public String getMetricName() {
        return "8";
    }
}
