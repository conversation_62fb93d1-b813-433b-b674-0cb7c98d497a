include 'BaijiCommonTypes.bjsc'
namespace java'com.ctrip.soa._24922'

class CompetitiveData {
    double inferiorNum;
    double gapValue;
    double targetValue;
    double inferiorRate;
}


class CompetitiveType {
    CompetitiveData coreInfo;
    CompetitiveData focusInfo;
    CompetitiveData generalInfo;
}

class OveaseaSubMetric {
    string subMetric;
    double completeValue;
    double targetValue;
    double completeRate;
    double yoyValue;
    double popValue;
    CompetitiveType klkCompetitiveData;
    CompetitiveType flyCompetitiveData;
    double weightedDefectRate;
    double payOrdCnt;
    double weightedDefectCnt;
    double weightedDefectAchievedRata;
    bool needDrillDown;
    string defaultField;
    string momType;
}

class OveaseaMetric {
    string metric;

    list<OveaseaSubMetric> subMetricList;
}

class TimeFilter {
    string dateType;
    string year;
    string month;
    string quarter;
    string half;
    int timeFrame;
}


class GetOverseaMetricCardDataV2RequestType {
    string domainName;
    TimeFilter timeFilter;
    int businessLine;
}

class GetOverseaTrendLineDataV2RequestType {
    string domainName;
    TimeFilter timeFilter;
    int businessLine;
    string metric;
    string subMetric;
    string queryType;
    string dimName;
    list<string> dimValueList;
}

class OverseaTrendLineDataItem {
    string name;
    string date;
    double value;
}


class OverseaTrendLine {
    string trendLineName;
    string trendLineType;
    list<OverseaTrendLineDataItem> dataList;
}

class GetOverseaTrendLineDataV2ResponseType {
    list<OverseaTrendLine> trendlines;
    BaijiCommonTypes.ResponseStatusType responseStatus;
}


class GetOverseaDrillDownBaseInfoV2RequestType {
    TimeFilter timeFilter;
    string metric;
    string subMetric;
    int businessLine;
    string domainName;
    string searchInDim;
    string searchWord;
}

class DimValueType {
    string id;
    string dimValue;
}

class DilldownDim {
    string dimName;
    list<DimValueType> dimValueList;
    bool needBubble;
    bool needTrendLine;
}

class GetOverseaDrillDownBaseInfoV2ResponseType {
    string defaultChosenDim;
    list<DilldownDim> dimList;
    BaijiCommonTypes.ResponseStatusType responseStatus;
}

class GetOverseaTableDataV2RequestType {
    string domainName;
    TimeFilter timeFilter;
    int businessLine;
    string metric;
    string subMetric;
    string dimName;
    list<string> dimValueList;
    int pageNo;
    int pageSize;
    string queryType;
    string querySource;
}

class OverseaTableDataRow {
    long regionId;
    string region;
    long subRegionId;
    string subRegion;
    string country;
    string examinee;
    int viewspotId;
    string viewspot;
    string site;
    string locale;
    string channel;
    long vendorId;
    string vendorName;
    double targetValue;
    double completeValue;
    double completeRate;
    double yoyValue;
    double popValue;
    double inferiorNum;
    double gapValue;
    double weightedDefectRate;
    double payOrdCnt;
    double weightedDefectCnt;
}

class GetOverseaTableDataV2ResponseType {
    list<string> tableHeaderList;
    list<OverseaTableDataRow> rows;
    int totalNum;
    string momType;
    BaijiCommonTypes.ResponseStatusType responseStatus;
}

class GetOverseaMetricCardDataV2ResponseType {
    list<OveaseaMetric> metricList;
    BaijiCommonTypes.ResponseStatusType responseStatus;
}

class GetOverseaMetricCardDataV2RequestType {
    string domainName;
    TimeFilter timeFilter;
    int businessLine;
}


class GetTicketPKEnumDataResponseType {
    list<EnumData> enumDataList;
    BaijiCommonTypes.ResponseStatusType responseStatus;
}

class EnumData{
    string key;
    string value;
    list<long> idList;
}

class GetTicketPKEnumDataRequestType {
    //供应商id(数据参谋侧必传项)
    long vendorId;
    //业务邮箱前缀(业务工作台侧必传项) pme
    string domainName;
    //公共枚举值有 channel(售卖渠道)  platform(终端)  scenicSpot(景点)  业务工作台独有的枚举值有  vendor(供应商)   product(产品)
    string type;
    //搜索词
    string searchWord;
    //当type=product 时，需要传景点Id
    long scenicSpotId;
}

class GetTicketPKSaleunitRankingResponseType {
    int totalNum;
    list<TicketPKSaleunitItem> saleunitItemList;
    BaijiCommonTypes.ResponseStatusType responseStatus;
}

class TicketPKSaleunitItem {
    long saleunitId;
    string saleunitName;
    //班期
    int schedule;
}

class GetTicketPKSaleunitRankingRequestType {
    TicketPkBaseQuery query;
    //售卖状态(0/null:不在售1:在售中)
    int status;
    int pageNo;
    int pageSize;
}

class TicketPkBaseQuery {
    //售卖渠道(locale)
    string channel;
    //终端(channel)
    int platform;
    //景点id
    long scenicSpotId;
    //供应商id
    long vendorId;
    //产品id
    long productId;
    //业务邮箱前缀
    string domainName;
    //票种id
    long saleUnitId;
    //资源id
    long resourceId;
    //票种id list
    list<long> saleUnitIdList;
}


class GetTicketPKResourceRankingResponseType {
    int totalNum;
    list<TicketPkResourceItem> resourceItemList;
    BaijiCommonTypes.ResponseStatusType responseStatus;
}

class TicketPkResourceItem {
    long resourceId;
    string resourceName;
    // 产品Id url拼接使用
    long productId;
    // 供商ID url拼接使用
    long vendorId;
    // 合同ID url拼接使用
    long contractId;
    // 对接码ID url拼接使用
    long connectNoId;
    //班期
    int schedule;
}

class GetTicketPKResourceRankingRequestType {
    TicketPkBaseQuery query;
    //售卖状态(0/null:不在售; 1:在售中)
    int status;
    int pageNo;
    int pageSize;
}

class GetTicketPKScheduleCalendarResponseType {
    list<ScheduleCalendarItem> scheduleCalendarItemList;
    BaijiCommonTypes.ResponseStatusType responseStatus;
}


class ScheduleCalendarItem {
    string date;
    //售卖状态(0：不在售/ 1：在售中 )
    int status;
    //抽签中标识(0:不是抽签中状态， 1：抽签中状态)
    int roundStatus;
    // 不可售原因code
    string roundCode;
}

class GetTicketPKScheduleCalendarRequestType {
    TicketPkBaseQuery query;
}


class GetTicketPKQualificationResponseType {
    //是否有竞争资格  true为有  false为没有
    bool havePKQualification;
    //资源上货相关不满足原因list
    list<ErrorDetailInfoItem> resourceShelfErrorDetailInfoList;
    //平台最低标准相关不满足原因list
    list<ErrorDetailInfoItem> platformStandardErrorDetailInfoList;
    //小百科 常驻在页面最下方的内容
    list<ErrorDetailInfoItem> resideErrorDetailInfoList;
    BaijiCommonTypes.ResponseStatusType responseStatus;
}


class ErrorDetailInfoItem {
    //从陶宇接口获取的失败状态码
    string code;
    //该code归属的类型  1 表示资源上货  2表示平台最低标准
    int errorType;
    //该code在特定错误类型中的排序 决定了前端的展示顺序 值越小 越靠前
    int sort;
    //3表示数据参谋  4表示业务工作台
    int type;
    //指标名称对应的sharkKey
    string nameSharkKey;
    //指标注释对应的sharkKey
    string explainSharkKey;
    //指标值
    double value;
    //指标值对应的sharkKey  前端展示规则  valueSharkKey+value;
    string valueSharkKey;
    // 0或null：表示无value返回; 1：表示value有值，需要特殊处理
    int valueHandlderType;
    //其他对应的值
    string other;
    //其他配置的sharkKey
    string otherSharkKey;
    //操作文案对应的sharkKey
    string operationSharkKey;
    //操作对应的跳转url 不配置不能跳转
    string operationUrl;
}

class GetTicketPKQualificationRequestType {
    TicketPkBaseQuery query;
    //售卖状态(0：不在售/ 1：在售中 )
    int status;
    // 合同ID url拼接使用
    long contractId;
    // 对接码ID url拼接使用
    long connectNoId;
    //请求来源 vbk(数据参谋)  offline(业务工作台)
    string source;
    //当前选中的时间
    string date;
}


class GetTicketPKTableResponseType {
    //pk状态 1：未参与;2.胜出;3.淘汰;4.抽签中
    int pkStatus;
    //pk状态对应的话术sharkKey
    string pkSharkKey;
    //pk状态 提示文案
    string pkExplainSharkKey;
    //竞争力pk项 和 优劣标准对应的信息
    list<PowerIntroduceItem> powerIntroduceList;
    //我的资源对应的信息
    TicketPKResourceRecall myResourceInfo;
    //竞对最后一名的资源对应信息(只有在我的资源不在售的情况下 才会出)
    TicketPKResourceRecall lastOrderResourceInfo;
    //竞对对应的信息(如果只有一个 则不出竞对切换按钮)
    list<TicketPKResourceRecall> rivalResourceInfoList;
    BaijiCommonTypes.ResponseStatusType responseStatus;
}

class PowerIntroduceItem {
    //商品力code
    string code;
    string codeSharkKey;
    string codeExplainSharkKey;
    //商品力优劣顺序
    list<DetailCodeItem> detailCodeList;
    //拼接商品力优劣顺序运算符
    string operator;
    //如果有额外信息 拼接在商品力优劣顺序后面 且必定高亮
    string addtionalInfo;
    //类型 1表示服务 2表示产品
    string type;
    //排序
    int sort;
}

class DetailCodeItem {
    string detailCode;
    string detailCodeSharkKey;
    //是否高亮
    bool needHighLight;
}

class TicketPKResourceRecall {
    //资源Id
    long resourceId;
    list<ResourceRecallItem> resourceRecallList;
    int sort;
    //售卖状态(0：不在售/ 1：在售中 )
    int status;
    //抽签中标识(0:不是抽签中状态， 1：抽签中状态)
    int roundStatus;
}

class ResourceRecallItem {
    //商品力code
    string code;
    string codeSharkKey;
    //具体code
    string detailCode;
    string detailCodeSharkKey;
    //具体的值
    string detailValue;
    //击败的竞争对手百分比
    decimal pkPercenTage;
    string operationSharkKey;
    string operationUrl;
    //是否大拇指
    bool best;
    //类型 1表示服务 2表示产品
    string type;
    //排序
    int sort;

}

class GetTicketPKTableRequestType {
    TicketPkBaseQuery query;
    //是否有竞争资格  true为有  false为没有
    bool havePKQualification;
    //售卖状态(0：不在售/ 1：在售中 )
    int status;
    //抽签中标识(0:不是抽签中状态， 1：抽签中状态)
    int roundStatus;
    // 不可售原因code
    string roundCode;
    //当前选中的时间
    string date;

}

class GetTicketPKBestStatusResponseType {
    list<TicketPKResourceRecall> resourceBestInfoList;
    BaijiCommonTypes.ResponseStatusType responseStatus;
}

class GetTicketPKBestStatusRequestType {
    TicketPkBaseQuery query;
    //当前选中的资源id列表
    list<long> resourceIdList;
    //当前选中的时间
    string date;
}

class GetTicketPKDefectOverallDataResponseType {
    //考核期开始时间
    string startDate;
    //考核期结束时间
    string endDate;
    //景点id
    string viewspotId;
    //景点名称
    string viewspotName;
    //票种名称
    string saleUnitName;
    //缺陷订单数(缺陷率分子)
    long defectOdrCnt;
    //原始订单量(缺陷率分母)
    long oriOdrCnt;
    //缺陷率
    double defectOdrRate;
    //几个问题数据的汇总值
    list<TicketPKDefectReasonItem> defectReasonList;
    BaijiCommonTypes.ResponseStatusType responseStatus;
}

class TicketPKDefectReasonItem {
    string id;
    string name;
    int value;
    /*汇总表头 几个id需要sharkkey*/
    string explainSharkKey;
}

class GetTicketPKDefectOverallDataRequestType {
    TicketPkBaseQuery query;
}

class GetTicketPKDefectTableDataResponseType {
    int totalNum;
    list<TicketPKDefectResourceItem> defectResourceList;
    BaijiCommonTypes.ResponseStatusType responseStatus;
}

class TicketPKDefectResourceItem {
    long resourceId;
    string resourceName;
    //几个问题数据的汇总值
    list<TicketPKDefectReasonItem> defectReasonItemList;
}

class GetTicketPKDefectTableDataRequestType {
    TicketPkBaseQuery query;
    int pageNo;
    int pageSize;
}

class GetTicketPKDefectOrderIdResponseType {
    list<string> orderIdList;
    BaijiCommonTypes.ResponseStatusType responseStatus;
}

class GetTicketPKDefectOrderIdRequestType {
    TicketPkBaseQuery query;
    //指标id
    string targetId;
}


class GetMixProductPreviewStatusRequestType {
    /* 请求基本信息 */
    GetRequestBaseDataType requestBaseData;
    /* 产品id/资源id */
    long mixProductId;
}

class GetRequestBaseDataType {
    /* 操作用户 */
    string dataOperator;

    /* 扩展参数，一般情况无需传值 */
    list<ExtensionParameterType> extParameterList;
}

/* 扩展参数 */
class ExtensionParameterType {

    /* 键，大小写不敏感 */
    string key;

    /* 值，大小写不敏感 */
    string value;
}


class GetMixProductPreviewStatusResponseType {
    /* 框架基本信息 */
    BaijiCommonTypes.ResponseStatusType responseStatus;
    /* 响应基本信息 */
    GetResponseBaseDataType responseBaseData;
    /*是否可预览*/
    bool canPreview;
    /* 不可预览原因 */
    list<string> cannotPreviewReason;
}

class GetResponseBaseDataType {

    /* 是否成功，部分失败返回false */
    bool success;

    /* 错误编码 */
    string errorCode;

    /* 业务错误信息 */
    string message;
}


class GetRequestBaseDataType {

    /* 操作用户 */
    string dataOperator;

    /* 扩展参数，一般情况无需传值 */
    list<ExtensionParameterType> extParameterList;
}

class GetGrpDestProvListResponseType {
    BaijiCommonTypes.ResponseStatusType responseStatus;
    list<string> provNameList;
}

class GetGrpDestProvListRequestType{

}

@serviceName='BIBusinessDashboardService'
@serviceNamespace='http://soa.ctrip.com/_24922'
service BIBusinessDashboardService {

GetOverseaMetricCardDataV2ResponseType getOverseaMetricCardDataV2(GetOverseaMetricCardDataV2RequestType request);

GetOverseaTrendLineDataV2ResponseType getOverseaTrendLineDataV2(GetOverseaTrendLineDataV2RequestType request);

GetOverseaDrillDownBaseInfoV2ResponseType getOverseaDrillDownBaseInfoV2(GetOverseaDrillDownBaseInfoV2RequestType request);

GetOverseaTableDataV2ResponseType getOverseaTableDataV2(GetOverseaTableDataV2RequestType request);

GetTicketPKEnumDataResponseType getTicketPKEnumData(GetTicketPKEnumDataRequestType getTicketPKEnumDataRequestType);

GetTicketPKSaleunitRankingResponseType getTicketPKSaleunitRanking(GetTicketPKSaleunitRankingRequestType getTicketPKSaleunitRankingRequestType);

GetTicketPKResourceRankingResponseType getTicketPKResourceRanking(GetTicketPKResourceRankingRequestType getTicketPKResourceRankingRequestType);

GetTicketPKScheduleCalendarResponseType getTicketPKScheduleCalendar(GetTicketPKScheduleCalendarRequestType getTicketPKScheduleCalendarRequestType);

GetTicketPKQualificationResponseType getTicketPKQualification(GetTicketPKQualificationRequestType getTicketPKQualificationRequestType);

GetTicketPKTableResponseType getTicketPKTable(GetTicketPKTableRequestType getTicketPKTableRequestType);

GetTicketPKBestStatusResponseType getTicketPKBestStatus(GetTicketPKBestStatusRequestType getTicketPKBestStatusRequestType);

GetTicketPKDefectOverallDataResponseType getTicketPKDefectOverallData(GetTicketPKDefectOverallDataRequestType getTicketPKDefectOverallDataRequestType);

GetTicketPKDefectTableDataResponseType getTicketPKDefectTableData(GetTicketPKDefectTableDataRequestType getTicketPKDefectTableDataRequestType);

GetTicketPKDefectOrderIdResponseType getTicketPKDefectOrderId(GetTicketPKDefectOrderIdRequestType getTicketPKDefectOrderIdRequestType);

GetMixProductPreviewStatusResponseType getMixProductPreviewStatus(GetMixProductPreviewStatusRequestType request);

GetGrpDestProvListResponseType getGrpDestProvList(GetGrpDestProvListRequestType getGrpDestProvListRequestType);
}