package com.ctrip.tour.business.dashboard.tktBusiness.helper;


import com.ctrip.soa._24922.TableDataItem;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.ConfigImportException;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.google.common.collect.Lists;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/7/29
 */
public class Bus8Helper {



    public static List<String> getDimList(){
        return Lists.newArrayList("ttd_sale1_stk_res_cnt","ttd_trgt_activity");
    }

    public static void makeUpMetricCardData(Map<String, Double> dimMap) {
        String extraDim = "ttd_sale1_stk_res_cnt|ttd_trgt_activity|/";
        Double fenzi = dimMap.get("ttd_sale1_stk_res_cnt");
        Double fenmu = dimMap.get("ttd_trgt_activity");
        if (GeneralUtil.isValidDivide(fenzi, fenmu)) {
            dimMap.put(extraDim, fenzi / fenmu);
        }
    }

    public static void makeUpTableData(List<TableDataItem> tableDataItemList) {
        for (TableDataItem item : tableDataItemList) {
            makeUpMetricCardData(item.getDimMap());
        }
    }

    public static Map<String,String> getLineChartTrendlineType(){
        Map<String,String> typeMap = new HashMap<>();
        typeMap.put("ttd_sale1_stk_res_cnt","barChart");
        typeMap.put("ttd_sale1_stk_res_cnt|ttd_trgt_activity|/","lineChart");
        return typeMap;
    }

    public static Map<String,String> getLineChartTrendlineTypeWithDrillDown(){
        Map<String,String> typeMap = new HashMap<>();
        typeMap.put("ttd_sale1_stk_res_cnt|ttd_trgt_activity|/","lineChart");
        return typeMap;
    }



    //根据考核层级确定可下钻维度
    public static List<String> getFieldList(String level){
        switch (level){
            case "国内":
            case "三方":
                return Lists.newArrayList("region_name","province_name");
            case "大区":
            case "省份":
                return Lists.newArrayList("province_name");
            default:
                throw new ConfigImportException("活动覆盖配置中导入了错误的层级:"+level);
        }
    }



}
