package com.ctrip.tour.business.dashboard.tktBusiness.biz;

import com.ctrip.soa._24922.GetTrendLineDataRequestType;
import com.ctrip.soa._27181.GetRawDataRequestType;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ExamineConfigBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.SinglePeriodDataBean;
import org.springframework.scheduling.annotation.Async;

import java.sql.SQLException;
import java.text.ParseException;
import java.util.concurrent.Future;

public interface SinglePeriodTrendLineBiz {


    //gmv
    @Async("trendLineExecutor")
    Future<SinglePeriodDataBean> getBus1SinglePeriodTrendLineData(GetTrendLineDataRequestType request,
                                                                  ExamineConfigBean examineConfigBean,
                                                                  String d) throws Exception;

    //毛利
    @Async("trendLineExecutor")
    Future<SinglePeriodDataBean> getBus2SinglePeriodTrendLineData(GetTrendLineDataRequestType request,
                                                                  ExamineConfigBean examineConfigBean,
                                                                  String d) throws Exception;

    //日游 gmv 毛利
    @Async("trendLineExecutor")
    Future<SinglePeriodDataBean> getBus1And2DayTourPeriodTrendLineData(GetRawDataRequestType currentReq,
                                                                       GetRawDataRequestType targetReq,
                                                                       GetRawDataRequestType lastyearReq,
                                                                       GetRawDataRequestType _2019Req,
                                                                       ExamineConfigBean examineConfig,
                                                                       MetricInfoBean metricInfoBean) throws Exception;

    //质量成本/gmv
    @Async("trendLineExecutor")
    Future<SinglePeriodDataBean> getBus3SinglePeriodTrendLineData(GetTrendLineDataRequestType request,
                                                                  ExamineConfigBean examineConfigBean,
                                                                  String d) throws SQLException;
    //直签
    @Async("trendLineExecutor")
    Future<SinglePeriodDataBean> getBus4SinglePeriodTrendLineData(GetTrendLineDataRequestType request,
                                                                  ExamineConfigBean examineConfigBean,
                                                                  String d) throws SQLException;

    //劣势(景点覆盖 票种覆盖 门票商品力)
    @Async("trendLineExecutor")
    Future<SinglePeriodDataBean> getBus567SinglePeriodTrendLineData(GetTrendLineDataRequestType request,
                                                                    ExamineConfigBean examineConfigBean,
                                                                    String d,
                                                                    String metric) throws SQLException, ParseException;


    //活动覆盖
    @Async("trendLineExecutor")
    Future<SinglePeriodDataBean> getBus8SinglePeriodTrendLineData(GetTrendLineDataRequestType request,
                                                                  ExamineConfigBean examineConfigBean,
                                                                  String d) throws SQLException;

    //品类覆盖
    @Async("trendLineExecutor")
    Future<SinglePeriodDataBean> getBus9SinglePeriodTrendLineData(GetTrendLineDataRequestType request,
                                                                  ExamineConfigBean examineConfigBean,
                                                                  String d) throws SQLException;
    //水牌覆盖
    @Async("trendLineExecutor")
    Future<SinglePeriodDataBean> getBus10SinglePeriodTrendLineData(GetTrendLineDataRequestType request,
                                                                   GetRawDataRequestType req,
                                                                   ExamineConfigBean examineConfigBean,
                                                                   MetricInfoBean metricInfoBean) throws Exception;

    //线路覆盖
    @Async("trendLineExecutor")
    Future<SinglePeriodDataBean> getBus11SinglePeriodTrendLineData(GetTrendLineDataRequestType request,
                                                                   MetricInfoBean metricInfoBean,
                                                                   String d,
                                                                   GetRawDataRequestType req,
                                                                   ExamineConfigBean examineConfigBean) throws Exception;

    @Async("trendLineExecutor")
    Future<SinglePeriodDataBean> getBus12SinglePeriodTrendLineData(GetTrendLineDataRequestType request,
                                                                   MetricInfoBean metricInfoBean,
                                                                   String d,
                                                                   GetRawDataRequestType req,
                                                                   ExamineConfigBean examineConfigBean) throws Exception;
}
