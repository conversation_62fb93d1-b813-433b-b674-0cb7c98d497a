package com.ctrip.tour.business.dashboard.grpBusiness.service;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.grpBusiness.config.GrpConfig;
import com.ctrip.tour.business.dashboard.grpBusiness.config.GrpConfigEntity;
import com.ctrip.tour.business.dashboard.grpBusiness.dao.GrpBusinessDashboardUpdatetimeDao;
import com.ctrip.tour.business.dashboard.grpBusiness.dao.mysql.DimPrdGrpBasedimWorkPlatformDf1Dao;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.AbstractAfterDataProcess;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.AbstractPreDSLProcess;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.process.data.AfterDataProcessEmpsInject;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.process.data.AfterDataProcessFilterAchieve;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.process.data.AfterDataProcessMetaSort;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.process.data.AfterDataProcessSortTrendData;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.dsl.DSLUtils;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.process.dsl.diy.*;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.query.StartRocksQuery;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.process.dsl.common.*;
import com.ctrip.tour.business.dashboard.grpBusiness.entity.EdwHrEmpVacation;
import com.ctrip.tour.business.dashboard.grpBusiness.enums.DiyMetricCategoryEnum;
import com.ctrip.tour.business.dashboard.grpBusiness.enums.DrillDownDim;
import com.ctrip.tour.business.dashboard.grpBusiness.enums.MetricCategoryEnum;
import com.ctrip.tour.business.dashboard.grpBusiness.enums.MetricEnum;
import com.ctrip.tour.business.dashboard.grpBusiness.excel.GrpDillDownExcelExporter;
import com.ctrip.tour.business.dashboard.grpBusiness.handler.dim.DimQueryService;
import com.ctrip.tour.business.dashboard.grpBusiness.handler.metric.IGrpBusinessMetricService;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.DomainQuery;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.model.DomainConfig;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.model.Indicator;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.model.ResultData;
import com.ctrip.tour.business.dashboard.grpBusiness.handler.metric.fittingNps.GrpCrpNpsRankService;
import com.ctrip.tour.business.dashboard.grpBusiness.handler.metric.selfSrv.GrpCrpSlfSrvRankService;
import com.ctrip.tour.business.dashboard.grpBusiness.metrics.fittingNps.GrpCprFittingNpsRankService;
import com.ctrip.tour.business.dashboard.grpBusiness.metrics.multiplePrice.GrpCprMultiPlePriceRankService;
import com.ctrip.tour.business.dashboard.grpBusiness.metrics.multiplePrice.GrpMpWeiAnomalyRateRankService;
import com.ctrip.tour.business.dashboard.grpBusiness.metrics.selfSrv.GrpCprSelfSrvRankService;
import com.ctrip.tour.business.dashboard.grpBusiness.metrics.topRateDriver.GrpGoodSevRankService;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;

import java.util.*;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class GrpBusinessService {


    @Autowired
    DimPrdGrpBasedimWorkPlatformDf1Dao dimPrdGrpBasedimWorkPlatformDf1Dao;

    private final ApplicationContext ac;
    List<String> prvPrdPatternList = new ArrayList<>(Arrays.asList("私家团", "自由行"));//NOSONAR
    List<String> grpPrdPatternList = new ArrayList<>(Arrays.asList("半自助游", "跟团游"));//NOSONAR
    ;//NOSONAR
    private final static String ADMIN_EID = "grp.admin.eid";
    private final static String GRP_GRP_ADMIN_EID = "grp.grp.admin.eid";
    private final static String GRP_PRV_ADMIN_EID = "grp.prv.admin.eid";

    @Autowired
    HrOrgEmpInfoService hrOrgEmpInfoService;
    @Autowired
    UserInfoService userInfoService;
    @Autowired
    GrpCprSelfSrvRankService grpCprSelfSrvRankService;
    @Autowired
    GrpCprFittingNpsRankService fittingNpsRankService;
    @Autowired
    DimQueryService dimQueryService;
    @Autowired
    RemoteConfig remoteConfig;
    @Autowired
    GrpBusinessDashboardUpdatetimeDao updatetimeDao;
    @Autowired
    GrpCprMultiPlePriceRankService multiPlePriceRankService;
    @Autowired
    GrpDillDownExcelExporter grpDillDownExcelExporter;
    @Autowired
    GrpMpWeiAnomalyRateRankService anomalyRateRankService;
    @Autowired
    GrpGoodSevRankService grpGoodSevRankService;
    @Autowired
    GrpCrpSlfSrvRankService grpCrpSlfSrvRankService;
    @Autowired
    GrpCrpNpsRankService grpCrpNpsRankService;
    @Autowired
    CustEmpOrgInfoService custEmpOrgInfoService;
    @Autowired
    GrpConfig grpConfig;

    @Autowired
    DepTreeCache depTreeCache;
    @Autowired
    StartRocksQuery startRocksQuery;

    public GrpBusinessService(ApplicationContext ac) {
        this.ac = ac;
    }

    public GetGrpUpdateTimeResponseType getGrpUpdateTime(GetGrpUpdateTimeRequestType requestType) throws Exception {
        //查询所有数据表的最后更新时间
        GetGrpUpdateTimeResponseType responseType = new GetGrpUpdateTimeResponseType();
        String updateTime = "";
        try {
            updateTime = updatetimeDao.getLastUpdateTime();
        } catch (Exception e) {
            log.error("getGrpUpdateTime error", e);
        }
        responseType.setUpdateTime(updateTime);

        return responseType;
    }

    public GetGrpOrganizationResponseType getGrpOrganization(GetGrpOrganizationRequestType requestType) throws Exception {

        GetGrpOrganizationResponseType responseType = new GetGrpOrganizationResponseType();
        responseType.setOrganizationItemList(new ArrayList<>());
        Integer selectTab = requestType.getSelectTab();
        if (selectTab == null) {
            selectTab = 1;
        }

        String searchKey = requestType.getSearchKey();
        String empCode = userInfoService.getMappingEmpCode();
//
//        boolean grpEmployee = hrOrgEmpInfoService.isGrpEmployee(empCode);
//        responseType.setHavePermission(grpEmployee);
//        //不是跟团的员工, 没有权限, 直接返回
//        if(!grpEmployee) {
//            return responseType;
//        }
        //是跟团的员工, 查询其下组织树
        List<GrpOrganizationNode> grpOrganizationNodeList = hrOrgEmpInfoService.getOrgTree(empCode, selectTab);
        if (CollectionUtils.isEmpty(grpOrganizationNodeList)) {
            responseType.setHavePermission(false);
            return responseType;
        }
        //搜索
        if (StringUtils.isNotBlank(searchKey)) {
            grpOrganizationNodeList = hrOrgEmpInfoService.searchOrgNode(grpOrganizationNodeList, searchKey);
        }

        responseType.setOrganizationItemList(grpOrganizationNodeList);
        responseType.setHavePermission(true);
        return responseType;
    }

    public GetGrpOrganizationResponseType getGrpOrganizationV2(GetGrpOrganizationRequestType requestType) throws Exception {
        Integer selectTab = requestType.getSelectTab();
        if (selectTab == null) {
            return getGrpOrganization(requestType);
        }
        switch (selectTab) {
            case 100:
                requestType.setSelectTab(1);
                break;
            case 210:
            case 220:
                requestType.setSelectTab(2);
                break;
            case 230:
                requestType.setSelectTab(3);
                break;
        }
        return getGrpOrganization(requestType);
    }

    public GetGrpEmployeeResponseType getGrpEmployee(GetGrpEmployeeRequestType requestType) throws Exception {

        String orgId = requestType.getOrgId();
//        String orgName = requestType.getOrgName();
        String searchKey = requestType.getSearchKey();


        GetGrpEmployeeResponseType responseType = new GetGrpEmployeeResponseType();
        List<GrpEmployeeItem> employeeItemList = hrOrgEmpInfoService.getGrpEmpListByOrgId(orgId, searchKey);
        if (StringUtils.equals("SO003419", orgId)) {
            employeeItemList = employeeItemList.stream().filter(item -> StringUtils.equals(item.getEmpCode(), "S40160")).collect(Collectors.toList());
        }

        if (CollectionUtils.isNotEmpty(employeeItemList)) {
            employeeItemList.sort((o1, o2) -> {
                if ("S08994".equals(o1.getEmpCode())) {
                    return -1; // 员工111排到前面
                } else if ("S08994".equals(o2.getEmpCode())) {
                    return 1; // 员工111排到前面
                }
                return 0; // 保持其他元素原有顺序
            });
        }

        responseType.setEmployeeItemList(employeeItemList);
        return responseType;

    }

    public GetGrpEmployeeResponseType getGrpEmployeeV2(GetGrpEmployeeRequestType requestType) throws Exception {
        GetGrpEmployeeResponseType grpEmployeeImpl = getGrpEmployee(requestType);
        if(grpEmployeeImpl.getEmployeeItemList() == null || grpEmployeeImpl.getEmployeeItemList().isEmpty()){
            String empMapping = userInfoService.getMappingEmpCode();
            if (empMapping != null && !empMapping.isEmpty()) {
                GrpEmployeeItem grpEmployeeItem = new GrpEmployeeItem();
                grpEmployeeItem.setEmpCode(empMapping);
                grpEmployeeImpl.getEmployeeItemList().add(grpEmployeeItem);
            }
        }
        return grpEmployeeImpl;
    }

    public GetGrpProductPatternResponseType getGrpProductPattern(GetGrpProductPatternRequestType requestType) throws Exception {

        GetGrpProductPatternResponseType responseType = new GetGrpProductPatternResponseType();
        Integer businessLine = requestType.getBusinessLine();

        if (Objects.equals(businessLine, 1)) {
            responseType.setProductPatternList(grpPrdPatternList);
        } else if (Objects.equals(businessLine, 2)) {
            responseType.setProductPatternList(prvPrdPatternList);
        } else {
            List<String> result = Lists.newArrayList();
            result.addAll(prvPrdPatternList);
            result.addAll(grpPrdPatternList);
            responseType.setProductPatternList(result);
        }

        return responseType;
    }

    public GetGrpProductPatternResponseType getGrpProductPatternV2(GetGrpProductPatternRequestType requestType) throws Exception {
        Integer businessLine = requestType.getBusinessLine();
        if (businessLine == null) {
            return getGrpProductPattern(requestType);
        }
        switch (businessLine) {
            case 100:
                requestType.setBusinessLine(1);
                break;
            case 210:
            case 220:
                requestType.setBusinessLine(2);
                break;
            case 230:
                requestType.setBusinessLine(3);
                break;
        }
        return getGrpProductPattern(requestType);
    }

    public GetGrpMetricCardResponseType getMetricCard(GetGrpMetricDataRequestType requestType, List<String> categoryNames) throws Exception {
        Integer selectTab = requestType.getSelectTab();
        GetGrpMetricCardResponseType responseType = new GetGrpMetricCardResponseType();
        if (Objects.equals(1, selectTab) || Objects.equals(2, selectTab)) {
            responseType = getGrpMetricCardV3(requestType, CollectionUtils.isEmpty(categoryNames) ? Lists.newArrayList() : categoryNames);
        } else if (Objects.equals(3, selectTab)) {
            responseType = getGrpMetricCardV3(requestType, CollectionUtils.isEmpty(categoryNames) ? DiyMetricCategoryEnum.getCategoryNameByDateType(requestType.getDateType()) : categoryNames);
        }
        return responseType;
    }


    public List<String> getDiyIndicators(DomainConfig domainConfig, String dateType, List<String> categoryNames) {
        List<String> indicators = new ArrayList<>();
        List<DiyMetricCategoryEnum> allMetricCategoryEnums;
        if (CollectionUtils.isNotEmpty(categoryNames)) {
            allMetricCategoryEnums = categoryNames.stream().map(DiyMetricCategoryEnum::getMetricCategoryEnumByEnglishName).collect(Collectors.toList());

        } else {
            allMetricCategoryEnums = DiyMetricCategoryEnum.getAllMetricCategoryEnums();
        }
        // 指标
        List<MetricEnum> metricEnumList = allMetricCategoryEnums.stream()
                .map(metricCategoryEnum -> DiyMetricCategoryEnum.getMetricEnumsByCategoryEnum(metricCategoryEnum, dateType))
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        // 指标名称
        indicators = metricEnumList.stream().map(v -> {
            Indicator indicatorsItem = domainConfig.getIndicatorsItemByIndicatorKeyName(v.getEnglishName());
            if (indicatorsItem == null) {
                log.warn("not defined indicator: {}", v.getMetricTipsSharkKey());
            }
            return indicatorsItem;
        }).filter(Objects::nonNull).map(Indicator::getIndicator_name).collect(Collectors.toList());
        return indicators;
    }

    // 获取查询的指标
    public List<String> getIndicators(DomainConfig domainConfig, List<String> categoryNames) {
        List<String> indicators = new ArrayList<>();
        // 业务线: 1:跟团游,2:私家游,3: 定制游
        List<MetricCategoryEnum> allMetricCategoryEnums;
        if (CollectionUtils.isNotEmpty(categoryNames)) {
            allMetricCategoryEnums = categoryNames.stream().map(MetricCategoryEnum::getMetricCategoryEnumByEnglishName).collect(Collectors.toList());
        } else {
            allMetricCategoryEnums = MetricCategoryEnum.getAllMetricCategoryEnums();
            indicators.addAll(Arrays.asList(
                    // nps
                    "fitting_nps_domestic_rank",
                    "fitting_nps_domestic_rank_max",
                    "fitting_nps_domestic_rank",
                    "fitting_nps_foreign_rank",
                    "fitting_nps_foreign_rank_max",
                    "fitting_nps_foreign_rank",
                    // 自服务
                    "self_sev_rate_domestic_rank",
                    "self_sev_rate_domestic_rank_max",
                    "self_sev_rate_domestic_rank_percentile",
                    "self_sev_rate_foreign_rank",
                    "self_sev_rate_foreign_rank_max",
                    "self_sev_rate_foreign_rank_percentile",
                    // 金牌导游
                    "good_guide_sev_ord_cnt_rate_rank",
                    "good_guide_sev_ord_cnt_rate_rank_max",
                    "good_guide_sev_ord_cnt_rate_rank_percentile",
                    // 价格倍数
                    "price_rate_rank",
                    "price_rate_rank_max",
                    "price_rate_rank_percentile",
                    // 信息分
                    "weighted_platform_score_rank",
                    "weighted_platform_score_rank_max",
                    "weighted_platform_score_rank_percentile"
            ));
        }
        // 指标
        List<MetricEnum> metricEnumList;
        metricEnumList = allMetricCategoryEnums.stream().map(metricCategoryEnum -> {
            List<MetricEnum> metricEnums = MetricCategoryEnum.getMetricEnumsByCategoryEnum(metricCategoryEnum);
            metricEnums = metricEnums.stream().filter(
                    metricEnum ->
//                            !metricEnum.equals(MetricEnum.GRP_INCOME_ACHIEVE_RATE)
//                            && !metricEnum.equals(MetricEnum.GRP_PROFIT_ACHIEVE_RATE)
//                            && !metricEnum.equals(MetricEnum.DISP_FAIL_RATE)
//                            && !metricEnum.equals(MetricEnum.TIMELY_RESPONSE_RATE)
//                            && !metricEnum.equals(MetricEnum.AVG_TRANSLATION_REVIEW_SCORE)
                            true
            ).collect(Collectors.toList());
            return metricEnums;
        }).flatMap(Collection::stream).collect(Collectors.toList());

        // 指标名称
        indicators.addAll(
                metricEnumList.stream().map(v -> {
                    Indicator indicatorsItem = domainConfig.getIndicatorsItemByIndicatorKeyName(v.getEnglishName());
                    if (indicatorsItem == null) {
                        log.warn("not defined indicator: {}", v.getMetricTipsSharkKey());
                    }
                    return indicatorsItem;
                }).filter(Objects::nonNull).map(Indicator::getIndicator_name).collect(Collectors.toList())
        );
        return indicators;
    }

    public List<GrpMetricCategorie> getDiyMetric(String dateType, List<String> categoryNames) {
        List<DiyMetricCategoryEnum> allMetricCategoryEnums;
        if (CollectionUtils.isNotEmpty(categoryNames)) {
            allMetricCategoryEnums = categoryNames.stream().map(DiyMetricCategoryEnum::getMetricCategoryEnumByEnglishName).collect(Collectors.toList());
        } else {
            allMetricCategoryEnums = DiyMetricCategoryEnum.getAllMetricCategoryEnums();
        }
        // 指标名称
        return allMetricCategoryEnums.stream().map(metricCategoryEnum -> {
                    GrpMetricCategorie category = new GrpMetricCategorie();
                    List<MetricEnum> metricEnums = DiyMetricCategoryEnum.getMetricEnumsByCategoryEnum(metricCategoryEnum, dateType);
                    List<GrpMetric> metrics = metricEnums.parallelStream().map(metricEnum -> {
                        GrpMetric v = new GrpMetric();
                        v.setMetricName(metricEnum.getEnglishName());
                        v.setMetricTipsSharkKey(metricEnum.getMetricTipsSharkKey());
                        v.setIsRatioValue(metricEnum.isRatio());
                        return v;
                    }).collect(Collectors.toList());
                    category.setMetricList(metrics);
                    category.setMetricCategorieName(metricCategoryEnum.getEnglishName());
                    category.setMetricCategorieTipsSharkkey(metricCategoryEnum.getCategoryTipsSharkKey());
                    return category;
                })
                .collect(Collectors.toList());
    }

    public List<GrpMetricCategorie> getMetric(List<String> categoryNames) {
        // 业务线: 1:跟团游,2:私家游,3: 定制游
        List<MetricCategoryEnum> allMetricCategoryEnums;
        if (CollectionUtils.isNotEmpty(categoryNames)) {
            allMetricCategoryEnums = categoryNames.stream().map(MetricCategoryEnum::getMetricCategoryEnumByEnglishName).collect(Collectors.toList());
        } else {
            allMetricCategoryEnums = MetricCategoryEnum.getAllMetricCategoryEnums();
        }
        List<GrpMetricCategorie> metricCardList = new ArrayList<>();
        GetGrpMetricCardResponseType responseType = new GetGrpMetricCardResponseType();
        responseType.setMetricCardList(metricCardList);
        return allMetricCategoryEnums.stream().map(metricCategoryEnum -> {
            GrpMetricCategorie category = new GrpMetricCategorie();
            List<MetricEnum> metricEnums = MetricCategoryEnum.getMetricEnumsByCategoryEnum(metricCategoryEnum);
            metricEnums = metricEnums.stream().filter(metricEnum ->
//                    !metricEnum.equals(MetricEnum.GRP_INCOME_ACHIEVE_RATE)
//                            && !metricEnum.equals(MetricEnum.GRP_PROFIT_ACHIEVE_RATE)
//                            && !metricEnum.equals(MetricEnum.DISP_FAIL_RATE)
//                            && !metricEnum.equals(MetricEnum.TIMELY_RESPONSE_RATE)
//                            && !metricEnum.equals(MetricEnum.AVG_TRANSLATION_REVIEW_SCORE)
                            true
            ).collect(Collectors.toList());
            List<GrpMetric> metrics = metricEnums.parallelStream().map(metricEnum -> {
                GrpMetric v = new GrpMetric();
                v.setMetricName(metricEnum.getEnglishName());
                v.setMetricTipsSharkKey(metricEnum.getMetricTipsSharkKey());
                v.setIsRatioValue(metricEnum.isRatio());
                return v;
            }).collect(Collectors.toList());
            category.setMetricList(metrics);
            category.setMetricCategorieName(metricCategoryEnum.getEnglishName());
            category.setMetricCategorieTipsSharkkey(metricCategoryEnum.getCategoryTipsSharkKey());
            return category;
        }).collect(Collectors.toList());
    }


    public GetGrpMetricCardResponseType processCardData(List<GrpMetricCategorie> grpMetricCategories, DSLResponseType data) {
        Map<String, Meta> metaMap = data.getMeta().stream().collect(Collectors.toMap(Meta::getIndicatorKey, v -> v, (newKey, oldKey) -> newKey));
        grpMetricCategories.forEach(grpMetricCategorie -> {
            Iterator<GrpMetric> iterator = grpMetricCategorie.getMetricList().iterator();
            while (iterator.hasNext()) {
                GrpMetric grpMetric = iterator.next();
                Meta meta = metaMap.get(grpMetric.getMetricTipsSharkKey());
                if (meta == null || meta.getIndicatorKey() == null || !meta.getIndicatorKey().equals(grpMetric.getMetricTipsSharkKey())) {
                    return;
                }
                String value = data.getData().get(0).getData().get(meta.getIndicatorName());
                Double doubleValue = null;
                if (value != null) {
                    doubleValue = Double.parseDouble(value);
                }
                String deriveDataValue = data.getData().get(0).getData().get(meta.getIndicatorName() + "DeriveData");
                Map<String, Object> deriveData = null;
                if (deriveDataValue != null && !deriveDataValue.equals("")) {
                    ObjectMapper mapper = new ObjectMapper();
                    try {
                        deriveData = mapper.readValue(deriveDataValue, Map.class);
                    } catch (JsonProcessingException e) {
                        throw new RuntimeException(e);
                    }
                }
                if (deriveData == null) {
                    deriveData = new HashMap<>();
                }
                grpMetric.setMetricValue(doubleValue);
                grpMetric.setExtraValues(new ExtraValues());
                if (deriveData.get("hb_rate") != null) {
                    grpMetric.getExtraValues().setMonthOverMonth(Double.parseDouble(deriveData.get("hb_rate").toString()));
                    grpMetric.getExtraValues().setWeekOverWeek(Double.parseDouble(deriveData.get("hb_rate").toString()));
                }
                if (deriveData.get("year_tb_rate") != null) {
                    grpMetric.getExtraValues().setYearOverYear(Double.parseDouble(deriveData.get("year_tb_rate").toString()));
                }

                // nps
                if (grpMetricCategorie.getMetricCategorieName().equals("fittingNpsCategory")) {
                    // 国内
                    Double domesticRank = null;
                    String domesticRankStr = data.getData().get(0).getData().get("fitting_nps_domestic_rank");
                    if (domesticRankStr != null) {
                        domesticRank = Double.parseDouble(domesticRankStr);
                    }
                    Double domesticPercentile = null;
                    String domesticPercentileStr = data.getData().get(0).getData().get("fitting_nps_domestic_rank_percentile");
                    if (domesticPercentileStr != null) {
                        domesticPercentile = Double.parseDouble(domesticPercentileStr);
                    }
                    grpMetricCategorie.setRank(domesticRank);
                    grpMetricCategorie.setRankRatio(domesticPercentile);
                    // 海外
                    Double foreignRank = null;
                    String foreignRankStr = data.getData().get(0).getData().get("fitting_nps_foreign_rank");
                    if (foreignRankStr != null) {
                        foreignRank = Double.parseDouble(foreignRankStr);
                    }
                    Double foreignPercentile = null;
                    String foreignPercentileStr = data.getData().get(0).getData().get("fitting_nps_foreign_rank_percentile");
                    if (foreignPercentileStr != null) {
                        foreignPercentile = Double.parseDouble(foreignPercentileStr);
                    }
                    grpMetricCategorie.setOverSeaRank(foreignRank);
                    grpMetricCategorie.setOverSeaRankRatio(foreignPercentile);
                }
                // 自服务
                if (grpMetricCategorie.getMetricCategorieName().equals("selfServiceCategory")) {
                    // 国内
                    Double domesticRank = null;
                    String domesticRankStr = data.getData().get(0).getData().get("self_sev_rate_domestic_rank");
                    if (domesticRankStr != null) {
                        domesticRank = Double.parseDouble(domesticRankStr);
                    }
                    Double domesticPercentile = null;
                    String domesticPercentileStr = data.getData().get(0).getData().get("self_sev_rate_domestic_rank_percentile");
                    if (domesticPercentileStr != null) {
                        domesticPercentile = Double.parseDouble(domesticPercentileStr);
                    }
                    grpMetricCategorie.setRank(domesticRank);
                    grpMetricCategorie.setRankRatio(domesticPercentile);
                    // 海外
                    Double foreignRank = null;
                    String foreignRankStr = data.getData().get(0).getData().get("self_sev_rate_foreign_rank");
                    if (foreignRankStr != null) {
                        foreignRank = Double.parseDouble(foreignRankStr);
                    }
                    Double foreignPercentile = null;
                    String foreignPercentileStr = data.getData().get(0).getData().get("self_sev_rate_foreign_rank_percentile");
                    if (foreignPercentileStr != null) {
                        foreignPercentile = Double.parseDouble(foreignPercentileStr);
                    }
                    grpMetricCategorie.setOverSeaRank(foreignRank);
                    grpMetricCategorie.setOverSeaRankRatio(foreignPercentile);
                }
                // 金牌导游
                if (grpMetricCategorie.getMetricCategorieName().equals("topRateGuiderCategory")) {
                    Double domesticRank = null;
                    String domesticRankStr = data.getData().get(0).getData().get("good_guide_sev_ord_cnt_rate_rank");
                    if (domesticRankStr != null) {
                        domesticRank = Double.parseDouble(domesticRankStr);
                    }
                    Double domesticPercentile = null;
                    String domesticPercentileStr = data.getData().get(0).getData().get("good_guide_sev_ord_cnt_rate_rank_percentile");
                    if (domesticPercentileStr != null) {
                        domesticPercentile = Double.parseDouble(domesticPercentileStr);
                    }
                    grpMetricCategorie.setRank(domesticRank);
                    grpMetricCategorie.setRankRatio(domesticPercentile);
                }
                // 价格倍数
                if (grpMetricCategorie.getMetricCategorieName().equals("multiplePriceCategory")) {
                    Double domesticRank = null;
                    String domesticRankStr = data.getData().get(0).getData().get("price_rate_rank");
                    if (domesticRankStr != null) {
                        domesticRank = Double.parseDouble(domesticRankStr);
                    }
                    Double domesticPercentile = null;
                    String domesticPercentileStr = data.getData().get(0).getData().get("price_rate_rank_percentile");
                    if (domesticPercentileStr != null) {
                        domesticPercentile = Double.parseDouble(domesticPercentileStr);
                    }
                    grpMetricCategorie.setRank(domesticRank);
                    grpMetricCategorie.setRankRatio(domesticPercentile);
                }
                // 信息分
                if (grpMetricCategorie.getMetricCategorieName().equals("infoScoreCategory")) {
                    Double domesticRank = null;
                    String domesticRankStr = data.getData().get(0).getData().get("weighted_platform_score_rank");
                    if (domesticRankStr != null) {
                        domesticRank = Double.parseDouble(domesticRankStr);
                    }
                    Double domesticPercentile = null;
                    String domesticPercentileStr = data.getData().get(0).getData().get("weighted_platform_score_rank_percentile");
                    if (domesticPercentileStr != null) {
                        domesticPercentile = Double.parseDouble(domesticPercentileStr);
                    }
                    grpMetricCategorie.setRank(domesticRank);
                    grpMetricCategorie.setRankRatio(domesticPercentile);
                }
                // 交易达成率
                if (grpMetricCategorie.getMetricCategorieName().equals("incomeCategory")) {
                    Double domesticRank = null;
                    String domesticRankStr = data.getData().get(0).getData().get("suc_income_rate_rank");
                    if (domesticRankStr != null) {
                        domesticRank = Double.parseDouble(domesticRankStr);
                    }
                    Double domesticPercentile = null;
                    String domesticPercentileStr = data.getData().get(0).getData().get("suc_income_rate_rank_percentile");
                    if (domesticPercentileStr != null) {
                        domesticPercentile = Double.parseDouble(domesticPercentileStr);
                    }
                    grpMetricCategorie.setRank(domesticRank);
                    grpMetricCategorie.setRankRatio(domesticPercentile);
                }
                // 毛利达成率
                if (grpMetricCategorie.getMetricCategorieName().equals("profitPriceCategory")) {
                    Double domesticRank = null;
                    String domesticRankStr = data.getData().get(0).getData().get("suc_profit_rate_rank");
                    if (domesticRankStr != null) {
                        domesticRank = Double.parseDouble(domesticRankStr);
                    }
                    Double domesticPercentile = null;
                    String domesticPercentileStr = data.getData().get(0).getData().get("suc_profit_rate_rank_percentile");
                    if (domesticPercentileStr != null) {
                        domesticPercentile = Double.parseDouble(domesticPercentileStr);
                    }
                    grpMetricCategorie.setRank(domesticRank);
                    grpMetricCategorie.setRankRatio(domesticPercentile);
                }
                // 删除绩效达成
                if ((grpMetric.getMetricName().equals("grpIncomeAchieveRate") ||
                        grpMetric.getMetricName().equals("grpProfitAchieveRate") ||
                        grpMetric.getMetricName().equals("diyIncomeAchieveRate") ||
                        grpMetric.getMetricName().equals("diyProfitAchieveRate"))
                        && grpMetric.getMetricValue() == null) {
                    iterator.remove();
                }
            }
        });
        GetGrpMetricCardResponseType getGrpMetricCardResponseType = new GetGrpMetricCardResponseType();
        getGrpMetricCardResponseType.setMetricCardList(grpMetricCategories);
        return getGrpMetricCardResponseType;
    }


    public Integer getBusinessLine(Integer businessType) {
        if (businessType != null) {
            switch (businessType) {
                case 1:
                    businessType = 100;
                    break;
                case 2:
                    businessType = 210;
                    break;
                case 3:
                    businessType = 230;
                    break;
            }
        }
        if (businessType == null) {
            GetGrpOrgTabListResponseType grpOrgTabListV2;
            try {
                grpOrgTabListV2 = hrOrgEmpInfoService.getGrpOrgTabListV2();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            if (grpOrgTabListV2 != null) {
                businessType = grpOrgTabListV2.defaultTabList;
            }
        }
        // 默认的业务线
        if (businessType == null || businessType == 0) {
            return 0;
        }
        return businessType;
    }


    public GetGrpMetricCardResponseType getGrpMetricCard(GetGrpMetricDataRequestType requestType, List<String> categoryNames) throws Exception {
        // depTreeCache.InitDepTreeSync();
        String mappingEmpCode = requestType.getEmpCode();
        if (StringUtils.isBlank(mappingEmpCode)) {
            mappingEmpCode = userInfoService.getMappingEmpCode();
        }

        int bizMode = requestType.getBusinessLine();
        Integer selectTab = requestType.getSelectTab();

        List<MetricCategoryEnum> allMetricCategoryEnums;
        if (CollectionUtils.isNotEmpty(categoryNames)) {
            allMetricCategoryEnums = categoryNames.stream().map(MetricCategoryEnum::getMetricCategoryEnumByEnglishName).collect(Collectors.toList());
        } else {
            allMetricCategoryEnums = MetricCategoryEnum.getAllMetricCategoryEnums();
        }


        List<GrpMetricCategorie> metricCardList = new ArrayList<>();
        GetGrpMetricCardResponseType responseType = new GetGrpMetricCardResponseType();
        responseType.setMetricCardList(metricCardList);

        String finalMappingEmpCode = mappingEmpCode;
        List<GrpMetricCategorie> categories = allMetricCategoryEnums.parallelStream().map(metricCategoryEnum -> {
            GrpMetricCategorie categorie = new GrpMetricCategorie();
            List<MetricEnum> metricEnums = MetricCategoryEnum.getMetricEnumsByCategoryEnum(metricCategoryEnum);
            if (selectTab == 2) {
                metricEnums = metricEnums.stream().filter(metricEnum -> !metricEnum.equals(MetricEnum.GRP_INCOME_ACHIEVE_RATE) && !metricEnum.equals(MetricEnum.GRP_PROFIT_ACHIEVE_RATE) && !metricEnum.equals(MetricEnum.DISP_FAIL_RATE) && !metricEnum.equals(MetricEnum.TIMELY_RESPONSE_RATE) && !metricEnum.equals(MetricEnum.AVG_TRANSLATION_REVIEW_SCORE)).collect(Collectors.toList());
            }
            List<GrpMetric> metrics = metricEnums.parallelStream().map(metricEnum -> {
                IGrpBusinessMetricService service = ac.getBean(metricEnum.getEnglishName(), IGrpBusinessMetricService.class);
                return service.queryMetricValue(requestType, metricEnum, service, bizMode);
            }).collect(Collectors.toList());
            categorie.setMetricList(metrics);
            categorie.setMetricCategorieName(metricCategoryEnum.getEnglishName());
            categorie.setMetricCategorieTipsSharkkey(metricCategoryEnum.getCategoryTipsSharkKey());
            if (metricCategoryEnum.equals(MetricCategoryEnum.SELF_SERVICE_CATEGORY)) {


                if (selectTab == 1) {
                    try {
                        Integer[] pmRank = grpCrpSlfSrvRankService.getPmRank(requestType.getEndDate(), finalMappingEmpCode);

                        if (pmRank.length >= 2) {
                            if (pmRank[0] != null && pmRank[0] != 0) {
                                categorie.setRank(pmRank[0].doubleValue());
                                categorie.setRankRatio(BigDecimal.valueOf(pmRank[0].doubleValue()).divide(BigDecimal.valueOf(pmRank[1].doubleValue()), 5, RoundingMode.HALF_UP).doubleValue());
                            }

                            if (pmRank[2] != null && pmRank[2] != 0) {
                                categorie.setOverSeaRank(pmRank[2].doubleValue());
                                categorie.setOverSeaRankRatio(BigDecimal.valueOf(pmRank[2].doubleValue()).divide(BigDecimal.valueOf(pmRank[3].doubleValue()), 5, RoundingMode.HALF_UP).doubleValue());
                            }
                        }
                    } catch (Exception e) {
                        log.warn("query mpRank error ", e);
                    }
                } else {
                    Map<String, Object> param = Maps.newHashMap();
                    param.put("business_team", requestType.getEmpCode());
                    String endDate = requestType.getEndDate();
                    String[] endDateArray = endDate.split("-");
                    param.put("stat_year", endDateArray[0]);
                    param.put("stat_month", endDateArray[1]);
                    List<Map<String, Object>> rankRsts = grpCprSelfSrvRankService.queryCardData(grpCprSelfSrvRankService, param, null);
                    if (CollectionUtils.isNotEmpty(rankRsts)) {
                        Map<String, Object> result = rankRsts.get(0);
                        BigDecimal rank = convertRankObjToDecimal(result.get("rank_val"));
                        BigDecimal total = convertRankObjToDecimal(result.get("total_cnt"));
                        if (Objects.nonNull(rank) && Objects.nonNull(total)) {
                            categorie.setRank(rank.doubleValue());
                            categorie.setRankRatio(rank.divide(total, 5, RoundingMode.HALF_UP).doubleValue());
                        }

                    }
                }


            } else if (metricCategoryEnum.equals(MetricCategoryEnum.FITTING_NPS_CATEGORY)) {

                if (selectTab == 1) {
                    try {
                        Integer[] pmRank = grpCrpNpsRankService.getPmRank(requestType.getEndDate(), finalMappingEmpCode);

                        if (pmRank.length >= 2) {
                            if (pmRank[0] != null && pmRank[0] != 0) {
                                categorie.setRank(pmRank[0].doubleValue());
                                categorie.setRankRatio(BigDecimal.valueOf(pmRank[0].doubleValue()).divide(BigDecimal.valueOf(pmRank[1].doubleValue()), 5, RoundingMode.HALF_UP).doubleValue());
                            }
                            if (pmRank[2] != null && pmRank[2] != 0) {
                                categorie.setOverSeaRank(pmRank[2].doubleValue());
                                categorie.setOverSeaRankRatio(BigDecimal.valueOf(pmRank[2].doubleValue()).divide(BigDecimal.valueOf(pmRank[3].doubleValue()), 5, RoundingMode.HALF_UP).doubleValue());
                            }
                        }
                    } catch (Exception e) {
                        log.warn("query mpRank error ", e);
                    }
                } else {
                    Map<String, Object> param = Maps.newHashMap();
                    param.put("sub_bu_type", "独立出游");//NOSONAR
                    param.put("business_team", finalMappingEmpCode);
                    String endDate = requestType.getEndDate();
                    String[] endDateArray = endDate.split("-");
                    param.put("stat_year", endDateArray[0]);
                    param.put("stat_month", endDateArray[1]);
                    List<Map<String, Object>> rankRsts = fittingNpsRankService.queryCardData(fittingNpsRankService, param, null);
                    if (CollectionUtils.isNotEmpty(rankRsts)) {
                        Map<String, Object> result = rankRsts.get(0);
                        BigDecimal rank = convertRankObjToDecimal(result.get("rank_val"));
                        BigDecimal total = convertRankObjToDecimal(result.get("total_cnt"));
                        if (Objects.nonNull(rank) && Objects.nonNull(total)) {
                            categorie.setRank(rank.doubleValue());
                            categorie.setRankRatio(rank.divide(total, 5, RoundingMode.HALF_UP).doubleValue());
                        }

                    }

                }
//
            } else if (metricCategoryEnum.equals(MetricCategoryEnum.MULTIPLE_PRICE_CATEGORY)) {

                try {
                    Integer[] pmRank = anomalyRateRankService.getPmRank(requestType.getStartDate(), requestType.getEndDate(), finalMappingEmpCode);

                    if (pmRank.length == 2) {
                        if (pmRank[0] != 0) {
                            categorie.setRank(pmRank[0].doubleValue());
                            categorie.setRankRatio(BigDecimal.valueOf(pmRank[0].doubleValue()).divide(BigDecimal.valueOf(pmRank[1].doubleValue()), 5, RoundingMode.HALF_UP).doubleValue());
                        }
                    }
                } catch (Exception e) {
                    log.warn("query mpRank error ", e);
                }

//                        Map<String, Object> param = Maps.newHashMap();
//                        param.put("sub_bu_type", requestType.getBusinessLine() == 1 ? "跟团游" : "独立出游");//NOSONAR
//                        param.put("business_team", requestType.getEmpCode());
//                        String endDate = requestType.getEndDate();
//                        param.put("view_month", endDate.substring(0, endDate.lastIndexOf("-")));
//                        List<Map<String, Object>> rankRsts = multiPlePriceRankService.queryCardData(multiPlePriceRankService, param, null);
//                        if (CollectionUtils.isNotEmpty(rankRsts)) {
//                            Map<String, Object> result = rankRsts.get(0);
//                            BigDecimal rank = convertRankObjToDecimal(result.get("rank_val"));
//                            BigDecimal total = convertRankObjToDecimal(result.get("total_cnt"));
//                            if (Objects.nonNull(rank) && Objects.nonNull(total)) {
//                                categorie.setRank(rank.doubleValue());
//                                categorie.setRankRatio(rank.divide(total, 5, RoundingMode.HALF_UP).doubleValue());
//                            }
//
//                        }
            } else if (metricCategoryEnum.equals(MetricCategoryEnum.TOP_RATE_GUIDER_CATEGORY)) {
                try {
                    Long[] pmRank = grpGoodSevRankService.getPmRank(requestType.getEndDate(), finalMappingEmpCode);

                    if (pmRank.length == 2) {
                        if (pmRank[0] != 0) {
                            categorie.setRank(pmRank[0].doubleValue());
                            if (pmRank[1] != null) {
                                categorie.setRankRatio(BigDecimal.valueOf(pmRank[0].doubleValue()).divide(BigDecimal.valueOf(pmRank[1].doubleValue()), 5, RoundingMode.HALF_UP).doubleValue());
                            }

                        }
                    }
                } catch (Exception e) {
                    log.warn("query mpRank error ", e);
                }
            }
            return categorie;
        }).collect(Collectors.toList());
        responseType.setMetricCardList(categories);


        return responseType;
    }

    public GetGrpMetricCardResponseType getCustMetricCard(GetGrpMetricDataRequestType requestType, List<String> categoryNames) throws Exception {

        int bizMode = requestType.getSelectTab();

        String dateType = requestType.getDateType();

        List<DiyMetricCategoryEnum> allMetricCategoryEnums;
        if (CollectionUtils.isNotEmpty(categoryNames)) {
            allMetricCategoryEnums = categoryNames.stream().map(DiyMetricCategoryEnum::getMetricCategoryEnumByEnglishName).collect(Collectors.toList());
        } else {
            allMetricCategoryEnums = DiyMetricCategoryEnum.getAllMetricCategoryEnums();
        }


        List<GrpMetricCategorie> metricCardList = new ArrayList<>();
        GetGrpMetricCardResponseType responseType = new GetGrpMetricCardResponseType();
        responseType.setMetricCardList(metricCardList);

        List<GrpMetricCategorie> categories = allMetricCategoryEnums.stream()//.parallelStream()
                .map(metricCategoryEnum -> {
                    GrpMetricCategorie categorie = new GrpMetricCategorie();
                    List<MetricEnum> metricEnums = DiyMetricCategoryEnum.getMetricEnumsByCategoryEnum(metricCategoryEnum, dateType);
                    List<GrpMetric> metrics = metricEnums.stream()//.parallelStream()
                            .map(metricEnum -> {
                                IGrpBusinessMetricService service = ac.getBean(metricEnum.getEnglishName(), IGrpBusinessMetricService.class);
                                System.out.println("====> " + metricEnum.getMetricTipsSharkKey() + " " + metricEnum.getEnglishName() + " " + metricEnum.getChineseName());
                                return service.queryMetricValue(requestType, metricEnum, service, bizMode);
                            }).collect(Collectors.toList());
                    categorie.setMetricList(metrics);
                    categorie.setMetricCategorieName(metricCategoryEnum.getEnglishName());
                    categorie.setMetricCategorieTipsSharkkey(metricCategoryEnum.getCategoryTipsSharkKey());
//                    if (metricCategoryEnum.equals(MetricCategoryEnum.SELF_SERVICE_CATEGORY)) {
//
//                        Map<String, Object> param = Maps.newHashMap();
//                        param.put("business_team", requestType.getEmpCode());
//                        String endDate = requestType.getEndDate();
//                        String[] endDateArray = endDate.split("-");
//                        param.put("stat_year", endDateArray[0]);
//                        param.put("stat_month", endDateArray[1]);
//                        List<Map<String, Object>> rankRsts = grpCprSelfSrvRankService.queryCardData(grpCprSelfSrvRankService, param, null);
//                        if (CollectionUtils.isNotEmpty(rankRsts)) {
//                            Map<String, Object> result = rankRsts.get(0);
//                            BigDecimal rank = convertRankObjToDecimal(result.get("rank_val"));
//                            BigDecimal total = convertRankObjToDecimal(result.get("total_cnt"));
//                            if (Objects.nonNull(rank) && Objects.nonNull(total)) {
//                                categorie.setRank(rank.doubleValue());
//                                categorie.setRankRatio(rank.divide(total, 5, RoundingMode.HALF_UP).doubleValue());
//                            }
//
//                        }
//                    } else if (metricCategoryEnum.equals(MetricCategoryEnum.FITTING_NPS_CATEGORY)) {
//
//                        Map<String, Object> param = Maps.newHashMap();
//                        param.put("sub_bu_type", requestType.getBusinessLine() == 1 ? "跟团游" : "独立出游");//NOSONAR
//                        param.put("business_team", requestType.getEmpCode());
//                        String endDate = requestType.getEndDate();
//                        String[] endDateArray = endDate.split("-");
//                        param.put("stat_year", endDateArray[0]);
//                        param.put("stat_month", endDateArray[1]);
//                        List<Map<String, Object>> rankRsts = fittingNpsRankService.queryCardData(fittingNpsRankService, param, null);
//                        if (CollectionUtils.isNotEmpty(rankRsts)) {
//                            Map<String, Object> result = rankRsts.get(0);
//                            BigDecimal rank = convertRankObjToDecimal(result.get("rank_val"));
//                            BigDecimal total = convertRankObjToDecimal(result.get("total_cnt"));
//                            if (Objects.nonNull(rank) && Objects.nonNull(total)) {
//                                categorie.setRank(rank.doubleValue());
//                                categorie.setRankRatio(rank.divide(total, 5, RoundingMode.HALF_UP).doubleValue());
//                            }
//
//                        }
//                    } else if (metricCategoryEnum.equals(MetricCategoryEnum.MULTIPLE_PRICE_CATEGORY)) {
//
//                        Map<String, Object> param = Maps.newHashMap();
//                        param.put("sub_bu_type", requestType.getBusinessLine() == 1 ? "跟团游" : "独立出游");//NOSONAR
//                        param.put("business_team", requestType.getEmpCode());
//                        String endDate = requestType.getEndDate();
//                        param.put("view_month", endDate.substring(0, endDate.lastIndexOf("-")));
//                        List<Map<String, Object>> rankRsts = multiPlePriceRankService.queryCardData(multiPlePriceRankService, param, null);
//                        if (CollectionUtils.isNotEmpty(rankRsts)) {
//                            Map<String, Object> result = rankRsts.get(0);
//                            BigDecimal rank = convertRankObjToDecimal(result.get("rank_val"));
//                            BigDecimal total = convertRankObjToDecimal(result.get("total_cnt"));
//                            if (Objects.nonNull(rank) && Objects.nonNull(total)) {
//                                categorie.setRank(rank.doubleValue());
//                                categorie.setRankRatio(rank.divide(total, 5, RoundingMode.HALF_UP).doubleValue());
//                            }
//
//                        }
//                    }
                    return categorie;
                }).collect(Collectors.toList());
        responseType.setMetricCardList(categories);


        return responseType;
    }

    private BigDecimal convertRankObjToDecimal(Object rankObj) {
        if (rankObj instanceof Integer) {
            Integer rankObjInt = (Integer) rankObj;
            return BigDecimal.valueOf((long) rankObjInt);
        } else if (rankObj instanceof Long) {
            return BigDecimal.valueOf((long) rankObj);
        } else if (rankObj instanceof Double) {
            return BigDecimal.valueOf((Double) rankObj);
        }
        return null;
    }

    public GetGrpTrendLineResponseType getTrendLineInfo(GetGrpMetricDataRequestType requestType) throws Exception {
//        GetGrpTrendLineResponseType responseType = new GetGrpTrendLineResponseType();
//        Integer selectTab = requestType.getSelectTab();
//        if (Objects.equals(1, selectTab) || Objects.equals(2, selectTab)) {
//            responseType = getGrpTrendLineV3(requestType);
//        } else if (Objects.equals(3, selectTab)) {
//            responseType = getCustTrendLine(requestType);
//        }
//        return responseType;
        return getGrpTrendLineV3(requestType);
    }

    public GetGrpTrendLineResponseType getGrpTrendLine(GetGrpMetricDataRequestType requestType) throws Exception {
        int bizMode = requestType.getBusinessLine();
        GetGrpTrendLineResponseType responseType = new GetGrpTrendLineResponseType();
        String metricCategoryEnum = requestType.getMetricCategorieName();
        List<MetricEnum> metricEnums = MetricCategoryEnum.getMetricEnumsByCategoryEnglishName(metricCategoryEnum);
        List<List<GrpTrendLinePoint>> result = metricEnums.parallelStream().map(metricEnum -> {
            if (StringUtils.equals(metricEnum.getEnglishName(), MetricEnum.DIY_INCOME_ACHIEVE_RATE.getEnglishName()) || StringUtils.equals(metricEnum.getEnglishName(), MetricEnum.GRP_INCOME_ACHIEVE_RATE.getEnglishName()) || StringUtils.equals(metricEnum.getEnglishName(), MetricEnum.DIY_PROFIT_ACHIEVE_RATE.getEnglishName()) || StringUtils.equals(metricEnum.getEnglishName(), MetricEnum.GRP_PROFIT_ACHIEVE_RATE.getEnglishName())) {
                return null;
            }
            IGrpBusinessMetricService service = ac.getBean(metricEnum.getEnglishName(), IGrpBusinessMetricService.class);
            return service.queryMetricTrendLineP(requestType, metricEnum, service, bizMode);
        }).filter(Objects::nonNull).collect(Collectors.toList());

        Map<String, List<GrpTrendLinePoint>> mapGrpByDate = result.parallelStream().flatMap(Collection::stream).collect(Collectors.groupingBy(GrpTrendLinePoint::getDate));

        List<GrpTrendLinePoint> trendLinePoints = mapGrpByDate.entrySet().stream().map(entry -> {
            GrpTrendLinePoint linePoint = new GrpTrendLinePoint();
            linePoint.setDate(entry.getKey());
            List<GrpTrendLinePoint> value = entry.getValue();
            List<GrpMetric> metrics = value.stream().flatMap(grpTrendLinePoint -> grpTrendLinePoint.getTrendLinePoints().stream()).collect(Collectors.toList());
            linePoint.setTrendLinePoints(metrics);
            return linePoint;
        }).sorted((l1, l2) -> {
            String date1 = l1.getDate();
            String date2 = l2.getDate();
            String start1 = date1.split("~")[0];
            String start2 = date2.split("~")[0];
            return start1.compareTo(start2);
        }).collect(Collectors.toList());
        responseType.setTrendLine(trendLinePoints);
        return responseType;
    }


    public GetGrpTrendLineResponseType afterProcessSortTrendData(GetGrpMetricDataRequestType requestType, List<GrpMetricCategorie> metric, ChangeTrendResponseType changeTrendResponseType) {
        Map<String, Meta> metaMap = changeTrendResponseType.getData().getMeta().stream().collect(Collectors.toMap(Meta::getIndicatorKey, v -> v));
        List<GrpTrendLinePoint> trendLinePoints = changeTrendResponseType.getData().getData().stream().map(v -> {
            GrpTrendLinePoint linePoint = new GrpTrendLinePoint();
            if (requestType.getAggregationGranularity().equals("week")) {
                linePoint.setDate(v.getData().get("date_week"));
            } else if (requestType.getAggregationGranularity().equals("month")) {
                linePoint.setDate(v.getData().get("date_month"));
            } else {
                linePoint.setDate(v.getData().get("date"));
            }
            if (linePoint.getDate() == null || linePoint.getDate().isEmpty()) {
                return null;
            }
            List<GrpMetric> collect = metric.get(0).getMetricList().stream().map(metricEnum -> {
                GrpMetric item = new GrpMetric();
                item.setIsRatioValue(metricEnum.isIsRatioValue());
                item.setMetricName(metricEnum.getMetricName());
                item.setMetricTipsSharkKey(metricEnum.getMetricTipsSharkKey());
                // 数据解析
                Meta meta = metaMap.get(metricEnum.getMetricTipsSharkKey());
                if (meta == null) {
                    return null;
                }
                Double value = null;
                if (v.getData().get(meta.getIndicatorName()) != null) {
                    value = Double.parseDouble(v.getData().get(meta.getIndicatorName()));
                }
                // 数值
                item.setMetricValue(value);
                // 同环比
                ExtraValues extraValues = new ExtraValues();
                item.setExtraValues(extraValues);
                // DeriveData year_tb hb
                String deriveDataValue = v.getData().get(meta.getIndicatorName() + "DeriveData");
                Map<String, Object> deriveData = null;
                if (deriveDataValue != null && !deriveDataValue.isEmpty()) {
                    ObjectMapper mapper = new ObjectMapper();
                    try {
                        deriveData = mapper.readValue(deriveDataValue, Map.class);
                    } catch (JsonProcessingException e) {
                        throw new RuntimeException(e);
                    }
                    if (deriveData.get("year_tb_rate") != null) {
                        extraValues.setYearOverYear((Double) deriveData.get("year_tb_rate"));
                    }
                    if (deriveData.get("hb_rate") != null) {
                        extraValues.setYearOverYear((Double) deriveData.get("hb_rate"));
                    }
                }
                return item;
            }).collect(Collectors.toList());
            linePoint.setTrendLinePoints(collect);
            return linePoint;
        }).filter(Objects::nonNull).sorted((l1, l2) -> {
            String date1 = l1.getDate();
            String date2 = l2.getDate();
            String start1 = date1.split("~")[0];
            String start2 = date2.split("~")[0];
            return start1.compareTo(start2);
        }).collect(Collectors.toList());
        GetGrpTrendLineResponseType responseType = new GetGrpTrendLineResponseType();
        responseType.setTrendLine(trendLinePoints);
        return responseType;
    }


    public GetGrpTrendLineResponseType getCustTrendLine(GetGrpMetricDataRequestType requestType) throws Exception {
        int bizMode = requestType.getBusinessLine();
        GetGrpTrendLineResponseType responseType = new GetGrpTrendLineResponseType();
        String metricCategoryEnum = requestType.getMetricCategorieName();
        List<MetricEnum> metricEnums = DiyMetricCategoryEnum.getMetricEnumsByCategoryEnglishName(metricCategoryEnum, requestType.getDateType());
        List<List<GrpTrendLinePoint>> result = metricEnums.parallelStream().map(metricEnum -> {
            IGrpBusinessMetricService service = ac.getBean(metricEnum.getEnglishName(), IGrpBusinessMetricService.class);
            return service.queryMetricTrendLineP(requestType, metricEnum, service, bizMode);
        }).collect(Collectors.toList());
        Map<String, List<GrpTrendLinePoint>> mapGrpByDate = result.parallelStream().flatMap(Collection::stream).collect(Collectors.groupingBy(GrpTrendLinePoint::getDate));
        List<GrpTrendLinePoint> trendLinePoints = mapGrpByDate.entrySet().stream().map(entry -> {
            GrpTrendLinePoint linePoint = new GrpTrendLinePoint();
            linePoint.setDate(entry.getKey());
            List<GrpTrendLinePoint> value = entry.getValue();
            List<GrpMetric> metrics = value.stream().flatMap(grpTrendLinePoint -> grpTrendLinePoint.getTrendLinePoints().stream()).collect(Collectors.toList());
            linePoint.setTrendLinePoints(metrics);
            return linePoint;
        }).sorted((l1, l2) -> {
            String date1 = l1.getDate();
            String date2 = l2.getDate();
            String start1 = date1.split("~")[0];
            String start2 = date2.split("~")[0];
            return start1.compareTo(start2);
        }).collect(Collectors.toList());
        responseType.setTrendLine(trendLinePoints);
        return responseType;
    }

    public GetGrpDillDownDimResponseType getGrpDillDownDim(GetGrpDillDownDimRequestType requestType) throws Exception {
        String metricCategorieName = requestType.getMetricCategorieName();

        List<DrillDownDim> drillDownDimEnumList = DrillDownDim.getDrillDownDimByMetricCategoryEnglishName(metricCategorieName, requestType.getSelectTab());

        List<String> dimChineseNameList = drillDownDimEnumList.stream().map(DrillDownDim::getChineseName).distinct().collect(Collectors.toList());

        GetGrpDillDownDimResponseType responseType = new GetGrpDillDownDimResponseType();
        responseType.setDrillDownDimList(dimChineseNameList);
        return responseType;
    }

    private static List<GrpConfigEntity.DimDrillDownEnum> getDimDrillDownEnums(GrpConfigEntity config, GetGrpDillDownDimResponseTypeV2 ret) {
        GrpConfigEntity.DimDrillDownConfig dimDillDownConfig = config.getDimDrillDownConfig();
        List<GrpConfigEntity.DimDrillDownItem> dimDillDown = dimDillDownConfig.getDimDrillDown();
        dimDillDown.forEach(v -> {
            Meta meta = new Meta();
            meta.setIndicatorName(v.getIndicatorName());
            meta.setIndicatorNameCN(v.getIndicatorNameCn());
            ret.getData().getMeta().add(meta);
        });
        return dimDillDownConfig.getDimDrillDownEnum();
    }

    public GetGrpDillDownDimResponseTypeV2 getGrpDillDownDimV2(GetGrpDillDownDimRequestTypeV2 requestType) {
        // 获取配置文件
        GrpConfigEntity config = grpConfig.getGrpConfig();

        // 纬度
        GetGrpDillDownDimResponseTypeV2 ret = new GetGrpDillDownDimResponseTypeV2();
        // 一级纬度
        if (requestType.getDimName() == null || requestType.getDimName().isEmpty()) {
            ret.setData(new DSLResponseType());
            ret.getData().setData(new ArrayList<>());
            ret.getData().setMeta(new ArrayList<>());
            List<GrpConfigEntity.DimDrillDownEnum> dimDillDownEnumMapping = getDimDrillDownEnums(config, ret);
            if (dimDillDownEnumMapping == null) {
                return ret;
            }
            Map<String, String> dimDillDownEnum = null;
            for (GrpConfigEntity.DimDrillDownEnum v : dimDillDownEnumMapping) {
                List<String> businessLines = v.getBusinessLine();
                if (businessLines.contains(requestType.getBusinessType().toString())) {
                    dimDillDownEnum = v.getMapping();
                    break;
                }
            }
            if (dimDillDownEnum == null || dimDillDownEnum.get(requestType.getIndicatorCategory()) == null) {
                return ret;
            }
            Set<String> containsSet = Arrays.stream(dimDillDownEnum.get(requestType.getIndicatorCategory()).split(",")).collect(Collectors.toSet());
            List<Meta> metaFilter = ret.getData().getMeta().stream().filter(v -> containsSet.contains(v.getIndicatorNameCN())).collect(Collectors.toList());
            ret.getData().setMeta(metaFilter);
        }
        // domainConfig
        Integer businessLine = getBusinessLine(requestType.businessType);
        DomainConfig domainConfig = DomainConfig.getAndUpdateDomainConfigByBusinessLine(businessLine, grpConfig);
        // 二级纬度
        if (requestType.getDimName() != null && !requestType.getDimName().isEmpty()) {
            DSLRequestType dsl = new DSLRequestType();
            dsl.setIndicators(Collections.singletonList(requestType.getDimName()));
            dsl.setGroupBy(Collections.singletonList(requestType.getDimName()));
            QueryByDSLResponseType queryByDSLResponseType = queryDataByDSLWithBusinessLine(dsl, domainConfig, null, null);
            ret.setData(queryByDSLResponseType.getData());
        }
        return ret;
    }


    public GetGrpDillDownDimEnumResponseType getGrpDillDownDimEnum(GetGrpDillDownDimEnumRequestType requestType) throws Exception {

        GetGrpDillDownDimEnumResponseType responseType = new GetGrpDillDownDimEnumResponseType();
        List<String> dims = dimQueryService.queryDimData(requestType);
        if (StringUtils.equals(requestType.getDrillDownDim(), "是否参与秒杀")) {//NOSONAR
            List<String> handledDims = dims.stream().map(d -> StringUtils.equals(d, "0") ? "不参与" : "参与").collect(Collectors.toList());//NOSONAR
            responseType.setDimEnumList(handledDims);
        } else {
            responseType.setDimEnumList(dims);
        }

        return responseType;
    }

    public GetGrpDillDownDetailResponseType getGrpDillDownDetail(GetGrpMetricDataRequestType requestType) throws Exception {
        GetGrpDillDownDetailResponseType res = new GetGrpDillDownDetailResponseType();

        String metricCategoryEnum = requestType.getMetricCategorieName();

        Integer selectTab = requestType.getSelectTab();
        List<MetricEnum> metricEnums = Lists.newArrayList();
        if (Objects.equals(1, selectTab) || Objects.equals(2, selectTab)) {
            metricEnums = MetricCategoryEnum.getMetricEnumsByCategoryEnglishName(metricCategoryEnum);
            if (selectTab == 2) {
                metricEnums = metricEnums.stream().filter(metricEnum -> !metricEnum.equals(MetricEnum.GRP_INCOME_ACHIEVE_RATE) && !metricEnum.equals(MetricEnum.GRP_PROFIT_ACHIEVE_RATE)).collect(Collectors.toList());
            }
        } else {
            metricEnums = DiyMetricCategoryEnum.getMetricEnumsByCategoryEnglishName(metricCategoryEnum, requestType.getDateType());
        }

        Integer businessLine = requestType.getBusinessLine();

        // case "S40160":
        //                //肖吟元（跟团游负责人）
        //                return Collections.singletonList(1);
        //            case "S40160":
        //                //周舟（私家团负责人）
//        List<Integer> bizModes = Lists.newArrayList();
//        //李小林账号单独处理
//        if (StringUtils.equals(remoteConfig.getExternalConfig(ADMIN_EID), requestType.getEmpCode())) {
//            if (Objects.equals(0, businessLine)) {
//                bizModes.add(0);
//                bizModes.add(1);
//                bizModes.add(2);
//            } else if (Objects.equals(1, businessLine)) {
//                bizModes.add(businessLine);
//                bizModes.add(3);
//            } else if (Objects.equals(2, businessLine)) {
//                bizModes.add(businessLine);
//                bizModes.add(4);
//            }
//        } else {
//            bizModes.add(businessLine);
//            if (Objects.equals(1, businessLine)) {
//                bizModes.add(5);
//            } else {
//                bizModes.add(6);
//            }
//        }
        List<Map<String, GrpMetric>> result = metricEnums.parallelStream().map(metricEnum -> {
            IGrpBusinessMetricService service = ac.getBean(metricEnum.getEnglishName(), IGrpBusinessMetricService.class);

            GetGrpMetricDataRequestType paramRquest = new GetGrpMetricDataRequestType();
            BeanUtils.copyProperties(requestType, paramRquest);
            return service.queryMetricDillDownResult(paramRquest, metricEnum, 0, service);
        }).collect(Collectors.toList());
//        Map<Integer, List<Map<String, GrpMetric>>> result = bizModes
//                .parallelStream()
//                .collect(Collectors.toMap(Function.identity(), i ->
//                        metricEnums.parallelStream()
//                        .map(metricEnum -> {
//                                    IGrpBusinessMetricService service = ac.getBean(metricEnum.getEnglishName(), IGrpBusinessMetricService.class);
//
//                                    GetGrpMetricDataRequestType paramRquest = new GetGrpMetricDataRequestType();
//                                    BeanUtils.copyProperties(requestType, paramRquest);
//                                    if (Objects.equals(1, i)) {
//                                        paramRquest.setBusinessLine(1);
//                                    } else if (Objects.equals(2, i)) {
//                                        paramRquest.setBusinessLine(2);
//                                    } else if (Objects.equals(5, i)) {
//                                        paramRquest.setBusinessLine(1);
//                                        paramRquest.setEmpCode(remoteConfig.getExternalConfig(GRP_GRP_ADMIN_EID));
//                                    } else if (Objects.equals(6, i)) {
//                                        paramRquest.setBusinessLine(2);
//                                        paramRquest.setEmpCode(remoteConfig.getExternalConfig(GRP_PRV_ADMIN_EID));
//                                    }
//                                    return service.queryMetricDillDownResult(paramRquest, metricEnum, i, service);
//                                }
//                                ).collect(Collectors.toList())));

        List<String> enumDims = result.stream().flatMap(map -> {
            return map.keySet().stream();
        }).distinct().collect(Collectors.toList());

//        List<Map<String, GrpMetric>> allMetricRst = result.get(0);
//        if (businessLine == 1) {
//            allMetricRst = result.get(3);
//        } else if (businessLine == 2) {
//            allMetricRst = result.get(4);
//        }

        // 查询所有供应商名称对应的ID


        List<String> selectDimEnum = requestType.getSelectDimEnum();
        if (StringUtils.equals("是否参与秒杀", requestType.getDrillDownDim())) {//NOSONAR
            selectDimEnum = selectDimEnum.stream().map(d -> StringUtils.equals(d, "不参与") ? "0" : "1").collect(Collectors.toList());//NOSONAR
        }
        try {
            List<Map<String, GrpMetric>> finalAllMetricRst = result;
            List<String> finalSelectDimEnum = selectDimEnum;
            List<GrpTableRow> grpTableRows = enumDims.stream().filter(enumDim -> {
                if (CollectionUtils.isNotEmpty(finalSelectDimEnum)) {
                    return finalSelectDimEnum.contains(enumDim);
                }
                return true;
            }).map(enumDim -> {
                GrpTableRow grpTableRow = new GrpTableRow();
                if (StringUtils.equals("是否参与秒杀", requestType.getDrillDownDim())) {//NOSONAR
                    grpTableRow.setDimEnum(StringUtils.equals("0", enumDim) ? "不参与" : "参与");//NOSONAR
                } else {
                    grpTableRow.setDimEnum(enumDim);
                }


                if (CollectionUtils.isNotEmpty(finalAllMetricRst)) {

                    grpTableRow.setMetricList(finalAllMetricRst.stream().flatMap(map -> map.entrySet().stream()).filter(entry -> StringUtils.equals(entry.getKey(), enumDim)).map(Map.Entry::getValue).collect(Collectors.toList()));
                } else {
                    grpTableRow.setMetricList(Lists.newArrayList());
                }

                if (StringUtils.equals("业务经理", requestType.getDrillDownDim()) || StringUtils.equals("驻地业务经理", requestType.getDrillDownDim())) {//NOSONAR
                    EdwHrEmpVacation emp = depTreeCache.getEmpByEmpID(grpTableRow.getDimEnum());
                    if (emp != null) {
                        grpTableRow.setDimEnum(emp.getDisplayName());
                    }
                }

                return grpTableRow;
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(grpTableRows)) {
                for (GrpTableRow grpTableRow : grpTableRows) {
                    List<GrpMetric> metricList = grpTableRow.getMetricList();
                    List<GrpMetric> busLine1MetricList = grpTableRow.getBusLine1MetricList();
                    List<GrpMetric> busLine2MetricList = grpTableRow.getBusLine2MetricList();
//                    if (CollectionUtils.isEmpty(metricList) || CollectionUtils.isEmpty(busLine1MetricList)
//                    || CollectionUtils.isEmpty(busLine2MetricList)) {
//
//                        if (CollectionUtils.isEmpty(metricList) ) {
//
//                            List<GrpMetric> grpMetrics = Lists.newArrayList();
//                            if (CollectionUtils.isNotEmpty(busLine1MetricList)) {
//                                grpMetrics = busLine1MetricList;
//                            } else if (CollectionUtils.isNotEmpty(busLine2MetricList)) {
//                                grpMetrics = busLine2MetricList;
//                            }
//                            List<GrpMetric> copyResult = Lists.newArrayList();
//                            for (GrpMetric grpMetric : grpMetrics) {
//                                GrpMetric metric = new GrpMetric();
//                                BeanUtils.copyProperties(grpMetric, metric);
//                                metric.setMetricValue(null);
//                                metric.setExtraValues(null);
//                                copyResult.add(metric);
//                            }
//                            grpTableRow.setMetricList(copyResult);
//                        }
//
//                        if (CollectionUtils.isEmpty(busLine1MetricList) ) {
//                            List<GrpMetric> grpMetrics = Lists.newArrayList();
//                            if (CollectionUtils.isNotEmpty(metricList)) {
//                                grpMetrics = metricList;
//                            } else if (CollectionUtils.isNotEmpty(busLine2MetricList)) {
//                                grpMetrics = busLine2MetricList;
//                            }
//
//                            List<GrpMetric> copyResult = Lists.newArrayList();
//                            for (GrpMetric grpMetric : grpMetrics) {
//                                GrpMetric metric = new GrpMetric();
//                                BeanUtils.copyProperties(grpMetric, metric);
//                                metric.setMetricValue(null);
//                                metric.setExtraValues(null);
//                                copyResult.add(metric);
//                            }
//                            grpTableRow.setBusLine1MetricList(copyResult);
//                        }
//
//                        if (CollectionUtils.isEmpty(busLine2MetricList) ) {
//                            List<GrpMetric> grpMetrics = Lists.newArrayList();
//                            if (CollectionUtils.isNotEmpty(metricList)) {
//                                grpMetrics = metricList;
//                            } else if (CollectionUtils.isNotEmpty(busLine1MetricList)) {
//                                grpMetrics = busLine1MetricList;
//                            }
//
//                            List<GrpMetric> copyResult = Lists.newArrayList();
//                            for (GrpMetric grpMetric : grpMetrics) {
//                                GrpMetric metric = new GrpMetric();
//                                BeanUtils.copyProperties(grpMetric, metric);
//                                metric.setMetricValue(null);
//                                metric.setExtraValues(null);
//                                copyResult.add(metric);
//                            }
//                            grpTableRow.setBusLine2MetricList(copyResult);
//                        }
//
//                    }
                }
            }
            res.setTableRows(grpTableRows);
        } catch (Exception e) {
            log.warn("assembly tablerow error", e);
        }

        return res;
    }


//    public GetGrpDillDownDetailResponseType getGrpDillDownDetailV3(GetGrpMetricDataRequestType requestType) throws Exception {
//        TopRankingRequestType topRankingRequestType = new TopRankingRequestType();
//        // emp
//        topRankingRequestType.setEmpCodes(Collections.singletonList(requestType.getEmpCode()));
//        // 时间
//        topRankingRequestType.setStartDate(requestType.getStartDate());
//        topRankingRequestType.setEndDate(requestType.getEndDate());
//        // 省份
//        topRankingRequestType.setProvNames(requestType.getAreas());
//        // 产品形态
//        topRankingRequestType.setProductPattern(requestType.getProvNameList());
//        // 同环比
//        topRankingRequestType.setCompareConfig(Arrays.asList("hb", "hb_year"));
//        // 指标
//        switch (requestType.getBusinessLine()) {
//            case 1:
//                topRankingRequestType.setBusinessType(100);
//                break;
//            case 2:
//                topRankingRequestType.setBusinessType(200);
//                break;
//            case 3:
//                topRankingRequestType.setBusinessType(230);
//                break;
//        }
//        // 领域模型配置文件
//        domainConfig.updateDomainWithBusinessLine(topRankingRequestType.getBusinessType());
//        topRankingRequestType.setIndicators(getIndicators(topRankingRequestType.getBusinessType(), Collections.singletonList(requestType.getMetricCategorieName())));
//        // gourpby
//        Map<String, Object> config = domainConfig.getConfig();
//        List<Map<String, String>> configList = (List<Map<String, String>>) ((Map<String, Object>) config.get("dim_dill_down_config")).get("dim_dill_down");
//        List<String> groupBys = configList.stream().filter(v -> v.get("indicator_name_cn").equals(requestType.getDrillDownDim())).map(v -> v.get("indicator_name")).collect(Collectors.toList());
//        topRankingRequestType.setGroupBy(groupBys);
//        topRankingRequestType.getIndicators().addAll(groupBys);
//        // filter
//        topRankingRequestType.setDimFilterValue(requestType.getSelectDimEnum());
//        // 查询
//        TopRankingResponseType topRankingResponseType = queryTopRanking(topRankingRequestType);
//        // response
//        List<GrpMetricCategorie> metric = getMetric(topRankingRequestType.getBusinessType(), Collections.singletonList(requestType.getMetricCategorieName()));
//        return null;
//    }

    public DownloadGrpDillDownDetailResponseType downloadGrpDillDownDetail(DownloadGrpDillDownDetailRequestType requestType) throws Exception {

        DownloadGrpDillDownDetailResponseType res = new DownloadGrpDillDownDetailResponseType();
        GetGrpMetricDataRequestType metricDataRequestType = new GetGrpMetricDataRequestType();
        BeanUtils.copyProperties(requestType, metricDataRequestType);
        metricDataRequestType.setSelectDimEnum(Lists.newArrayList());
        GetGrpDillDownDetailResponseType grpDillDownDetail = getGrpDillDownDetail(metricDataRequestType);
        List<GrpTableRow> tableRows = grpDillDownDetail.getTableRows();
        String exportDillDownFileUrl = grpDillDownExcelExporter.getExportDillDownFileUrl(tableRows, requestType.getMetricCategorieName(), requestType.getDrillDownDim());
        res.setFileUrl(exportDillDownFileUrl);
        return res;
    }

    public GetGrpFirstPageMetricCardResponseType getGrpFirstPageMetricCard(GetGrpFirstPageMetricCardRequestType requestType) throws Exception {
        GetGrpFirstPageMetricCardResponseType firstPageMetricCardResponseType = new GetGrpFirstPageMetricCardResponseType();

        String mappingEmpCode = userInfoService.getMappingEmpCode();

        if (StringUtils.equals("S00546", mappingEmpCode)) {
            return getGrpLeaderFirstPageMetricCard(requestType, Lists.newArrayList(1, 2, 3), mappingEmpCode);
        } else if (StringUtils.equals("S40160", mappingEmpCode)) {
            return getGrpLeaderFirstPageMetricCard(requestType, Lists.newArrayList(2, 3), mappingEmpCode);
        }

        GetGrpOrganizationResponseType getGrpOrganizationResponseType = getGrpOrganization(new GetGrpOrganizationRequestType());
        Boolean havePermission = getGrpOrganizationResponseType.isHavePermission();
        List<GrpOrganizationNode> grpOrganizationNodeList = getGrpOrganizationResponseType.getOrganizationItemList();

        if (!havePermission) {
            return firstPageMetricCardResponseType;
        }
        if (CollectionUtils.isEmpty(grpOrganizationNodeList)) {
            return firstPageMetricCardResponseType;
        }

        GetGrpEmployeeRequestType employeeRequestType = new GetGrpEmployeeRequestType();
        employeeRequestType.setOrgId(grpOrganizationNodeList.get(0).getOrgId());
        GetGrpEmployeeResponseType employeeResponseType = getGrpEmployee(employeeRequestType);
        List<GrpEmployeeItem> employeeItemList = employeeResponseType.getEmployeeItemList();
        if (CollectionUtils.isEmpty(employeeItemList)) {
            return new GetGrpFirstPageMetricCardResponseType();
        }

        Integer businessLine = null;
        try {
            businessLine = employeeItemList.get(0).getBusinessLine().get(0);
        } catch (Exception e) {
            log.warn("get businessLine error", e);
            return new GetGrpFirstPageMetricCardResponseType();
        }
        String empCode = employeeItemList.get(0).getEmpCode();

        //小林


        GetGrpMetricDataRequestType metricDataRequestType = new GetGrpMetricDataRequestType();
        metricDataRequestType.setStartDate(requestType.getStartDate());
        metricDataRequestType.setEndDate(requestType.getEndDate());
        metricDataRequestType.setBusinessLine(businessLine);
        metricDataRequestType.setSelectTab(businessLine);
        metricDataRequestType.setEmpCode(empCode);

        List<String> cates = Lists.newArrayList(MetricCategoryEnum.INCOME_CATEGORY.getEnglishName(), MetricCategoryEnum.PROFIT_PRICE_CATEGORY.getEnglishName());
        if (businessLine == 1) {
            cates.add(MetricCategoryEnum.UV_CATEGORY.getEnglishName());
            cates.add(MetricCategoryEnum.CONVERSION_RATE_CATEGORY.getEnglishName());
        }

        //查询指标卡
        GetGrpMetricCardResponseType metricCardResponseType = getGrpMetricCard(metricDataRequestType, cates);
        List<GrpMetricCategorie> metricCategories = metricCardResponseType.getMetricCardList();

        //查询销售额趋势线
        metricDataRequestType.setAggregationGranularity("day");
        metricDataRequestType.setMetricCategorieName(MetricCategoryEnum.INCOME_CATEGORY.getEnglishName());
        GetGrpTrendLineResponseType incomeMetricTrendLineDataResponseType = getGrpTrendLine(metricDataRequestType);


        //查询利润趋势线
        metricDataRequestType.setMetricCategorieName(MetricCategoryEnum.PROFIT_PRICE_CATEGORY.getEnglishName());
        GetGrpTrendLineResponseType profitPriceTrendLineDataResponseType = getGrpTrendLine(metricDataRequestType);


        List<GrpFirstPageMetricCategorie> firstPageMetricCategories = new ArrayList<>();
        for (GrpMetricCategorie metricCategorie : metricCategories) {

            if (metricCategorie.getMetricCategorieName().equals(MetricCategoryEnum.INCOME_CATEGORY.getEnglishName()) || metricCategorie.getMetricCategorieName().equals(MetricCategoryEnum.PROFIT_PRICE_CATEGORY.getEnglishName())) {
                List<GrpMetric> metricList = metricCategorie.getMetricList();
                metricList = metricList.stream().filter(metric -> metric.getMetricName().equals(MetricEnum.INCOME.getEnglishName()) || metric.getMetricName().equals(MetricEnum.DIY_INCOME.getEnglishName()) || metric.getMetricName().equals(MetricEnum.PROFIT_PRICE.getEnglishName()) || metric.getMetricName().equals(MetricEnum.DIY_PROFIT.getEnglishName())).collect(Collectors.toList());
                metricCategorie.setMetricList(metricList);
            } else if (businessLine != 1 && (!metricCategorie.getMetricCategorieName().equals(MetricCategoryEnum.UV_CATEGORY.getEnglishName()) && !metricCategorie.getMetricCategorieName().equals(MetricCategoryEnum.CONVERSION_RATE_CATEGORY.getEnglishName()))) {
                continue;
            }

            GrpFirstPageMetricCategorie firstPageMetricCategorie = new GrpFirstPageMetricCategorie();
            firstPageMetricCategorie.setMetricCategorieName(metricCategorie.getMetricCategorieName());
            firstPageMetricCategorie.setMetricCategorieTipsSharkkey(metricCategorie.getMetricCategorieTipsSharkkey());
            firstPageMetricCategorie.setMetricList(metricCategorie.getMetricList());

//            firstPageMetricCategorie.setRank(metricCategorie.getRank());
//            firstPageMetricCategorie.setRankRatio(metricCategorie.getRankRatio());

            if (metricCategorie.getMetricCategorieName().equals(MetricCategoryEnum.INCOME_CATEGORY.getEnglishName())) {
                firstPageMetricCategorie.setTrendLine(incomeMetricTrendLineDataResponseType.getTrendLine());
            } else if (metricCategorie.getMetricCategorieName().equals(MetricCategoryEnum.PROFIT_PRICE_CATEGORY.getEnglishName())) {
                firstPageMetricCategorie.setTrendLine(profitPriceTrendLineDataResponseType.getTrendLine());
            }
            firstPageMetricCategories.add(firstPageMetricCategorie);
        }


        firstPageMetricCardResponseType.setFirstPageMetricCardList(firstPageMetricCategories);
        return firstPageMetricCardResponseType;
    }

    private GetGrpFirstPageMetricCardResponseType getGrpLeaderFirstPageMetricCard(GetGrpFirstPageMetricCardRequestType requestType, List<Integer> bizLines, String empCode) throws Exception {


        List<ArrayList<GrpMetricCategorie>> totalMericCardCates = bizLines.parallelStream().map(i -> {
            GetGrpMetricDataRequestType metricDataRequestType = new GetGrpMetricDataRequestType();
            metricDataRequestType.setStartDate(requestType.getStartDate());
            metricDataRequestType.setEndDate(requestType.getEndDate());
            metricDataRequestType.setBusinessLine(i);
            metricDataRequestType.setEmpCode(empCode);
            metricDataRequestType.setSelectTab(i);
            //查询指标卡
            ArrayList<GrpMetricCategorie> result = Lists.newArrayList();
            try {
                GetGrpMetricCardResponseType metricCardResponseType = getMetricCard(metricDataRequestType, Lists.newArrayList(MetricCategoryEnum.INCOME_CATEGORY.getEnglishName(), MetricCategoryEnum.PROFIT_PRICE_CATEGORY.getEnglishName()));
                result.addAll(metricCardResponseType.getMetricCardList());
            } catch (Exception e) {
                log.warn("query metricCard error", e);
            }
            return result;
        }).collect(Collectors.toList());

        Double incomeSum = 0.0;
        double incomeSumLastMon = 0.0;


        Double profitSum = 0.0;
        double profitSumLastMon = 0.0;

        for (ArrayList<GrpMetricCategorie> totalMericCardCate : totalMericCardCates) {
            for (GrpMetricCategorie grpMetricCategorie : totalMericCardCate) {
                GrpMetric incomMetric = grpMetricCategorie.getMetricList().stream().filter(m -> StringUtils.equals(m.getMetricName(), MetricEnum.INCOME.getEnglishName()) || StringUtils.equals(m.getMetricName(), MetricEnum.DIY_INCOME.getEnglishName())).findFirst().orElse(null);
                if (Objects.nonNull(incomMetric)) {
                    if (incomMetric.getMetricValue() != null) {
                        incomeSum += incomMetric.getMetricValue();
                    }
                    if (Objects.nonNull(incomMetric.getExtraValues()) && Objects.nonNull(incomMetric.getExtraValues().getMonthOverMonth()))
                        incomeSumLastMon += incomMetric.getMetricValue() / (incomMetric.getExtraValues().getMonthOverMonth() + 1);
                }

                GrpMetric profitMetric = grpMetricCategorie.getMetricList().stream().filter(m -> StringUtils.equals(m.getMetricName(), MetricEnum.PROFIT_PRICE.getEnglishName()) || StringUtils.equals(m.getMetricName(), MetricEnum.DIY_PROFIT.getEnglishName())).findFirst().orElse(null);
                if (Objects.nonNull(profitMetric)) {
                    if (profitMetric.getMetricValue() != null) {
                        profitSum += profitMetric.getMetricValue();
                    }
                    if (Objects.nonNull(profitMetric.getExtraValues()) && Objects.nonNull(profitMetric.getExtraValues().getMonthOverMonth()))
                        profitSumLastMon += profitMetric.getMetricValue() / (profitMetric.getExtraValues().getMonthOverMonth() + 1);
                }
            }
        }

        List<Integer> tdIndexLine = Lists.newArrayList();
        if (bizLines.size() == 2) {
            tdIndexLine.add(0);
            tdIndexLine.add(1);
        } else {
            tdIndexLine.add(0);
            tdIndexLine.add(1);
            tdIndexLine.add(2);
        }

        List<Map<String, GetGrpTrendLineResponseType>> tdLineTotal = tdIndexLine.parallelStream().map(i -> {
            return Lists.newArrayList(MetricCategoryEnum.INCOME_CATEGORY.getEnglishName(), MetricCategoryEnum.PROFIT_PRICE_CATEGORY.getEnglishName()).stream().collect(Collectors.toMap(cate -> cate + "-" + i, cate -> {
                GetGrpMetricDataRequestType metricDataRequestType = new GetGrpMetricDataRequestType();
                metricDataRequestType.setStartDate(requestType.getStartDate());
                metricDataRequestType.setEndDate(requestType.getEndDate());
                metricDataRequestType.setBusinessLine(i);
                metricDataRequestType.setEmpCode(empCode);
                metricDataRequestType.setMetricCategorieName(cate);
                metricDataRequestType.setSelectTab(i);
                metricDataRequestType.setAggregationGranularity("day");
                try {
                    return getGrpTrendLine(metricDataRequestType);
                } catch (Exception e) {
                    log.warn("getGrpLeaderFirstPageMetricCard error", e);
                }
                return null;
            }));
        }).filter(Objects::nonNull).collect(Collectors.toList());

        Map<String, GetGrpTrendLineResponseType> tdLine1Map = tdLineTotal.get(0);
        Map<String, GetGrpTrendLineResponseType> tdLine2Map = tdLineTotal.get(1);

        Map<String, List<GrpMetric>> incomeLine3DetailMap = null;
        Map<String, List<GrpMetric>> profitLine3DetailMap = null;
        if (bizLines.size() == 3) {
            Map<String, GetGrpTrendLineResponseType> tdLine3Map = tdLineTotal.get(2);
            GetGrpTrendLineResponseType incomeLine3Resp = tdLine3Map.get(MetricCategoryEnum.INCOME_CATEGORY.getEnglishName() + "-" + 2);
            GetGrpTrendLineResponseType profitLine3Resp = tdLine3Map.get(MetricCategoryEnum.PROFIT_PRICE_CATEGORY.getEnglishName() + "-" + 2);
            incomeLine3DetailMap = incomeLine3Resp.getTrendLine().stream().collect(Collectors.toMap(GrpTrendLinePoint::getDate, GrpTrendLinePoint::getTrendLinePoints));
            profitLine3DetailMap = profitLine3Resp.getTrendLine().stream().collect(Collectors.toMap(GrpTrendLinePoint::getDate, GrpTrendLinePoint::getTrendLinePoints));
        }


        GetGrpTrendLineResponseType incomeLine1Resp = tdLine1Map.get(MetricCategoryEnum.INCOME_CATEGORY.getEnglishName() + "-" + 0);
        GetGrpTrendLineResponseType incomeLine2Resp = tdLine2Map.get(MetricCategoryEnum.INCOME_CATEGORY.getEnglishName() + "-" + 1);


        GetGrpTrendLineResponseType profitLine1Resp = tdLine1Map.get(MetricCategoryEnum.PROFIT_PRICE_CATEGORY.getEnglishName() + "-" + 0);
        GetGrpTrendLineResponseType profitLine2Resp = tdLine2Map.get(MetricCategoryEnum.PROFIT_PRICE_CATEGORY.getEnglishName() + "-" + 1);


        Map<String, List<GrpMetric>> incomeLine2DetailMap = incomeLine2Resp.getTrendLine().stream().collect(Collectors.toMap(GrpTrendLinePoint::getDate, GrpTrendLinePoint::getTrendLinePoints));


        Map<String, List<GrpMetric>> profitLine2DetailMap = profitLine2Resp.getTrendLine().stream().collect(Collectors.toMap(GrpTrendLinePoint::getDate, GrpTrendLinePoint::getTrendLinePoints));


        ArrayList<GrpTrendLinePoint> inComeTotalTdResults = Lists.newArrayList();

        for (GrpTrendLinePoint linePoint : incomeLine1Resp.getTrendLine()) {

            try {
                String date = linePoint.getDate();

                GrpTrendLinePoint totalTdRes = new GrpTrendLinePoint();
                totalTdRes.setDate(date);

                double incomeTdSum = 0.0;
                double incomeTdLastYearSum = 0.0;

                List<GrpMetric> trendLinePoints = linePoint.getTrendLinePoints();
                GrpMetric metric1 = trendLinePoints.get(0);
                if (Objects.nonNull(metric1) && Objects.nonNull(metric1.getMetricValue())) {
                    incomeTdSum += metric1.getMetricValue();
                }
                if (Objects.nonNull(metric1.getExtraValues()) && Objects.nonNull(metric1.getExtraValues().getYearOverYear()) && metric1.getExtraValues().getYearOverYear() != -1.0) {
                    incomeTdLastYearSum += metric1.getMetricValue() / (metric1.getExtraValues().getYearOverYear() + 1);

                }

                List<GrpMetric> metrics2 = incomeLine2DetailMap.get(date);
                if (CollectionUtils.isNotEmpty(metrics2)) {
                    GrpMetric metric2 = metrics2.get(0);

                    if (Objects.nonNull(metric2) && Objects.nonNull(metric2.getMetricValue())) {
                        incomeTdSum += metric2.getMetricValue();
                    }
                    if (Objects.nonNull(metric2.getExtraValues()) && Objects.nonNull(metric2.getExtraValues().getYearOverYear()) && metric2.getExtraValues().getYearOverYear() != -1.0) {
                        incomeTdLastYearSum += metric2.getMetricValue() / (metric2.getExtraValues().getYearOverYear() + 1);
                    }
                }

                if (MapUtils.isNotEmpty(incomeLine3DetailMap)) {
                    List<GrpMetric> metrics3 = incomeLine3DetailMap.get(date);
                    if (CollectionUtils.isNotEmpty(metrics3)) {
                        GrpMetric metric3 = metrics3.get(0);
                        if (Objects.nonNull(metric3) && Objects.nonNull(metric3.getMetricValue())) {
                            incomeTdSum += metric3.getMetricValue();
                        }
                        if (Objects.nonNull(metric3.getExtraValues()) && Objects.nonNull(metric3.getExtraValues().getYearOverYear()) && metric3.getExtraValues().getYearOverYear() != -1.0) {
                            incomeTdLastYearSum += metric3.getMetricValue() / (metric3.getExtraValues().getYearOverYear() + 1);
                        }
                    }
                }


                GrpMetric metric = new GrpMetric();
                metric.setMetricName(MetricEnum.INCOME.getEnglishName());
                metric.setMetricValue(incomeTdSum);
                metric.setMetricTipsSharkKey(MetricEnum.INCOME.getMetricTipsSharkKey());
                BigDecimal yoy = calcRatio(BigDecimal.valueOf(incomeTdLastYearSum), BigDecimal.valueOf(incomeTdSum));
                ExtraValues extraValues = new ExtraValues();
                extraValues.setYearOverYear(yoy == null ? null : yoy.doubleValue());
                metric.setExtraValues(extraValues);
                totalTdRes.setTrendLinePoints(Lists.newArrayList(metric));
                inComeTotalTdResults.add(totalTdRes);
            } catch (Exception e) {
                System.out.println("" + linePoint);
                log.warn("handle futres error", e);
            }

        }

        ArrayList<GrpTrendLinePoint> profitTotalTdResults = Lists.newArrayList();

        for (GrpTrendLinePoint linePoint : profitLine1Resp.getTrendLine()) {

            String date = linePoint.getDate();

            GrpTrendLinePoint totalTdRes = new GrpTrendLinePoint();
            totalTdRes.setDate(date);

            double profitTdSum = 0.0;
            double profitTdLastYearSum = 0.0;

            List<GrpMetric> trendLinePoints = linePoint.getTrendLinePoints();
            GrpMetric metric1 = trendLinePoints.get(0);
            if (Objects.nonNull(metric1) && Objects.nonNull(metric1.getMetricValue())) {
                profitTdSum += metric1.getMetricValue();
            }
            if (Objects.nonNull(metric1.getExtraValues()) && Objects.nonNull(metric1.getExtraValues().getYearOverYear()) && metric1.getExtraValues().getYearOverYear() != -1.0) {
                profitTdLastYearSum += metric1.getMetricValue() / (metric1.getExtraValues().getYearOverYear() + 1);

            }

            List<GrpMetric> metrics2 = profitLine2DetailMap.get(date);
            if (CollectionUtils.isNotEmpty(metrics2)) {
                GrpMetric metric2 = metrics2.get(0);
                if (Objects.nonNull(metric2) && Objects.nonNull(metric2.getMetricValue())) {
                    profitTdSum += metric2.getMetricValue();
                }
                if (Objects.nonNull(metric2.getExtraValues()) && Objects.nonNull(metric2.getExtraValues().getYearOverYear()) && metric2.getExtraValues().getYearOverYear() != -1.0) {
                    profitTdLastYearSum += metric2.getMetricValue() / (metric2.getExtraValues().getYearOverYear() + 1);
                }
            }

            if (MapUtils.isNotEmpty(profitLine3DetailMap)) {
                List<GrpMetric> metrics3 = profitLine3DetailMap.get(date);
                if (CollectionUtils.isNotEmpty(metrics3)) {
                    GrpMetric metric3 = metrics3.get(0);
                    if (Objects.nonNull(metric3) && Objects.nonNull(metric3.getMetricValue())) {
                        profitTdSum += metric3.getMetricValue();
                    }
                    if (Objects.nonNull(metric3.getExtraValues()) && Objects.nonNull(metric3.getExtraValues().getYearOverYear()) && metric3.getExtraValues().getYearOverYear() != -1.0) {
                        profitTdLastYearSum += metric3.getMetricValue() / (metric3.getExtraValues().getYearOverYear() + 1);
                    }
                }
            }


            GrpMetric metric = new GrpMetric();
            metric.setMetricName(MetricEnum.INCOME.getEnglishName());
            metric.setMetricValue(profitTdSum);
            metric.setMetricTipsSharkKey(MetricEnum.INCOME.getMetricTipsSharkKey());
            BigDecimal yoy = calcRatio(BigDecimal.valueOf(profitTdLastYearSum), BigDecimal.valueOf(profitTdSum));
            ExtraValues extraValues = new ExtraValues();
            extraValues.setYearOverYear(yoy == null ? null : yoy.doubleValue());
            metric.setExtraValues(extraValues);
            totalTdRes.setTrendLinePoints(Lists.newArrayList(metric));
            profitTotalTdResults.add(totalTdRes);
        }


        GrpFirstPageMetricCategorie fstIncomePageMetricCate = new GrpFirstPageMetricCategorie();
        GrpMetric incomMetric = new GrpMetric();
        incomMetric.setMetricName(MetricEnum.INCOME.getEnglishName());
        incomMetric.setMetricValue(incomeSum);
        incomMetric.setIsRatioValue(false);
        incomMetric.setMetricTipsSharkKey(MetricEnum.INCOME.getMetricTipsSharkKey());
        ExtraValues extraValues = new ExtraValues();
        BigDecimal mom = calcRatio(BigDecimal.valueOf(incomeSumLastMon), BigDecimal.valueOf(incomeSum));
        extraValues.setMonthOverMonth(mom == null ? null : mom.doubleValue());
        incomMetric.setExtraValues(extraValues);

        fstIncomePageMetricCate.setMetricCategorieName(MetricCategoryEnum.INCOME_CATEGORY.getEnglishName());
        fstIncomePageMetricCate.setMetricCategorieTipsSharkkey(MetricCategoryEnum.INCOME_CATEGORY.getCategoryTipsSharkKey());
        fstIncomePageMetricCate.setMetricList(Lists.newArrayList(incomMetric));
        fstIncomePageMetricCate.setTrendLine(inComeTotalTdResults);


        GrpFirstPageMetricCategorie fstProfitPageMetricCate = new GrpFirstPageMetricCategorie();
        GrpMetric profitMetric = new GrpMetric();
        profitMetric.setMetricName(MetricEnum.PROFIT_PRICE.getEnglishName());
        profitMetric.setMetricValue(profitSum);
        profitMetric.setIsRatioValue(false);
        profitMetric.setMetricTipsSharkKey(MetricEnum.PROFIT_PRICE.getMetricTipsSharkKey());
        ExtraValues extraValuesProfit = new ExtraValues();
        BigDecimal momProfit = calcRatio(BigDecimal.valueOf(profitSumLastMon), BigDecimal.valueOf(profitSum));
        extraValuesProfit.setMonthOverMonth(momProfit == null ? null : momProfit.doubleValue());
        profitMetric.setExtraValues(extraValuesProfit);

        fstProfitPageMetricCate.setMetricCategorieName(MetricCategoryEnum.PROFIT_PRICE_CATEGORY.getEnglishName());
        fstProfitPageMetricCate.setMetricCategorieTipsSharkkey(MetricCategoryEnum.PROFIT_PRICE_CATEGORY.getCategoryTipsSharkKey());
        fstProfitPageMetricCate.setMetricList(Lists.newArrayList(profitMetric));
        fstProfitPageMetricCate.setTrendLine(profitTotalTdResults);

        ArrayList<GrpFirstPageMetricCategorie> grpFirstPageMetricCategories = Lists.newArrayList(fstIncomePageMetricCate, fstProfitPageMetricCate);
        GetGrpFirstPageMetricCardResponseType responseType = new GetGrpFirstPageMetricCardResponseType();
        responseType.setFirstPageMetricCardList(grpFirstPageMetricCategories);
        return responseType;
    }

    private BigDecimal calcRatio(BigDecimal a, BigDecimal b) {
        if (Objects.isNull(a) || BigDecimal.ZERO.compareTo(a) == 0) {
            return null;
        }
        return b.subtract(a).divide(a, 5, RoundingMode.HALF_UP);
    }


    // ==================================模版查询======================================
    public QueryByDSLResponseType queryDataByDSL(DSLRequestType dsl) {
        // json格式化
//        ObjectMapper mapper = new ObjectMapper();
//        String jsonString = null;
//        try {
//            jsonString = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(dsl);
//        } catch (JsonProcessingException e) {
//            throw new RuntimeException(e);
//        }
//        System.out.println(jsonString);
        // dsl查询
        DomainConfig domainConfig = DomainConfig.getAndUpdateDomainConfigByBusinessLine(100, grpConfig);
        return queryDataByDSLWithBusinessLine(dsl, domainConfig, null, null);
    }

    public QueryByDSLResponseType queryDataByDSLWithBusinessLine(DSLRequestType dsl, DomainConfig domainConfig, List<AbstractPreDSLProcess> dslProcesses, List<AbstractAfterDataProcess> dataProcesses) {
        // 注册数据源
        DomainQuery.RegDataSource("starrocks", startRocksQuery);
        // 查询
        ResultData resultData = DomainQuery.withDomainConfig(domainConfig).withDSLProcess(dslProcesses).withDataProcess(dataProcesses).queryWithDSL(dsl);
        QueryByDSLResponseType resp = new QueryByDSLResponseType();
        resp.data = DSLUtils.formatResponseType(resultData);
        return resp;
    }

    public PerformanceCardResponseType queryPerformanceCard(PerformanceCardRequestType performanceCardRequestType) throws Exception {
        // 初始化
        DSLRequestType dsl = new DSLRequestType();
        dsl.setIndicators(performanceCardRequestType.getIndicators());

        // 前置dsl处理
        // business line
        // emp
        // date 初始化
        // date 同环比
        // 目的地，跟团+私家团
        // 产线，跟团+私家团
        // 评级区域
        // 日期类型，定制游
        // 供应商、销售的指标注入
        List<AbstractPreDSLProcess> dslProcesses = Arrays.asList(
                // business line
                DSLProcessBusinessLine.getInstance(performanceCardRequestType.getBusinessType(), hrOrgEmpInfoService, grpConfig),
                // emp
                DSLProcessEmps.getInstance(userInfoService, depTreeCache, performanceCardRequestType.getEmpCodes()),
                // date 初始化
                DSLProcessDateSet.getInstance(performanceCardRequestType.getStartDate(), performanceCardRequestType.getEndDate()),
                // date 同环比
                DSLProcessCompareSet.getInstance(performanceCardRequestType.getCompareConfig()),
                // 目的地，跟团+私家团
                DSLProcessProvNamesPares.getInstance(performanceCardRequestType.getProvNames()),
                // 产线，跟团+私家团
                DSLProcessProductPatternPares.getInstance(performanceCardRequestType.getProductPattern()),
                // 评级区域
                DSLProcessAreaPares.getInstance(performanceCardRequestType.getArea(), custEmpOrgInfoService),
                // 日期类型，定制游
                DSLProcessDateTypePares.getInstance(performanceCardRequestType.getDateType()),
                // 供应商、销售的指标注入
                DSLProcessDIYIndicatorInject.getInstance()
        );
        // 数据后置处理
        List<AbstractAfterDataProcess> dataProcesses = Collections.singletonList(
                // 指标过滤
                AfterDataProcessFilterAchieve.getInstance()
        );

        // 查询
        QueryByDSLResponseType queryByDSLResponseType = queryDataByDSLWithBusinessLine(dsl, null, dslProcesses, dataProcesses);
        PerformanceCardResponseType performanceCardResponseType = new PerformanceCardResponseType();
        performanceCardResponseType.setData(queryByDSLResponseType.getData());
        return performanceCardResponseType;
    }


    public ChangeTrendResponseType queryChangeTrend(ChangeTrendRequestType changeTrendRequestType) throws Exception {
        // 初始化
        DSLRequestType dsl = new DSLRequestType();
        dsl.setIndicators(changeTrendRequestType.getIndicators());

        // 前置dsl处理
        List<AbstractPreDSLProcess> dslProcesses = Arrays.asList(
                // business line
                DSLProcessBusinessLine.getInstance(changeTrendRequestType.getBusinessType(), hrOrgEmpInfoService, grpConfig),
                // emp
                DSLProcessEmps.getInstance(userInfoService, depTreeCache, changeTrendRequestType.getEmpCodes()),
                // date 初始化
                DSLProcessDateSet.getInstance(changeTrendRequestType.getStartDate(), changeTrendRequestType.getEndDate()),
                // group by
                DSLProcessGroupBySet.getInstance(changeTrendRequestType.getGroupBy()),
                // date 同环比
                DSLProcessCompareSet.getInstance(changeTrendRequestType.getCompareConfig()),
                // 目的地，跟团+私家团
                DSLProcessProvNamesPares.getInstance(changeTrendRequestType.getProvNames()),
                // 产线，跟团+私家团
                DSLProcessProductPatternPares.getInstance(changeTrendRequestType.getProductPattern()),
                // 评级区域
                DSLProcessAreaPares.getInstance(changeTrendRequestType.getArea(), custEmpOrgInfoService),
                // 日期类型，定制游
                DSLProcessDateTypePares.getInstance(changeTrendRequestType.getDateType()),
                // 供应商、销售的指标注入
                DSLProcessDIYIndicatorInject.getInstance()
        );
        // 数据后置处理
        List<AbstractAfterDataProcess> dataProcesses = Collections.singletonList(
                // 日期排序
                AfterDataProcessSortTrendData.getInstance()
        );
        // 查询
        QueryByDSLResponseType queryByDSLResponseType = queryDataByDSLWithBusinessLine(dsl, null, dslProcesses, dataProcesses);
        ChangeTrendResponseType changeTrendResponseType = new ChangeTrendResponseType();
        changeTrendResponseType.setData(queryByDSLResponseType.getData());
        return changeTrendResponseType;
    }

    public TopRankingResponseType queryTopRanking(TopRankingRequestType topRankingRequestType) throws Exception {
        // 初始化
        DSLRequestType dsl = new DSLRequestType();
        dsl.setIndicators(topRankingRequestType.getIndicators());
        // 前置dsl处理
        List<AbstractPreDSLProcess> dslProcesses = Arrays.asList(
                // business line
                DSLProcessBusinessLine.getInstance(topRankingRequestType.getBusinessType(), hrOrgEmpInfoService, grpConfig),
                // emp
                DSLProcessEmps.getInstance(userInfoService, depTreeCache, topRankingRequestType.getEmpCodes()),
                // date 初始化
                DSLProcessDateSet.getInstance(topRankingRequestType.getStartDate(), topRankingRequestType.getEndDate()),
                // group by
                DSLProcessGroupBySet.getInstance(topRankingRequestType.getGroupBy()),
                // order by
                DSLProcessOrderFieldSet.getInstance(Collections.singletonList(new OrderBy(topRankingRequestType.getOrderField(), topRankingRequestType.getOrderType()))),
                // date 同环比
                DSLProcessCompareSet.getInstance(topRankingRequestType.getCompareConfig()),
                // limit
                DSLProcessLimitSet.getInstance(topRankingRequestType.getPage(), topRankingRequestType.getSize()),
                // 目的地，跟团+私家团
                DSLProcessProvNamesPares.getInstance(topRankingRequestType.getProvNames()),
                // 产线，跟团+私家团
                DSLProcessProductPatternPares.getInstance(topRankingRequestType.getProductPattern()),
                // 日期类型，定制游
                DSLProcessDateTypePares.getInstance(topRankingRequestType.getDateType()),
                // 供应商、销售的指标注入
                DSLProcessDIYIndicatorInject.getInstance(),
                // 评级区域
                DSLProcessAreaPares.getInstance(topRankingRequestType.getArea(), custEmpOrgInfoService),
                // filter
                DSLProcessWhereConditionSet.getInstance(topRankingRequestType.getGroupBy().get(0), topRankingRequestType.getDimFilterValues())
        );
        // 数据后置处理
        List<AbstractAfterDataProcess> dataProcesses = Arrays.asList(
                AfterDataProcessEmpsInject.getInstance(depTreeCache),
                AfterDataProcessMetaSort.getInstance()
        );
        // 查询
        QueryByDSLResponseType queryByDSLResponseType = queryDataByDSLWithBusinessLine(dsl, null, dslProcesses, dataProcesses);
        TopRankingResponseType topRankingResponseType = new TopRankingResponseType();
        topRankingResponseType.setData(queryByDSLResponseType.getData());
        return topRankingResponseType;
    }


    public GetGrpMetricCardResponseType getGrpMetricCardV3(GetGrpMetricDataRequestType requestType, List<String> categoryNames) throws Exception {
        PerformanceCardRequestType performanceCardRequestType = new PerformanceCardRequestType();
        // emp
        performanceCardRequestType.setEmpCodes(Collections.singletonList(requestType.getEmpCode()));
        // 时间
        performanceCardRequestType.setStartDate(requestType.getStartDate());
        performanceCardRequestType.setEndDate(requestType.getEndDate());
        // 省份
        performanceCardRequestType.setProvNames(requestType.getAreas());
        // 产品形态
        performanceCardRequestType.setProductPattern(requestType.getProvNameList());
        // 同环比
        performanceCardRequestType.setCompareConfig(Arrays.asList("hb", "year_tb"));
        // 评级区域
        performanceCardRequestType.setArea(requestType.getAreas());
        performanceCardRequestType.setDateType(requestType.getDateType());
        // 获取业务线
        Integer businessLine = getBusinessLine(requestType.businessLine);
        performanceCardRequestType.setBusinessType(businessLine);
        DomainConfig domainConfig = DomainConfig.getAndUpdateDomainConfigByBusinessLine(businessLine, grpConfig);
        // 指标配置
        List<GrpMetricCategorie> metric;
        if (businessLine == 230) {
            performanceCardRequestType.setIndicators(getDiyIndicators(domainConfig, performanceCardRequestType.getDateType(), categoryNames));
            metric = getDiyMetric(performanceCardRequestType.getDateType(), categoryNames);
        } else {
            performanceCardRequestType.setIndicators(getIndicators(domainConfig, categoryNames));
            metric = getMetric(categoryNames);
        }
        // 查询
        PerformanceCardResponseType performanceCardResponseType = queryPerformanceCard(performanceCardRequestType);
        // response
        return processCardData(metric, performanceCardResponseType.getData());
    }

    public GetGrpTrendLineResponseType getGrpTrendLineV3(GetGrpMetricDataRequestType requestType) throws Exception {
        ChangeTrendRequestType changeTrendRequestType = new ChangeTrendRequestType();
        // emp
        changeTrendRequestType.setEmpCodes(Collections.singletonList(requestType.getEmpCode()));
        // 时间
        changeTrendRequestType.setStartDate(requestType.getStartDate());
        changeTrendRequestType.setEndDate(requestType.getEndDate());
        // 省份
        changeTrendRequestType.setProvNames(requestType.getAreas());
        // 产品形态
        changeTrendRequestType.setProductPattern(requestType.getProvNameList());
        // 评级区域
        changeTrendRequestType.setArea(requestType.getAreas());
        changeTrendRequestType.setDateType(requestType.getDateType());
        // 同环比
        changeTrendRequestType.setCompareConfig(Collections.singletonList("year_tb"));
        // gourpby
        switch (requestType.getAggregationGranularity()) {
            case "day":
                changeTrendRequestType.setGroupBy(Collections.singletonList("date"));
                break;
            case "week":
                changeTrendRequestType.setGroupBy(Collections.singletonList("date_week"));
                break;
            case "month":
                changeTrendRequestType.setGroupBy(Collections.singletonList("date_month"));
                break;
        }
        // 领域模型配置文件
        Integer businessLine = getBusinessLine(requestType.businessLine);
        changeTrendRequestType.setBusinessType(businessLine);
        DomainConfig domainConfig = DomainConfig.getAndUpdateDomainConfigByBusinessLine(businessLine, grpConfig);
        // 指标配置
        List<GrpMetricCategorie> metric;
        if (businessLine == 230) {
            changeTrendRequestType.setIndicators(getDiyIndicators(domainConfig, requestType.getDateType(), Collections.singletonList(requestType.getMetricCategorieName())));
            metric = getDiyMetric(requestType.getDateType(), Collections.singletonList(requestType.getMetricCategorieName()));
        } else {
            changeTrendRequestType.setIndicators(getIndicators(domainConfig, Collections.singletonList(requestType.getMetricCategorieName())));
            metric = getMetric(Collections.singletonList(requestType.getMetricCategorieName()));
        }
        // 查询
        ChangeTrendResponseType changeTrendResponseType = queryChangeTrend(changeTrendRequestType);
        // process
        return afterProcessSortTrendData(requestType, metric, changeTrendResponseType);
    }
}
