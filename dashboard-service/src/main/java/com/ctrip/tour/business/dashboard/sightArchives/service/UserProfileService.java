package com.ctrip.tour.business.dashboard.sightArchives.service;

import com.ctrip.soa._24922.*;

//用户分析
public interface UserProfileService {

    //用户画像 - 提前预订天数&用户年龄分布（柱状图）
    GetUserProfileHistogramResponseType getUserProfileHistogram(GetUserProfileHistogramRequestType requestType);

    //用户画像 - 客户等级&用户类型&本异地%境内外用户分布（饼图）
    GetUserProfilePieChartResponseType getUserProfilePieChart(GetUserProfilePieChartRequestType requestType);

    //用户画像 - 客源城市分布（地图&表格）
    GetUserResidenceDistributionResponseType getUserResidenceDistribution(GetUserResidenceDistributionRequestType requestType);

    //用户搜索偏好
    GetUserSearchPreferenceResponseType getUserSearchPreference(GetUserSearchPreferenceRequestType requestType);

}
