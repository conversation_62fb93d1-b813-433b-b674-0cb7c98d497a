package com.ctrip.tour.business.dashboard.tktBusiness.dao;

import com.ctrip.platform.dal.dao.*;
import com.ctrip.platform.dal.dao.sqlbuilder.*;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.sql.Types;
import java.util.List;


import com.ctrip.platform.dal.dao.helper.DalDefaultJpaParser;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardOverseaExamineeConfig;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2023-03-30
 */
@Repository
public class BusinessDashboardOverseaExamineeConfigDao {
	private static final boolean ASC = true;
	private DalTableDao<BusinessDashboardOverseaExamineeConfig> client;
	
	public BusinessDashboardOverseaExamineeConfigDao() throws SQLException {
		this.client = new DalTableDao<>(new DalDefaultJpaParser<>(BusinessDashboardOverseaExamineeConfig.class));
	}

	/**
	 * Query BusinessDashboardOverseaExamineeConfig by complex primary key
	 */
	public BusinessDashboardOverseaExamineeConfig queryByPk(Long id, Timestamp datachangeLasttime)
			throws SQLException {
		return queryByPk(id, datachangeLasttime, null);
	}

	/**
	 * Query BusinessDashboardOverseaExamineeConfig by complex primary key
	 */
	public BusinessDashboardOverseaExamineeConfig queryByPk(Long id, Timestamp datachangeLasttime, DalHints hints)
			throws SQLException {
		hints = DalHints.createIfAbsent(hints);
		BusinessDashboardOverseaExamineeConfig pk = new BusinessDashboardOverseaExamineeConfig();		
		pk.setId(id);
		pk.setDatachangeLasttime(datachangeLasttime);
		return client.queryByPk(pk, hints);
	}

	/**
	 * Query BusinessDashboardOverseaExamineeConfig by BusinessDashboardOverseaExamineeConfig instance which the primary key is set
	 */
	public BusinessDashboardOverseaExamineeConfig queryByPk(BusinessDashboardOverseaExamineeConfig pk)
			throws SQLException {
		return queryByPk(pk, null);
	}

	/**
	 * Query BusinessDashboardOverseaExamineeConfig by BusinessDashboardOverseaExamineeConfig instance which the primary key is set
	 */
	public BusinessDashboardOverseaExamineeConfig queryByPk(BusinessDashboardOverseaExamineeConfig pk, DalHints hints)
			throws SQLException {
		hints = DalHints.createIfAbsent(hints);
		return client.queryByPk(pk, hints);
	}

	/**
	 * Query against sample pojo. All not null attributes of the passed in pojo
	 * will be used as search criteria.
	 */
	public List<BusinessDashboardOverseaExamineeConfig> queryBy(BusinessDashboardOverseaExamineeConfig sample)
			throws SQLException {
		return queryBy(sample, null);
	}

	/**
	 * Query against sample pojo. All not null attributes of the passed in pojo
	 * will be used as search criteria.
	 */
	public List<BusinessDashboardOverseaExamineeConfig> queryBy(BusinessDashboardOverseaExamineeConfig sample, DalHints hints)
			throws SQLException {
		hints = DalHints.createIfAbsent(hints);
		return client.queryBy(sample, hints);
	}

	/**
	 * Get the all records count
	 */
	public int count() throws SQLException {
		return count(null);
	}

	/**
	 * Get the all records count
	 */
	public int count(DalHints hints) throws SQLException {
		hints = DalHints.createIfAbsent(hints);
		SelectSqlBuilder builder = new SelectSqlBuilder().selectCount();
		return client.count(builder, hints).intValue();
	}

	/**
	 * Query BusinessDashboardOverseaExamineeConfig with paging function
	 * The pageSize and pageNo must be greater than zero.
	 */
	public List<BusinessDashboardOverseaExamineeConfig> queryAllByPage(int pageNo, int pageSize)  throws SQLException {
		return queryAllByPage(pageNo, pageSize, null);
	}

	/**
	 * Query BusinessDashboardOverseaExamineeConfig with paging function
	 * The pageSize and pageNo must be greater than zero.
	 */
	public List<BusinessDashboardOverseaExamineeConfig> queryAllByPage(int pageNo, int pageSize, DalHints hints)  throws SQLException {
		hints = DalHints.createIfAbsent(hints);

		SelectSqlBuilder builder = new SelectSqlBuilder();
		builder.selectAll().atPage(pageNo, pageSize).orderBy("id,DataChange_LastTime", ASC);

		return client.query(builder, hints);
	}

	/**
	 * Get all records from table
	 */
	public List<BusinessDashboardOverseaExamineeConfig> queryAll() throws SQLException {
		return queryAll(null);
	}

	/**
	 * Get all records from table
	 */
	public List<BusinessDashboardOverseaExamineeConfig> queryAll(DalHints hints) throws SQLException {
		hints = DalHints.createIfAbsent(hints);
		
		SelectSqlBuilder builder = new SelectSqlBuilder().selectAll().orderBy("id,DataChange_LastTime", ASC);
		
		return client.query(builder, hints);
	}

	/**
	 * Insert single pojo
	 *
	 * @param daoPojo
	 *            pojo to be inserted
	 * @return how many rows been affected
	 * @throws SQLException
	 */
	public int insert(BusinessDashboardOverseaExamineeConfig daoPojo) throws SQLException {
		return insert(null, daoPojo);
	}

	/**
	 * Insert single pojo
	 * 
	 * @param hints
	 *            Additional parameters that instruct how DAL Client perform database operation.
	 * @param daoPojo
	 *            pojo to be inserted
	 * @return how many rows been affected
	 * @throws SQLException
	 */
	public int insert(DalHints hints, BusinessDashboardOverseaExamineeConfig daoPojo) throws SQLException {
		if (null == daoPojo) {
			return 0;
		}
		hints = DalHints.createIfAbsent(hints);
		return client.insert(hints, daoPojo);
	}

	/**
	 * Insert pojos one by one. If you want to inert them in the batch mode,
	 * user batchInsert instead. You can also use the combinedInsert.
	 *
	 * @param daoPojos
	 *            list of pojos to be inserted
	 * @return how many rows been affected
	 */
	public int[] insert(List<BusinessDashboardOverseaExamineeConfig> daoPojos) throws SQLException {
		return insert(null, daoPojos);
	}

	/**
	 * Insert pojos one by one. If you want to inert them in the batch mode,
	 * user batchInsert instead. You can also use the combinedInsert.
	 * 
	 * @param hints 
	 *            Additional parameters that instruct how DAL Client perform database operation.
	 *            DalHintEnum.continueOnError can be used
	 *            to indicate that the inserting can be go on if there is any
	 *            failure.
	 * @param daoPojos
	 *            list of pojos to be inserted
	 * @return how many rows been affected
	 */
	public int[] insert(DalHints hints, List<BusinessDashboardOverseaExamineeConfig> daoPojos) throws SQLException {
		if (null == daoPojos || daoPojos.size() <= 0) {
			return new int[0];
		}
		hints = DalHints.createIfAbsent(hints);
		return client.insert(hints, daoPojos);
	}

	/**
	 * Insert pojo and get the generated PK back in keyHolder.
	 * If the "set no count on" for MS SqlServer is set, the operation may fail.
	 * Please don't pass keyholder for MS SqlServer to avoid the failure in such case.
	 *
	 * @param keyHolder
	 *            holder for generated primary keys
	 * @param daoPojo
	 *            pojo to be inserted
	 * @return how many rows been affected
	 * @throws SQLException
	 */
	public int insertWithKeyHolder(KeyHolder keyHolder, BusinessDashboardOverseaExamineeConfig daoPojo) throws SQLException {
		return insert(null, keyHolder, daoPojo);
	}

	/**
	 * Insert pojo and get the generated PK back in keyHolder. 
	 * If the "set no count on" for MS SqlServer is set, the operation may fail.
	 * Please don't pass keyholder for MS SqlServer to avoid the failure in such case.
	 * 
	 * @param hints
	 *            Additional parameters that instruct how DAL Client perform database operation.
	 * @param keyHolder
	 *            holder for generated primary keys
	 * @param daoPojo
	 *            pojo to be inserted
	 * @return how many rows been affected
	 * @throws SQLException
	 */
	public int insert(DalHints hints, KeyHolder keyHolder, BusinessDashboardOverseaExamineeConfig daoPojo) throws SQLException {
		if (null == daoPojo) {
			return 0;
		}
		hints = DalHints.createIfAbsent(hints);
		return client.insert(hints, keyHolder, daoPojo);
	}

	/**
	 * Insert pojos and get the generated PK back in keyHolder.
	 * If the "set no count on" for MS SqlServer is set, the operation may fail.
	 * Please don't pass keyholder for MS SqlServer to avoid the failure in such case.
	 *
	 * @param keyHolder
	 *            holder for generated primary keys
	 * @param daoPojos
	 *            list of pojos to be inserted
	 * @return how many rows been affected
	 * @throws SQLException
	 */
	public int[] insertWithKeyHolder(KeyHolder keyHolder, List<BusinessDashboardOverseaExamineeConfig> daoPojos) throws SQLException {
		return insert(null, keyHolder, daoPojos);
	}

	/**
	 * Insert pojos and get the generated PK back in keyHolder. 
	 * If the "set no count on" for MS SqlServer is set, the operation may fail.
	 * Please don't pass keyholder for MS SqlServer to avoid the failure in such case.
	 * 
	 * @param hints
	 *            Additional parameters that instruct how DAL Client perform database operation.
	 *            DalHintEnum.continueOnError can be used
	 *            to indicate that the inserting can be go on if there is any
	 *            failure.
	 * @param keyHolder
	 *            holder for generated primary keys
	 * @param daoPojos
	 *            list of pojos to be inserted
	 * @return how many rows been affected
	 * @throws SQLException
	 */
	public int[] insert(DalHints hints, KeyHolder keyHolder, List<BusinessDashboardOverseaExamineeConfig> daoPojos) throws SQLException {
		if (null == daoPojos || daoPojos.size() <= 0) {
			return new int[0];
		}
		hints = DalHints.createIfAbsent(hints);
		return client.insert(hints, keyHolder, daoPojos);
	}

	/**
	 * Insert pojos in batch mode.
	 * The DalDetailResults will be set in hints to allow client know how the operation performed in each of the shard.
	 *
	 * @param daoPojos list of pojos to be inserted
	 * @return how many rows been affected for inserting each of the pojo
	 * @throws SQLException
	 */
	public int[] batchInsert(List<BusinessDashboardOverseaExamineeConfig> daoPojos) throws SQLException {
		return batchInsert(null, daoPojos);
	}

	/**
	 * Insert pojos in batch mode. 
	 * The DalDetailResults will be set in hints to allow client know how the operation performed in each of the shard.
	 * 
	 * @param hints Additional parameters that instruct how DAL Client perform database operation.
	 * @param daoPojos list of pojos to be inserted
	 * @return how many rows been affected for inserting each of the pojo
	 * @throws SQLException
	 */
	public int[] batchInsert(DalHints hints, List<BusinessDashboardOverseaExamineeConfig> daoPojos) throws SQLException {
		if (null == daoPojos || daoPojos.size() <= 0) {
			return new int[0];
		}
		hints = DalHints.createIfAbsent(hints);
		return client.batchInsert(hints, daoPojos);
	}

	/**
	 * Insert multiple pojos in one INSERT SQL
	 * The DalDetailResults will be set in hints to allow client know how the operation performed in each of the shard.
	 *
	 * @param daoPojos list of pojos to be inserted
	 * @return how many rows been affected
	 * @throws SQLException
	 */
	public int combinedInsert(List<BusinessDashboardOverseaExamineeConfig> daoPojos) throws SQLException {
		return combinedInsert(null, daoPojos);
	}

	/**
	 * Insert multiple pojos in one INSERT SQL
	 * The DalDetailResults will be set in hints to allow client know how the operation performed in each of the shard.
	 * 
	 * @param hints Additional parameters that instruct how DAL Client perform database operation.
	 * @param daoPojos list of pojos to be inserted
	 * @return how many rows been affected
	 * @throws SQLException
	 */
	public int combinedInsert(DalHints hints, List<BusinessDashboardOverseaExamineeConfig> daoPojos) throws SQLException {
		if (null == daoPojos || daoPojos.size() <= 0) {
			return 0;
		}
		hints = DalHints.createIfAbsent(hints);
		return client.combinedInsert(hints, daoPojos);
	}

	/**
	 * Insert multiple pojos in one INSERT SQL and get the generated PK back in keyHolder.
	 * If the "set no count on" for MS SqlServer is set, the operation may fail.
	 * Please don't pass keyholder for MS SqlServer to avoid the failure in such case.
	 * The DalDetailResults will be set in hints to allow client know how the operation performed in each of the shard.
	 *
	 * @param keyHolder holder for generated primary keys
	 * @param daoPojos list of pojos to be inserted
	 * @return how many rows been affected
	 * @throws SQLException
	 */
	public int combinedInsertWithKeyHolder(KeyHolder keyHolder, List<BusinessDashboardOverseaExamineeConfig> daoPojos) throws SQLException {
		return combinedInsert(null, keyHolder, daoPojos);
	}

	/**
	 * Insert multiple pojos in one INSERT SQL and get the generated PK back in keyHolder.
	 * If the "set no count on" for MS SqlServer is set, the operation may fail.
	 * Please don't pass keyholder for MS SqlServer to avoid the failure in such case.
	 * The DalDetailResults will be set in hints to allow client know how the operation performed in each of the shard.
	 * 
	 * @param hints Additional parameters that instruct how DAL Client perform database operation.
	 * @param keyHolder holder for generated primary keys
	 * @param daoPojos list of pojos to be inserted
	 * @return how many rows been affected
	 * @throws SQLException
	 */
	public int combinedInsert(DalHints hints, KeyHolder keyHolder, List<BusinessDashboardOverseaExamineeConfig> daoPojos) throws SQLException {
		if (null == daoPojos || daoPojos.size() <= 0) {
			return 0;
		}
		hints = DalHints.createIfAbsent(hints);
		return client.combinedInsert(hints, keyHolder, daoPojos);
	}

	/**
	 * Delete the given pojo.
	 *
	 * @param daoPojo pojo to be deleted
	 * @return how many rows been affected
	 * @throws SQLException
	 */
	public int delete(BusinessDashboardOverseaExamineeConfig daoPojo) throws SQLException {
		return delete(null, daoPojo);
	}

	/**
	 * Delete the given pojo.
	 * 
	 * @param hints Additional parameters that instruct how DAL Client perform database operation.
	 * @param daoPojo pojo to be deleted
	 * @return how many rows been affected
	 * @throws SQLException
	 */
	public int delete(DalHints hints, BusinessDashboardOverseaExamineeConfig daoPojo) throws SQLException {
		if (null == daoPojo) {
			return 0;
		}
		hints = DalHints.createIfAbsent(hints);
		return client.delete(hints, daoPojo);
	}

	/**
	 * Delete the given pojos list one by one.
	 *
	 * @param daoPojos list of pojos to be deleted
	 * @return how many rows been affected
	 * @throws SQLException
	 */
	public int[] delete(List<BusinessDashboardOverseaExamineeConfig> daoPojos) throws SQLException {
		return delete(null, daoPojos);
	}

	/**
	 * Delete the given pojos list one by one.
	 * 
	 * @param hints Additional parameters that instruct how DAL Client perform database operation.
	 * @param daoPojos list of pojos to be deleted
	 * @return how many rows been affected
	 * @throws SQLException
	 */
	public int[] delete(DalHints hints, List<BusinessDashboardOverseaExamineeConfig> daoPojos) throws SQLException {
		if (null == daoPojos || daoPojos.size() <= 0) {
			return new int[0];
		}
		hints = DalHints.createIfAbsent(hints);
		return client.delete(hints, daoPojos);
	}

	/**
	 * Delete the given pojo list in batch.
	 * The DalDetailResults will be set in hints to allow client know how the operation performed in each of the shard.
	 *
	 * @param daoPojos list of pojos to be deleted
	 * @return how many rows been affected for deleting each of the pojo
	 * @throws SQLException
	 */
	public int[] batchDelete(List<BusinessDashboardOverseaExamineeConfig> daoPojos) throws SQLException {
		return batchDelete(null, daoPojos);
	}

	/**
	 * Delete the given pojo list in batch. 
	 * The DalDetailResults will be set in hints to allow client know how the operation performed in each of the shard.
	 * 
	 * @param hints Additional parameters that instruct how DAL Client perform database operation.
	 * @param daoPojos list of pojos to be deleted
	 * @return how many rows been affected for deleting each of the pojo
	 * @throws SQLException
	 */
	public int[] batchDelete(DalHints hints, List<BusinessDashboardOverseaExamineeConfig> daoPojos) throws SQLException {
		if (null == daoPojos || daoPojos.size() <= 0) {
			return new int[0];
		}
		hints = DalHints.createIfAbsent(hints);
		return client.batchDelete(hints, daoPojos);
	}
    /**
         * Delete multiple pojos in one DELETE SQL.
         *
         * @param daoPojos list of pojos to be deleted
         * @return how many rows been affected
         * @throws SQLException
         */
    public int combinedDelete(List<BusinessDashboardOverseaExamineeConfig> daoPojos) throws SQLException {
        return combinedDelete(null, daoPojos);
    }

    /**
     * Delete multiple pojos in one DELETE SQL.
     *
     * @param hints Additional parameters that instruct how DAL Client perform database operation.
     * @param daoPojos list of pojos to be deleted
     * @return how many rows been affected
     * @throws SQLException
     */
    public int combinedDelete(DalHints hints, List<BusinessDashboardOverseaExamineeConfig> daoPojos) throws SQLException {
        if (null == daoPojos || daoPojos.size() <= 0) {
            return 0;
        }
        hints = DalHints.createIfAbsent(hints);
        return client.combinedDelete(hints, daoPojos);
    }

	/**
	 * Update the given pojo . By default, if a field of pojo is null value,
	 * that field will be ignored, so that it will not be updated. You can
	 * overwrite this by set updateNullField in hints.
	 *
	 * @param daoPojo pojo to be updated
	 * @return how many rows been affected
	 * @throws SQLException
	 */
	public int update(BusinessDashboardOverseaExamineeConfig daoPojo) throws SQLException {
		return update(null, daoPojo);
	}

	/**
	 * Update the given pojo . By default, if a field of pojo is null value,
	 * that field will be ignored, so that it will not be updated. You can
	 * overwrite this by set updateNullField in hints.
	 * 
	 * @param hints
	 * 			Additional parameters that instruct how DAL Client perform database operation.
	 *          DalHintEnum.updateNullField can be used
	 *          to indicate that the field of pojo is null value will be update.
	 * @param daoPojo pojo to be updated
	 * @return how many rows been affected
	 * @throws SQLException
	 */
	public int update(DalHints hints, BusinessDashboardOverseaExamineeConfig daoPojo) throws SQLException {
		if (null == daoPojo) {
			return 0;
		}
		hints = DalHints.createIfAbsent(hints);
		return client.update(hints, daoPojo);
	}

	/**
	 * Update the given pojo list one by one. By default, if a field of pojo is null value,
	 * that field will be ignored, so that it will not be updated. You can
	 * overwrite this by set updateNullField in hints.
	 *
	 * @param daoPojos list of pojos to be updated
	 * @return how many rows been affected
	 * @throws SQLException
	 */
	public int[] update(List<BusinessDashboardOverseaExamineeConfig> daoPojos) throws SQLException {
		return update(null, daoPojos);
	}

	/**
	 * Update the given pojo list one by one. By default, if a field of pojo is null value,
	 * that field will be ignored, so that it will not be updated. You can
	 * overwrite this by set updateNullField in hints.
	 * 
	 * @param hints
	 * 			Additional parameters that instruct how DAL Client perform database operation.
	 *          DalHintEnum.updateNullField can be used
	 *          to indicate that the field of pojo is null value will be update.
	 * @param daoPojos list of pojos to be updated
	 * @return how many rows been affected
	 * @throws SQLException
	 */
	public int[] update(DalHints hints, List<BusinessDashboardOverseaExamineeConfig> daoPojos) throws SQLException {
		if (null == daoPojos || daoPojos.size() <= 0) {
			return new int[0];
		}
		hints = DalHints.createIfAbsent(hints);
		return client.update(hints, daoPojos);
	}

	/**
	 * Update the given pojo list in batch.
	 *
	 * @return how many rows been affected
	 * @throws SQLException
	 */
	public int[] batchUpdate(List<BusinessDashboardOverseaExamineeConfig> daoPojos) throws SQLException {
		return batchUpdate(null, daoPojos);
	}

	/**
	 * Update the given pojo list in batch. 
	 * 
	 * @return how many rows been affected
	 * @throws SQLException
	 */
	public int[] batchUpdate(DalHints hints, List<BusinessDashboardOverseaExamineeConfig> daoPojos) throws SQLException {
		if (null == daoPojos || daoPojos.size() <= 0) {
			return new int[0];
		}
		hints = DalHints.createIfAbsent(hints);
		return client.batchUpdate(hints, daoPojos);
	}


	/**
	 * 获取某个考核周期下用户配置的所有指标
	 * @param domainName
	 * @param queryD
	 * @param year
	 * @param quarter
	 * @return
	 * @throws SQLException
	 */
	public List<BusinessDashboardOverseaExamineeConfig> querySpecificPeriodAllMetricConfig(String domainName,
																						   String queryD,
																						   String year,
																						   String quarter) throws SQLException {
		SelectSqlBuilder builder = new SelectSqlBuilder();
		builder.equal("domain_name", domainName, Types.VARCHAR);
		builder.and().equal("query_d", queryD, Types.VARCHAR);
		builder.and().equal("year", year, Types.VARCHAR);
		builder.and().equal("quarter", quarter, Types.VARCHAR);
		return client.query(builder, new DalHints());
	}

	/**
	 * 获取某个考核周期下用户配置的所有指标
	 * @param domainName
	 * @param queryD
	 * @param year
	 * @param quarter
	 * @return
	 * @throws SQLException
	 */
	public List<BusinessDashboardOverseaExamineeConfig> querySpecificPeriodAllMetricConfigV2(String domainName,
																						   String queryD,
																						   String year,
																						   String quarter) throws SQLException {
		SelectSqlBuilder builder = new SelectSqlBuilder();
		builder.equal("domain_name", domainName, Types.VARCHAR);
		builder.and().equal("query_d", queryD, Types.VARCHAR);
		builder.and().equal("year", year, Types.VARCHAR);
		builder.and().equal("quarter", quarter, Types.VARCHAR);
		return client.query(builder, new DalHints());
	}
	/**
	 * 获取所有考核周期下用户某个指标的所有配置
	 *
	 * @param domainName
	 * @param queryD
	 * @param metric
	 * @return
	 * @throws SQLException
	 */
	public List<BusinessDashboardOverseaExamineeConfig> queryMetricAllConfig(String domainName,
																			 String queryD,
																			 String metric) throws SQLException {
		SelectSqlBuilder builder = new SelectSqlBuilder();
		builder.equal("domain_name", domainName, Types.VARCHAR);
		builder.and().equal("query_d", queryD, Types.VARCHAR);
		builder.and().like("examine_metric", "%" + metric + "%", Types.VARCHAR);
		return client.query(builder, new DalHints());
	}


	/**
	 * 获取某个考核周期下用户某个指标的所有配置
	 *
	 * @param domainName
	 * @param queryD
	 * @param metric
	 * @return
	 * @throws SQLException
	 */
	public List<BusinessDashboardOverseaExamineeConfig> queryMetricConfig(String domainName,
																		  String queryD,
																		  String year,
																		  String quarter,
																		  String metric) throws SQLException {
		SelectSqlBuilder builder = new SelectSqlBuilder();
		builder.equal("domain_name", domainName, Types.VARCHAR);
		builder.and().equal("query_d", queryD, Types.VARCHAR);
		builder.and().equal("year", year, Types.VARCHAR);
		builder.and().equal("quarter", quarter, Types.VARCHAR);
		builder.and().like("examine_metric", "%" + metric + "%", Types.VARCHAR);
		return client.query(builder, new DalHints());
	}



}
