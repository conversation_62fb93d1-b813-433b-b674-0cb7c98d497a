package com.ctrip.tour.business.dashboard.grpBusiness.domain.process.data;

import com.ctrip.soa._24922.DSLRequestType;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.AbstractAfterDataProcess;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.model.ResultData;
import com.ctrip.tour.business.dashboard.grpBusiness.service.DepTreeCache;

import java.util.Comparator;

public class AfterDataProcessSortTrendData extends AbstractAfterDataProcess {
    private DepTreeCache depTreeCache;
    private static volatile AfterDataProcessSortTrendData instance;

    public static AbstractAfterDataProcess getInstance() {
        if (instance == null) { // 第一次检查
            synchronized (AfterDataProcessSortTrendData.class) { // 加锁
                if (instance == null) { // 第二次检查
                    instance = new AfterDataProcessSortTrendData();
                }
            }
        }
        return instance;
    }

    @Override
    public ResultData process(DSLRequestType dslRequestType, ResultData data) {
        data.getData().sort(Comparator.comparing(o -> (o.getOrDefault("date", "").toString() + o.getOrDefault("date_week", "").toString() + o.getOrDefault("date_month", "").toString())));
        return data;
    }
}
