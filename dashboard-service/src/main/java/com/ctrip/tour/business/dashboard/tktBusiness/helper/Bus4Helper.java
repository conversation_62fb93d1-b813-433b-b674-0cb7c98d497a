package com.ctrip.tour.business.dashboard.tktBusiness.helper;


import com.ctrip.tour.business.dashboard.tktBusiness.exception.ConfigImportException;
import com.google.common.collect.Lists;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/8/1
 */
public class Bus4Helper {







    public static List<String> getDimList() {
        return Lists.newArrayList("complete_score", "target_score","delta_complete_rate");
    }

    public static List<String> getLevelList() {
        return Lists.newArrayList("actual_sign_scenic_cnt","target_sign_scenic_cnt", "gap_sign_scenic_cnt","treasuremap_scenic_cnt");
    }

    public static List<String> getTableDimList(){
        return Lists.newArrayList("actual_sign_scenic_cnt","target_sign_scenic_cnt",
                "gap_sign_scenic_cnt","treasuremap_scenic_cnt","ttd_cps_sign_rate");
    }

    public static List<String> getStackDimList(){
        return Lists.newArrayList("actual_sign_scenic_cnt");
    }


//    public static void makeUpMetricCardData(Map<String, Double> dimMap) {
//        String extraDim = "ttd_weight1_sign_score|ttd_trgt_weight_sign|/";
//        Double fenzi = dimMap.get("ttd_weight1_sign_score");
//        Double fenmu = dimMap.get("ttd_trgt_weight_sign");
//        if (GeneralUtil.isValidDivide(fenzi, fenmu)) {
//            dimMap.put(extraDim, fenzi / fenmu);
//        }
//    }

    public static Map<String,String> getLineChartTrendlineType(){
        Map<String,String> typeMap = new HashMap<>();
        typeMap.put("delta_complete_rate","lineChart");
        return typeMap;
    }

    public static Map<String,String> getStackLineChartTrendLineType(){
        Map<String,String> typeMap = new HashMap<>();
        typeMap.put("actual_sign_scenic_cnt","stackLineChart");
        return typeMap;
    }

    public static Map<String,String> getLineChartTrendlineTypeWithDrillDown(){
        Map<String,String> typeMap = new HashMap<>();
        typeMap.put("delta_complete_rate","lineChart");
        return typeMap;
    }






    //将分层进展和目标转化为加权进展和目标
//    public static List<List<Object>> getMetricCardWeightList(List<List<Object>> scenicList) {
//        List<List<Object>> resultList = new ArrayList<>();
//        Double weightProgress = 0d;
//        Double weightTarget = 0d;
//        for (List<Object> rowResult : scenicList) {
//            String scenic = String.valueOf(rowResult.get(0));
//            Double progress = Double.valueOf(String.valueOf(rowResult.get(1)));
//            Double target = Double.valueOf(String.valueOf(rowResult.get(2)));
//            weightProgress += getScenicSumValue(scenic, progress);
//            weightTarget += getScenicSumValue(scenic, target);
//        }
//
//        List<Object> weightList = new ArrayList<>();
//        resultList.add(weightList);
//        weightList.add(weightProgress);
//        weightList.add(weightTarget);
//
//        return resultList;
//    }




//    private static Double getScenicSumValue(String scenic,
//                                            Double target){
//        switch (scenic) {
//            case "1":
//                return target * 5;
//            case "2":
//                return target * 3;
//            case "3":
//                return target * 1;
//            default:
//                throw new ConfigImportException("直签配置中导入了非法的分层信息:"+scenic);
//        }
//    }






//    public static List<List<Object>> getTrendlineWeightList(List<List<Object>> scenicTargetList,
//                                                            List<String> groupTagList,
//                                                            List<String> dimList) {
//        Map<String, Double> tempMap = new HashMap<>();
//        Set<String> drillDownSet = new HashSet<>();
//        List<List<Object>> resultList = new ArrayList<>();
//        int size = groupTagList.size();
//        for (List<Object> rowResult : scenicTargetList) {
//            String scenic = String.valueOf(rowResult.get(0));
//            StringBuilder sb = new StringBuilder();
//            for (int i = 2; i < size; i++) {
//                sb.append(rowResult.get(i)).append(":");
//            }
//            sb.deleteCharAt(sb.length() - 1);
//            String preffix = sb.toString();
//            drillDownSet.add(preffix);
//            for (int i = size; i < rowResult.size(); i++) {
//                String dim = dimList.get(i - size);
//                Double value = Double.valueOf(String.valueOf(rowResult.get(i)));
//                Double sumValue = tempMap.getOrDefault(preffix + dim, 0d);
//                sumValue += getScenicSumValue(scenic, value);
//                tempMap.put(preffix + dim, sumValue);
//            }
//        }
//        for (String preffix : drillDownSet) {
//            List<Object> rowList = new ArrayList<>();
//            String[] array = preffix.split(":");
//            for (String arrayItem : array) {
//                rowList.add(arrayItem);
//            }
//            for (String dim : dimList) {
//                String key = preffix + dim;
//                Double weightValue = tempMap.getOrDefault(key, 0d);
//                rowList.add(weightValue);
//            }
//            resultList.add(rowList);
//        }
//        return resultList;
//    }

//    public static void calWeightTableData(List<TableDataItem> tableDataItemList,
//                                          List<String> groupTagList) {
//        String progressMetric = "ttd_online_sign_scenic_cnt";
//        String targetMetric = "ttd_trgt_sign";
//        Map<String, Double> tempMap = new HashMap<>();
//        for (TableDataItem item : tableDataItemList) {
//            StringBuilder sb = new StringBuilder();
//            Map<String, String> fieldMap = item.getFieldMap();
//            Map<String, Double> dimMap = item.getDimMap();
//            for (int i = 0; i < groupTagList.size() - 2; i++) {
//                sb.append(fieldMap.get(groupTagList.get(i))).append(":");
//            }
//            String scenic = fieldMap.get("scenic_class_id");
//            String preffix = sb.toString();
//
//            Double progress = dimMap.get(progressMetric);
//            Double target = dimMap.get(targetMetric);
//            Double weightProgress = tempMap.getOrDefault(preffix + progressMetric, 0d);
//            Double weightTarget = tempMap.getOrDefault(preffix + targetMetric, 0d);
//            weightProgress += getScenicSumValue(scenic, progress);
//            weightTarget += getScenicSumValue(scenic, target);
//            tempMap.put(preffix + progressMetric, weightProgress);
//            tempMap.put(preffix + targetMetric, weightTarget);
//        }
//
//        for (TableDataItem item : tableDataItemList) {
//            StringBuilder sb = new StringBuilder();
//            Map<String, String> fieldMap = item.getFieldMap();
//            Map<String, Double> dimMap = item.getDimMap();
//            for (int i = 0; i < groupTagList.size() - 2; i++) {
//                sb.append(fieldMap.get(groupTagList.get(i))).append(":");
//            }
//            String preffix = sb.toString();
//            dimMap.put("ttd_weight1_sign_score", tempMap.get(preffix + progressMetric));
//            dimMap.put("ttd_trgt_weight_sign", tempMap.get(preffix + targetMetric));
//        }
//    }

//    public static void makeUpTableData(List<TableDataItem> tableDataItemList) {
//        for (TableDataItem item : tableDataItemList) {
//            makeUpMetricCardData(item.getDimMap());
//        }
//    }


    //根据考核层级确定可下钻维度
    public static List<String> getFieldList(String level){
        switch (level){
            case "国内":
            case "三方":
                return Lists.newArrayList("region_name","province_name");
            case "大区":
            case "省份":
                return Lists.newArrayList("province_name");
            default:
                throw new ConfigImportException("直签配置中导入了错误的层级:"+level);
        }
    }


    //获取首页表头信息
    public static List<String> getFirstTableList(){
        return Arrays.asList("directSightRange","directCrashSightNum","directRealSightNum","directSightGap","directRate","directWeight","directCompleteScore","directTargetScore","directCompleteRate");
    }

    //获取详情页表头信息
    public static List<String> getDetailTableList(){
        return Arrays.asList("directSightRange","directCrashSightNum","directRealSightNum","directRate","directWeight","directCompleteScore","directTargetScore","directCompleteRate");
    }


}
