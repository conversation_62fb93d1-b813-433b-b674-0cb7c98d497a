package com.ctrip.tour.business.dashboard.utils;

import com.ctrip.tour.business.dashboard.tktBusiness.select43556.JSONNullSerializer;
import com.google.gson.*;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONNull;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/21
 */
@Slf4j
public class MapperUtil {

    /*
    * 定制calendar类的反序列化格式  便于后续解析
    * 定制Object转Number策略 解决Object转为int或者long时带上的小数点
    * */
    private static final Gson gson = new GsonBuilder()
            .setObjectToNumberStrategy(ToNumberPolicy.LAZILY_PARSED_NUMBER)
            .registerTypeHierarchyAdapter(Calendar.class,new CalendarSerializer())
            .registerTypeAdapter(JSONNull.class,new JSONNullSerializer())
            .disableHtmlEscaping().create();

    /*
    * 对象转字符串
    * */
    public static String obj2Str(Object obj){
        return gson.toJson(obj);
    }

    /*
     *字符串转实体类
     */
    public static <T> T str2Obj(String jsonString, Class<T> clazz) {
        return gson.fromJson(jsonString, clazz);
    }


    /*
     *字符串转实体类
     */
    public static <T> T str2Obj(String jsonString, Type typeOfT) {
        return gson.fromJson(jsonString, typeOfT);
    }

    /*
     *字符串转List
     */
    public static <T> List<T> str2List(String jsonString, Class<T> clazz) {
        List<T> list = new ArrayList<>();
        try {
            JsonArray arry = new JsonParser().parse(jsonString).getAsJsonArray();
            for (JsonElement jsonElement : arry) {
                list.add(gson.fromJson(jsonElement, clazz));
            }
        } catch (Exception e) {
            log.error("反序列化字符串失败,原始字符串为:" + jsonString + "," +
                    "尝试反序列化的类型为:" + clazz.getTypeName());
        }
        return list;
    }

    /*
     *字符串转ListList
     */
    public static <T> List<List<T>> str2ListList(String jsonString, Class<T> clazz) {
        List<T> list = str2List(jsonString, clazz);
        List<List<T>> listList = new ArrayList<>();
        for (T item : list) {
            listList.add(str2List(obj2Str(item), clazz));
        }
        return listList;
    }
}
