//package com.ctrip.tour.business.dashboard.sightArchives.dao.salesDao;
//
//import com.ctrip.soa._24922.SalesPieChartSegment;
//import com.ctrip.soa._27181.PreparedParameterBean;
//import com.ctrip.tour.business.dashboard.sightArchives.enums.sales.SalesMetricEnumType;
//import com.ctrip.tour.business.dashboard.tktBusiness.dao.TktStarRocksDao;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Repository;
//
//import java.sql.SQLException;
//import java.sql.Types;
//import java.util.*;
//
//@Repository
//@Slf4j
//public class CdmOrdTtdVstArchiveForFhpDfDao {
//
//    //机酒交叉产量汇总表 数仓侧设计文档:http://conf.ctripcorp.com/pages/viewpage.action?pageId=3672460730
//    //数据源表: dw_ticketdb.cdm_ord_ttd_vst_archive_for_fhp_df
//
//    @Autowired
//    private TktStarRocksDao tktStarRocksDao;
//
//    /**
//     * ord_ttd_suc_ord_cnt	bigint	成交订单量
//     * ord_ttd_fhp_suc_ord_cnt	bigint	机酒交叉成交订单量
//     * ord_ttd_no_fhp_suc_ord_cnt	bigint	非机酒交叉成交订单量
//     * ord_ttd_suc_qty_amt	bigint	成交票量
//     * ord_ttd_fhp_suc_qty_amt	bigint	机酒交叉成交票量
//     * ord_ttd_no_fhp_suc_qty_amt	bigint	非机酒交叉成交票量
//     * ord_ttd_suc_income	decimal(14,2)	成交GMV
//     * ord_ttd_fhp_suc_income	decimal(14,2)	机酒交叉成交GMV
//     * ord_ttd_no_fhp_suc_income	decimal(14,2)	非机酒交叉成交GMV
//     * ord_ttd_suc_profit	decimal(14,2)	成交毛利
//     * ord_ttd_fhp_suc_profit	decimal(14,2)	机酒交叉成交毛利
//     * ord_ttd_no_fhp_suc_profit	decimal(14,2)	非机酒交叉成交毛利
//     * ord_ttd_total_refund_ord_cnt	bigint	全退订单数
//     * ord_ttd_fhp_total_refund_ord_cnt	bigint	机酒交叉全退订单量
//     * ord_ttd_no_fhp_total_refund_ord_cnt	bigint	非机酒交叉全退订单量
//     * ord_ttd_sbt_ord_cnt	bigint	提交订单量
//     * ord_ttd_fhp_sbt_ord_cnt	bigint	机酒交叉提交订单量
//     * ord_ttd_no_fhp_sbt_ord_cnt	bigint	非机酒交叉提交订单量
//     */
//
//
//    public List<SalesPieChartSegment> querySalesPieChart(
//            Long sightId
//            , Integer dateType
//            , String startDate
//            , String endDate
//            , Boolean needSubSight
//            , Integer businessType
//            , List<Long> vendorIdList
//            , String drillDownColumn
//            , String metricName
//    ){
//
//        StringBuilder sql = new StringBuilder(
//                "select "
//                        //机酒交叉成交订单量/成交订单量
//                        + "sum(ord_ttd_fhp_suc_ord_cnt)/sum(ord_ttd_suc_ord_cnt) as fhp_suc_ord_cnt_ratio, "
//                        //非机酒交叉成交订单量/成交订单量
//                        + "sum(ord_ttd_no_fhp_suc_ord_cnt)/sum(ord_ttd_suc_ord_cnt) as no_fhp_suc_ord_cnt_ratio, "
//                        //机酒交叉成交票量/成交票量
//                        + "sum(ord_ttd_fhp_suc_qty_amt)/sum(ord_ttd_suc_qty_amt) as fhp_suc_qty_amt_ratio, "
//                        //非机酒交叉成交票量/成交票量
//                        + "sum(ord_ttd_no_fhp_suc_qty_amt)/sum(ord_ttd_suc_qty_amt) as no_fhp_suc_qty_amt_ratio, "
//                        //机酒交叉成交GMV/成交GMV
//                        + "sum(ord_ttd_fhp_suc_income)/sum(ord_ttd_suc_income) as fhp_suc_income_ratio, "
//                        //非机酒交叉成交GMV/成交GMV
//                        + "sum(ord_ttd_no_fhp_suc_income)/sum(ord_ttd_suc_income) as no_fhp_suc_income_ratio, "
//                        //机酒交叉成交毛利/成交毛利
//                        + "sum(ord_ttd_fhp_suc_profit)/sum(ord_ttd_suc_profit) as fhp_suc_profit_ratio, "
//                        //非机酒交叉成交毛利/成交毛利
//                        + "sum(ord_ttd_no_fhp_suc_profit)/sum(ord_ttd_suc_profit) as no_fhp_suc_profit_ratio, "
//                        //机酒交叉成交毛利率
//                        + "sum(ord_ttd_fhp_suc_profit)/sum(ord_ttd_fhp_suc_income) as fhp_suc_profit_rate, "
//                        //非机酒交叉成交毛利率
//                        + "sum(ord_ttd_no_fhp_suc_profit)/sum(ord_ttd_no_fhp_suc_income) as no_fhp_suc_profit_rate, "
//                        //机酒交叉全退订单量/全退订单量
//                        + "sum(ord_ttd_fhp_total_refund_ord_cnt)/sum(ord_ttd_total_refund_ord_cnt) as fhp_total_refund_ord_cnt_ratio, "
//                        //非机酒交叉全退订单量/全退订单量
//                        + "sum(ord_ttd_no_fhp_total_refund_ord_cnt)/sum(ord_ttd_total_refund_ord_cnt) as no_fhp_total_refund_ord_cnt_ratio "
//
//                        + "from cdm_ord_ttd_vst_archive_df "
//        );
//
//        List<PreparedParameterBean> parameters = new ArrayList<>();
//        appendSightId(parameters, sql, sightId, needSubSight);
//        appendDateRange(parameters, sql, dateType, startDate, endDate);
//        appendBusinessType(parameters, sql, businessType);
////        appendVendorIdList(parameters, sql, vendorIdList);
//        appendGroupBy(sql, drillDownColumn);
//
//        List<Map<String, Object>> result = null;
//        try {
//            result = tktStarRocksDao.getListResultNew(sql.toString(),parameters);
//        } catch (SQLException e) {
//            log.warn("querySalesPieChart error", e);
//        }
//        result = CollectionUtils.isEmpty(result) ? new ArrayList<>() : result;
//        if(CollectionUtils.isEmpty(result)){
//            return new ArrayList<>();
//        }
//        Map<String,Object> map = result.get(0);
//
//        List<SalesPieChartSegment> pieChartSegmentList = new ArrayList<>();
//        SalesPieChartSegment fhpPieChartSegment = new SalesPieChartSegment();
//        fhpPieChartSegment.setName("机酒交叉");
//        SalesPieChartSegment noFhpPieChartSegment = new SalesPieChartSegment();
//        noFhpPieChartSegment.setName("非机酒交叉");
//        if(SalesMetricEnumType.ORDER_COUNT.getEnglishName().equals(metricName)) {
//            fhpPieChartSegment.setMetricValue(map.get("fhp_suc_ord_cnt_ratio") == null ? 0 : (Double) map.get("fhp_suc_ord_cnt_ratio"));
//            noFhpPieChartSegment.setMetricValue(map.get("no_fhp_suc_ord_cnt_ratio") == null ? 0 : (Double) map.get("no_fhp_suc_ord_cnt_ratio"));
//        }else if(SalesMetricEnumType.SOLD_TICKET_COUNT.getEnglishName().equals(metricName)){
//            fhpPieChartSegment.setMetricValue(map.get("fhp_suc_qty_amt_ratio") == null ? 0 : (Double) map.get("fhp_suc_qty_amt_ratio"));
//            noFhpPieChartSegment.setMetricValue(map.get("no_fhp_suc_qty_amt_ratio") == null ? 0 : (Double) map.get("no_fhp_suc_qty_amt_ratio"));
//        }else if(SalesMetricEnumType.GMV.getEnglishName().equals(metricName)){
//            fhpPieChartSegment.setMetricValue(map.get("fhp_suc_income_ratio") == null ? 0 : (Double) map.get("fhp_suc_income_ratio"));
//            noFhpPieChartSegment.setMetricValue(map.get("no_fhp_suc_income_ratio") == null ? 0 : (Double) map.get("no_fhp_suc_income_ratio"));
//        }else if(SalesMetricEnumType.PROFIT.getEnglishName().equals(metricName)){
//            fhpPieChartSegment.setMetricValue(map.get("fhp_suc_profit_ratio") == null ? 0 : (Double) map.get("fhp_suc_profit_ratio"));
//            noFhpPieChartSegment.setMetricValue(map.get("no_fhp_suc_profit_ratio") == null ? 0 : (Double) map.get("no_fhp_suc_profit_ratio"));
//        }else if(SalesMetricEnumType.PROFIT_RATE.getEnglishName().equals(metricName)){
//            fhpPieChartSegment.setMetricValue(map.get("fhp_suc_profit_ratio") == null ? 0 : (Double) map.get("fhp_suc_profit_ratio"));
//            noFhpPieChartSegment.setMetricValue(map.get("no_fhp_suc_profit_ratio") == null ? 0 : (Double) map.get("no_fhp_suc_profit_ratio"));
//        }else if(SalesMetricEnumType.FULLY_REFUNDED_ORDER_RATE.getEnglishName().equals(metricName)){
//            fhpPieChartSegment.setMetricValue(map.get("fhp_total_refund_ord_cnt_ratio") == null ? 0 : (Double) map.get("fhp_total_refund_ord_cnt_ratio"));
//            noFhpPieChartSegment.setMetricValue(map.get("no_fhp_total_refund_ord_cnt_ratio") == null ? 0 : (Double) map.get("no_fhp_total_refund_ord_cnt_ratio"));
//        }
//        pieChartSegmentList.add(fhpPieChartSegment);
//        pieChartSegmentList.add(noFhpPieChartSegment);
//        return pieChartSegmentList;
//    }
//
//    private void appendGroupBy(StringBuilder sql, String drillDownColumn){
//        sql.append(" group by ").append(drillDownColumn);
//    }
//
//    //拼景点id
//    private void appendSightId(List<PreparedParameterBean> parameters, StringBuilder sql, Long sightId, Boolean needSubSight){
//        if(needSubSight){
//            sql.append(" where parent_vst_id = ?");
//        }else {
//            sql.append(" where vst_id = ?");
//        }
//        parameters.add(new PreparedParameterBean(String.valueOf(sightId), Types.BIGINT));
//    }
//    //拼日期范围
//    private void appendDateRange(List<PreparedParameterBean> parameters, StringBuilder sql, Integer dateType, String startDate, String endDate){
//
//        if(dateType == 2){
//            sql.append(" and ord_date between ? and ?");
//        }else {
//            sql.append(" and use_date between ? and ?");
//        }
//        parameters.add(new PreparedParameterBean(startDate, Types.VARCHAR));
//        parameters.add(new PreparedParameterBean(endDate, Types.VARCHAR));
//
//    }
//
//    //拼业务类型
//    private void appendBusinessType(List<PreparedParameterBean> parameters, StringBuilder sql, Integer businessType){
//        if(businessType != 1){
//            sql.append(" and bu_type = ?");
//            parameters.add(new PreparedParameterBean(businessType==2?"门票":"活动", Types.VARCHAR));
//        }
//
//    }
//    //拼供应商id列表
//    private void appendVendorIdList(List<PreparedParameterBean> parameters, StringBuilder sql, List<Long> vendorIdList){
//        if(CollectionUtils.isNotEmpty(vendorIdList)){
//            sql.append(" and vendor_id in (");
//            for(int i = 0; i < vendorIdList.size(); i++){
//                if(i == 0){
//                    sql.append("?");
//                }else {
//                    sql.append(",?");
//                }
//                parameters.add(new PreparedParameterBean(String.valueOf(vendorIdList.get(i)), Types.BIGINT));
//            }
//            sql.append(")");
//        }
//    }
//
//
//}
