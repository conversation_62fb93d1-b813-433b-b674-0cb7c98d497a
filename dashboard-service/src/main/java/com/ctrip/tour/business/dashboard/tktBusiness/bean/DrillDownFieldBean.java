package com.ctrip.tour.business.dashboard.tktBusiness.bean;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
public class DrillDownFieldBean {

    String subMetric;
    String field;
    Long baseInfoId;
    List<String> baseInfoGroupList;
    List<Integer> baseInfoLikeIndexList;
    Map<String,List<String>> tableGroupListMap;
    List<String> tableOrderList;
    //获取下钻数据id对应map
    Map<String,Long> tableDataIdMap;
    //获取气泡图数据分组字段对应的map
    Map<String,List<String>> bubbleGroupListMap;
    //多个子指标按商拓下钻时 需要拼接的特殊条件字段的key
    String bdSpecificCondition;
    //多个子指标按商拓下钻时 获取同比数据需要加载的子指标
    List<String> bdSpecificList;
    //子指标对应的examineTypeList
    List<Integer> examineTypeList;

}
