package com.ctrip.tour.business.dashboard.tktBusiness.entity;


import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

@Entity
@Database(name = "TtdReportDB_W")
@Table(name = "dim_prd_tkt_scenic_info_core_mapping")
public class DimPrdTktScenicInfoCoreMapping {

    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 景点id
     */
    @Column(name = "viewspot_id")
    @Type(value = Types.BIGINT)
    private String viewspotId;

    /**
     * 景点名称
     */
    @Column(name = "viewspot_name")
    @Type(value = Types.VARCHAR)
    private Integer viewspotName;

    /**
     * 国内海外:  0-海外； 1-国内
     */
    @Column(name = "is_domestic")
    @Type(value = Types.INTEGER)
    private String isDomestic;

    /**
     * 城市id
     */
    @Column(name = "city_id")
    @Type(value = Types.BIGINT)
    private String cityId;

    /**
     * 城市名称
     */
    @Column(name = "city_name")
    @Type(value = Types.VARCHAR)
    private String cityName;

    /**
     * 省份id
     */
    @Column(name = "province_id")
    @Type(value = Types.BIGINT)
    private Integer provinceId;

    /**
     * 省份名称
     */
    @Column(name = "province_name")
    @Type(value = Types.VARCHAR)
    private Integer provinceName;

    /**
     * 国家id
     */
    @Column(name = "country_id")
    @Type(value = Types.BIGINT)
    private Integer countryId;

    /**
     * 国家名称
     */
    @Column(name = "country_name")
    @Type(value = Types.VARCHAR)
    private Integer countryName;

    /**
     * 业务大区id
     */
    @Column(name = "business_region_id")
    @Type(value = Types.BIGINT)
    private Integer businessRegionId;

    /**
     * 业务大区名称
     */
    @Column(name = "business_region_name")
    @Type(value = Types.VARCHAR)
    private Integer businessRegionName;

    /**
     * 景点经理 域账号
     */
    @Column(name = "viewspot_meid")
    @Type(value = Types.VARCHAR)
    private Integer viewspotMeid;

    /**
     * 景点助理 域账号
     */
    @Column(name = "viewspot_aeid")
    @Type(value = Types.VARCHAR)
    private Integer viewspotAeid;

    /**
     * 分区日期
     */
    @Column(name = "partition_d")
    @Type(value = Types.VARCHAR)
    private Integer partitionD;

    /**
     * 修改时间
     */
    @Column(name = "DataChange_LastTime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getViewspotId() {
        return viewspotId;
    }

    public void setViewspotId(String viewspotId) {
        this.viewspotId = viewspotId;
    }

    public Integer getViewspotName() {
        return viewspotName;
    }

    public void setViewspotName(Integer viewspotName) {
        this.viewspotName = viewspotName;
    }

    public String getIsDomestic() {
        return isDomestic;
    }

    public void setIsDomestic(String isDomestic) {
        this.isDomestic = isDomestic;
    }

    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public Integer getProvinceId() {
        return provinceId;
    }

    public void setProvinceId(Integer provinceId) {
        this.provinceId = provinceId;
    }

    public Integer getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(Integer provinceName) {
        this.provinceName = provinceName;
    }

    public Integer getCountryId() {
        return countryId;
    }

    public void setCountryId(Integer countryId) {
        this.countryId = countryId;
    }

    public Integer getCountryName() {
        return countryName;
    }

    public void setCountryName(Integer countryName) {
        this.countryName = countryName;
    }

    public Integer getBusinessRegionId() {
        return businessRegionId;
    }

    public void setBusinessRegionId(Integer businessRegionId) {
        this.businessRegionId = businessRegionId;
    }

    public Integer getBusinessRegionName() {
        return businessRegionName;
    }

    public void setBusinessRegionName(Integer businessRegionName) {
        this.businessRegionName = businessRegionName;
    }

    public Integer getViewspotMeid() {
        return viewspotMeid;
    }

    public void setViewspotMeid(Integer viewspotMeid) {
        this.viewspotMeid = viewspotMeid;
    }

    public Integer getViewspotAeid() {
        return viewspotAeid;
    }

    public void setViewspotAeid(Integer viewspotAeid) {
        this.viewspotAeid = viewspotAeid;
    }

    public Integer getPartitionD() {
        return partitionD;
    }

    public void setPartitionD(Integer partitionD) {
        this.partitionD = partitionD;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }
}
