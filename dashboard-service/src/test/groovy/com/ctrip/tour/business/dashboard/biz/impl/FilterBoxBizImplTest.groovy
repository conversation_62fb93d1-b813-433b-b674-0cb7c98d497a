package com.ctrip.tour.business.dashboard.biz.impl


import com.ctrip.soa._24922.GetEmployeeByFilterRequestType
import com.ctrip.soa._24922.GetOrganizationByFilterRequestType
import com.ctrip.tour.business.dashboard.tktBusiness.biz.impl.FilterBoxBizImpl
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardEmployeeInfoDao
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardOrganizationInfoDao
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardEmployeeInfo
import com.ctrip.tour.business.dashboard.utils.MapperUtil
import spock.lang.Specification

class FilterBoxBizImplTest extends Specification {

    def employeeInfoDao = Mock(BusinessDashboardEmployeeInfoDao)

    def organizationInfoDao = Mock(BusinessDashboardOrganizationInfoDao)

    def remoteConfig = Mock(RemoteConfig)

    def filterBoxBiz = new FilterBoxBizImpl(employeeInfoDao: employeeInfoDao,organizationInfoDao: organizationInfoDao,remoteConfig: remoteConfig)

    def "GetEmployeeByFilter"() {

        given: "设置请求参数"
        def request = new GetEmployeeByFilterRequestType(orgId:orgId, boardType:boardType)

        and: "mock qconfig配置信息"
        remoteConfig.getExternalConfig("admin") >> "S35097"
        remoteConfig.getEmployeeRelationValue(null) >> "S35097"
        remoteConfig.getExternalConfig("adminInferior") >> "D00024|S52754"

        and: "mock接口请求返回数据"
        employeeInfoDao.queryByEmpCode("D00024") >> getResponse("{\"id\":452,\"empCode\":\"D00024\",\"displayName\":\"Zhe Li （李哲）\",\"domainName\":\"zheli\",\"leaderEmpCode\":\"S35097\",\"leaderEmpName\":\"JIM Ji （季毅华）\",\"teamId\":\"41999\",\"teamCname\":\"门票活动业务部\",\"orgIdPath\":\"Ctrip_CO0001 Ctrip_Board 29 SO002916 25481 41999\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部\",\"position\":\"\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:38:56 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:38:56 AM\"}")
        employeeInfoDao.queryByEmpCode("S52754") >> getResponse("{\"id\":171,\"empCode\":\"S52754\",\"displayName\":\"Chase Liu （刘畅）\",\"domainName\":\"chang.liu\",\"leaderEmpCode\":\"S35097\",\"leaderEmpName\":\"JIM Ji （季毅华）\",\"teamId\":\"41999\",\"teamCname\":\"门票活动业务部\",\"orgIdPath\":\"Ctrip_CO0001 Ctrip_Board 29 SO002916 25481 41999\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部\",\"position\":\"\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:38:56 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:38:56 AM\"}")

        when: "调用接口"
        def response = filterBoxBiz.getEmployeeByFilter(request);

        then: "验证返回结果"
        with(response) {
            employeeItemList.size()==2
            employeeItemList[0].empCode == "S52754"
            employeeItemList[1].empCode == "D00024"
        }

        where: "用表格方式验证多种返回结果"
        orgId   | boardType
        "41999" | "oversea"
    }

    def "getOrganizationByFilter"(){
        given: "设置请求参数"
        def request = new GetOrganizationByFilterRequestType()

        and: "mock qconfig配置信息"
        remoteConfig.getExternalConfig("admin") >> "S35097"
        remoteConfig.getEmployeeRelationValue(null) >> "S35097"
        remoteConfig.getExternalConfig("adminInferior") >> "D00024|S52754"

        and: "mock接口请求返回数据"
        employeeInfoDao.queryByEmpCode(_)>>null

        when: "调用接口"
        def response = filterBoxBiz.getOrganizationByFilter(request);

        then: "验证返回结果"
        with(response) {
               organizationItemList.size()==0
        }

    }


    def getResponse(def str) {
        return MapperUtil.str2Obj(str, BusinessDashboardEmployeeInfo.class);
    }
}
