package com.ctrip.tour.business.dashboard.tktBusiness.validator;

import com.ctrip.soa._24922.GetDrillDownBaseInfoRequestType;
import com.ctrip.soa._24922.GetDrillDownBaseInfoResponseType;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.InputArgumentException;
import com.ctrip.tour.business.dashboard.utils.MapperUtil;
import com.ctrip.tour.business.dashboard.utils.ParamterCheckUtil;
import com.ctrip.train.tieyouflight.soa.validation.validator.BusinessConstraintValidator;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/9/8
 */
@Component
public class GetDrillDownBaseInfoValidator extends BusinessConstraintValidator<GetDrillDownBaseInfoRequestType, GetDrillDownBaseInfoResponseType> {
    @Override
    public GetDrillDownBaseInfoResponseType validateBusiness(GetDrillDownBaseInfoRequestType request) {

        String domainName = request.getDomainName();
        if (!ParamterCheckUtil.checkDomainName(domainName)) {
            throw new InputArgumentException("input error domainName:" + MapperUtil.obj2Str(request));
        }
        String metric = request.getMetric();
        if (!ParamterCheckUtil.checkMetric(metric)) {
            throw new InputArgumentException("input error metric:" + MapperUtil.obj2Str(request));
        }
        Boolean needSearch = request.isNeedSearch();
        if(!ParamterCheckUtil.checkNeedSearch(needSearch)){
            throw new InputArgumentException("input error needSearch:" + MapperUtil.obj2Str(request));
        }
        String searchField = request.getSearchField();
        if(needSearch){
            if(!ParamterCheckUtil.checkField(searchField)){
                throw new InputArgumentException("input error searchField:" + MapperUtil.obj2Str(request));
            }
        }
        return null;
    }
}
