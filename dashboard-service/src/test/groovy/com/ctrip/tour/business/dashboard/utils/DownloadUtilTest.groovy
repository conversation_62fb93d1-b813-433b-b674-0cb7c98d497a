package com.ctrip.tour.business.dashboard.utils

import spock.lang.Specification

class DownloadUtilTest extends Specification {
    def "test post"() {
        expect: "call & verify"
        DownloadUtil.post(url, data)

        where: "多条件验证"
        url     |   data
        "http://offline.fx.ctripcorp.com/soa2/28369/getLatelyViewPage"  |   "{\"userName\": \"yyang25\"}"
    }

    /*def "test get"() {
        expect: "call & verify"
        DownloadUtil.get("http://wwww.baidu.com")
    }*/
}
