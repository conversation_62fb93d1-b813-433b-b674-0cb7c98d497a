//package com.ctrip.tour.business.dashboard.grpBusiness.handler.metric.multiplePrice;
//
//import static com.ctrip.tour.business.dashboard.grpBusiness.exception.ExceptionEnum.EMP_CAN_NOT_FIND;
//
//import com.ctrip.soa._24922.GetGrpMetricDataRequestType;
//import com.ctrip.tour.business.dashboard.grpBusiness.annotation.MetricData;
//import com.ctrip.tour.business.dashboard.grpBusiness.common.GrpConstant;
//import com.ctrip.tour.business.dashboard.grpBusiness.dao.GrpBusinessDashboardUpdatetimeDao;
//import com.ctrip.tour.business.dashboard.grpBusiness.dao.starrocks.AdmPrdGrpMultiplePriceWorkPlatformDfDao;
//import com.ctrip.tour.business.dashboard.grpBusiness.enums.MetricCategoryEnum;
//import com.ctrip.tour.business.dashboard.grpBusiness.enums.MetricDimEnum;
//import com.ctrip.tour.business.dashboard.grpBusiness.enums.TimeAggTypeEnum;
//import com.ctrip.tour.business.dashboard.grpBusiness.handler.dim.DimconvertHandler;
//import com.ctrip.tour.business.dashboard.grpBusiness.handler.metric.GrpBussinessAbstractMetricService;
//import com.ctrip.tour.business.dashboard.grpBusiness.metrics.multiplePrice.GrpCprMultiplePriceLessRateService;
//import com.ctrip.tour.business.dashboard.grpBusiness.service.HrOrgEmpInfoService;
//import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
//import com.ctrip.tour.business.dashboard.tktBusiness.exception.ServiceException;
//import com.ctrip.tour.business.dashboard.utils.Calculator;
//import com.ctrip.tour.business.dashboard.utils.MapperUtil;
//import com.ctrip.tour.business.dashboard.utils.SqlBuilder;
//import com.google.common.base.Joiner;
//import com.google.common.collect.Lists;
//import com.google.common.collect.Maps;
//import lombok.extern.slf4j.Slf4j;
//
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.lang.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.ApplicationContext;
//import org.springframework.stereotype.Service;
//
//import java.math.BigDecimal;
//import java.sql.SQLException;
//import java.text.SimpleDateFormat;
//import java.time.LocalDate;
//import java.time.format.DateTimeFormatter;
//import java.util.Collection;
//import java.util.Date;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.Objects;
//import java.util.function.Function;
//import java.util.stream.Collectors;
//
//
////@Service(value = GrpConstant.MULTIPLE_PRICE_LESS_RATE)
//@Slf4j
////@MetricData(value = GrpConstant.MULTIPLE_PRICE_LESS_RATE, needWeekOverWeek = true, isRatio = true)
//public class GrpCrpMultiplePriceLessRateQueryServiceBak extends GrpBussinessAbstractMetricService {
//
//
//    private final static String PRODUCT_ID = "productid";
//    private final static String SUB_BU_TYPE = "sub_bu_type";
//    private final static String PM_EID = "pm_eid";
//    private final static String LOCAL_PM_EID = "local_pmeid";
//    private final static String AVG_MULTIPLE_PRICE = "avg_multiple_price";
//    private final static String CLICK_PV = "click_pv";
//    private final static String VIEW_DATE = "view_date";
//    private final static String PRD_REGION_ID = "prd_region_id";
//    private final static String PRD_PATTERN_ID = "prd_pattern_name";
//    private final static String BIZ_MODE = "biz_mode";
//    private final static String EMP_CODES = "empCodes";
//    private final static String OR = "OR";
//    private final static String ADMIN_EID = "grp.admin.eid";
//    private final static String GRP_GRP_ADMIN_EID = "grp.grp.admin.eid";
//    private final static String GRP_PRV_ADMIN_EID = "grp.prv.admin.eid";
//
//    @Autowired
//    private RemoteConfig remoteConfig;
//    @Autowired
//    private GrpCprMultiplePriceLessRateService grpCprMultiplePriceLessRateService;
//    @Autowired
//    private HrOrgEmpInfoService hrOrgEmpInfoService;
//    @Autowired
//    GrpBusinessDashboardUpdatetimeDao updatetimeDao;
//    @Autowired
//    AdmPrdGrpMultiplePriceWorkPlatformDfDao multiplePriceDfDao;
//
//    public GrpCrpMultiplePriceLessRateQueryServiceBak(ApplicationContext ac) {
//        super(ac);
//    }
//
//    @Override
//    protected Map<String, Object> request2Param(GetGrpMetricDataRequestType requestType, int bizMode) {
//        Map<String, Object> param = Maps.newHashMap();
//
//        List<String> empCodes = Lists.newArrayList(requestType.getEmpCode());
//        try {
//
//            String adminEid = remoteConfig.getExternalConfig(ADMIN_EID);
//
//            if (StringUtils.equals(requestType.getEmpCode(), adminEid)) {
//                String grpAdminEid = remoteConfig.getExternalConfig(GRP_GRP_ADMIN_EID);
//                String prvAdminEid = remoteConfig.getExternalConfig(GRP_PRV_ADMIN_EID);
//                if (Objects.equals(bizMode, 0)) {
//                    empCodes.addAll(hrOrgEmpInfoService.getAllSubordinateEmpCode(grpAdminEid));
//                    empCodes.addAll(hrOrgEmpInfoService.getAllSubordinateEmpCode(prvAdminEid));
//                } else if (Objects.equals(bizMode, 1) || Objects.equals(bizMode, 3)) {
//                    empCodes.addAll(hrOrgEmpInfoService.getAllSubordinateEmpCode(grpAdminEid));
//                } else if (Objects.equals(bizMode, 2) || Objects.equals(bizMode, 4)) {
//                    empCodes.addAll(hrOrgEmpInfoService.getAllSubordinateEmpCode(prvAdminEid));
//                }
//            } else {
//                empCodes.addAll(hrOrgEmpInfoService.getAllSubordinateEmpCode(requestType.getEmpCode()));
//            }
//        } catch (SQLException e) {
//            log.warn("query empcode error" + requestType.getEmpCode(), e);
//            throw new ServiceException(EMP_CAN_NOT_FIND.getCode(),EMP_CAN_NOT_FIND.getMsg());
//        }
//
//
//        Integer businessLine = requestType.getBusinessLine();
//
//        if (Objects.equals(0, businessLine)) {
//            param.put(BIZ_MODE, 1);
//            param.put(EMP_CODES, empCodes);
//        } else if (Objects.equals(1, businessLine)) { //跟团
//            param.put(Joiner.on("-").join(OR, PM_EID, LOCAL_PM_EID), new List[]{empCodes, empCodes});
//        } else if (Objects.equals(2, businessLine)) {//私家团
//            param.put(PM_EID, empCodes);
//        }
//
//
//        param.put(PRD_PATTERN_ID, requestType.getProductPattern());
//        param.put(VIEW_DATE, new String[]{requestType.getStartDate(), requestType.getEndDate()});
//        if (businessLine == 1 || businessLine == 2) {
//            param.put(SUB_BU_TYPE, businessLine == 1?"跟团游":"独立出游");//NOSONAR
//        }
//        return param;
//    }
//
//
//
//    private String[] calcOverDate(String startDate, String endDate, int offset) {
//        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
//        LocalDate hisStartDate = LocalDate.parse(startDate, dtf).minusDays(offset);
//        LocalDate hisEndDate = LocalDate.parse(endDate, dtf).minusDays(offset);
//        return new String[]{dtf.format(hisStartDate), dtf.format(hisEndDate)};
//    }
//
//    @Override
//    protected Map<String, Object> replaceQueryDate4Week(Map<String, Object> param , String startDate, String endDate) {
//        param.put(VIEW_DATE, calcOverDate(startDate, endDate, 7));
//        return param;
//    }
//
//    @Override
//    protected Map<String, Object> replaceQueryDate4Month(Map<String, Object> param, String startDate, String endDate) {
//        param.put(VIEW_DATE, calcOverDate(startDate, endDate, 30));
//        return param;
//    }
//
//    @Override
//    protected Map<String, Object> replaceQueryDate4Year(Map<String, Object> param, String startDate, String endDate) {
//        param.put(VIEW_DATE, calcOverYearDate(startDate, endDate));
//        return param;
//    }
//
//
//    @Override
//    public Map<String, Object> queryMetricTrendLine(Map<String, Object> param, GetGrpMetricDataRequestType requestType) {
//
//        String timeAggType = requestType.getAggregationGranularity();
//
//        String tableName = "adm_prd_grp_multiple_price_work_platform_df";
//        String calcExpression = "multiple_price_less_cnt/product_cnt";
//        String calcFieldName = "multiple_price_less_cnt,product_cnt";
//        String calcDateName = "view_date";
//
//        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
//        List<Map<String, Object>> resultData = Lists.newArrayList();
//        if (StringUtils.equals(timeAggType, TimeAggTypeEnum.DAY.getName())) {
////            List<String> allDays = getAllDayByDateRange(dateRange[0], dateRange[1]);
//            List<String> gropCols = Lists.newArrayList("productid", VIEW_DATE);
//            HashMap<String, Object> paramMap = Maps.newHashMap();
//            paramMap.putAll(param);
//            String sql = buildQuerySql(paramMap, gropCols,  MetricDimEnum.MULTIPLE_PRICE_LESS_RATE.getTableName(), timeAggType);
//            System.out.println("sql: " + sql+" param: "+ MapperUtil.obj2Str(param));  //todo 本地调试
//            List<Map<String, Object>> rowData = null;
//            try {
//                rowData = multiplePriceDfDao.query(sql, null);
//            } catch (SQLException e) {
//                log.warn("query multiplePrice error", e);
//            }
//            if (CollectionUtils.isEmpty(rowData)) {
//                return null;
//            }
//
//            List<String> allDays = grpCprMultiplePriceLessRateService.getAllDays(requestType.getStartDate(), requestType.getEndDate());
//
//            Map<String, Object> calcRst = rowData.stream()
//                    .collect(Collectors.toMap(k -> {
//                        Object o = k.get(calcDateName);
//                        if (o instanceof String) {
//                            return String.valueOf(o);
//                        }
//                        if (o instanceof Date) {
//
//                            return formatter.format((Date) o);
//                        }
//                        return null;
//                    }, k -> {
//                        if (StringUtils.isNotBlank(calcExpression)) {
//                            BigDecimal bigDecimal = Calculator.parseExpressionAndCalc(calcExpression, k);
//                            return bigDecimal;
//                        }
//                        Object resultValue = k.get("result_value");
//                        if (Objects.isNull(resultValue)) {
//                            resultValue = k.get(calcFieldName);
//                        }
//                        if (resultValue instanceof Long) {
//                            return BigDecimal.valueOf((Long) resultValue);
//                        } else if (resultValue instanceof Double) {
//                            return BigDecimal.valueOf((Double) resultValue);
//                        }
//                        return resultValue;
//                    }));
//            return allDays.stream().collect(Collectors.toMap(Function.identity(), day -> {
//                Object o = calcRst.get(day);
//                if (Objects.isNull(o)) {
//                    return "";
//                }
//                return o;
//            }));
//
//
//        } else if (StringUtils.equals(timeAggType, TimeAggTypeEnum.WEEK.getName())) {
//            List<String> allWeeks = grpCprMultiplePriceLessRateService.getAllWeekByDateRange(requestType.getStartDate(), requestType.getEndDate());
//
//            resultData = allWeeks.parallelStream()
//                    .map(week -> {
//                        List<String> gropCols = Lists.newArrayList("productid");
//                        HashMap<String, Object> paramMap = Maps.newHashMap();
//                        paramMap.putAll(param);
//                        paramMap.put(calcDateName, week.split("~"));
//                        String sql = buildQuerySql(paramMap, gropCols,  MetricDimEnum.MULTIPLE_PRICE_LESS_RATE.getTableName(), "");
//                        System.out.println("sql: " + sql+" param: "+ MapperUtil.obj2Str(param));  //todo 本地调试
//                        List<Map<String, Object>> rowData = null;
//                        try {
//                            rowData = multiplePriceDfDao.query(sql, null);
//                        } catch (SQLException e) {
//                            log.warn("query multiplePrice error", e);
//                        }
//                        if (CollectionUtils.isEmpty(rowData)) {
//                            return null;
//                        }
//                        Map<String, Object> resultMap = rowData.get(0);
//                        resultMap.put(calcDateName, week);
//                        return resultMap;
//                    }).filter(Objects::nonNull).collect(Collectors.toList());
//        } else if (StringUtils.equals(timeAggType, TimeAggTypeEnum.MONTH.getName())) {
//            List<String> allMonths = grpCprMultiplePriceLessRateService.getAllMonthByDateRange(requestType.getStartDate(), requestType.getEndDate());
//            resultData = allMonths.parallelStream()
//                    .map(month -> {
//                        List<String> gropCols = Lists.newArrayList("productid");
//                        HashMap<String, Object> paramMap = Maps.newHashMap();
//                        paramMap.putAll(param);
//                        paramMap.put(calcDateName, month.split("~"));
//                        String sql = buildQuerySql(paramMap, gropCols,  MetricDimEnum.MULTIPLE_PRICE_LESS_RATE.getTableName(),"");
//                        System.out.println("sql: " + sql+" param: "+ MapperUtil.obj2Str(param));  //todo 本地调试
//                        List<Map<String, Object>> rowData = null;
//                        try {
//                            rowData = multiplePriceDfDao.query(sql, null);
//                        } catch (SQLException e) {
//                            log.warn("query multiplePrice error", e);
//                        }
//                        if (CollectionUtils.isEmpty(rowData)) {
//                            return null;
//                        }
//                        Map<String, Object> resultMap = rowData.get(0);
//                        String monthStartDate = month.split("~")[0];
//                        resultMap.put(calcDateName, monthStartDate.substring(0, monthStartDate.lastIndexOf("-")));
//                        return resultMap;
//                    }).filter(Objects::nonNull).collect(Collectors.toList());
//        }
//
//
//
////        String sql = buildQuerySql(param, groupByCols, timeAggType, tableName);
////        List<Map<String, Object>> rowData = queryData(sql, param, groupByCols, timeAggType);
//
//        List<Map<String, Object>> maps = grpCprMultiplePriceLessRateService.doHandleResults(grpCprMultiplePriceLessRateService, param, timeAggType, true, resultData);
//        if (CollectionUtils.isNotEmpty(maps)) {
//            return maps.get(0);
//        }
//        return Maps.newHashMap();
//    }
//
//    @Override
//    public List<Map<String, Object>> queryMetricDillDown(Map<String, Object> param, GetGrpMetricDataRequestType requestType) {
//
////        String drillDownDim = requestType.getDrillDownDim();
////
////        List<Map<String, Object>> result = grpCprMultiplePriceLessRateService.queryDillDownData(grpCprMultiplePriceLessRateService, param,
////                Lists.newArrayList(drillDownDim));
////
////        return result;
//        String drillDownDim = requestType.getDrillDownDim();
//        String dimEnumName = DimconvertHandler.convertDimEnumName(drillDownDim, requestType.getMetricCategorieName());
//        List<String> gropCols = Lists.newArrayList("productid", dimEnumName);
//        String sql = buildQuerySql(param, gropCols,  MetricDimEnum.MULTIPLE_PRICE_LESS_RATE.getTableName(),"");
//        List<Map<String, Object>> rowdata = null;
//        try {
//            rowdata = multiplePriceDfDao.query(sql, null);
//        } catch (SQLException e) {
//            log.warn("query multiplePrice error", e);
//        }
//        return grpCprMultiplePriceLessRateService.doHandleResults(grpCprMultiplePriceLessRateService, param, "", false, rowdata);
//    }
//
//    @Override
//    public List<Map<String, Object>> queryMetricCardData(Map<String, Object> param, GetGrpMetricDataRequestType requestType) {
////        List<Map<String, Object>> result = grpCprMultiplePriceLessRateService.queryCardData(grpCprMultiplePriceLessRateService, param,
////                null);
////        return result;
//        List<String> gropCols = Lists.newArrayList("productid");
//        String sql = buildQuerySql(param, gropCols,  MetricDimEnum.MULTIPLE_PRICE_LESS_RATE.getTableName(),"");
//        List<Map<String, Object>> rowdata = null;
//        try {
//            rowdata = multiplePriceDfDao.query(sql, null);
//        } catch (SQLException e) {
//            log.warn("query multiplePrice error", e);
//        }
//        return grpCprMultiplePriceLessRateService.doHandleResults(grpCprMultiplePriceLessRateService, param, "", false, rowdata);
//
//    }
//
//
//
//    private String buildQuerySql(Map<String, ?> param, List<String> groupByCols, String tableName, String timeAggType) {
//
//        String categoryName = MetricCategoryEnum.MULTIPLE_PRICE_CATEGORY.getEnglishName();
//        StringBuilder sql = new StringBuilder();
//
//        sql.append("select count( case\n" + "             when  avg_multiple_price <0.6  then productid\n" + "         end) as multiple_price_less_cnt,\n" + "         count( productid) as product_cnt\n")
//                .append(StringUtils.equalsIgnoreCase("day", timeAggType) ? ",view_date " : "");
//        String dimQuerySql = "";
//        List<String> queryCols = groupByCols.stream().filter(c -> !StringUtils.equalsIgnoreCase("productid", c))
//                .collect(Collectors.toList());
//        if (CollectionUtils.isNotEmpty(queryCols)) {
//            dimQuerySql = Joiner.on(",").join(queryCols);
//            sql.append(",").append(dimQuerySql);
//        }
//        sql.append("     from ");
//
//
//
//
//        SqlBuilder sqlBuilder = new SqlBuilder();
//        String selectSql = "replace(max(concat(view_date,pre1d_less06_pv)), max(view_date),'') as avg_multiple_price";
//        if (CollectionUtils.isNotEmpty(groupByCols)) {
//            selectSql = String.join(",", selectSql, Joiner.on(",").skipNulls().join(groupByCols));
//        }
//        sqlBuilder.select(selectSql);
//        sqlBuilder.from(tableName);
//        SqlBuilder.Condition condition = new SqlBuilder.Condition();
//        for (Map.Entry<String, ?> entry : param.entrySet()) {
//            String key = entry.getKey();
//
//            if (StringUtils.equals("biz_mode", key) || StringUtils.equals("empCodes", key)) {
//                continue;
//            }
//            Object value = entry.getValue();
//            if (key.startsWith(OR)) {
//                String[] splitKey = key.split("-");
//
//                SqlBuilder.Condition orConditon = new SqlBuilder.Condition();
//                for (int i = 1; i < splitKey.length; i++) {
//                    SqlBuilder.Condition subCondition = buildCondition(splitKey[i], ((Object[]) value)[i-1]);
//                    orConditon.or(subCondition);
//                }
//                String sqlWithParams = orConditon.getSqlWithParams();
//                sqlBuilder.and("(" + sqlWithParams + ")");
//            } else {
//                SqlBuilder.Condition newCondition = buildCondition(key, value);
//                condition.and(newCondition);
//
//            }
//
//        }
//
//        String otherCon = doOtherCon(param, categoryName);
//        if (StringUtils.isNotBlank(otherCon)) {
//            condition.and(otherCon);
//        }
//        sqlBuilder.whereWithCondition(condition);
//        sqlBuilder.whereWithCondition(getPartitionDCondition());
//        if (CollectionUtils.isNotEmpty(groupByCols)) {
//            sqlBuilder.groupBy(Joiner.on(",").skipNulls().join(groupByCols));
//        }
//        String whereSql = sqlBuilder.getSql();
//
//        sql.append("(").append(whereSql).append(") as t");
//
//        if (StringUtils.equalsIgnoreCase("day", timeAggType) || StringUtils.isNotBlank(dimQuerySql)) {
//            sql.append(" group by ");
//            if (StringUtils.equalsIgnoreCase("day", timeAggType)) {
//                sql.append(StringUtils.equalsIgnoreCase("day", timeAggType) ? " view_date " : "");
//            }
//            if (StringUtils.isNotBlank(dimQuerySql)) {
//
//                if (StringUtils.equalsIgnoreCase("day", timeAggType)) {
//                    sql.append(",");
//                }
//
//                sql.append(dimQuerySql);
//            }
//        }
//        sql.append(";");
//        return sql.toString();
//    }
//
//    private SqlBuilder.Condition buildCondition(String paramKey, Object paramVal) {
//
//        if (StringUtils.isBlank(paramKey) || Objects.isNull(paramVal)) {
//            return null;
//        }
//
//        SqlBuilder.Condition condition = new SqlBuilder.Condition();
//
//        if (paramVal instanceof String) {
//            condition.eq(paramKey, "'" + paramVal + "'");
//        }else if ( paramVal instanceof Number) {
//            condition.eq(paramKey, paramVal);
//        } else if (paramVal instanceof Collection) {
//            if (((Collection<?>)paramVal).size() > 0) {
//                condition.in(paramKey, (List<?>) paramVal);
//            }
//        } else if (paramVal.getClass().isArray()) {
//            Object[] arrayParam = (Object[]) paramVal;
//            SqlBuilder.Condition lteCon = new SqlBuilder.Condition();
//            if (Objects.nonNull(arrayParam[0])) {
//                if (arrayParam[0] instanceof String) {
//                    condition.gte(paramKey, "'" + arrayParam[0] + "'");
//                } else {
//                    condition.gte(paramKey, arrayParam[0]);
//                }
//
//            }
//            condition.and(lteCon.lte(paramKey, arrayParam[0] instanceof String ?"'" + arrayParam[1] + "'"
//                    : arrayParam[1]), () ->arrayParam.length > 1 && Objects.nonNull(arrayParam[1]));
//        } else {
//            throw new ServiceException("1000400", "value type is not supported");
//        }
//        return condition;
//    }
//
//    private SqlBuilder.Condition getPartitionDCondition() {
//        String switchStr = remoteConfig.getConfigValue("grp.partd.where.switch");
//
//        if ((StringUtils.isBlank(switchStr) || StringUtils.equals("on", switchStr))) {
//            String updateTime = "";
//            try {
//                updateTime = updatetimeDao.getLastUpdateTime();
//            }catch (Exception e){
//                log.error("getGrpUpdateTime error",e);
//            }
//            SqlBuilder.Condition condition = new SqlBuilder.Condition();
//            condition.eq("partition_d", "'" + updateTime + "'");
//            return condition;
//        }
//        return null;
//    }
//
//    protected String doOtherCon(Map<String, ?> param, String categoryName) {
//        Object bizMode = param.get("biz_mode");
//        Object empCodes = param.get("empCodes");
//        if (bizMode != null && Objects.nonNull(empCodes) && CollectionUtils.isNotEmpty((List<String>) empCodes) && Objects.equals(1, bizMode)) {
//
//            List<String> empcodeFormat = ((List<?>) empCodes).stream().map(empCode -> "'" + empCode + "'")
//                    .collect(Collectors.toList());
//
//            String teamSql = SUB_BU_TYPE + " = " + "'跟团游'" + " and (" + PM_EID + " in (" + Joiner.on(",").join(empcodeFormat) + ")"//NOSONAR
//                    + " or " + DimconvertHandler.getLocalPmEidName(categoryName) + " in (" + Joiner.on(",").join(empcodeFormat) + "))";
//
//            String priSql = SUB_BU_TYPE + " = " + "'独立出游'" + " and " + PM_EID + " in (" + Joiner.on(",").join(empcodeFormat) + ")";//NOSONAR
//
//            return " ((" + teamSql + ") or (" + priSql + ")) ";
//        }
//        return null;
//    }
//
//
//    private List<String> queryAllTargetEmpCodes(String empCode) {
//        return null;
//    }
//
//}
