//package com.ctrip.tour.business.dashboard.biz.impl;
//
//import com.ctrip.tour.business.dashboard.grpBusiness.service.HrOrgEmpInfoService;
//import com.google.common.collect.Lists;
//import com.google.common.collect.Maps;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.sql.SQLException;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//
//@SpringBootTest
//@RunWith(SpringRunner.class)
//public class GrpOrgEmpTest {
//
//    @Autowired
//    private HrOrgEmpInfoService hrOrgEmpInfoService;
//
//
//    @Test
//    public void test1() throws SQLException {
//
//        List<String> allSubordinateEmpCode = hrOrgEmpInfoService.getAllSubordinateEmpCode("S40160");
//        System.out.println(allSubordinateEmpCode);
//
//    }
//}
