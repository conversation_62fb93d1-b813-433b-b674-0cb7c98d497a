package com.ctrip.tour.business.dashboard.sightArchives.entity.commonEntity;


import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2025-03-27
 */
@Entity
@Database(name = "TtdReportDB_W")
@Table(name = "bus_sight_archives_annual_intake")
public class BusSightArchivesAnnualIntake {
    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 景点ID
     */
    @Column(name = "sight_id")
    @Type(value = Types.BIGINT)
    private Long sightId;

    /**
     * 年入园量
     */
    @Column(name = "annual_intake")
    @Type(value = Types.DOUBLE)
    private Double annualIntake;

    /**
     * 最后修改人
     */
    @Column(name = "datachange_lastuser")
    @Type(value = Types.VARCHAR)
    private Timestamp datachangeLastuser;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSightId() {
        return sightId;
    }

    public void setSightId(Long sightId) {
        this.sightId = sightId;
    }

    public Double getAnnualIntake() {
        return annualIntake;
    }

    public void setAnnualIntake(Double annualIntake) {
        this.annualIntake = annualIntake;
    }

    public Timestamp getDatachangeLastuser() {
        return datachangeLastuser;
    }

    public void setDatachangeLastuser(Timestamp datachangeLastuser) {
        this.datachangeLastuser = datachangeLastuser;
    }

    public Timestamp getDatachangeLasttime() {
        return datachangeLasttime;
    }

    public void setDatachangeLasttime(Timestamp datachangeLasttime) {
        this.datachangeLasttime = datachangeLasttime;
    }
}
