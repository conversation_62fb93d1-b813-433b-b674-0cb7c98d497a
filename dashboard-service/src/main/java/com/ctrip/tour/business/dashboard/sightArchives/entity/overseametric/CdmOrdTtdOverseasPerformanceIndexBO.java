package com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;
import java.util.List;
@Entity
@Getter
@Setter
public class CdmOrdTtdOverseasPerformanceIndexBO {
    String d;
    // 邮箱前缀
    String domainName;
    //考核层级
    String examineLevel;
    /** 日期，计算30日环比时查询- **/
    String useDate;
    //按季度查询时搜索
    String quarter;
    //按半年查询时搜索
    String halfYear;
    //指定年份
    String year;
    //业务线
    String buTypeName;
    //业务大区
    String buRegionNames;
    String buRegionNameEn;
    Long buRegionId;

    //业务子区域
    String buSubRegionNames;
    String buSubRegionNameEn;
    Long buSubRegionId;
    //CT站
    String ct;
    //站点
    String sites;
    String locale;
    String disChannelName;
    //国家ID
    Long ctryId;
    String ctryName;
    String ctryNameEn;
    Long provId;
    String provName;
    String provNameEn;
    Long cityId;
    String cityName;
    String cityNameEn;
    Long vendId;
    String vendName;
    //景点经理
    String vstMeids;
    //景点助理
    String vstAeids;
    //景点景点ID
    Long vstId;
    String vstName;
    String vstNameEn;

    Double gmv;
    Double profit;
    Double qty;
}
