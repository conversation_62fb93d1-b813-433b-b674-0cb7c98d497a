package com.ctrip.tour.business.dashboard.sightArchives.dao.marketDao;

import com.ctrip.soa._24922.LocationHeatForecastTrendLineItem;
import com.ctrip.soa._27181.PreparedParameterBean;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.TktStarRocksDao;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.sql.Types;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Repository
public class AdmOrdTtdViewspotArchivesFhttSaleSummaryDfDao {

    @Autowired
    private TktStarRocksDao tktStarRocksDao;

    //机酒火出行热度   数仓侧设计文档：http://conf.ctripcorp.com/pages/viewpage.action?pageId=3668691112
    //dw_ticketdb.adm_ord_ttd_viewspot_archives_fhtt_sale_summary_df


    public List<Map<String,Object>> queryHistoryHeatList(String queryD, String areaName, Integer areaType, String startDate, String endDate) {

        StringBuilder sql = new StringBuilder(
                "select "
                        +"use_date,"
                        + "sum(flt_suc_qty) as flt_suc_qty,"
                        + "sum(htl_suc_qty) as htl_suc_qty,"
                        + "sum(trn_suc_qty) as trn_suc_qty"
                + " from adm_ord_ttd_viewspot_archives_fhtt_sale_summary_df");
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendAreaName(parameters, sql, areaName, areaType);
        appendQueryD(parameters, sql, queryD);

        sql.append(" and use_date between ? and ? ");
        parameters.add(new PreparedParameterBean(startDate, Types.VARCHAR));
        parameters.add(new PreparedParameterBean(endDate, Types.VARCHAR));

        sql.append(" group by use_date");


        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(),parameters);
        } catch (SQLException e) {
            log.warn("queryDeliveryPageList error", e);
        }
        if(CollectionUtils.isEmpty(result)){
            return new ArrayList<>();
        }

        return result;

    }

    private void appendAreaName(List<PreparedParameterBean> parameters, StringBuilder sql, String areaName, Integer areaType) {

        sql.append(" where ");
        if(areaType == 1) {
            sql.append(" city_name = ?");
        }else {
            sql.append(" country_name = ?");
        }

        parameters.add(new PreparedParameterBean(areaName, Types.VARCHAR));
    }


    private void appendQueryD(List<PreparedParameterBean> parameters, StringBuilder sql, String queryD){
        sql.append(" and d = ?");
        parameters.add(new PreparedParameterBean(queryD, Types.VARCHAR));
    }

    private void appendDateRange(List<PreparedParameterBean> parameters, StringBuilder sql, String startDate, String endDate) {
        sql.append(" and use_date between ? and ? ");
        parameters.add(new PreparedParameterBean(startDate, Types.VARCHAR));
        parameters.add(new PreparedParameterBean(endDate, Types.VARCHAR));
    }




    public List<LocationHeatForecastTrendLineItem> queryForecastHeatList(String queryD,String areaName, Integer areaType, String startDate, String endDate) {
        List<LocationHeatForecastTrendLineItem> res = new ArrayList<>();
        List<String> timeArrayList = DateUtil.generateDateRange(startDate, endDate);
        //遍历每一天
        for(String today:timeArrayList) {
            //today与startDate之间的差值，today是计算的日期
            //order_date<startDate的记录 并且use_date==计算的日期
            StringBuilder sql = new StringBuilder(
                    "select use_date,"
                            + " sum(flt_suc_qty) as flt_suc_qty,"
                            + " sum(htl_suc_qty) as htl_suc_qty,"
                            + " sum(trn_suc_qty) as trn_suc_qty"
                            + " from adm_ord_ttd_viewspot_archives_fhtt_sale_summary_df");
            List<PreparedParameterBean> parameters = new ArrayList<>();
            appendAreaName(parameters, sql, areaName, areaType);
            appendQueryD(parameters, sql, queryD);
            sql.append(" and order_date < ? and use_date = ? ");
            parameters.add(new PreparedParameterBean(startDate, Types.VARCHAR));
            parameters.add(new PreparedParameterBean(today, Types.VARCHAR));
            sql.append(" group by use_date");
            List<Map<String, Object>> result = null;//order_date<startDate的记录 并且use_date==计算的日期   今年
            try {
                result = tktStarRocksDao.getListResultNew(sql.toString(), parameters);
            } catch (SQLException e) {
                log.warn("queryDeliveryPageList error", e);
            }
            if (CollectionUtils.isEmpty(result)) {
                continue;
            }
            Map<String, Object> thisYearWithLimit = result.get(0);


            //计算去年的时间 使用日期同一天
            StringBuilder sqlOfLastYear = new StringBuilder(
                    "select date_lastyear from "
                            + "v_dim_date where date_solar = ?");
            List<PreparedParameterBean> parametersOfLastYear = new ArrayList<>();
            parametersOfLastYear.add(new PreparedParameterBean(today, Types.VARCHAR));
            String lastYear = null;
            try {
                List<Map<String, Object>> lastYearResult = tktStarRocksDao.getListResultNew(sqlOfLastYear.toString(), parametersOfLastYear);
                if (CollectionUtils.isNotEmpty(lastYearResult)) {
                    lastYear = String.valueOf(lastYearResult.get(0).get("date_lastyear"));
                }
            } catch (SQLException e) {
                log.warn("queryDeliveryPageList error", e);
            }

            //计算去年的Starttime
            String startTimeOfLastYear = calculateStartTimeOfLastYear(today, startDate, lastYear);

            //计算去年的order_date<startTimeOfLastYear的记录 并且use_date==lastYear
            StringBuilder sqlOfLastYear2 = new StringBuilder(
                    "select use_date,"
                            + " sum(flt_suc_qty) as flt_suc_qty,"
                            + " sum(htl_suc_qty) as htl_suc_qty,"
                            + " sum(trn_suc_qty) as trn_suc_qty"
                            + " from adm_ord_ttd_viewspot_archives_fhtt_sale_summary_df");
            List<PreparedParameterBean> parametersOfLastYear2 = new ArrayList<>();
            appendAreaName(parametersOfLastYear2, sqlOfLastYear2, areaName, areaType);
            sqlOfLastYear2.append(" and order_date < ? and use_date = ? ");
            parametersOfLastYear2.add(new PreparedParameterBean(startTimeOfLastYear, Types.VARCHAR));
            parametersOfLastYear2.add(new PreparedParameterBean(lastYear, Types.VARCHAR));
            sqlOfLastYear2.append(" group by use_date");
            List<Map<String, Object>> lastYearResult = null;//order_date<startTimeOfLastYear的记录 并且use_date==lastYear   去年
            try {
                lastYearResult = tktStarRocksDao.getListResultNew(sqlOfLastYear2.toString(), parametersOfLastYear2);
            } catch (SQLException e) {
                log.warn("queryDeliveryPageList error", e);
            }
            if (CollectionUtils.isEmpty(lastYearResult)) {
                continue;
            }
            Map<String, Object> lastYearWithLimit = lastYearResult.get(0);

            //计算年的use_date==lastYear  不限制order_date
            StringBuilder sqlOfLastYearNoLimit = new StringBuilder(
                    "select use_date,"
                            + " sum(flt_suc_qty) as flt_suc_qty,"
                            + " sum(htl_suc_qty) as htl_suc_qty,"
                            + " sum(trn_suc_qty) as trn_suc_qty"
                            + " from adm_ord_ttd_viewspot_archives_fhtt_sale_summary_df");
            List<PreparedParameterBean> parametersOfLastYearNoLimit = new ArrayList<>();
            appendAreaName(parametersOfLastYearNoLimit, sqlOfLastYearNoLimit, areaName, areaType);
            sqlOfLastYearNoLimit.append(" and use_date = ? ");
            parametersOfLastYearNoLimit.add(new PreparedParameterBean(lastYear, Types.VARCHAR));
            sqlOfLastYearNoLimit.append(" group by use_date");
            List<Map<String, Object>> lastYearResultNoLimit = null;//use_date==lastYear  不限制order_date
            try {
                lastYearResultNoLimit = tktStarRocksDao.getListResultNew(sqlOfLastYearNoLimit.toString(), parametersOfLastYearNoLimit);
            } catch (SQLException e) {
                log.warn("queryDeliveryPageList error", e);
            }
            if (CollectionUtils.isEmpty(lastYearResultNoLimit)) {
                continue;
            }
            Map<String, Object> lastYearNoLimit = lastYearResultNoLimit.get(0);

            //计算预测值
            LocationHeatForecastTrendLineItem locationHeatForecastTrendLineItem = new LocationHeatForecastTrendLineItem();
            locationHeatForecastTrendLineItem.setDate(today);
            if(Double.compare((Double) lastYearWithLimit.get("flt_suc_qty"),0)>0) {
                locationHeatForecastTrendLineItem.setAirplaneTicketHeat((Double) lastYearNoLimit.get("flt_suc_qty") * (Double) thisYearWithLimit.get("flt_suc_qty") / (Double) lastYearWithLimit.get("flt_suc_qty"));//预测值
            } else {
                locationHeatForecastTrendLineItem.setAirplaneTicketHeat(0.0);
            }
            if (Double.compare((Double) lastYearWithLimit.get("htl_suc_qty"),0)>0) {
                locationHeatForecastTrendLineItem.setHotelTicketHeat((Double) lastYearNoLimit.get("htl_suc_qty") * (Double) thisYearWithLimit.get("htl_suc_qty") / (Double) lastYearWithLimit.get("htl_suc_qty"));//预测值
            } else {
                locationHeatForecastTrendLineItem.setHotelTicketHeat(0.0);
            }
            if(Double.compare((Double) lastYearWithLimit.get("trn_suc_qty"),0)>0) {
                locationHeatForecastTrendLineItem.setTrainTicketHeat((Double) lastYearNoLimit.get("trn_suc_qty") * (Double) thisYearWithLimit.get("trn_suc_qty") / (Double) lastYearWithLimit.get("trn_suc_qty"));//预测值
            }else {
                locationHeatForecastTrendLineItem.setTrainTicketHeat(0.0);
            }
            try {
                if(Double.compare((Double)thisYearWithLimit.getOrDefault("htl_suc_qty",0.0)+(Double) thisYearWithLimit.getOrDefault("flt_suc_qty",0.0),0.0)>0){
                    Double temp =((Double) lastYearNoLimit.getOrDefault("htl_suc_qty",0.0)+(Double) lastYearNoLimit.getOrDefault("flt_suc_qty",0.0) )/((Double)lastYearWithLimit.getOrDefault("htl_suc_qty",0.0)+(Double) lastYearWithLimit.getOrDefault("flt_suc_qty",0.0));
                    locationHeatForecastTrendLineItem.setTrainTicketHeat((Double) thisYearWithLimit.get("trn_suc_qty") * temp);//预测值
                }
            }catch (Exception e){
                locationHeatForecastTrendLineItem.setTrainTicketHeat(0.0);
            }

            locationHeatForecastTrendLineItem.setCityHeat(locationHeatForecastTrendLineItem.getAirplaneTicketHeat() + locationHeatForecastTrendLineItem.getHotelTicketHeat() + locationHeatForecastTrendLineItem.getTrainTicketHeat());
            if (Double.compare((Double) lastYearNoLimit.get("flt_suc_qty") + (Double) lastYearNoLimit.get("htl_suc_qty"),0)>0) {
                Double lastYearCityHeat = (Double) lastYearNoLimit.get("flt_suc_qty") + (Double) lastYearNoLimit.get("htl_suc_qty");
                Double thisYearCityHeat = locationHeatForecastTrendLineItem.getAirplaneTicketHeat()+locationHeatForecastTrendLineItem.getHotelTicketHeat();
                locationHeatForecastTrendLineItem.setCityHeatYoy(
                        (thisYearCityHeat - lastYearCityHeat) / lastYearCityHeat
                );//预测值
            } else {
                locationHeatForecastTrendLineItem.setCityHeatYoy(0.0);
            }
            res.add(locationHeatForecastTrendLineItem);
        }

        return res;
    }

    public List<Map<String, Object>> queryHistoryHeatListPop(String queryD,String areaName, int areaType, String startDate, String endDate) {
        StringBuilder sql = new StringBuilder(
                "select "
                        +" t2.date_solar as use_date,"
                        + " sum(flt_suc_qty) as flt_suc_qty_pop,"
                        + " sum(htl_suc_qty) as htl_suc_qty_pop,"
                        + " sum(trn_suc_qty) as trn_suc_qty_pop"
                        + " from adm_ord_ttd_viewspot_archives_fhtt_sale_summary_df t1 inner join v_dim_date t2 on t1.use_date = t2.date_lastyear ");
        List<PreparedParameterBean> parameters = new ArrayList<>();
        appendAreaName(parameters, sql, areaName, areaType);
        sql.append(" and t1.d = ?");
        parameters.add(new PreparedParameterBean(queryD, Types.VARCHAR));
        sql.append(" and t2.date_solar between ? and ? ");
        parameters.add(new PreparedParameterBean(startDate, Types.VARCHAR));
        parameters.add(new PreparedParameterBean(endDate, Types.VARCHAR));
        sql.append(" group by t2.date_solar");


        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(),parameters);
        } catch (SQLException e) {
            log.warn("queryDeliveryPageList error", e);
        }
        if(CollectionUtils.isEmpty(result)){
            return new ArrayList<>();
        }

        return result;
    }

    public static String calculateStartTimeOfLastYear(String today, String startDate, String lastYear) {
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 将字符串转换为 LocalDate
        LocalDate todayDate = LocalDate.parse(today, formatter);
        LocalDate startDateDate = LocalDate.parse(startDate, formatter);
        LocalDate lastYearDate = LocalDate.parse(lastYear, formatter);

        // 计算 today 和 startDate 之间的天数间隔
        long daysBetween = ChronoUnit.DAYS.between(startDateDate, todayDate);

        // 计算 startTimeOfLastYear
        LocalDate startTimeOfLastYearDate = lastYearDate.minusDays(daysBetween);

        // 返回结果
        return startTimeOfLastYearDate.format(formatter);
    }
}
