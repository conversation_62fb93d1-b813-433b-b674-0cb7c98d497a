package com.ctrip.tour.business.dashboard.tktBusiness.biz.impl;

import com.ctrip.soa._24922.GetUserInfoRequestType;
import com.ctrip.soa._24922.GetUserInfoResponseType;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.UserInfoBiz;
import com.ctrip.tour.business.dashboard.utils.UserUtil;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class UserInfoBizImpl implements UserInfoBiz {

    @Override
    public GetUserInfoResponseType getUserInfo(GetUserInfoRequestType getUserInfoRequestType) throws Exception {

        Map<String,String> userInfoMap = new HashMap<>();
        GetUserInfoResponseType response = new GetUserInfoResponseType();
        response.setUserInfoMap(userInfoMap);

        String appName = UserUtil.getAppName();
        if("dashboardpc".equals(appName)){
            return response;
        }


        String empCode = UserUtil.getEmpCode();
        String empName = UserUtil.getEmpName();
        userInfoMap.put("empCode",empCode);
        userInfoMap.put("empName",empName);

        return response;
    }
}
