package com.ctrip.tour.business.dashboard.helper

import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig
import com.ctrip.tour.business.dashboard.tktBusiness.helper.Bus1And2DayTourHelper
import spock.lang.Specification
import spock.lang.Unroll

class Bus1And2DayTourHelperTest extends Specification {

    def remoteConfig = Mock(RemoteConfig)

    def setup() {
    }

    @Unroll
    def "获取可下钻维度数据接口 输入的odtLevel为 #odtLevel odtRegionList #odtRegionList"() {
        given: "设定请求参数"
        def metricInfoBean = new MetricInfoBean(odtLevel: odtLevel, odtRegionList: odtRegionList, overseaOdtLevel: odtLevel, overseaOdtRegionList: odtRegionList)

        and: "mock qconfig配置信息"
        remoteConfig.getConfigValue("domestic") >> "国内"
        remoteConfig.getConfigValue("region") >> "大区"

        when:
        def resultList = Bus1And2DayTourHelper.getDrillDownFieldList(metricInfoBean, remoteConfig, subMetric)

        then: "验证多种返回结果"
        resultList == actualResultList

        where: "用表格方式验证多种返回结果"
        odtLevel | odtRegionList    | subMetric         | actualResultList
        "国内"   | []               | "overseaDayTour"  | ["region_name", "province_name", "examinee"]
        "国内"   | []               | "domesticDayTour" | ["region_name", "province_name", "examinee"]
        "大区"   | ["华东大区"]     | "domesticDayTour" | ["province_name", "examinee"]
        "省份"   | ["江苏", "上海"] | "domesticDayTour" | ["province_name", "examinee"]
        "省份"   | ["江苏"]         | "domesticDayTour" | ["examinee"]
    }
}
