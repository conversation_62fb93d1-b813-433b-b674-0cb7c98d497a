package com.ctrip.tour.business.dashboard.sightArchives.dao.salesDao;

import com.ctrip.soa._27181.PreparedParameterBean;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.TktStarRocksDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.sql.Types;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Repository
@Slf4j
public class AdmPrmVacViewspotDistributionProvinceDetailDfDao {

    //投放地分布表 数仓侧设计文档:http://conf.ctripcorp.com/pages/viewpage.action?pageId=3669341013
    //数据源表: dw_diydb.adm_prm_vac_viewspot_distribution_province_detail_df

    @Autowired
    private TktStarRocksDao tktStarRocksDao;

    public List<String> queryDeliveryProvinceList(String queryD,Long sightId, Boolean needSubSight) {
        //省份分布
        StringBuilder sql = new StringBuilder("select distinct locate as locate from adm_prm_vac_viewspot_distribution_province_detail_df ");
        List<PreparedParameterBean> parameters = new ArrayList<>();

        appendSightId(parameters, sql, sightId, needSubSight);
        appendQueryD(parameters, sql, queryD);
        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(),parameters);
        } catch (SQLException e) {
            log.warn("querySalesMetricCard error", e);
        }
        if(CollectionUtils.isEmpty(result)) {
            return new ArrayList<>();
        }else {
            return result.stream().map(map -> (String) map.getOrDefault("locate", "")).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        }

    }

    //拼景点id
    private void appendSightId(List<PreparedParameterBean> parameters, StringBuilder sql, Long sightId, Boolean needSubSight){
        if(needSubSight){
            sql.append(" where parent_viewspotid = ?");
        }else {
            sql.append(" where viewspot_id = ?");
        }
        parameters.add(new PreparedParameterBean(String.valueOf(sightId), Types.BIGINT));
    }

    private void appendQueryD(List<PreparedParameterBean> parameters, StringBuilder sql, String queryD){
        sql.append(" and d = ?");
        parameters.add(new PreparedParameterBean(queryD, Types.VARCHAR));
    }

}
