package com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;

@Entity
@Getter
@Setter
public class DimOrdTtdDestinationTargetBO {
    //业务大区
    private String businessRegionName;
    //业务子区域
    private String businessSubRegionName;
    //考核年
    private String examineYear;
    //C/T站
    private String CT;
    //业务线
    private String buType;
    //考核指标类型
    private String examineMetricType;
    private String q1;
    private String q2;
    private String q3;
    private String q4;

}
