package com.ctrip.tour.business.dashboard.tktBusiness.bean;

import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
@Getter
@Setter
public class OverseaMetricInfoBeanV3 {

    //邮箱前缀
    private String domainName;
    //指标
    private String metric;
    //目的地考核层级
    private String destinationLevel;
    //目的地考核范围
    private List<String> destinationRangeList;
    //站点考核范围
    private List<String> siteRangeList;
    //渠道考核范围
    private List<String> channelRangeList;

}
