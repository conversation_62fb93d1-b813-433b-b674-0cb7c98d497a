package com.ctrip.tour.business.dashboard.tktBusiness.configuration;

import com.ctrip.platform.dal.dao.annotation.EnableDalTransaction;
import com.ctrip.soa._27181.BIBaseReportQueryServiceClient;
import com.ctrip.tour.rights.client.TourRightsServiceClient;
import com.ctrip.tour.ttd.product.soa.TtdProductBasicServiceClient;
import com.ctrip.tour.vbkProviderSvc.service.VbkProviderServiceClient;
import com.ctrip.tour.vendor.providersvc.soa.v1.service.VendorProviderServiceClient;
import com.ctrip.tour.vendor.usersvc.soa.v1.service.VendorUserServiceClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import qunar.tc.qmq.consumer.idempotent.CRedisIdempotentChecker;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 配置类目前作用
 * 1.初始化第三方client
 * 2.初始化线程池
 * 3.声明需要支持mysql事务
 *
 * <AUTHOR>
 * @date 2022/7/16
 */

@Configuration
@EnableDalTransaction
public class AppConfiguration {

    @Autowired
    private RemoteConfig remoteConfig;

    //度假公共权限校验 获取登录信息
    @Bean
    public VendorUserServiceClient vendorUserServiceClient() {
        return VendorUserServiceClient.getInstance();
    }

    // 奖惩系统获取奖惩数据
    @Bean
    public VendorProviderServiceClient vendorProviderServiceClient(){
        return VendorProviderServiceClient.getInstance();
    }

    // 奖惩系统获取供应商信息
    @Bean
    public VbkProviderServiceClient vbkProviderServiceClient(){
        return VbkProviderServiceClient.getInstance();
    }


    @Bean(name = "redisIdempotent")
    public CRedisIdempotentChecker redisIdempotent() {
        return new CRedisIdempotentChecker(remoteConfig.getConfigValue("profileRedisName"));
    }

    @Bean
    public TtdProductBasicServiceClient ttdProductBasicServiceClient(){
        return TtdProductBasicServiceClient.getInstance();
    }

    //报表基础查询服务
    @Bean
    public BIBaseReportQueryServiceClient biBaseReportQueryServiceClient(){
        return BIBaseReportQueryServiceClient.getInstance();
    }

    //度假权益服务
    @Bean
    public TourRightsServiceClient tourRightsServiceClient(){
        return TourRightsServiceClient.getInstance();
    }

    @Bean(name = "metricCardExecutor")
    public ThreadPoolExecutor getMetricCardExecutor() {
        return new ThreadPoolExecutor(
                20,
                200,
                60,
                TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(200),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }


    @Bean(name = "trendLineExecutor")
    public ThreadPoolExecutor getTrendlineExecutor() {

        return new ThreadPoolExecutor(
                20,
                200,
                60,
                TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(200),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }

    @Bean(name = "subMetricCardExecutor")
    public ThreadPoolExecutor getSubMetricCardExecutor(){
        return new ThreadPoolExecutor(
                20,
                200,
                60,
                TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(200),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }


    @Bean(name = "taskFlowMetricCardExecutor")
    public ThreadPoolExecutor getTaskFlowMetricCardExecutor() {
        return new ThreadPoolExecutor(
                20,
                40,
                60,
                TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(512),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }

    @Bean(name = "sightArchivesExecutor")
    public ThreadPoolExecutor getSightArchivesExecutor() {
        return new ThreadPoolExecutor(
                20,
                40,
                60,
                TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(512),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }

}
