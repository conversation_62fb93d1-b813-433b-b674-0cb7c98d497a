package com.ctrip.tour.business.dashboard.tktBusiness.strategy.impl;

import com.ctrip.soa._24922.*;
import com.ctrip.soa._27181.BIBaseReportQueryServiceClient;
import com.ctrip.soa._27181.GetRawDataRequestType;
import com.ctrip.soa._27181.GetRawDataResponseType;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.*;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.SinglePeriodTrendLineBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.bo.ExamineConfigBo;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardExamineeConfigV2Dao;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.*;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.MetricCalStrategy;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;


/**
 * @auther：yyang25
 */
@Component
public class Bus12MetricStrategy implements MetricCalStrategy {

    @Autowired
    private BIBaseReportQueryServiceClient baseReportQueryServiceClient;

    @Autowired
    private BusinessDashboardExamineeConfigV2Dao examineeConfigV2Dao;

    @Autowired
    private SinglePeriodTrendLineBiz singlePeriodTrendLineBiz;

    @Autowired
    private RemoteConfig remoteConfig;

    @Autowired
    private SwitchNewTableHelper switchNewTableHelper;

    @Override
    public Future<MetricDetailInfo> getSingleMetricCardData(String domainName,
                                                            TimeFilter timeFilter,
                                                            MetricInfoBean metricInfoBean,
                                                            String d,
                                                            Boolean needRank) throws Exception {
        MetricDetailInfo metricDetailInfo = new MetricDetailInfo();

        String metric = metricInfoBean.getMetric();
        Map<String, Double> dimMap = new HashMap<>();
        metricDetailInfo.setDimData(dimMap);
        metricDetailInfo.setMetric(metric);

        MetricHelper.setOdtMetricCardDrillDownInfo(metricInfoBean, metricDetailInfo,remoteConfig);

        SqlParamterBean bean = Bus12Helper.getMetricCardSqlBean(timeFilter, null, metricInfoBean, domainName, d);
        Integer addtionalGapDay = Bus12Helper.getAddtionalGapDay(timeFilter, null, d, bean.getAndMap());
        GetRawDataRequestType metricCardReq = bean.convertBeanToRequest(true);
        GetRawDataResponseType metricCardRes = switchNewTableHelper.switchRemoteDatabase(metricCardReq);
        Bus12Helper.processMetricCardData(metricCardRes, dimMap);

//        Integer gapDays = Bus11Helper.getGapDays(timeFilter, null, d);
        Integer gapDays = getGapDays(timeFilter, d);

        Bus12Helper.calMetricCardAverageData(dimMap, gapDays, addtionalGapDay);
        Bus12Helper.makeUpMetricData(dimMap);

        if (needRank) {
            SqlParamterBean rankingBean = Bus12Helper.getRankingSqlBean(domainName, timeFilter, d);
            GetRawDataRequestType rankingReq = rankingBean.convertBeanToRequest(true);
            GetRawDataResponseType rankingRes = switchNewTableHelper.switchRemoteDatabase(rankingReq);
            Bus12Helper.processRankData(rankingRes, metricDetailInfo);
        }

        return new AsyncResult<>(metricDetailInfo);
    }

    public Integer getGapDays(TimeFilter timeFilter,
                              String d) throws Exception {
        SqlParamterBean gapDaysSqlBean = Bus12Helper.getGapDaysSqlBean(timeFilter.getYear(), timeFilter.getDateType(),
                timeFilter.getMonth(), timeFilter.getQuarter(), "12", d);
        GetRawDataRequestType gapDaysReq = gapDaysSqlBean.convertBeanToRequest(true);
        GetRawDataResponseType gapDaysRes = switchNewTableHelper.switchRemoteDatabase(gapDaysReq);
        return MetricHelper.getGapDays(gapDaysRes);
    }

    @Override
    public GetTrendLineDataResponseType getSingleTrendlineData(GetTrendLineDataRequestType request,
                                                               MetricInfoBean metricInfoBean,
                                                               String d) throws Exception {
        String queryType = request.getQueryType();
        if ("trendline".equals(queryType)) {
            return getSingleTrendlineDataWithoutDrillDown(request, metricInfoBean, d);
        } else {
            return getSingleTrendlineDataWithDrillDown(request, metricInfoBean, d);
        }
    }


    private GetTrendLineDataResponseType getSingleTrendlineDataWithoutDrillDown(GetTrendLineDataRequestType request,
                                                                                MetricInfoBean metricInfoBean,
                                                                                String d) throws Exception {
        GetTrendLineDataResponseType response = new GetTrendLineDataResponseType();
        List<TrendLineDetailInfo> trendLineDetailInfoList = new ArrayList<>();
        response.setTrendLineDetailInfoList(trendLineDetailInfoList);


        TimeFilter timeFilter = request.getTimeFilter();
        String domainName = request.getDomainName();

        List<ExamineConfigBean> examineConfigBeanList = MultiPeriodMappingHelper.getMultiPeriodConfigList(timeFilter, d, examineeConfigV2Dao.queryMetricAllConfig(domainName, d, getMetricName()), null);
        List<Future<SinglePeriodDataBean>> futureList = new ArrayList<>();

        for (ExamineConfigBean bean : examineConfigBeanList) {
            ExamineConfigBo bo = new ExamineConfigBo();
            MetricInfoBean innerMetricInfoBean = bo.getMetricInfoBean(bean.getBusinessDashboardExamineeConfigV2(), remoteConfig);
            SqlParamterBean sqlParamterBean = Bus12Helper.getMetricCardSqlBean(null, bean, innerMetricInfoBean, domainName, d);
            GetRawDataRequestType singlePeriodReq = sqlParamterBean.convertBeanToRequest(true);
            futureList.add(singlePeriodTrendLineBiz.getBus12SinglePeriodTrendLineData(request, innerMetricInfoBean, d, singlePeriodReq, bean));

        }

        //转化汇总返回数据
        PeriodDataBean periodDataBean = new PeriodDataBean();
        periodDataBean.setBeanData(futureList);

        //由于该指标要求24年开始生效  可以等价为海外指标
        List<String> timeList = DateUtil.getAllSelectedTime(request.getTimeFilter(), "2024");

        Bus12Helper.processOverallTrendLineData(trendLineDetailInfoList, periodDataBean, timeList);


        return response;
    }


    private GetTrendLineDataResponseType getSingleTrendlineDataWithDrillDown(GetTrendLineDataRequestType request,
                                                                             MetricInfoBean metricInfoBean,
                                                                             String d) throws Exception {
        GetTrendLineDataResponseType response = new GetTrendLineDataResponseType();
        List<TrendLineDetailInfo> trendLineDetailInfoList = new ArrayList<>();
        response.setTrendLineDetailInfoList(trendLineDetailInfoList);

        TimeFilter timeFilter = request.getTimeFilter();
        String domainName = request.getDomainName();

        List<ExamineConfigBean> examineConfigBeanList = MultiPeriodMappingHelper.getMultiPeriodConfigList(timeFilter, d, examineeConfigV2Dao.queryMetricAllConfig(domainName, d, getMetricName()), null);
        List<Future<SinglePeriodDataBean>> futureList = new ArrayList<>();


        for (ExamineConfigBean bean : examineConfigBeanList) {
            ExamineConfigBo bo = new ExamineConfigBo();
            MetricInfoBean innerMetricInfoBean = bo.getMetricInfoBean(bean.getBusinessDashboardExamineeConfigV2(), remoteConfig);
            SqlParamterBean sqlParamterBean = Bus12Helper.getTrendLineWithDrillDownSqlBean(innerMetricInfoBean, d, request.getDrillDownFilter(), bean, remoteConfig);
            GetRawDataRequestType singlePeriodReq = sqlParamterBean.convertBeanToRequest(true);
            futureList.add(singlePeriodTrendLineBiz.getBus12SinglePeriodTrendLineData(request, innerMetricInfoBean, d, singlePeriodReq, bean));

        }

        //转化汇总返回数据
        PeriodDataBean periodDataBean = new PeriodDataBean();
        periodDataBean.setBeanData(futureList);

        //由于该指标要求23年开始生效  可以等价为海外指标
        List<String> timeList = DateUtil.getAllSelectedTime(request.getTimeFilter(), "2024");

        Bus12Helper.processDrilldownTrendLineData(trendLineDetailInfoList, periodDataBean, timeList);


        return response;
    }


    @Override
    public GetTableDataResponseType getSingleTableData(GetTableDataRequestType request,
                                                       MetricInfoBean metricInfoBean,
                                                       String d) throws Exception {
        GetTableDataResponseType response = new GetTableDataResponseType();
        List<TableDataItem> tableDataItemList = new ArrayList<>();
        response.setTableDataItemList(tableDataItemList);

        SqlParamterBean bean = Bus12Helper.getTableSqlBean(request, metricInfoBean, d, remoteConfig);
        Integer addtionalGapDay = Bus12Helper.getAddtionalGapDay(request.getTimeFilter(), null, d, bean.getAndMap());
        GetRawDataRequestType tableReq = bean.convertBeanToRequest(true);
        GetRawDataResponseType tableRes = switchNewTableHelper.switchRemoteDatabase(tableReq);

        Bus10Helper.processTableData(tableRes, tableDataItemList);

//        Integer gapDays = Bus11Helper.getGapDays(request.getTimeFilter(), null, d);
        Integer gapDays = getGapDays(request.getTimeFilter(), d);
        Bus12Helper.calTableAverageData(tableDataItemList, gapDays, addtionalGapDay);
        Bus12Helper.makeUpTableData(tableDataItemList);

        response.setTotalNum(tableRes.getTotalNum());
        return response;
    }

    @Override
    public GetDrillDownBaseInfoResponseType getSingleDrillDownBaseInfo(GetDrillDownBaseInfoRequestType request,
                                                                       MetricInfoBean metricInfoBean,
                                                                       String d) throws Exception {
        GetDrillDownBaseInfoResponseType response = new GetDrillDownBaseInfoResponseType();
        List<FieldDataItem> fieldDataItemList = new ArrayList<>();
        response.setFieldDataItemList(fieldDataItemList);

        Boolean needSearch = request.isNeedSearch();
        if (GeneralUtil.isEmpty(needSearch)) {
            request.setNeedSearch(false);
            needSearch = false;
        }
        List<String> fieldList = new ArrayList<>();
        if (needSearch) {
            fieldList.add(MetricHelper.getDrillDownColumnName(request.getSearchField()));
        } else {
            fieldList.addAll(Bus12Helper.getDrillDownFieldList(metricInfoBean));
            response.setDefaultChosenField(MetricHelper.getDataBaseColumnName(fieldList.get(0)));
        }
        Map<String, Future<GetRawDataResponseType>> fieldMap = new HashMap<>();
        for (String field : fieldList) {
            SqlParamterBean sqlParamterBean = Bus12Helper.getDrillDownBaseInfoSqlBean(field, request, d, metricInfoBean, remoteConfig);
            GetRawDataRequestType fieldRequest = sqlParamterBean.convertBeanToRequest(false);
            fieldMap.put(field, switchNewTableHelper.switchRemoteDatabaseAsync(fieldRequest));
        }

        for (String field : fieldList) {
            Bus12Helper.processDrillDownBaseInfo(field, fieldMap.get(field).get(), fieldDataItemList);
        }

        return response;
    }

    @Override
    public String getMetricName() {
        return "12";
    }
}
