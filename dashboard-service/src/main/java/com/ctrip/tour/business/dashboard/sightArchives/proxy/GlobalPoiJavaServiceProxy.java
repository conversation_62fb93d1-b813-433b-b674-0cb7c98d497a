package com.ctrip.tour.business.dashboard.sightArchives.proxy;


import com.ctrip.gs.globalpoi.soa.contract.*;
import com.ctrip.tour.business.dashboard.utils.MapperUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@Slf4j
public class GlobalPoiJavaServiceProxy {

    GlobalPoiJavaClient globalPoiJavaClient = GlobalPoiJavaClient.getInstance();

    public Map<Long,Long> getGlobalPoiJavaById(List<Long> sightIdList) {
        GetPoiIdsByBusinessIdsAndPoiTypeRequestType getPoiIdRequest = new GetPoiIdsByBusinessIdsAndPoiTypeRequestType();
        getPoiIdRequest.setBusinessIds(new ArrayList<>(new HashSet<>(sightIdList)));
        getPoiIdRequest.setPoiType((short) 3);   //景点的poiType为3

        log.info("getPoiIdsByBusinessIdsAndPoiType,request={}", MapperUtil.obj2Str(getPoiIdRequest));
        GetPoiIdsByBusinessIdsAndPoiTypeResponseType responseType3 = null;
        try {
            responseType3 = globalPoiJavaClient.getPoiIdsByBusinessIdsAndPoiType(getPoiIdRequest);
        } catch (Exception e) {
            log.warn("getPoiIdsByBusinessIdsAndPoiType failed, sightIdList: {}, error: {}", sightIdList, e.getMessage(), e);
        }
        log.info("getPoiIdsByBusinessIdsAndPoiType,response={}", MapperUtil.obj2Str(responseType3));
        Map<Long, Long> map = new HashMap<>();
        if (responseType3!=null && CollectionUtils.isNotEmpty(responseType3.getResult())) {
            List<PoiIdAndBusinessIdMappingDto> poiList3 = responseType3.getResult();
            for (PoiIdAndBusinessIdMappingDto poi : poiList3) {
                if (poi.getBusinessId() != null && poi.getPoiId() != null) {
                    map.put(poi.getBusinessId(), poi.getPoiId());  //businessId为sightId，poiId为globalPoiId
                }
            }
        }

        getPoiIdRequest.setPoiType((short) 66);   //景点玩乐的poiType为66
        log.info("getPoiIdsByBusinessIdsAndPoiType,request={}", MapperUtil.obj2Str(getPoiIdRequest));
        GetPoiIdsByBusinessIdsAndPoiTypeResponseType responseType66 = null;
        try {
            responseType66 = globalPoiJavaClient.getPoiIdsByBusinessIdsAndPoiType(getPoiIdRequest);
        } catch (Exception e) {
            log.warn("getPoiIdsByBusinessIdsAndPoiType failed, sightIdList: {}, error: {}", sightIdList, e.getMessage(), e);
        }
        log.info("getPoiIdsByBusinessIdsAndPoiType,response={}", MapperUtil.obj2Str(responseType66));
        if (responseType66!=null && CollectionUtils.isNotEmpty(responseType66.getResult())) {
            List<PoiIdAndBusinessIdMappingDto> poiList66 = responseType66.getResult();
            for (PoiIdAndBusinessIdMappingDto poi : poiList66) {
                if (poi.getBusinessId() != null && poi.getPoiId() != null && !map.containsKey(poi.getBusinessId())) {
                    map.put(poi.getBusinessId(), poi.getPoiId());  //businessId为sightId，poiId为globalPoiId
                }
            }
        }

        return map;
    }


    public Map<Long,String> getSightNameTranslateResult(List<Long> sightIdList) {
        Map<Long, Long> globalPoiMap = getGlobalPoiJavaById(sightIdList);

        GetPoiDetailRequestType requestType = new GetPoiDetailRequestType();
        requestType.setPoiIds(new ArrayList<>(globalPoiMap.values()));

        GetPoiDetailResponseType responseType;
        try {
            responseType = globalPoiJavaClient.getPoiDetail(requestType);
        } catch (Exception e) {
            log.warn("getGlobalPoiEnglishNameById failed, sightIdList: {}, error: {}", sightIdList, e.getMessage(), e);
            return Collections.emptyMap();
        }
        if(responseType == null || CollectionUtils.isEmpty(responseType.getResult())) {
            log.warn("getGlobalPoiEnglishNameById response is empty, sightIdList: {}", sightIdList);
            return Collections.emptyMap();
        }
        List<PoiDto> poiDtoList = responseType.getResult();
        Map<Long,String> resultMap = new HashMap<>();
        for(PoiDto poiDto : poiDtoList){
            if(poiDto != null && poiDto.getBusinessId() != null && StringUtils.isNotBlank(poiDto.getEName())) {
                resultMap.put(poiDto.getBusinessId(),poiDto.getEName());
            }
        }

        return resultMap;
    }


}
