package com.ctrip.tour.business.dashboard.grpBusiness.domain.process.dsl.diy;

import com.ctrip.soa._24922.CustTourRegionInfo;
import com.ctrip.soa._24922.DSLRequestType;
import com.ctrip.soa._24922.EnumOperators;
import com.ctrip.soa._24922.WhereCondition;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.AbstractPreDSLProcess;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.dsl.DSLUtils;
import com.ctrip.tour.business.dashboard.grpBusiness.service.CustEmpOrgInfoService;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 评级区域
 */
public class DSLProcessAreaPares extends AbstractPreDSLProcess {
    private List<String> area;
    private CustEmpOrgInfoService custEmpOrgInfoService;

    public static AbstractPreDSLProcess getInstance(List<String> area, CustEmpOrgInfoService custEmpOrgInfoService) {
        DSLProcessAreaPares dslProcessLimitSet = new DSLProcessAreaPares();
        dslProcessLimitSet.area = area;
        dslProcessLimitSet.custEmpOrgInfoService = custEmpOrgInfoService;
        return dslProcessLimitSet;
    }

    @Override
    public DSLRequestType process(DSLRequestType dsl, EarlyReturn earlyReturn) {
        // 获取业务线
        WhereCondition businessTypeCondition = DSLUtils.getWhereConditionByFilterName(dsl, "business_line");
        if (businessTypeCondition == null || businessTypeCondition.getFilterValues().isEmpty()) {
            earlyReturn.setStatus(-1);
            return dsl;
        }
        int businessType = Integer.parseInt(businessTypeCondition.getFilterValues().get(0));
        // 非定制游直接返回
        if (businessType != 230) {
            return dsl;
        }
        // 初始化
        if (dsl.getWhereCondition() == null) {
            dsl.setWhereCondition(new WhereCondition());
        }
        if (dsl.getWhereCondition().getSubWhereConditions() == null) {
            dsl.getWhereCondition().setSubWhereConditions(new ArrayList<>());
        }

        WhereCondition areaCondition = new WhereCondition();
        if (area != null && !area.isEmpty()) {
            // 非空直接初始化
            areaCondition.setFilterName("area");
            areaCondition.setOperators(EnumOperators.IN);
            areaCondition.setFilterValues(area);
            dsl.getWhereCondition().getSubWhereConditions().add(areaCondition);
        } else {
            // 空获取有权限的
            List<CustTourRegionInfo> custTourRegionList;
            try {
                custTourRegionList = custEmpOrgInfoService.getCustTourRegionList("");
            } catch (Exception e) {
                earlyReturn.setStatus(-1);
                return dsl;
            }
            if (custTourRegionList == null || custTourRegionList.isEmpty()) {
                earlyReturn.setStatus(-1);
                return dsl;
            }
            List<String> grdAreas = custTourRegionList.stream().flatMap(custTourRegionInfo -> custTourRegionInfo.getAreas().stream()).collect(Collectors.toList());
            areaCondition.setFilterName("area");
            areaCondition.setOperators(EnumOperators.IN);
            areaCondition.setFilterValues(grdAreas);
        }
        // 只有一个区域，看排名
        dsl.getWhereCondition().getSubWhereConditions().add(areaCondition);
        if (areaCondition.getFilterValues().size() == 1) {
            dsl.getIndicators().add("suc_income_rate_rank");
            dsl.getIndicators().add("suc_profit_rate_rank");
//            dsl.getIndicators().remove("suc_income_rate_rank");
//            dsl.getIndicators().remove("suc_profit_rate_rank");
//            dsl.getIndicators().remove("suc_income_target");
//            dsl.getIndicators().remove("suc_profit_target");
//            dsl.getIndicators().remove("suc_income_rate_rank_max");
//            dsl.getIndicators().remove("suc_profit_rate_rank_max");
//            dsl.getIndicators().remove("suc_income_rate");
//            dsl.getIndicators().remove("suc_profit_rate");
        }
        return dsl;
    }
}
