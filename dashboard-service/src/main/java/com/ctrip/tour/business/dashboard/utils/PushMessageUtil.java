package com.ctrip.tour.business.dashboard.utils;

import com.ctrip.tour.business.dashboard.tktBusiness.bean.ContractHyperLinkBean;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

public class PushMessageUtil {
    /**
     * 根据tag和列表数据获取指定的html
     *
     * @param headList
     * @param tag
     * @return
     */
    public static String getTagHtml(List<String> headList, List<ContractHyperLinkBean> hyperLinks, String tag) {
        List<String> heads = new ArrayList<>();
        Map<Integer, ContractHyperLinkBean> hyperMap = hyperLinks.stream()
                .collect(Collectors.toMap(ContractHyperLinkBean::getIndex, // 分组依据
                        ContractHyperLinkBean -> ContractHyperLinkBean, // 分组后的元素处理
                        (v1, v2) -> v1)); // 分组后的Value冲突处理, 选择第一个
        switch (tag){
            case "th":
                for (String h : headList) {
                    heads.add(String.format("<%s style=\"word-wrap: break-word\">%s</%s>", tag, h, tag));
                }
                break;
            case "td":
                for(int i=0; i<headList.size(); i++){
                    String h = headList.get(i);
                    if(hyperMap.containsKey(i)){
                        ContractHyperLinkBean bean = hyperMap.get(i);
                        int type = bean.getType();
                        if(type == 1){
                            List<String> hs = new ArrayList<>(Arrays.asList(h.split("_")));
                            String href = hs.get(bean.getApparentIndex());
                            hs.remove(bean.getApparentIndex());
                            String apparent = StringUtils.join(hs, "_");
                            heads.add(String.format("<%s style=\"word-wrap: break-word\"><a href=\"%s\" target=\"_blank\">%s</a></%s>", tag, href, apparent, tag));
                        }else{
                            heads.add(String.format("<%s style=\"word-wrap: break-word\"><a href=\"%s\" target=\"_blank\">%s</a></%s>", tag, h, bean.getCustom(), tag));
                        }
                    }else{
                        heads.add(String.format("<%s style=\"word-wrap: break-word\">%s</%s>", tag, h, tag));
                    }
                }
                break;
        }
        return String.join("\n", heads);
    }

    /**
     * 根据指定长度的数据获取指定的html
     *
     * @param result
     * @param len
     * @return
     */
    public static String getBody(List<List<String>> result, List<ContractHyperLinkBean> hyperLinks, int len) {
        List<String> trList = new ArrayList<>();
        for (List<String> tds : result.subList(0, len)) {
            trList.add(String.format("<tr>\n%s</tr>", getTagHtml(tds, hyperLinks, "td")));
        }
        return String.join("\n", trList);
    }

    public static boolean verifyLen(String html, int length) {
        if (html.length() > length) {
            return false;
        }
        return true;
    }

    /**
     * 获取推送消息的内容，要求返回html; 只适用于自服务质量
     *
     * @param title：推送消息表格外的标题内容
     * @param result：推送消息表格内的数据
     * @param length：规定返回多少length的内容，且保持返回内容的完整性
     * @return
     */
    public static String getPushMessage(String title, List<List<String>> result, List<String> headList, int length) {
        // 对传入数据按照销量进行降序排列
        List<List<String>> resultSort = result.stream()
                .sorted(Comparator.comparing(x -> Double.valueOf(((List<String>) x).get(((List<String>) x).size() - 1))).reversed()) // 降序排序
                .collect(Collectors.toList());
        return getPushMessageHtml(title, resultSort, headList, length, new ArrayList<>());
    }


    /**
     * 获取推送消息的内容，要求返回html; 通用场景
     * @param title：推送消息表格外的标题内容
     * @param result：推送消息表格内的数据
     * @param length：规定返回多少length的内容，且保持返回内容的完整性
     *              TODO: 这里的长度限制很耗性能；后期技改可以考虑换成限制条数 而不是长度
     * @return
     */
    public static String getPushMessageHtml(String title, List<List<String>> result, List<String> headList, int length, List<ContractHyperLinkBean> hyperLinks) {
        if(hyperLinks == null){
            hyperLinks = new ArrayList<>();
        }
        String formatHtml = "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n  </head>\n<body>\n    <p>%s</p>\n    <table  style=\"border-collapse:collapse; table-layout: fixed;\" border=\"1\" cellspacing=\"0\" width=\"100%%\">\n        <thread>\n%s</thread>\n        <tbody>\n%s</tbody>    </table>\n<p>%s</p></body>\n</html>";
        String head = getTagHtml(headList, hyperLinks,"th");
        String countHtml = String.format("共%s项，全部清单见附件", result.size());   // NOSONAR

        // 获取表头字段的index
        getHyperLinkIndex(headList, hyperLinks);

        // 根据body列表长度递减 验证html长度是否合法
        for (int i = 1; i <= result.size(); i++) {
            // int size = result.size() - i;
            String body = getBody(result, hyperLinks, i);
            String html = String.format(formatHtml, title, head, body, countHtml);

            // 因为是从0开始，所以刚开始就会满足，所以直接continue; 所以不满足的情况下，拿上一步的结果
            if (!verifyLen(html, length)) {
                return String.format(formatHtml, title, head, getBody(result, hyperLinks, i-1), countHtml);
            }
            // 如果已经进行到最后一组判断，还是满足的情况，就直接返回结果；so 该步骤要先经过判断是否满足
            if(i == result.size()){
                return String.format(formatHtml, title, head, body, "");
            }
        }
        return null;
    }

    /**
     * 获取表头字段的index下标
     * @param headList
     * @param hyperLinks
     */
    public static void getHyperLinkIndex(List<String> headList, List<ContractHyperLinkBean> hyperLinks){
        for (ContractHyperLinkBean h : hyperLinks) {
            if(headList.contains(h.getHead())){
                h.setIndex(headList.indexOf(h.getHead()));
            }
        }
    }



    /**
     * 判断生成的html是否超长; 不超长返回true; 超长返回false
     *
     * @param title
     * @param result
     * @param headList
     * @param length
     */
    public static boolean isOverLength(String title, List<List<String>> result, List<String> headList, int length, List<ContractHyperLinkBean> hyperLinks) {
        if(hyperLinks == null){
            hyperLinks = new ArrayList<>();
        }
        String formatHtml = "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n  </head>\n<body>\n    <p>%s</p>\n    <table  style=\"border-collapse:collapse; table-layout: fixed;\" border=\"1\" cellspacing=\"0\" width=\"100%%\">\n        <thread>\n%s</thread>\n        <tbody>\n%s</tbody>    </table>\n</body>\n</html>";
        String head = getTagHtml(headList, hyperLinks, "th");
        String body = getBody(result, hyperLinks, result.size());
        String html = String.format(formatHtml, title, head, body);
        return verifyLen(html, length);
    }

    /*public static void main(String[] args) throws IOException, NoSuchAlgorithmException {
        List<List<String>> result = new ArrayList<>();
        result.add(Arrays.asList("1", "zhangsan", "0.8", "0.7", "120", "2000"));
        result.add(Arrays.asList("2", "lisi", "0.7", "", "110", "11000"));
        result.add(Arrays.asList("2", "lisi", "0.7", "0.9", "110", "10000"));
        result.add(Arrays.asList("2", "lisi", "0.7", "0.9", "110", "0"));

//        List<List<String>> r = result.stream()
//                .sorted(Comparator.comparing(x -> Double.valueOf(((List<String>) x).get(((List<String>) x).size() - 1))).reversed()) // 降序排序
//                .collect(Collectors.toList());
//
//        System.out.println(r);
//
        List<String> headList = Arrays.asList("供应商ID", "供应商名称", "120S回复率", "差评率", "总会话量", "AA月GMV");
//        System.out.println(getPushMessage("您名下供应商在XX月份的考核中低于平台预警考核线标准，已被预警。平台预警线：供应商的120s回复率的预警线为【】，差评率预警线为【】", result, headList, 4000));
        // System.out.println(isOverLength("您名下供应商在XX月份的考核中低于平台预警考核线标准，已被预警。平台预警线：供应商的120s回复率的预警线为【】，差评率预警线为【】", result, headList, 400));
        System.out.println(getPushMessage("title...", result, headList, 4000));

        List<List<String>> result1 = new ArrayList<>();
        result.add(Arrays.asList("1", "probleam1", "product1", "desc1", "2024-01-02", "http://www.baidu.com"));
        result.add(Arrays.asList("2", "probleam2", "product2", "desc2", "2024-03-02", "http://www.baidu.com"));
        result.add(Arrays.asList("2", "probleam3", "product3", "desc3", "2024-04-02", "http://www.baidu.com"));
        result.add(Arrays.asList("2", "probleam4", "product4", "desc4", "2024-02-02", "http://www.baidu.com"));
        String title = "您负责的产品涉嫌违规将被处理，请尽快整改，如有异议，请在最晚整改完成时间前联系供应商及时申诉，逾期不予受理，明细如下：";
        List<String> heads = Arrays.asList("供应商", "违规原因", "产品", "问题描述", "最晚整改完成时间", "申诉链接");
        List<ContractHyperLinkBean> hyperLinks = new ArrayList<>();
        ContractHyperLinkBean b = new ContractHyperLinkBean("产品", 1, 1, 2, "");
        ContractHyperLinkBean b1 = new ContractHyperLinkBean("申诉链接", 1, 2, 0, "点击申诉");
        hyperLinks.add(b);
        hyperLinks.add(b1);
//        System.out.println(getPushMessageHtml(title, result1, heads, 4000, hyperLinks));


    }*/
}
