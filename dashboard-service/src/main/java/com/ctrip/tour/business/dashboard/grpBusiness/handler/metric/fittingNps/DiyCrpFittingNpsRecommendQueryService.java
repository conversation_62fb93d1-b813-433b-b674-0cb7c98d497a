package com.ctrip.tour.business.dashboard.grpBusiness.handler.metric.fittingNps;

import static com.ctrip.tour.business.dashboard.grpBusiness.exception.ExceptionEnum.EMP_CAN_NOT_FIND;

import java.sql.SQLException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import com.ctrip.soa._24922.CustTourRegionInfo;
import com.ctrip.soa._24922.GetGrpMetricDataRequestType;
import com.ctrip.tour.business.dashboard.grpBusiness.annotation.MetricData;
import com.ctrip.tour.business.dashboard.grpBusiness.common.GrpConstant;
import com.ctrip.tour.business.dashboard.grpBusiness.handler.metric.GrpBussinessAbstractMetricService;
import com.ctrip.tour.business.dashboard.grpBusiness.metrics.fittingNps.DiyCprFittingNpsRecommendService;
import com.ctrip.tour.business.dashboard.grpBusiness.metrics.fittingNps.GrpCprFittingNpsRecommendService;
import com.ctrip.tour.business.dashboard.grpBusiness.service.CustEmpOrgInfoService;
import com.ctrip.tour.business.dashboard.grpBusiness.service.HrOrgEmpInfoService;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.ServiceException;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2024/12/11
 */
@Service(value = GrpConstant.DIY_RECOMMENDATION_RATE)
@Slf4j
@MetricData(value = GrpConstant.DIY_RECOMMENDATION_RATE, needMonthOveMonth = true, needWeekOverWeek = true, isRatio = true)
public class DiyCrpFittingNpsRecommendQueryService extends GrpBussinessAbstractMetricService {

    private final static String TOUR_ENDDATE = "tour_enddate";
    private final static String GRADE_REGION_NAME = "grade_region_name";

    @Autowired
    private RemoteConfig remoteConfig;
    @Autowired
    private DiyCprFittingNpsRecommendService diyCprFittingNpsRecommendService;
    @Autowired
    private CustEmpOrgInfoService custEmpOrgInfoService;

    public DiyCrpFittingNpsRecommendQueryService(ApplicationContext ac) {
        super(ac);
    }

    @Override
    protected Map<String, Object> request2Param(GetGrpMetricDataRequestType requestType, int bizMode) {
        Map<String, Object> param = Maps.newHashMap();

        try {

            List<String> grdAreas = requestType.getAreas();
            if (CollectionUtils.isEmpty(requestType.getAreas())) {
                List<CustTourRegionInfo> custTourRegionList = custEmpOrgInfoService.getCustTourRegionList(requestType.getEmpCode());
                grdAreas = custTourRegionList.stream().flatMap(custTourRegionInfo -> custTourRegionInfo.getAreas().stream())
                        .collect(Collectors.toList());
            }
            param.put(GRADE_REGION_NAME, grdAreas);
        } catch (Exception e) {
            log.warn("query empcode error" + requestType.getEmpCode(), e);
            throw new ServiceException(EMP_CAN_NOT_FIND.getCode(),EMP_CAN_NOT_FIND.getMsg());
        }
        param.put(TOUR_ENDDATE, new String[]{requestType.getStartDate(), requestType.getEndDate()});
        return param;
    }



    private String[] calcOverDate(String startDate, String endDate, int offset) {
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate hisStartDate = LocalDate.parse(startDate, dtf).minusDays(offset);
        LocalDate hisEndDate = LocalDate.parse(endDate, dtf).minusDays(offset);
        return new String[]{dtf.format(hisStartDate), dtf.format(hisEndDate)};
    }

    @Override
    protected Map<String, Object> replaceQueryDate4Week(Map<String, Object> param , String startDate, String endDate) {
        param.put(TOUR_ENDDATE, calcOverDate(startDate, endDate, 7));
        return param;
    }

    @Override
    protected Map<String, Object> replaceQueryDate4Month(Map<String, Object> param, String startDate, String endDate) {
        param.put(TOUR_ENDDATE, calcOverDate(startDate, endDate, 30));
        return param;
    }

    @Override
    protected Map<String, Object> replaceQueryDate4Year(Map<String, Object> param, String startDate, String endDate) {
        param.put(TOUR_ENDDATE, calcOverYearDate(startDate, endDate));
        return param;
    }


    @Override
    public Map<String, Object> queryMetricTrendLine(Map<String, Object> param, GetGrpMetricDataRequestType requestType) {

        String aggregationGranularity = requestType.getAggregationGranularity();

        Map<String, Object> result = diyCprFittingNpsRecommendService.queryTrendLineData(diyCprFittingNpsRecommendService, param,
                Lists.newArrayList(TOUR_ENDDATE), aggregationGranularity);

        return result;
    }

    @Override
    public List<Map<String, Object>> queryMetricDillDown(Map<String, Object> param, GetGrpMetricDataRequestType requestType) {

        String drillDownDim = requestType.getDrillDownDim();

        List<Map<String, Object>> result = diyCprFittingNpsRecommendService.queryDillDownData(diyCprFittingNpsRecommendService, param,
                Lists.newArrayList(drillDownDim));

        return result;
    }

    @Override
    public List<Map<String, Object>> queryMetricCardData(Map<String, Object> param, GetGrpMetricDataRequestType requestType) {
        List<Map<String, Object>> result = diyCprFittingNpsRecommendService.queryCardData(diyCprFittingNpsRecommendService, param,
                null);
        return result;
    }


    private List<String> queryAllTargetEmpCodes(String empCode) {
        return null;
    }



}
