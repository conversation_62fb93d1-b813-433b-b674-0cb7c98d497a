package com.ctrip.tour.business.dashboard.biz.impl


import com.ctrip.soa._27181.BIBaseReportQueryServiceClient
import com.ctrip.tour.business.dashboard.tktBusiness.biz.impl.DataUpdateBizImpl
import com.ctrip.tour.business.dashboard.tktBusiness.biz.impl.TaskFlowMetricCardBizImpl
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardEmployeeInfoDao
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardExamineeConfigV2Dao
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardOrganizationInfoDao
import com.ctrip.tour.business.dashboard.utils.MapperUtil
import spock.lang.Specification

class TaskFlowMetricCardBizImplTest extends Specification {
    def employeeInfoDao = Mock(BusinessDashboardEmployeeInfoDao)
    def organizationInfoDao = Mock(BusinessDashboardOrganizationInfoDao)
    def examineConfigV2Dao = Mock(BusinessDashboardExamineeConfigV2Dao)
    def remoteConfig = MapperUtil.str2Obj("{\"configMap\":{\"targetManageUrlPreffix\":\"http://product.activity.fat29.qa.nt.ctripcorp.com/ttd-operation/targetManage\",\"province\":\"省份\",\"universalStudios\":\"环球影城\",\"region\":\"大区\",\"bd\":\"商拓\",\"kaRegion\":\"KA大区\",\"three\":\"三方\",\"destinationT\":\"T站\",\"excludeOrgId\":\"62431\",\"provinceId\":\"-9999|5001|6001|7001|8001\",\"subRegion\":\"子区域\",\"oversea\":\"海外\",\"scenario\":\"tour-bi-business-dashboard-log\",\"childrenOrgIdList\":\"62337|62355|62390|SO001665|62402|SO002565|SO004005|SO002566|SO005325\",\"destinationC\":\"C站\",\"profileRedisName\":\"ttd_bi_profile_redis\",\"viewspot\":\"景点\",\"domestic\":\"国内\",\"parentOrgId\":\"SO001057\"},\"employeeRelationMap\":{\"S68368\":\"D00024\",\"TR019390\":\"D00024\",\"S79530\":\"S35097\",\"S22035\":\"S52754\",\"TR015896\":\"D00024\",\"S68332\":\"D00024\",\"TR009198\":\"S35097\",\"S58662\":\"D00024\",\"S78157\":\"D00024\",\"S28153\":\"S08033\",\"TR021161\":\"D00024\",\"TR015613\":\"S04850\",\"S52246\":\"D00024\",\"TR032346\":\"D03977\",\"S58720\":\"D00024\",\"S79743\":\"S08033\",\"TR018838\":\"S35097\",\"TR019403\":\"S35097\",\"S75483\":\"D00024\",\"S76285\":\"D00024\",\"S20149\":\"S08033\",\"S41524\":\"S08033\",\"S79971\":\"D00024\",\"S74773\":\"S08033\",\"TR013600\":\"S35097\",\"TR003932\":\"S08033\",\"TR032913\":\"S35097\",\"S74975\":\"S08033\",\"TR005148\":\"S35097\",\"hr003\":\"S08033\",\"S78177\":\"D00024\",\"S39497\":\"D00024\"},\"externalConfigMap\":{\"adminInferior\":\"D00024|S52754\",\"adminOrgId\":\"41999\",\"regionRankId\":\"62402|62355|62337|SO002322|62390|SO001665\",\"specailRegionOperate\":\"wsong\",\"overseaDeptId\":\"SO003287\",\"halfDomainName\":\"chang.liu|leisun\",\"admin\":\"S35097\",\"domesticDeptId\":\"SO001057\",\"specialRegionId\":\"62390\"},\"organizationOrderMap\":{\"SO001057\":\"62337|62355|62390|SO001665|62402|SO002322|SO002565|SO004005|SO002566|SO005325\",\"SO002322\":\"SO002326|SO002328|SO002325|SO002323|SO002324\"},\"overseaDrillDownMetadata\":{\"metricMetadataList\":[{\"metricList\":[\"101\",\"102\"],\"subMetricFieldList\":[{\"subMetricList\":[\"destination\",\"destination_c\",\"destination_t\"],\"field\":\"region\",\"baseInfoId\":6,\"baseInfoGroupList\":[\"region_id\",\"region_name\"],\"baseInfoLikeIndexList\":[1],\"needBubble\":true,\"tableDataIdMap\":{\"other\":6,\"target\":1},\"bubbleGroupListMap\":{\"other\":[\"region_id\",\"region_name\"],\"target\":[\"region_id\"]},\"conditionColumn\":\"region_id\",\"needTarget\":true,\"headerFieldList\":[\"region_name\"],\"tableGroupListMap\":{\"other\":[\"region_id\",\"region_name\"],\"target\":[\"region_id\"]},\"showField\":\"region_name\",\"showFieldId\":\"region_id\"},{\"subMetricList\":[\"destination\",\"destination_c\",\"destination_t\"],\"field\":\"province\",\"baseInfoId\":6,\"baseInfoGroupList\":[\"province_id\",\"province_name\"],\"baseInfoLikeIndexList\":[1],\"needBubble\":true,\"tableDataIdMap\":{\"other\":6,\"target\":1},\"bubbleGroupListMap\":{\"other\":[\"province_id\",\"province_name\"],\"target\":[\"province_id\"]},\"conditionColumn\":\"province_id\",\"needTarget\":true,\"headerFieldList\":[\"region_name\",\"province_name\"],\"tableGroupListMap\":{\"other\":[\"region_id\",\"region_name\",\"province_id\",\"province_name\"],\"target\":[\"region_id\",\"province_id\"]},\"showField\":\"province_name\",\"showFieldId\":\"province_id\"},{\"subMetricList\":[\"destination\",\"destination_c\",\"destination_t\"],\"field\":\"country\",\"baseInfoId\":6,\"baseInfoGroupList\":[\"country_id\",\"country_name\"],\"baseInfoLikeIndexList\":[1],\"needBubble\":false,\"tableDataIdMap\":{\"other\":6},\"conditionColumn\":\"country_id\",\"needTarget\":false,\"headerFieldList\":[\"region_name\",\"province_name\",\"country_name\"],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\",\"country_id\",\"country_name\"]}},{\"subMetricList\":[\"destination\",\"destination_c\",\"destination_t\"],\"field\":\"examinee\",\"baseInfoId\":12,\"baseInfoGroupList\":[\"examinee\",\"examinee_display\"],\"baseInfoLikeIndexList\":[1],\"needBubble\":false,\"tableDataIdMap\":{\"2019\":6,\"target\":4,\"lastyear\":6,\"current\":12},\"conditionColumn\":\"examinee\",\"needTarget\":true,\"headerFieldList\":[\"region_name\",\"province_name\",\"examinee_display\"],\"tableGroupListMap\":{\"2019\":[\"region_id\",\"province_id\"],\"target\":[\"region_id\",\"province_id\",\"examinee\"],\"current\":[\"region_name\",\"region_id\",\"province_name\",\"province_id\",\"examinee_display\",\"examinee\"],\"lastyear\":[\"region_id\",\"province_id\"]},\"pagingConditionColumn\":\"province_id\"},{\"subMetricList\":[\"destination\",\"destination_c\",\"destination_t\"],\"field\":\"viewspot\",\"baseInfoId\":10,\"baseInfoGroupList\":[\"viewspot_id\",\"viewspot_name\"],\"baseInfoLikeIndexList\":[0,1],\"needBubble\":false,\"tableDataIdMap\":{\"current\":10,\"other\":11},\"conditionColumn\":\"viewspot_id\",\"needTarget\":false,\"headerFieldList\":[\"region_name\",\"province_name\",\"country_name\",\"viewspot_id\",\"viewspot_name\"],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\",\"country_name\",\"viewspot_id\",\"viewspot_name\"]}},{\"subMetricList\":[\"site\"],\"field\":\"site\",\"baseInfoId\":9,\"baseInfoGroupList\":[\"site_code\",\"site\"],\"baseInfoLikeIndexList\":[1],\"needBubble\":true,\"tableDataIdMap\":{\"other\":9,\"target\":2},\"bubbleGroupListMap\":{\"other\":[\"site_code\",\"site\"],\"target\":[\"site_code\"]},\"conditionColumn\":\"site_code\",\"needTarget\":true,\"headerFieldList\":[\"site\"],\"tableGroupListMap\":{\"other\":[\"site_code\",\"site\"],\"target\":[\"site_code\"]},\"showField\":\"site\",\"showFieldId\":\"site_code\"},{\"subMetricList\":[\"site\"],\"field\":\"locale\",\"baseInfoId\":9,\"baseInfoGroupList\":[\"locale_code\",\"locale\"],\"baseInfoLikeIndexList\":[1],\"needBubble\":false,\"tableDataIdMap\":{\"other\":9},\"conditionColumn\":\"locale_code\",\"needTarget\":false,\"headerFieldList\":[\"site\",\"locale\"],\"tableGroupListMap\":{\"other\":[\"site\",\"locale_code\",\"locale\"]}},{\"subMetricList\":[\"channel\"],\"field\":\"channel\",\"baseInfoId\":9,\"baseInfoGroupList\":[\"channel_code\",\"channel_name\"],\"baseInfoLikeIndexList\":[1],\"needBubble\":true,\"tableDataIdMap\":{\"other\":9,\"target\":3},\"bubbleGroupListMap\":{\"other\":[\"channel_code\",\"channel_name\"],\"target\":[\"channel_code\"]},\"conditionColumn\":\"channel_code\",\"needTarget\":true,\"headerFieldList\":[\"channel_name\"],\"tableGroupListMap\":{\"other\":[\"channel_code\",\"channel_name\"],\"target\":[\"channel_code\"]},\"showField\":\"channel_name\",\"showFieldId\":\"channel_code\"},{\"subMetricList\":[\"channel\"],\"field\":\"site\",\"baseInfoId\":9,\"baseInfoGroupList\":[\"site_code\",\"site\"],\"baseInfoLikeIndexList\":[1],\"needBubble\":false,\"tableDataIdMap\":{\"other\":9},\"conditionColumn\":\"site_code\",\"needTarget\":false,\"headerFieldList\":[\"channel_name\",\"site\"],\"tableGroupListMap\":{\"other\":[\"channel_name\",\"site\",\"site_code\"]}},{\"subMetricList\":[\"channel\"],\"field\":\"locale\",\"baseInfoId\":9,\"baseInfoGroupList\":[\"locale_code\",\"locale\"],\"baseInfoLikeIndexList\":[1],\"needBubble\":false,\"tableDataIdMap\":{\"other\":9},\"conditionColumn\":\"locale_code\",\"needTarget\":false,\"headerFieldList\":[\"channel_name\",\"site\",\"locale\"],\"tableGroupListMap\":{\"other\":[\"channel_name\",\"site\",\"locale_code\",\"locale\"]}}]},{\"metricList\":[\"103\"],\"subMetricFieldList\":[{\"subMetricList\":[\"destination_c\",\"destination_t\"],\"field\":\"region\",\"baseInfoId\":32,\"baseInfoGroupList\":[\"region_id\",\"region_name\"],\"baseInfoLikeIndexList\":[1],\"needLine\":true,\"tableDataIdMap\":{\"other\":32,\"target\":38},\"lineGroupListMap\":{\"current\":[\"region_name\"]},\"conditionColumn\":\"region_id\",\"needTarget\":true,\"headerFieldList\":[\"region_name\"],\"tableGroupListMap\":{\"other\":[\"region_id\",\"region_name\"],\"target\":[\"region_id\"]},\"showField\":\"region_name\",\"showFieldId\":\"region_id\"},{\"subMetricList\":[\"destination_c\",\"destination_t\"],\"field\":\"province\",\"baseInfoId\":32,\"baseInfoGroupList\":[\"province_id\",\"province_name\"],\"baseInfoLikeIndexList\":[1],\"needLine\":true,\"tableDataIdMap\":{\"other\":32,\"target\":38},\"lineGroupListMap\":{\"current\":[\"province_name\"]},\"conditionColumn\":\"province_id\",\"needTarget\":true,\"headerFieldList\":[\"region_name\",\"province_name\"],\"tableGroupListMap\":{\"other\":[\"region_id\",\"region_name\",\"province_id\",\"province_name\"],\"target\":[\"region_id\",\"province_id\"]},\"showField\":\"province_name\",\"showFieldId\":\"province_id\"},{\"subMetricList\":[\"destination_c\",\"destination_t\"],\"field\":\"country\",\"baseInfoId\":32,\"baseInfoGroupList\":[\"country_id\",\"country_name\"],\"baseInfoLikeIndexList\":[1],\"needLine\":false,\"tableDataIdMap\":{\"other\":32},\"conditionColumn\":\"country_id\",\"needTarget\":false,\"headerFieldList\":[\"region_name\",\"province_name\",\"country_name\"],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\",\"country_id\",\"country_name\"]}},{\"subMetricList\":[\"destination_c\",\"destination_t\"],\"field\":\"examinee\",\"baseInfoId\":33,\"baseInfoGroupList\":[\"examinee\",\"examinee_display\"],\"baseInfoLikeIndexList\":[1],\"needLine\":false,\"tableDataIdMap\":{\"other\":33,\"target\":38},\"conditionColumn\":\"examinee\",\"needTarget\":true,\"headerFieldList\":[\"region_name\",\"province_name\",\"examinee_display\"],\"tableGroupListMap\":{\"other\":[\"region_name\",\"region_id\",\"province_name\",\"province_id\",\"examinee_display\",\"examinee\"],\"target\":[\"region_id\",\"province_id\"]}},{\"subMetricList\":[\"destination_c\",\"destination_t\"],\"field\":\"viewspot\",\"baseInfoId\":34,\"baseInfoGroupList\":[\"viewspot_id\",\"viewspot_name\"],\"baseInfoLikeIndexList\":[0,1],\"needLine\":false,\"tableDataIdMap\":{\"other\":34},\"conditionColumn\":\"viewspot_id\",\"needTarget\":false,\"headerFieldList\":[\"region_name\",\"province_name\",\"country_name\",\"viewspot_id\",\"viewspot_name\"],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\",\"country_name\",\"viewspot_id\",\"viewspot_name\"]}},{\"subMetricList\":[\"destination_c\",\"destination_t\"],\"field\":\"vendor\",\"baseInfoId\":35,\"baseInfoGroupList\":[\"vendor_id\",\"vendor_name\"],\"baseInfoLikeIndexList\":[0,1],\"needLine\":false,\"tableDataIdMap\":{\"other\":35},\"conditionColumn\":\"vendor_id\",\"needTarget\":false,\"headerFieldList\":[\"region_name\",\"province_name\",\"vendor_id\",\"vendor_name\"],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\",\"vendor_id\",\"vendor_name\"]}},{\"subMetricList\":[\"site\"],\"field\":\"site\",\"baseInfoId\":37,\"baseInfoGroupList\":[\"site_code\",\"site\"],\"baseInfoLikeIndexList\":[1],\"needLine\":true,\"tableDataIdMap\":{\"other\":37,\"target\":39},\"lineGroupListMap\":{\"current\":[\"site\"]},\"conditionColumn\":\"site_code\",\"needTarget\":true,\"headerFieldList\":[\"site\"],\"tableGroupListMap\":{\"other\":[\"site_code\",\"site\"],\"target\":[\"site_code\"]},\"showField\":\"site\",\"showFieldId\":\"site_code\"},{\"subMetricList\":[\"site\"],\"field\":\"viewspot\",\"baseInfoId\":36,\"baseInfoGroupList\":[\"viewspot_id\",\"viewspot_name\"],\"baseInfoLikeIndexList\":[0,1],\"needLine\":false,\"tableDataIdMap\":{\"other\":36},\"conditionColumn\":\"viewspot_id\",\"needTarget\":false,\"headerFieldList\":[\"site\",\"viewspot_id\",\"viewspot_name\"],\"tableGroupListMap\":{\"other\":[\"site\",\"viewspot_id\",\"viewspot_name\"]}},{\"subMetricList\":[\"site\"],\"field\":\"vendor\",\"baseInfoId\":37,\"baseInfoGroupList\":[\"vendor_id\",\"vendor_name\"],\"baseInfoLikeIndexList\":[0,1],\"needLine\":false,\"tableDataIdMap\":{\"other\":37},\"conditionColumn\":\"vendor_id\",\"needTarget\":false,\"headerFieldList\":[\"site\",\"vendor_id\",\"vendor_name\"],\"tableGroupListMap\":{\"other\":[\"site\",\"vendor_id\",\"vendor_name\"]}}]},{\"metricList\":[\"105\",\"106\",\"107\"],\"subMetricFieldList\":[{\"subMetricList\":[\"tklk\",\"tfly\"],\"field\":\"region\",\"baseInfoGroupList\":[\"region_id\",\"region_name\"],\"baseInfoLikeIndexList\":[1],\"needLine\":true,\"lineGroupListMap\":{\"current\":[\"region_name\"]},\"conditionColumn\":\"region_id\",\"headerFieldList\":[\"region_name\"],\"tableGroupListMap\":{\"other\":[\"region_id\",\"region_name\"]},\"showField\":\"region_name\",\"showFieldId\":\"region_id\"},{\"subMetricList\":[\"tklk\",\"tfly\"],\"field\":\"province\",\"baseInfoGroupList\":[\"province_id\",\"province_name\"],\"baseInfoLikeIndexList\":[1],\"needLine\":true,\"lineGroupListMap\":{\"current\":[\"province_name\"]},\"conditionColumn\":\"province_id\",\"headerFieldList\":[\"region_name\",\"province_name\"],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\"]},\"showField\":\"province_name\",\"showFieldId\":\"province_id\"},{\"subMetricList\":[\"tklk\",\"tfly\"],\"field\":\"country\",\"baseInfoGroupList\":[\"country_id\",\"country_name\"],\"baseInfoLikeIndexList\":[1],\"needLine\":false,\"conditionColumn\":\"country_id\",\"headerFieldList\":[\"region_name\",\"province_name\",\"country_name\"],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\",\"country_name\"]}},{\"subMetricList\":[\"tklk\",\"tfly\"],\"field\":\"examinee\",\"baseInfoGroupList\":[\"examinee_display\",\"examinee\"],\"baseInfoLikeIndexList\":[1],\"needLine\":false,\"conditionColumn\":\"examinee\",\"headerFieldList\":[\"region_name\",\"province_name\",\"examinee_display\"],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\",\"examinee_display\"]}}]}]},\"domesticDrillDownMetadata\":{\"metricMetadataList\":[{\"metricList\":[\"10\"],\"drillDownFieldList\":[{\"field\":\"region_name\",\"baseInfoId\":13,\"baseInfoGroupList\":[\"region_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"firstpage\":[\"region_name\"],\"detailpage\":[\"region_name\"]},\"tableOrderList\":[\"complete_people_rate\"]},{\"field\":\"province_name\",\"baseInfoId\":13,\"baseInfoGroupList\":[\"province_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"firstpage\":[\"province_name\"],\"detailpage\":[\"region_name\",\"province_name\"]},\"tableOrderList\":[\"complete_people_rate\"]},{\"field\":\"examinee\",\"baseInfoId\":15,\"baseInfoGroupList\":[\"examinee\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"firstpage\":[\"examinee\",\"examinee_name\"],\"detailpage\":[\"region_name\",\"province_name\",\"examinee\",\"examinee_name\"]},\"tableOrderList\":[\"complete_people_rate\"]}]},{\"metricList\":[\"1\",\"2\"],\"drillDownFieldList\":[{\"subMetric\":\"domesticDayTour\",\"field\":\"region_name\",\"baseInfoId\":17,\"baseInfoGroupList\":[\"region_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"other\":[\"region_name\"]},\"tableDataIdMap\":{\"current\":17,\"other\":18,\"target\":19},\"bubbleGroupListMap\":{\"other\":[\"region_name\"]}},{\"subMetric\":\"domesticDayTour\",\"field\":\"province_name\",\"baseInfoId\":17,\"baseInfoGroupList\":[\"province_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\"]},\"tableDataIdMap\":{\"current\":17,\"other\":18,\"target\":19},\"bubbleGroupListMap\":{\"other\":[\"province_name\"]}},{\"subMetric\":\"domesticDayTour\",\"field\":\"examinee\",\"baseInfoId\":21,\"baseInfoGroupList\":[\"examinee\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"current\":[\"region_name\",\"province_name\",\"examinee_name\",\"examinee\"],\"other\":[\"examinee\"]},\"tableDataIdMap\":{\"current\":21,\"other\":22,\"target\":23},\"bubbleGroupListMap\":{\"other\":[\"examinee\"]}},{\"subMetric\":\"overseaDayTour\",\"field\":\"region_name\",\"baseInfoId\":42,\"baseInfoGroupList\":[\"region_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"other\":[\"region_name\"]},\"tableDataIdMap\":{\"current\":42,\"other\":43,\"target\":44},\"bubbleGroupListMap\":{\"other\":[\"region_name\"]}},{\"subMetric\":\"overseaDayTour\",\"field\":\"province_name\",\"baseInfoId\":42,\"baseInfoGroupList\":[\"province_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\"]},\"tableDataIdMap\":{\"current\":42,\"other\":43,\"target\":44},\"bubbleGroupListMap\":{\"other\":[\"province_name\"]}},{\"subMetric\":\"overseaDayTour\",\"field\":\"examinee\",\"baseInfoId\":45,\"baseInfoGroupList\":[\"examinee\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"current\":[\"region_name\",\"province_name\",\"examinee_name\",\"examinee\"],\"other\":[\"examinee\"]},\"tableDataIdMap\":{\"current\":45,\"other\":46,\"target\":47},\"bubbleGroupListMap\":{\"other\":[\"examinee\"]}},{\"subMetric\":\"ticketActivity+domesticDayTour\",\"field\":\"region_name\",\"baseInfoId\":24,\"baseInfoGroupList\":[\"region_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"other\":[\"region_name\"]},\"tableDataIdMap\":{\"current\":24,\"other\":25,\"target\":26},\"bubbleGroupListMap\":{\"other\":[\"region_name\"]}},{\"subMetric\":\"ticketActivity+domesticDayTour\",\"field\":\"province_name\",\"baseInfoId\":24,\"baseInfoGroupList\":[\"province_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\"]},\"tableDataIdMap\":{\"current\":24,\"other\":25,\"target\":26},\"bubbleGroupListMap\":{\"other\":[\"province_name\"]}},{\"subMetric\":\"ticketActivity+domesticDayTour\",\"field\":\"examinee\",\"baseInfoId\":54,\"baseInfoGroupList\":[\"examinee\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"current\":[\"region_name\",\"ttd_province_name\",\"odt_province_name\",\"examinee_name\",\"examinee\"],\"other\":[\"examinee\"]},\"tableDataIdMap\":{\"currentmonth\":54,\"currentquarter\":55,\"otherttd\":31,\"otherodt\":22},\"bdSpecificCondition\":\"ttd_province_name|odt_province_name\",\"bdSpecificList\":[\"ttd\",\"odt\"]},{\"subMetric\":\"ticketActivity+domesticDayTour+overseaDayTour\",\"field\":\"region_name\",\"baseInfoId\":49,\"baseInfoGroupList\":[\"region_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"other\":[\"region_name\"]},\"tableDataIdMap\":{\"current\":49,\"other\":50,\"target\":51},\"bubbleGroupListMap\":{\"other\":[\"region_name\"]}},{\"subMetric\":\"ticketActivity+domesticDayTour+overseaDayTour\",\"field\":\"province_name\",\"baseInfoId\":49,\"baseInfoGroupList\":[\"province_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"other\":[\"region_name\",\"province_name\"]},\"tableDataIdMap\":{\"current\":49,\"other\":50,\"target\":51},\"bubbleGroupListMap\":{\"other\":[\"province_name\"]}},{\"subMetric\":\"ticketActivity+domesticDayTour+overseaDayTour\",\"field\":\"examinee\",\"baseInfoId\":56,\"baseInfoGroupList\":[\"examinee\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"current\":[\"region_name\",\"ttd_province_name\",\"odt_province_name\",\"odt_ob_province_name\",\"examinee_name\",\"examinee\"],\"other\":[\"examinee\"]},\"tableDataIdMap\":{\"currentmonth\":56,\"currentquarter\":57,\"otherttd\":31,\"otherodt\":22,\"otherodtob\":46},\"bdSpecificCondition\":\"ttd_province_name|odt_province_name|odt_ob_province_name\",\"bdSpecificList\":[\"ttd\",\"odt\",\"odtob\"]}]},{\"metricList\":[\"11\"],\"drillDownFieldList\":[{\"field\":\"region_name\",\"baseInfoId\":27,\"baseInfoGroupList\":[\"region_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"firstpage\":[\"region_name\"],\"detailpage\":[\"region_name\"]}},{\"field\":\"province_name\",\"baseInfoId\":27,\"baseInfoGroupList\":[\"province_name\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"firstpage\":[\"province_name\"],\"detailpage\":[\"region_name\",\"province_name\"]}},{\"field\":\"examinee\",\"baseInfoId\":27,\"baseInfoGroupList\":[\"examinee\"],\"baseInfoLikeIndexList\":[0],\"tableGroupListMap\":{\"firstpage\":[\"province_name\"],\"detailpage\":[\"region_name\",\"province_name\",\"examinee_name\",\"examinee\"]}}]}]},\"domesticExamineTypeConfigData\":{\"mergedSubMetricDataList\":[[\"ticketActivity\",\"domesticDayTour\",\"overseaDayTour\"],[\"ticketActivity\",\"domesticDayTour\"]],\"examineTypeMetadataList\":[{\"examineTypeList\":[1,2,3],\"subMetricList\":[\"ticketActivity\"]},{\"examineTypeList\":[4,5,6],\"subMetricList\":[\"ticketActivity\",\"domesticDayTour\"]},{\"examineTypeList\":[7],\"subMetricList\":[\"domesticDayTour\"]},{\"examineTypeList\":[8],\"subMetricList\":[\"overseaDayTour\"]},{\"examineTypeList\":[9,10,12],\"subMetricList\":[\"ticketActivity\",\"overseaDayTour\"]},{\"examineTypeList\":[11],\"subMetricList\":[\"domesticDayTour\",\"overseaDayTour\"]},{\"examineTypeList\":[13,14,15],\"subMetricList\":[\"ticketActivity\",\"domesticDayTour\",\"overseaDayTour\"]}]}}", RemoteConfig.class)
    def dataUpdateBiz = Mock(DataUpdateBizImpl)
    def client = Mock(BIBaseReportQueryServiceClient)

    def taskFlowMetricCardBizImpl = new TaskFlowMetricCardBizImpl(
            employeeInfoDao: employeeInfoDao,
            organizationInfoDao: organizationInfoDao,
            examineConfigV2Dao: examineConfigV2Dao,
            remoteConfig: remoteConfig,
            dataUpdateBiz: dataUpdateBiz,
            client: client
    )


//    def "GetTaskFlowMetricCardData"() {
//        given: "mock request"
//        def request = MapperUtil.str2Obj("{\"dateType\":\"current_quarter\",\"taskDimInfoList\":[{\"type\":\"biz_category_code\",\"value\":\"\",\"code\":\"JIANGHULING\"},{\"type\":\"biz_category_code\",\"value\":\"\",\"code\":\"BOARD\"},{\"type\":\"task_collection_code\",\"value\":\"\",\"code\":\"COLLECTION_MKT_BUYOUT_STOCKTICKET\"}],\"drillDownType\":\"subordinate\",\"subordinateType\":\"allSubordinate\",\"drillDownMetric\":\"average_process_time\",\"pageNo\":1,\"pageSize\":10}", GetTaskFlowMetricCardDataRequestType.class)
//        def empCode = "D00024"
//        def needLastPeriod = true
//
//        and: "mock dal"
//        def queryByEmpCode = "{\"id\":452,\"empCode\":\"D00024\",\"displayName\":\"Zhe Li （李哲）\",\"domainName\":\"zheli\",\"leaderEmpCode\":\"S35097\",\"leaderEmpName\":\"JIM Ji （季毅华）\",\"teamId\":\"41999\",\"teamCname\":\"门票活动业务部\",\"orgIdPath\":\"Ctrip_CO0001 Ctrip_Board 29 SO002916 25481 41999\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部\",\"position\":\"\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:38:56 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:38:56 AM\"}";
//        dataUpdateBiz.getUpdateTime() >> LocalDate.now().plusDays(-1).toString()
//        employeeInfoDao.queryByEmpCode(_) >> MapperUtil.str2Obj(queryByEmpCode, BusinessDashboardEmployeeInfo.class)
//        examineConfigV2Dao.querySpecificPeriodAllMetricConfig(_,_,_,_,_) >> MapperUtil.str2List("[{\"id\":7057086,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2023\",\"quarter\":\"Q4\",\"examineMetric\":\"1;2\",\"examineType\":15,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"国内\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2023-12-18\",\"datachangeLasttime\":\"Dec 18, 2023 1:19:55 AM\",\"odtExamineLevel\":\"国内\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"国内\",\"overseaOdtExamineRange\":\"\"},{\"id\":7057093,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2023\",\"quarter\":\"Q4\",\"examineMetric\":\"3\",\"examineType\":0,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2023-12-18\",\"datachangeLasttime\":\"Dec 18, 2023 1:19:55 AM\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"},{\"id\":7057100,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2023\",\"quarter\":\"Q4\",\"examineMetric\":\"4\",\"examineType\":0,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2023-12-18\",\"datachangeLasttime\":\"Dec 18, 2023 1:19:55 AM\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"},{\"id\":7057107,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2023\",\"quarter\":\"Q4\",\"examineMetric\":\"5;6;7\",\"examineType\":0,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2023-12-18\",\"datachangeLasttime\":\"Dec 18, 2023 1:19:55 AM\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"},{\"id\":7057114,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2023\",\"quarter\":\"Q4\",\"examineMetric\":\"9\",\"examineType\":0,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2023-12-18\",\"datachangeLasttime\":\"Dec 18, 2023 1:19:55 AM\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"},{\"id\":7057121,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2023\",\"quarter\":\"Q4\",\"examineMetric\":\"10\",\"examineType\":0,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2023-12-18\",\"datachangeLasttime\":\"Dec 18, 2023 1:19:55 AM\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"},{\"id\":7057128,\"domainName\":\"zheli\",\"role\":7,\"year\":\"2023\",\"quarter\":\"Q4\",\"examineMetric\":\"11\",\"examineType\":0,\"examineLevel\":\"国内\",\"examineRange\":\"\",\"actExamineLevel\":\"\",\"actExamineRange\":\"\",\"needUniversalStudios\":\"\",\"queryD\":\"2023-12-18\",\"datachangeLasttime\":\"Dec 18, 2023 1:19:55 AM\",\"odtExamineLevel\":\"\",\"odtExamineRange\":\"\",\"overseaOdtExamineLevel\":\"\",\"overseaOdtExamineRange\":\"\"}]", BusinessDashboardExamineeConfigV2.class)
//        employeeInfoDao.getLeaderEmployeeInfo(_) >> MapperUtil.str2Obj("", BusinessDashboardEmployeeInfo.class)
//        employeeInfoDao.queryByDomainName(_) >> MapperUtil.str2Obj(queryByEmpCode, BusinessDashboardEmployeeInfo.class)
//        organizationInfoDao.queryByLeaderEmpCode(_) >> MapperUtil.str2List("[{\"id\":132,\"nodeOrgId\":\"SO001057\",\"nodeOrgType\":\"Team\",\"nodeOrgName\":\"门票活动国内业务部\",\"nodeOrgLevel\":\"7\",\"parentOrgId\":\"41999\",\"buId\":\"25481\",\"deptId\":\"41999\",\"orgIdPath\":\"Ctrip_CO0001.Ctrip_Board.29.SO002916.25481.41999.SO001057\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>门票活动国内业务部\",\"leaderEmpCode\":\"D00024\",\"leaderEmpName\":\"Zhe Li （李哲）\",\"empCnt\":\"314\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:26:08 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:26:08 AM\"},{\"id\":133,\"nodeOrgId\":\"SO002566\",\"nodeOrgType\":\"Team\",\"nodeOrgName\":\"国内业务三方供应中心\",\"nodeOrgLevel\":\"8\",\"parentOrgId\":\"SO001057\",\"buId\":\"25481\",\"deptId\":\"41999\",\"orgIdPath\":\"Ctrip_CO0001.Ctrip_Board.29.SO002916.25481.41999.SO001057.SO002566\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>门票活动国内业务部>国内业务三方供应中心\",\"leaderEmpCode\":\"D00024\",\"leaderEmpName\":\"Zhe Li （李哲）\",\"empCnt\":\"\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:26:08 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:26:08 AM\"},{\"id\":134,\"nodeOrgId\":\"62390\",\"nodeOrgType\":\"Team\",\"nodeOrgName\":\"华西大区\",\"nodeOrgLevel\":\"8\",\"parentOrgId\":\"SO001057\",\"buId\":\"25481\",\"deptId\":\"41999\",\"orgIdPath\":\"Ctrip_CO0001.Ctrip_Board.29.SO002916.25481.41999.SO001057.62390\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>门票活动国内业务部>华西大区\",\"leaderEmpCode\":\"D00024\",\"leaderEmpName\":\"Zhe Li （李哲）\",\"empCnt\":\"61\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:26:08 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:26:08 AM\"},{\"id\":135,\"nodeOrgId\":\"SO003279\",\"nodeOrgType\":\"Team\",\"nodeOrgName\":\"海外供应链运营组\",\"nodeOrgLevel\":\"9\",\"parentOrgId\":\"SO002566\",\"buId\":\"25481\",\"deptId\":\"41999\",\"orgIdPath\":\"Ctrip_CO0001.Ctrip_Board.29.SO002916.25481.41999.SO001057.SO002566.SO003279\",\"orgNamePath\":\"Group>Ctrip Board>携程旅行网>BG-旅游事业群>BG-旅游事业群>门票活动业务部>门票活动国内业务部>国内业务三方供应中心>海外供应链运营组\",\"leaderEmpCode\":\"D00024\",\"leaderEmpName\":\"Zhe Li （李哲）\",\"empCnt\":\"\",\"datachangeCreateuser\":\"\",\"datachangeCreatetime\":\"Nov 8, 2023 6:26:08 AM\",\"datachangeLastuser\":\"\",\"datachangeLasttime\":\"Nov 8, 2023 6:26:08 AM\"}]", BusinessDashboardOrganizationInfo.class)
//
//        and: "mock client"
//        def resp1 = "{\"result\":\"[[0.2143,0.5036,182,3.75]]\",\"metricList\":[\"complete_rate\",\"ontime_complete_rate\",\"overtime_event_cnt\",\"average_process_time\"]}"
//        def resp2 = "{\"result\":\"[[0.2143,0.5036,182,3.75]]\",\"metricList\":[\"complete_rate\",\"ontime_complete_rate\",\"overtime_event_cnt\",\"average_process_time\"]}"
//        client.getRawData(_) >> MapperUtil.str2Obj(resp1, GetRawDataResponseType.class) >> MapperUtil.str2Obj(resp2, GetRawDataResponseType.class)
//
//        when: "调用接口"
//        def response = taskFlowMetricCardBizImpl.getTaskFlowMetricCardData(request, empCode, needLastPeriod)
//
//        then: "验证reponse"
//        with(response){
//            response.get().status == "normal"
//            response.get().dimData.get("complete_rate_lastperiod") == 0.2143
//        }
//
//    }
}
