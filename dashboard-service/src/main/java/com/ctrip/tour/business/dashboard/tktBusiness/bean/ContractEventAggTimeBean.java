package com.ctrip.tour.business.dashboard.tktBusiness.bean;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ContractEventAggTimeBean {
    public int endType; // 1: 当前时间  2：配置时间
    public int endTime; // 发送内容聚合时间，单位为小时，比如9，表示每天的9点
    public int interval; // 时间间隔, 单位是小时
}
