//package com.ctrip.tour.business.dashboard.sightArchives.proxy;
//
//
//import com.ctrip.gs.poibiz.information.contract.*;
//import com.ctrip.tour.business.dashboard.utils.UserUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang.StringUtils;
//import org.springframework.stereotype.Component;
//
//import java.util.*;
//
//@Component
//@Slf4j
//public class PoiBizInformationServiceProxy {
//
//    static PoiBizInformationServiceClient client = PoiBizInformationServiceClient.getInstance();
//
//    public Map<Long,String> getMidPoiBizInformation(List<Long> viewSpotIdList) {
//
//        viewSpotIdList = new ArrayList<>(new HashSet<>(viewSpotIdList));
//
//        GetMidPoiBizInformationRequestType requestType = new GetMidPoiBizInformationRequestType();
//        RequestHeadType head = new RequestHeadType();
//        head.setLocale(UserUtil.getVbkLocale());
//        requestType.setHead(head);
//        List<BusinessItemType> businessItemTypeList = new ArrayList<>();
//        BusinessItemType businessItemType = new BusinessItemType();
//        businessItemType.setBusinessType(3); //3=景点、66=玩乐，其它类型请参考:http://conf.ctripcorp.com/pages/viewpage.action?pageId=175000145
//        businessItemType.setBusinessIdList(viewSpotIdList);
//        businessItemTypeList.add(businessItemType);
//        requestType.setBusinessItemList(businessItemTypeList);
//
//        GetMidPoiBizInformationResponseType responseType;
//        try {
//            responseType = client.getMidPoiBizInformation(requestType);
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
//        List<PoiDetailType> poiDetailList = responseType.getPoiDetailList();
//        Map<Long, String> map = new java.util.HashMap<>();
//        for (PoiDetailType poiDetail : poiDetailList) {
//            SightDetailType sightDetail = poiDetail.getSightDetail();
//            if (sightDetail != null && sightDetail.getBasicInfo() != null && StringUtils.isNotBlank(sightDetail.getBasicInfo().getSightEName())) {
//                map.put(sightDetail.getBasicInfo().getSightId(), sightDetail.getBasicInfo().getSightEName());
//            }
//        }
//
//        if(map.keySet().size()<viewSpotIdList.size()) {
//            List<Long> missingIds = new ArrayList<>(viewSpotIdList);
//            missingIds.removeAll(map.keySet());
//            GetMidPoiBizInformationResponseType responseType1;
//            try {
//                responseType1 = client.getMidPoiBizInformation(new GetMidPoiBizInformationRequestType() {{
//                    setBusinessItemList(Collections.singletonList(new BusinessItemType() {{
//                        setBusinessType(66); // 66=玩乐
//                        setBusinessIdList(missingIds);
//                    }}));
//                }});
//            } catch (Exception e) {
//                throw new RuntimeException(e);
//            }
//            List<PoiDetailType> poiDetailList1 = responseType1.getPoiDetailList();
//            for (PoiDetailType poiDetail : poiDetailList1) {
//                SightDetailType sightDetail = poiDetail.getSightDetail();
//                if (sightDetail != null && sightDetail.getBasicInfo() != null && StringUtils.isNotBlank(sightDetail.getBasicInfo().getSightEName())) {
//                    map.put(sightDetail.getBasicInfo().getSightId(), sightDetail.getBasicInfo().getSightEName());
//                }
//            }
//        }
//
//        return map;
//    }
//}
