package com.ctrip.tour.business.dashboard.tktBusiness.strategy.overseaImpl;

import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ExamineConfigBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardOverseaExamineeConfigDao;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardOverseaExamineeConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.Bus101102Helper;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.MultiPeriodMappingHelper;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.OverseaMetricCalStrategy;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.subMetricImpl.OverseaSubMetricCalStategyBizImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Future;

@Component
public class Bus102MetricStrategy implements OverseaMetricCalStrategy {

    @Autowired
    private OverseaSubMetricCalStategyBizImpl overseaSubMetricCalStategyBiz;

    @Autowired
    private BusinessDashboardOverseaExamineeConfigDao examineeConfigDao;


    @Override
    public Future<List<MetricDetailInfo>> getOverseaSingleMetricCardData(TimeFilter timeFilter,
                                                                         OverseaMetricInfoBean metricInfoBean,
                                                                         String d,
                                                                         AvailableSubMetric availableSubMetric) throws Exception {
        List<MetricDetailInfo> metricDetailInfoList = new ArrayList<>();

        List<String> subMetricList = availableSubMetric.getSubMetricList();
        List<Future<MetricDetailInfo>> futureList = new ArrayList<>();
        for (String subMetric : subMetricList) {
            String actualSubMetric = Bus101102Helper.getActualSubMetric(subMetric, metricInfoBean);
            futureList.add(overseaSubMetricCalStategyBiz.getBus101102SubMetricCardData(timeFilter, metricInfoBean, d, "102", actualSubMetric));
        }
        for (Future<MetricDetailInfo> futureResult : futureList) {
            metricDetailInfoList.add(futureResult.get());
        }
        return new AsyncResult<>(metricDetailInfoList);
    }

    @Override
    public GetOverseaTrendLineDataResponseType getOverseaSingleTrendlineData(GetOverseaTrendLineDataRequestType request,
                                                                             String d) throws Exception {
        String domainName = request.getDomainName();
        String metric = request.getMetric();
        List<BusinessDashboardOverseaExamineeConfig> examineeConfigList = examineeConfigDao.queryMetricAllConfig(domainName, d, metric);
        List<ExamineConfigBean> examineConfigBeanList = MultiPeriodMappingHelper.getMultiPeriodConfigList(request.getTimeFilter(), d, null, examineeConfigList);

        return overseaSubMetricCalStategyBiz.getBus101102SubTrendlineData(request, d, examineConfigBeanList);
    }

    @Override
    public GetOverseaDrillDownBaseInfoResponseType getOverseaSingleDrillDownBaseInfo(GetOverseaDrillDownBaseInfoRequestType request, String d, OverseaMetricInfoBean metricInfoBean) throws Exception {
        return overseaSubMetricCalStategyBiz.getBus101102SubDrillDownBaseInfo(request, d, metricInfoBean);
    }

    @Override
    public GetOverseaTableDataResponseType getOverseaSingleTableData(GetOverseaTableDataRequestType request,
                                                                     String d,
                                                                     OverseaMetricInfoBean metricInfoBean) throws Exception {
        return overseaSubMetricCalStategyBiz.getBus101102SubTableData(request, d, metricInfoBean);
    }


    @Override
    public String getMetricName() {
        return "102";
    }
}
