package com.ctrip.tour.business.dashboard.sightArchives.entity.domesticmetric;

import lombok.Data;

import java.util.List;

@Data
public class Domestic12TargetParamBean {
    // bd名称
    String domainName;
    //年份
    String year;
    String type;
    //月份
    List<String> monthList;
    //业务线列表（101国内门票，102国内活动，103国内日游）
    List<String> businessLines;
    //查询指标
    String metric;

    // 分区d
    String d;
    List<String> domainNames;
    String field;
    List<String> businessRegionnameTgt;
    List<String> businessProvincenameTgt;


}
