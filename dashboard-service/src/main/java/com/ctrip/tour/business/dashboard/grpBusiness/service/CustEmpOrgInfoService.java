package com.ctrip.tour.business.dashboard.grpBusiness.service;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ctrip.soa._24922.CustTourRegionInfo;
import com.ctrip.soa._24922.GrpEmployeeItem;
import com.ctrip.tour.business.dashboard.grpBusiness.bo.CustRegionOrgInfoDTO;
import com.ctrip.tour.business.dashboard.grpBusiness.entity.EdwHrEmpVacation;
import com.ctrip.tour.business.dashboard.utils.MapperUtil;
import com.ctrip.tourtailor.product.contract.BusinessAreaInfoDTO;
import com.ctrip.tourtailor.product.contract.GradeAreaInfoDTO;
import com.ctrip.tourtailor.product.contract.Productservicev2Client;
import com.ctrip.tourtailor.product.contract.QueryBusinessAreaListRequestType;
import com.ctrip.tourtailor.product.contract.QueryBusinessAreaListResponseType;
import com.ctrip.tourtailor.product.contract.QueryGradeAreaListRequestType;
import com.ctrip.tourtailor.product.contract.QueryGradeAreaListResponseType;
import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;
import qunar.tc.qconfig.client.spring.QMapConfig;

/**
 * <AUTHOR>
 * @Date 2025/3/25
 */
@Service
@Slf4j
public class CustEmpOrgInfoService {

    @Autowired
    HrOrgEmpInfoService hrOrgEmpInfoService;
    @QMapConfig("config.properties")
    private Map<String,String> configMap;
    @Autowired
            UserInfoService userInfoService;

    private static final String TOP_LEADER_CODE = "S00546";
    private static final String CUST_LEADER_CODE = "S43455";
    private static final String DEP_LEADER_CODE = "S40160";

    Productservicev2Client productservicev2 = Productservicev2Client.getInstance();

    private static final String AREA_PREFIX = "area-";
    private static final String REGION_PREFIX = "region-";
    String regex = "[\\(（](.*?)[\\)）]";


    public List<CustTourRegionInfo> getCustTourRegionList(String empCode) throws Exception {

        empCode = CUST_LEADER_CODE;

        if (StringUtils.isBlank(empCode)) {
            empCode = userInfoService.getMappingEmpCode();
            if (StringUtils.equals(TOP_LEADER_CODE, empCode)) {
                empCode = CUST_LEADER_CODE;
            }

        }
        return getRegionList(empCode, null, null);
    }


    public Map<String, List<String>> getEmpNoByAreaNames(List<String> names, boolean isRegion) throws Exception {

        if (isRegion) {
            QueryBusinessAreaListRequestType requestType = new QueryBusinessAreaListRequestType();
            requestType.setBizType("C");
            requestType.setPageIndex(1);
            requestType.setPageSize(1000);

            QueryBusinessAreaListResponseType baListResp = productservicev2.queryBusinessAreaList(requestType);
            return baListResp.getBusinessAreaList().stream().collect(Collectors.groupingBy(BusinessAreaInfoDTO::getEid,
                    Collectors.mapping(BusinessAreaInfoDTO::getBizAreaName, Collectors.toList())));
        } else {
            QueryGradeAreaListRequestType areaListRequestType = new QueryGradeAreaListRequestType();
            areaListRequestType.setBizType("C");
            areaListRequestType.setPageIndex(1);
            areaListRequestType.setPageSize(1000);


            QueryGradeAreaListResponseType gaListResp = productservicev2.queryGradeAreaList(areaListRequestType);


            List<GradeAreaInfoDTO> gradeAreaList = gaListResp.getGradeAreaList();
            String handleAreaNameSwitch = configMap.get("handle.areaName.switch");
            if (StringUtils.equals("on", handleAreaNameSwitch)) {
                for (GradeAreaInfoDTO gradeAreaInfoDTO : gradeAreaList) {
                    gradeAreaInfoDTO.setAreaName(gradeAreaInfoDTO.getAreaName().replace("【公司】", ""));//NOSONAR
                }
            }

            String on = configMap.getOrDefault("cust.jobNumber.spec.hanle.switch", "on");
            if (StringUtils.equals("on", on)) {

                return gradeAreaList.stream().filter(ga -> StringUtils.isNotBlank(ga.getJobName()))
                        .collect(Collectors.groupingBy(ga -> {
                                    Pattern pattern = Pattern.compile(regex);
                                    Matcher matcher = pattern.matcher(ga.getJobName());
                                    if (matcher.find()) {
                                        return matcher.group(1);
                                    }
                                    return "";
                                },
                                Collectors.mapping(ga -> ga.getAreaName().replace("【个人】", ""), Collectors.toList())));//NOSONAR

            } else {
                return gradeAreaList.stream().filter(ga -> StringUtils.isNotBlank(ga.getJobNumber()))
                        .collect(Collectors.groupingBy(GradeAreaInfoDTO::getJobNumber,
                                Collectors.mapping(GradeAreaInfoDTO::getAreaName, Collectors.toList())));
            }


        }






    }


    public List<GrpEmployeeItem> getCustEmployeeList(String orgId, String displayName) throws Exception {
        //定制游特殊编码
        String empCode = userInfoService.getMappingEmpCode();
        empCode = CUST_LEADER_CODE;

        if (orgId.startsWith(AREA_PREFIX)) {
            QueryGradeAreaListRequestType areaListRequestType = new QueryGradeAreaListRequestType();
            areaListRequestType.setBizType("C");
            areaListRequestType.setPageIndex(1);
            areaListRequestType.setPageSize(1000);


            QueryGradeAreaListResponseType gaListResp = productservicev2.queryGradeAreaList(areaListRequestType);


            List<GradeAreaInfoDTO> gradeAreaList = gaListResp.getGradeAreaList();

            String handleAreaNameSwitch = configMap.get("handle.areaName.switch");
            if (StringUtils.equals("on", handleAreaNameSwitch)) {
                for (GradeAreaInfoDTO gradeAreaInfoDTO : gradeAreaList) {
                    gradeAreaInfoDTO.setAreaName(gradeAreaInfoDTO.getAreaName().replace("【公司】", ""));//NOSONAR
                }
            }

           return gradeAreaList
                    .stream()
                    .filter(ga -> StringUtils.equals(orgId, String.join("", AREA_PREFIX, ga.getBizAreaId().toString())))
                    .map(ga -> {
                        GrpEmployeeItem grpEmployeeItem = new GrpEmployeeItem();
                        grpEmployeeItem.setDefaultBusinessLine(3);
                        grpEmployeeItem.setEmpCode(ga.getJobNumber());
                        grpEmployeeItem.setOrgId(orgId);
                        grpEmployeeItem.setOrgName(ga.getAreaName());
                        grpEmployeeItem.setBusinessLine(Lists.newArrayList(3));
                        grpEmployeeItem.setDisplayName(displayName);
                        grpEmployeeItem.setDomainName(ga.getJobName());
                        return grpEmployeeItem;
                    }).collect(Collectors.toList());

        } else if (orgId.startsWith(REGION_PREFIX)) {
            QueryBusinessAreaListRequestType requestType = new QueryBusinessAreaListRequestType();
            requestType.setBizType("C");
            requestType.setPageIndex(1);
            requestType.setPageSize(1000);



            QueryBusinessAreaListResponseType baListResp = productservicev2.queryBusinessAreaList(requestType);

            return baListResp.getBusinessAreaList()
                    .stream()
                    .filter(ga -> StringUtils.equals(orgId, String.join("", REGION_PREFIX, ga.getBizAreaId().toString())))
                    .map(ga -> {
                        GrpEmployeeItem grpEmployeeItem = new GrpEmployeeItem();
                        grpEmployeeItem.setDefaultBusinessLine(3);
                        grpEmployeeItem.setEmpCode(ga.getEid());
                        grpEmployeeItem.setOrgId(orgId);
                        grpEmployeeItem.setOrgName(ga.getBizAreaName());
                        grpEmployeeItem.setBusinessLine(Lists.newArrayList(3));
                        EdwHrEmpVacation empVacation = null;
                        try {
                            empVacation = hrOrgEmpInfoService.queryEmpInfoByCode(ga.getEid());
                        } catch (SQLException e) {
                            log.warn("query emp info error ", e);
                        }
                        if (Objects.nonNull(empVacation)) {
                            grpEmployeeItem.setDisplayName(empVacation.getDisplayName());
                            grpEmployeeItem.setDomainName(empVacation.getDomainName());
                        } else {
                            grpEmployeeItem.setDisplayName(ga.getBizAreaName());
                            grpEmployeeItem.setDomainName(ga.getBizAreaName());
                        }
                        return grpEmployeeItem;
                    }).collect(Collectors.toList());
        } else {
            if (StringUtils.equals("SO011086", orgId) && (StringUtils.equals(TOP_LEADER_CODE, empCode) ||
                    StringUtils.equals(DEP_LEADER_CODE, empCode)||
                    StringUtils.equals(CUST_LEADER_CODE, empCode))) {
                GrpEmployeeItem grpEmployeeItem = new GrpEmployeeItem();
                grpEmployeeItem.setDefaultBusinessLine(3);
                grpEmployeeItem.setEmpCode(CUST_LEADER_CODE);
                grpEmployeeItem.setOrgId(orgId);
                grpEmployeeItem.setOrgName("定制游");//NOSONAR
                grpEmployeeItem.setDisplayName("王海涛");//NOSONAR
                grpEmployeeItem.setDomainName("Ted Wang");
                grpEmployeeItem.setBusinessLine(Lists.newArrayList(3));
                return Lists.newArrayList(grpEmployeeItem);
            }
        }
        return Lists.newArrayList();
    }

    public List<CustTourRegionInfo> getRegionList(String empCode, String regionName, String areaName) throws Exception {
        empCode = CUST_LEADER_CODE;
        QueryBusinessAreaListRequestType requestType = new QueryBusinessAreaListRequestType();
        requestType.setBizType("C");
        requestType.setPageSize(1000);
        requestType.setPageIndex(1);
        QueryBusinessAreaListResponseType baListResp = productservicev2.queryBusinessAreaList(requestType);

        QueryGradeAreaListRequestType areaListRequestType = new QueryGradeAreaListRequestType();
        areaListRequestType.setBizType("C");
        areaListRequestType.setPageSize(1000);
        areaListRequestType.setPageIndex(1);


        QueryGradeAreaListResponseType gaListResp = productservicev2.queryGradeAreaList(areaListRequestType);


        List<GradeAreaInfoDTO> gradeAreaList = gaListResp.getGradeAreaList();

        String handleAreaNameSwitch = configMap.get("handle.areaName.switch");
        if (StringUtils.equals("on", handleAreaNameSwitch)) {
            for (GradeAreaInfoDTO gradeAreaInfoDTO : gradeAreaList) {
                gradeAreaInfoDTO.setAreaName(gradeAreaInfoDTO.getAreaName().replace("【公司】", ""));//NOSONAR
            }
        }

        //筛选评级区域负责人或王海涛权限
        String finalEmpCode = empCode;
        List<GradeAreaInfoDTO> grdEmpRegionList = gradeAreaList.stream().filter(ga -> (StringUtils.isBlank(areaName) || StringUtils.equals(areaName, ga.getAreaName())) &&
                        (StringUtils.equals(finalEmpCode, ga.getJobNumber()) || StringUtils.equals(finalEmpCode, "S43455")))
                .collect(Collectors.toList());


        //评级区域不为空则说明是评级区域负责人
        if (CollectionUtils.isNotEmpty(grdEmpRegionList)) {
            Map<String, List<GradeAreaInfoDTO>> grdEmpRegionMap = grdEmpRegionList.stream().collect(Collectors.groupingBy(GradeAreaInfoDTO::getBizAreaName));
            return grdEmpRegionMap.entrySet()
                    .stream()
                    .map(entry -> {
                        CustTourRegionInfo custTourRegionInfo = new CustTourRegionInfo();
                        custTourRegionInfo.setRegionName(entry.getKey());
                        custTourRegionInfo.setAreas(entry.getValue().stream().map(GradeAreaInfoDTO::getAreaName).collect(Collectors.toList()));
                        return custTourRegionInfo;
                    }).collect(Collectors.toList());
        }

        Map<Long, List<GradeAreaInfoDTO>> gaInfoMap = gradeAreaList.stream()
                .filter(ga -> (StringUtils.isBlank(areaName) || StringUtils.equals(areaName, ga.getAreaName())))
                .collect(Collectors.groupingBy(GradeAreaInfoDTO::getBizAreaId));

        String finalEmpCode1 = empCode;
        return baListResp.getBusinessAreaList().stream()
                .filter(ba -> (StringUtils.isBlank(regionName) || StringUtils.equals(regionName, ba.getBizAreaName())) &&
                        StringUtils.equals(finalEmpCode1, ba.getEid()))
                .map(ba -> {
                    CustTourRegionInfo custTourRegionInfo = new CustTourRegionInfo();
                    custTourRegionInfo.setRegionName(ba.getBizAreaName());
                    custTourRegionInfo.setAreas(gaInfoMap.getOrDefault(ba.getBizAreaId(), new ArrayList<>()).stream().map(GradeAreaInfoDTO::getAreaName).collect(Collectors.toList()));
                    return custTourRegionInfo;
                }).collect(Collectors.toList());

    }

    public List<CustRegionOrgInfoDTO> getCustOrgRegionList(String empCode, String regionName, String areaName) throws Exception {
        empCode = CUST_LEADER_CODE;
        QueryBusinessAreaListRequestType requestType = new QueryBusinessAreaListRequestType();
        requestType.setBizType("C");
        requestType.setPageIndex(1);
        requestType.setPageSize(1000);


        QueryBusinessAreaListResponseType baListResp = productservicev2.queryBusinessAreaList(requestType);

        QueryGradeAreaListRequestType areaListRequestType = new QueryGradeAreaListRequestType();
        areaListRequestType.setBizType("C");
        areaListRequestType.setPageIndex(1);
        areaListRequestType.setPageSize(1000);


        QueryGradeAreaListResponseType gaListResp = productservicev2.queryGradeAreaList(areaListRequestType);



        List<GradeAreaInfoDTO> gradeAreaList = gaListResp.getGradeAreaList();

        String handleAreaNameSwitch = configMap.get("handle.areaName.switch");
        if (StringUtils.equals("on", handleAreaNameSwitch)) {
            for (GradeAreaInfoDTO gradeAreaInfoDTO : gradeAreaList) {
                gradeAreaInfoDTO.setAreaName(gradeAreaInfoDTO.getAreaName().replace("【公司】", ""));//NOSONAR
            }
        }

        //筛选评级区域负责人或王海涛权限
        String finalEmpCode = empCode;
        List<GradeAreaInfoDTO> grdEmpRegionList = gradeAreaList.stream().filter(ga -> (StringUtils.isBlank(areaName) || StringUtils.equals(areaName, ga.getAreaName())) &&
                        (StringUtils.equals(finalEmpCode, ga.getJobNumber()) || StringUtils.equals(finalEmpCode, "S43455")))
                .collect(Collectors.toList());


        //评级区域不为空则说明是评级区域负责人
        if (CollectionUtils.isNotEmpty(grdEmpRegionList)) {
            Map<String, List<GradeAreaInfoDTO>> grdEmpRegionMap = grdEmpRegionList.stream().collect(Collectors.groupingBy(ga -> String.join("-", ga.getBizAreaName(), ga.getBizAreaId().toString())));
            return grdEmpRegionMap.entrySet()
                    .stream()
                    .map(entry -> {
                        CustRegionOrgInfoDTO custRegionOrgInfoDTO = new CustRegionOrgInfoDTO();
                        String[] regionInfoSplit = entry.getKey().split("-");
                        custRegionOrgInfoDTO.setRegionName(regionInfoSplit[0]);
                        custRegionOrgInfoDTO.setRegionId(String.join("", REGION_PREFIX, regionInfoSplit[1]));

                        custRegionOrgInfoDTO.setAreaInfos(entry.getValue().stream().map(gai -> {
                            CustRegionOrgInfoDTO.AreaInfo areaInfo = new CustRegionOrgInfoDTO.AreaInfo();
                            areaInfo.setAreaId(String.join("",AREA_PREFIX, gai.getAreaId().toString()));
                            areaInfo.setAreaName(gai.getAreaName());
                            areaInfo.setJobNumber(gai.getJobNumber());
                            return areaInfo;
                        }).collect(Collectors.toList()));
                        return custRegionOrgInfoDTO;
                    }).collect(Collectors.toList());
        }

        Map<Long, List<GradeAreaInfoDTO>> gaInfoMap = gradeAreaList.stream()
                .filter(ga -> (StringUtils.isBlank(areaName) || StringUtils.equals(areaName, ga.getAreaName())))
                .collect(Collectors.groupingBy(GradeAreaInfoDTO::getBizAreaId));

        String finalEmpCode1 = empCode;
        return baListResp.getBusinessAreaList().stream()
                .filter(ba -> (StringUtils.isBlank(regionName) || StringUtils.equals(regionName, ba.getBizAreaName())) &&
                        StringUtils.equals(finalEmpCode1, ba.getEid()))
                .map(ba -> {

                    CustRegionOrgInfoDTO custRegionOrgInfoDTO = new CustRegionOrgInfoDTO();
                    custRegionOrgInfoDTO.setRegionName(ba.getBizAreaName());
                    custRegionOrgInfoDTO.setRegionId(String.join("", REGION_PREFIX, ba.getBizAreaId().toString()));
                    custRegionOrgInfoDTO.setEid(ba.getEid());
                    custRegionOrgInfoDTO.setAreaInfos(gaInfoMap.getOrDefault(ba.getBizAreaId(), new ArrayList<>()).stream()
                            .map(gai -> {
                                CustRegionOrgInfoDTO.AreaInfo areaInfo = new CustRegionOrgInfoDTO.AreaInfo();
                                areaInfo.setAreaId(String.join("", AREA_PREFIX, gai.getAreaId().toString()));
                                areaInfo.setAreaName(gai.getAreaName());
                                areaInfo.setJobNumber(gai.getJobNumber());
                                return areaInfo;
                            }).collect(Collectors.toList()));

                    return custRegionOrgInfoDTO;
                }).collect(Collectors.toList());

    }

}
