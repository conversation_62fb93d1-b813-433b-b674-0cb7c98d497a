package com.ctrip.tour.business.dashboard.sightArchives.entity.commonEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Sensitive;
import com.ctrip.platform.dal.dao.annotation.Type;
import java.sql.Types;
import java.sql.Timestamp;

import com.ctrip.platform.dal.dao.DalPojo;

/**
 * <AUTHOR>
 * @date 2025-03-27
 */
@Entity
@Database(name = "TtdReportDB_W")
@Table(name = "adm_prd_ttd_cpd_trip_dim_info_unified_output")
public class AdmPrdTtdCpdTripDimInfoUnifiedOutput implements DalPojo {

    /**
     * 主键
     */
    @Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long id;

    /**
     * 景点ID
     */
	@Column(name = "viewspot_id")
	@Type(value = Types.BIGINT)
	private Long viewspotId;

    /**
     * 景点名称
     */
	@Column(name = "viewspot_name")
	@Type(value = Types.VARCHAR)
	private String viewspotName;

	/**
	 * POI英文名称
	 */
	@Column(name = "poi_en_name")
	@Type(value = Types.VARCHAR)
	private String poiEnName;

    /**
     * 父景点ID
     */
	@Column(name = "parent_viewspot_id")
	@Type(value = Types.BIGINT)
	private Long parentViewspotId;

    /**
     * 父景点名称
     */
	@Column(name = "parent_viewspot_name")
	@Type(value = Types.VARCHAR)
	private String parentViewspotName;

    /**
     * POI_ID
     */
	@Column(name = "poi_id")
	@Type(value = Types.BIGINT)
	private Long poiId;

    /**
     * POI_名称
     */
	@Column(name = "poi_name")
	@Type(value = Types.VARCHAR)
	private String poiName;

    /**
     * 是否国内
     */
	@Column(name = "is_domistic")
	@Type(value = Types.BIGINT)
	private Long isDomestic;

    /**
     * 业务大区
     */
	@Column(name = "business_region_name")
	@Type(value = Types.VARCHAR)
	private String businessRegionName;

	/**
	 * 业务子区域
	 */
	@Column(name = "business_sub_region_name")
	@Type(value = Types.VARCHAR)
	private String businessSubRegionName;

	/**
	 * 国家id
	 */
	@Column(name = "country_id")
	@Type(value = Types.BIGINT)
	private Long countryId;

    /**
     * 国家名称
     */
	@Column(name = "country_name")
	@Type(value = Types.VARCHAR)
	private String countryName;

	/**
	 * 省份id
	 */
	@Column(name = "province_id")
	@Type(value = Types.BIGINT)
	private Long provinceId;

    /**
     * 省份名称
     */
	@Column(name = "province_name")
	@Type(value = Types.VARCHAR)
	private String provinceName;


	/**
	 * 城市id
	 */
	@Column(name = "city_id")
	@Type(value = Types.BIGINT)
	private Long cityId;

    /**
     * 城市名称
     */
	@Column(name = "city_name")
	@Type(value = Types.VARCHAR)
	private String cityName;

    /**
     * 是否含子景点
     */
	@Column(name = "is_has_sub_viewspot")
	@Type(value = Types.BIGINT)
	private Long isHasSubViewspot;

    /**
     * 景点是否有效
     */
	@Column(name = "is_active")
	@Type(value = Types.BIGINT)
	private Long isActive;

    /**
     * 景点分层
     */
	@Column(name = "viewspot_class")
	@Type(value = Types.VARCHAR)
	private String viewspotClass;

    /**
     * 景点等级
     */
	@Column(name = "viewspot_level")
	@Type(value = Types.VARCHAR)
	private String viewspotLevel;

    /**
     * 原始景点经理
     */
	@Column(name = "ori_viewspot_meid")
	@Type(value = Types.VARCHAR)
	private String oriViewspotMeid;

    /**
     * 原始景点助理
     */
	@Column(name = "ori_viewspot_aeid")
	@Type(value = Types.VARCHAR)
	private String oriViewspotAeid;


    /**
     * 更新时间
     */
	@Column(name = "datachange_lasttime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;

	public String getPoiEnName() {
		return poiEnName;
	}

	public void setPoiEnName(String poiEnName) {
		this.poiEnName = poiEnName;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getViewspotId() {
		return viewspotId;
	}

	public void setViewspotId(Long viewspotId) {
		this.viewspotId = viewspotId;
	}

	public String getViewspotName() {
		return viewspotName;
	}

	public void setViewspotName(String viewspotName) {
		this.viewspotName = viewspotName;
	}

	public Long getParentViewspotId() {
		return parentViewspotId;
	}

	public void setParentViewspotId(Long parentViewspotId) {
		this.parentViewspotId = parentViewspotId;
	}

	public String getParentViewspotName() {
		return parentViewspotName;
	}

	public void setParentViewspotName(String parentViewspotName) {
		this.parentViewspotName = parentViewspotName;
	}

	public Long getPoiId() {
		return poiId;
	}

	public void setPoiId(Long poiId) {
		this.poiId = poiId;
	}

	public String getPoiName() {
		return poiName;
	}

	public void setPoiName(String poiName) {
		this.poiName = poiName;
	}

	public Long getIsDomestic() {
		return isDomestic;
	}

	public void setIsDomestic(Long isDomestic) {
		this.isDomestic = isDomestic;
	}

	public String getBusinessRegionName() {
		return businessRegionName;
	}

	public void setBusinessRegionName(String businessRegionName) {
		this.businessRegionName = businessRegionName;
	}

	public String getCountryName() {
		return countryName;
	}

	public void setCountryName(String countryName) {
		this.countryName = countryName;
	}

	public String getProvinceName() {
		return provinceName;
	}

	public void setProvinceName(String provinceName) {
		this.provinceName = provinceName;
	}

	public String getCityName() {
		return cityName;
	}

	public void setCityName(String cityName) {
		this.cityName = cityName;
	}

	public Long getIsHasSubViewspot() {
		return isHasSubViewspot;
	}

	public void setIsHasSubViewspot(Long isHasSubViewspot) {
		this.isHasSubViewspot = isHasSubViewspot;
	}

	public Long getIsActive() {
		return isActive;
	}

	public void setIsActive(Long isActive) {
		this.isActive = isActive;
	}

	public String getViewspotClass() {
		return viewspotClass;
	}

	public void setViewspotClass(String viewspotClass) {
		this.viewspotClass = viewspotClass;
	}

	public String getViewspotLevel() {
		return viewspotLevel;
	}

	public void setViewspotLevel(String viewspotLevel) {
		this.viewspotLevel = viewspotLevel;
	}

	public String getOriViewspotMeid() {
		return oriViewspotMeid;
	}

	public void setOriViewspotMeid(String oriViewspotMeid) {
		this.oriViewspotMeid = oriViewspotMeid;
	}

	public String getOriViewspotAeid() {
		return oriViewspotAeid;
	}

	public void setOriViewspotAeid(String oriViewspotAeid) {
		this.oriViewspotAeid = oriViewspotAeid;
	}

	public String getBusinessSubRegionName() {
		return businessSubRegionName;
	}

	public void setBusinessSubRegionName(String businessSubRegionName) {
		this.businessSubRegionName = businessSubRegionName;
	}

	public Timestamp getDatachangeLasttime() {
		return datachangeLasttime;
	}

	public void setDatachangeLasttime(Timestamp datachangeLasttime) {
		this.datachangeLasttime = datachangeLasttime;
	}

	public Long getCountryId() {
		return countryId;
	}

	public void setCountryId(Long countryId) {
		this.countryId = countryId;
	}

	public Long getProvinceId() {
		return provinceId;
	}

	public void setProvinceId(Long provinceId) {
		this.provinceId = provinceId;
	}

	public Long getCityId() {
		return cityId;
	}

	public void setCityId(Long cityId) {
		this.cityId = cityId;
	}
}