package com.ctrip.tour.business.dashboard.grpBusiness.domain.plan;

import com.ctrip.soa._24922.CompareConfig;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.AbstractOrchestrationFunction;
import com.ctrip.tour.business.dashboard.grpBusiness.domain.model.ResultData;
import lombok.Getter;
import lombok.Setter;
import org.joda.time.DateTime;
import org.joda.time.Days;

import java.util.*;
import java.util.stream.Collectors;

@Getter
@Setter
public class DefaultCompareFunction extends AbstractOrchestrationFunction {
    CompareConfig compareConfig; // 同环比配置
    List<String> dims; // 纬度
    boolean needFixDate; // 是否需要时间修复

    public DefaultCompareFunction() {
        super();
    }

    public DefaultCompareFunction(CompareConfig compareConfig, List<String> dims) {
        super();
        this.compareConfig = compareConfig;
        if (dims != null) {
            this.dims = new ArrayList<>(dims);
        }
    }

    public String getCompareDate(String date) {
        DateTime compareStartTime = new DateTime(compareConfig.getCompareDate().get(0));
        DateTime compareEndTime = new DateTime(compareConfig.getCompareDate().get(1));
        DateTime startTime = compareEndTime.plusDays(1);
        // 对比日期
        DateTime curTime = new DateTime(date);
        Days diff = Days.daysBetween(startTime, curTime);
        return compareStartTime.plusDays(diff.getDays()).toString("yyyy-MM-dd");
    }

    // 时间修复
    public void fixDate(ResultData mainQueryDataResult) {
        // 开始时间
        DateTime compareStartTime = new DateTime(compareConfig.getCompareDate().get(0));
        DateTime compareEndTime = new DateTime(compareConfig.getCompareDate().get(1));
        Days diff = Days.daysBetween(compareStartTime, compareEndTime);
        DateTime startTime = compareEndTime.plusDays(1);
        DateTime endTime = startTime.plusDays(diff.getDays());

        Map<String, Boolean> containsMap = new HashMap<>();
        mainQueryDataResult.getData().forEach(
                v -> containsMap.put(v.get(compareConfig.getCompareIndicatorName()).toString(), true)
        );
        while (!startTime.isAfter(endTime)) {
            String dateItem = startTime.toString("yyyy-MM-dd");
            if (containsMap.get(dateItem) == null) {
                Map<String, Object> item = new HashMap<>();
                item.put(compareConfig.getCompareIndicatorName(), dateItem);
                mainQueryDataResult.getData().add(item);
            }
            startTime = startTime.plusDays(1);
        }
    }

    @Override
    public void compare(LogicQueryPlan mainLogicPlain, LogicQueryPlan otherLogicPlain) {
        ResultData mainQueryDataResult = mainLogicPlain.getReduceQueryDataResult(); // 主查询
        ResultData otherQueryDataResult = otherLogicPlain.getReduceQueryDataResult(); // 子查询

        // 时间修复
        if (needFixDate) {
            fixDate(mainQueryDataResult);
        }
        // 数据合并
        Map<String, Map<String, Object>> compareMap = otherQueryDataResult.getData().stream()
                .collect(Collectors.toMap(
                        v -> {
                            String v1 = v.getOrDefault(compareConfig.getCompareIndicatorName(), "").toString();
                            StringBuilder v2 = new StringBuilder(v1);
                            if (dims != null) {
                                dims.forEach(dim -> {
                                    v2.append(v.getOrDefault(dim, "").toString());
                                });
                            }
                            return v2.toString();
                        },
                        v -> v
                ));

        // 数据合并
        mainQueryDataResult.getData().forEach(
                v -> {
                    String d = v.getOrDefault(compareConfig.getCompareIndicatorName(), "").toString();
                    StringBuilder compareDate = new StringBuilder();
                    if (d != null && !d.isEmpty()) {
                        compareDate.append(getCompareDate(d));
                    }
                    if (dims != null) {
                        dims.forEach(dim -> compareDate.append(v.getOrDefault(dim, "").toString()));
                    }
                    Map<String, Object> compareData = compareMap.get(compareDate.toString());
                    // 数据合并
                    mainQueryDataResult.getMeta().forEach(meta -> {
                        // 是否纬度？
                        if (meta.isIndicatorIsDimension()) {
                            return;
                        }
                        String indicatorName = meta.getIndicatorName();
                        Map<String, Object> deriveData;
                        Object o = v.get(indicatorName + "DeriveData");
                        if (o instanceof Map) {
                            deriveData = (Map<String, Object>) o;
                        } else {
                            deriveData = new HashMap<>();
                            deriveData.put(compareConfig.getPrefix() + "_value", null);
                            deriveData.put(compareConfig.getPrefix() + "_rate", null);
                            v.put(indicatorName + "DeriveData", deriveData);
                        }
                        // 同环比计算
                        if (compareData != null) {
                            Object m = v.get(indicatorName);
                            Object t = compareData.get(indicatorName);
                            Double mValue = null; // 主
                            Double tValue = null; // 从
                            if (m != null) {
                                mValue = Double.parseDouble(m.toString());
                            }
                            if (t != null) {
                                tValue = Double.parseDouble(t.toString());
                                deriveData.put(compareConfig.getPrefix() + "_value", tValue);
                            }
                            if (mValue != null && tValue != null) {
                                double r = mValue / tValue;
                                if (!Double.isNaN(r) && !Double.isInfinite(r)) {
                                    deriveData.put(compareConfig.getPrefix() + "_rate", mValue / tValue - 1);
                                }
                            }
                        }
                    });
                }
        );
    }
}
