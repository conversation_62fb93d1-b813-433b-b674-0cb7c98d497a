package com.ctrip.tour.business.dashboard.grpBusiness.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Sensitive;
import com.ctrip.platform.dal.dao.annotation.Type;
import java.sql.Types;
import java.sql.Timestamp;
import java.util.List;

import com.ctrip.platform.dal.dao.DalPojo;

/**
 * <AUTHOR>
 * @date 2024-12-12
 */
@Entity
@Database(name = "TtdReportDB_W")
@Table(name = "edw_hr_emp_vacation")
public class EdwHrEmpVacation implements DalPojo {

    /**
     * 主键
     */
    @Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long id;

    /**
     * 工号
     */
	@Column(name = "emp_code")
	@Type(value = Types.VARCHAR)
	private String empCode;

    /**
     * 姓名
     */
	@Column(name = "display_name")
	@Type(value = Types.VARCHAR)
	private String displayName;

    /**
     * 域账号(邮箱前缀)
     */
	@Column(name = "domain_name")
	@Type(value = Types.VARCHAR)
	private String domainName;

    /**
     * 主管工号
     */
	@Column(name = "leader_emp_code")
	@Type(value = Types.VARCHAR)
	private String leaderEmpCode;

    /**
     * 主管姓名
     */
	@Column(name = "leader_emp_name")
	@Type(value = Types.VARCHAR)
	private String leaderEmpName;

    /**
     * 小组1组织编码-部门下一层
     */
	@Column(name = "team_id_1")
	@Type(value = Types.VARCHAR)
	private String teamId1;

    /**
     * 小组1组织名称-部门下一层
     */
	@Column(name = "team_cname_1")
	@Type(value = Types.VARCHAR)
	private String teamCname1;

    /**
     * 小组2组织编码-部门下两层
     */
	@Column(name = "team_id_2")
	@Type(value = Types.VARCHAR)
	private String teamId2;

    /**
     * 小组2组织名称-部门下两层
     */
	@Column(name = "team_cname_2")
	@Type(value = Types.VARCHAR)
	private String teamCname2;

    /**
     * 小组3组织编码-部门下三层
     */
	@Column(name = "team_id_3")
	@Type(value = Types.VARCHAR)
	private String teamId3;

    /**
     * 小组3组织名称-部门下三层
     */
	@Column(name = "team_cname_3")
	@Type(value = Types.VARCHAR)
	private String teamCname3;

    /**
     * 小组4组织编码-部门下四层
     */
	@Column(name = "team_id_4")
	@Type(value = Types.VARCHAR)
	private String teamId4;

    /**
     * 小组4组织名称-部门下四层
     */
	@Column(name = "team_cname_4")
	@Type(value = Types.VARCHAR)
	private String teamCname4;

    /**
     * 当前小组编码
     */
	@Column(name = "team_id")
	@Type(value = Types.VARCHAR)
	private String teamId;

    /**
     * 当前小组中文名
     */
	@Column(name = "team_cname")
	@Type(value = Types.VARCHAR)
	private String teamCname;

    /**
     * 组织ID路径
     */
	@Column(name = "org_id_path")
	@Type(value = Types.VARCHAR)
	private String orgIdPath;

    /**
     * 组织路径
     */
	@Column(name = "org_name_path")
	@Type(value = Types.VARCHAR)
	private String orgNamePath;

    /**
     * 岗位
     */
	@Column(name = "position")
	@Type(value = Types.VARCHAR)
	private String position;

    /**
     * 部门编码
     */
	@Column(name = "dept_id")
	@Type(value = Types.VARCHAR)
	private String deptId;

    /**
     * 部门中文
     */
	@Column(name = "dept_cname")
	@Type(value = Types.VARCHAR)
	private String deptCname;

    /**
     * 是否在职
     */
	@Column(name = "is_on_job")
	@Type(value = Types.VARCHAR)
	private String isOnJob;

    /**
     * 分区日期
     */
	@Column(name = "partition_d")
	@Type(value = Types.VARCHAR)
	private String partitionD;

	// 管理的部门
	public List<DimOrgTreeVacation> managedDept;
	public DimOrgTreeVacation dept;

    /**
     * 更新时间
     */
	@Column(name = "datachange_lasttime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getEmpCode() {
		return empCode;
	}

	public void setEmpCode(String empCode) {
		this.empCode = empCode;
	}

	public String getDisplayName() {
		return displayName;
	}

	public void setDisplayName(String displayName) {
		this.displayName = displayName;
	}

	public String getDomainName() {
		return domainName;
	}

	public void setDomainName(String domainName) {
		this.domainName = domainName;
	}

	public String getLeaderEmpCode() {
		return leaderEmpCode;
	}

	public void setLeaderEmpCode(String leaderEmpCode) {
		this.leaderEmpCode = leaderEmpCode;
	}

	public String getLeaderEmpName() {
		return leaderEmpName;
	}

	public void setLeaderEmpName(String leaderEmpName) {
		this.leaderEmpName = leaderEmpName;
	}

	public String getTeamId1() {
		return teamId1;
	}

	public void setTeamId1(String teamId1) {
		this.teamId1 = teamId1;
	}

	public String getTeamCname1() {
		return teamCname1;
	}

	public void setTeamCname1(String teamCname1) {
		this.teamCname1 = teamCname1;
	}

	public String getTeamId2() {
		return teamId2;
	}

	public void setTeamId2(String teamId2) {
		this.teamId2 = teamId2;
	}

	public String getTeamCname2() {
		return teamCname2;
	}

	public void setTeamCname2(String teamCname2) {
		this.teamCname2 = teamCname2;
	}

	public String getTeamId3() {
		return teamId3;
	}

	public void setTeamId3(String teamId3) {
		this.teamId3 = teamId3;
	}

	public String getTeamCname3() {
		return teamCname3;
	}

	public void setTeamCname3(String teamCname3) {
		this.teamCname3 = teamCname3;
	}

	public String getTeamId4() {
		return teamId4;
	}

	public void setTeamId4(String teamId4) {
		this.teamId4 = teamId4;
	}

	public String getTeamCname4() {
		return teamCname4;
	}

	public void setTeamCname4(String teamCname4) {
		this.teamCname4 = teamCname4;
	}

	public String getTeamId() {
		return teamId;
	}

	public void setTeamId(String teamId) {
		this.teamId = teamId;
	}

	public String getTeamCname() {
		return teamCname;
	}

	public void setTeamCname(String teamCname) {
		this.teamCname = teamCname;
	}

	public String getOrgIdPath() {
		return orgIdPath;
	}

	public void setOrgIdPath(String orgIdPath) {
		this.orgIdPath = orgIdPath;
	}

	public String getOrgNamePath() {
		return orgNamePath;
	}

	public void setOrgNamePath(String orgNamePath) {
		this.orgNamePath = orgNamePath;
	}

	public String getPosition() {
		return position;
	}

	public void setPosition(String position) {
		this.position = position;
	}

	public String getDeptId() {
		return deptId;
	}

	public void setDeptId(String deptId) {
		this.deptId = deptId;
	}

	public String getDeptCname() {
		return deptCname;
	}

	public void setDeptCname(String deptCname) {
		this.deptCname = deptCname;
	}

	public String getIsOnJob() {
		return isOnJob;
	}

	public void setIsOnJob(String isOnJob) {
		this.isOnJob = isOnJob;
	}

	public String getPartitionD() {
		return partitionD;
	}

	public void setPartitionD(String partitionD) {
		this.partitionD = partitionD;
	}

	public Timestamp getDatachangeLasttime() {
		return datachangeLasttime;
	}

	public void setDatachangeLasttime(Timestamp datachangeLasttime) {
		this.datachangeLasttime = datachangeLasttime;
	}

}