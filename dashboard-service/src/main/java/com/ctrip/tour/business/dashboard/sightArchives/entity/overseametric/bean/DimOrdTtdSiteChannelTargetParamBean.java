package com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.bean;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class DimOrdTtdSiteChannelTargetParamBean {
    String d;
    //业务线
    private List<String> buType;
    //考核年
    private String examineYear;
    //考核季
    private List<String> examineQuarter;
    //考核类型站点/渠道
    private String examineType;
    //考核类型值HK/TRIP增长
    private List<String> examineTypeValue;
    //考核指标类型
    private String examineMetricType;
}
