package com.ctrip.tour.business.dashboard.sightArchives.entity.domesticmetric;

import lombok.Data;

import java.util.List;

@Data
public class Domestic12ParamBean {
    // bd名称
    String domainName;
    //年份
    String year;
    //月份
    String mouth;
    //季度列表
    List<String> quarters;
    //业务线
    List<String> business;
    //开始时间
    String startDate;
    //结束时间
    String endDate;
    //指标（1gmv，2毛利）
    Integer metricType;
    //分区时间
    String d;
    // 实际业务线
    Integer businessId;
}
