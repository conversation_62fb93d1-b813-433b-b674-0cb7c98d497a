package com.ctrip.tour.business.dashboard.tktBusiness.helper;

import com.ctrip.soa._24922.*;
import com.ctrip.soa._27181.GetRawDataResponseType;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.CdmOrdTtdOverseasPerformanceIndexBO;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.DimOrdTtdDestinationTargetBO;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.DimOrdTtdPersonTargetBO;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.DimOrdTtdSiteChannelTargetBO;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.bean.DimOrdTtdDestinationTargetParamBean;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.bean.DimOrdTtdPersonTargetParamBean;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.bean.DimOrdTtdSiteChannelTargetParamBean;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.bean.OverseasPerformanceInfoParamBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.*;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.enums.BuTypeEnum;
import com.ctrip.tour.business.dashboard.tktBusiness.enums.ChannelSiteEnum;
import com.ctrip.tour.business.dashboard.tktBusiness.enums.DestinationLevelTypeEnum;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.InputArgumentException;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.ctrip.tour.business.dashboard.utils.MapperUtil;
import com.ctrip.tour.business.dashboard.utils.UserUtil;
import com.ctrip.tour.business.dashboard.utils.time.DateRange;
import com.ctrip.tour.business.dashboard.utils.time.TimeUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;


import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

public class Bus101102Helper {

    private static Map<String, String> extraDimMap = new HashMap<String, String>() {{
        put("101", "ttd_suc_income");
        put("102", "ttd_suc_subsidy_rebate_profit");
    }};

    private static Map<String, String> extraTargetMap = new HashMap<String, String>() {{
        put("101", "ttd_trgt_income");
        put("102", "ttd_trgt_profit");
    }};


    //构造目的地子指标sql请求数据(指标卡/趋势线)

    /**
     * 优先级 examineConfigBean > timeFilter 如果传入了examineConfigBean 优先从里面获取相应数据
     * @param type
     * @param timeFilter
     * @param metricInfoBean
     * @param d
     * @param subMetric
     * @param remoteConfig
     * @param examineConfigBean
     * @return
     * @throws Exception
     */
    public static SqlParamterBean getDestinationMetricCardSqlBean(String type,
                                                                  TimeFilter timeFilter,
                                                                  OverseaMetricInfoBean metricInfoBean,
                                                                  String d,
                                                                  String subMetric,
                                                                  RemoteConfig remoteConfig,
                                                                  ExamineConfigBean examineConfigBean) throws Exception {
        SqlParamterBean bean = new SqlParamterBean();
        if ("current".equals(type)) {
            bean.setId(5L);
        } else if ("target".equals(type)) {
            bean.setId(1L);
        } else {
            bean.setId(6L);
        }
        if ("mom".equals(type)) {
            List<Map<String, String>> baseMapList = OverseaMetricHelper.generateMomBaseMapList(timeFilter, d);
            Map<String, String> baseMap = baseMapList.get(0);
            OverseaMetricHelper.setDestinationValue(baseMap, subMetric, remoteConfig);
            OverseaMetricHelper.setDestinationRangeValue(baseMap, metricInfoBean, remoteConfig);
            bean.setAndMap(baseMap);
            Map<String, String> baseMap2 = baseMapList.get(1);
            OverseaMetricHelper.setDestinationValue(baseMap2, subMetric, remoteConfig);
            OverseaMetricHelper.setDestinationRangeValue(baseMap2, metricInfoBean, remoteConfig);
            bean.setAndMap2(baseMap2);
        } else {
            Map<String, String> baseMap = OverseaMetricHelper.generateBaseMap(timeFilter, type, d, examineConfigBean);
            OverseaMetricHelper.setDestinationValue(baseMap, subMetric, remoteConfig);
            OverseaMetricHelper.setDestinationRangeValue(baseMap, metricInfoBean, remoteConfig);
            bean.setAndMap(baseMap);
        }
        return bean;
    }

    //构造站点子指标sql请求数据(指标卡/趋势线)
    public static SqlParamterBean getSiteMetricCardSqlBean(String type,
                                                           TimeFilter timeFilter,
                                                           OverseaMetricInfoBean metricInfoBean,
                                                           String d,
                                                           RemoteConfig remoteConfig,
                                                           ExamineConfigBean examineConfigBean) throws Exception {
        SqlParamterBean bean = new SqlParamterBean();
        if ("current".equals(type)) {
            bean.setId(7L);
        } else if ("target".equals(type)) {
            bean.setId(2L);
        } else {
            bean.setId(9L);
        }
        if ("mom".equals(type)) {
            List<Map<String, String>> baseMapList = OverseaMetricHelper.generateMomBaseMapList(timeFilter, d);
            Map<String, String> baseMap = baseMapList.get(0);
            OverseaMetricHelper.setSiteRangeValue(baseMap, metricInfoBean, remoteConfig);
            bean.setAndMap(baseMap);
            Map<String, String> baseMap2 = baseMapList.get(1);
            OverseaMetricHelper.setSiteRangeValue(baseMap2, metricInfoBean, remoteConfig);
            bean.setAndMap2(baseMap2);
        } else {
            Map<String, String> baseMap = OverseaMetricHelper.generateBaseMap(timeFilter, type, d, examineConfigBean);
            OverseaMetricHelper.setSiteRangeValue(baseMap, metricInfoBean, remoteConfig);
            bean.setAndMap(baseMap);
        }
        return bean;
    }

    //构造渠道子指标sql请求数据(指标卡/趋势线)
    public static SqlParamterBean getChannelMetricCardSqlBean(String type,
                                                              TimeFilter timeFilter,
                                                              OverseaMetricInfoBean metricInfoBean,
                                                              String d,
                                                              RemoteConfig remoteConfig,
                                                              ExamineConfigBean examineConfigBean) throws Exception {
        SqlParamterBean bean = new SqlParamterBean();
        if ("current".equals(type)) {
            bean.setId(8L);
        } else if ("target".equals(type)) {
            bean.setId(3L);
        } else {
            bean.setId(9L);
        }
        if ("mom".equals(type)) {
            List<Map<String, String>> baseMapList = OverseaMetricHelper.generateMomBaseMapList(timeFilter, d);
            Map<String, String> baseMap = baseMapList.get(0);
            OverseaMetricHelper.setChannelRangeValue(baseMap, metricInfoBean, remoteConfig);
            bean.setAndMap(baseMap);
            Map<String, String> baseMap2 = baseMapList.get(1);
            OverseaMetricHelper.setChannelRangeValue(baseMap2, metricInfoBean, remoteConfig);
            bean.setAndMap2(baseMap2);
        } else {
            Map<String, String> baseMap = OverseaMetricHelper.generateBaseMap(timeFilter, type, d, examineConfigBean);
            OverseaMetricHelper.setChannelRangeValue(baseMap, metricInfoBean, remoteConfig);
            bean.setAndMap(baseMap);
        }
        return bean;
    }

    //构造目的地子指标sql请求数据(下钻基础数据)
    public static SqlParamterBean getDestinationDrillDownBaseInfoSqlBean(String field,
                                                                         GetOverseaDrillDownBaseInfoRequestType request,
                                                                         String d,
                                                                         OverseaMetricInfoBean metricInfoBean,
                                                                         RemoteConfig remoteConfig) throws Exception {
        SqlParamterBean bean = new SqlParamterBean();

        String metric = request.getMetric();
        String subMetric = request.getSubMetric();

        SubMetricFiledBean configBean = remoteConfig.getSubMetricFiledBean(metric, subMetric, field);

        bean.setId(configBean.getBaseInfoId());

        Map<String, String> baseMap = OverseaMetricHelper.generateBaseMap(request.getTimeFilter(), "current", d, null);
        OverseaMetricHelper.setDestinationValue(baseMap, request.getSubMetric(), remoteConfig);
        OverseaMetricHelper.setDestinationRangeValue(baseMap, metricInfoBean, remoteConfig);
        bean.setAndMap(baseMap);

        List<String> groupList = configBean.getBaseInfoGroupList();
        List<String> translateGroupList = OverseaMetricHelper.translateGroupList(groupList);
        bean.setGroupList(translateGroupList);
        bean.setOrderList(Lists.newArrayList(translateGroupList.get(0)));
        bean.setOrderTypeList(Lists.newArrayList("asc"));

        if (request.isNeedSearch()) {
            String searchWord = request.getSearchWord();
            Map<String, String> likeMap = new HashMap<>();
            List<Integer> likeIndexList = configBean.getBaseInfoLikeIndexList();
            for (Integer index : likeIndexList) {
                likeMap.put(translateGroupList.get(index), searchWord);
            }
            //由于上游的基础服务逻辑变更了 先特殊处理下有多个条件对应一个搜索词的场景

            bean.setLikeMap(convertLikeMap(likeMap));
        } else {
            //非搜索模式下对于景点数据控制返回只有前50条
            if ("viewspot".equals(field)) {
                bean.setPageNo(1);
                bean.setPageSize(50);
            }
        }

        return bean;
    }
    //构造目的地子指标sql请求数据(下钻基础数据)
    public static SqlParamterBean getDestinationDrillDownBaseInfoSqlBeanV2(String field,
                                                                         GetOverseaDrillDownBaseInfoV2RequestType request,
                                                                         String d,
                                                                           OverseaMetricInfoBean metricInfoBean,
                                                                         RemoteConfig remoteConfig) throws Exception {
        SqlParamterBean bean = new SqlParamterBean();

        String metric = request.getMetric();
        String subMetric = request.getSubMetric();

        SubMetricFiledBean configBean = remoteConfig.getSubMetricFiledBeanV2(metric, subMetric, field);

        bean.setId(configBean.getBaseInfoId());

        Map<String, String> baseMap = OverseaMetricHelper.generateBaseMap(request.getTimeFilter(), "current", d, null);
        OverseaMetricHelper.setDestinationValue(baseMap, request.getSubMetric(), remoteConfig);
        OverseaMetricHelper.setDestinationRangeValue(baseMap, metricInfoBean, remoteConfig);
        bean.setAndMap(baseMap);

        List<String> groupList = configBean.getBaseInfoGroupList();
        List<String> translateGroupList = OverseaMetricHelper.translateGroupList(groupList);
        bean.setGroupList(translateGroupList);
        bean.setOrderList(Lists.newArrayList(translateGroupList.get(0)));
        bean.setOrderTypeList(Lists.newArrayList("asc"));

        if (StringUtils.isNotEmpty(request.getSearchWord())) {
            String searchWord = request.getSearchWord();
            Map<String, String> likeMap = new HashMap<>();
            List<Integer> likeIndexList = configBean.getBaseInfoLikeIndexList();
            for (Integer index : likeIndexList) {
                likeMap.put(translateGroupList.get(index), searchWord);
            }
            //由于上游的基础服务逻辑变更了 先特殊处理下有多个条件对应一个搜索词的场景

            bean.setLikeMap(convertLikeMap(likeMap));
        } else {
            //非搜索模式下对于景点数据控制返回只有前50条
            if ("viewspot".equals(field)) {
                bean.setPageNo(1);
                bean.setPageSize(50);
            }
        }

        return bean;
    }

    public static Map<String, String> convertLikeMap(Map<String, String> likeMap) {
        Map<String, String> newMap = new HashMap<>();
        for (Map.Entry<String, String> entry : likeMap.entrySet()) {
            String value = entry.getKey();
            String key = entry.getValue();
            String originValue = newMap.get(key);
            newMap.put(key, GeneralUtil.isNotEmpty(originValue) ? originValue + "|" + value : value);
        }
        return newMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey));
    }


    //构造站点子指标sql请求数据(下钻基础数据)
    public static SqlParamterBean getSiteDrillDownBaseInfoSqlBean(String field,
                                                                  GetOverseaDrillDownBaseInfoRequestType request,
                                                                  String d,
                                                                  OverseaMetricInfoBean metricInfoBean,
                                                                  RemoteConfig remoteConfig) throws Exception {
        SqlParamterBean bean = new SqlParamterBean();

        String metric = request.getMetric();
        String subMetric = request.getSubMetric();

        SubMetricFiledBean configBean = remoteConfig.getSubMetricFiledBean(metric, subMetric, field);

        bean.setId(configBean.getBaseInfoId());

        Map<String, String> baseMap = OverseaMetricHelper.generateBaseMap(request.getTimeFilter(), "current", d, null);
        OverseaMetricHelper.setSiteRangeValue(baseMap, metricInfoBean, remoteConfig);
        bean.setAndMap(baseMap);

        List<String> groupList = configBean.getBaseInfoGroupList();
        List<String> translateGroupList = OverseaMetricHelper.translateGroupList(groupList);
        bean.setGroupList(translateGroupList);
        bean.setOrderList(Lists.newArrayList(translateGroupList.get(0)));
        bean.setOrderTypeList(Lists.newArrayList("asc"));

        if (request.isNeedSearch()) {
            String searchWord = request.getSearchWord();
            Map<String, String> likeMap = new HashMap<>();
            List<Integer> likeIndexList = configBean.getBaseInfoLikeIndexList();
            for (Integer index : likeIndexList) {
                likeMap.put(translateGroupList.get(index), searchWord);
            }
            bean.setLikeMap(likeMap);
        }

        return bean;
    }
    //构造站点子指标sql请求数据(下钻基础数据)
    public static SqlParamterBean getSiteDrillDownBaseInfoSqlBeanV2(String field,
                                                                  GetOverseaDrillDownBaseInfoV2RequestType request,
                                                                  String d,
                                                                  OverseaMetricInfoBean metricInfoBean,
                                                                  RemoteConfig remoteConfig) throws Exception {
        SqlParamterBean bean = new SqlParamterBean();

        String metric = request.getMetric();
        String subMetric = request.getSubMetric();

        SubMetricFiledBean configBean = remoteConfig.getSubMetricFiledBeanV2(metric, subMetric, field);

        bean.setId(configBean.getBaseInfoId());

        Map<String, String> baseMap = OverseaMetricHelper.generateBaseMap(request.getTimeFilter(), "current", d, null);
        OverseaMetricHelper.setSiteRangeValue(baseMap, metricInfoBean, remoteConfig);
        bean.setAndMap(baseMap);

        List<String> groupList = configBean.getBaseInfoGroupList();
        List<String> translateGroupList = OverseaMetricHelper.translateGroupList(groupList);
        bean.setGroupList(translateGroupList);
        bean.setOrderList(Lists.newArrayList(translateGroupList.get(0)));
        bean.setOrderTypeList(Lists.newArrayList("asc"));

        if (StringUtils.isNotEmpty(request.getSearchWord())) {
            String searchWord = request.getSearchWord();
            Map<String, String> likeMap = new HashMap<>();
            List<Integer> likeIndexList = configBean.getBaseInfoLikeIndexList();
            for (Integer index : likeIndexList) {
                likeMap.put(translateGroupList.get(index), searchWord);
            }
            bean.setLikeMap(likeMap);
        }

        return bean;
    }

    //构造渠道子指标sql请求数据(下钻基础数据)
    public static SqlParamterBean getChannelDrillDownBaseInfoSqlBean(String field,
                                                                     GetOverseaDrillDownBaseInfoRequestType request,
                                                                     String d,
                                                                     OverseaMetricInfoBean metricInfoBean,
                                                                     RemoteConfig remoteConfig) throws Exception {
        SqlParamterBean bean = new SqlParamterBean();

        String metric = request.getMetric();
        String subMetric = request.getSubMetric();

        SubMetricFiledBean configBean = remoteConfig.getSubMetricFiledBean(metric, subMetric, field);

        bean.setId(configBean.getBaseInfoId());

        Map<String, String> baseMap = OverseaMetricHelper.generateBaseMap(request.getTimeFilter(), "current", d, null);
        OverseaMetricHelper.setChannelRangeValue(baseMap, metricInfoBean, remoteConfig);
        bean.setAndMap(baseMap);

        List<String> groupList = configBean.getBaseInfoGroupList();
        List<String> translateGroupList = OverseaMetricHelper.translateGroupList(groupList);
        bean.setGroupList(translateGroupList);
        bean.setOrderList(Lists.newArrayList(translateGroupList.get(0)));
        bean.setOrderTypeList(Lists.newArrayList("asc"));

        if (request.isNeedSearch()) {
            String searchWord = request.getSearchWord();
            Map<String, String> likeMap = new HashMap<>();
            List<Integer> likeIndexList = configBean.getBaseInfoLikeIndexList();
            for (Integer index : likeIndexList) {
                likeMap.put(translateGroupList.get(index), searchWord);
            }
            bean.setLikeMap(likeMap);
        }

        return bean;
    }
    //构造渠道子指标sql请求数据(下钻基础数据)
    public static SqlParamterBean getChannelDrillDownBaseInfoSqlBeanV2(String field,
                                                                     GetOverseaDrillDownBaseInfoV2RequestType request,
                                                                     String d,
                                                                     OverseaMetricInfoBean metricInfoBean,
                                                                     RemoteConfig remoteConfig) throws Exception {
        SqlParamterBean bean = new SqlParamterBean();

        String metric = request.getMetric();
        String subMetric = request.getSubMetric();

        SubMetricFiledBean configBean = remoteConfig.getSubMetricFiledBeanV2(metric, subMetric, field);

        bean.setId(configBean.getBaseInfoId());

        Map<String, String> baseMap = OverseaMetricHelper.generateBaseMap(request.getTimeFilter(), "current", d, null);
        OverseaMetricHelper.setChannelRangeValue(baseMap, metricInfoBean, remoteConfig);
        bean.setAndMap(baseMap);

        List<String> groupList = configBean.getBaseInfoGroupList();
        List<String> translateGroupList = OverseaMetricHelper.translateGroupList(groupList);
        bean.setGroupList(translateGroupList);
        bean.setOrderList(Lists.newArrayList(translateGroupList.get(0)));
        bean.setOrderTypeList(Lists.newArrayList("asc"));

        if (StringUtils.isNotEmpty(request.getSearchWord())) {
            String searchWord = request.getSearchWord();
            Map<String, String> likeMap = new HashMap<>();
            List<Integer> likeIndexList = configBean.getBaseInfoLikeIndexList();
            for (Integer index : likeIndexList) {
                likeMap.put(translateGroupList.get(index), searchWord);
            }
            bean.setLikeMap(likeMap);
        }

        return bean;
    }
    //构造目的地子指标sql请求数据
    //下钻数据  包括气泡图  和  下钻表格
    //包括首页和详情页

    /**
     *
     * @param request
     * @param d
     * @param metricInfoBean
     * @param configBean
     * @param remoteConfig
     * @param type
     * @param pagingFieldValueList 计算同比数据时需要限制的范围 计算环比数据时不使用此限制
     * @return
     * @throws Exception
     */

    public static SqlParamterBean getDestinationTableDataSqlBean(GetOverseaTableDataRequestType request,
                                                                 String d,
                                                                 OverseaMetricInfoBean metricInfoBean,
                                                                 SubMetricFiledBean configBean,
                                                                 RemoteConfig remoteConfig,
                                                                 String type,
                                                                 List<String> pagingFieldValueList) throws Exception {
        SqlParamterBean bean = new SqlParamterBean();

        String metric = request.getMetric();

        setDrillDownIdAndGroupListForSqlBean(request, bean, configBean, type);

        TimeFilter timeFilter = request.getTimeFilter();
        String subMetric = request.getSubMetric();
        String pagingConditionColumn = configBean.getPagingConditionColumn();

        //特殊逻辑 用于处理商拓下钻获取同比数据的场景
        if (GeneralUtil.isNotEmpty(pagingConditionColumn) && ("lastyear".equals(type) || "2019".equals(type))) {
            Map<String, String> baseMap = OverseaMetricHelper.generateBaseMap(timeFilter, type, d, null);
            OverseaMetricHelper.setPagingConditionValue(baseMap, pagingConditionColumn, pagingFieldValueList);
            bean.setAndMap(baseMap);
            return bean;
        }

        if ("mom".equals(type)) {
            //环比仅存在于  首页的下钻
            //此时下钻维度只有大区 子区域  站点  直接查全量做匹配
            List<Map<String, String>> baseMapList = OverseaMetricHelper.generateMomBaseMapList(timeFilter, d);
            Map<String, String> baseMap = baseMapList.get(0);
            OverseaMetricHelper.setDestinationValue(baseMap, subMetric, remoteConfig);
            OverseaMetricHelper.setDestinationRangeValue(baseMap, metricInfoBean, remoteConfig);
            OverseaMetricHelper.setConditionValue(baseMap, configBean, pagingFieldValueList);
            bean.setAndMap(baseMap);
            Map<String, String> baseMap2 = baseMapList.get(1);
            OverseaMetricHelper.setDestinationValue(baseMap2, subMetric, remoteConfig);
            OverseaMetricHelper.setDestinationRangeValue(baseMap2, metricInfoBean, remoteConfig);
            OverseaMetricHelper.setConditionValue(baseMap2, configBean, pagingFieldValueList);
            bean.setAndMap2(baseMap2);
        } else {
            Map<String, String> baseMap = OverseaMetricHelper.generateBaseMap(timeFilter, type, d, null);
            OverseaMetricHelper.setDestinationValue(baseMap, subMetric, remoteConfig);
            OverseaMetricHelper.setDestinationRangeValue(baseMap, metricInfoBean, remoteConfig);

            DrillDownFilter drillDownFilter = request.getDrillDownFilter();
            //设置前端传入的条件
            OverseaMetricHelper.setConditionValue(baseMap, configBean, drillDownFilter.getFieldValueList());
            //获取当前数据时 需要设置前端传入的分页  其他数据不需要
            //同时需要设置排序条件
            if ("current".equals(type)) {
                bean.setPageNo(request.getPageNo());
                bean.setPageSize(request.getPageSize());
                bean.setOrderList(Lists.newArrayList(extraDimMap.get(metric)));
                bean.setOrderTypeList(Lists.newArrayList("desc"));
            }
            //获取同比数据时  需要设置上一步中获取当前数据得到的条件
            if ("lastyear".equals(type) || "2019".equals(type)) {
                OverseaMetricHelper.setConditionValue(baseMap, configBean, pagingFieldValueList);
            }
            bean.setAndMap(baseMap);
        }

        return bean;
    }


    //构造站点子指标sql请求数据
    //下钻数据  包括气泡图  和  下钻表格
    //包括首页和详情页

    /**
     * @param request
     * @param d
     * @param metricInfoBean
     * @param configBean
     * @param remoteConfig
     * @param type
     * @param pagingFieldValueList 计算同比数据时需要限制的范围 计算环比数据时不使用此限制
     * @return
     * @throws Exception
     */

    public static SqlParamterBean getSiteTableDataSqlBean(GetOverseaTableDataRequestType request,
                                                          String d,
                                                          OverseaMetricInfoBean metricInfoBean,
                                                          SubMetricFiledBean configBean,
                                                          RemoteConfig remoteConfig,
                                                          String type,
                                                          List<String> pagingFieldValueList) throws Exception {
        SqlParamterBean bean = new SqlParamterBean();

        String metric = request.getMetric();

        setDrillDownIdAndGroupListForSqlBean(request, bean, configBean, type);

        TimeFilter timeFilter = request.getTimeFilter();
        if ("mom".equals(type)) {
            //环比仅存在于  首页的下钻
            //此时下钻维度只有大区 子区域  站点  直接查全量做匹配
            List<Map<String, String>> baseMapList = OverseaMetricHelper.generateMomBaseMapList(timeFilter, d);
            Map<String, String> baseMap = baseMapList.get(0);
            OverseaMetricHelper.setSiteRangeValue(baseMap, metricInfoBean, remoteConfig);
            OverseaMetricHelper.setConditionValue(baseMap, configBean, pagingFieldValueList);
            bean.setAndMap(baseMap);
            Map<String, String> baseMap2 = baseMapList.get(1);
            OverseaMetricHelper.setSiteRangeValue(baseMap2, metricInfoBean, remoteConfig);
            OverseaMetricHelper.setConditionValue(baseMap2, configBean, pagingFieldValueList);
            bean.setAndMap2(baseMap2);
        } else {
            Map<String, String> baseMap = OverseaMetricHelper.generateBaseMap(timeFilter, type, d, null);
            OverseaMetricHelper.setSiteRangeValue(baseMap, metricInfoBean, remoteConfig);

            DrillDownFilter drillDownFilter = request.getDrillDownFilter();
            //设置前端传入的条件
            OverseaMetricHelper.setConditionValue(baseMap, configBean, drillDownFilter.getFieldValueList());
            //获取当前数据时 需要设置前端传入的分页  其他数据不需要
            //同时需要设置排序条件
            if ("current".equals(type)) {
                bean.setPageNo(request.getPageNo());
                bean.setPageSize(request.getPageSize());
                bean.setOrderList(Lists.newArrayList(extraDimMap.get(metric)));
                bean.setOrderTypeList(Lists.newArrayList("desc"));
            }
            //获取同比数据时  需要设置上一步中获取当前数据得到的条件
            if ("lastyear".equals(type) || "2019".equals(type)) {
                OverseaMetricHelper.setConditionValue(baseMap, configBean, pagingFieldValueList);
            }
            bean.setAndMap(baseMap);
        }

        return bean;
    }


    //构造渠道子指标sql请求数据
    //下钻数据  包括气泡图  和  下钻表格
    //包括首页和详情页

    /**
     * @param request
     * @param d
     * @param metricInfoBean
     * @param configBean
     * @param remoteConfig
     * @param type
     * @param pagingFieldValueList 计算同比数据时需要限制的范围 计算环比数据时不使用此限制
     * @return
     * @throws Exception
     */
    public static OverseasPerformanceInfoParamBean getChannelTableDataSqlBeanV2(String momType,
                                                                                GetOverseaTableDataV2RequestType request,
                                                                                String d,
                                                                                OverseaMetricInfoBean metricInfoBean,
                                                                                String year,
                                                                                RemoteConfig remoteConfig,
                                                                                List<String> channelList) throws ParseException {
        OverseasPerformanceInfoParamBean overseasPerformanceInfoParamBean = new OverseasPerformanceInfoParamBean();
        overseasPerformanceInfoParamBean.setD(d);
        overseasPerformanceInfoParamBean.setField(request.getDimName());
        overseasPerformanceInfoParamBean.setPageIndex(request.getPageNo());
        overseasPerformanceInfoParamBean.setPageSize(request.getPageSize());
        String tkt = remoteConfig.getConfigValue("tkt");
        String act = remoteConfig.getConfigValue("act");
        String odt = remoteConfig.getConfigValue("odt");
        List<String> buType = BuTypeEnum.getBuTypeList(request.getBusinessLine(), tkt, act, odt);
        overseasPerformanceInfoParamBean.setBuTypeName(buType);
        if ("30".equalsIgnoreCase(momType)) {
            //对于下钻部分，仅会有30日环比，
            String lastDay = DateUtil.getDayOfInterval(d, -1);
            String startDate = DateUtil.getDayOfInterval(lastDay, -29);
            overseasPerformanceInfoParamBean.setStartDate(startDate);
            overseasPerformanceInfoParamBean.setEndDate(lastDay);
            overseasPerformanceInfoParamBean.setQuarter(null);
            overseasPerformanceInfoParamBean.setYear(null);
        } else if ("last30".equalsIgnoreCase(momType)) {
            String momEndDate = DateUtil.getDayOfInterval(d, -31);
            String momStartDate = DateUtil.getDayOfInterval(momEndDate, -29);
            overseasPerformanceInfoParamBean.setStartDate(momStartDate);
            overseasPerformanceInfoParamBean.setEndDate(momEndDate);
            overseasPerformanceInfoParamBean.setQuarter(null);
            overseasPerformanceInfoParamBean.setYear(null);
        } else {
            if ("current".equalsIgnoreCase(year)) {
                overseasPerformanceInfoParamBean.setYear(request.getTimeFilter().getYear());
            } else if ("lastYear".equalsIgnoreCase(year)) {
                Integer yearValue = Integer.parseInt(request.getTimeFilter().getYear()) - 1;
                overseasPerformanceInfoParamBean.setYear(String.valueOf(yearValue));
                overseasPerformanceInfoParamBean.setPageIndex(null);
                overseasPerformanceInfoParamBean.setPageSize(null);
            }
            if (request.getTimeFilter().getDateType().equalsIgnoreCase("quarter")) {
                overseasPerformanceInfoParamBean.setQuarter(request.getTimeFilter().getQuarter());
            } else if (request.getTimeFilter().getDateType().equalsIgnoreCase("half")) {
                overseasPerformanceInfoParamBean.setHalfYear(request.getTimeFilter().getHalf());
            }
            if ("lastYear".equalsIgnoreCase(year)) {
                if ("channel".equalsIgnoreCase(request.getDimName())) {
                    overseasPerformanceInfoParamBean.setDisChannelName(getIntersection(overseasPerformanceInfoParamBean.getDisChannelName(), channelList));
                } else if ("site".equalsIgnoreCase(request.getDimName())) {
                    overseasPerformanceInfoParamBean.setSites(getIntersection(overseasPerformanceInfoParamBean.getSites(), channelList));
                }
            }
        }
        if (CollectionUtils.isNotEmpty(metricInfoBean.getSiteRangeList()) && !metricInfoBean.getSiteRangeList().contains("ALL")) {
            overseasPerformanceInfoParamBean.setSites(metricInfoBean.getSiteRangeList());
        }
        overseasPerformanceInfoParamBean.setDisChannelName(metricInfoBean.getChannelRangeList());

        if ("channel".equalsIgnoreCase(request.getDimName())) {
            overseasPerformanceInfoParamBean.setDisChannelName(getIntersection(overseasPerformanceInfoParamBean.getDisChannelName(), request.getDimValueList()));
        } else if ("site".equalsIgnoreCase(request.getDimName())) {
            overseasPerformanceInfoParamBean.setSites(getIntersection(overseasPerformanceInfoParamBean.getSites(), request.getDimValueList()));
        } else if ("locale".equalsIgnoreCase(request.getDimName())) {
            overseasPerformanceInfoParamBean.setLocale(request.getDimValueList());
        }

        return overseasPerformanceInfoParamBean;

    }
    public static SqlParamterBean getChannelTableDataSqlBean(GetOverseaTableDataRequestType request,
                                                             String d,
                                                             OverseaMetricInfoBean metricInfoBean,
                                                             SubMetricFiledBean configBean,
                                                             RemoteConfig remoteConfig,
                                                             String type,
                                                             List<String> pagingFieldValueList) throws Exception {
        SqlParamterBean bean = new SqlParamterBean();

        String metric = request.getMetric();

        setDrillDownIdAndGroupListForSqlBean(request, bean, configBean, type);

        TimeFilter timeFilter = request.getTimeFilter();
        if ("mom".equals(type)) {
            //环比仅存在于  首页的下钻
            //此时下钻维度只有大区 子区域  站点  直接查全量做匹配
            List<Map<String, String>> baseMapList = OverseaMetricHelper.generateMomBaseMapList(timeFilter, d);
            Map<String, String> baseMap = baseMapList.get(0);
            OverseaMetricHelper.setChannelRangeValue(baseMap, metricInfoBean, remoteConfig);
            OverseaMetricHelper.setConditionValue(baseMap, configBean, pagingFieldValueList);
            bean.setAndMap(baseMap);
            Map<String, String> baseMap2 = baseMapList.get(1);
            OverseaMetricHelper.setChannelRangeValue(baseMap2, metricInfoBean, remoteConfig);
            OverseaMetricHelper.setConditionValue(baseMap2, configBean, pagingFieldValueList);
            bean.setAndMap2(baseMap2);
        } else {
            Map<String, String> baseMap = OverseaMetricHelper.generateBaseMap(timeFilter, type, d, null);
            OverseaMetricHelper.setChannelRangeValue(baseMap, metricInfoBean, remoteConfig);

            DrillDownFilter drillDownFilter = request.getDrillDownFilter();
            //设置前端传入的条件
            OverseaMetricHelper.setConditionValue(baseMap, configBean, drillDownFilter.getFieldValueList());
            //获取当前数据时 需要设置前端传入的分页  其他数据不需要
            //同时需要设置排序条件
            if ("current".equals(type)) {
                bean.setPageNo(request.getPageNo());
                bean.setPageSize(request.getPageSize());
                bean.setOrderList(Lists.newArrayList(extraDimMap.get(metric)));
                bean.setOrderTypeList(Lists.newArrayList("desc"));
            }
            //获取同比数据时  需要设置上一步中获取当前数据得到的条件
            if ("lastyear".equals(type) || "2019".equals(type)) {
                OverseaMetricHelper.setConditionValue(baseMap, configBean, pagingFieldValueList);
            }
            bean.setAndMap(baseMap);
        }

        return bean;
    }


    /**
     * 下钻场景设置sqlbean的一些公共信息
     * id和groupList
     * @param request
     * @param bean
     * @param configBean
     * @param type
     */
    private static void setDrillDownIdAndGroupListForSqlBean(GetOverseaTableDataRequestType request,
                                                             SqlParamterBean bean,
                                                             SubMetricFiledBean configBean,
                                                             String type){

        Long otherId = configBean.getTableDataIdMap().get("other");
        Long id = configBean.getTableDataIdMap().get(type);
        bean.setId(GeneralUtil.isEmpty(id) ? otherId : id);

        String source = request.getSource();
        String queryType = request.getQueryType();
        if ("firstpage".equals(source) || ("detailpage".equals(source) && "bubble".equals(queryType))) {
            //首页或者详情页气泡图
            List<String> otherGroupList = configBean.getBubbleGroupListMap().get("other");
            List<String> groupList = configBean.getBubbleGroupListMap().get(type);
            bean.setGroupList(GeneralUtil.isEmpty(groupList) ? OverseaMetricHelper.translateGroupList(otherGroupList)
                    : OverseaMetricHelper.translateGroupList(groupList));
        } else {
            //详情页表格
            List<String> otherGroupList = configBean.getTableGroupListMap().get("other");
            List<String> groupList = configBean.getTableGroupListMap().get(type);
            bean.setGroupList(GeneralUtil.isEmpty(groupList) ? OverseaMetricHelper.translateGroupList(otherGroupList)
                    : OverseaMetricHelper.translateGroupList(groupList));
        }
    }




    //填充指标卡基础数据
    public static void processMetricCardBaseData(GetRawDataResponseType currentRes,
                                                 GetRawDataResponseType targetRes,
                                                 Map<String, Double> dimMap,
                                                 String metric) {
        ChartHelper.fillOverallDimMap(MapperUtil.str2ListList(currentRes.getResult(), Object.class), currentRes.getMetricList(), dimMap);
        ChartHelper.fillOverallDimMap(MapperUtil.str2ListList(targetRes.getResult(), Object.class), targetRes.getMetricList(), dimMap);
        //计算完成率
        String extraDim = extraDimMap.get(metric) + "_complete_rate";
        dimMap.put(extraDim, DimHelper.getSpecialDimValue(extraDim, "", dimMap, new HashMap<>()));
    }

    //填充指标卡同比数据
    public static void processMetricCardPopData(GetRawDataResponseType popRes,
                                                Map<String, Double> dimMap,
                                                String metric,
                                                String type) {
        Map<String, Double> popMap = new HashMap<>();
        ChartHelper.fillOverallDimMap(MapperUtil.str2ListList(popRes.getResult(), Object.class), popRes.getMetricList(), popMap);
        //计算同比
        String extraDim = extraDimMap.get(metric) + "_" + type;
        dimMap.put(extraDim, DimHelper.getSpecialDimValue(extraDim, "", dimMap, popMap));
    }

    //填充指标卡环比数据
    public static void processMetricCardMomData(GetRawDataResponseType momRes,
                                                GetRawDataResponseType momRes2,
                                                Map<String, Double> dimMap,
                                                String metric,
                                                String momType) {
        Map<String, Double> momMap = new HashMap<>();
        Map<String, Double> momMap2 = new HashMap<>();
        ChartHelper.fillOverallDimMap(MapperUtil.str2ListList(momRes.getResult(), Object.class), momRes.getMetricList(), momMap);
        ChartHelper.fillOverallDimMap(MapperUtil.str2ListList(momRes2.getResult(), Object.class), momRes2.getMetricList(), momMap2);
        //计算环比
        String extraDim = extraDimMap.get(metric) + "_" + momType;
        dimMap.put(extraDim, DimHelper.getSpecialDimValue(extraDim, "", momMap, momMap2));
    }


    //填充趋势线完成率数据
    public static void processTrendLineBaseData(List<TrendLineDetailInfo> trendLineDetailInfoList,
                                                PeriodDataBean periodDataBean,
                                                List<String> timeList,
                                                String metric) {
        Map<String, Double> dimMap = new HashMap<>();
        Set<String> drillDownSet = new HashSet<>();
        List<List<Object>> reachList = periodDataBean.getReachList();
        List<List<Object>> targetList = periodDataBean.getTargetList();
        List<String> reachHeaderList = periodDataBean.getReachHeaderList();
        List<String> targetHeaderList = periodDataBean.getTargetHeaderList();
        List<String> reachDimList = reachHeaderList.subList(1, reachHeaderList.size());
        List<String> targetDimList = targetHeaderList.subList(1, targetHeaderList.size());

        ChartHelper.setMonthlyMetricTrendlineActualReach(dimMap, drillDownSet, reachList, Lists.newArrayList("time"), reachDimList);
        ChartHelper.setMonthlyMetricTrendlineActualReach(dimMap, drillDownSet, targetList, Lists.newArrayList("time"), targetDimList);

        String dim = extraDimMap.get(metric);
        Map<String, String> typeMap = new HashMap<>();
        typeMap.put(dim, "barChart");
        typeMap.put(dim + "_complete_rate", "lineChart");

        ChartHelper.fillLineChartTrendLineData(null, timeList, dimMap,
                trendLineDetailInfoList, typeMap);

    }

    //填充趋势线同比数据
    public static void processTrendLinePopData(List<TrendLineDetailInfo> trendLineDetailInfoList,
                                               PeriodDataBean periodDataBean,
                                               List<String> timeList,
                                               String metric,
                                               String popType) {
        Map<String, Double> dimMap = new HashMap<>();
        Set<String> drillDownSet = new HashSet<>();
        List<List<Object>> popList;
        List<String> popDimList;
        if ("lastyear".equals(popType)) {
            popList = periodDataBean.getLastyearList();
            List<String> popHeaderList = periodDataBean.getLastyearHeaderList();
            popDimList = popHeaderList.subList(1, popHeaderList.size());
        } else {
            popList = periodDataBean.get2019List();
            List<String> popHeaderList = periodDataBean.get2019HeaderList();
            popDimList = popHeaderList.subList(1, popHeaderList.size());
        }
        popDimList = popDimList
                .stream()
                .map(i -> i + "_" + popType)
                .collect(Collectors.toList());
        ChartHelper.setMonthlyMetricTrendlineActualReach(dimMap, drillDownSet, popList, Lists.newArrayList("time"), popDimList);
        String dim = extraDimMap.get(metric);
        Map<String, String> typeMap = new HashMap<>();
        typeMap.put(dim + "_" + popType, "lineChart");
        ChartHelper.fillLineChartTrendLineData(null, timeList, dimMap, trendLineDetailInfoList, typeMap);
    }

    //填充下钻基础数据
    public static void processDrillDownBaseInfo(GetOverseaDrillDownBaseInfoRequestType request,
                                                String field,
                                                GetRawDataResponseType response,
                                                FieldDataItem fieldDataItem,
                                                RemoteConfig remoteConfig) {
        String metric = request.getMetric();
        String subMetric = request.getSubMetric();
        SubMetricFiledBean configBean = remoteConfig.getSubMetricFiledBean(metric, subMetric, field);
        fieldDataItem.setField(field);
        fieldDataItem.setNeedBubble(configBean.getNeedBubble());
        fieldDataItem.setNeedLine(configBean.getNeedLine());
        ChartHelper.fillFieldDataItem(MapperUtil.str2ListList(response.getResult(), Object.class), fieldDataItem);
    }

    //填充下钻基础数据
    public static void processDrillDownBaseInfoV2(GetOverseaDrillDownBaseInfoV2RequestType request,
                                                String field,
                                                GetRawDataResponseType response,
                                                  DilldownDim fieldDataItem,
                                                RemoteConfig remoteConfig) {
        String metric = request.getMetric();
        String subMetric = request.getSubMetric();
        SubMetricFiledBean configBean = remoteConfig.getSubMetricFiledBeanV2(metric, subMetric, field);
        fieldDataItem.setDimName(field);
        fieldDataItem.setNeedBubble(configBean.getNeedBubble());
        fieldDataItem.setNeedTrendLine(configBean.getNeedLine());

        ChartHelper.fillFieldDataItemV2(request.getMetric(),MapperUtil.str2ListList(response.getResult(), Object.class), fieldDataItem);
    }
    //填充表格基础数据
    public static void processTableBaseData(GetRawDataResponseType currentRes,
                                            GetRawDataResponseType targetRes,
                                            List<TableDataItem> tableDataItemList,
                                            String metric,
                                            Boolean needTarget) {
        List<List<Object>> currentList = MapperUtil.str2ListList(currentRes.getResult(), Object.class);
        //还原翻译字段
        List<String> currentGroupList = OverseaMetricHelper.revertGroupList(currentRes.getGroupList());
        List<String> currentMetricList = currentRes.getMetricList();

        if (needTarget) {
            List<List<Object>> targetList = MapperUtil.str2ListList(targetRes.getResult(), Object.class);
            List<String> targetGroupList = OverseaMetricHelper.revertGroupList(targetRes.getGroupList());
            List<String> targetMetricList = targetRes.getMetricList();
            ChartHelper.fillCommmonTableDataV2(tableDataItemList,
                    currentGroupList,
                    targetGroupList,
                    currentMetricList,
                    targetMetricList,
                    currentList,
                    targetList);
            //计算完成率
            String extraDim = extraDimMap.get(metric) + "_complete_rate";
            for (TableDataItem item : tableDataItemList) {
                Map<String, Double> dimMap = item.getDimMap();
                dimMap.put(extraDim, DimHelper.getSpecialDimValue(extraDim, "", dimMap, new HashMap<>()));
            }
        } else {
            ChartHelper.fillCommmonTableDataV2(tableDataItemList,
                    currentGroupList,
                    new ArrayList<>(),
                    currentMetricList,
                    new ArrayList<>(),
                    currentList,
                    new ArrayList<>());
        }
    }
    private static Double calcTargetValue(TimeFilter timeFilter, String q1,String q2,String q3,String q4) {
        String dateType = timeFilter.getDateType();
        //获取应该展示的指标列表
        List<String> quarterList = "quarter".equals(dateType) ? Collections.singletonList(timeFilter.getQuarter()) : DateUtil.getQuarterOfHalf(timeFilter.getHalf());
        Double targetValue = new Double(0);
        for (String quarter : quarterList) {
            switch (quarter.toUpperCase()) {
                case "Q1":
                    targetValue = targetValue + new Double(q1);
                    break;
                case "Q2":
                    targetValue = targetValue + new Double(q2);
                    break;
                case "Q3":
                    targetValue = targetValue + new Double(q3);
                    break;
                case "Q4":
                    targetValue = targetValue + new Double(q4);
                    break;
            }
        }
        return targetValue;
    }

    private static String buildTargetTableMapKey(String field,DimOrdTtdDestinationTargetBO bean) {
        StringBuilder key = new StringBuilder();
        //景点、国家没有目标值，商拓在另一个处理文件里
        switch (field) {
            case "region":
                key.append(bean.getBusinessRegionName()).append(":");
                break;
            case "province":
                key.append(bean.getBusinessRegionName()).append(":");
                key.append(bean.getBusinessSubRegionName()).append(":");
                break;
        }
        return key.toString();
    }

    private static void buildCommonRow(OverseaMetricInfoBean metricInfoBean,OverseaTableDataRow row,
                                       CdmOrdTtdOverseasPerformanceIndexBO bean, List<String> headerList) {
        //这里根据下钻维度查出了表头部分数据，根据表头数据set具体的值，不一定每个值都展示，都set，只set表头数据部分

        String vbkLocale = UserUtil.getVbkLocale();
        Boolean userZhCn = "zh-CN".equalsIgnoreCase(vbkLocale);
        for (String header : headerList) {
            switch (header) {
                case "region":
                    row.setRegionId(bean.getBuRegionId());
                    row.setRegion(userZhCn ? bean.getBuRegionNames() : bean.getBuRegionNameEn());
                    break;
                case "subRegion":
                    row.setSubRegionId(bean.getBuSubRegionId());
                    row.setSubRegion(userZhCn ? bean.getBuSubRegionNames() : bean.getBuSubRegionNameEn());
                    break;
                case "country":
                    if (metricInfoBean.getDestinationLevel().equalsIgnoreCase("国家")) {//NOSONAR
                        row.setCountry(userZhCn ? bean.getCtryName() : bean.getCtryNameEn());
                    } else if (metricInfoBean.getDestinationLevel().equalsIgnoreCase("省份")) {//NOSONAR
                        row.setCountry(userZhCn ? bean.getProvName() : bean.getProvNameEn());
                    } else if (metricInfoBean.getDestinationLevel().equalsIgnoreCase("城市")) {//NOSONAR
                        row.setCountry(userZhCn ? bean.getCityName() : bean.getCityNameEn());
                    } else {
                        row.setCountry(userZhCn ? bean.getCtryName() : bean.getCtryNameEn());
                    }
                    break;
                case "examinee":
                    row.setExaminee(bean.getDomainName());
                    break;
                case "viewspot":
                    row.setViewspotId(bean.getVstId().intValue());
                    row.setViewspot(userZhCn ? bean.getVstName() : bean.getVstNameEn());
                    break;
                case "site":
                    row.setSite(bean.getSites());
                    break;
                case "locale":
                    row.setLocale(bean.getLocale());
                    break;
                case "channel":
                    row.setChannel(bean.getDisChannelName());
                    break;
                case "vendorId":
                    row.setVendorId(bean.getVendId());
                    break;
                case "vendorName":
                    row.setVendorName(bean.getVendName());
                    break;
            }
        }
    }

    //填充表格同比数据
    public static void processTablePopData(GetRawDataResponseType popRes,
                                           List<TableDataItem> tableDataItemList,
                                           String type) {
        List<List<Object>> popList = MapperUtil.str2ListList(popRes.getResult(), Object.class);
        //还原翻译字段
        List<String> popGroupList = OverseaMetricHelper.revertGroupList(popRes.getGroupList());
        List<String> popMetricList = popRes.getMetricList();
        ChartHelper.fillTableSingleDimPopData(tableDataItemList,
                popList,
                popGroupList,
                "_" + type,
                popMetricList,
                "");
    }


    //填充指标卡环比数据
    public static void processTableMomData(GetRawDataResponseType momRes,
                                           GetRawDataResponseType momRes2,
                                           List<TableDataItem> tableDataItemList,
                                           String momType) {
        List<List<Object>> momList = MapperUtil.str2ListList(momRes.getResult(), Object.class);
        List<List<Object>> mom2List = MapperUtil.str2ListList(momRes2.getResult(), Object.class);
        List<String> groupList = OverseaMetricHelper.revertGroupList(momRes.getGroupList());
        List<String> metricList = momRes.getMetricList();
        ChartHelper.makeUpTableMomData(tableDataItemList,
                momList,
                mom2List,
                metricList,
                groupList,
                "_" + momType);
    }



    //对于指标卡 获取实际计算的子指标类型
    //除了孙磊(层级海外)以外 不存在同时需要考核站点和渠道的人
    public static String getActualSubMetric(String subMetric,
                                            OverseaMetricInfoBean metricInfoBean) {

        if ("site".equals(subMetric) && GeneralUtil.isNotEmpty(metricInfoBean.getChannelRangeList())) {
            return "channel";
        }
        return subMetric;
    }
    //生成下钻表格表头
    public static List<String> getTableHeaderListV2(SubMetricFiledBean configBean,
                                                  Boolean needTarget) {
        List<String> tableHeaderList = new ArrayList<>();
        tableHeaderList.addAll(configBean.getHeaderFieldList());
        if (needTarget) {
            tableHeaderList.add("targetValue");
        }
        tableHeaderList.add("completeValue");
        tableHeaderList.add("yoyValue");
        if (needTarget) {
            tableHeaderList.add("completeRate");
        }
        return tableHeaderList;
    }
    //生成下钻表格表头
    public static List<String> getTableHeaderList(SubMetricFiledBean configBean,
                                                  String metirc,
                                                  Boolean needTarget) {
        List<String> tableHeaderList = new ArrayList<>();
        tableHeaderList.addAll(configBean.getHeaderFieldList());
        if (needTarget) {
            tableHeaderList.add(extraTargetMap.get(metirc));
        }
        String dim = extraDimMap.get(metirc);
        tableHeaderList.add(dim);
        tableHeaderList.add(dim + "_lastyear");
        tableHeaderList.add(dim + "_30day");
        if (needTarget) {
            tableHeaderList.add(dim + "_complete_rate");
        }
        return tableHeaderList;
    }


    //填充指标卡所需的下钻信息(首页需要)
    public static void setMetricCardDrillDownParmater(MetricDetailInfo metricDetailInfo,
                                                      String subMetric,
                                                      OverseaMetricInfoBean metricInfoBean,
                                                      RemoteConfig remoteConfig) {
        String oversea = remoteConfig.getConfigValue("oversea");
        String region = remoteConfig.getConfigValue("region");
        String subRegion = remoteConfig.getConfigValue("subRegion");
        String destinationLevel = metricInfoBean.getDestinationLevel();
        metricDetailInfo.setNeedDrillDown(false);
        //在前端指标卡上能露出的渠道(实际前端名为站点)都不能下钻
        if ("channel".equals(subMetric)) {
            metricDetailInfo.setNeedDrillDown(false);
            return;
        }
        if ("site".equals(subMetric)) {
            if(oversea.equals(destinationLevel)){
                metricDetailInfo.setNeedDrillDown(true);
                metricDetailInfo.setDefaultField("site");
                return;
            }else{
                List<String> siteList = metricInfoBean.getSiteRangeList();
                if (GeneralUtil.isNotEmpty(siteList) && siteList.size() > 1) {
                    metricDetailInfo.setNeedDrillDown(true);
                    metricDetailInfo.setDefaultField("site");
                    return;
                }
            }

        }

        //竞品的子策略虽然是客路和飞猪
        //但是考核配置字段沿用的还是目的地的考核配置字段 因此特殊处理下该判断
        if (subMetric.startsWith("destination") || subMetric.equals("tfly") || subMetric.equals("tklk")) {
            List<String> destinationRangeList = metricInfoBean.getDestinationRangeList();
            //海外  看大区
            if (oversea.equals(destinationLevel)) {
                metricDetailInfo.setNeedDrillDown(true);
                metricDetailInfo.setDefaultField("region");
            } else if (region.equals(destinationLevel)) {
                //多个大区 看大区
                //一个大区 看子区域
                if (GeneralUtil.isNotEmpty(destinationRangeList) && destinationRangeList.size() > 1) {
                    metricDetailInfo.setNeedDrillDown(true);
                    metricDetailInfo.setDefaultField("region");
                }
                if (GeneralUtil.isNotEmpty(destinationRangeList) && destinationRangeList.size() == 1) {
                    metricDetailInfo.setNeedDrillDown(true);
                    metricDetailInfo.setDefaultField("province");
                }
            } else if (subRegion.equals(destinationLevel)) {
                //多个子区域 看子区域
                if (GeneralUtil.isNotEmpty(destinationRangeList) && destinationRangeList.size() >= 1) {
                    metricDetailInfo.setNeedDrillDown(true);
                    metricDetailInfo.setDefaultField("province");
                }
            }
        }
    }

    /**
     * 填充指标卡所需的下钻信息(首页需要)
     * @param oveaseaSubMetric
     * @param subMetric
     * @param metricInfoBean
     * @param remoteConfig
     */
    public static void setMetricCardDrillDownParmaterV2(OveaseaSubMetric oveaseaSubMetric,
                                                        String subMetric,
                                                        OverseaMetricInfoBeanV2 metricInfoBean,
                                                        RemoteConfig remoteConfig,
                                                        TimeFilter timeFilter) {
        String oversea = remoteConfig.getConfigValue("oversea");
        String region = remoteConfig.getConfigValue("region");
        String subRegion = remoteConfig.getConfigValue("subRegion");
        OverseaMetricInfoBeanV2Child child = selectLevelAndRange(timeFilter, metricInfoBean);
        // 目的地考核层级（不包括渠道站点），且国家及以下就返回other
        String destinationLevel = OverseaMetricHelper.getDestinationLevelWithOverseaMetricChild(child);
        // 目的地考核范围，为上面的destinationLevel匹配的考核范围
        List<String> destinationRange = Optional.ofNullable(child.getDestinationLevelMap().get(destinationLevel)).orElse(Collections.emptyList());
        // 站点考核范围，为站点key对应的考核范围
        List<String> siteRange = Optional.ofNullable(child.getDestinationLevelMap().get(ChannelSiteEnum.SITE.getName())).orElse(Collections.emptyList());
        oveaseaSubMetric.setNeedDrillDown(false);
        //在前端指标卡上能露出的渠道(实际前端名为站点)都不能下钻
        if ("channel".equals(subMetric)) {
            oveaseaSubMetric.setNeedDrillDown(false);
            return;
        }
        if ("site".equals(subMetric)) {
            if (oversea.equals(destinationLevel)) {
                oveaseaSubMetric.setNeedDrillDown(true);
                oveaseaSubMetric.setDefaultField("site");
                return;
            } else {
                if (GeneralUtil.isNotEmpty(siteRange)) {
                    if (siteRange.size() > 1 ||
                            (siteRange.size() == 1 && siteRange.get(0).equals("ALL"))) {
                        //多个站点或者单个站点包含全部需要下钻
                        oveaseaSubMetric.setNeedDrillDown(true);
                        oveaseaSubMetric.setDefaultField("site");
                        return;
                    }
                }
            }
        }

        //竞品的子策略虽然是客路和飞猪
        //但是考核配置字段沿用的还是目的地的考核配置字段 因此特殊处理下该判断
        if (subMetric.startsWith("destination") || subMetric.equals("tfly") || subMetric.equals("tklk")) {
            //海外  看大区
            if (oversea.equals(destinationLevel)) {
                oveaseaSubMetric.setNeedDrillDown(true);
                oveaseaSubMetric.setDefaultField("region");
            } else if (region.equals(destinationLevel)) {
                //多个大区 看大区
                //一个大区 看子区域
                if (GeneralUtil.isNotEmpty(destinationRange) && destinationRange.size() > 1) {
                    oveaseaSubMetric.setNeedDrillDown(true);
                    oveaseaSubMetric.setDefaultField("region");
                }
                if (GeneralUtil.isNotEmpty(destinationRange) && destinationRange.size() == 1) {
                    oveaseaSubMetric.setNeedDrillDown(true);
                    oveaseaSubMetric.setDefaultField("province");
                }
            } else if (subRegion.equals(destinationLevel)) {
                //多个子区域才展示下钻
                if (GeneralUtil.isNotEmpty(destinationRange) && destinationRange.size() > 1) {
                    oveaseaSubMetric.setNeedDrillDown(true);
                    oveaseaSubMetric.setDefaultField("province");
                }
            }
        }
    }

    /**
     * 获取和季度匹配的考核层级和考核范围
     * @param timeFilter
     * @param metricInfoBeanV2
     * @return
     */
    public static OverseaMetricInfoBeanV2Child selectLevelAndRange(TimeFilter timeFilter, OverseaMetricInfoBeanV2 metricInfoBeanV2) {
        String quarter = DateUtil.selectQuarterWithDiffTimeType(timeFilter);
        return metricInfoBeanV2.getQuarterInfoMap().get(quarter) != null ?
                metricInfoBeanV2.getQuarterInfoMap().get(quarter) :
                new OverseaMetricInfoBeanV2Child();
    }

    public static List<String> getDrillDownFieldList(String subMetric,
                                                     OverseaMetricInfoBean metricInfoBean,
                                                     RemoteConfig remoteConfig) {
        String oversea = remoteConfig.getConfigValue("oversea");
        String region = remoteConfig.getConfigValue("region");
        String subRegion = remoteConfig.getConfigValue("subRegion");
        String destinationLevel = metricInfoBean.getDestinationLevel();
        if ("channel".equals(subMetric)) {
            //是海外 则可以按渠道、站点、语言站点下钻
            //不是则只能按照站点、语言站点下钻
            if (oversea.equals(destinationLevel)) {
                return Lists.newArrayList("channel", "site", "locale");
            } else {
                return Lists.newArrayList("site", "locale");
            }
        }
        if ("site".equals(subMetric)) {
            List<String> siteList = metricInfoBean.getSiteRangeList();
            //是海外或者多个站点  则可以按照站点或者语言站点下钻
            if (oversea.equals(destinationLevel)) {
                return Lists.newArrayList("site", "locale");
            }
            if (GeneralUtil.isNotEmpty(siteList)) {
                if (siteList.size() > 1||"ALL".equalsIgnoreCase(siteList.get(0))) {
                    return Lists.newArrayList("site", "locale");
                } else {
                    return Lists.newArrayList("locale");
                }
            }
        }
        if (subMetric.startsWith("destination")) {
            //海外  大区、子区域、国家/地区、商拓、景点
            //多个大区  大区、子区域、国家/地区、商拓、景点
            //一个大区  子区域、国家/地区、商拓、景点
            //多个子区域 子区域、国家/地区、商拓、景点
            //一个子区域 国家/地区、商拓、景点
            if (oversea.equals(destinationLevel)) {
                return Lists.newArrayList("region", "province", "country", "examinee", "viewspot");
            }
            List<String> destinationRangeList = metricInfoBean.getDestinationRangeList();
            if (GeneralUtil.isNotEmpty(destinationRangeList)) {
                if (region.equals(destinationLevel) && destinationRangeList.size() > 1) {
                    return Lists.newArrayList("region", "province", "country", "examinee", "viewspot");
                }

                if (region.equals(destinationLevel) && destinationRangeList.size() == 1) {
                    return Lists.newArrayList("province", "country", "examinee", "viewspot");
                }

                if (subRegion.equals(destinationLevel) && destinationRangeList.size() > 1) {
                    return Lists.newArrayList("province", "country", "examinee", "viewspot");
                }

                if (subRegion.equals(destinationLevel) && destinationRangeList.size() == 1) {
                    return Lists.newArrayList("country", "examinee", "viewspot");
                }
            }
        }
        throw new InputArgumentException("invalid subMetric:" + subMetric);
    }

    public static List<String> getDrillDownFieldListV2(String subMetric,
                                                       OverseaMetricInfoBean metricInfoBean,
                                                       RemoteConfig remoteConfig) {
        String oversea = remoteConfig.getConfigValue("oversea");
        String region = remoteConfig.getConfigValue("region");
        String subRegion = remoteConfig.getConfigValue("subRegion");
        //国家、地区、省份、城市
        String country = remoteConfig.getConfigValue("country");
        //景点经理、景点助理、景点
        String spot = remoteConfig.getConfigValue("spot");
        Set<String> result = new HashSet<>();
        String destinationLevel = metricInfoBean.getDestinationLevel();
        if ("channel".equals(subMetric)) {
            //是海外 则可以按渠道、站点、语言站点下钻
            //不是则只能按照站点、语言站点下钻
            if (oversea.equals(destinationLevel)) {
                return Lists.newArrayList("channel", "site", "locale");
            } else {
                return Lists.newArrayList("site", "locale");
            }
        }
        if ("site".equals(subMetric)) {
            List<String> siteList = metricInfoBean.getSiteRangeList();
            //是海外或者多个站点  则可以按照站点或者语言站点下钻
            if (oversea.equals(destinationLevel)) {
                return Lists.newArrayList("site", "locale");
            }
            if (GeneralUtil.isNotEmpty(siteList)) {
                if (siteList.size() > 1||"ALL".equalsIgnoreCase(siteList.get(0))) {
                    return Lists.newArrayList("site", "locale");
                } else {
                    return Lists.newArrayList("locale");
                }
            }
        }
        if (subMetric.startsWith("destination")) {
            //海外  大区、子区域、国家/地区、商拓、景点
            //多个大区  大区、子区域、国家/地区、商拓、景点
            //一个大区  子区域、国家/地区、商拓、景点
            //多个子区域 子区域、国家/地区、商拓、景点
            //一个子区域 国家/地区、商拓、景点
            if (oversea.equals(destinationLevel)) {
                return Lists.newArrayList("region", "province", "country", "examinee", "viewspot");
            }
            List<String> destinationRangeList = metricInfoBean.getDestinationRangeList();
            if (GeneralUtil.isNotEmpty(destinationRangeList)) {
                //多个大区，展示大区
                if (region.equals(destinationLevel) && destinationRangeList.size() > 1) {
                    return Lists.newArrayList("region", "province", "country", "examinee", "viewspot");
                }
//只有一个大区，展示子区域
                if (region.equals(destinationLevel) && destinationRangeList.size() == 1) {
                    return Lists.newArrayList("province", "country", "examinee", "viewspot");
                }
//考核层级是子区域，多个子区域，展示子区域
                if (subRegion.equals(destinationLevel) && destinationRangeList.size() > 1) {
                    return Lists.newArrayList("province", "country", "examinee", "viewspot");
                }
//考核层级是子区域，1个子区域，展示子国家
                if (subRegion.equals(destinationLevel) && destinationRangeList.size() == 1) {
                    return Lists.newArrayList("country", "examinee", "viewspot");
                }
//考核层级是国家、地区、省份、城市，1个子区域，展示子国家
                if (country.contains(destinationLevel) && destinationRangeList.size() > 1) {
                    return Lists.newArrayList("country", "viewspot");
                }
                if (country.contains(destinationLevel) && destinationRangeList.size() == 1) {
                    return Lists.newArrayList("viewspot");
                }
//考核层级是景点经理、景点助理、景点，展示景点
                if (spot.contains(destinationLevel)) {
                    return Lists.newArrayList("viewspot");
                }
            }

        }
        return new ArrayList<>(result);
    }
    /**
     * 构建GMV/毛利 海外 人维度 目标请求参数构建
     * @param timeFilter
     * @param subMetric
     * @return
     */
    public static OverseasRelatedSearchParamBean generate101102110PersonTargetOverseaInfoSearch(TimeFilter timeFilter,String subMetric, String metric, String domainName, List<String> buType,String quarter, String d){
        String year = timeFilter.getYear();
        // H1获取Q1和Q2，H3获取Q3和Q4，季度维度保持原状
        OverseasRelatedSearchParamBean overseasRelatedSearchParamBean = new OverseasRelatedSearchParamBean();
        overseasRelatedSearchParamBean.setQuarter(quarter);
        overseasRelatedSearchParamBean.setYear(year);
        overseasRelatedSearchParamBean.setBuTypeNames(buType);
        overseasRelatedSearchParamBean.setExamineMetricType(metric);
        overseasRelatedSearchParamBean.setCt(OverseaMetricHelper.judgeCT(subMetric));
        overseasRelatedSearchParamBean.setDomainName(domainName);
        overseasRelatedSearchParamBean.setD(d);
        return overseasRelatedSearchParamBean;
    }

    /**
     * 构建GMV/毛利 海外 组织维度 目的地请求参数构建
     * @param timeFilter
     * @param subMetric
     * @return
     */
    public static OverseasRelatedSearchParamBean generate101102110DestinationTargetOverseaInfoSearch(TimeFilter timeFilter,
                                                                                                     String subMetric,
                                                                                                     OverseaPersonConfigResponse overseaPersonConfigResponse,
                                                                                                     List<String> buType,
                                                                                                     String metric,
                                                                                                     String quarter,
                                                                                                     String d){
        String year = timeFilter.getYear();
        OverseasRelatedSearchParamBean overseasRelatedSearchParamBean = new OverseasRelatedSearchParamBean();
        overseasRelatedSearchParamBean.setBuTypeNames(buType);
        overseasRelatedSearchParamBean.setYear(year);
        if (overseaPersonConfigResponse.getDestinationLevelList().stream().anyMatch(DestinationLevelTypeEnum.SUB_REGION.getName()::equals)) {
            overseasRelatedSearchParamBean.setBusinessSubRegionNames(overseaPersonConfigResponse.getDestinationRangeList());
        }
        if (overseaPersonConfigResponse.getDestinationLevelList().stream().anyMatch(DestinationLevelTypeEnum.REGION.getName()::equals)) {
            overseasRelatedSearchParamBean.setBusinessRegionName(overseaPersonConfigResponse.getDestinationRangeList());
        }
        overseasRelatedSearchParamBean.setExamineMetricType(metric);
        overseasRelatedSearchParamBean.setQuarter(quarter);
        overseasRelatedSearchParamBean.setCt(OverseaMetricHelper.judgeCT(subMetric));
        overseasRelatedSearchParamBean.setD(d);
        return overseasRelatedSearchParamBean;
    }

    /**
     * 构建GMV/毛利/票量 海外 组织维度 站点渠道请求参数构建
     * @return
     */
    public static OverseasRelatedSearchParamBean generate101102110SiteChannelTargetOverseaInfoSearch(String quarter,
                                                                                                     String year,
                                                                                                     List<String> destinationRanges,
                                                                                                     String siteType,
                                                                                                     String metric,
                                                                                                     String d){
        OverseasRelatedSearchParamBean overseasRelatedSearchParamBean = new OverseasRelatedSearchParamBean();
        overseasRelatedSearchParamBean.setQuarter(quarter);
        overseasRelatedSearchParamBean.setYear(year);
        overseasRelatedSearchParamBean.setExamineType(siteType);
        overseasRelatedSearchParamBean.setExamineTypeValue(destinationRanges);
        overseasRelatedSearchParamBean.setExamineMetricType(metric);
        overseasRelatedSearchParamBean.setD(d);
        return overseasRelatedSearchParamBean;
    }


    /**
     * 构建GMV/毛利/票量 海外 完成值 目的地 请求参数构建
     * @throws ParseException
     */
    public static OverseasRelatedSearchParamBean generate101102110CompleteDestinationOverseaInfoSearch(Generate101102110SqlBean request) throws ParseException {
        OverseasRelatedSearchParamBean searchParamBean = new OverseasRelatedSearchParamBean();
        searchParamBean.setQuarter(request.getQuarter());
        searchParamBean.setYear(request.getYear());
        searchParamBean.setBuTypeNames(request.getBuTypeNames());
        searchParamBean.setExamineLevel(request.getDestinationLevels());
        searchParamBean.setCt(request.getCt());
        searchParamBean.setDomainName(request.getDomainName());
        searchParamBean.setExamineMetricType(request.getMetric());

        switch (request.getSearchType()) {
            case "30days":
                String lastDay = DateUtil.getDayOfInterval(request.getD(), -1);
                String startDate = DateUtil.getDayOfInterval(lastDay, -29);
                searchParamBean.setStartDate(startDate);
                searchParamBean.setEndDate(lastDay);
                searchParamBean.setYear(null);
                searchParamBean.setQuarter(null);
                break;
            case "last30days":
                String momEndDate = DateUtil.getDayOfInterval(request.getD(), -31);
                String momStartDate = DateUtil.getDayOfInterval(momEndDate, -29);
                searchParamBean.setStartDate(momStartDate);
                searchParamBean.setEndDate(momEndDate);
                searchParamBean.setYear(null);
                searchParamBean.setQuarter(null);
                break;
            case "lastYear":
                searchParamBean.setYear(String.valueOf(Integer.parseInt(request.getYear()) - 1));
                searchParamBean.setEndDate(DateUtil.getLastYearEndDate(request.getD()));
                break;
            case "lastCycle":
                LastQOrHTimeInfoBean lastQOrHTimeInfoBean = DateUtil.getLastQOrHTimeInfo(request.getDateType(), request.getYear(), request.getQuarter());
                searchParamBean.setYear(lastQOrHTimeInfoBean.getYear());
                searchParamBean.setQuarter(lastQOrHTimeInfoBean.getQuarter());
                break;
            default:
                break;
        }
        return searchParamBean;
    }

    /**
     * 构建GMV/毛利/票量 海外 完成值 站点渠道 请求参数构建
     * @throws ParseException
     */
    public static OverseasRelatedSearchParamBean generate101102110CompleteChannelSiteOverseaInfoSearch(Generate101102110SqlBean request) throws ParseException {
        OverseasRelatedSearchParamBean searchParamBean = new OverseasRelatedSearchParamBean();
        searchParamBean.setQuarter(request.getQuarter());
        searchParamBean.setYear(request.getYear());
        searchParamBean.setBuTypeNames(request.getBuTypeNames());
        searchParamBean.setExamineLevel(request.getDestinationLevels());
        searchParamBean.setDomainName(request.getDomainName());
        searchParamBean.setExamineMetricType(request.getMetric());

        switch (request.getSearchType()) {
            case "30days":
                String lastDay = DateUtil.getDayOfInterval(request.getD(), -1);
                String startDate = DateUtil.getDayOfInterval(lastDay, -29);
                searchParamBean.setStartDate(startDate);
                searchParamBean.setEndDate(lastDay);
                searchParamBean.setQuarter(null);
                searchParamBean.setYear(null);
                break;
            case "last30days":
                String momEndDate = DateUtil.getDayOfInterval(request.getD(), -31);
                String momStartDate = DateUtil.getDayOfInterval(momEndDate, -29);
                searchParamBean.setStartDate(momStartDate);
                searchParamBean.setEndDate(momEndDate);
                searchParamBean.setQuarter(null);
                searchParamBean.setYear(null);
                break;
            case "lastYear":
                searchParamBean.setYear(String.valueOf(Integer.parseInt(request.getYear()) - 1));
                searchParamBean.setEndDate(DateUtil.getLastYearEndDate(request.getD()));
                break;
            case "lastCycle":
                LastQOrHTimeInfoBean lastQOrHTimeInfoBean = DateUtil.getLastQOrHTimeInfo(request.getDateType(), request.getYear(), request.getQuarter());
                searchParamBean.setYear(lastQOrHTimeInfoBean.getYear());
                searchParamBean.setQuarter(lastQOrHTimeInfoBean.getQuarter());
                break;
            default:
                break;
        }
        return searchParamBean;
    }


    /**
     * 查询人员考核业绩表获取当前数据
     * @param request
     * @param metricInfoBean
     * @return
     */
    public static OverseasPerformanceInfoParamBean getDestinationTableDataSqlBeanV2(String momType,
                                                                                    GetOverseaTableDataV2RequestType request,
                                                                                    OverseaMetricInfoBean metricInfoBean,
                                                                                    String d,
                                                                                    String year,
                                                                                    RemoteConfig remoteConfig,
                                                                                    List<CdmOrdTtdOverseasPerformanceIndexBO> currentResult) throws ParseException{
        OverseasPerformanceInfoParamBean overseasPerformanceInfoParamBean = new OverseasPerformanceInfoParamBean();
        overseasPerformanceInfoParamBean.setD(d);
        String vbkLocale = UserUtil.getVbkLocale();
        overseasPerformanceInfoParamBean.setVbkLocale(vbkLocale);
        overseasPerformanceInfoParamBean.setField(request.getDimName());
        overseasPerformanceInfoParamBean.setPageIndex(request.getPageNo());
        overseasPerformanceInfoParamBean.setPageSize(request.getPageSize());
        overseasPerformanceInfoParamBean.setMetric(request.getMetric());
        String tkt = remoteConfig.getConfigValue("tkt");
        String act = remoteConfig.getConfigValue("act");
        String odt = remoteConfig.getConfigValue("odt");
        List<String> buType = BuTypeEnum.getBuTypeList(request.getBusinessLine(), tkt, act, odt);
        overseasPerformanceInfoParamBean.setBuTypeName(buType);
        overseasPerformanceInfoParamBean.setCt(OverseaMetricHelper.convertCT(remoteConfig, request.getSubMetric()));
        overseasPerformanceInfoParamBean.setField(request.getDimName());
        if ("30".equalsIgnoreCase(momType)) {
            //对于下钻部分，仅会有30日环比，
            String lastDay = DateUtil.getDayOfInterval(d, -1);
            String startDate = DateUtil.getDayOfInterval(lastDay, -29);
            overseasPerformanceInfoParamBean.setStartDate(startDate);
            overseasPerformanceInfoParamBean.setEndDate(lastDay);
            overseasPerformanceInfoParamBean.setQuarter(null);
            overseasPerformanceInfoParamBean.setYear(null);
        } else if ("last30".equalsIgnoreCase(momType)) {
            String momEndDate = DateUtil.getDayOfInterval(d, -31);
            String momStartDate = DateUtil.getDayOfInterval(momEndDate, -29);
            overseasPerformanceInfoParamBean.setStartDate(momStartDate);
            overseasPerformanceInfoParamBean.setEndDate(momEndDate);
            overseasPerformanceInfoParamBean.setQuarter(null);
            overseasPerformanceInfoParamBean.setYear(null);
        } else {
            if ("current".equalsIgnoreCase(year)) {
                overseasPerformanceInfoParamBean.setYear(request.getTimeFilter().getYear());
            } else if ("lastYear".equalsIgnoreCase(year)) {
                Integer yearValue = Integer.parseInt(request.getTimeFilter().getYear()) - 1;
                overseasPerformanceInfoParamBean.setYear(String.valueOf(yearValue));
                DateRange yoyDateRange = TimeUtil.getYoYDateRangeWithWindow(request.getTimeFilter(), d);
                overseasPerformanceInfoParamBean.setStartDate(yoyDateRange.getStartDate());
                overseasPerformanceInfoParamBean.setEndDate(yoyDateRange.getEndDate());
            }
            if (request.getTimeFilter().getDateType().equalsIgnoreCase("quarter")) {
                overseasPerformanceInfoParamBean.setQuarter(request.getTimeFilter().getQuarter());
            } else if (request.getTimeFilter().getDateType().equalsIgnoreCase("half")) {
                overseasPerformanceInfoParamBean.setHalfYear(request.getTimeFilter().getHalf());
            }
        }

        //根据考核指标传入考核范围，若考核范围为空，则不查，表示查询全部
        //业绩到目的地表没有考核层级字段限制，仅查考核范围
        overseasPerformanceInfoParamBean.setExamineLevel(metricInfoBean.getDestinationLevel());
        if (metricInfoBean.getDestinationLevel().contains("大区")) {//NOSONAR
            overseasPerformanceInfoParamBean.setBuRegionNames(metricInfoBean.getDestinationRangeList());
        }
        if (metricInfoBean.getDestinationLevel().contains("子区域")) {//NOSONAR
            overseasPerformanceInfoParamBean.setBuSubRegionNames(metricInfoBean.getDestinationRangeList());
        }
        if (metricInfoBean.getDestinationLevel().contains("国家/地区")) {//NOSONAR
            overseasPerformanceInfoParamBean.setCtryNames(metricInfoBean.getDestinationRangeList());
        }
        if (metricInfoBean.getDestinationLevel().contains("省份")) {//NOSONAR
            overseasPerformanceInfoParamBean.setProvNames(metricInfoBean.getDestinationRangeList());
        }
        if (metricInfoBean.getDestinationLevel().contains("城市")) {//NOSONAR
            overseasPerformanceInfoParamBean.setCityNames(metricInfoBean.getDestinationRangeList());
        }
        if (metricInfoBean.getDestinationLevel().contains("景点经理")) {//NOSONAR
            overseasPerformanceInfoParamBean.setVstMeids(metricInfoBean.getDestinationRangeList());
        }
        if (metricInfoBean.getDestinationLevel().contains("景点助理")) {//NOSONAR
            overseasPerformanceInfoParamBean.setVstAeids(metricInfoBean.getDestinationRangeList());
        }
        if (metricInfoBean.getDestinationLevel().contains("景点")) {//NOSONAR
            overseasPerformanceInfoParamBean.setVstNames(metricInfoBean.getDestinationRangeList());
        }
        Boolean useZhCN = "zh-CN".equalsIgnoreCase(vbkLocale);
        if (CollectionUtils.isNotEmpty(request.getDimValueList())) {
            if (useZhCN) {
                switch (request.getDimName()) {
                    case "region":
                        overseasPerformanceInfoParamBean.setDimValueBuRegionNames(request.getDimValueList());
                        break;
                    case "province":
                        overseasPerformanceInfoParamBean.setDimValueBuSubRegionNames(request.getDimValueList());
                        break;
                    case "country":
                        overseasPerformanceInfoParamBean.setCtryNames(request.getDimValueList());
                        break;
                    case "examinee":
                        overseasPerformanceInfoParamBean.setDomainNames(request.getDimValueList());
                        break;
                    case "viewspot":
                        overseasPerformanceInfoParamBean.setDimVstNames(request.getDimValueList());
                        break;
                }
            } else {
                switch (request.getDimName()) {
                    case "region":
                        overseasPerformanceInfoParamBean.setDimValueBuRegionNameEns(request.getDimValueList());
                        break;
                    case "province":
                        overseasPerformanceInfoParamBean.setDimValueBuSubRegionNameEns(request.getDimValueList());
                        break;
                    case "country":
                        overseasPerformanceInfoParamBean.setCtryNameEns(request.getDimValueList());
                        break;
                    case "examinee":
                        overseasPerformanceInfoParamBean.setDomainNames(request.getDimValueList());
                        break;
                    case "viewspot":
                        overseasPerformanceInfoParamBean.setDimVstNameEns(request.getDimValueList());
                        break;
                }
            }
        }
        if ("lastYear".equalsIgnoreCase(year)) {
            if(useZhCN){
                switch (request.getDimName()) {
                    case "region":
                        List<String> regionList = currentResult.stream().map(x ->x.getBuRegionNames())
                                .collect(Collectors.toList());
                        overseasPerformanceInfoParamBean.setBuRegionNames(regionList);
                        break;
                    case "province":
                        List<String> subRegionList = currentResult.stream().map(x ->x.getBuSubRegionNames()).collect(Collectors.toList());
                        overseasPerformanceInfoParamBean.setBuSubRegionNames(subRegionList);
                        break;
                    case "country":
                        List<String> countryList = currentResult.stream().map(x ->x.getCtryName()).collect(Collectors.toList());
                        overseasPerformanceInfoParamBean.setCtryNames(countryList);
                        break;
                    case "examinee":
                        List<String> domains = currentResult.stream().map(x -> x.getDomainName()).collect(Collectors.toList());
                        overseasPerformanceInfoParamBean.setDomainNames(domains);
                        break;
                    case "viewspot":
                        List<String> localeList = currentResult.stream().map(x ->x.getVstName()).collect(Collectors.toList());
                        overseasPerformanceInfoParamBean.setVstNames(localeList);
                        break;
                }
            } else {
                switch (request.getDimName()) {
                    case "region":
                        List<String> regionList = currentResult.stream().map(x -> x.getBuRegionNameEn())
                                .collect(Collectors.toList());
                        overseasPerformanceInfoParamBean.setBuRegionNameEns(regionList);
                        break;
                    case "province":
                        List<String> subRegionList = currentResult.stream().map(x -> x.getBuSubRegionNameEn()).collect(Collectors.toList());
                        overseasPerformanceInfoParamBean.setBuSubRegionNameEns(subRegionList);
                        break;
                    case "country":
                        List<String> countryList = currentResult.stream().map(x -> x.getCtryNameEn()).collect(Collectors.toList());
                        overseasPerformanceInfoParamBean.setCtryNameEns(countryList);
                        break;
                    case "examinee":
                        List<String> domains = currentResult.stream().map(x -> x.getDomainName()).collect(Collectors.toList());
                        overseasPerformanceInfoParamBean.setDomainNames(domains);
                        break;
                    case "viewspot":
                        List<String> localeList = currentResult.stream().map(x -> x.getVstNameEn()).collect(Collectors.toList());
                        overseasPerformanceInfoParamBean.setVstNameEns(localeList);
                        break;
                }
            }
        }
        return overseasPerformanceInfoParamBean;
    }

    private static List<String> getIntersection(List<String> rangeList, List<String> dimValueList) {
        if (CollectionUtils.isNotEmpty(rangeList) && rangeList.contains("ALL")) {
            rangeList = null;
        }
        if (CollectionUtils.isEmpty(rangeList)) {
            return dimValueList;
        }
        if (CollectionUtils.isEmpty(dimValueList)) {
            return rangeList;
        }
        if (CollectionUtils.isNotEmpty(rangeList) && CollectionUtils.isNotEmpty(dimValueList)) {
            Set<String> set = new HashSet<>(rangeList);
            set.addAll(dimValueList);
            new ArrayList<>(set);
        }
        return null;
    }

    /**
     * 获取目标查目的地表
     * @param request
     * @return
     */
    public static DimOrdTtdDestinationTargetParamBean getDimOrdTtdDestinationTargetParamBean(GetOverseaTableDataV2RequestType request,
                                                                                             List<String> regionNameList,
                                                                                             List<String> subRegionNameList,
                                                                                             String d, RemoteConfig remoteConfig) {

        DimOrdTtdDestinationTargetParamBean param = new DimOrdTtdDestinationTargetParamBean();
        param.setD(d);
        param.setBuType(BuTypeEnum.getCTByByType(request.getBusinessLine()));
        param.setExamineYear(request.getTimeFilter().getYear());
        List<String> quarterList = "quarter".equals(request.getTimeFilter().getDateType()) ? Collections.singletonList(request.getTimeFilter().getQuarter()) : DateUtil.getQuarterOfHalf(request.getTimeFilter().getHalf());
        param.setExamineQuaters(quarterList);
        if ("region".equalsIgnoreCase(request.getDimName())) {
            param.setBusinessRegionName(regionNameList);
        }
        if ("subRegion".equalsIgnoreCase(request.getDimName())) {
            param.setBusinessSubRegionName(subRegionNameList);
        }
        param.setCT(OverseaMetricHelper.convertCTdestination(remoteConfig,request.getSubMetric()));
        param.setExamineMetricType(request.getMetric());
        return param;
    }


    public static DimOrdTtdPersonTargetParamBean getDimOrdTtdPersonTargetParamBean(List<String> dommainList,GetOverseaTableDataV2RequestType request,
                                                                                   OverseaMetricInfoBean metricInfoBean,
                                                                                   String d, RemoteConfig remoteConfig) {
        DimOrdTtdPersonTargetParamBean paramBean = new DimOrdTtdPersonTargetParamBean();
        paramBean.setD(d);
        String tkt = remoteConfig.getConfigValue("tkt");
        String act = remoteConfig.getConfigValue("act");
        String odt = remoteConfig.getConfigValue("odt");
        paramBean.setBuType(BuTypeEnum.getBuTypeList(request.getBusinessLine(), tkt, act, odt));
        paramBean.setDomainName(dommainList);
        paramBean.setExamineYear(request.getTimeFilter().getYear());
        List<String> quarterList = "quarter".equals(request.getTimeFilter().getDateType()) ? Collections.singletonList(request.getTimeFilter().getQuarter()) : DateUtil.getQuarterOfHalf(request.getTimeFilter().getHalf());
        paramBean.setExamineQuater(quarterList);
        paramBean.setCT(OverseaMetricHelper.convertCTdestination(remoteConfig, request.getSubMetric()));
        paramBean.setExamineMetricType(request.getMetric());
        return paramBean;
    }

    public static DimOrdTtdSiteChannelTargetParamBean getDimOrdTtdSiteChannelTargetParamBean(GetOverseaTableDataV2RequestType request,
                                                                                             OverseaMetricInfoBean metricInfoBean,
                                                                                             String examineType,
                                                                                             String d){
        DimOrdTtdSiteChannelTargetParamBean paramBean = new DimOrdTtdSiteChannelTargetParamBean();
        paramBean.setD(d);
        paramBean.setBuType(BuTypeEnum.getCTByByType(request.getBusinessLine()));
        paramBean.setExamineYear(request.getTimeFilter().getYear());

        paramBean.setExamineMetricType(request.getMetric());
        paramBean.setExamineType(examineType);
        if ("站点".equalsIgnoreCase(examineType)||CollectionUtils.isNotEmpty(metricInfoBean.getSiteRangeList())) {//NOSONAR
            //站点情况只有下钻维度是站点时有目标值
            if ("site".equalsIgnoreCase(request.getDimName())) {
                paramBean.setExamineTypeValue(getIntersection(metricInfoBean.getSiteRangeList(), request.getDimValueList()));
            }
        } else if ("渠道".equalsIgnoreCase(examineType)||CollectionUtils.isNotEmpty(metricInfoBean.getChannelRangeList())) {//NOSONAR
            paramBean.setExamineTypeValue(metricInfoBean.getChannelRangeList());
            //渠道情况只有下钻维度是渠道时有目标值，
            if ("channel".equalsIgnoreCase(request.getDimName())) {
                paramBean.setExamineTypeValue(getIntersection(metricInfoBean.getChannelRangeList(), request.getDimValueList()));
            }
        }
        return paramBean;
    }

    public static void processTableBaseDataByChannel(GetOverseaTableDataV2RequestType request,
                                                     OverseaMetricInfoBean metricInfoBean,
                                                     List<String> headerList,
                                                     List<CdmOrdTtdOverseasPerformanceIndexBO> currentResult,
                                                     List<DimOrdTtdSiteChannelTargetBO> targetResult,
                                                     List<CdmOrdTtdOverseasPerformanceIndexBO> lastYearResult,
                                                     List<CdmOrdTtdOverseasPerformanceIndexBO> momResult,
                                                     List<CdmOrdTtdOverseasPerformanceIndexBO> last30daysResult,
                                                     List<OverseaTableDataRow> tableDataItemList) {

        String field = request.getDimName();
        String metric = request.getMetric();
        String subMetric = request.getSubMetric();
        TimeFilter timeFilter = request.getTimeFilter();
        Map<String, Double> lastYearValueMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(lastYearResult)) {
            for (CdmOrdTtdOverseasPerformanceIndexBO bean : lastYearResult) {
                Double valueBean = chooseValueByMetric(bean, metric);
                String key = null;
                if ("channel".equalsIgnoreCase(subMetric)) {
                    key = buildChannelTableMapKey(field, bean);
                } else if ("site".equalsIgnoreCase(subMetric)) {
                    key = buildSiteTableMapKey(field, bean);
                }
                if (lastYearValueMap.containsKey(key)) {
                    Double value = lastYearValueMap.get(key);
                    value = valueBean + value;
                    lastYearValueMap.put(key, value);
                } else {
                    lastYearValueMap.put(key, valueBean);
                }
            }
        }

        Map<String, OverseaTableDataRow> tableMap = new HashMap<>();
        for (CdmOrdTtdOverseasPerformanceIndexBO bean : currentResult) {
            Double valueBean = chooseValueByMetric(bean, metric);
            String key = null;
            if ("channel".equalsIgnoreCase(subMetric)) {
                key = buildChannelTableMapKey(field, bean);
            } else if ("site".equalsIgnoreCase(subMetric)) {
                key = buildSiteTableMapKey(field, bean);
            }
            if (tableMap.containsKey(key)) {
                OverseaTableDataRow value = tableMap.get(key);
                value.setCompleteValue(value.getCompleteValue() + valueBean);
            } else {
                OverseaTableDataRow row = new OverseaTableDataRow();
                buildCommonRow(metricInfoBean, row, bean, headerList);
                row.setCompleteValue(valueBean);
                tableMap.put(key, row);
                tableDataItemList.add(row);
                //计算去年值
                Double popValue = lastYearValueMap.get(key);
                Double value = row.getCompleteValue();
                if (GeneralUtil.isValidDivide(value, popValue)) {
                    row.setYoyValue(value / popValue - 1);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(targetResult)) {
            //渠道时仅下钻维度是渠道有目标值
            //站点时站点维度有目标值
            for (DimOrdTtdSiteChannelTargetBO targetBO : targetResult) {
                String key = targetBO.getExamineTypeValue() + ":";
                OverseaTableDataRow row = tableMap.get(key);
                if (row == null) {
                    continue;
                }
                Double targetValue = calcTargetValue(timeFilter, targetBO.getQ1(), targetBO.getQ2(), targetBO.getQ3(), targetBO.getQ4());
                row.setTargetValue(targetValue);
                row.setCompleteRate(row.getCompleteValue() / row.getTargetValue());
            }
        }
        Map<String, Double> momValueMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(momResult) && CollectionUtils.isNotEmpty(last30daysResult)) {
            for (CdmOrdTtdOverseasPerformanceIndexBO momBo : momResult) {
                String key = null;
                if ("channel".equalsIgnoreCase(subMetric)) {
                    key = buildChannelTableMapKey(field, momBo);
                } else if ("site".equalsIgnoreCase(subMetric)) {
                    key = buildSiteTableMapKey(field, momBo);
                }
                Double valueBean = chooseValueByMetric(momBo, metric);
                momValueMap.put(key, valueBean);
            }
            for (CdmOrdTtdOverseasPerformanceIndexBO momBo : last30daysResult) {
                String key = null;
                if ("channel".equalsIgnoreCase(subMetric)) {
                    key = buildChannelTableMapKey(field, momBo);
                } else if ("site".equalsIgnoreCase(subMetric)) {
                    key = buildSiteTableMapKey(field, momBo);
                }
                Double valueBean = chooseValueByMetric(momBo, metric);
                OverseaTableDataRow row = tableMap.get(key);
                Double momValue = momValueMap.get(key);
                if (row == null || momValue == null) {
                    continue;
                }
                Double momData = valueBean == 0 ? 0 : (momValue - valueBean) / valueBean;
                row.setPopValue(momData);
            }
        }


    }

    private static String buildChannelTableMapKey(String field, CdmOrdTtdOverseasPerformanceIndexBO bean) {
        StringBuilder key = new StringBuilder();
        switch (field) {
            case "channel":
                key.append(bean.getDisChannelName()).append(":");
                break;
            case "site":
                key.append(bean.getDisChannelName()).append(":");
                key.append(bean.getSites()).append(":");
                break;
            case "locale":
                key.append(bean.getDisChannelName()).append(":");
                key.append(bean.getSites()).append(":");
                key.append(bean.getLocale()).append(":");
                break;

        }
        return key.toString();
    }
    private static String buildSiteTableMapKey(String field, CdmOrdTtdOverseasPerformanceIndexBO bean) {
        StringBuilder key = new StringBuilder();
        switch (field) {
            case "site":
                key.append(bean.getSites()).append(":");
                break;
            case "locale":
                key.append(bean.getSites()).append(":");
                key.append(bean.getLocale()).append(":");
                break;

        }
        return key.toString();
    }

    public static OverseasPerformanceInfoParamBean getDestinationExamineeTableDataSqlBeanV2(GetOverseaTableDataV2RequestType request,
                                                                                            OverseaMetricInfoBean metricInfoBean,
                                                                                            String d,
                                                                                            String year,
                                                                                            RemoteConfig remoteConfig,
                                                                                            List<String> domainList) {
        String region=remoteConfig.getConfigValue("region");
        String subRegion=remoteConfig.getConfigValue("subRegion");
        String vbkLocale = UserUtil.getVbkLocale();
        //商拓维度时，限定考核范围是子区域
        OverseasPerformanceInfoParamBean overseasPerformanceInfoParamBean = new OverseasPerformanceInfoParamBean();
        overseasPerformanceInfoParamBean.setD(d);
        overseasPerformanceInfoParamBean.setPageIndex(request.getPageNo());
        overseasPerformanceInfoParamBean.setPageSize(request.getPageSize());
        overseasPerformanceInfoParamBean.setExamineLevel(subRegion);
        String tkt=remoteConfig.getConfigValue("tkt");
        String act=remoteConfig.getConfigValue("act");
        String odt=remoteConfig.getConfigValue("odt");
        List<String> buType = BuTypeEnum.getBuTypeList(request.getBusinessLine(),tkt,act,odt);
        overseasPerformanceInfoParamBean.setBuTypeName(buType);
//        overseasPerformanceInfoParamBean.setCt(OverseaMetricHelper.convertCT(remoteConfig,request.getSubMetric()));
        if ("current".equalsIgnoreCase(year)) {
            overseasPerformanceInfoParamBean.setYear(request.getTimeFilter().getYear());
        } else if ("lastYear".equalsIgnoreCase(year)) {
            Integer yearValue = Integer.parseInt(request.getTimeFilter().getYear()) - 1;
            overseasPerformanceInfoParamBean.setYear(String.valueOf(yearValue));
            overseasPerformanceInfoParamBean.setPageIndex(null);
            overseasPerformanceInfoParamBean.setPageSize(null);
            DateRange yoyDateRange = TimeUtil.getYoYDateRangeWithWindow(request.getTimeFilter(), d);
            overseasPerformanceInfoParamBean.setStartDate(yoyDateRange.getStartDate());
            overseasPerformanceInfoParamBean.setEndDate(yoyDateRange.getEndDate());
        }
        if (request.getTimeFilter().getDateType().equalsIgnoreCase("quarter")) {
            overseasPerformanceInfoParamBean.setQuarter(request.getTimeFilter().getQuarter());
        } else if (request.getTimeFilter().getDateType().equalsIgnoreCase("half")) {
            overseasPerformanceInfoParamBean.setHalfYear(request.getTimeFilter().getHalf());
        }
        //根据考核指标传入考核范围，若考核范围为空，则不查，表示查询全部
        if (metricInfoBean.getDestinationLevel().contains(region)) {
            overseasPerformanceInfoParamBean.setBuRegionNames(metricInfoBean.getDestinationRangeList());
        }
        if (metricInfoBean.getDestinationLevel().contains(subRegion)) {
            overseasPerformanceInfoParamBean.setBuSubRegionNames(metricInfoBean.getDestinationRangeList());
        }
        if (CollectionUtils.isNotEmpty(request.getDimValueList())){
            overseasPerformanceInfoParamBean.setDomainNames(request.getDimValueList());
        }
        if ("lastYear".equalsIgnoreCase(year) && CollectionUtils.isNotEmpty(domainList)) {
            overseasPerformanceInfoParamBean.setDomainNames(domainList);
        }
        return overseasPerformanceInfoParamBean;
    }

    public static void processTableBaseDataByDestinationV2(List<CdmOrdTtdOverseasPerformanceIndexBO> currentResult,
                                                           List<DimOrdTtdDestinationTargetBO> targetResult,
                                                           List<CdmOrdTtdOverseasPerformanceIndexBO> lastYearResult,
                                                           List<CdmOrdTtdOverseasPerformanceIndexBO> momResult,
                                                           List<CdmOrdTtdOverseasPerformanceIndexBO> last30daysResult ,
                                                           List<OverseaTableDataRow> tableDataItemList,
                                                           GetOverseaTableDataV2RequestType request,
                                                           OverseaMetricInfoBean metricInfoBean,
                                                           List<String> headerList) {
        String field = request.getDimName();
        String metric = request.getMetric();
        TimeFilter timeFilter = request.getTimeFilter();
        //现按key和lastyear数据来map数据
        //在处理当前数据，同一个key，处理当前数据和去年数据，
        //最后如果有目标数据，处理目标数据
        Map<String, Double> lastYearValueMap = new HashMap<>();
        for (CdmOrdTtdOverseasPerformanceIndexBO bean : lastYearResult) {
            Double valueBean = chooseValueByMetric(bean, metric);
            String key = buildDestinationTableMapKey(field, bean, metricInfoBean );
            if (lastYearValueMap.containsKey(key)) {
                Double value = lastYearValueMap.get(key);
                value = valueBean + value;
                lastYearValueMap.put(key, value);
            } else {
                lastYearValueMap.put(key, valueBean);
            }
        }

        Map<String, OverseaTableDataRow> currentMap = new HashMap<>();
        for (CdmOrdTtdOverseasPerformanceIndexBO bean : currentResult) {
            Double valueBean = chooseValueByMetric(bean, metric);
            String key = buildDestinationTableMapKey(field,bean, metricInfoBean);
            if (currentMap.containsKey(key)) {
                OverseaTableDataRow value = currentMap.get(key);
                value.setCompleteValue(value.getCompleteValue() + valueBean);
                currentMap.put(key, value);
            } else {
                OverseaTableDataRow row = new OverseaTableDataRow();
                buildCommonRow(metricInfoBean, row, bean, headerList);
                row.setCompleteValue(valueBean);
                row.setTargetValue(Double.valueOf(0));
                Double popValue = lastYearValueMap.get(key);
                Double value = row.getCompleteValue();
                if (GeneralUtil.isValidDivide(value, popValue)) {
                    row.setYoyValue(value / popValue - 1);
                }
                currentMap.put(key, row);
                tableDataItemList.add(row);
            }
        }
        if (CollectionUtils.isNotEmpty(targetResult)) {
            for (DimOrdTtdDestinationTargetBO targetBO : targetResult) {
                String key = buildTargetTableMapKey(field, targetBO);
                OverseaTableDataRow row = currentMap.get(key);
                if (row == null) {
                    continue;
                }
                Double targetValue = calcTargetValue(timeFilter, targetBO.getQ1(), targetBO.getQ2(), targetBO.getQ3(), targetBO.getQ4());
                row.setTargetValue(targetValue + row.getTargetValue());
                row.setCompleteRate(Double.valueOf(0).equals(targetValue)  ? Double.valueOf(0) : row.getCompleteValue() / row.getTargetValue());
            }
        }
        Map<String, Double> momValueMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(momResult) && CollectionUtils.isNotEmpty(last30daysResult)) {
            for (CdmOrdTtdOverseasPerformanceIndexBO momBo : momResult) {
                String key = buildDestinationTableMapKey(field, momBo, metricInfoBean);
                Double valueBean = chooseValueByMetric(momBo, metric);
                momValueMap.put(key, valueBean);
            }
            for (CdmOrdTtdOverseasPerformanceIndexBO momBo : last30daysResult) {
                String key = buildDestinationTableMapKey(field, momBo, metricInfoBean);
                Double valueBean = chooseValueByMetric(momBo, metric);
                OverseaTableDataRow row = currentMap.get(key);
                Double momValue = momValueMap.get(key);
                if (row == null || momValue == null) {
                    continue;
                }
                Double momData = valueBean == 0 ? 0 : (momValue - valueBean) / valueBean;
                row.setPopValue(momData);
            }
        }
    }
    public static void processTableBaseDataByExamineeV2(List<CdmOrdTtdOverseasPerformanceIndexBO> currentResult,
                                                           List<DimOrdTtdPersonTargetBO> persontargetResult,
                                                           List<CdmOrdTtdOverseasPerformanceIndexBO> lastYearResult,
                                                           List<OverseaTableDataRow> tableDataItemList,
                                                           GetOverseaTableDataV2RequestType request,
                                                           OverseaMetricInfoBean metricInfoBean,
                                                        List<String> headerList) {
        String field = request.getDimName();
        String metric = request.getMetric();
        TimeFilter timeFilter = request.getTimeFilter();
        //现按key和lastyear数据来map数据
        //在处理当前数据，同一个key，处理当前数据和去年数据，
        //最后如果有目标数据，处理目标数据
        Map<String, Double> lastYearValueMap = new HashMap<>();
        for (CdmOrdTtdOverseasPerformanceIndexBO bean : lastYearResult) {
            Double valueBean = chooseValueByMetric(bean, metric);
            String key = buildDestinationTableMapKey(field, bean, metricInfoBean);
            if (lastYearValueMap.containsKey(key)) {
                Double value = lastYearValueMap.get(key);
                value = valueBean + value;
                lastYearValueMap.put(key, value);
            } else {
                lastYearValueMap.put(key, valueBean);
            }
        }
        Map<String, Double> targetMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(persontargetResult)) {
            for (DimOrdTtdPersonTargetBO targetBO : persontargetResult) {
                Double targetValue = calcTargetValue(timeFilter, targetBO.getQ1(), targetBO.getQ2(), targetBO.getQ3(), targetBO.getQ4());
                targetMap.put(targetBO.getDomainName(), targetValue);
            }
        }
        Map<String, OverseaTableDataRow> currentMap = new HashMap<>();
        for (CdmOrdTtdOverseasPerformanceIndexBO bean : currentResult) {
            Double valueBean = chooseValueByMetric(bean, metric);
            String key = buildDestinationTableMapKey(field,bean, metricInfoBean);
            if (currentMap.containsKey(key)) {
                OverseaTableDataRow row = currentMap.get(key);
                row.setCompleteValue(row.getCompleteValue() + valueBean);
                Double popValue = lastYearValueMap.get(key);
                Double currentValue = row.getCompleteValue();
                if (GeneralUtil.isValidDivide(currentValue, popValue)) {
                    row.setYoyValue(currentValue / popValue - 1);
                }
                currentMap.put(key, row);
            } else {
                OverseaTableDataRow row = new OverseaTableDataRow();
                buildCommonRow(metricInfoBean, row, bean, headerList);
                row.setCompleteValue(valueBean);
                row.setTargetValue(targetMap.get(bean.getDomainName()));
                if (row.getTargetValue() != null && row.getTargetValue() != 0) {
                    row.setCompleteRate(row.getCompleteValue() / row.getTargetValue());
                }
                currentMap.put(key, row);
                Double popValue = lastYearValueMap.get(key);
                Double currentValue = row.getCompleteValue();
                if (GeneralUtil.isValidDivide(currentValue, popValue)) {
                    row.setYoyValue(currentValue / popValue - 1);
                }
                tableDataItemList.add(row);
            }
        }
    }


    private static Double chooseValueByMetric(CdmOrdTtdOverseasPerformanceIndexBO bean, String metric) {
        switch (metric) {
            case "101":
                return bean.getGmv();
            case "102":
                return bean.getProfit();
            case "110":
                return bean.getQty();
        }
        return new Double(0);
    }

    private static String buildDestinationTableMapKey(String field,CdmOrdTtdOverseasPerformanceIndexBO bean,
                                                      OverseaMetricInfoBean metricInfoBean) {
        //具体展示那几列是按照下钻维度区分的，所以这里key的维度也要按照展示几列来定
        StringBuilder key = new StringBuilder();
        switch (field) {
            case "region":
                key.append(bean.getBuRegionNames()).append(":");
                break;
            case "province":
                key.append(bean.getBuRegionNames()).append(":");
                key.append(bean.getBuSubRegionNames()).append(":");
                break;
            case "country":
                key.append(bean.getBuRegionNames()).append(":");
                key.append(bean.getBuSubRegionNames()).append(":");
                if (metricInfoBean.getDestinationLevel().equalsIgnoreCase("国家")) {//NOSONAR
                    key.append(bean.getCtryName()).append(":");
                } else if (metricInfoBean.getDestinationLevel().equalsIgnoreCase("省份")) {//NOSONAR
                    key.append(bean.getProvName()).append(":");
                } else if (metricInfoBean.getDestinationLevel().equalsIgnoreCase("城市")) {//NOSONAR
                    key.append(bean.getCityName()).append(":");
                }else {
                    key.append(bean.getCtryName()).append(":");
                }
                break;
            case "examinee":
                key.append(bean.getBuRegionNames()).append(":");
                key.append(bean.getBuSubRegionNames()).append(":");
                key.append(bean.getDomainName()).append(":");
                break;
            case "viewspot":
                key.append(bean.getBuRegionNames()).append(":");
                key.append(bean.getBuSubRegionNames()).append(":");
                key.append(bean.getVstId()).append(":");
                key.append(bean.getVstName()).append(":");
                break;
            case "site":
                key.append(bean.getBuRegionNames()).append(":");
                key.append(bean.getBuSubRegionNames()).append(":");
                key.append(bean.getVstId()).append(":");
                key.append(bean.getVstName()).append(":");
                break;
            case "channel":
                key.append(bean.getBuRegionNames()).append(":");
                key.append(bean.getBuSubRegionNames()).append(":");
                key.append(bean.getVstId()).append(":");
                key.append(bean.getVstName()).append(":");
                break;
            case "locale":
                key.append(bean.getBuRegionNames()).append(":");
                key.append(bean.getBuSubRegionNames()).append(":");
                key.append(bean.getVstId()).append(":");
                key.append(bean.getVstName()).append(":");
                break;
        }
        return key.toString();
    }
}
