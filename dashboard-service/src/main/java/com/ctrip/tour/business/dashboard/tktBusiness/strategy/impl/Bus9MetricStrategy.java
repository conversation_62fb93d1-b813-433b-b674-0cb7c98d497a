package com.ctrip.tour.business.dashboard.tktBusiness.strategy.impl;

import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ExamineConfigBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.SinglePeriodDataBean;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.SinglePeriodTrendLineBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.Bus9NewDao;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardExamineeConfigV2Dao;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.*;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.MetricCalStrategy;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @date 2022/7/29
 */
@Component
public class Bus9MetricStrategy implements MetricCalStrategy {

    @Autowired
    private RemoteConfig remoteConfig;

    @Autowired
    private Bus9NewDao bus9NewDao;


    @Autowired
    private BusinessDashboardExamineeConfigV2Dao examineeConfigV2Dao;

    @Autowired
    private SinglePeriodTrendLineBiz singlePeriodTrendLineBiz;


    @Override
    public Future<MetricDetailInfo> getSingleMetricCardData(String domainName,
                                                            TimeFilter timeFilter,
                                                            MetricInfoBean metricInfoBean,
                                                            String d,
                                                            Boolean needRank) throws Exception {
        MetricDetailInfo metricDetailInfo = new MetricDetailInfo();
        String metric = metricInfoBean.getMetric();
        Map<String, Double> dimMap = new HashMap<>();
        metricDetailInfo.setDimData(dimMap);
        metricDetailInfo.setMetric(metric);

        String year = timeFilter.getYear();
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();
        String dateType = MetricHelper.getDateTypeColumnName(timeFilter.getDateType());

        String level = MetricHelper.getLevelColumnName(metricInfoBean.getLevel());

        List<String> regionList = metricInfoBean.getRegionList();

        DalHints rankDalHints = new DalHints().asyncExecution();
        if (needRank) {
            getRankDataAsync(dateType, year, quarter, month, d, domainName, metric, rankDalHints);
        }

        //由于mysql需要设置索引 因此需要保持顺序
        Map<String, List<String>> inMap = new HashMap<>();
        inMap.put("query_d", Lists.newArrayList(d));
        inMap.put("year", Lists.newArrayList(year));
        inMap.put("date_type", Lists.newArrayList(dateType));
        if ("month".equals(dateType)) {
            inMap.put("month", Lists.newArrayList(month));
        } else {
            inMap.put("quarter", Lists.newArrayList(quarter));
        }
        if (!"".equals(level)) {
            inMap.put(level, regionList);
        }
        List<List<Object>> reachList = bus9NewDao.getMetricCardData(inMap);
        List<String> dimList = Lists.newArrayList("multi_category_complete_rate");
        ChartHelper.fillOverallDimMap(reachList, dimList, dimMap);


        MetricHelper.setMetricCardDrillDownInfo(metricInfoBean, metricDetailInfo);

        //获取排名数据
        if (needRank) {
            ChartHelper.fillRankData(metricDetailInfo, rankDalHints.getListResult());
        }


        return new AsyncResult<>(metricDetailInfo);
    }

    @Override
    public GetTrendLineDataResponseType getSingleTrendlineData(GetTrendLineDataRequestType request,
                                                               MetricInfoBean metricInfoBean,
                                                               String d) throws Exception {

        GetTrendLineDataResponseType response = new GetTrendLineDataResponseType();
        List<TrendLineDetailInfo> trendLineDetailInfoList = new ArrayList<>();
        response.setTrendLineDetailInfoList(trendLineDetailInfoList);

        String queryType = request.getQueryType();
        TimeFilter timeFilter = request.getTimeFilter();

        String domainName = request.getDomainName();
        List<ExamineConfigBean> examineConfigBeanList = MultiPeriodMappingHelper.getMultiPeriodConfigList(timeFilter, d, examineeConfigV2Dao.queryMetricAllConfig(domainName, d, getMetricName()), null);
        List<Future<SinglePeriodDataBean>> futureList = new ArrayList<>();
        for (ExamineConfigBean examineConfigBean : examineConfigBeanList) {
            futureList.add(singlePeriodTrendLineBiz.getBus9SinglePeriodTrendLineData(request, examineConfigBean, d));
        }
        List<List<Object>> reachList = new ArrayList<>();
        List<List<Object>> stackList = new ArrayList<>();
        for (Future<SinglePeriodDataBean> futureResult : futureList) {
            SinglePeriodDataBean singlePeriodDataBean = futureResult.get();
            List<List<Object>> periodReachList = singlePeriodDataBean.getPeriodReachList();
            List<List<Object>> periodStackList = singlePeriodDataBean.getPeriodStackList();
            if (GeneralUtil.isNotEmpty(periodReachList)) {
                reachList.addAll(periodReachList);
            }
            if (GeneralUtil.isNotEmpty(periodStackList)) {
                stackList.addAll(periodStackList);
            }
        }


        //有效时间范围
        List<String> timeFrameList = DateUtil.getAllSelectedTime(timeFilter, "domestic");

        List<String> groupTagList = Lists.newArrayList("time");
        DrillDownFilter drillDownFilter = request.getDrillDownFilter();
        if ("drilldown".equals(queryType)) {
            String field = MetricHelper.getDrillDownColumnName(drillDownFilter.getField());
            groupTagList.add(field);
        }


        //填充综合品类完成率
        Map<String, Double> dimMap1 = new HashMap<>();
        Set<String> drillDownSet1 = new HashSet<>();
        List<String> dimList1 = Lists.newArrayList("multi_category_complete_rate");
        ChartHelper.setMonthlyMetricTrendlineActualReach(dimMap1, drillDownSet1, reachList, groupTagList, dimList1);


        if ("drilldown".equals(queryType)) {
            //下钻时需要按下钻方式填充数据
            ChartHelper.fillLineChartTrendLineDataWithDrillDown(null, timeFrameList, dimMap1, trendLineDetailInfoList,
                    Bus9Helper.getLineChartTrendlineTypeNew(), drillDownSet1, false);
        } else {
            //填充堆叠柱状图数据
            Map<String, Double> dimMap = new HashMap<>();
            Set<String> drillDownSet = new HashSet<>();
            List<String> dimList = Lists.newArrayList("cover_scenic_cnt");
            ChartHelper.setMonthlyMetricTrendlineActualReach(dimMap, drillDownSet, stackList, Lists.newArrayList("time", "viewspot_type_name"), dimList);

            ChartHelper.fillLineChartTrendLineDataWithDrillDown(null, timeFrameList, dimMap, trendLineDetailInfoList,
                    Bus9Helper.getStackLineChartTrendLineType(), drillDownSet, false);
            //非下钻时不按下钻填充数据
            ChartHelper.fillLineChartTrendLineData(null, timeFrameList, dimMap1,
                    trendLineDetailInfoList, Bus9Helper.getLineChartTrendlineTypeNew());
        }
        return response;
    }

    @Override
    public GetTableDataResponseType getSingleTableData(GetTableDataRequestType request,
                                                       MetricInfoBean metricInfoBean,
                                                       String d) throws Exception {

        String queryType = request.getQueryType();
        if (GeneralUtil.isEmpty(queryType)) {
            queryType = "drilldown";
        }

        if ("overall".equals(queryType)) {
            return getSingleTableDataWithOutDrillDown(request, metricInfoBean, d);
        }

        return getSingleTableDataWithDrillDown(request, metricInfoBean, d);
    }

    private GetTableDataResponseType getSingleTableDataWithOutDrillDown(GetTableDataRequestType request,
                                                                        MetricInfoBean metricInfoBean,
                                                                        String d) throws SQLException {
        GetTableDataResponseType response = new GetTableDataResponseType();
        List<TableDataItem> tableDataItemList = new ArrayList<>();
        response.setTableDataItemList(tableDataItemList);
        TimeFilter timeFilter = request.getTimeFilter();
        String year = timeFilter.getYear();
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();
        String dateType = MetricHelper.getDateTypeColumnName(timeFilter.getDateType());

        String level = MetricHelper.getLevelColumnName(metricInfoBean.getLevel());
        List<String> regionList = metricInfoBean.getRegionList();

        //由于mysql需要设置索引 因此需要保持顺序
        Map<String, List<String>> inMap = new HashMap<>();
        inMap.put("query_d", Lists.newArrayList(d));
        inMap.put("year", Lists.newArrayList(year));
        inMap.put("date_type", Lists.newArrayList(dateType));
        if ("month".equals(dateType)) {
            inMap.put("month", Lists.newArrayList(month));
        } else {
            inMap.put("quarter", Lists.newArrayList(quarter));
        }
        if (!"".equals(level)) {
            inMap.put(level, regionList);
        }
        List<String> groupTagList = Lists.newArrayList("viewspot_type_name", "viewspot_type_id", "viewspot_type_weight");
        List<String> orderTagList = Lists.newArrayList("viewspot_type_name");
        List<List<Object>> singleCategoryList = bus9NewDao.getSingleCategoryTableData(inMap, groupTagList, orderTagList);

        //获取数据总条数(假)
        response.setTotalNum(singleCategoryList.size());

        //拼接数据
        List<String> tableDimList = Bus9Helper.getTableDimList();
        ChartHelper.fillCommmonTableData(tableDataItemList, groupTagList,
                tableDimList, new ArrayList<>(), singleCategoryList, new ArrayList<>());
        JumpHelper.makeUpTargetManageUrl(remoteConfig, timeFilter, metricInfoBean, tableDataItemList);
        return response;
    }


    private GetTableDataResponseType getSingleTableDataWithDrillDown(GetTableDataRequestType request,
                                                                     MetricInfoBean metricInfoBean,
                                                                     String d) throws SQLException {
        GetTableDataResponseType response = new GetTableDataResponseType();
        List<TableDataItem> tableDataItemList = new ArrayList<>();
        response.setTableDataItemList(tableDataItemList);
        TimeFilter timeFilter = request.getTimeFilter();
        String year = timeFilter.getYear();
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();
        String dateType = MetricHelper.getDateTypeColumnName(timeFilter.getDateType());

        String level = MetricHelper.getLevelColumnName(metricInfoBean.getLevel());
        List<String> regionList = metricInfoBean.getRegionList();

        //由于mysql需要设置索引 因此需要保持顺序
        Map<String, List<String>> inMap = new HashMap<>();
        inMap.put("query_d", Lists.newArrayList(d));
        inMap.put("year", Lists.newArrayList(year));
        inMap.put("date_type", Lists.newArrayList(dateType));
        if ("month".equals(dateType)) {
            inMap.put("month", Lists.newArrayList(month));
        } else {
            inMap.put("quarter", Lists.newArrayList(quarter));
        }
        if (!"".equals(level)) {
            inMap.put(level, regionList);
        }

        //获取某个考核周期的品类个数
        Integer categoryNum = bus9NewDao.getCategoryNum(inMap);
        response.setCategoryNum(categoryNum);

        List<String> categoryEnumList = bus9NewDao.getCategoryEnum(inMap, Lists.newArrayList("viewspot_type_name"));


        DrillDownFilter drillDownFilter = request.getDrillDownFilter();
        String field = MetricHelper.getDrillDownColumnName(drillDownFilter.getField());
        List<String> fieldValueList = drillDownFilter.getFieldValueList();

        if (!GeneralUtil.isEmpty(fieldValueList)) {
            inMap.put(field, fieldValueList);
        }

        //由于该指标的计算方式
        //所以需要单独拉取排序字段的值 做分页  每次根据分页参数取对应的5个
        //作为条件放入事实表的查询
        List<String> pagingGroupTagList = Lists.newArrayList(field);
        List<List<Object>> fieldList = bus9NewDao.getFieldList(inMap, new HashMap<>(), pagingGroupTagList);

        Integer pageNo = request.getPageNo();
        Integer pageSize = request.getPageSize();
        if (pageSize == null) {
            pageSize = 5;
        }

        List<String> pagingCondition = Bus9Helper.getPagingCondition(fieldList, pageNo, pageSize);
        Integer totalNum = fieldList.size();
        response.setTotalNum(totalNum);

        //没有数据或者查出的页码超出范围时
        //不进入实际查询
        if (totalNum > 0 && GeneralUtil.isNotEmpty(pagingCondition)) {
            inMap.put(field, pagingCondition);

            List<String> groupTagList = MetricHelper.getTableDrillDownGroupList(field);
            groupTagList.addAll(Lists.newArrayList("viewspot_type_name", "viewspot_type_weight"));

            List<String> orderTagList = MetricHelper.getTableDrillDownGroupList(field);
            orderTagList.addAll(Lists.newArrayList("viewspot_type_name"));
            List<List<Object>> singleCategoryList = bus9NewDao.getSingleCategoryTableData(inMap, groupTagList, orderTagList);


            //获取综合完成率
            List<String> outerGroupTagList = MetricHelper.getTableDrillDownGroupList(field);
            List<String> innerGroupTagList = MetricHelper.getTableDrillDownGroupList(field);
            innerGroupTagList.addAll(Lists.newArrayList("viewspot_type_name"));
            List<List<Object>> multiCategoryList = bus9NewDao.getMultiCategoryTrendlineData(inMap, innerGroupTagList, outerGroupTagList);

            //拼接数据
            List<String> tableDimList = Bus9Helper.getTableDimList();

            ChartHelper.fillCommmonTableData(tableDataItemList, groupTagList, tableDimList, new ArrayList<>(), singleCategoryList, new ArrayList<>());
            tableDataItemList = Bus9Helper.getNewTableDataItemList(categoryEnumList, field, tableDataItemList);
            ChartHelper.fillCommmonTableDataV2(tableDataItemList, new ArrayList<>(), outerGroupTagList, new ArrayList<>(), Lists.newArrayList("multi_category_complete_rate"), new ArrayList<>(), multiCategoryList);
            response.setTableDataItemList(tableDataItemList);
        }


        return response;
    }

    @Override
    public GetDrillDownBaseInfoResponseType getSingleDrillDownBaseInfo(GetDrillDownBaseInfoRequestType request,
                                                                       MetricInfoBean metricInfoBean,
                                                                       String d) throws Exception {
        GetDrillDownBaseInfoResponseType response = new GetDrillDownBaseInfoResponseType();
        List<FieldDataItem> fieldDataItemList = new ArrayList<>();
        response.setFieldDataItemList(fieldDataItemList);

        String metric = metricInfoBean.getMetric();
        List<String> regionList = metricInfoBean.getRegionList();
        String level = metricInfoBean.getLevel();//国内  三方  大区  省份
        String levelColumnName = MetricHelper.getLevelColumnName(level);

        TimeFilter timeFilter = request.getTimeFilter();
        String year = timeFilter.getYear();
        String dateType = timeFilter.getDateType();
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();

        boolean needSearch = request.isNeedSearch();
        String searchWord = request.getSearchWord();
        List<String> fieldList = Bus9Helper.getFieldList(level);

        if (needSearch) {
            String searchField = request.getSearchField();//大区 省份

            if (searchField != null) {
                String searchFieldColumnName = MetricHelper.getDrillDownColumnName(searchField);
                fieldList = Lists.newArrayList(searchFieldColumnName);
            }

        } else {
            response.setDefaultChosenField(MetricHelper.getDefaultChosenField(level, metric));
        }
        Map<String, List<String>> inMap = new LinkedHashMap<>();
        inMap.put("query_d", Lists.newArrayList(d));
        inMap.put("year", Lists.newArrayList(year));
        if ("month".equals(dateType)) {
            inMap.put("month", Lists.newArrayList(month));
        } else {
            inMap.put("quarter", Lists.newArrayList(quarter));
        }
        if (!"".equals(levelColumnName)) {
            inMap.put(levelColumnName, regionList);
        }
        for (String field : fieldList) {
            List<String> tagList = Lists.newArrayList(field);
            Map<String, String> likeMap = new HashMap<>();
            if (needSearch) {
                likeMap.put(field, searchWord);
            }
            List<List<Object>> rawObjectList = bus9NewDao.getFieldList(inMap, likeMap, tagList);
            ChartHelper.fillFieldDataItemList(field, rawObjectList, fieldDataItemList);
        }
        return response;
    }

    private void getRankDataAsync(String dateType,
                                  String year,
                                  String quarter,
                                  String month,
                                  String d,
                                  String domainName,
                                  String metric,
                                  DalHints dalHints) throws Exception {
        Map<String, List<String>> inMap = new LinkedHashMap<>();
        inMap.put("query_d", Lists.newArrayList(d));
        inMap.put("examinee", Lists.newArrayList(domainName));
        inMap.put("year", Lists.newArrayList(year));
        if ("month".equals(dateType)) {
            inMap.put("month", Lists.newArrayList(month));
        } else {
            String mappingMonth = DateUtil.getMappingMonthByQuarter(d, year, quarter);
            inMap.put("month", Lists.newArrayList(mappingMonth));
        }
        inMap.put("metric", Lists.newArrayList(metric));
        bus9NewDao.getRankAsync(inMap, dalHints);
    }


    @Override
    public String getMetricName() {
        return "9";
    }
}
