//package com.ctrip.tour.business.dashboard.grpBusiness.entity;
//
//import javax.persistence.Column;
//import javax.persistence.Entity;
//import javax.persistence.GeneratedValue;
//import javax.persistence.GenerationType;
//import javax.persistence.Id;
//import javax.persistence.Table;
//import com.ctrip.platform.dal.dao.annotation.Database;
//import com.ctrip.platform.dal.dao.annotation.Sensitive;
//import com.ctrip.platform.dal.dao.annotation.Type;
//import java.sql.Types;
//import java.sql.Timestamp;
//
//import com.ctrip.platform.dal.dao.DalPojo;
//
///**
// * <AUTHOR>
// * @date 2024-12-10
// */
//@Entity
//@Database(name = "TtdReportDB_W")
//@Table(name = "adm_grp_tor_work_platform_order_df")
//public class AdmGrpTorWorkPlatformOrderDf implements DalPojo {
//
//    /**
//     * 主键
//     */
//    @Id
//	@Column(name = "id")
//	@GeneratedValue(strategy = GenerationType.AUTO)
//	@Type(value = Types.BIGINT)
//	private Long id;
//
//    /**
//     * 业务线：团队游
//     */
//	@Column(name = "bu_type")
//	@Type(value = Types.VARCHAR)
//	private String buType;
//
//    /**
//     * 产线：跟团游/独立出游
//     */
//	@Column(name = "sub_bu_type")
//	@Type(value = Types.VARCHAR)
//	private String subBuType;
//
//    /**
//     * 出发日期
//     */
//	@Column(name = "dep_date")
//	@Type(value = Types.VARCHAR)
//	private String depDate;
//
//    /**
//     * 产品类型ID
//     */
//	@Column(name = "prd_category_id")
//	@Type(value = Types.BIGINT)
//	private Long prdCategoryId;
//
//    /**
//     * 产品类型
//     */
//	@Column(name = "prd_category_name")
//	@Type(value = Types.VARCHAR)
//	private String prdCategoryName;
//
//    /**
//     * 产品形态ID
//     */
//	@Column(name = "prd_pattern_id")
//	@Type(value = Types.BIGINT)
//	private Long prdPatternId;
//
//    /**
//     * 产品形态名称
//     */
//	@Column(name = "prd_pattern_name")
//	@Type(value = Types.VARCHAR)
//	private String prdPatternName;
//
//    /**
//     * 目的地区域：国内/海外
//     */
//	@Column(name = "dest_domain")
//	@Type(value = Types.VARCHAR)
//	private String destDomain;
//
//    /**
//     * 销售渠道名称
//     */
//	@Column(name = "sale_channel_name")
//	@Type(value = Types.VARCHAR)
//	private String saleChannelName;
//
//    /**
//     * 销售模式名称
//     */
//	@Column(name = "sale_mode_name")
//	@Type(value = Types.VARCHAR)
//	private String saleModeName;
//
//    /**
//     * 客源地目的地
//     */
//	@Column(name = "tour_region_type")
//	@Type(value = Types.VARCHAR)
//	private String tourRegionType;
//
//    /**
//     * 产品大区ID
//     */
//	@Column(name = "prd_region_id")
//	@Type(value = Types.BIGINT)
//	private Long prdRegionId;
//
//    /**
//     * 产品大区名称
//     */
//	@Column(name = "prd_region_name")
//	@Type(value = Types.VARCHAR)
//	private String prdRegionName;
//
//    /**
//     * 产品经理
//     */
//	@Column(name = "pm_eid")
//	@Type(value = Types.VARCHAR)
//	private String pmEid;
//
//    /**
//     * 驻地业务经理
//     */
//	@Column(name = "local_pm_eid")
//	@Type(value = Types.VARCHAR)
//	private String localPmEid;
//
//    /**
//     * 运营一级大区
//     */
//	@Column(name = "dest_first_region")
//	@Type(value = Types.VARCHAR)
//	private String destFirstRegion;
//
//    /**
//     * 目的地省份ID
//     */
//	@Column(name = "dest_province_id")
//	@Type(value = Types.BIGINT)
//	private Long destProvinceId;
//
//    /**
//     * 目的地省份名称
//     */
//	@Column(name = "dest_province_name")
//	@Type(value = Types.VARCHAR)
//	private String destProvinceName;
//
//    /**
//     * 目的地国家ID
//     */
//	@Column(name = "dest_ctry_id")
//	@Type(value = Types.BIGINT)
//	private Long destCtryId;
//
//    /**
//     * 目的地国家名称
//     */
//	@Column(name = "dest_ctry")
//	@Type(value = Types.VARCHAR)
//	private String destCtry;
//
//    /**
//     * 订单供应商ID
//     */
//	@Column(name = "ord_vendor_id")
//	@Type(value = Types.BIGINT)
//	private Long ordVendorId;
//
//    /**
//     * 订单供应商名称
//     */
//	@Column(name = "ord_vendor_name")
//	@Type(value = Types.VARCHAR)
//	private String ordVendorName;
//
//    /**
//     * 供应商ID
//     */
//	@Column(name = "vendor_id")
//	@Type(value = Types.BIGINT)
//	private Long vendorId;
//
//    /**
//     * 供应商名称
//     */
//	@Column(name = "vendor_name")
//	@Type(value = Types.VARCHAR)
//	private String vendorName;
//
//    /**
//     * 是否参与秒杀
//     */
//	@Column(name = "is_sec_kill")
//	@Type(value = Types.BIGINT)
//	private Long isSecKill;
//
//    /**
//     * 业务板块
//     */
//	@Column(name = "business_plat")
//	@Type(value = Types.VARCHAR)
//	private String businessPlat;
//
//    /**
//     * 注册地所属大区
//     */
//	@Column(name = "register_domain")
//	@Type(value = Types.VARCHAR)
//	private String registerDomain;
//
//    /**
//     * GMV
//     */
//	@Column(name = "suc_income")
//	@Type(value = Types.DOUBLE)
//	private Double sucIncome;
//
//    /**
//     * 毛利
//     */
//	@Column(name = "suc_profit")
//	@Type(value = Types.DOUBLE)
//	private Double sucProfit;
//
//    /**
//     * 更新时间
//     */
//	@Column(name = "datachange_lasttime", insertable = false, updatable = false)
//	@Type(value = Types.TIMESTAMP)
//	private Timestamp datachangeLasttime;
//
//	public Long getId() {
//		return id;
//	}
//
//	public void setId(Long id) {
//		this.id = id;
//	}
//
//	public String getBuType() {
//		return buType;
//	}
//
//	public void setBuType(String buType) {
//		this.buType = buType;
//	}
//
//	public String getSubBuType() {
//		return subBuType;
//	}
//
//	public void setSubBuType(String subBuType) {
//		this.subBuType = subBuType;
//	}
//
//	public String getDepDate() {
//		return depDate;
//	}
//
//	public void setDepDate(String depDate) {
//		this.depDate = depDate;
//	}
//
//	public Long getPrdCategoryId() {
//		return prdCategoryId;
//	}
//
//	public void setPrdCategoryId(Long prdCategoryId) {
//		this.prdCategoryId = prdCategoryId;
//	}
//
//	public String getPrdCategoryName() {
//		return prdCategoryName;
//	}
//
//	public void setPrdCategoryName(String prdCategoryName) {
//		this.prdCategoryName = prdCategoryName;
//	}
//
//	public Long getPrdPatternId() {
//		return prdPatternId;
//	}
//
//	public void setPrdPatternId(Long prdPatternId) {
//		this.prdPatternId = prdPatternId;
//	}
//
//	public String getPrdPatternName() {
//		return prdPatternName;
//	}
//
//	public void setPrdPatternName(String prdPatternName) {
//		this.prdPatternName = prdPatternName;
//	}
//
//	public String getDestDomain() {
//		return destDomain;
//	}
//
//	public void setDestDomain(String destDomain) {
//		this.destDomain = destDomain;
//	}
//
//	public String getSaleChannelName() {
//		return saleChannelName;
//	}
//
//	public void setSaleChannelName(String saleChannelName) {
//		this.saleChannelName = saleChannelName;
//	}
//
//	public String getSaleModeName() {
//		return saleModeName;
//	}
//
//	public void setSaleModeName(String saleModeName) {
//		this.saleModeName = saleModeName;
//	}
//
//	public String getTourRegionType() {
//		return tourRegionType;
//	}
//
//	public void setTourRegionType(String tourRegionType) {
//		this.tourRegionType = tourRegionType;
//	}
//
//	public Long getPrdRegionId() {
//		return prdRegionId;
//	}
//
//	public void setPrdRegionId(Long prdRegionId) {
//		this.prdRegionId = prdRegionId;
//	}
//
//	public String getPrdRegionName() {
//		return prdRegionName;
//	}
//
//	public void setPrdRegionName(String prdRegionName) {
//		this.prdRegionName = prdRegionName;
//	}
//
//	public String getPmEid() {
//		return pmEid;
//	}
//
//	public void setPmEid(String pmEid) {
//		this.pmEid = pmEid;
//	}
//
//	public String getLocalPmEid() {
//		return localPmEid;
//	}
//
//	public void setLocalPmEid(String localPmEid) {
//		this.localPmEid = localPmEid;
//	}
//
//	public String getDestFirstRegion() {
//		return destFirstRegion;
//	}
//
//	public void setDestFirstRegion(String destFirstRegion) {
//		this.destFirstRegion = destFirstRegion;
//	}
//
//	public Long getDestProvinceId() {
//		return destProvinceId;
//	}
//
//	public void setDestProvinceId(Long destProvinceId) {
//		this.destProvinceId = destProvinceId;
//	}
//
//	public String getDestProvinceName() {
//		return destProvinceName;
//	}
//
//	public void setDestProvinceName(String destProvinceName) {
//		this.destProvinceName = destProvinceName;
//	}
//
//	public Long getDestCtryId() {
//		return destCtryId;
//	}
//
//	public void setDestCtryId(Long destCtryId) {
//		this.destCtryId = destCtryId;
//	}
//
//	public String getDestCtry() {
//		return destCtry;
//	}
//
//	public void setDestCtry(String destCtry) {
//		this.destCtry = destCtry;
//	}
//
//	public Long getOrdVendorId() {
//		return ordVendorId;
//	}
//
//	public void setOrdVendorId(Long ordVendorId) {
//		this.ordVendorId = ordVendorId;
//	}
//
//	public String getOrdVendorName() {
//		return ordVendorName;
//	}
//
//	public void setOrdVendorName(String ordVendorName) {
//		this.ordVendorName = ordVendorName;
//	}
//
//	public Long getVendorId() {
//		return vendorId;
//	}
//
//	public void setVendorId(Long vendorId) {
//		this.vendorId = vendorId;
//	}
//
//	public String getVendorName() {
//		return vendorName;
//	}
//
//	public void setVendorName(String vendorName) {
//		this.vendorName = vendorName;
//	}
//
//	public Long getIsSecKill() {
//		return isSecKill;
//	}
//
//	public void setIsSecKill(Long isSecKill) {
//		this.isSecKill = isSecKill;
//	}
//
//	public String getBusinessPlat() {
//		return businessPlat;
//	}
//
//	public void setBusinessPlat(String businessPlat) {
//		this.businessPlat = businessPlat;
//	}
//
//	public String getRegisterDomain() {
//		return registerDomain;
//	}
//
//	public void setRegisterDomain(String registerDomain) {
//		this.registerDomain = registerDomain;
//	}
//
//	public Double getSucIncome() {
//		return sucIncome;
//	}
//
//	public void setSucIncome(Double sucIncome) {
//		this.sucIncome = sucIncome;
//	}
//
//	public Double getSucProfit() {
//		return sucProfit;
//	}
//
//	public void setSucProfit(Double sucProfit) {
//		this.sucProfit = sucProfit;
//	}
//
//	public Timestamp getDatachangeLasttime() {
//		return datachangeLasttime;
//	}
//
//	public void setDatachangeLasttime(Timestamp datachangeLasttime) {
//		this.datachangeLasttime = datachangeLasttime;
//	}
//
//}