package com.ctrip.tour.business.dashboard.tktBusiness.strategy.impl;

import com.ctrip.soa._24922.*;
import com.ctrip.soa._27181.BIBaseReportQueryServiceClient;
import com.ctrip.soa._27181.GetRawDataRequestType;
import com.ctrip.soa._27181.GetRawDataResponseType;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.*;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.SinglePeriodTrendLineBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.bo.ExamineConfigBo;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardExamineeConfigV2Dao;

import com.ctrip.tour.business.dashboard.tktBusiness.helper.Bus1And2DayTourHelper;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.MetricHelper;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.MultiPeriodMappingHelper;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.SwitchNewTableHelper;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.google.common.util.concurrent.ListenableFuture;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * 包含国内日游和出境日游的策略
 */
@Component
public class Bus1And2DayTourStrategy {


    @Autowired
    private RemoteConfig remoteConfig;

    @Autowired
    private BIBaseReportQueryServiceClient baseReportQueryServiceClient;

    @Autowired
    private BusinessDashboardExamineeConfigV2Dao examineeConfigV2Dao;

    @Autowired
    private SinglePeriodTrendLineBiz singlePeriodTrendLineBiz;

    @Autowired
    private SwitchNewTableHelper switchNewTableHelper;


    public MetricDetailInfo getSingleMetricCardData(TimeFilter timeFilter,
                                                    MetricInfoBean metricInfoBean,
                                                    String d,
                                                    String metric,
                                                    String subMetric) throws Exception {
        MetricDetailInfo metricDetailInfo = new MetricDetailInfo();
        metricDetailInfo.setSubMetric(subMetric);
        String momType = Bus1And2DayTourHelper.getMomType(timeFilter, d);
        metricDetailInfo.setMomType(momType);

        Map<String, Double> dimMap = new HashMap<>();
        metricDetailInfo.setDimData(dimMap);

        //获取当期数据
        SqlParamterBean currentBean = Bus1And2DayTourHelper.getMetricCardSqlBean("current", momType, timeFilter, metricInfoBean, d, remoteConfig, null, subMetric);
        GetRawDataRequestType currentReq = currentBean.convertBeanToRequest(true);
        ListenableFuture<GetRawDataResponseType> currentResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(currentReq);

        //获取目标
        SqlParamterBean targetBean = Bus1And2DayTourHelper.getMetricCardSqlBean("target", momType, timeFilter, metricInfoBean, d, remoteConfig, null, subMetric);
        GetRawDataRequestType targetReq = targetBean.convertBeanToRequest(true);
        ListenableFuture<GetRawDataResponseType> targetResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(targetReq);

        //获取去年数据
        SqlParamterBean lastyearBean = Bus1And2DayTourHelper.getMetricCardSqlBean("lastyear", momType, timeFilter, metricInfoBean, d, remoteConfig, null, subMetric);
        GetRawDataRequestType lastyearReq = lastyearBean.convertBeanToRequest(true);
        ListenableFuture<GetRawDataResponseType> lastyearResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(lastyearReq);
        //获取2019年数据
        SqlParamterBean _2019Bean = Bus1And2DayTourHelper.getMetricCardSqlBean("2019", momType, timeFilter, metricInfoBean, d, remoteConfig, null, subMetric);
        GetRawDataRequestType _2019Req = _2019Bean.convertBeanToRequest(true);
        ListenableFuture<GetRawDataResponseType> _2019ResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(_2019Req);

        //获取环比数据
        SqlParamterBean momBean = Bus1And2DayTourHelper.getMetricCardSqlBean("mom", momType, timeFilter, metricInfoBean, d, remoteConfig, null, subMetric);
        //环比的当前数据
        GetRawDataRequestType momReq = momBean.convertBeanToRequest(true);
        ListenableFuture<GetRawDataResponseType> momRes = switchNewTableHelper.switchRemoteDatabaseAsync(momReq);
        //环比的上一期数据
        GetRawDataRequestType momReq2 = momBean.convertBeanToRequest2(true);
        ListenableFuture<GetRawDataResponseType> momRes2 = switchNewTableHelper.switchRemoteDatabaseAsync(momReq2);

        //指标卡基础数据
        Bus1And2DayTourHelper.processMetricCardBaseData(currentResFuture.get(), targetResFuture.get(), dimMap);
        //指标卡完成率数据
        Bus1And2DayTourHelper.processMetricCardCompleteData(dimMap, metric, subMetric);
        //指标卡同比数据
        Bus1And2DayTourHelper.processMetricCardPopData(lastyearResFuture.get(), dimMap, metric, "lastyear", subMetric);
        Bus1And2DayTourHelper.processMetricCardPopData(_2019ResFuture.get(), dimMap, metric, "2019", subMetric);
        //指标卡环比数据
        Bus1And2DayTourHelper.processMetricCardMomData(momRes.get(), momRes2.get(), dimMap, metric, momType, subMetric);

        return metricDetailInfo;
    }


    public GetTrendLineDataResponseType getSingleTrendlineData(GetTrendLineDataRequestType request,
                                                               String d) throws Exception {
        GetTrendLineDataResponseType response = new GetTrendLineDataResponseType();
        List<TrendLineDetailInfo> trendLineDetailInfoList = new ArrayList<>();
        response.setTrendLineDetailInfoList(trendLineDetailInfoList);


        TimeFilter timeFilter = request.getTimeFilter();
        String domainName = request.getDomainName();
        String metric = request.getMetric();
        String subMetric = request.getSubMetric();
        List<Future<SinglePeriodDataBean>> futureList = new ArrayList<>();

        List<ExamineConfigBean> examineConfigBeanList = MultiPeriodMappingHelper.getMultiPeriodConfigList(timeFilter, d, examineeConfigV2Dao.queryMetricAllConfig(domainName, d, "1"), null);

        for (ExamineConfigBean bean : examineConfigBeanList) {
            ExamineConfigBo bo = new ExamineConfigBo();
            MetricInfoBean metricInfoBean = bo.getMetricInfoBean(bean.getBusinessDashboardExamineeConfigV2(), remoteConfig);

            //获取当期数据
            SqlParamterBean currentBean = Bus1And2DayTourHelper.getMetricCardSqlBean("current", null, null, metricInfoBean, d, remoteConfig, bean, subMetric);
            GetRawDataRequestType currentReq = currentBean.convertBeanToRequest(true);

            //获取目标
            SqlParamterBean targetBean = Bus1And2DayTourHelper.getMetricCardSqlBean("target", null, null, metricInfoBean, d, remoteConfig, bean, subMetric);
            GetRawDataRequestType targetReq = targetBean.convertBeanToRequest(true);

            //获取去年数据
            SqlParamterBean lastyearBean = Bus1And2DayTourHelper.getMetricCardSqlBean("lastyear", null, null, metricInfoBean, d, remoteConfig, bean, subMetric);
            GetRawDataRequestType lastyearReq = lastyearBean.convertBeanToRequest(true);

            //获取2019年数据
            SqlParamterBean _2019Bean = Bus1And2DayTourHelper.getMetricCardSqlBean("2019", null, null, metricInfoBean, d, remoteConfig, bean, subMetric);
            GetRawDataRequestType _2019Req = _2019Bean.convertBeanToRequest(true);

            futureList.add(singlePeriodTrendLineBiz.getBus1And2DayTourPeriodTrendLineData(currentReq, targetReq, lastyearReq, _2019Req, bean, metricInfoBean));

        }


        //转化汇总返回数据
        PeriodDataBean periodDataBean = new PeriodDataBean();
        periodDataBean.setBeanData(futureList);

        List<String> timeList;
        //有效时间范围
        if ("overseaDayTour".equals(subMetric)) {
            timeList = DateUtil.getAllSelectedTime(request.getTimeFilter(), "overseaDayTour");
        } else {
            timeList = DateUtil.getAllSelectedTime(request.getTimeFilter(), "oversea");
        }


        //趋势线基础数据
        Bus1And2DayTourHelper.processTrendLineBaseData(trendLineDetailInfoList, periodDataBean, timeList, metric, subMetric);
        //趋势线同比数据
        Bus1And2DayTourHelper.processTrendLinePopData(trendLineDetailInfoList, periodDataBean, timeList, metric, "lastyear", subMetric);
        Bus1And2DayTourHelper.processTrendLinePopData(trendLineDetailInfoList, periodDataBean, timeList, metric, "2019", subMetric);

        return response;
    }


    public GetTableDataResponseType getSingleTableData(GetTableDataRequestType request,
                                                       MetricInfoBean metricInfoBean,
                                                       String d) throws Exception {

        GetTableDataResponseType response = new GetTableDataResponseType();
        List<TableDataItem> tableDataItemList = new ArrayList<>();
        response.setTableDataItemList(tableDataItemList);
        String momType = Bus1And2DayTourHelper.getMomType(request.getTimeFilter(), d);
        response.setMomType(momType);

        DrillDownFilter drillDownFilter = request.getDrillDownFilter();
        String field = MetricHelper.getDrillDownColumnName(drillDownFilter.getField());
        String metric = request.getMetric();
        String subMetric = request.getSubMetric();
        DrillDownFieldBean configBean = remoteConfig.getDrillDownFieldBean(metric, subMetric, field);

        //如果按商拓下钻 判定是不是需要展示同比数据
        Boolean needShowExamineePopData = DateUtil.isLastestQuarter(request.getTimeFilter(), d);
        response.setNeedShowExamineePopData(needShowExamineePopData);

        //获取当前数据
        SqlParamterBean currentBean = Bus1And2DayTourHelper.getTableDataSqlBean(request, d, metricInfoBean, configBean,
                remoteConfig, "current", null, momType);
        GetRawDataRequestType currentReq = currentBean.convertBeanToRequest(true);
        ListenableFuture<GetRawDataResponseType> currentResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(currentReq);

        //获取目标数据
        SqlParamterBean targetBean = Bus1And2DayTourHelper.getTableDataSqlBean(request, d, metricInfoBean, configBean,
                remoteConfig, "target", null, momType);
        GetRawDataRequestType targetReq = targetBean.convertBeanToRequest(true);
        ListenableFuture<GetRawDataResponseType> targetResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(targetReq);

        Bus1And2DayTourHelper.processTableBaseData(currentResFuture.get(), targetResFuture.get(), tableDataItemList, metric, subMetric);

        //收集下钻字段的数据
        List<String> pagingFieldValueList = tableDataItemList.stream()
                .map(i -> i.getFieldMap().get(field))
                .collect(Collectors.toList());

        //如果当前有数据 则去获取同环比
        if (GeneralUtil.isNotEmpty(pagingFieldValueList)) {
            //下钻层级是商拓 且不是最新季度数据  不需要展示同环比
            if ("examinee".equals(field) && !needShowExamineePopData) {
                return response;
            }

            //获取同比当期数据
            SqlParamterBean currentpopBean = Bus1And2DayTourHelper.getTableDataSqlBean(request, d, metricInfoBean, configBean,
                    remoteConfig, "currentpop", null, momType);
            GetRawDataRequestType currentpopReq = currentpopBean.convertBeanToRequest(true);
            ListenableFuture<GetRawDataResponseType> currentpopResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(currentpopReq);

            //获取去年数据
            SqlParamterBean lastyearBean = Bus1And2DayTourHelper.getTableDataSqlBean(request, d, metricInfoBean, configBean,
                    remoteConfig, "lastyear", null, momType);
            GetRawDataRequestType lastyearReq = lastyearBean.convertBeanToRequest(true);
            ListenableFuture<GetRawDataResponseType> lastyearResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(lastyearReq);

            //获取2019年数据
            SqlParamterBean _2019Bean = Bus1And2DayTourHelper.getTableDataSqlBean(request, d, metricInfoBean, configBean,
                    remoteConfig, "2019", null, momType);
            GetRawDataRequestType _2019Req = _2019Bean.convertBeanToRequest(true);
            ListenableFuture<GetRawDataResponseType> _2019ResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(_2019Req);

            Bus1And2DayTourHelper.processTableCurrentPopData(currentpopResFuture.get(), tableDataItemList);
            Bus1And2DayTourHelper.processTablePopData(lastyearResFuture.get(), tableDataItemList, "lastyear");
            Bus1And2DayTourHelper.processTablePopData(_2019ResFuture.get(), tableDataItemList, "2019");

            //只有首页需要获取环比数据
            if ("firstpage".equals(request.getSource())) {
                //获取环比数据
                SqlParamterBean momBean = Bus1And2DayTourHelper.getTableDataSqlBean(request, d, metricInfoBean, configBean,
                        remoteConfig, "mom", null, momType);
                //环比的当前数据
                GetRawDataRequestType momReq = momBean.convertBeanToRequest(true);
                ListenableFuture<GetRawDataResponseType> momResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(momReq);
                //环比的上一期数据
                GetRawDataRequestType momReq2 = momBean.convertBeanToRequest2(true);
                ListenableFuture<GetRawDataResponseType> momRes2Future = switchNewTableHelper.switchRemoteDatabaseAsync(momReq2);
                Bus1And2DayTourHelper.processTableMomData(momResFuture.get(), momRes2Future.get(), tableDataItemList, momType);
            }
        }

        response.setTotalNum(currentResFuture.get().getTotalNum());
        return response;
    }


    public GetDrillDownBaseInfoResponseType getSingleDrillDownBaseInfo(GetDrillDownBaseInfoRequestType request,
                                                                       MetricInfoBean metricInfoBean,
                                                                       String d) throws Exception {

        GetDrillDownBaseInfoResponseType response = new GetDrillDownBaseInfoResponseType();

        Boolean needSearch = request.isNeedSearch();
        if (GeneralUtil.isEmpty(needSearch)) {
            request.setNeedSearch(false);
            needSearch = false;
        }
        List<String> fieldList = new ArrayList<>();
        String subMetric = request.getSubMetric();
        if (needSearch) {
            fieldList.add(MetricHelper.getDrillDownColumnName(request.getSearchField()));
        } else {
            fieldList.addAll(Bus1And2DayTourHelper.getDrillDownFieldList(metricInfoBean, remoteConfig, subMetric));
            response.setDefaultChosenField(MetricHelper.getDataBaseColumnName(fieldList.get(0)));
        }
        Map<String, Future<GetRawDataResponseType>> fieldMap = new HashMap<>();
        for (String field : fieldList) {
            SqlParamterBean sqlParamterBean = Bus1And2DayTourHelper.getDrillDownBaseInfoSqlBean(field, request, d, metricInfoBean, remoteConfig);
            GetRawDataRequestType fieldRequest = sqlParamterBean.convertBeanToRequest(false);
            fieldMap.put(field, switchNewTableHelper.switchRemoteDatabaseAsync(fieldRequest));
        }
        List<FieldDataItem> fieldDataItemList = new ArrayList<>();
        response.setFieldDataItemList(fieldDataItemList);
        for (String field : fieldList) {
            Bus1And2DayTourHelper.processDrillDownBaseInfo(field, fieldMap.get(field).get(), fieldDataItemList);
        }

        return response;
    }


}
