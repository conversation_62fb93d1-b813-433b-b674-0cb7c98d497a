package com.ctrip.tour.business.dashboard.tktBusiness.enums;

public enum TrendLineNameEnum {
    COMPLETE_VALUE("completeValue"),
    COMPLETE_RATE("completeRate"),
    YOY_VALUE("yoyValue"),
    TARGET_VALUE("targetValue"),
    WEIGHT_DEFECT_RATE("weightedDefectRate"),
    CW_NUM("cwNum"),
    FW_NUM("fwNum"),
    TW_NUM("twNum"),
    CW_GAP("cwGap"),
    FW_GAP("fwGap"),
    TW_GAP("twGap"),
    CW_RATE("cwRate"),
    FW_RATE("fwRate"),
    TW_RATE("twRate");

    private String name;

    TrendLineNameEnum(String name) {
        this.name = name;
    }


    public String getName() {
        return name;
    }

    public static TrendLineNameEnum getByCode(String code) {
        for (TrendLineNameEnum buType : TrendLineNameEnum.values()) {
            if (buType.getName().equals(code)) {
                return buType;
            }
        }
        return null;
    }
}
