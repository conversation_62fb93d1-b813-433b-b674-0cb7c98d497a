package com.ctrip.tour.business.dashboard.sightArchives.entity.domesticMetricEntity.bean;

import lombok.Data;

import java.util.List;

@Data
public class Domestic11And12WeaknessParam {
    Integer pageIndex;
    Integer pageSize;
    String field;
    String metric;
    //分区
    String d;
    //	考核对象
    List<String> examine;
    List<String> provinceNameList;
    List<String> businessRegionNameList;



    //考核指标集合
    String examineMetricList;
    //考核层级
    String examineLevel;
    //统计维度ID
    String statisticsDimId;
    //考核年份
    String examineYear;
    //考核季度
    List<String> examineQuarter;
    //考核月份
    String examineMonth;
    //	考核日期
    String examineDate;
    //序号小于等于5的标识符
    String isCoefficientIdentifier;

}
