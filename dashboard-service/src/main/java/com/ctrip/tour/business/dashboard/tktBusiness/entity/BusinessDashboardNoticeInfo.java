package com.ctrip.tour.business.dashboard.tktBusiness.entity;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2024-05-10
 */
@Entity
@Database(name = "TtdReportDB_W")
@Table(name = "business_dashboard_notice_info")
public class BusinessDashboardNoticeInfo implements DalPojo {

    /**
     * 主键
     */
    @Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long id;

    /**
     * qmq自己生成的值
     */
	@Column(name = "messageId")
	@Type(value = Types.VARCHAR)
	private String messageId;

    /**
     * 消息类型；1:通知[需要汇总]; 2: 任务[收到就发送]
     */
	@Column(name = "messageType")
	@Type(value = Types.VARCHAR)
	private String messageType;

    /**
     * 场景名字
     */
	@Column(name = "sceneType")
	@Type(value = Types.VARCHAR)
	private String sceneType;

    /**
     * 消息内容
     */
	@Column(name = "message")
	@Type(value = Types.LONGVARCHAR)
	private String message;

    /**
     * 接收人工号
     */
	@Column(name = "receiver")
	@Type(value = Types.VARCHAR)
	private String receiver;

	/**
	 * 日期
	 */
	@Column(name = "date")
	@Type(value = Types.VARCHAR)
	private String date;

    /**
     * 更新时间
     */
	@Column(name = "datachange_lasttime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getMessageId() {
		return messageId;
	}

	public void setMessageId(String messageId) {
		this.messageId = messageId;
	}

	public String getMessageType() {
		return messageType;
	}

	public void setMessageType(String messageType) {
		this.messageType = messageType;
	}

	public String getSceneType() {
		return sceneType;
	}

	public void setSceneType(String sceneType) {
		this.sceneType = sceneType;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public String getReceiver() {
		return receiver;
	}

	public void setReceiver(String receiver) {
		this.receiver = receiver;
	}

	public Timestamp getDatachangeLasttime() {
		return datachangeLasttime;
	}

	public void setDatachangeLasttime(Timestamp datachangeLasttime) {
		this.datachangeLasttime = datachangeLasttime;
	}

	public String getDate() {
		return date;
	}

	public void setDate(String date) {
		this.date = date;
	}
}