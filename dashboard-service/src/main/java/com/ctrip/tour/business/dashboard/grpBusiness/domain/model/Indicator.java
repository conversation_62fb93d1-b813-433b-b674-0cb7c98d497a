package com.ctrip.tour.business.dashboard.grpBusiness.domain.model;


import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

@Setter
@Getter
public class Indicator {
    private String indicator_name;
    private String indicator_name_cn;
    private String indicator_type;
    private String indicator_format;
    private String indicator_key;
    private String indicator_key_name;
    private Boolean indicator_is_dimension;
    private Boolean indicator_is_support_sorting;

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true; // 地址相等
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false; // 对象为空或类不同
        }
        Indicator other = (Indicator) obj;
        return Objects.equals(indicator_name, other.indicator_name);
    }

    @Override
    public int hashCode() {
        int result = 17;
        return 31 * result + (indicator_name != null ? indicator_name.hashCode() : 0);
    }
}

