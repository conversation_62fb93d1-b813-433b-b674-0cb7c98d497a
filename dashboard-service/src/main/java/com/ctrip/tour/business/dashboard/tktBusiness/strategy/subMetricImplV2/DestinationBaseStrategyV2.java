package com.ctrip.tour.business.dashboard.tktBusiness.strategy.subMetricImplV2;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.ctrip.soa._24922.*;
import com.ctrip.soa._27181.BIBaseReportQueryServiceClient;
import com.ctrip.soa._27181.GetRawDataRequestType;
import com.ctrip.soa._27181.GetRawDataResponseType;
import com.ctrip.soa._27181.PreparedParameterBean;
import com.ctrip.tour.business.dashboard.grpBusiness.dao.starrocks.StarRocksDao;
import com.ctrip.tour.business.dashboard.sightArchives.dao.overseametric.*;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.CdmOrdTtdOverseasPerformanceIndexBO;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.DimOrdTtdDestinationTargetBO;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.DimOrdTtdPersonTargetBO;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.DimOrdTtdTargetBO;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.bean.DimOrdTtdDestinationTargetParamBean;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.bean.DimOrdTtdPersonTargetParamBean;
import com.ctrip.tour.business.dashboard.sightArchives.entity.overseametric.bean.OverseasPerformanceInfoParamBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.*;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.OverseaSinglePeriodTrendLineBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.bo.OverSeaExamineConfigBO;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.enums.*;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.InputArgumentException;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.Bus101102Helper;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.Bus103Helper;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.OverseaMetricHelper;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.SwitchNewTableHelper;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.UserUtil;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ListenableFuture;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.sql.Types;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Component
public class DestinationBaseStrategyV2 implements InitializingBean, DisposableBean {

    @Autowired
    StarRocksDao starRocksDao;

    @Autowired
    private BIBaseReportQueryServiceClient baseReportQueryServiceClient;

    @Autowired
    private RemoteConfig remoteConfig;

    @Autowired
    private OverseaSinglePeriodTrendLineBiz overseaSinglePeriodTrendLineBiz;

    @Autowired
    private SwitchNewTableHelper switchNewTableHelper;
    @Autowired
    private CdmOrdTtdOverseasPerformanceIndexDao cdmOrdTtdOverseasPerformanceIndexDao;
    @Autowired
    private CdmOrdTtdOverseasPerformanceIndexAddrDao cdmOrdTtdOverseasPerformanceIndexAddrDao;
    @Autowired
    private DimOrdTtdDestinationTargetConfigDao dimOrdTtdDestinationTargetConfigDao;
    @Autowired
    private DimOrdTtdPersonTargetConfigDao dimOrdTtdPersonTargetConfigDao;
    @Autowired
    private DimOrdTtdSiteChannelTargetConfigDao dimOrdTtdSiteChannelTargetConfigDao;
    @Autowired
    private DimOrdTtdTargetConfigDao dimOrdTtdTargetConfigDao;

    private static ExecutorService executor;


    @Override
    public void afterPropertiesSet() throws Exception {
        executor = Executors.newCachedThreadPool();
    }

    @Override
    public void destroy() throws Exception {
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
    }

    /**
     * 季度维度查询指标
     *
     * @param timeFilter
     * @param metricInfoBean
     * @param d
     * @param subMetric
     * @param request
     * @return
     * @throws Exception
     */
    public Future<OveaseaSubMetric> getBus101102110SubMetricCardDataQuarter(TimeFilter timeFilter, OverseaMetricInfoBeanV2 metricInfoBean,
                                                                            String d,
                                                                            String subMetric,
                                                                            GetOverseaMetricCardDataV2RequestType request) throws Exception {
        OveaseaSubMetric oveaseaSubMetric = new OveaseaSubMetric();
        oveaseaSubMetric.setSubMetric(subMetric);
        List<String> buType = OverseaMetricHelper.getBuType(request.getBusinessLine());
        List<String> buTypeTarget = OverseaMetricHelper.getBuTypeWithTarget(request.getBusinessLine());
        String isCT = OverseaMetricHelper.judgeCT(subMetric);
        //填充一些前端需要的额外信息
        oveaseaSubMetric.setMomType(OverseaMetricHelper.getMomType(timeFilter, d));
        Bus101102Helper.setMetricCardDrillDownParmaterV2(oveaseaSubMetric, subMetric, metricInfoBean, remoteConfig, timeFilter);

        String quarter = timeFilter.quarter;
        OverseaPersonConfigResponse personConfigResponse = getExamineLevel(quarter, timeFilter.getYear(), buType, metricInfoBean.getMetric(), metricInfoBean.getDomainName(), d);
        OverseaPersonConfigResponse personConfigWith30RelatedBean = getExamineLevelWith30(buType, metricInfoBean.getMetric(), metricInfoBean.getDomainName(), d, true);
        OverseaPersonConfigResponse personConfigWithLast30RelatedBean = getExamineLevelWith30(buType, metricInfoBean.getMetric(), metricInfoBean.getDomainName(), d, false);

        // 获取目标来源，区分组织目标和个人目标
        double targetValue = getTargetValue(timeFilter, metricInfoBean.getDomainName(), metricInfoBean.getMetric(), subMetric, personConfigResponse, buTypeTarget, quarter, d);
        Boolean isCalComplete = targetValue != 0;

        // 获取当期达成值
        double completeCurrentValue = getCompleteValueWithHOrQ("default", timeFilter, d, request.getDomainName(), buType, isCT, personConfigResponse, quarter, isCalComplete, subMetric, metricInfoBean.getMetric());

        // 获取去年达成值
        double completeLastYearValue = getCompleteValueWithHOrQ("lastYear", timeFilter, d, request.getDomainName(), buType, isCT, personConfigResponse, quarter, isCalComplete, subMetric, metricInfoBean.getMetric());

        // 获取环比相关数据
        double momData;
        if ("30days".equals(oveaseaSubMetric.getMomType())) {
            // 最近30天数据
            double complete30DaysYearValue = getCompleteValueWithHOrQ("30days", timeFilter, d, request.getDomainName(), buType, isCT, personConfigWith30RelatedBean, quarter, isCalComplete, subMetric, metricInfoBean.getMetric());
            // 上个30天数据
            double completeLast30DaysYearValue = getCompleteValueWithHOrQ("last30days", timeFilter, d, request.getDomainName(), buType, isCT, personConfigWithLast30RelatedBean, quarter, isCalComplete, subMetric, metricInfoBean.getMetric());

            momData = completeLast30DaysYearValue == 0 ? 0 : (complete30DaysYearValue - completeLast30DaysYearValue) / completeLast30DaysYearValue;
        } else {
            // 上个周期数据(半年或季)
            double completeLastQorHYearValue = getCompleteValueWithHOrQ("lastCycle", timeFilter, d, request.getDomainName(), buType, isCT, personConfigResponse, quarter, isCalComplete, subMetric, metricInfoBean.getMetric());
            momData = completeLastQorHYearValue == 0 ? 0 : (completeCurrentValue - completeLastQorHYearValue) / completeLastQorHYearValue;
        }
        oveaseaSubMetric.setCompleteValue(completeCurrentValue);
        oveaseaSubMetric.setTargetValue(targetValue);
        oveaseaSubMetric.setCompleteRate(targetValue == 0 ? 0 : completeCurrentValue / targetValue);
        oveaseaSubMetric.setYoyValue(completeLastYearValue == 0 ? 0 : (completeCurrentValue - completeLastYearValue) / completeLastYearValue);
        oveaseaSubMetric.setPopValue(momData);

        return new AsyncResult<>(oveaseaSubMetric);
    }

    /**
     * 半年维度查询指标
     *
     * @param timeFilter
     * @param metricInfoBean
     * @param d
     * @param subMetric
     * @param request
     * @return
     * @throws Exception
     */
    public Future<OveaseaSubMetric> getBus101102110SubMetricCardDataHalf(TimeFilter timeFilter,
                                                                         OverseaMetricInfoBeanV2 metricInfoBean,
                                                                         String d,
                                                                         String subMetric,
                                                                         GetOverseaMetricCardDataV2RequestType request) throws Exception {
        OveaseaSubMetric oveaseaSubMetric = new OveaseaSubMetric();
        oveaseaSubMetric.setSubMetric(subMetric);
        List<String> buType = OverseaMetricHelper.getBuType(request.getBusinessLine());
        List<String> buTypeTarget = OverseaMetricHelper.getBuTypeWithTarget(request.getBusinessLine());
        String isCT = OverseaMetricHelper.judgeCT(subMetric);
        //填充一些前端需要的额外信息
        oveaseaSubMetric.setMomType(OverseaMetricHelper.getMomType(timeFilter, d));
        Bus101102Helper.setMetricCardDrillDownParmaterV2(oveaseaSubMetric, subMetric, metricInfoBean, remoteConfig, timeFilter);

        String quarter1 = metricInfoBean.getQuarters().get(0);
        String quarter2 = metricInfoBean.getQuarters().get(1);
        OverseaPersonConfigResponse personConfigResponse1 = getExamineLevel(quarter1, timeFilter.getYear(), buType, metricInfoBean.getMetric(), metricInfoBean.getDomainName(), d);
        OverseaPersonConfigResponse personConfigResponse2 = getExamineLevel(quarter2, timeFilter.getYear(), buType, metricInfoBean.getMetric(), metricInfoBean.getDomainName(), d);

        OverseaPersonConfigResponse personConfigWith30RelatedBean = getExamineLevelWith30(buType, metricInfoBean.getMetric(), metricInfoBean.getDomainName(), d, true);
        OverseaPersonConfigResponse personConfigWithLast30RelatedBean = getExamineLevelWith30(buType, metricInfoBean.getMetric(), metricInfoBean.getDomainName(), d, false);


        // 获取目标来源，区分组织目标和个人目标
        double targetQ1 = getTargetValue(timeFilter, metricInfoBean.getDomainName(), metricInfoBean.getMetric(), subMetric, personConfigResponse1, buTypeTarget, quarter1, d);
        double targetQ2 = getTargetValue(timeFilter, metricInfoBean.getDomainName(), metricInfoBean.getMetric(), subMetric, personConfigResponse2, buTypeTarget, quarter2, d);
        double targetValue = targetQ1 + targetQ2;
        Boolean isCalCompleteQ1 = targetQ1 != 0;
        Boolean isCalCompleteQ2 = targetQ2 != 0;

        // 获取当期达成值
        double completeCurrentValue = getCompleteValueWithHOrQ("default", timeFilter, d, request.getDomainName(), buType, isCT, personConfigResponse1, quarter1, isCalCompleteQ1, subMetric, metricInfoBean.getMetric()) +
                getCompleteValueWithHOrQ("default", timeFilter, d, request.getDomainName(), buType, isCT, personConfigResponse2, quarter2, isCalCompleteQ2, subMetric, metricInfoBean.getMetric());

        // 获取去年达成值
        double completeLastYearValue = getCompleteValueWithHOrQ("lastYear", timeFilter, d, request.getDomainName(), buType, isCT, personConfigResponse1, quarter1, isCalCompleteQ1, subMetric, metricInfoBean.getMetric()) +
                getCompleteValueWithHOrQ("lastYear", timeFilter, d, request.getDomainName(), buType, isCT, personConfigResponse2, quarter2, isCalCompleteQ2, subMetric, metricInfoBean.getMetric());

        // 获取环比相关数据
        double momData;
        if ("30days".equals(oveaseaSubMetric.getMomType())) {
            // 最近30天数据
            double complete30DaysYearValue = getCompleteValueWithHOrQ("30days", timeFilter, d, request.getDomainName(), buType, isCT, personConfigWith30RelatedBean, quarter1, isCalCompleteQ1, subMetric, metricInfoBean.getMetric());

            // 上个30天数据
            double completeLast30DaysYearValue = getCompleteValueWithHOrQ("last30days", timeFilter, d, request.getDomainName(), buType, isCT, personConfigWithLast30RelatedBean, quarter1, isCalCompleteQ1, subMetric, metricInfoBean.getMetric());

            momData = completeLast30DaysYearValue == 0 ? 0 : (complete30DaysYearValue - completeLast30DaysYearValue) / completeLast30DaysYearValue;
        } else {
            // 上个周期数据(半年或季)
            double completeLastQorHYearValue = getCompleteValueWithHOrQ("lastCycle", timeFilter, d, request.getDomainName(), buType, isCT, personConfigResponse1, quarter1, isCalCompleteQ1, subMetric, metricInfoBean.getMetric()) +
                    getCompleteValueWithHOrQ("lastCycle", timeFilter, d, request.getDomainName(), buType, isCT, personConfigResponse2, quarter2, isCalCompleteQ2, subMetric, metricInfoBean.getMetric());

            momData = completeLastQorHYearValue == 0 ? 0 : (completeCurrentValue - completeLastQorHYearValue) / completeLastQorHYearValue;
        }
        oveaseaSubMetric.setCompleteValue(completeCurrentValue);
        oveaseaSubMetric.setTargetValue(targetValue);
        oveaseaSubMetric.setCompleteRate(targetValue == 0 ? 0 : completeCurrentValue / targetValue);
        oveaseaSubMetric.setYoyValue(completeLastYearValue == 0 ? 0 : (completeCurrentValue - completeLastYearValue) / completeLastYearValue);
        oveaseaSubMetric.setPopValue(momData);

        return new AsyncResult<>(oveaseaSubMetric);
    }

    /**
     * 获取目标表数据
     *
     * @param timeFilter
     * @param domainName
     * @param subMetric
     * @param metric
     * @param configResponse
     * @return
     * @throws ParseException
     */
    public Double getTargetValue(TimeFilter timeFilter,
                                 String domainName,
                                 String metric,
                                 String subMetric,
                                 OverseaPersonConfigResponse configResponse,
                                 List<String> buType,
                                 String quarter,
                                 String d) {
        Boolean isOrgTarget = OverseaMetricHelper.selectTargetFrom(configResponse.getDestinationLevelList());
        // 个人目标
        OverseasRelatedSearchParamBean personalTargetParam = Bus101102Helper.generate101102110PersonTargetOverseaInfoSearch(timeFilter, subMetric, metric, domainName, buType, quarter, d);
        Double targetValue = dimOrdTtdPersonTargetConfigDao.queryOverseaDestinationTargetInfo(personalTargetParam);
        if (targetValue != null) {
            return targetValue;
        } else if (isOrgTarget) {
            // 目的地组织目标
            if (buType.size() == 2) {
                OverseaPersonConfigResponse personConfigResponseTic = getExamineLevel(quarter, timeFilter.getYear(), Collections.singletonList("门票"), metric, domainName, d);//NOSONAR
                OverseaPersonConfigResponse personConfigResponseAct = getExamineLevel(quarter, timeFilter.getYear(), Arrays.asList("日游", "活动"), metric, domainName, d);//NOSONAR
                boolean isTicValid = !CollectionUtils.isEmpty(personConfigResponseTic.getDestinationLevelList());
                boolean isActValid = !CollectionUtils.isEmpty(personConfigResponseAct.getDestinationLevelList());
                if (!isTicValid && !isActValid) {
                    targetValue = 0.00;
                    return targetValue;
                }
                OverseaPersonConfigResponse finalConfig = isTicValid ? personConfigResponseTic : personConfigResponseAct;
                List<String> newBuTypes = isTicValid ? Collections.singletonList("门票") : Collections.singletonList("玩乐");//NOSONAR
                if (isTicValid && isActValid) {
                    finalConfig = configResponse;  // 两者有效时使用外部配置
                    newBuTypes = Arrays.asList("门票", "玩乐");//NOSONAR
                }

                OverseasRelatedSearchParamBean destinationTargetParam = Bus101102Helper.generate101102110DestinationTargetOverseaInfoSearch(timeFilter, subMetric, finalConfig, newBuTypes, metric, quarter, d);
                targetValue = dimOrdTtdDestinationTargetConfigDao.queryOverseaDestinationTargetInfo(destinationTargetParam);
            } else {
                if (CollectionUtils.isEmpty(configResponse.getDestinationLevelList())) {
                    targetValue = 0.00;
                    return targetValue;
                }
                OverseasRelatedSearchParamBean destinationTargetParam = Bus101102Helper.generate101102110DestinationTargetOverseaInfoSearch(timeFilter, subMetric, configResponse, buType, metric, quarter, d);
                targetValue = dimOrdTtdDestinationTargetConfigDao.queryOverseaDestinationTargetInfo(destinationTargetParam);
            }
        } else {
            targetValue = 0d;
        }
        return targetValue;
    }


    /**
     * 抽象完成值查询提取
     *
     * @param searchType
     * @param timeFilter
     * @param d
     * @param domainName
     * @param buTypeList
     * @param ct
     * @return
     * @throws ParseException
     */
    public Double getCompleteValueWithHOrQ(String searchType,
                                           TimeFilter timeFilter,
                                           String d,
                                           String domainName,
                                           List<String> buTypeList,
                                           String ct,
                                           OverseaPersonConfigResponse configResponse,
                                           String quarter,
                                           Boolean isCalculateComplete,
                                           String subMetric,
                                           String metric) throws ParseException {
        // 如果目标为空，不计算相关的完成值
        if (!isCalculateComplete) {
            return 0.0;
        }
        if (buTypeList.size() == 3) {
            double ticTarget = getTargetValue(timeFilter, domainName, metric, subMetric, configResponse, Collections.singletonList("门票"), quarter, d);//NOSONAR
            double ActTarget = getTargetValue(timeFilter, domainName, metric, subMetric, configResponse, Collections.singletonList("玩乐"), quarter, d);//NOSONAR
            if (ticTarget == 0 && ActTarget == 0) {
                return 0.0;
            }
            if (ticTarget == 0) {
                buTypeList = Arrays.asList("活动", "日游");//NOSONAR
            }
            if (ActTarget == 0) {
                buTypeList = Collections.singletonList("门票");//NOSONAR
            }
        }
        Generate101102110SqlBean sqlBeanQ1 = new Generate101102110SqlBean(searchType, quarter, timeFilter.getYear(), buTypeList, configResponse.getDestinationLevelList(), domainName, ct, d, timeFilter.getDateType(), metric);
        OverseasRelatedSearchParamBean personalCurrentCompleteParamQ1 = Bus101102Helper.generate101102110CompleteDestinationOverseaInfoSearch(sqlBeanQ1);
        personalCurrentCompleteParamQ1.setD(d);
        return cdmOrdTtdOverseasPerformanceIndexDao.queryOverseasPerformanceInfo(personalCurrentCompleteParamQ1);
    }

    /**
     * 获取海外考核人员配置信息
     *
     * @param quarter    季度
     * @param year       年份
     * @param buType     业务类型
     * @param metric     考核指标
     * @param domainName 域名
     * @return OverseaPersonConfigResponse
     */
    public OverseaPersonConfigResponse getExamineLevel(String quarter, String year, List<String> buType, String metric, String domainName, String d) {
        OverseaPersonConfigResponse overseaPersonConfigResponse = new OverseaPersonConfigResponse();
        //目的地考核层级
        List<String> destinationLevelList = new ArrayList<>();
        //站点考核范围
        List<String> siteChannelRangeList = new ArrayList<>();
        List<OverseaMetricInfoWithMetricBean> overseaMetricInfoWithMetricBeanList = dimOrdTtdTargetConfigDao.queryOverseaPersonInfo(Collections.singletonList(quarter), year, buType, metric, domainName, "other", d);
        overseaMetricInfoWithMetricBeanList.forEach(OverseaMetricInfoWithMetricBean -> {
            if (ChannelSiteEnum.CHANNEL.getName().equals(OverseaMetricInfoWithMetricBean.getDestinationLevel())
                    || ChannelSiteTypeEnum.SITE.getName().equals(OverseaMetricInfoWithMetricBean.getDestinationLevel())) {
                return;
            }
            destinationLevelList.add(OverseaMetricInfoWithMetricBean.getDestinationLevel());
            siteChannelRangeList.add(OverseaMetricInfoWithMetricBean.getDestinationRange());
        });

        //去重
        Set<String> destinationLevelSet = new HashSet<>(destinationLevelList);
        overseaPersonConfigResponse.setDestinationLevelList(new ArrayList<>(destinationLevelSet));
        Set<String> destinationRangeSet = new HashSet<>(siteChannelRangeList);
        overseaPersonConfigResponse.setDestinationRangeList(new ArrayList<>(destinationRangeSet));
        return overseaPersonConfigResponse;
    }


    /**
     * 获取海外考核人员配置信息（30天和上个30天）
     *
     * @param buType
     * @param metric
     * @param domainName
     * @param d
     * @param is30days
     * @return
     * @throws ParseException
     */
    public OverseaPersonConfigResponse getExamineLevelWith30(List<String> buType, String metric, String domainName, String d, Boolean is30days) throws ParseException {
        OverseaPersonConfigResponse overseaPersonConfigResponse = new OverseaPersonConfigResponse();
        String lastDay = DateUtil.getDayOfInterval(d, -1);
        String startDate = DateUtil.getDayOfInterval(lastDay, -29);
        List<String> destinationLevelList = new ArrayList<>();
        List<String> yearQuarter;
        if (is30days) {
            yearQuarter = DateUtil.generateYearAndQuarter(startDate, lastDay);
        } else {
            String momEndDate = DateUtil.getDayOfInterval(d, -31);
            String momStartDate = DateUtil.getDayOfInterval(momEndDate, -29);
            yearQuarter = DateUtil.generateYearAndQuarter(momStartDate, momEndDate);
        }
        for (String yq : yearQuarter) {
            String year = yq.split("-")[0];
            String quarter = yq.split("-")[1];
            List<OverseaMetricInfoWithMetricBean> overseaMetricInfoWithMetricBeanList = dimOrdTtdTargetConfigDao.queryOverseaPersonInfo(Collections.singletonList(quarter), year, buType, metric, domainName, "other", d);
            overseaMetricInfoWithMetricBeanList.forEach(OverseaMetricInfoWithMetricBean -> {
                destinationLevelList.add(OverseaMetricInfoWithMetricBean.getDestinationLevel());
            });
        }
        Set<String> destinationLevelSet = new HashSet<>(destinationLevelList);
        overseaPersonConfigResponse.setDestinationLevelList(new ArrayList<>(destinationLevelSet));
        return overseaPersonConfigResponse;
    }


    public GetOverseaTrendLineDataV2ResponseType getBus101102110SubTrendlineData(GetOverseaTrendLineDataV2RequestType request,
                                                                                 String d,
                                                                                 List<String> timeList) throws Exception {
        GetOverseaTrendLineDataV2ResponseType response = new GetOverseaTrendLineDataV2ResponseType();
        List<OverseaTrendLine> trendLineDetailInfoList = new ArrayList<>();
        response.setTrendlines(trendLineDetailInfoList);

        String subMetric = request.getSubMetric();
        String isCT = OverseaMetricHelper.judgeCT(subMetric);
        List<OverseaTrendLineDataItem> completeItems = new ArrayList<>();
        List<OverseaTrendLineDataItem> completeRateItems = new ArrayList<>();
        List<OverseaTrendLineDataItem> yoyItems = new ArrayList<>();

        List<String> buType = OverseaMetricHelper.getBuType(request.getBusinessLine());
        List<String> buTypeTarget = OverseaMetricHelper.getBuTypeWithTarget(request.getBusinessLine());

        for (String bean : timeList) {
            String year = bean.split("-")[0];
            String quarter = bean.split("-")[1];
            request.getTimeFilter().setDateType("quarter");
            request.getTimeFilter().setQuarter(quarter);
            request.getTimeFilter().setYear(year);
            if (Integer.parseInt(year) <= 2024) {
                continue;
            }

            OverseaPersonConfigResponse personConfigResponse = getExamineLevel(quarter, year, buType, request.getMetric(), request.getDomainName(), d);

            // 获取目标来源，区分组织目标和个人目标
            Boolean isOrgTarget = OverseaMetricHelper.selectTargetFrom(personConfigResponse.getDestinationLevelList());// 个人目标
            OverseasRelatedSearchParamBean personalTargetParam = Bus101102Helper.generate101102110PersonTargetOverseaInfoSearch(request.getTimeFilter(), subMetric, request.getMetric(), request.getDomainName(), buTypeTarget, quarter, d);
            Double targetValue = dimOrdTtdPersonTargetConfigDao.queryOverseaDestinationTargetInfo(personalTargetParam);
            if (targetValue == null) {
                if (isOrgTarget) {
                    // 目的地组织目标
                    if (buTypeTarget.size() == 2) {
                        OverseaPersonConfigResponse personConfigResponseTic = getExamineLevel(quarter, year, Collections.singletonList("门票"), request.getMetric(), request.getDomainName(), d);//NOSONAR
                        OverseaPersonConfigResponse personConfigResponseAct = getExamineLevel(quarter, year, Arrays.asList("日游", "活动"), request.getMetric(), request.getDomainName(), d);//NOSONAR
                        boolean isTicValid = !CollectionUtils.isEmpty(personConfigResponseTic.getDestinationLevelList());
                        boolean isActValid = !CollectionUtils.isEmpty(personConfigResponseAct.getDestinationLevelList());
                        if (!isTicValid && !isActValid) {
                            targetValue = 0d;
                        } else {
                            OverseaPersonConfigResponse finalConfig = isTicValid ? personConfigResponseTic : personConfigResponseAct;
                            List<String> newBuTypes = isTicValid ? Collections.singletonList("门票") : Collections.singletonList("玩乐");//NOSONAR
                            if (isTicValid && isActValid) {
                                finalConfig = personConfigResponse;  // 两者有效时使用外部配置
                                newBuTypes = Arrays.asList("门票", "玩乐");//NOSONAR
                            }

                            OverseasRelatedSearchParamBean destinationTargetParam = Bus101102Helper.generate101102110DestinationTargetOverseaInfoSearch(request.getTimeFilter(), subMetric, finalConfig, newBuTypes, request.getMetric(), quarter, d);
                            targetValue = dimOrdTtdDestinationTargetConfigDao.queryOverseaDestinationTargetInfo(destinationTargetParam);
                        }
                    } else {
                        if (CollectionUtils.isEmpty(personConfigResponse.getDestinationLevelList())) {
                            targetValue = 0d;
                        } else {
                            OverseasRelatedSearchParamBean destinationTargetParam = Bus101102Helper.generate101102110DestinationTargetOverseaInfoSearch(request.getTimeFilter(), subMetric, personConfigResponse, buTypeTarget, request.getMetric(), quarter, d);
                            targetValue = dimOrdTtdDestinationTargetConfigDao.queryOverseaDestinationTargetInfo(destinationTargetParam);
                        }
                    }
                } else {
                    targetValue = 0d;
                }
            }
            Boolean isCalComplete = targetValue != 0d;

            // 获取当期达成值
            double completeCurrentValue = getCompleteValueWithHOrQ("default", request.getTimeFilter(), d, request.getDomainName(), buType, isCT, personConfigResponse, quarter, isCalComplete, subMetric, request.getMetric());

            // 获取去年达成值
            double completeLastYearValue = getCompleteValueWithHOrQ("lastYear", request.getTimeFilter(), d, request.getDomainName(), buType, isCT, personConfigResponse, quarter, isCalComplete, subMetric, request.getMetric());

            String dateInfo = year + "-" + quarter;
            completeItems.add(new OverseaTrendLineDataItem(null, dateInfo, completeCurrentValue));
            completeRateItems.add(new OverseaTrendLineDataItem(null, dateInfo, targetValue == 0 ? 0 : completeCurrentValue / targetValue));
            yoyItems.add(new OverseaTrendLineDataItem(null, dateInfo, completeLastYearValue == 0 ? 0 : (completeCurrentValue - completeLastYearValue) / completeLastYearValue));
        }

        trendLineDetailInfoList.add(new OverseaTrendLine(TrendLineNameEnum.COMPLETE_VALUE.getName(), GraphTypeEnum.BAR_CHART.getName(), completeItems));
        trendLineDetailInfoList.add(new OverseaTrendLine(TrendLineNameEnum.COMPLETE_RATE.getName(), GraphTypeEnum.LINE_CHART.getName(), completeRateItems));
        trendLineDetailInfoList.add(new OverseaTrendLine(TrendLineNameEnum.YOY_VALUE.getName(), GraphTypeEnum.LINE_CHART.getName(), yoyItems));

        return response;
    }


    public GetOverseaDrillDownBaseInfoV2ResponseType getBus101102SubDrillDownBaseInfo(GetOverseaDrillDownBaseInfoV2RequestType request,
                                                                                      String d,
                                                                                      OverseaMetricInfoBean metricInfoBean) throws Exception {
        GetOverseaDrillDownBaseInfoV2ResponseType response = new GetOverseaDrillDownBaseInfoV2ResponseType();
        Boolean needSearch = StringUtils.isNotEmpty(request.getSearchWord());
        String subMetric = request.getSubMetric();
        List<String> fieldList = new ArrayList<>();
        if (needSearch) {
            fieldList.add(request.getSearchInDim());
        } else {
            fieldList.addAll(Bus101102Helper.getDrillDownFieldListV2(subMetric, metricInfoBean, remoteConfig));
            response.setDefaultChosenDim(fieldList.get(0));
        }
        Map<String, Future<GetRawDataResponseType>> fieldMap = new HashMap<>();
        for (String field : fieldList) {
            fieldMap.put(field, getRegionProvinceCountryEnums(request, d, metricInfoBean, field));
        }
        List<DilldownDim> fieldDataItemList = new ArrayList<>();
        response.setDimList(fieldDataItemList);
        for (String field : fieldList) {
            DilldownDim item = new DilldownDim();
            fieldDataItemList.add(item);
            Bus101102Helper.processDrillDownBaseInfoV2(request, field, fieldMap.get(field).get(), item, remoteConfig);
        }
        return response;
    }

    public GetOverseaDrillDownBaseInfoV2ResponseType getBus110SubDrillDownBaseInfo(GetOverseaDrillDownBaseInfoV2RequestType request,
                                                                                   String d,
                                                                                   OverseaMetricInfoBean metricInfoBean) throws Exception {
        GetOverseaDrillDownBaseInfoV2ResponseType response = new GetOverseaDrillDownBaseInfoV2ResponseType();
        Boolean needSearch = StringUtils.isNotEmpty(request.getSearchWord());

        String subMetric = request.getSubMetric();
        List<String> fieldList = new ArrayList<>();
        if (needSearch) {
            fieldList.add(request.getSearchInDim());
        } else {
            fieldList.addAll(Bus101102Helper.getDrillDownFieldList(subMetric, metricInfoBean, remoteConfig));
            response.setDefaultChosenDim(fieldList.get(0));
        }
        Map<String, Future<GetRawDataResponseType>> fieldMap = new HashMap<>();
        for (String field : fieldList) {
            fieldMap.put(field, getRegionProvinceCountryEnums(request, d, metricInfoBean, field));
        }
        List<DilldownDim> fieldDataItemList = new ArrayList<>();
        response.setDimList(fieldDataItemList);
        for (String field : fieldList) {
            DilldownDim item = new DilldownDim();
            fieldDataItemList.add(item);
            Bus101102Helper.processDrillDownBaseInfoV2(request, field, fieldMap.get(field).get(), item, remoteConfig);
        }
        return response;
    }

    private Future<GetRawDataResponseType> getRegionProvinceCountryEnums(GetOverseaDrillDownBaseInfoV2RequestType request, String d, OverseaMetricInfoBean metricInfoBean, String field) throws Exception {
        if (Lists.newArrayList(
                "region", "province", "country"
        ).contains(field)) {
            boolean isEn = "en-US".equals(UserUtil.getVbkLocale());
            FutureTask<GetRawDataResponseType> getRawDataResponseTypeFutureTask = new FutureTask<>(() -> {
                String selectItem = "", showName = "";
                switch (field) {
                    case "region":
                        selectItem = "business_region_id as id, business_region_name";
                        showName = "business_region_name";
                        break;
                    case "province":
                        selectItem = "business_sub_region_id as id, business_sub_region_name";
                        showName = "business_sub_region_name";
                        break;
                    case "country":
                        selectItem = "dep_ctry_id as id, dep_ctry_name";
                        showName = "dep_ctry_name";
                        break;
                }
                if (isEn) {
                    selectItem = selectItem + "_en";
                    showName = showName + "_en";
                }
                selectItem = selectItem + " as name ";

                List<PreparedParameterBean> parameters = new ArrayList<>();
                parameters.add(new PreparedParameterBean(d, Types.VARCHAR));

                List<Map<String, Object>> listResult =
//                        JSONObject.parseObject("[{\"id\":\"100\",\"name\":\"North East Asia\"},{\"id\":\"200\",\"name\":\"South East Asia\"},{\"id\":\"300\",\"name\":\"Hong Kong&Macau&Singapore\"},{\"id\":\"400\",\"name\":\"Long Haul\"},{\"id\":\"800\",\"name\":\"Thailand\"}]",new TypeReference<List<Map<String, Object>>>(){});
                        starRocksDao.getListResult("SELECT DISTINCT " + selectItem + " FROM dim_prd_ttd_overseas_geo_mapping_df WHERE d = ? and " + showName + " != 'unkwn' ", parameters);


                List<ArrayList<String>> collect = listResult.stream().map(x -> {
                    ArrayList<String> strings = new ArrayList<>();
                    strings.add(x.get("id").toString());
                    strings.add(x.get("name").toString());
                    return strings;
                }).collect(Collectors.toList());

                GetRawDataResponseType response = new GetRawDataResponseType();
                response.setResult(JSONObject.toJSONString(collect));
                return response;
            });
            executor.submit(getRawDataResponseTypeFutureTask);
            return getRawDataResponseTypeFutureTask;
        } else {
            SqlParamterBean sqlParamterBean = Bus101102Helper.getDestinationDrillDownBaseInfoSqlBeanV2(field, request, d, metricInfoBean, remoteConfig);
            GetRawDataRequestType fieldRequest = sqlParamterBean.convertBeanToRequest(false);
            return switchNewTableHelper.switchRemoteDatabaseAsync(fieldRequest);
        }
    }


    public GetOverseaTableDataV2ResponseType getBus101102SubTableData(GetOverseaTableDataV2RequestType request,
                                                                      String d,
                                                                      OverseaMetricInfoBean metricInfoBean) throws Exception {
        GetOverseaTableDataV2ResponseType response = new GetOverseaTableDataV2ResponseType();
        response.setMomType(OverseaMetricHelper.getMomType(request.getTimeFilter(), d));
        List<OverseaTableDataRow> tableDataItemList = new ArrayList<>();
        String metric = request.getMetric();
        String subMetric = request.getSubMetric();
        String field = request.getDimName();
        SubMetricFiledBean configBean = remoteConfig.getSubMetricFiledBeanV2(metric, subMetric, field);
        Boolean needBubble = configBean.getNeedBubble();
        String type = request.getQueryType();
        if ("bubble".equals(type) && !needBubble) {
            throw new InputArgumentException("this filed can't get bubble,please check it!");
        }
        Boolean needTarget = configBean.getNeedTarget();
        response.setTableHeaderList(Bus101102Helper.getTableHeaderListV2(configBean, needTarget));
        if ("examinee".equalsIgnoreCase(field)) {
            OverseasPerformanceInfoParamBean overseasPerformanceInfoParamBean =
                    Bus101102Helper.getDestinationExamineeTableDataSqlBeanV2(request, metricInfoBean, d, "current", remoteConfig, null);
            Integer totalCount = cdmOrdTtdOverseasPerformanceIndexDao.getOverseasProductionResponseInfoCount(overseasPerformanceInfoParamBean);
            if (totalCount == 0) {
                return response;
            }
            response.setTotalNum(totalCount);
            List<CdmOrdTtdOverseasPerformanceIndexBO> currentResult
                    = cdmOrdTtdOverseasPerformanceIndexDao.getOverseasProductionResponseInfo(overseasPerformanceInfoParamBean);
            List<DimOrdTtdPersonTargetBO> targetResult = null;
            List<String> dommainList = currentResult.stream()
                    .filter(x -> !"unkwn".equalsIgnoreCase(x.getDomainName()))
                    .map((x -> x.getDomainName())).distinct().collect(Collectors.toList());
            if (needTarget) {

                DimOrdTtdPersonTargetParamBean param = Bus101102Helper.getDimOrdTtdPersonTargetParamBean(dommainList, request, metricInfoBean, d, remoteConfig);
                targetResult = dimOrdTtdPersonTargetConfigDao.queryDimOrdTtdPersonTargetBOs(param);
                //
                List<DimOrdTtdPersonTargetBO> specialTargetResult = getSpecialTarget(d, dommainList, targetResult, request);
                targetResult.addAll(specialTargetResult);
            }
            OverseasPerformanceInfoParamBean lastYearResultParam = Bus101102Helper.getDestinationExamineeTableDataSqlBeanV2(request, metricInfoBean, d, "lastYear", remoteConfig, dommainList);
            List<CdmOrdTtdOverseasPerformanceIndexBO> lastYearResult = cdmOrdTtdOverseasPerformanceIndexDao.getOverseasProductionResponseInfo(lastYearResultParam);
            Bus101102Helper.processTableBaseDataByExamineeV2(currentResult, targetResult, lastYearResult, tableDataItemList,
                    request, metricInfoBean, response.getTableHeaderList());
        } else {
            OverseasPerformanceInfoParamBean overseasPerformanceInfoParamBean =
                    Bus101102Helper.getDestinationTableDataSqlBeanV2(null, request, metricInfoBean, d, "current", remoteConfig, null);
            Integer totalCount = cdmOrdTtdOverseasPerformanceIndexAddrDao.queryOverseasPerformanceInfoCount(overseasPerformanceInfoParamBean);
            if (totalCount == 0) {
                return response;
            }
            response.setTotalNum(totalCount);
            List<CdmOrdTtdOverseasPerformanceIndexBO> currentResult
                    = cdmOrdTtdOverseasPerformanceIndexAddrDao.getOverseasProductionResponseInfo(overseasPerformanceInfoParamBean);
            List<DimOrdTtdDestinationTargetBO> targetResult = null;
            if (needTarget) {
                List<String> regionNameList = currentResult.stream().map(CdmOrdTtdOverseasPerformanceIndexBO::getBuRegionNames).collect(Collectors.toList());
                List<String> subRegionNameList = currentResult.stream().map(CdmOrdTtdOverseasPerformanceIndexBO::getBuSubRegionNames).collect(Collectors.toList());
                DimOrdTtdDestinationTargetParamBean param = Bus101102Helper.getDimOrdTtdDestinationTargetParamBean(request, regionNameList, subRegionNameList, d, remoteConfig);
                targetResult = dimOrdTtdDestinationTargetConfigDao.queryDimOrdTtdDestinationTargetBOList(param);
            }
            // 获取去年同期数据
            OverseasPerformanceInfoParamBean lastYearParam = Bus101102Helper.getDestinationTableDataSqlBeanV2(null, request, metricInfoBean, d, "lastYear", remoteConfig, currentResult);
            List<CdmOrdTtdOverseasPerformanceIndexBO> lastYearResult = cdmOrdTtdOverseasPerformanceIndexAddrDao.getOverseasProductionResponseInfo(lastYearParam);

            List<CdmOrdTtdOverseasPerformanceIndexBO> momResult = null;
            List<CdmOrdTtdOverseasPerformanceIndexBO> last30daysResult = null;
            if ("firstPage".equalsIgnoreCase(request.getQuerySource())) {
                //求30日环比
                OverseasPerformanceInfoParamBean momParam = Bus101102Helper.getDestinationTableDataSqlBeanV2("30", request, metricInfoBean, d, null, remoteConfig, currentResult);
                momResult = cdmOrdTtdOverseasPerformanceIndexAddrDao.getMomOverseasProductionResponseInfo(momParam);
                OverseasPerformanceInfoParamBean last30daysParam = Bus101102Helper.getDestinationTableDataSqlBeanV2("last30", request, metricInfoBean, d, null, remoteConfig, currentResult);
                last30daysResult = cdmOrdTtdOverseasPerformanceIndexAddrDao.getMomOverseasProductionResponseInfo(last30daysParam);
            }
            Bus101102Helper.processTableBaseDataByDestinationV2(currentResult, targetResult, lastYearResult, momResult, last30daysResult, tableDataItemList,
                    request, metricInfoBean, response.getTableHeaderList());
        }
        response.setRows(tableDataItemList);
        return response;
    }

    private List<DimOrdTtdPersonTargetBO> getSpecialTarget(String d, List<String> dommainList,
                                                           List<DimOrdTtdPersonTargetBO> targetResult,
                                                           GetOverseaTableDataV2RequestType request) {
        List<DimOrdTtdPersonTargetBO> specialTargetList = new ArrayList<>();
        List<String> sppecialDomalinList = new ArrayList<>(dommainList);
        for (DimOrdTtdPersonTargetBO targetBO : targetResult) {
            if (dommainList.contains(targetBO.getDomainName())) {
                sppecialDomalinList.remove(targetBO.getDomainName());
            }
        }
        if (CollectionUtils.isEmpty(sppecialDomalinList)) {
            return specialTargetList;
        }
        String year = request.getTimeFilter().getYear();
        String dateType = request.getTimeFilter().getDateType();
        List<String> quarterList = "quarter".equals(dateType) ? Collections.singletonList(request.getTimeFilter().getQuarter()) : DateUtil.getQuarterOfHalf(request.getTimeFilter().getHalf());
        String tkt = remoteConfig.getConfigValue("tkt");
        String act = remoteConfig.getConfigValue("act");
        String odt = remoteConfig.getConfigValue("odt");
        List<String> buType = BuTypeEnum.getBuTypeList(request.getBusinessLine(), tkt, act, odt);
        for (String domainName : sppecialDomalinList) {
            DimOrdTtdPersonTargetBO personTargetBO = new DimOrdTtdPersonTargetBO();
            personTargetBO.setDomainName(domainName);
            specialTargetList.add(personTargetBO);
            List<DimOrdTtdTargetBO> examineeConfigList = dimOrdTtdTargetConfigDao.queryDimOrdTtdTarget(d, buType, domainName, year, quarterList);
            if (CollectionUtils.isEmpty(examineeConfigList)) {
                continue;
            }
            OverSeaExamineConfigBO bo = new OverSeaExamineConfigBO();
            OverseaMetricInfoBean overseaMetricInfoBean = bo.getSingleMetricInfoBeanV2(examineeConfigList, remoteConfig, request.getMetric(), request.getSubMetric());
//目的地，查目标表
            DimOrdTtdDestinationTargetParamBean param = new DimOrdTtdDestinationTargetParamBean();
            param.setD(d);
            param.setBuType(BuTypeEnum.getCTByByType(request.getBusinessLine()));
            param.setExamineYear(request.getTimeFilter().getYear());
            param.setExamineQuaters(quarterList);
            if ("大区".equalsIgnoreCase(overseaMetricInfoBean.getDestinationLevel())) {//NOSONAR
                param.setBusinessRegionName(overseaMetricInfoBean.getDestinationRangeList());
            }
            if ("子区域".equalsIgnoreCase(overseaMetricInfoBean.getDestinationLevel())) {//NOSONAR
                param.setBusinessSubRegionName(overseaMetricInfoBean.getDestinationRangeList());
            }
            param.setCT(OverseaMetricHelper.convertCTdestination(remoteConfig, request.getSubMetric()));
            param.setExamineMetricType(request.getMetric());
            List<DimOrdTtdDestinationTargetBO> targetDestinationResult = dimOrdTtdDestinationTargetConfigDao.queryDimOrdTtdDestinationTargetBOList(param);
            if (CollectionUtils.isEmpty(targetDestinationResult)) {
                continue;
            }
            Double q1 = new Double(0);
            Double q2 = new Double(0);
            Double q3 = new Double(0);
            Double q4 = new Double(0);
            for (DimOrdTtdDestinationTargetBO targetBO : targetDestinationResult) {
                q1 = q1 + new Double(targetBO.getQ1());
                q2 = q2 + new Double(targetBO.getQ2());
                q3 = q3 + new Double(targetBO.getQ3());
                q4 = q4 + new Double(targetBO.getQ4());
            }
            personTargetBO.setQ1(q1.toString());
            personTargetBO.setQ2(q2.toString());
            personTargetBO.setQ3(q3.toString());
            personTargetBO.setQ4(q3.toString());
        }
        return specialTargetList;
    }


    public Future<OveaseaMetric> getBus103SubMetricCardData(TimeFilter timeFilter,
                                                            OverseaMetricInfoBeanV2 metricInfoBean,
                                                            String d,
                                                            String metric,
                                                            String subMetric) throws Exception {

        OveaseaMetric oveaseaMetric = new OveaseaMetric();
        oveaseaMetric.setMetric(metric);
        return new AsyncResult<>(oveaseaMetric);
    }

    public Future<OveaseaSubMetric> getBus110SubMetricCardData(TimeFilter timeFilter,
                                                               OverseaMetricInfoBeanV2 metricInfoBean,
                                                               String d,
                                                               String subMetric,
                                                               GetOverseaMetricCardDataV2RequestType request) throws Exception {

        OveaseaSubMetric oveaseaSubMetric = new OveaseaSubMetric();

        //todo
        return new AsyncResult<>(oveaseaSubMetric);
    }


    public GetOverseaTrendLineDataV2ResponseType getBus103SubTrendLineData(GetOverseaTrendLineDataV2RequestType request,
                                                                           String d,
                                                                           List<ExamineConfigBean> examineConfigBeanList) throws Exception {
        //todo 目标

        //todo 质量（加权缺陷率）
        return null;
    }


    public GetOverseaDrillDownBaseInfoV2ResponseType getBus103SubDrillDownBaseInfo(GetOverseaDrillDownBaseInfoV2RequestType request,
                                                                                   String d,
                                                                                   OverseaMetricInfoBean metricInfoBean) throws Exception {
        GetOverseaDrillDownBaseInfoV2ResponseType response = new GetOverseaDrillDownBaseInfoV2ResponseType();
        Boolean needSearch = StringUtils.isNotEmpty(request.getSearchInDim());
        String subMetric = request.getSubMetric();
        List<String> fieldList = new ArrayList<>();
        if (needSearch) {
            fieldList.add(request.getSearchInDim());
        } else {
            fieldList.addAll(Bus103Helper.getDrillDownFieldList(subMetric, metricInfoBean, remoteConfig));
            response.setDefaultChosenDim(fieldList.get(0));
        }
        Map<String, Future<GetRawDataResponseType>> fieldMap = new HashMap<>();
        for (String field : fieldList) {
            SqlParamterBean sqlParamterBean = Bus103Helper.getDestinationDrillDownBaseInfoSqlBeanV2(field, request, d, metricInfoBean, remoteConfig);
            GetRawDataRequestType fieldRequest = sqlParamterBean.convertBeanToRequest(false);
            fieldMap.put(field, switchNewTableHelper.switchRemoteDatabaseAsync(fieldRequest));
        }
        List<DilldownDim> fieldDataItemList = new ArrayList<>();
        response.setDimList(fieldDataItemList);
        for (String field : fieldList) {
            DilldownDim item = new DilldownDim();
            fieldDataItemList.add(item);
            Bus101102Helper.processDrillDownBaseInfoV2(request, field, fieldMap.get(field).get(), item, remoteConfig);
        }
        return response;
    }


    public GetOverseaTableDataV2ResponseType getBus103SubTableData(GetOverseaTableDataV2RequestType request,
                                                                   String d,
                                                                   OverseaMetricInfoBean metricInfoBean) throws Exception {

        GetOverseaTableDataV2ResponseType response = new GetOverseaTableDataV2ResponseType();
        List<OverseaTableDataRow> tableDataItemList = new ArrayList<>();
        response.setRows(tableDataItemList);
        String metric = request.getMetric();
        String subMetric = request.getSubMetric();
//        if ("destinationC".equalsIgnoreCase(subMetric)) {
//            subMetric = "destination_c";
//        } else if ("destinationT".equalsIgnoreCase(subMetric)) {
//            subMetric = "destination_t";
//        }
        String field = request.getDimName();
        String year = request.getTimeFilter().getYear();
        int y = year != null ? Integer.parseInt(year) : 0;
        SubMetricFiledBean configBean;
        if (y >= 2024) {
            configBean = remoteConfig.getSubMetricFiledBeanV2(year, metric, subMetric, field);
        } else {
            configBean = remoteConfig.getSubMetricFiledBeanV2(metric, subMetric, field);
        }

        Boolean needTarget = configBean.getNeedTarget();

        //获取当前数据
        SqlParamterBean currentBean = Bus103Helper.getDestinationTableDataSqlBeanV2(request, d, metricInfoBean, configBean, remoteConfig, "current");
        GetRawDataRequestType currentReq = currentBean.convertBeanToRequest(true);
        ListenableFuture<GetRawDataResponseType> currentResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(currentReq);

        if (needTarget) {
            //获取目标数据
            SqlParamterBean targetBean = Bus103Helper.getDestinationTableDataSqlBeanV2(request, d, metricInfoBean, configBean, remoteConfig, "target");
            GetRawDataRequestType targetReq = targetBean.convertBeanToRequest(true);
            ListenableFuture<GetRawDataResponseType> targetResFuture = switchNewTableHelper.switchRemoteDatabaseAsync(targetReq);
            Bus103Helper.processTableBaseDataV2(currentResFuture.get(), targetResFuture.get(), tableDataItemList, needTarget, year);
        } else {
            Bus103Helper.processTableBaseDataV2(currentResFuture.get(), null, tableDataItemList, needTarget, year);
        }
        response.setTotalNum(currentResFuture.get().getTotalNum());
        response.setTableHeaderList(Bus103Helper.getTableHeaderList(configBean, year));
        response.setMomType(OverseaMetricHelper.getMomType(request.getTimeFilter(), d));
        return response;
    }

}
