package com.ctrip.tour.business.dashboard.grpBusiness.handler.dim;

import java.util.Map;

import org.apache.commons.lang.StringUtils;

import com.ctrip.tour.business.dashboard.grpBusiness.enums.MetricCategoryEnum;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Maps;

/**
 * <AUTHOR>
 * @Date 2024/12/23
 */
public class DiydimconvertHandler {

    static Map<String, String> dimEnumMap = Maps.newHashMap();

    static ImmutableList<String> localPmEidCategoryList = ImmutableList.of(MetricCategoryEnum.INCOME_CATEGORY.getEnglishName(), MetricCategoryEnum.MULTIPLE_PRICE_CATEGORY.getEnglishName(),
           MetricCategoryEnum.PROFIT_PRICE_CATEGORY.getEnglishName(), MetricCategoryEnum.INFO_SCORE_CATEGORY.getEnglishName());

    private final static String DOMAIN = "domain";
    private final static String GRADE_REGION_NAME = "grade_region_name";
    private final static String DESTINATIONLINE = "destinationline";
    private final static String VBK_PROVIDER_NAME = "vbk_provider_name";
    private final static String BUSINESS_REGION_NAME = "business_region_name";

    static {
        dimEnumMap.put("境内外", DOMAIN);//NOSONAR
        dimEnumMap.put("大区", BUSINESS_REGION_NAME);//NOSONAR
        dimEnumMap.put("评级区域", GRADE_REGION_NAME);//NOSONAR
        dimEnumMap.put("目的地区域", DESTINATIONLINE);//NOSONAR
        dimEnumMap.put("供应商", VBK_PROVIDER_NAME);//NOSONAR
    }

    public static String convertDimEnumName(String name, String categoryName) {
        return dimEnumMap.getOrDefault(name, "");
    }

}
