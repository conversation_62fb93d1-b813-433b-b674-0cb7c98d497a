package com.ctrip.tour.business.dashboard.grpBusiness.metrics;

import static com.ctrip.tour.business.dashboard.grpBusiness.common.GrpConstant.CALC_DATE_NAME;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.ctrip.tour.business.dashboard.grpBusiness.common.GrpConstant;
import com.ctrip.tour.business.dashboard.grpBusiness.dao.GrpBusinessDashboardUpdatetimeDao;
import com.ctrip.tour.business.dashboard.grpBusiness.handler.dim.DimconvertHandler;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.utils.MapperUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import com.ctrip.tour.business.dashboard.grpBusiness.annotation.IndexAssemblyHandler;
import com.ctrip.tour.business.dashboard.grpBusiness.enums.TimeAggTypeEnum;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.ServiceException;
import com.ctrip.tour.business.dashboard.utils.Calculator;
import com.ctrip.tour.business.dashboard.utils.SqlBuilder;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2024/12/9
 */
@Service
@Slf4j
public abstract class IndexCommonQueryAbstractSerice implements IndexCommonQueryService {


    private ApplicationContext applicationContext;

    private static final String OR="OR-";
    private final static String PM_EID = "pm_eid";
    private final static String SUB_BU_TYPE = "sub_bu_type";



    ImmutableList<String> partitionTableList = ImmutableList.of("adm_ord_grp_work_platform_prdt_df", "adm_prd_grp_multiple_price_work_platform_df", "cdm_sev_grp_cpr_platform_fitting_nps_df",
            "cdm_sev_grp_cpr_platform_self_srv_cr_df","adm_prd_grp_avg_multiple_price_work_platform_df","adm_ord_cus_work_platform_prdt_df","adm_ord_cus_work_platform_prdt_prvdr_df","adm_ord_cus_work_platform_prdt_target_achieve_df",
            "adm_sev_cus_metrics_tag_to_workbench_df", "cdm_sev_grp_platform_self_srv_summary_df");

    @Autowired
    public IndexCommonQueryAbstractSerice(ApplicationContext ac) {
        applicationContext = ac;
    }
    @Autowired
    GrpBusinessDashboardUpdatetimeDao updatetimeDao;
    @Autowired
    RemoteConfig remoteConfig;


    @Override
    public List<Map<String, Object>> queryCardData(IndexCommonQueryAbstractSerice serice, Map<String, ?> param, List<String> groupByCols) {
        IndexAssemblyHandler annotation = serice.getClass().getAnnotation(IndexAssemblyHandler.class);
        String tableName = annotation.tableName();
        List<String> grpCols = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(groupByCols)) {
            List<String> convertedGrpCols = groupByCols.stream().map(col -> DimconvertHandler.convertDimEnumName(col, serice.getClass().getName())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(convertedGrpCols)
                    && convertedGrpCols.stream().anyMatch(col -> StringUtils.isNotBlank(col.trim()))) {
                grpCols.addAll(convertedGrpCols);
            } else {
                grpCols.addAll(groupByCols);
            }

        }

        String sql = buildQuerySql(param, grpCols, null, tableName);
        System.out.println("sql: " + sql+" param: "+ MapperUtil.obj2Str(param));  //todo 本地调试
        List<Map<String, Object>> rowData = queryData(sql, param, grpCols, null);

        return handleResults(serice, param, null, false, rowData, Lists.newArrayList());
    }

    @Override
    public Map<String, Object> queryTrendLineData(IndexCommonQueryAbstractSerice serice, Map<String, ?> param, List<String> groupByCols, String timeAggType) {
        IndexAssemblyHandler annotation = serice.getClass().getAnnotation(IndexAssemblyHandler.class);
        String tableName = annotation.tableName();
        String calcDateName="";
        if (Objects.nonNull(param.get(CALC_DATE_NAME))) {
            calcDateName = (String) param.get(CALC_DATE_NAME);
        } else {
            calcDateName = annotation.calcDateName();
        }

        String calcFieldName = annotation.calcFieldName();
        String calcExpression = annotation.calcExpression();
        Object dateRangeObj = param.get(calcDateName);
        if (Objects.isNull(dateRangeObj)) {
            return Maps.newHashMap();
        }
        List<String> grpCols = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(groupByCols)) {
            List<String> convertedGrpCols = groupByCols.stream().map(col -> DimconvertHandler.convertDimEnumName(col, serice.getClass().getName())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(convertedGrpCols)
                    && convertedGrpCols.stream().anyMatch(col -> StringUtils.isNotBlank(col.trim()))) {
                grpCols.addAll(convertedGrpCols);
            } else {
                grpCols.addAll(groupByCols);
            }
        }
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        String[] dateRange = (String[])dateRangeObj;

        List<Map<String, Object>> resultData = Lists.newArrayList();

        if (StringUtils.equals(timeAggType, TimeAggTypeEnum.DAY.getName())) {
//            List<String> allDays = getAllDayByDateRange(dateRange[0], dateRange[1]);

            HashMap<String, Object> paramMap = Maps.newHashMap();
            paramMap.putAll(param);
//                        paramMap.put(calcDateName, day);
            groupByCols.add(calcDateName);
            String sql = buildQuerySql(paramMap, grpCols, timeAggType, tableName);
            System.out.println("sql: " + sql+" param: "+ MapperUtil.obj2Str(param));  //todo 本地调试
            List<Map<String, Object>> rowData = queryData(sql, param, grpCols, timeAggType);
            if (CollectionUtils.isEmpty(rowData)) {
                return null;
            }

            List<String> allDays = getAllDays(dateRange[0], dateRange[1]);

            String finalCalcDateName = calcDateName;
            Map<String, Object> calcRst = rowData.stream()
                    .collect(Collectors.toMap(k -> {
                        Object o = k.get(finalCalcDateName);
                        if (o instanceof String) {
                            return String.valueOf(o);
                        }
                        if (o instanceof Date) {

                            return formatter.format((Date) o);
                        }
                        return null;
                    }, k -> {
                        if (StringUtils.isNotBlank(calcExpression)) {
                            BigDecimal bigDecimal = Calculator.parseExpressionAndCalc(calcExpression, k);
                            return Objects.isNull(bigDecimal)? "":bigDecimal;
                        }
                        Object resultValue = k.get("result_value");
                        if (Objects.isNull(resultValue)) {
                            resultValue = k.get(calcFieldName);
                        }
                        if (resultValue instanceof Long) {
                            return BigDecimal.valueOf((Long) resultValue);
                        } else if (resultValue instanceof Double) {
                            return BigDecimal.valueOf((Double) resultValue);
                        }
                        return resultValue;
                    }));
            return allDays.stream().collect(Collectors.toMap(Function.identity(), day -> {
                Object o = calcRst.get(day);
                if (Objects.isNull(o)) {
                    return "";
                }
                return o;
            }));


        } else if (StringUtils.equals(timeAggType, TimeAggTypeEnum.WEEK.getName())) {
            List<String> allWeeks = getAllWeekByDateRange(dateRange[0], dateRange[1]);

            String finalCalcDateName1 = calcDateName;
            resultData = allWeeks.parallelStream()
                    .map(week -> {
                        HashMap<String, Object> paramMap = Maps.newHashMap();
                        paramMap.putAll(param);
                        paramMap.put(finalCalcDateName1, week.split("~"));
                        String sql = buildQuerySql(paramMap, grpCols, timeAggType, tableName);
                        System.out.println("sql: " + sql+" param: "+ MapperUtil.obj2Str(param));  //todo 本地调试
                        List<Map<String, Object>> rowData = queryData(sql, param, grpCols, timeAggType);
                        if (CollectionUtils.isEmpty(rowData)) {
                            return null;
                        }
                        Map<String, Object> resultMap = rowData.get(0);
                        resultMap.put(finalCalcDateName1, week);
                        return resultMap;
                    }).filter(Objects::nonNull).collect(Collectors.toList());
        } else if (StringUtils.equals(timeAggType, TimeAggTypeEnum.MONTH.getName())) {
            List<String> allMonths = getAllMonthByDateRange(dateRange[0], dateRange[1]);
            String finalCalcDateName2 = calcDateName;
            resultData = allMonths.parallelStream()
                    .map(month -> {
                        HashMap<String, Object> paramMap = Maps.newHashMap();
                        paramMap.putAll(param);
                        paramMap.put(finalCalcDateName2, month.split("~"));
                        String sql = buildQuerySql(paramMap, grpCols, timeAggType, tableName);
                        System.out.println("sql: " + sql+" param: "+ MapperUtil.obj2Str(param));  //todo 本地调试
                        List<Map<String, Object>> rowData = queryData(sql, param, grpCols, timeAggType);
                        if (CollectionUtils.isEmpty(rowData)) {
                            return null;
                        }
                        Map<String, Object> resultMap = rowData.get(0);
                        String monthStartDate = month.split("~")[0];
                        if (monthStartDate.length() > 7) {
                            resultMap.put(finalCalcDateName2, monthStartDate.substring(0, monthStartDate.lastIndexOf("-")));
                        } else {
                            resultMap.put(finalCalcDateName2, monthStartDate);
                        }

                        return resultMap;
                    }).filter(Objects::nonNull).collect(Collectors.toList());
        }



//        String sql = buildQuerySql(param, groupByCols, timeAggType, tableName);
//        List<Map<String, Object>> rowData = queryData(sql, param, groupByCols, timeAggType);

        List<Map<String, Object>> maps = handleResults(serice, param, timeAggType, true, resultData, Lists.newArrayList());
        if (CollectionUtils.isNotEmpty(maps)) {
            return maps.get(0);
        }
        return Maps.newHashMap();
    }

    public List<String> getAllDays(String startDateStr, String endDateStr) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate startDate = LocalDate.parse(startDateStr, formatter);
        LocalDate endDate = LocalDate.parse(endDateStr, formatter);

        List<String> dateList = new ArrayList<>();
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            dateList.add(date.format(formatter));
        }

        return dateList;
    }

    @Override
    public List<Map<String, Object>> queryDillDownData(IndexCommonQueryAbstractSerice serice, Map<String, ?> param, List<String> groupByCols) {
        IndexAssemblyHandler annotation = serice.getClass().getAnnotation(IndexAssemblyHandler.class);
        String tableName = annotation.tableName();
        List<String> grpCols = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(groupByCols)) {
            List<String> convertedGrpCols = groupByCols.stream().map(col -> DimconvertHandler.convertDimEnumName(col, serice.getClass().getName())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(convertedGrpCols)
            && convertedGrpCols.stream().anyMatch(col -> StringUtils.isNotBlank(col.trim()))) {
                grpCols.addAll(convertedGrpCols);
            } else {
                grpCols.addAll(groupByCols);
            }
        }
        String sql = buildQuerySql(param, grpCols, null, tableName);
        System.out.println("sql: " + sql+" param: "+ MapperUtil.obj2Str(param));  //todo 本地调试
        List<Map<String, Object>> rowData = queryData(sql, param, grpCols, null);

        return handleResults(serice, param, null, false, rowData, groupByCols);
    }




    protected abstract List<Map<String, Object>> handleResults(IndexCommonQueryAbstractSerice serice, Map<String, ?> param,
                                                               String timeAggType, boolean needTimeAgg, List<Map<String, Object>> rowData, List<String> groupCols);

    public List<Map<String, Object>> doHandleResults(IndexCommonQueryAbstractSerice serice, Map<String, ?> param,
                                                      String timeAggType, boolean needTimeAgg,List<Map<String, Object>> rowData) {
        IndexAssemblyHandler annotation = serice.getClass().getAnnotation(IndexAssemblyHandler.class);
        String calcDateName="";
        if (Objects.nonNull(param.get(CALC_DATE_NAME))) {
            calcDateName = (String) param.get(CALC_DATE_NAME);
        } else {
            calcDateName = annotation.calcDateName();
        }
        String calcExpression = annotation.calcExpression();
        String calcFieldName = annotation.calcFieldName();
        if (StringUtils.isNotBlank(timeAggType) && needTimeAgg) {
            String[] dateRange = (String[])param.get(calcDateName);

            if (StringUtils.equals(timeAggType, TimeAggTypeEnum.WEEK.getName())) {
                String finalCalcDateName = calcDateName;
                Map<String, Map<String, Object>> fieldAggValueMap = Splitter.on(",").splitToList(calcFieldName)
                        .stream()
                        .collect(Collectors.toMap(Function.identity(),
                                fieldName -> rstAggByTimeDim(rowData, timeAggType, finalCalcDateName, fieldName)));
                Map<String, Object> aggFinalResult = calcAggWeekValue(fieldAggValueMap, dateRange[0], dateRange[1], calcExpression);
                return Lists.newArrayList(aggFinalResult);

            } else if (StringUtils.equals(timeAggType, TimeAggTypeEnum.MONTH.getName())) {
                String finalCalcDateName1 = calcDateName;
                Map<String, Map<String, Object>> fieldAggValueMap = Splitter.on(",").splitToList(calcFieldName)
                        .stream()
                        .collect(Collectors.toMap(Function.identity(),
                                fieldName -> rstAggByTimeDim(rowData, timeAggType, finalCalcDateName1, fieldName)));
                Map<String, Object> aggFinalResult = calcAggMonthValue(fieldAggValueMap, dateRange[0], dateRange[1], calcExpression);
                return Lists.newArrayList(aggFinalResult);
            }
        }

        return computeResult(rowData, calcExpression);
    }

//    @Override
//    public List<Map<String, Object>> queryBySql(IndexCommonQueryAbstractSerice serice, String sql, Map<String, ?> param, List<String> groupByCols, String timeAggType, boolean needTimeAgg) {
//        List<Map<String, Object>> rowData = queryData(sql, param, groupByCols, timeAggType);
//        return handleResults(serice, param, timeAggType, needTimeAgg, rowData);
//    }

    public Map<String, Object> calcAggWeekValue(Map<String, Map<String, Object>> fieldAggValueMap,
                                                   String startDate, String endDate, String expression) {

        List<String> allWeekByDateRange = getAllWeekByDateRange(startDate, endDate);

        return allWeekByDateRange.stream()
                .collect(Collectors.toMap(Function.identity(), week -> {
                    HashMap<String, Object> rowData = Maps.newHashMap();
                    for (String s : fieldAggValueMap.keySet()) {
                        Map<String, Object> aggMap = fieldAggValueMap.get(s);
                        if (StringUtils.isBlank(expression) && StringUtils.equals("result_value", s)) {
                            Object o = aggMap.get(week);
                           return Objects.isNull(o)? "":o;
                        }
                        if (MapUtils.isEmpty(aggMap)) {
                            return "";
                        }
                        Object valObj = aggMap.get(week);
                        if (Objects.isNull(valObj)) {
                            return "";
                        }
                        BigDecimal val = BigDecimal.ZERO;
                        if (valObj instanceof Long) {
                            val = BigDecimal.valueOf((long)valObj);
                        } else if (valObj instanceof Double) {
                            val = BigDecimal.valueOf((double)valObj);
                        } else {
                            val = (BigDecimal) valObj;
                        }
                        if (BigDecimal.ZERO.compareTo(val) == 0) {
                            return "";
                        }
                        rowData.put(s, valObj);

                    }
                    BigDecimal result = Calculator.parseExpressionAndCalc(expression, rowData);
                    return Objects.isNull(result)? "":(Object) result;
                }));

    }

    private Map<String, Object> calcAggMonthValue(Map<String, Map<String, Object>> fieldAggValueMap,
                                                  String startDate, String endDate, String expression) {

        List<String> allMonthByDateRange = getAllMonthByDateRange(startDate, endDate);

        return allMonthByDateRange.stream()
                .collect(Collectors.toMap(Function.identity(), month -> {
                    HashMap<String, Object> rowData = Maps.newHashMap();
                    for (String s : fieldAggValueMap.keySet()) {
                        Map<String, Object> aggMap = fieldAggValueMap.get(s);

                        if (MapUtils.isEmpty(aggMap)) {
                            return "";
                        }
                        String split = month.split("~")[0];

                        String substring = "";
                        if (split.length() > 7) {
                            substring = split.substring(0, split.lastIndexOf("-"));
                        } else {
                            substring = split;
                        }


                        if (StringUtils.isBlank(expression) && StringUtils.equals("result_value", s)) {
                            return aggMap.get(substring);
                        }

                        Object val = aggMap.get(substring);
                        rowData.put(s, val);

                    }
                    BigDecimal result = Calculator.parseExpressionAndCalc(expression, rowData);
                    return Objects.isNull(result)? "":(Object) result;
                }));

    }

    private List<String> getAllDayByDateRange(String startDateStr, String endDateStr) {
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate startDate = LocalDate.parse(startDateStr, dtf);
        LocalDate endDate = LocalDate.parse(endDateStr, dtf);

        List<String> dates = new ArrayList<>();
        long numOfDaysBetween = ChronoUnit.DAYS.between(startDate, endDate);
        for (int i = 0; i <= numOfDaysBetween; i++) {
            dates.add(startDate.plusDays(i).toString());
        }
        return dates;
    }
    public List<String> getAllWeekByDateRange(String startDateStr, String endDateStr) {

        List<String> weekList = Lists.newArrayList();

        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate startDate = LocalDate.parse(startDateStr, dtf);
        LocalDate endDate = LocalDate.parse(endDateStr, dtf);

        // 找到 startDate 所在周的周一
        LocalDate currentStartOfWeek = startDate.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        if (currentStartOfWeek.isBefore(startDate)) {
            currentStartOfWeek = startDate;
        }

        // 找到 startDate 所在周的周日
        LocalDate currentEndOfWeek = currentStartOfWeek.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));

        // 遍历每一周
        while (!currentStartOfWeek.isAfter(endDate)) {
            // 确保当前周的开始时间不早于 startDate
            LocalDate adjustedStartOfWeek = currentStartOfWeek.isBefore(startDate) ? startDate : currentStartOfWeek;
            // 确保当前周的结束时间不晚于 endDate
            LocalDate adjustedEndOfWeek = currentEndOfWeek.isAfter(endDate) ? endDate : currentEndOfWeek;

            // 添加到结果列表
            weekList.add(dtf.format(adjustedStartOfWeek) + "~" + dtf.format(adjustedEndOfWeek));
            // 移动到下一周
            currentStartOfWeek = currentStartOfWeek.plusWeeks(1).with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
            currentEndOfWeek = currentStartOfWeek.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));
        }
        return weekList;

    }

    public List<String> getAllMonthByDateRange(String startDateStr, String endDateStr) {

        List<String> monthList = Lists.newArrayList();

        if (StringUtils.isNotBlank(startDateStr) && StringUtils.isNotBlank(endDateStr)
        && startDateStr.length() == 7 && endDateStr.length() == 7) {
            DateTimeFormatter mndtf = DateTimeFormatter.ofPattern("yyyy-MM");

            LocalDate startDate = YearMonth.parse(startDateStr, mndtf).atDay(1);
            LocalDate endDate = YearMonth.parse(endDateStr, mndtf).atDay(1);

            while (!startDate.isAfter(endDate)) {
                monthList.add(mndtf.format(startDate) + "~" + mndtf.format(startDate));
                startDate = startDate.plusMonths(1);
            }
            return monthList;
        } else {
            DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate startDate = LocalDate.parse(startDateStr, dtf);
            LocalDate endDate = LocalDate.parse(endDateStr, dtf);
            // 获取开始月份
            YearMonth startYearMonth = YearMonth.from(startDate);
            // 获取结束月份
            YearMonth endYearMonth = YearMonth.from(endDate);

            // 遍历每个月
            for (YearMonth yearMonth = startYearMonth; !yearMonth.isAfter(endYearMonth); yearMonth = yearMonth.plusMonths(1)) {
                // 计算当前月份的第一天
                LocalDate firstDayOfMonth = yearMonth.atDay(1);
                // 计算当前月份的最后一天
                LocalDate lastDayOfMonth = yearMonth.atEndOfMonth();

                // 调整第一个月的开始时间和最后一个月的结束时间
                LocalDate adjustedFirstDay = firstDayOfMonth.isBefore(startDate) ? startDate : firstDayOfMonth;
                LocalDate adjustedLastDay = lastDayOfMonth.isAfter(endDate) ? endDate : lastDayOfMonth;

                // 添加到结果列表
                monthList.add(dtf.format(adjustedFirstDay) + "~" + dtf.format(adjustedLastDay));
            }
            return monthList;
        }



    }

    protected abstract String otherCon(Map<String, ?> param, List<String> groupByCols, String timeAggType);

    protected abstract SqlBuilder selectColsAssembly(SqlBuilder sqlBuilder, Map<String, ?> param, List<String> groupByCols, String timeAggType);

    protected abstract List<Map<String, Object>> queryData(String sql, Map<String, ?> param, List<String> groupByCols, String timeAggType);


    protected String doOtherCon(Map<String, ?> param, String categoryName) {
        Object bizMode = param.get("biz_mode");
        Object empCodes = param.get("empCodes");
        if (bizMode != null && Objects.nonNull(empCodes) && CollectionUtils.isNotEmpty((List<String>) empCodes) && Objects.equals(1, bizMode)) {

            List<String> empcodeFormat = ((List<?>) empCodes).stream().map(empCode -> "'" + empCode + "'")
                    .collect(Collectors.toList());

            String teamSql = SUB_BU_TYPE + " = " + "'跟团游'" + " and (" + PM_EID + " in (" + Joiner.on(",").join(empcodeFormat) + ")"//NOSONAR
                    + " or " + DimconvertHandler.getLocalPmEidName(categoryName) + " in (" + Joiner.on(",").join(empcodeFormat) + "))";

            String priSql = SUB_BU_TYPE + " = " + "'独立出游'" + " and " + PM_EID + " in (" + Joiner.on(",").join(empcodeFormat) + ")";//NOSONAR

            return " ((" + teamSql + ") or (" + priSql + ")) ";
        }
        return null;
    }

    private List<Map<String, Object>> computeResult(List<Map<String, Object>> rowData, String calcExpression) {

        if (StringUtils.isNotBlank(calcExpression)) {
            for (Map<String, Object> rowDatum : rowData) {
                BigDecimal bigDecimal = Calculator.parseExpressionAndCalc(calcExpression, rowDatum);
                rowDatum.put("result_value", bigDecimal);
            }
        }
        return rowData;

    }

    private String buildQuerySql(Map<String, ?> param, List<String> groupByCols, String timeAggType, String tableName) {

        SqlBuilder sqlBuilder = new SqlBuilder();
        SqlBuilder.Condition condition = new SqlBuilder.Condition();

        sqlBuilder = selectColsAssembly(sqlBuilder, param, groupByCols, timeAggType);
        sqlBuilder.from(tableName);

        for (Map.Entry<String, ?> entry : param.entrySet()) {
            String key = entry.getKey();

            if (StringUtils.equals("biz_mode", key) ||
                    StringUtils.equals("empCodes", key) ||
                    StringUtils.equals(CALC_DATE_NAME, key) ||
                    StringUtils.equals("ext_info", key)) {
                continue;
            }
            Object value = entry.getValue();
            if (key.startsWith(OR)) {
                String[] splitKey = key.split("-");

                SqlBuilder.Condition orConditon = new SqlBuilder.Condition();
                for (int i = 1; i < splitKey.length; i++) {
                    SqlBuilder.Condition subCondition = buildCondition(splitKey[i], ((Object[]) value)[i-1]);
                    orConditon.or(subCondition);
                }
                String sqlWithParams = orConditon.getSqlWithParams();
                sqlBuilder.and("(" + sqlWithParams + ")");
            } else {
                SqlBuilder.Condition newCondition = buildCondition(key, value);
                condition.and(newCondition);

            }

        }

        String otherCon = otherCon(param, groupByCols, timeAggType);
        if (StringUtils.isNotBlank(otherCon)) {
            condition.and(otherCon);
        }
        sqlBuilder.whereWithCondition(condition);
        sqlBuilder.whereWithCondition(getPartitionDCondition(tableName));
        if (CollectionUtils.isNotEmpty(groupByCols)) {
            sqlBuilder.groupBy(Joiner.on(",").skipNulls().join(groupByCols));
        }
        return sqlBuilder.getSql();
    }

    private SqlBuilder.Condition getPartitionDCondition(String tableName) {
        String switchStr = remoteConfig.getConfigValue("grp.partd.where.switch");

        if ((StringUtils.isBlank(switchStr) || StringUtils.equals("on", switchStr)) && partitionTableList.contains(tableName)) {
            String updateTime = "";
            try {
                updateTime = updatetimeDao.getLastUpdateTime();
            }catch (Exception e){
                log.error("getGrpUpdateTime error",e);
            }
            SqlBuilder.Condition condition = new SqlBuilder.Condition();
            condition.eq("partition_d", "'" + updateTime + "'");
            return condition;
        }
        return null;
    }


    private SqlBuilder.Condition buildCondition(String paramKey, Object paramVal) {

        if (StringUtils.isBlank(paramKey) || Objects.isNull(paramVal)) {
            return null;
        }

        SqlBuilder.Condition condition = new SqlBuilder.Condition();

        if (paramVal instanceof String) {
            condition.eq(paramKey, "'" + paramVal + "'");
        }else if ( paramVal instanceof Number) {
            condition.eq(paramKey, paramVal);
        } else if (paramVal instanceof Collection) {
            if (((Collection<?>)paramVal).size() > 0) {
                condition.in(paramKey, (List<?>) paramVal);
            }
        } else if (paramVal.getClass().isArray()) {
            Object[] arrayParam = (Object[]) paramVal;
            SqlBuilder.Condition lteCon = new SqlBuilder.Condition();
            if (Objects.nonNull(arrayParam[0])) {
                if (arrayParam[0] instanceof String) {
                    condition.gte(paramKey, "'" + arrayParam[0] + "'");
                } else {
                    condition.gte(paramKey, arrayParam[0]);
                }

            }
            condition.and(lteCon.lte(paramKey, arrayParam[0] instanceof String ?"'" + arrayParam[1] + "'"
                    : arrayParam[1]), () ->arrayParam.length > 1 && Objects.nonNull(arrayParam[1]));
        } else {
            throw new ServiceException("1000400", "value type is not supported");
        }
        return condition;
    }

    protected Map<String, Object> rstAggByTimeDim(List<Map<String, Object>> rst, String timeAggType,
                                                String calcDateName, String fieldName) {



        if (StringUtils.equals(timeAggType, TimeAggTypeEnum.WEEK.getName()) ||
                StringUtils.equals(timeAggType, TimeAggTypeEnum.MONTH.getName())) {
            return rst.stream()
                    .filter(o -> Objects.nonNull(o.get(fieldName)))
                    .collect(Collectors.toMap(entry -> (String) entry.get(calcDateName),
                            entry -> BigDecimal.valueOf(((Number) entry.get(fieldName)).doubleValue())));
        } else {
            throw new ServiceException("1000400", "timeagg type is not supported");
        }

    }

//    public Map<String, Object> aggregateByWeek(List<Map<String, Object>> data, String calcDateName,
//                                               String fieldName,String startDate, String endDate) {
//        return data.stream()
//                .filter(o -> Objects.nonNull(o.get(fieldName)))
//                .collect(Collectors.toMap(entry -> (String) entry.get(calcDateName),
//                        entry -> BigDecimal.valueOf(((Number) entry.get(fieldName)).doubleValue())));
//    }

//    private String getWeekRangeString(String dateStr, String startDate, String endDate) {
//        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
//        LocalDate date = LocalDate.parse(dateStr, dtf);
//        LocalDate startOfWeek = date.with(TemporalAdjusters.previousOrSame(java.time.DayOfWeek.MONDAY));
//        LocalDate endOfWeek = date.with(TemporalAdjusters.nextOrSame(java.time.DayOfWeek.SUNDAY));
//
//        if (date.isBefore(LocalDate.parse(startDate, dtf))) {
//            startOfWeek = LocalDate.parse(startDate, dtf);
//        }
//
//        if (date.isAfter(LocalDate.parse(endDate, dtf))) {
//            endOfWeek = LocalDate.parse(endDate, dtf);
//        }
//
//        return dtf.format(startOfWeek) + "~" + dtf.format(endOfWeek);
//    }

//    private Map<String, Object> aggregateByMonth(List<Map<String, Object>> data, String calcDateName, String fieldName) {
//        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
//
//        return data.stream()
//                .collect(Collectors.groupingBy(
//                        entry -> getMonthString(LocalDate.parse((String) entry.get(calcDateName), dtf)),
//                        Collectors.collectingAndThen(
//                                Collectors.mapping(
//                                        entry -> BigDecimal.valueOf(((Number) entry.get(fieldName)).doubleValue()),
//                                        Collectors.reducing(BigDecimal.ZERO, BigDecimal::add)
//                                ),
//                                result -> result
//                        )
//                ));
//    }

//    private String getMonthString(LocalDate date) {
//        DateTimeFormatter dtfMonth = DateTimeFormatter.ofPattern("yyyy-MM");
//        return dtfMonth.format(date);
//    }


}
