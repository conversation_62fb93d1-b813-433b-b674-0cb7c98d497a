package com.ctrip.tour.business.dashboard.grpBusiness.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Sensitive;
import com.ctrip.platform.dal.dao.annotation.Type;
import java.sql.Types;
import java.sql.Timestamp;

import com.ctrip.platform.dal.dao.DalPojo;

/**
 * <AUTHOR>
 * @date 2024-12-10
 */
@Entity
@Database(name = "TtdReportDB_W")
@Table(name = "dim_prd_grp_basedim_work_platform_df1")
public class DimPrdGrpBasedimWorkPlatformDf1 implements DalPojo {

    /**
     * 主键id
     */
    @Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long id;

    /**
     * 产品id
     */
	@Column(name = "productid")
	@Type(value = Types.BIGINT)
	private Long productid;

    /**
     * 子业务线
     */
	@Column(name = "sub_bu_type")
	@Type(value = Types.VARCHAR)
	private String subBuType;

    /**
     * 产品类型id
     */
	@Column(name = "prd_category_id")
	@Type(value = Types.INTEGER)
	private Integer prdCategoryId;

    /**
     * 产品类型名称
     */
	@Column(name = "prd_category_name")
	@Type(value = Types.VARCHAR)
	private String prdCategoryName;

    /**
     * 产品板块
     */
	@Column(name = "prd_pattern_plat")
	@Type(value = Types.VARCHAR)
	private String prdPatternPlat;

    /**
     * 目的地区域
     */
	@Column(name = "dest_domain")
	@Type(value = Types.VARCHAR)
	private String destDomain;

    /**
     * 销售模式名称
     */
	@Column(name = "sale_mode_name")
	@Type(value = Types.VARCHAR)
	private String saleModeName;

    /**
     * 客源地/目的地
     */
	@Column(name = "tour_region_type")
	@Type(value = Types.VARCHAR)
	private String tourRegionType;

    /**
     * 产品形态id
     */
	@Column(name = "prd_pattern_id")
	@Type(value = Types.INTEGER)
	private Integer prdPatternId;

    /**
     * 产品形态名称
     */
	@Column(name = "prd_pattern_name")
	@Type(value = Types.VARCHAR)
	private String prdPatternName;

    /**
     * 产品大区id
     */
	@Column(name = "prd_region_id")
	@Type(value = Types.INTEGER)
	private Integer prdRegionId;

    /**
     * 产品大区名称
     */
	@Column(name = "prd_region_name")
	@Type(value = Types.VARCHAR)
	private String prdRegionName;

    /**
     * 产品经理eid
     */
	@Column(name = "pm_eid_email")
	@Type(value = Types.VARCHAR)
	private String pmEidEmail;

    /**
     * 运营一级大区
     */
	@Column(name = "dest_first_region")
	@Type(value = Types.VARCHAR)
	private String destFirstRegion;

    /**
     * 运营二级大区
     */
	@Column(name = "dest_sec_region")
	@Type(value = Types.VARCHAR)
	private String destSecRegion;

    /**
     * 目的地大洲ID
     */
	@Column(name = "dest_continent_id")
	@Type(value = Types.INTEGER)
	private Integer destContinentId;

    /**
     * 目的地大洲名称
     */
	@Column(name = "dest_continent_name")
	@Type(value = Types.VARCHAR)
	private String destContinentName;

    /**
     * 目的地国家ID
     */
	@Column(name = "dest_country_id")
	@Type(value = Types.INTEGER)
	private Integer destCountryId;

    /**
     * 目的地国家名称
     */
	@Column(name = "dest_country_name")
	@Type(value = Types.VARCHAR)
	private String destCountryName;

    /**
     * 目的地省份ID
     */
	@Column(name = "dest_province_id")
	@Type(value = Types.INTEGER)
	private Integer destProvinceId;

    /**
     * 目的地省份名称
     */
	@Column(name = "dest_province_name")
	@Type(value = Types.VARCHAR)
	private String destProvinceName;

    /**
     * 目的地城市ID
     */
	@Column(name = "dest_city_id")
	@Type(value = Types.INTEGER)
	private Integer destCityId;

    /**
     * 目的地城市名称
     */
	@Column(name = "dest_city_name")
	@Type(value = Types.VARCHAR)
	private String destCityName;

    /**
     * 驻地业务经理
     */
	@Column(name = "local_pmeid")
	@Type(value = Types.VARCHAR)
	private String localPmeid;

    /**
     * 分区日期
     */
	@Column(name = "partition_d")
	@Type(value = Types.VARCHAR)
	private String partitionD;

    /**
     * 更新时间
     */
	@Column(name = "datachange_lasttime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getProductid() {
		return productid;
	}

	public void setProductid(Long productid) {
		this.productid = productid;
	}

	public String getSubBuType() {
		return subBuType;
	}

	public void setSubBuType(String subBuType) {
		this.subBuType = subBuType;
	}

	public Integer getPrdCategoryId() {
		return prdCategoryId;
	}

	public void setPrdCategoryId(Integer prdCategoryId) {
		this.prdCategoryId = prdCategoryId;
	}

	public String getPrdCategoryName() {
		return prdCategoryName;
	}

	public void setPrdCategoryName(String prdCategoryName) {
		this.prdCategoryName = prdCategoryName;
	}

	public String getPrdPatternPlat() {
		return prdPatternPlat;
	}

	public void setPrdPatternPlat(String prdPatternPlat) {
		this.prdPatternPlat = prdPatternPlat;
	}

	public String getDestDomain() {
		return destDomain;
	}

	public void setDestDomain(String destDomain) {
		this.destDomain = destDomain;
	}

	public String getSaleModeName() {
		return saleModeName;
	}

	public void setSaleModeName(String saleModeName) {
		this.saleModeName = saleModeName;
	}

	public String getTourRegionType() {
		return tourRegionType;
	}

	public void setTourRegionType(String tourRegionType) {
		this.tourRegionType = tourRegionType;
	}

	public Integer getPrdPatternId() {
		return prdPatternId;
	}

	public void setPrdPatternId(Integer prdPatternId) {
		this.prdPatternId = prdPatternId;
	}

	public String getPrdPatternName() {
		return prdPatternName;
	}

	public void setPrdPatternName(String prdPatternName) {
		this.prdPatternName = prdPatternName;
	}

	public Integer getPrdRegionId() {
		return prdRegionId;
	}

	public void setPrdRegionId(Integer prdRegionId) {
		this.prdRegionId = prdRegionId;
	}

	public String getPrdRegionName() {
		return prdRegionName;
	}

	public void setPrdRegionName(String prdRegionName) {
		this.prdRegionName = prdRegionName;
	}

	public String getPmEidEmail() {
		return pmEidEmail;
	}

	public void setPmEidEmail(String pmEidEmail) {
		this.pmEidEmail = pmEidEmail;
	}

	public String getDestFirstRegion() {
		return destFirstRegion;
	}

	public void setDestFirstRegion(String destFirstRegion) {
		this.destFirstRegion = destFirstRegion;
	}

	public String getDestSecRegion() {
		return destSecRegion;
	}

	public void setDestSecRegion(String destSecRegion) {
		this.destSecRegion = destSecRegion;
	}

	public Integer getDestContinentId() {
		return destContinentId;
	}

	public void setDestContinentId(Integer destContinentId) {
		this.destContinentId = destContinentId;
	}

	public String getDestContinentName() {
		return destContinentName;
	}

	public void setDestContinentName(String destContinentName) {
		this.destContinentName = destContinentName;
	}

	public Integer getDestCountryId() {
		return destCountryId;
	}

	public void setDestCountryId(Integer destCountryId) {
		this.destCountryId = destCountryId;
	}

	public String getDestCountryName() {
		return destCountryName;
	}

	public void setDestCountryName(String destCountryName) {
		this.destCountryName = destCountryName;
	}

	public Integer getDestProvinceId() {
		return destProvinceId;
	}

	public void setDestProvinceId(Integer destProvinceId) {
		this.destProvinceId = destProvinceId;
	}

	public String getDestProvinceName() {
		return destProvinceName;
	}

	public void setDestProvinceName(String destProvinceName) {
		this.destProvinceName = destProvinceName;
	}

	public Integer getDestCityId() {
		return destCityId;
	}

	public void setDestCityId(Integer destCityId) {
		this.destCityId = destCityId;
	}

	public String getDestCityName() {
		return destCityName;
	}

	public void setDestCityName(String destCityName) {
		this.destCityName = destCityName;
	}

	public String getLocalPmeid() {
		return localPmeid;
	}

	public void setLocalPmeid(String localPmeid) {
		this.localPmeid = localPmeid;
	}

	public String getPartitionD() {
		return partitionD;
	}

	public void setPartitionD(String partitionD) {
		this.partitionD = partitionD;
	}

	public Timestamp getDatachangeLasttime() {
		return datachangeLasttime;
	}

	public void setDatachangeLasttime(Timestamp datachangeLasttime) {
		this.datachangeLasttime = datachangeLasttime;
	}

}