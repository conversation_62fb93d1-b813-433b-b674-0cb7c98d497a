package com.ctrip.tour.business.dashboard.grpBusiness.exception;

/**
 * <AUTHOR>
 * @Date 2024/12/10
 */
public enum ExceptionEnum {

    OPERATE_NOT_SUPPORT("1000402", "calc operate not support"),
    EMP_CAN_NOT_FIND("1000403", "EMP CAN NOT FIND")

    ;


    ExceptionEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private String code;

    private String msg;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
