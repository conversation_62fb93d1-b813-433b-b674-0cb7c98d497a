package com.ctrip.tour.business.dashboard.tktBusiness.strategy.domestic.impl;

import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.grpBusiness.enums.DomesticMetricEnum;
import com.ctrip.tour.business.dashboard.sightArchives.dao.domesticmetric.EdwPrdTktCpdDashboardWeaknessStatisticsTDao;
import com.ctrip.tour.business.dashboard.sightArchives.entity.domesticMetricEntity.Domestic567WeakNessBO;
import com.ctrip.tour.business.dashboard.sightArchives.entity.domesticMetricEntity.bean.Domestic567Param;
import com.ctrip.tour.business.dashboard.sightArchives.entity.domesticmetric.*;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.DomesticSingelDrillBaseInfo;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.ExamineConfigBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.MetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.biz.SinglePeriodTrendLineBiz;
import com.ctrip.tour.business.dashboard.tktBusiness.bo.ExamineConfigBo;
import com.ctrip.tour.business.dashboard.tktBusiness.configuration.RemoteConfig;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.Bus567Dao;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.BusinessDashboardExamineeConfigV2Dao;
import com.ctrip.tour.business.dashboard.tktBusiness.entity.BusinessDashboardExamineeConfigV2;
import com.ctrip.tour.business.dashboard.tktBusiness.helper.*;
import com.ctrip.tour.business.dashboard.utils.DateUtil;
import com.ctrip.tour.business.dashboard.utils.GeneralUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class Bus567DomesticMetricStrategy {

    @Autowired
    private Bus567Dao dao;

    @Autowired
    private BusinessDashboardExamineeConfigV2Dao examineeConfigV2Dao;

    @Autowired
    private RemoteConfig remoteConfig;

    @Autowired
    private EdwPrdTktCpdDashboardWeaknessStatisticsTDao edwPrdTktCpdDashboardWeaknessStatisticsTDao;


    public DomesticMetricDetailInfo getSingleMetricCardData(String domainName,
                                                            TimeFilter timeFilter,
                                                            List<MetricInfoBean> metricInfoBeanList,
                                                            String d,
                                                            Boolean isFirst,
                                                            Integer metricId) throws Exception {
        DomesticMetricDetailInfo metricDetailInfo = new DomesticMetricDetailInfo();
        metricDetailInfo.setMetricCode(DomesticMetricEnum.getCodeById(Integer.parseInt(metricInfoBeanList.stream().map(MetricInfoBean::getMetric).filter(Objects::nonNull).findFirst().orElse("0"))));
        Map<String, MetricInfoBean> metricQuarterMap = metricInfoBeanList.stream().collect(Collectors.toMap(MetricInfoBean::getQuarter, metricInfoBean -> metricInfoBean));
        if (metricQuarterMap.isEmpty()) {
            return new DomesticMetricDetailInfo();
        }


        List<String> dateTypeList = new ArrayList<>(metricQuarterMap.keySet());
        if ("month".equals(timeFilter.getDateType())) {
            dateTypeList = Collections.singletonList(timeFilter.getMonth());
        }
        Domestic567ParamBean domestic567ParamBean = generate567SearchParamBean(d, timeFilter.getYear(), "1", dateTypeList, domainName, "month".equals(timeFilter.getDateType()), metricId, timeFilter.getMonth());
        Map<String, Domestic567SearchResult> resultMap = edwPrdTktCpdDashboardWeaknessStatisticsTDao.getTicketWeaknessInfo(domestic567ParamBean);

        int additionalGapDay = Bus567Helper.getAdditionalGapDayV4(timeFilter.getYear(),timeFilter.getDateType(),dateTypeList, timeFilter.getMonth(), timeFilter.getQuarter(), timeFilter.getHalf(),d);
        Integer gapDays = dao.getGapDaysV2("5,6,7", timeFilter.getYear(), timeFilter.getDateType(), d, dateTypeList);
        if (gapDays == null || gapDays <= 0) {
            return new DomesticMetricDetailInfo();
        }
        gapDays = gapDays - additionalGapDay;

        metricDetailInfo.setTableHeaderList(Bus567Helper.getTableList());
        List<DomesticNoDrillTableData> tableList = getTableInfoList(resultMap, metricDetailInfo.getMetricCode(), metricDetailInfo, gapDays);
        metricDetailInfo.setTableDataItemList(tableList);
        metricDetailInfo.setCompleteRate(tableList.stream().map(DomesticNoDrillTableData::getSightCompleteRate)
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(0.0));

        //设置默认下钻维度
        String maxQuarter = DateUtil.getMaxQuarters(new ArrayList<>(metricQuarterMap.keySet()));
        MetricHelper.setMetricCardDrillDownInfV2(metricQuarterMap.get(maxQuarter), metricDetailInfo);
        //获取排名数据
        DalHints rankDalHints = new DalHints().asyncExecution();
        getRankDataAsync(timeFilter.getDateType(), timeFilter.getYear(), maxQuarter, timeFilter.getMonth(), d, domainName, String.valueOf(DomesticMetricEnum.getIdByCode(metricDetailInfo.getMetricCode())), rankDalHints);
        ChartHelper.fillRankDataV2(metricDetailInfo, rankDalHints.getListResult());

        return metricDetailInfo;
    }

    /**
     * 设置表格数据
     * @return
     */
    public List<DomesticNoDrillTableData> getTableInfoList(Map<String, Domestic567SearchResult> resultMap,String metricCode, DomesticMetricDetailInfo metricDetailInfo, Integer gapDays) {
        List<DomesticNoDrillTableData> tableDataList = new ArrayList<>();
        Domestic567WeightBean weightBean = getWeightInfo(metricCode);
        DomesticNoDrillTableData core = new DomesticNoDrillTableData();
        DomesticNoDrillTableData focus = new DomesticNoDrillTableData();
        DomesticNoDrillTableData general = new DomesticNoDrillTableData();
        DomesticNoDrillTableData tailHighValue = new DomesticNoDrillTableData();
        DomesticNoDrillTableData tailOther = new DomesticNoDrillTableData();

        Domestic567SearchResult core567Result = resultMap.get("core");
        core.setSightCompleteRate(core567Result.getSightCompleteRate() / gapDays);
        core.setSightRange("core");
        core.setSightDisadvantageRate(core567Result.getSightDisadvantageRate() / gapDays);
        core.setSightTargetValue(0.00);
        core.setSightGapValue(core.getSightDisadvantageRate() - core.getSightTargetValue());
        core.setSightLevelCompleteRate(computeLevelCompleteRate(core.getSightGapValue()));
        core.setSightWeight(weightBean.getCoreWeight());

        Domestic567SearchResult general567Result = resultMap.get("general");
        general.setSightRange("general");
        general.setSightDisadvantageRate(general567Result.getSightDisadvantageRate() / gapDays);
        general.setSightTargetValue(0.03);
        general.setSightGapValue(general.getSightDisadvantageRate() - general.getSightTargetValue());
        general.setSightLevelCompleteRate(computeLevelCompleteRate(general.getSightGapValue()));
        general.setSightWeight(weightBean.getGeneralWeight());
        if (metricDetailInfo != null) {
            metricDetailInfo.setDisadvantageRate(general567Result.getSightDisadvantageRate() / gapDays);
            metricDetailInfo.setOutSystemCompleteValue((general567Result.getSightDisadvantageCount() / gapDays));
        }

        Domestic567SearchResult focus567Result = resultMap.get("focus");
        focus.setSightRange("focus");
        focus.setSightDisadvantageRate(focus567Result.getSightDisadvantageRate() / gapDays);
        focus.setSightTargetValue(0.01);
        focus.setSightGapValue(focus.getSightDisadvantageRate() - focus.getSightTargetValue());
        focus.setSightLevelCompleteRate(computeLevelCompleteRate(focus.getSightGapValue()));
        focus.setSightWeight(weightBean.getFocusWeight());

        Domestic567SearchResult tailHighValue567Result = resultMap.get("tailHighValue");
        tailHighValue.setSightRange("tailHighValue");
        tailHighValue.setSightDisadvantageRate(tailHighValue567Result.getSightDisadvantageRate() / gapDays);
        tailHighValue.setSightTargetValue(0.01);
        tailHighValue.setSightGapValue(tailHighValue.getSightDisadvantageRate() - tailHighValue.getSightTargetValue());
        tailHighValue.setSightLevelCompleteRate(computeLevelCompleteRate(tailHighValue.getSightGapValue()));
        tailHighValue.setSightWeight(weightBean.getTailHighValueWeight());

        Domestic567SearchResult tailOther567Result = resultMap.get("tailOther");
        tailOther.setSightRange("tailOther");
        tailOther.setSightDisadvantageRate(tailOther567Result.getSightDisadvantageRate() / gapDays);
        tailOther.setSightTargetValue(0.03);
        tailOther.setSightGapValue(tailOther.getSightDisadvantageRate() - tailOther.getSightTargetValue());
        tailOther.setSightLevelCompleteRate(computeLevelCompleteRate(tailOther.getSightGapValue()));
        tailOther.setSightWeight(weightBean.getTailOtherWeight());

        double totalCompleteRate;
        if ("sightCover".equals(metricCode)) {
            totalCompleteRate = focus.getSightLevelCompleteRate() * 0.35 + tailHighValue.getSightLevelCompleteRate() * 0.35 +tailOther.getSightLevelCompleteRate() * 0.3;
        }else{
            totalCompleteRate = core.getSightLevelCompleteRate() * 0.25 + focus.getSightLevelCompleteRate() * 0.25 + tailHighValue.getSightLevelCompleteRate() * 0.25 +tailOther.getSightLevelCompleteRate() * 0.25;
        }
        core.setSightCompleteRate(totalCompleteRate);
        focus.setSightCompleteRate(totalCompleteRate);
        general.setSightCompleteRate(totalCompleteRate);
        tailHighValue.setSightCompleteRate(totalCompleteRate);
        tailOther.setSightCompleteRate(totalCompleteRate);

        // 如果核心劣势率为0，权重不下发，前端默认变成--
        if (core567Result.getSightDisadvantageRate() / gapDays > 0 && metricCode.equals(DomesticMetricEnum.SIGHT_COVER.getCode())) {
            core.setSightWeight(null);
            focus.setSightWeight(null);
            general.setSightWeight(null);
            tailHighValue.setSightWeight(null);
            tailOther.setSightWeight(null);
            core.setSightCompleteRate(0.00);
            focus.setSightCompleteRate(0.00);
            general.setSightCompleteRate(0.00);
            tailHighValue.setSightCompleteRate(0.00);
            tailOther.setSightCompleteRate(0.00);
        }


        tableDataList.add(core);
        tableDataList.add(focus);
        tableDataList.add(general);
        tableDataList.add(tailHighValue);
        tableDataList.add(tailOther);
        return tableDataList;
    }

    // 计算分层完成率
    public double computeLevelCompleteRate(double gap) {
        if (gap <= 0.005) {
            return Math.min(1.2, -40 * gap + 1);
        }else if (gap > 0.005 && gap <= 0.015) {
            return -20 * gap + 0.9;
        }else{
            return 0;
        }
    }

    /**
     * 获取权重信息
     * @return
     */
    public Domestic567WeightBean getWeightInfo(String metricCode) {
        Domestic567WeightBean weightBean = new Domestic567WeightBean();
        switch (metricCode) {
            case "sightCover":
                weightBean.setCoreWeight(0.00);
                weightBean.setFocusWeight(0.35);
                weightBean.setTailHighValueWeight(0.35);
                weightBean.setTailOtherWeight(0.3);
                weightBean.setGeneralWeight(0.00);
                break;
            case "typeCover":
            case "ticketBooking":
                weightBean.setCoreWeight(0.25);
                weightBean.setFocusWeight(0.25);
                weightBean.setTailHighValueWeight(0.25);
                weightBean.setTailOtherWeight(0.25);
                weightBean.setGeneralWeight(0.00);
                break;
        }
        return weightBean;
    }

    /**
     * 等级数据查询
     */
    private void getRankDataAsync(String dateType,
                                  String year,
                                  String quarter,
                                  String month,
                                  String d,
                                  String domainName,
                                  String metric,
                                  DalHints dalHints) throws Exception {
        Map<String, List<String>> inMap = new LinkedHashMap<>();
        inMap.put("query_d", Lists.newArrayList(d));
        inMap.put("examinee", Lists.newArrayList(domainName));
        inMap.put("year", Lists.newArrayList(year));
        if ("month".equals(dateType)) {
            inMap.put("month", Lists.newArrayList(month));
        } else {
            String mappingMonth = DateUtil.getMappingMonthByQuarter(d, year, quarter);
            inMap.put("month", Lists.newArrayList(mappingMonth));
        }
        inMap.put("metric", Lists.newArrayList(metric));
        dao.getRankAsync(inMap, dalHints);
    }

    public Domestic567ParamBean generate567SearchParamBean(String d,
                                                           String year,
                                                           String statisticDimId,
                                                           List<String> dateTypeList,
                                                           String domainName,
                                                           Boolean isMonth,
                                                           Integer metricId,
                                                           String monthParam) {
        Domestic567ParamBean paramBean = new Domestic567ParamBean();
        paramBean.setD(d);
        paramBean.setYear(year);
        paramBean.setStatisticDimId(statisticDimId);
        if (isMonth) {
            paramBean.setMonth(dateTypeList.stream().filter(StringUtils::isNotEmpty).map(e -> e.substring(1)).collect(Collectors.toList()));
        }else{
            paramBean.setQuarters(dateTypeList.stream().filter(StringUtils::isNotEmpty).map(e -> e.substring(1)).collect(Collectors.toList()));
        }
        paramBean.setDomainName(domainName);
        paramBean.setMetricId(metricId);
        paramBean.setMonthParam(monthParam);
        return paramBean;
    }


    public GetDomesticMetricTrendDataResponseType getDomesticMetricTrendLineData(GetDomesticMetricTrendDataRequestType request, String d) throws Exception {
        GetDomesticMetricTrendDataResponseType response = new GetDomesticMetricTrendDataResponseType();
        List<TrendLineDetailInfo> trendLineDetailInfoList = new ArrayList<>();
        response.setTrendLineDetailInfoList(trendLineDetailInfoList);
        TimeFilter timeFilter = request.getTimeFilter();
        String domainName = request.getDomainName();

        TimeFilter timeFilterWithDomain = DateUtil.getMaxQuarterTimeFilter(timeFilter, d);
        List<ExamineConfigBean> examineConfigBeanList = MultiPeriodMappingHelper.getMultiPeriodConfigList(timeFilterWithDomain, d, examineeConfigV2Dao.queryMetricAllConfig(domainName, d, String.valueOf(DomesticMetricEnum.getIdByCode(request.getMetricCode()))), null);

        TrendLineDetailInfo lineChart = new TrendLineDetailInfo();

        lineChart.setType("lineChart");
        lineChart.setDim(request.getMetricCode());

        List<TrendLineDataItem> lineDataItems = new ArrayList<>();

        lineChart.setTrendLineDataItemList(lineDataItems);
        for (ExamineConfigBean examineConfigBean : examineConfigBeanList) {
            //只下发25年的
            if (Integer.parseInt(examineConfigBean.getYear()) <= 2024) {
                continue;
            }
            //获取单季或单月的数据
            List<TrendLineDataItem> singleLineDataItems = getSingleTrendLineDataWithQuarterOrMonth(request, examineConfigBean, d);
            lineDataItems.addAll(singleLineDataItems);
        }
        trendLineDetailInfoList.add(lineChart);

        return response;
    }


    public List<TrendLineDataItem> getSingleTrendLineDataWithQuarterOrMonth(GetDomesticMetricTrendDataRequestType request,
                                                                            ExamineConfigBean examineConfigBean,
                                                                            String d) throws Exception {
        List<TrendLineDataItem> trendLineDataItems = new ArrayList<>();
        String timeString = DomesticMetricHelper.getTimeFormat(examineConfigBean);


        String domainName = request.getDomainName();
        boolean isMonth = "month".equals(examineConfigBean.getDateType());
        List<String> timeList = isMonth ? DateUtil.getMonthList(examineConfigBean.getMonth(), false) : Collections.singletonList(examineConfigBean.getQuarter());

        Domestic567ParamBean domestic567ParamBean = generate567SearchParamBean(d, examineConfigBean.getYear(), "1", timeList, domainName, isMonth, DomesticMetricEnum.getIdByCode(request.getMetricCode()),request.getTimeFilter().getMonth());
        Map<String, Domestic567SearchResult> resultMap = edwPrdTktCpdDashboardWeaknessStatisticsTDao.getTicketWeaknessInfo(domestic567ParamBean);
        if (resultMap.isEmpty()) {
            return new ArrayList<>();
        }

        int additionalGapDay = Bus567Helper.getAdditionalGapDayV4(request.getTimeFilter().getYear(),request.getTimeFilter().getDateType(),timeList, examineConfigBean.getMonth(), request.getTimeFilter().getQuarter(),request.getTimeFilter().getHalf(),d);
        Integer gapDays = dao.getGapDaysV2("5,6,7", request.getTimeFilter().getYear(), request.getTimeFilter().getDateType(), d, timeList);
        if (gapDays == null || gapDays <= 0 ) {
            return new ArrayList<>();
        }
        gapDays = gapDays - additionalGapDay;

        List<DomesticNoDrillTableData> tableData = getTableInfoList(resultMap, request.getMetricCode(), null, gapDays);


        TrendLineDataItem coreItem = new TrendLineDataItem();
        TrendLineDataItem focusItem = new TrendLineDataItem();
        TrendLineDataItem generalItem = new TrendLineDataItem();
        TrendLineDataItem tailHighValueItem = new TrendLineDataItem();
        TrendLineDataItem tailOtherItem = new TrendLineDataItem();

        TrendLineDataItem coreItemGap = new TrendLineDataItem();
        TrendLineDataItem focusItemGap = new TrendLineDataItem();
        TrendLineDataItem generalItemGap = new TrendLineDataItem();
        TrendLineDataItem tailHighValueItemGap = new TrendLineDataItem();
        TrendLineDataItem tailOtherItemGap = new TrendLineDataItem();
        for (DomesticNoDrillTableData sight : tableData) {
            switch (sight.getSightRange()) {
                case "core":
                    coreItem.setName("core");
                    coreItem.setTime(timeString);
                    coreItem.setValue(sight.getSightDisadvantageRate());

                    coreItemGap.setName("coreGap");
                    coreItemGap.setTime(timeString);
                    coreItemGap.setValue(sight.getSightGapValue());
                    break;
                case "focus":
                    focusItem.setName("focus");
                    focusItem.setTime(timeString);
                    focusItem.setValue(sight.getSightDisadvantageRate());

                    focusItemGap.setName("focusGap");
                    focusItemGap.setTime(timeString);
                    focusItemGap.setValue(sight.getSightGapValue());
                    break;
                case "general":
                    generalItem.setName("general");
                    generalItem.setTime(timeString);
                    generalItem.setValue(sight.getSightDisadvantageRate());

                    generalItemGap.setName("generalGap");
                    generalItemGap.setTime(timeString);
                    generalItemGap.setValue(sight.getSightGapValue());
                    break;
                case "tailHighValue":
                    tailHighValueItem.setName("tailHighValue");
                    tailHighValueItem.setTime(timeString);
                    tailHighValueItem.setValue(sight.getSightDisadvantageRate());

                    tailHighValueItemGap.setName("tailHighValueGap");
                    tailHighValueItemGap.setTime(timeString);
                    tailHighValueItemGap.setValue(sight.getSightGapValue());
                    break;
                case "tailOther":
                    tailOtherItem.setName("tailOther");
                    tailOtherItem.setTime(timeString);
                    tailOtherItem.setValue(sight.getSightDisadvantageRate());

                    tailOtherItemGap.setName("tailOtherGap");
                    tailOtherItemGap.setTime(timeString);
                    tailOtherItemGap.setValue(sight.getSightGapValue());
                    break;
            }
        }

        trendLineDataItems.add(coreItem);
        trendLineDataItems.add(focusItem);
        trendLineDataItems.add(generalItem);
        trendLineDataItems.add(tailHighValueItem);
        trendLineDataItems.add(tailOtherItem);

        trendLineDataItems.add(coreItemGap);
        trendLineDataItems.add(focusItemGap);
        trendLineDataItems.add(generalItemGap);
        trendLineDataItems.add(tailHighValueItemGap);
        trendLineDataItems.add(tailOtherItemGap);

        return trendLineDataItems;
    }



    public GetDomesticDrillDownBaseInfoResponseType getSingleDrillDownBaseInfo(GetDomesticDrillDownBaseInfoRequestType request,
                                                                               MetricInfoBean metricInfoBean,
                                                                               String d) throws Exception {
        //567 不需要区分业务线，仅景点有，玩乐没有。
        //取考核范围也仅需要取景点考核范围，不需要取其他范围
        GetDomesticDrillDownBaseInfoResponseType response = new GetDomesticDrillDownBaseInfoResponseType();
        List<FieldDataItem> fieldDataItemList = new ArrayList<>();
        response.setFieldDataItemList(fieldDataItemList);

        TimeFilter timeFilter = request.getTimeFilter();
        String year = timeFilter.getYear();

        String metric = metricInfoBean.getMetric();
        String originLevel = metricInfoBean.getLevel();//国内  三方  大区  省份  景点

        boolean needSearch = StringUtils.isNotEmpty(request.getSearchWord());
        List<String> fieldList = Bus567Helper.getFieldList(originLevel);

        if (needSearch) {
            String searchField = request.getSearchField();//大区 省份 商拓
            if (searchField != null) {
                String searchFieldColumnName = MetricHelper.getDrillDownColumnName(searchField);
                fieldList = Lists.newArrayList(searchFieldColumnName);
            }
        } else {
            response.setDefaultChosenField(MetricHelper.getDefaultChosenField(originLevel, metric));
        }

        for (String field : fieldList) {
            String name = MetricHelper.getDataBaseColumnName(field);
            Domestic567Param param = getDomestic567TableParam(d,null,null, year, request.getTimeFilter(), originLevel, metricInfoBean.getRegionList(), name, null);
            String month = timeFilter.getMonth();
            String quarter = timeFilter.getQuarter();
            String dateType = MetricHelper.getDateTypeColumnName(timeFilter.getDateType());
            List<String> timeList = DateUtil.getTimeList(dateType, month, quarter,timeFilter.getHalf());
            Map<String, List<String>> inMap = new LinkedHashMap<>();
            inMap.put(dateType, timeList);
            Integer additionalGapDay = Bus567Helper.getAddtionalGapDay(inMap, null, year, dateType, month, quarter, timeFilter.getHalf(), d);

            if (inMap.size() > 2) {
                param.setIsCoefficientIdentifier(inMap.get("is_coefficient_identifier").get(0));
            }
            List<String> drillDataList = edwPrdTktCpdDashboardWeaknessStatisticsTDao.getDrillDataByField(param);
            FieldDataItem item = new FieldDataItem();
            item.setNeedBubble(false);
            item.setNeedLine(false);
            DomesticSingelDrillBaseInfo domesticSingelDrillBaseInfo = remoteConfig.getDrillDownFieldBeanV2(request.getMetricCode(), request.getBusinessId(), request.getSubBusinessId(), name);
            if (domesticSingelDrillBaseInfo != null) {
                item.setNeedBubble(domesticSingelDrillBaseInfo.getNeedBubble());
                item.setNeedLine(domesticSingelDrillBaseInfo.getNeedTrend());
            }
            fieldDataItemList.add(item);
            item.setField(name);
            List<FieldValueItem> fieldValueItemList = new ArrayList<>();
            item.setFieldValueItemList(fieldValueItemList);
            for (String rowResult : drillDataList) {
                FieldValueItem fieldValueItem = new FieldValueItem();
                fieldValueItem.setValue(rowResult);
                fieldValueItemList.add(fieldValueItem);
            }
        }
        return response;
    }

    private Domestic567Param getDomestic567TableParam(String d,Integer pageIndex,Integer pageSize, String year, TimeFilter timeFilter, String originLevel,
                                                      List<String> rangeList, String field, List<String> fieldValueList) {
        Domestic567Param param = new Domestic567Param();

        param.setD(d);
        param.setPageIndex(pageIndex);
        if (pageSize != null) {
            param.setPageSize(pageSize / 4);
        }
        param.setField(field);
        param.setYear(year);
        //按月，按季度，按半年，按年
        if (timeFilter.getDateType().equalsIgnoreCase("quarter")) {
            switch (timeFilter.getQuarter()){
                case "Q1":
                    param.setQuarters(Arrays.asList("1"));
                    break;
                case "Q2":
                    param.setQuarters(Arrays.asList("2"));
                    break;
                case "Q3":
                    param.setQuarters(Arrays.asList("3"));
                    break;
                case "Q4":
                    param.setQuarters(Arrays.asList("4"));
                    break;
            }
        } else if (timeFilter.getDateType().equalsIgnoreCase("half")) {
            if ("H1".equalsIgnoreCase(timeFilter.getHalf())) {
                param.setQuarters(Arrays.asList("1", "2"));
            } else {
                param.setQuarters(Arrays.asList("3", "4"));
            }
        } else if (timeFilter.getDateType().equalsIgnoreCase("month")) {
            param.setMonth(Arrays.asList(timeFilter.getMonth()));
        }
        switch (originLevel){
            case "大区"://NOSONAR
                param.setBusinessRegionName(rangeList);
                break;
            case "省份"://NOSONAR
                param.setProvinceName(rangeList);
                break;
            case "景点"://NOSONAR
                param.setExamineObject(rangeList);
                break;
        }
        if (StringUtils.isNotEmpty(field)) {
            param.setExamineLevel(field);
            switch (field) {
                case "大区"://NOSONAR
                    param.setStatisticsDimId("3");
                    break;
                case "省份"://NOSONAR
                    param.setStatisticsDimId("4");
                    break;
                case "商拓"://NOSONAR
                    param.setStatisticsDimId("4");
                    param.setExamineLevel("景点");//NOSONAR
                    break;
            }
        }
        //下钻数据限制范围
        if(CollectionUtils.isNotEmpty(fieldValueList)){
            switch (field){
                case "大区"://NOSONAR
                    param.setBusinessRegionName(fieldValueList);
                    break;
                case "省份"://NOSONAR
                    param.setProvinceName(fieldValueList);
                    break;
                case "商拓"://NOSONAR
                    param.setExamineObject(fieldValueList);
                    break;
            }
        }

        return param;
    }


    public GetDomesticTableDataResponseType getSingleTableData(GetDomesticTableDataRequestType request,
                                                               MetricInfoBean metricInfoBean, String d) throws Exception {
        GetDomesticTableDataResponseType response = new GetDomesticTableDataResponseType();
        List<DomesticTableData> tableDataItemList = new ArrayList<>();
        response.setTableDataItemList(tableDataItemList);

        Integer metricId = DomesticMetricEnum.getIdByCode(request.getMetricCode());


        String originLevel = metricInfoBean.getLevel();
        TimeFilter timeFilter = request.getTimeFilter();
        String year = timeFilter.getYear();
        List<String> regionList = metricInfoBean.getRegionList();

        DrillDownFilter drillDownFilter = request.getDrillDownFilter();
        String field = MetricHelper.getDrillDownColumnName(drillDownFilter.getField());
        List<String> fieldValueList = drillDownFilter.getFieldValueList();
        response.setTableHeaderList(Bus567Helper.getTableHeaderList(field));

        if("trend".equalsIgnoreCase(request.getQueryType())){
            List<TrendLineDetailInfo> trendLineDetailInfoList = getDrillThrendLineDetailInfoList(request, metricInfoBean, d);
            response.setTrendLineDetailInfoList(trendLineDetailInfoList);
            return response;
        }

        if (request.getBusinessId() != null && request.getBusinessId() == 2) {
            return response;
        }
        //当前值
        Domestic567Param param = getDomestic567TableParam(d, request.getPageNo(), request.getPageSize(), year, request.getTimeFilter(), originLevel, regionList, drillDownFilter.getField(), fieldValueList);
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();
        String dateType = MetricHelper.getDateTypeColumnName(timeFilter.getDateType());
        List<String> timeList = DateUtil.getTimeList(dateType, month, quarter,timeFilter.getHalf());
        Map<String, List<String>> inMap = new LinkedHashMap<>();
        inMap.put(dateType, timeList);
        Integer additionalGapDay = Bus567Helper.getAddtionalGapDay(inMap, null, year, dateType, month, quarter, timeFilter.getHalf(), d);
        Integer gapDays = dao.getGapDays("5,6,7", year, dateType, d, timeList);
        if (inMap.size() > 1) {
            param.setIsCoefficientIdentifier(inMap.get("is_coefficient_identifier").get(0));
        }
        Integer actualGapDays = 0;
        if (gapDays != null && additionalGapDay != null) {
            actualGapDays = gapDays - additionalGapDay;
        }
        Integer totalCount = edwPrdTktCpdDashboardWeaknessStatisticsTDao.getDrillTableCountByField(param);
        response.setTotalNum(totalCount);
        if (totalCount == null) {
            return response;
        }
        response.setTotalNum(totalCount*4);


        List<Domestic567WeakNessBO> currentResult = edwPrdTktCpdDashboardWeaknessStatisticsTDao.getDrillTableByField(param);

        //组装数据
        ChartHelper.buildBus567DomesticTableData(actualGapDays,metricId, currentResult, tableDataItemList, field, response.getTableHeaderList());


        return response;
    }

    private List<TrendLineDetailInfo> getDrillThrendLineDetailInfoList(GetDomesticTableDataRequestType request, MetricInfoBean metricInfoBean, String d) throws Exception {
        TimeFilter timeFilter = request.getTimeFilter();
        String domainName = request.getDomainName();
        Integer metric = DomesticMetricEnum.getIdByCode(request.getMetricCode());
        TimeFilter timeFilterWithDomain = DateUtil.getMaxQuarterTimeFilter(timeFilter, d);
        List<ExamineConfigBean> examineConfigBeanList = MultiPeriodMappingHelper.getMultiPeriodConfigList(timeFilterWithDomain, d, examineeConfigV2Dao.queryMetricAllConfig(domainName, d, String.valueOf(metric)), null);
        List<TrendLineDetailInfo> trendLineDetailInfoList = new ArrayList<>();

        Map<String, List<TrendLineDataItem>> dimRegionThrendMap = new HashMap<>();
        for (ExamineConfigBean examineConfigBean : examineConfigBeanList) {
            //只下发25年的
            if (Integer.parseInt(examineConfigBean.getYear()) <= 2024) {
                continue;
            }
            //获取单季或单月的数据
            Map<String, TrendLineDataItem> dimRegionDBMap = getTrendLineDataByDrill(request, examineConfigBean, d);
            for (Map.Entry<String, TrendLineDataItem> item : dimRegionDBMap.entrySet()) {
                if (dimRegionThrendMap.containsKey(item.getKey())) {
                    List<TrendLineDataItem> lineDataItems = dimRegionThrendMap.get(item.getKey());
                    lineDataItems.add(item.getValue());
                } else {
                    List<TrendLineDataItem> lineDataItems =new ArrayList<>();
                    lineDataItems.add(item.getValue());
                    dimRegionThrendMap.put(item.getKey(), lineDataItems);
                }
            }
        }
        for(Map.Entry<String, List<TrendLineDataItem>> item : dimRegionThrendMap.entrySet()){
            String dim=item.getKey().split(":")[0];
            TrendLineDetailInfo lineChartFwRate = new TrendLineDetailInfo();
            lineChartFwRate.setType("lineChart");
            lineChartFwRate.setDim(dim);
            List<TrendLineDataItem> lineDataItems = new ArrayList<>();
            lineChartFwRate.setTrendLineDataItemList(item.getValue());
            trendLineDetailInfoList.add(lineChartFwRate);
        }
        return trendLineDetailInfoList;
    }

    private Map<String, TrendLineDataItem> getTrendLineDataByDrill(GetDomesticTableDataRequestType request, ExamineConfigBean examineConfigBean, String d) throws Exception {
       //对不同月份，不同考核层级，不同下钻维度，查出实际劣势率
        String timeString = DomesticMetricHelper.getTimeFormat(examineConfigBean);
        BusinessDashboardExamineeConfigV2 examineeConfigV2 = examineConfigBean.getBusinessDashboardExamineeConfigV2();
        String originLevel = examineeConfigV2.getExamineLevel();
        List<String> regionList = null;
        if (StringUtils.isNotEmpty(examineeConfigV2.getExamineRange())) {
            regionList = Arrays.stream(examineeConfigV2.getExamineRange().split(",")).map(String::trim).collect(Collectors.toList());
        }
        //根据下钻维度获取完成值
        Domestic567Param param = getDomestic567TableParam(d, request.getPageNo(), request.getPageSize(), examineConfigBean.getYear(), request.getTimeFilter(), originLevel, regionList, request.getDrillDownFilter().getField(), request.getDrillDownFilter().getFieldValueList());
        TimeFilter timeFilter = request.getTimeFilter();
        String year = timeFilter.getYear();
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();
        String dateType = MetricHelper.getDateTypeColumnName(timeFilter.getDateType());
        List<String> timeList = DateUtil.getTimeList(dateType, month, quarter,timeFilter.getHalf());
        Map<String, List<String>> inMap = new LinkedHashMap<>();
        inMap.put(dateType, timeList);
        Integer additionalGapDay = Bus567Helper.getAddtionalGapDay(inMap, null, year, dateType, month, quarter, timeFilter.getHalf(), d);
//        d = "2025-07-30";
        Integer gapDays = dao.getGapDays("5,6,7", year, dateType, d, timeList);
        if (inMap.size() > 1) {
            param.setIsCoefficientIdentifier(inMap.get("is_coefficient_identifier").get(0));
        }
        Integer actualGapDays = 0;
        if (gapDays != null && additionalGapDay != null) {
            actualGapDays = gapDays - additionalGapDay;
        }
        List<Domestic567WeakNessBO> currentResult = edwPrdTktCpdDashboardWeaknessStatisticsTDao.getDrillTableByField(param);
        Integer metric = DomesticMetricEnum.getIdByCode(request.getMetricCode());
        Map<String, TrendLineDataItem> dimRegionThrendMap = new HashMap<>();
        for (Domestic567WeakNessBO bo : currentResult) {
            buildDimRegionThrendMap(actualGapDays,dimRegionThrendMap, bo, metric, timeString, request.getDrillDownFilter().getField(), "核心", "CwRate");//NOSONAR
            buildDimRegionThrendMap(actualGapDays,dimRegionThrendMap, bo, metric, timeString, request.getDrillDownFilter().getField(), "长尾高价值", "LhvwRate");//NOSONAR
            buildDimRegionThrendMap(actualGapDays,dimRegionThrendMap, bo, metric, timeString, request.getDrillDownFilter().getField(), "聚焦", "FwRate");//NOSONAR
            buildDimRegionThrendMap(actualGapDays,dimRegionThrendMap, bo, metric, timeString, request.getDrillDownFilter().getField(), "长尾其他", "LowRate");//NOSONAR
        }
        return dimRegionThrendMap;
    }

    private void buildDimRegionThrendMap(Integer actualGapDays,Map<String, TrendLineDataItem> dimRegionThrendMap,
                                         Domestic567WeakNessBO bo,
                                         Integer metric,
                                         String timeString,
                                         String field,
                                         String level,
                                         String cwRate) {

        String name = "";
        switch (field) {
            case "大区"://NOSONAR
                name = bo.getBusinessRegionName();
                break;
            case "省份"://NOSONAR
                name = bo.getProvinceName();
                break;
            case "商拓"://NOSONAR
                name = bo.getExamineObject();
                break;
        }
        String key = String.format("%s:%s", cwRate, name);
        //实际劣势率
        Double value = ChartHelper.getValueByMetric(metric, level, bo);
        TrendLineDataItem item = new TrendLineDataItem();
        item.setTime(timeString);
        item.setValue(value);
        if (actualGapDays != 0) {
            item.setValue(value / actualGapDays);
        }
        item.setName(name);
        dimRegionThrendMap.put(key, item);
    }

    public GetFirstPageDomesticMetricCardDrillDataResponseType getFirstPageDomesticMetricCardDrillData(
            GetFirstPageDomesticMetricCardDrillDataRequestType request, MetricInfoBean metricInfoBean, String d) throws Exception {
        GetFirstPageDomesticMetricCardDrillDataResponseType response = new GetFirstPageDomesticMetricCardDrillDataResponseType();
        List<FirstPageDomesticTableData> tableDataItemList = new ArrayList<>();
        response.setTableDataItemList(tableDataItemList);

        Integer metricId = DomesticMetricEnum.getIdByCode(request.getMetricCode());

        String originLevel = metricInfoBean.getLevel();
        TimeFilter timeFilter = request.getTimeFilter();
        String year = timeFilter.getYear();
        List<String> regionList = metricInfoBean.getRegionList();

        DrillDownFilter drillDownFilter = new DrillDownFilter();
        drillDownFilter.setField(request.getDefaultField());
        String field = MetricHelper.getDrillDownColumnName(drillDownFilter.getField());
        List<String> fieldValueList = drillDownFilter.getFieldValueList();

        response.setTableHeaderList(Bus567Helper.getFirstDrillHeaderList(field));
        //当前值
        Domestic567Param param = getDomestic567TableParam(d,null,null, year, request.getTimeFilter(), originLevel, regionList, drillDownFilter.getField(), fieldValueList);
        String month = timeFilter.getMonth();
        String quarter = timeFilter.getQuarter();
        String dateType = MetricHelper.getDateTypeColumnName(timeFilter.getDateType());
        List<String> timeList = DateUtil.getTimeList(dateType, month, quarter,timeFilter.getHalf());
        Map<String, List<String>> inMap = new LinkedHashMap<>();
        inMap.put(dateType, timeList);
        Integer additionalGapDay = Bus567Helper.getAddtionalGapDay(inMap, null, year, dateType, month, quarter, timeFilter.getHalf(), d);
//        d = "2025-07-30";
        Integer gapDays = dao.getGapDays("5,6,7", year, dateType, d, timeList);
        if (inMap.size() > 1) {
            param.setIsCoefficientIdentifier(inMap.get("is_coefficient_identifier").get(0));
        }
        Integer actualGapDays = 0;
        if (gapDays != null && additionalGapDay != null) {
            actualGapDays = gapDays - additionalGapDay;
        }
        List<Domestic567WeakNessBO> currentResult = edwPrdTktCpdDashboardWeaknessStatisticsTDao.getDrillTableByField(param);

        //组装数据
        List<DomesticTableData> oldTableDataItemList = new ArrayList<>();
        ChartHelper.buildBus567DomesticTableData(actualGapDays,metricId, currentResult, oldTableDataItemList, field, response.getTableHeaderList());
        Map<String, FirstPageDomesticTableData> firstPageDomesticTableDataMap = new HashMap<>();
        for (DomesticTableData data : oldTableDataItemList) {
            String key = String.format("%s:%s", data.getRegionName(), data.getProvinceName());
            FirstPageDomesticTableData row = null;
            if (firstPageDomesticTableDataMap.containsKey(key)) {
                row = firstPageDomesticTableDataMap.get(key);
            } else {
                row = new FirstPageDomesticTableData();
                tableDataItemList.add(row);
                firstPageDomesticTableDataMap.put(key,row);
                row.setRegionName(data.getRegionName());
                row.setProvinceName(data.getProvinceName());
                row.setCompleteRate(new Double(0.0));
            }
            if (metricId == 5) {
                String ratioLevel = data.getRatioLevel();
                Double completeRate = new Double(0.0);
                switch (ratioLevel) {
                    case "核心"://NOSONAR
                        row.setCoreDisadvantageRate(data.getCompleteRate());
                        row.setCoreDisadvantageTargetRate(data.getTargetRate());
                        break;
                    case "聚焦"://NOSONAR
                        row.setFocusDisadvantageRate(data.getCompleteRate());
                        row.setFocusDisadvantageTargetRate(data.getTargetRate());
                        completeRate = data.getCompleteRate() * 0.35;
                        break;
                    case "长尾高价值"://NOSONAR
                        row.setTailHighValueDisadvantageRate(data.getCompleteRate());
                        row.setTailHighValueDisadvantageTargetRate(data.getTargetRate());
                        completeRate = data.getCompleteRate() * 0.35;
                        break;
                    case "长尾其他"://NOSONAR
                        row.setTailHighOtherDisadvantageRate(data.getCompleteRate());
                        row.setTailHighOtherDisadvantageTargetRate(data.getTargetRate());
                        completeRate = data.getCompleteRate() * 0.3;
                        break;
                }
                row.setCompleteRate(row.getCompleteRate() + completeRate);
            } else {
                String ratioLevel = data.getRatioLevel();
                Double completeRate = new Double(0.0);
                switch (ratioLevel) {
                    case "核心"://NOSONAR
                        row.setCoreDisadvantageRate(data.getCompleteRate());
                        row.setCoreDisadvantageTargetRate(data.getTargetRate());
                        completeRate = data.getCompleteRate() * 0.25;
                        break;
                    case "聚焦"://NOSONAR
                        row.setFocusDisadvantageRate(data.getCompleteRate());
                        row.setFocusDisadvantageTargetRate(data.getTargetRate());
                        completeRate = data.getCompleteRate() * 0.25;
                        break;
                    case "长尾高价值"://NOSONAR
                        row.setTailHighValueDisadvantageRate(data.getCompleteRate());
                        row.setTailHighValueDisadvantageTargetRate(data.getTargetRate());
                        completeRate = data.getCompleteRate() * 0.25;
                        break;
                    case "长尾其他"://NOSONAR
                        row.setTailHighOtherDisadvantageRate(data.getCompleteRate());
                        row.setTailHighOtherDisadvantageTargetRate(data.getTargetRate());
                        completeRate = data.getCompleteRate() * 0.25;
                        break;
                }
                row.setCompleteRate(row.getCompleteRate() + completeRate);
            }
        }
        if (metricId == 5) {
            Double coreValue = new Double(0.0);
            for (FirstPageDomesticTableData row : tableDataItemList) {
                if (coreValue.equals(row.getCoreDisadvantageRate())) {
                    row.setCompleteRate(coreValue);
                }
            }
        }
        return response;
    }
}
