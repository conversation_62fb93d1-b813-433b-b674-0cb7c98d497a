include 'BaijiCommonTypes.bjsc'
include 'getMetricData.bjsc'
namespace java 'com.ctrip.soa._24922'



class CheckUserPermissionRequestType{
    //时间选择器
    getMetricData.TimeFilter timeFilter;
    string domainName;
    //该请求的来源  firstpage(首页 后续app端也算是首页的一种) detailpage(详情页)
    string source;
    //当是首页请求的时候 标识一下该首页看板是国内看板还是海外看板 枚举值 oversea 和  domestic
    string boardType;
}


class CheckUserPermissionResponseType{
    //false则没有权限查看仪表盘模块
    bool havePermission;
    //人员对应的考核业务线和指标(25年以后生效)
    list<BusinessMetricMap> businessMetricMapList
    //有权限查看时 此时可以查看的指标列表
    list<string> metricList;
    //输出此时前端应展示的看板类型 针对详情页(首页前端可自己判断) 枚举值 oversea 和  domestic
    string boardType;
    //海外看板的基础配置
    OverseaBasicConfig overseaBasicConfig;
    //国内看板的基础配置数据(适用于收入力指标的展示)
    DomesticBasicConfig domesticBasicConfig;
    BaijiCommonTypes.ResponseStatusType responseStatus;
}

class BusinessMetricMap{
    //id，一级（业务线）：(1门票，2玩乐)——二级（指标）：（1gmv，2毛利，3质量，4直签，5景点覆盖，6票种覆盖，7门票预订政策，9品类覆盖，11日游覆盖，12日游预订政策）——三级（指标）：（2玩乐，3活动，4日游）
    int id
    //名称，一级（业务线）：(tic、play)——二级（指标）：（gmv,profit,quality,directSign,sightCover,typeCover,ticketBooking,categoryCover,dayTourCover,dayTourBooking）——三级（指标）：（play，act，dayTour）
    string code
    //业务线对应的指标列表
    list<BusinessMetricMap> metricList
}


class OverseaBasicConfig{
    //海外看板需要展示的时间类型  quarter只需要季度框(适用于大部分人)   half只需要半年框(适用于孙磊 刘畅)
    string dateType;
    //key大指标枚举值 101 102  value拥有的子指标权限
    map<string,availableSubMetric> metricCardConfigMap;
    map<string,availableSubMetric> trendLineConfigMap;

}


class DomesticBasicConfig{
    //key大指标枚举值 101 102  value拥有的子指标权限
    map<string,availableSubMetric> metricCardConfigMap;
    map<string,availableSubMetric> trendLineConfigMap;

}


class availableSubMetric{
    //目前海外的枚举值 destination(目的地) destination_c(目的地c站) destination_t(目的地t站) site(站点)  channel(渠道)  tklk(客路) tfly(飞猪)
    //目前国内的枚举值 ticketActivity(门票活动)  domesticDayTour(国内日游)  overseaDayTour(出境日游)  ticketActivity+domesticDayTour(门票活动+国内日游)  ticketActivity+domesticDayTour+overseaDayTour(门票活动+国内日游+出境日游)
    list<string> subMetricList;
}



class CheckAdminPermissionRequestType{

}


class CheckAdminPermissionResponseType{
    //如果有管理员权限  则需要同时展示国内和海外看板
    bool haveAdminPermission;
    BaijiCommonTypes.ResponseStatusType responseStatus;
}


@serviceName='BIBusinessDashboardService'
@serviceNamespace='http://soa.ctrip.com/_24922'
service BIBusinessDashboardService {
    CheckUserPermissionResponseType checkUserPermission(CheckUserPermissionRequestType request);
    CheckAdminPermissionResponseType checkAdminPermission(CheckAdminPermissionRequestType request);
}