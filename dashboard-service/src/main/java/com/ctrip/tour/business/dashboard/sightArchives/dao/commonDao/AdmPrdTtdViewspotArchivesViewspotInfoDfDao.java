package com.ctrip.tour.business.dashboard.sightArchives.dao.commonDao;


import com.ctrip.soa._24922.DeliveryPage;
import com.ctrip.soa._27181.PreparedParameterBean;
import com.ctrip.tour.business.dashboard.tktBusiness.dao.TktStarRocksDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.sql.Types;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class AdmPrdTtdViewspotArchivesViewspotInfoDfDao {

    //景点竞争力信息  数仓侧设计文档：http://conf.ctripcorp.com/pages/viewpage.action?pageId=3668691112
    //dw_ticketdb.adm_prd_ttd_viewspot_archives_viewspot_info_df

    @Autowired
    TktStarRocksDao tktStarRocksDao;


    //查询景点竞争力信息
    public Map<String, Object> querySightCompetitiveInfo(Long sightId,String queryD, String startDate, String endDate) {

        String sql = "select " +
                " sum(is_adult_cover) as is_adult_cover, " +   //成人票覆盖
                " sum(is_children_cover) as is_children_cover, " +   //儿童票覆盖
                " sum(is_elder_cover) as is_elder_cover, " +   //老人票覆盖
                " sum(is_student_cover)as is_student_cover," +   //学生票覆盖
                " sum(is_family_cover) as is_family_cover, " +   //亲子家庭票覆盖

                " sum(is_today_book) as is_today_book," +   //当天可订覆盖
                " sum(is_immediately_confirm) as is_immediately_confirm, " +   //下单立即确认覆盖
                " sum(is_immediately_available) as is_immediately_available," +   //出票立即可用覆盖
                " sum(is_enter_directly) as is_enter_directly," +   //直接入园覆盖
                " sum(is_anytime_refund) as is_anytime_refund," +   //随时退覆盖
                " sum(is_price_schedule) as is_price_schedule" +   //班期覆盖

                " from adm_prd_ttd_viewspot_archives_viewspot_info_df" +
                " where d between ? and ? and viewspot_id = ?";
        List<PreparedParameterBean> parameters = new ArrayList<>();
        parameters.add(new PreparedParameterBean(startDate, Types.VARCHAR));
        parameters.add(new PreparedParameterBean(endDate, Types.VARCHAR));
        parameters.add(new PreparedParameterBean(String.valueOf(sightId), Types.BIGINT));
        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql,parameters);
        } catch (SQLException e) {
            log.warn("querySightCompetitiveInfo error", e);
        }

        if (CollectionUtils.isEmpty(result)) {
            return new HashMap<>();
        }
        return result.get(0);
    }



    //查询竞争圈景点id
    public List<Long> queryCompetitiveSightIdList(Long sightId, Integer competitorType, String queryD) {

        String sql = "select same_tag_viewspot_list,same_area_viewspot_list from adm_prd_ttd_viewspot_archives_viewspot_info_df where d=? and viewspot_id = ?";
        List<PreparedParameterBean> parameters = new ArrayList<>();
        parameters.add(new PreparedParameterBean(queryD, Types.VARCHAR));
        parameters.add(new PreparedParameterBean(String.valueOf(sightId), Types.BIGINT));
        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql,parameters);
        } catch (SQLException e) {
            log.warn("queryCompetitiveSightIdList error", e);
        }

        List<Long> competitiveSightIdList = new ArrayList<>();
        if (CollectionUtils.isEmpty(result)) {
            return competitiveSightIdList;
        }
        Map<String, Object> map = result.get(0);
        String competitiveSightIdListStr = "";
        if (competitorType == 1) {  //竞争圈类型 1-同品类 2-同区域
            competitiveSightIdListStr = (String) map.get("same_tag_viewspot_list");
        } else if (competitorType == 2) {
            competitiveSightIdListStr = (String) map.get("same_area_viewspot_list");
        }
        if (StringUtils.isNotBlank(competitiveSightIdListStr)) {
            String[] split = competitiveSightIdListStr.split(",");
            for (String s : split) {
                try{
                    competitiveSightIdList.add(Long.valueOf(s));
                }catch (NumberFormatException e){
                    log.warn("Long.valueOf error", e);
                }
            }
        }


        return competitiveSightIdList;

    }

    //竞争圈中覆盖景点数
    public Map<String, Object> querySightCoverCount(List<Long> sightIdList,String startDate,String endDate) {

        StringBuilder sql =new StringBuilder(
                "select " +
                        " count(distinct case when is_adult_cover>0 then viewspot_id end ) as is_adult_cover, " +   //成人票覆盖
                        " count(distinct case when is_children_cover>0 then viewspot_id end ) as is_children_cover, " +   //儿童票覆盖
                        " count(distinct case when is_elder_cover>0 then viewspot_id end ) as is_elder_cover, " +   //老人票覆盖
                        " count(distinct case when is_student_cover>0 then viewspot_id end ) as is_student_cover, " +   //学生票覆盖
                        " count(distinct case when is_family_cover>0 then viewspot_id end ) as is_family_cover, " +   //亲子家庭票覆盖

                        " count(distinct case when is_today_book>0 then viewspot_id end ) as is_today_book, " +   //当天可订覆盖
                        " count(distinct case when is_immediately_confirm>0 then viewspot_id end ) as is_immediately_confirm, " +   //下单立即确认覆盖
                        " count(distinct case when is_immediately_available>0 then viewspot_id end ) as is_immediately_available, " +   //出票立即可用覆盖
                        " count(distinct case when is_enter_directly>0 then viewspot_id end ) as is_enter_directly, " +   //直接入园覆盖
                        " count(distinct case when is_anytime_refund>0 then viewspot_id end ) as is_anytime_refund, " +   //随时退覆盖
                        " count(distinct case when is_price_schedule>0 then viewspot_id end ) as is_price_schedule " +   //班期覆盖

                " from adm_prd_ttd_viewspot_archives_viewspot_info_df where d between ? and ? "
        );

        List<PreparedParameterBean> parameters = new ArrayList<>();
        parameters.add(new PreparedParameterBean(startDate, Types.VARCHAR));
        parameters.add(new PreparedParameterBean(endDate, Types.VARCHAR));
        appendSightList(sightIdList, sql, parameters);
//        sql.append(" group by viewspot_id");

        List<Map<String, Object>> result = null;
        try {
            result = tktStarRocksDao.getListResultNew(sql.toString(),parameters);
        } catch (SQLException e) {
            log.warn("querySightCoverCount error", e);
        }
        if (CollectionUtils.isEmpty(result)) {
            return new HashMap<>();
        }
        return result.get(0);

    }

    private void appendSightList(List<Long> sightIdList, StringBuilder sql, List<PreparedParameterBean> parameters) {
        sql.append(" and viewspot_id in ( ");
        for(Long sightId : sightIdList){
            sql.append("?,");
            parameters.add(new PreparedParameterBean(String.valueOf(sightId), Types.BIGINT));
        }
        sql.delete(sql.length()-1,sql.length());
        sql.append(" ) ");


    }


}
