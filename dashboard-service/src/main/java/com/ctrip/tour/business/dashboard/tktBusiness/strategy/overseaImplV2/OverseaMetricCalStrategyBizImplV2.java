package com.ctrip.tour.business.dashboard.tktBusiness.strategy.overseaImplV2;



import com.ctrip.soa._24922.*;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBean;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBeanV2;
import com.ctrip.tour.business.dashboard.tktBusiness.bean.OverseaMetricInfoBeanV3;
import com.ctrip.tour.business.dashboard.tktBusiness.exception.StrategyException;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.OverseaMetricCalStrategy;
import com.ctrip.tour.business.dashboard.tktBusiness.strategy.OverseaMetricCalStrategyV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;

@Service
@Slf4j
public class OverseaMetricCalStrategyBizImplV2 implements ApplicationContextAware {


    private ApplicationContext applicationContext;

    private ConcurrentHashMap<String, OverseaMetricCalStrategyV2> metricCalStrategyMap = new ConcurrentHashMap<>();


    public Future<OveaseaMetric> getOverseaSingleMetricCardData(TimeFilter timeFilter,
                                                                OverseaMetricInfoBeanV2 metricInfoBean,
                                                                String d,
                                                                Map<String, AvailableSubMetric> metricCardConfigMap,
                                                                GetOverseaMetricCardDataV2RequestType request) throws Exception {
        String metric = metricInfoBean.getMetric();
        OverseaMetricCalStrategyV2 metricCalStrategy = metricCalStrategyMap.get(metric);
        if (metricCalStrategy == null) {
            throw new StrategyException("strategy is not found!");
        }
        return metricCalStrategy.getOverseaSingleMetricCardData(timeFilter, metricInfoBean, d, metricCardConfigMap.get(metric), request);
    }


    public GetOverseaTrendLineDataV2ResponseType getOverseaSingleTrendLineData(GetOverseaTrendLineDataV2RequestType request,
                                                                             String d) throws Exception {
        String metric = request.getMetric();
        OverseaMetricCalStrategyV2 metricCalStrategy = metricCalStrategyMap.get(metric);
        if (metricCalStrategy == null) {
            throw new StrategyException("strategy is not found!");
        }
        return metricCalStrategy.getOverseaSingleTrendlineData(request, d);
    }

    public GetOverseaDrillDownBaseInfoV2ResponseType getOverseaSingleDrillDownBaseInfo(GetOverseaDrillDownBaseInfoV2RequestType request,
                                                                                     String d,
                                                                                       OverseaMetricInfoBean metricInfoBean) throws Exception {
        String metric = request.getMetric();
        OverseaMetricCalStrategyV2 metricCalStrategy = metricCalStrategyMap.get(metric);
        if (metricCalStrategy == null) {
            throw new StrategyException("strategy is not found!");
        }
        return metricCalStrategy.getOverseaSingleDrillDownBaseInfo(request, d, metricInfoBean);
    }


    public GetOverseaTableDataV2ResponseType getOverseaSingleTableData(GetOverseaTableDataV2RequestType request,
                                                                     String d,
                                                                       OverseaMetricInfoBean metricInfoBean) throws Exception {
        String metric = request.getMetric();
        OverseaMetricCalStrategyV2 metricCalStrategy = metricCalStrategyMap.get(metric);
        if (metricCalStrategy == null) {
            throw new StrategyException("strategy is not found!");
        }
        return metricCalStrategy.getOverseaSingleTableData(request, d, metricInfoBean);
    }


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
        Map<String, OverseaMetricCalStrategyV2> strategyMap = applicationContext.getBeansOfType(OverseaMetricCalStrategyV2.class);
        strategyMap.values().forEach(e -> metricCalStrategyMap.put(e.getMetricName(), e));
    }
}
